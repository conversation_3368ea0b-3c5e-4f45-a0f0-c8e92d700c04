#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I $SDK_ROOT/drivers/cmsis/sf32lb52x
#include "rtconfig.h"
#include "mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE {    ; load region size_region
  ER_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   *(FSymTab)
   startup_bf0_lcpu.o (.text)
   bf0_hal_mpi.o (.text.*)
   bf0_hal_mpi_ex.o (.text.*)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   bf0_hal_lpaon.o (.text.*)
   drv_spi_flash.o (+RO)
   system_bf0_ap.o (+RO)
   bf0_hal.o (+RO)
   
   bf0_hal_rcc.o   (.text.*)
#ifdef BSP_USING_PM   
   bf0_pm.o        (.text.sifli_light_handler)
   bf0_pm.o        (.text.sifli_deep_handler)
   bf0_pm.o        (.text.sifli_standby_handler)
   bf0_pm.o        (.text.SystemPowerOnModeInit)
   bf0_pm.o        (.text.SystemPowerOnModeGet)
   bf0_pm.o        (.text.BSP_GPIO_Set)
   drv_io.o           (.text.*)
   bf0_hal_gpio.o     (.text.*)
   bf0_hal.o          (.text.HAL_Init)
   *.o                (.text.HAL_MspInit)
   bf0_hal_pinmux.o   (+RO)
   bf0_pin_const.o    (+RO)
   *.o                (.text.rt_memset)
   ;*.o                (.text.mpu_config)
   ;*.o                (.rodata.mpu_config)
#endif     
   
   *.o (+RO)
   *.o (.rodata.*)   
   
  }
  RW_IRAM1 AlignExpr(LCPU_RAM_CODE_START_ADDR_S+ImageLength(ER_IROM1), 16) ALIGN 16 NOCOMPRESS  {  ; RW data
   .ANY (+RW +ZI)
  }
  ; Load Address must be equal to Exec Address
  ScatterAssert((LoadBase(RW_IRAM1) OR 0x20000000) == ImageBase(RW_IRAM1))
  ScatterAssert((ImageLength(ER_IROM1)+ImageLength(RW_IRAM1)+LCPU_MBOX_SIZE)<LPSYS_RAM_SIZE)
}

