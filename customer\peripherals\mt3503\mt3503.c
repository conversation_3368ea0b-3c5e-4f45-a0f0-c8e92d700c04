/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503.c
@Time    :   2025/02/13 20:34:53
@Brief   :   光旋钮驱动API模块
@Details :
*
************************************************************/

#include "rtconfig.h"
#ifdef BSP_USING_MT3503
#include "mt3503.h"
#include "mt3503_algo.h"
#include "mt3503_reg.h"
#include "key_module.h"
#include "qw_system_params.h"
#include <drv_log.h>
#ifdef BSP_USING_KNOB
#include "drv_knob.h"
#else
#define DRV_MT3503_TEST    // 底层使用事件方式，避免开关传感器事件过长阻塞。
#endif

//#define DRV_DEBUG        // 调试LOG开关

#ifdef DRV_MT3503_TEST

//#define MT2503_POLL_MODE   // 使用POLL模式   使用中断模式，会导致TP异常

// 事件集合
#define MT3503_EVENT_START        (1 << 0)  //事件0标志位置
#define MT3503_EVENT_STOP         (1 << 1)  //事件1标志位置
#define MT3503_EVENT_CALIB_START  (1 << 2)  //事件2标志位置
#define MT3503_EVENT_CALIB_STOP   (1 << 3)  //事件3标志位置
#define MT3503_EVENT_POLL         (1 << 4)  //事件4标志位置

rt_thread_t mt3503_thread;
rt_event_t mt3503_event;
volatile static bool mt3503_ispoll = false;     // 默认开机关闭
volatile static bool mt3503_work_flag = false;  // 默认开机关闭
volatile static bool mt3503_calib_flag = false; // 默认开机关闭

#define MT2503_MOTION_INT_PIN 70

#ifndef MT2503_POLL_MODE
const uint8_t mt3503_int_pin = MT2503_MOTION_INT_PIN;
#endif
#endif

volatile static bool mt3503_init_flag = false;  // 默认开机关闭
volatile static bool mt3503_iswork = false;     // 默认开机关闭

/* Private variables --------------------------------------------------------*/
static struct rt_i2c_bus_device *mt3503_bus = RT_NULL;
static stmdev_ctx_t dev_ctx;
static mt3503_id_t id;
static mt3503_delta_xy_t data_xy;
static struct mxs_algo_data mt3053_data;

static int32_t mxs_calib_counter = 0;     // X轴累计值
static uint16_t mxs_res_x = 0;            // X轴RES_X
static uint16_t mxs_res_x_calib = 0x01BD; // X轴RES_X的校正值,当前板子推荐值对应的大致校正值
static uint32_t mxs_target = 3600;        // X轴旋转一周设定的目标值,推荐3600
static bool mxs_calib = false;            // 是否在校正
static rt_err_t mt3503_calib_start(void); // 光旋钮校正开始
static rt_err_t mt3503_calib_stop(void);  // 光旋钮校正结束
static int mxs_calib_deal(int16_t dx);    // 光旋钮校正X增量数据累计

static rt_err_t mt3503_start(void);       // 光旋钮开启
static rt_err_t mt3503_stop(void);        // 光旋钮关闭

//----------------------------------------IIC读写接口---------------------------------------------------------------------
static uint8_t mt3503_write_8bit(uint8_t address, uint8_t tx_cmd)
{
    struct rt_i2c_msg msgs[1];
    uint8_t value[2];
    uint32_t res;
    if (mt3503_bus)
    {
        value[0] = address;
        value[1] = tx_cmd;
        msgs[0].addr = MT3503_IIC_ADDR;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = value;
        msgs[0].len = 2;
        if (rt_i2c_transfer(mt3503_bus, msgs, 1) == 1)
        {
            return RT_EOK;
        }
    }
    return RT_ERROR;
}

static uint8_t mt3503_read_8bit(uint8_t address, uint8_t *rx_buffer, const uint8_t len)
{
    struct rt_i2c_msg msgs[2];
    uint32_t res;
    if (mt3503_bus)
    {
        msgs[0].addr = MT3503_IIC_ADDR;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = &address;
        msgs[0].len = 1;
        msgs[1].addr = MT3503_IIC_ADDR;
        msgs[1].flags = RT_I2C_RD;
        msgs[1].buf = rx_buffer;
        msgs[1].len = len;

        if (rt_i2c_transfer(mt3503_bus, msgs, 2) == 2)
        {
            return RT_EOK;
        }
    }
    HAL_Delay(1);
    return RT_ERROR;
}

static int32_t write_reg(void *handle, uint8_t reg, const uint8_t *bufp, uint16_t len)
{
    // 用mt3503_write_8bit封装
    for (int i = 0; i < len; i++)
    {
        if (mt3503_write_8bit(reg + i, bufp[i]) != RT_EOK)
        {
            return -RT_ERROR;
        }
    }
    // rt_kprintf("w1\n");
    return RT_EOK;
}

static int read_reg(void *handle, uint8_t reg, uint8_t *bufp, uint16_t len)
{
    rt_uint8_t tmp = reg;
    struct rt_i2c_msg msgs[2];
    msgs[0].addr = MT3503_IIC_ADDR; /* Slave address */
    msgs[0].flags = RT_I2C_WR;      /* Write flag */
    msgs[0].buf = &tmp;             /* Slave register address */
    msgs[0].len = 1;                /* Number of bytes sent */
    msgs[1].addr = MT3503_IIC_ADDR; /* Slave address */
    msgs[1].flags = RT_I2C_RD;      /* Read flag */
    msgs[1].buf = bufp;             /* Read data pointer */
    msgs[1].len = len;              /* Number of bytes read */
    if (rt_i2c_transfer((struct rt_i2c_bus_device *)handle, msgs, 2) != 2)
    {
        return -RT_ERROR;
    }
    return RT_EOK;
}

static void gpio_dlps_ctrl(bool en_dlps)
{
    if(en_dlps){ // 浮空输入
        // 光旋扭中断关闭时
        //HAL_PIN_Set_Analog(PAD_PA70, 1); // 管脚低功耗时的配置
        GPIO_InitTypeDef GPIO_InitStruct;
        HAL_PIN_Set(PAD_PA70, GPIO_A70, PIN_NOPULL, 1);
        GPIO_InitStruct.Pin = 70;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);
    }else{        // 上拉输入
        // 光旋扭中断开启时
        GPIO_InitTypeDef GPIO_InitStruct;
        HAL_PIN_Set(PAD_PA70, GPIO_A70, PIN_PULLUP, 1);
        GPIO_InitStruct.Pin = 70;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
        HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);
    }
}

#ifndef MT2503_POLL_MODE
static int mt3503_fifo_clear(void)
{
    int pin_val = 0;
    int try_times = 0;
    do{  // 读一段清除历史缓存？方便触发中断
        pin_val = rt_pin_read(MT2503_MOTION_INT_PIN);
        if(pin_val == 0)
        {
            int ret = mt3503_data_get(&dev_ctx, &data_xy);
            if(ret == RT_EOK)
            {
                //MT3503_LOG_D("###x = %d,y = %d,motion = %d, rt_pin_read = %d\n",data_xy.x,data_xy.y,data_xy.motion,pin_val);
                //if(data_xy.motion == 0) break;
                try_times++;
                if(try_times>=30){
                    MT3503_LOG_E("###mt3503_fifo_clear err\n");
                    return RT_ERROR;
                }
            }
            else
            {
                MT3503_LOG_E("mt3503_data_get err\n");
                return RT_ERROR;
            }
        }
        //rt_thread_mdelay(10);
    }while(pin_val == 0);
    //MT3503_LOG_D("###mt3503_fifo_clear\n");
    return RT_EOK;
}
#endif

static uint16_t times = 0;
static uint8_t lastdata_index = 0;
static int16_t lastdata[20] = {0};
static int32_t lastdata_couter = 0;
static uint32_t lasttime_start = 0;
static uint32_t lasttime_end = 0;

static void mt3503_poll_init()
{
    times = 0;
    lastdata_index = 0;
    lastdata_couter = 0;

    lasttime_start = 0;
    lasttime_end = 0;

    memset(lastdata,0,20*2);
    memset(&mt3053_data,0,sizeof(struct mxs_algo_data));
    mxs_algo_reset();
}


#ifdef DRV_MT3503_TEST

static rt_timer_t mt3503_timer = RT_NULL;

static void mt3503_timer_callback(void *p_context)
{
    //MT3503_LOG_D("mt3503_timer_callback...\n");
    rt_event_send(mt3503_event, MT3503_EVENT_POLL);           //发送事件标志位
}

static void mt3503_module_poll_init(void){
    mt3503_timer = rt_timer_create("mt3503",mt3503_timer_callback,NULL,MXS_MOTION_SAMPLE_INTERVAL,RT_TIMER_FLAG_PERIODIC|RT_TIMER_FLAG_SOFT_TIMER);
    RT_ASSERT(mt3503_timer);
}

static void mt3503_module_poll_suspend(void)
{
    if(mt3503_ispoll){
#ifndef MT2503_POLL_MODE
        //开中断
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_ENABLE);

        mt3503_fifo_clear(); // 清空缓存
#endif
        mt3503_ispoll = false;
        rt_timer_stop(mt3503_timer);
    }
}

static void mt3503_module_poll_resume(void)
{
    if(!mt3503_ispoll){
        mt3503_ispoll = true;
#ifndef MT2503_POLL_MODE
        //关中断
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_DISABLE);
#endif
        rt_timer_start(mt3503_timer);
    }
}

static void mt3503_module_poll_deinit(void)
{
    rt_timer_delete(mt3503_timer);
    mt3503_timer = RT_NULL;
}
#endif

static rt_err_t mt3503_poll(void* msg, bool is_calib)
{
    int32_t ret = RT_EOK;
    if(mt3503_iswork)
    {
        //通过读取PIN脚减少读取I2C
        int pin_val = rt_pin_read(MT2503_MOTION_INT_PIN);
        if(pin_val){
            memset(&data_xy,0,sizeof(mt3503_delta_xy_t));
        }else {
            mt3503_data_get(&dev_ctx, &data_xy);
        }
        /* 符号变换 */
        int16_t dx = 0;
        int16_t dy = 0;
        if(data_xy.x>=2048) dx = data_xy.x - 4096;
        else  dx = data_xy.x;
        if(data_xy.y>=2048) dy = data_xy.y - 4096;
        else  dy = data_xy.y;

        int32_t value = 0;             // 累积值
        mxs_algo_motion_event_t event = MXS_ALGO_MOTION_RELEASE; // 事件
        uint32_t timestamp = rt_tick_get();
        mxs_algo_data_t p_data = (mxs_algo_data_t)msg;

        if(is_calib){
           value = mxs_calib_deal(dx); // 光旋转校正
           if(value>0) event = MXS_ALGO_MOTION_ROTATE_CW;
           else event = MXS_ALGO_MOTION_ROTATE_CCW;
        }
        else
        {
           mxs_algo_deal(dx,dy,timestamp,&event,&value); /* 过滤算法 */
        }

#ifdef DRV_DEBUG /* 通过串口看波形,打印不录入LOG */
        if(event == MXS_ALGO_MOTION_ROTATE_CW)
        {
            rt_kprintf("MT3503: %04d,%04d\n", dx + 2048, 0);
        }
        else  if(event == MXS_ALGO_MOTION_ROTATE_CCW)
        {
            rt_kprintf("MT3503: %04d,%04d\n", dx + 2048, 1000);
        }
        else
        {
            rt_kprintf("MT3503: %04d,%04d\n", dx + 2048, 500);
        }
#endif
        /* 速度算法 */
        int32_t velocity = 0;
        bool mulknob_running = false;
        if(!is_calib){
#if 1
            // 累计20次的平均增量,用于估算旋转速度,值越大,旋转速度越大。
            lastdata_couter += dx;
            lastdata_couter -= lastdata[lastdata_index];
            lastdata[lastdata_index] = dx;
            lastdata_index += 1;
            lastdata_index = lastdata_index % 20;
            // 延时释放
            if (dx != 0)
            {
                times = 0;
                mulknob_running = true;
                if(lasttime_start == 0) {
                    lasttime_start = timestamp;
                    //MT3503_LOG_D("MT3503: last_time start = %d\n",lasttime_start);
                }
                p_data->last_time = timestamp - lasttime_start;
            }else{
                if(times<10)
                {
                    times++;
                    mulknob_running = true;
                }

                if((lasttime_start!=0)&&(lasttime_end == 0)) {
                    lasttime_end = timestamp;
                    //p_data->last_time = lasttime_end - lasttime_start;
                    //MT3503_LOG_D("MT3503: last_time = %d, start = %d,end = %d\n",p_data->last_time,lasttime_start,lasttime_end);
                    lasttime_start = 0;
                    lasttime_end = 0;
                }
            }
            if((mulknob_running == false)||(value == 0)||(p_data->last_time<=0)){
                p_data->last_time = 0;
            }

            // 估算旋转速度
            velocity = (lastdata_couter/((20-times)*10));
            // 释放时清空累计缓存
            #ifdef MT2503_POLL_MODE
            if(!mulknob_running)
            {
                lastdata_couter = 0;
                memset((void*)lastdata,0,20*2);
                lastdata_index = 0;
                times = 0;
            }
            #endif
#else
            // 单次旋转平均速度
            if (dx != 0) {
                mulknob_running = true;
                times = 0;
                if(lasttime_start == 0) {
                    lasttime_start = timestamp;
                    MT3503_LOG_D("MT3503: last_time start = %d\n",lasttime_start);
                }
                p_data->last_time = timestamp - lasttime_start;
            }else{
                if(times<10)
                {
                    times++;
                    mulknob_running = true;
                }
                if((lasttime_start!=0)&&(lasttime_end == 0)) {
                    lasttime_end = timestamp;
                    //p_data->last_time = lasttime_end - lasttime_start;
                    MT3503_LOG_D("MT3503: last_time = %d, start = %d,end = %d\n",p_data->last_time,lasttime_start,lasttime_end);
                    lasttime_start = 0;
                    lasttime_end = 0;
                }
            }
            // 估算旋转速度*60 =  转每分
            if((mulknob_running == false)||(value == 0)||(p_data->last_time<=0)){
                velocity = 0;
                p_data->last_time = 0;
            }
            else
            {
                if(value>=0) velocity = ((value*60)/((p_data->last_time*MXS_MOTION_CALIB_TARGET)/1000));
                else if(value < 0) velocity = (((0-value)*60)/((p_data->last_time*MXS_MOTION_CALIB_TARGET)/1000));
            }
#endif
        }else{
            //MT3503_LOG_D("MT3503: dx = %d, start = %d,end = %d,times = %d\n",dx,lasttime_start,lasttime_end,times);
            if (dx != 0) {
                mulknob_running = true;
                if(lasttime_start == 0) {
                    lasttime_start = timestamp;
                    p_data->last_time = 0;
                    mxs_calib_counter = dx;
                    lasttime_end = 0;
                    //MT3503_LOG_D("MT3503: last_time start = %d\n",lasttime_start);
                }
                p_data->last_time = timestamp - lasttime_start;
            }else{
                if((lasttime_start!=0)&&(lasttime_end == 0)) {
                    lasttime_end = timestamp;
                    //p_data->last_time = lasttime_end - lasttime_start;
                    //MT3503_LOG_D("MT3503: last_time = %d, start = %d,end = %d\n",p_data->last_time,lasttime_start,lasttime_end);
                    lasttime_start = 0;
                    lasttime_end = 0;
                }
            }
            // 估算旋转速度*60
            if(p_data->last_time>0){
                if(value>=0) velocity = ((value*60)/((p_data->last_time*MXS_MOTION_CALIB_TARGET)/1000));
                else if(value < 0) velocity = (((0-value)*60)/((p_data->last_time*MXS_MOTION_CALIB_TARGET)/1000));
            }
        }

        p_data->event = event;
        p_data->x = dx;
        p_data->y = dy;
        p_data->scale_factor = mxs_target;
        //单次累计旋转角度,根据mxs_target值量化成角度。
        if(value>= 0) p_data->angle = (value)/(mxs_target/360);
        else          p_data->angle = 0 - ((0 - value)/(mxs_target/360));
        p_data->speed = velocity;                         //旋转速度
        p_data->running = mulknob_running;

        //MT3503_LOG_D("MT3503: is_calib =%d, dx = %d,dy = %d,value = %d angle = %d speed = %d\n",is_calib,dx,dy,value,p_data->angle,p_data->speed);

        #ifdef DRV_MT3503_TEST
        if(!mulknob_running){
            #ifndef MT2503_POLL_MODE
            mt3503_module_poll_suspend();
            //mt3503_poll_init();
            #endif
        }
        #endif
    }
    return ret;
}


#ifdef DRV_MT3503_TEST

// 工作线程   检测到中断后,累加dx,达到某一阈值时,调用用户回调,然后清除状态 和 x
static void mt3503_thread_task(void *parameter)
{
    while (1)
    {
           rt_uint32_t e;   //创建收到的事件事件
           if (rt_event_recv(mt3503_event, (MT3503_EVENT_START|MT3503_EVENT_STOP|MT3503_EVENT_POLL|MT3503_EVENT_CALIB_START|MT3503_EVENT_CALIB_STOP),
           RT_EVENT_FLAG_OR|RT_EVENT_FLAG_CLEAR,RT_WAITING_FOREVER, &e) == RT_EOK)
           {
               //MT3503_LOG_D("mt3503_event = 0x%08x %d,%d,%d,%d\n",e,mt3503_init_flag,mt3503_work_flag,mt3503_calib_flag,mt3503_ispoll);
               if((e&MT3503_EVENT_CALIB_STOP)>0)   // CALIB_STOP
               {
                   if(mt3503_init_flag && mt3503_calib_flag)
                   {
                       mt3503_calib_stop();
                       mt3503_calib_flag = false;
                   }
               }
               else if((e&MT3503_EVENT_CALIB_START)>0)  // CALIB_START
               {
                   if(mt3503_init_flag && (!mt3503_calib_flag))
                   {
                       mt3503_calib_start();
                       mt3503_calib_flag = true;
                   }
               }
               else if((e&MT3503_EVENT_STOP)>0)         // STOP
               {
                   if(mt3503_init_flag && mt3503_work_flag)
                   {
                       mt3503_stop();
                       mt3503_work_flag = false;
                   }
               }
               else if((e&MT3503_EVENT_START)>0)        // START
               {
                   if(mt3503_init_flag && (!mt3503_work_flag))
                   {
                       mt3503_start();
                       mt3503_work_flag = true;
                   }
               }
               else if((e&MT3503_EVENT_POLL)>0)         // POLL
               {
                   if(mt3503_init_flag && (mt3503_work_flag || mt3503_calib_flag))
                   {
                       mt3503_poll((void *)&mt3053_data,mt3503_calib_flag);
                   }
               }
           }
    }
}

#ifndef MT2503_POLL_MODE
static void mt3503_int_callback(void *args)
{
    //rt_kprintf("####### mt3503_int_callback %d,%d,%d\n",mt3503_iswork,mt3503_ispoll,rt_pin_read(MT2503_MOTION_INT_PIN));
    if(mt3503_iswork&&(!mt3503_ispoll))
    {
        mt3503_module_poll_resume();
        mt3503_poll_init();        // 清除速度,和累计角度值
    }
}
#endif
#endif

int mt3503_init(void)
{
    //MT3503_LOG_D("mt3503_init\n");
    if(!mt3503_init_flag)
    {
        int32_t ret = 0;
        /* get i2c bus device */
        mt3503_bus = rt_i2c_bus_device_find(MT3503_IIC_BUS);
        if (mt3503_bus)
        {
            rt_device_open(&(mt3503_bus->parent), RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_RX | RT_DEVICE_FLAG_INT_TX);
            //MT3503_LOG_D("Find i2c bus device %s\n", MT3503_IIC_BUS);
        }
        else
        {
            MT3503_LOG_E("Can not found i2c bus %s, init fail\n", MT3503_IIC_BUS);
            return RT_ERROR;
        }
        dev_ctx.handle = mt3503_bus;
        dev_ctx.write_reg = write_reg;
        dev_ctx.read_reg = read_reg;
        dev_ctx.mdelay = (stmdev_mdelay_ptr)rt_thread_mdelay;
        // 1、引脚设置
        // 1.1、使能脚：开启时配置
        #ifndef MT2503_POLL_MODE
        //// 1.2、中断脚（上拉输入：demo上是浮空输入）、使能中断(下降沿触发)
        gpio_dlps_ctrl(false);
        rt_pin_mode(MT2503_MOTION_INT_PIN, PIN_MODE_INPUT_PULLUP);
        //rt_pin_attach_irq(MT2503_MOTION_INT_PIN, PIN_IRQ_MODE_FALLING, mt3503_int_callback, (void *)(uintptr_t)mt3503_int_pin);
        //rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_ENABLE);
        #endif
        // 2、传感器初始化
        mt3503_mode_enter(&dev_ctx, MT3503_MODE_RESET);
        dev_ctx.mdelay(10);
        mt3503_state_init(&dev_ctx);
        // 3、写入校正值
        //mxs_res_x_calib 需要从NV读取
        //mt3503_res_x_set(&dev_ctx, mxs_res_x_calib);
        // 4、 验证ID
        if (mt3503_id_get(&dev_ctx, &id) == RT_EOK)
        {
            if (id.whoami != MT3503_ID)
            {
                MT3503_LOG_I("This device(id=0x%X) is not MT3503_ID", id.whoami);
                return RT_ERROR;
            }
            else
            {
                MT3503_LOG_I("This device(id=0x%X) is MT3503_ID", id.whoami);
            }
        }
        else
        {
            MT3503_LOG_E("failed to get mt3503 ID");
            return RT_ERROR;
        }
    #ifdef DRV_MT3503_TEST /* 初始化后默认关闭 */
        //mt3503_event = rt_event_create("mt3503_e",RT_IPC_FLAG_PRIO);
        //RT_ASSERT(mt3503_event);
        static struct rt_event knob_event;
        mt3503_event = &knob_event;
        rt_err_t err = rt_event_init(mt3503_event,"mt3503_e",RT_IPC_FLAG_PRIO);
        if (RT_EOK != err)
        {
            mt3503_event = RT_NULL;
            return RT_ERROR;
        }
        mt3503_thread = rt_thread_create("mt3503_thread", mt3503_thread_task, RT_NULL, 1024*4,24,RT_THREAD_TICK_DEFAULT);
        RT_ASSERT(mt3503_thread);
        //启动线程
        rt_thread_startup(mt3503_thread);

        mt3503_module_poll_init();
    #ifndef MT2503_POLL_MODE
        //SF32LB55x-FAQ-V0.03_1219.pdf 在 detach irq 之前，需要先关闭该 pin 的中断
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_DISABLE);
        rt_pin_detach_irq(MT2503_MOTION_INT_PIN);
        gpio_dlps_ctrl(true);  /* mt3503会自己进入休眠，这里只处理INT */
    #endif
    #endif

        mt3503_mode_enter(&dev_ctx, MT3503_MODE_POWER_DOWN);

    #if defined(USE_KNOB_KEY_MODE) || defined(USE_KNOB_TOUCH_MODE)
        mxs_algo_init(knob_key_handler);
    #endif
        mt3503_init_flag = true;
        mt3503_iswork = false;
    #ifdef DRV_MT3503_TEST
        mt3503_control(MT3503_CMD_START, NULL);
    #endif
    }
    //给工模传递init信息
	fat_dev_info_t fat_dev_info={0};
	fat_dev_info.dev_type = FAT_DEV_KNOB;
	fat_dev_info.dev_id = id.whoami;
	fat_dev_info.dev_state = 1;
	fat_set_dev_init_info(fat_dev_info);
    return RT_EOK;
}
#if BSP_USING_KNOB == 0
INIT_DEVICE_EXPORT(mt3503_init);
#endif

int mt3503_deinit(void)
{
    //MT3503_LOG_D("mt3503_deinit\n");
    if(mt3503_init_flag)
    {
#ifdef DRV_MT3503_TEST
        if (mt3503_thread)
        {
            rt_thread_delete(mt3503_thread);
            mt3503_thread = RT_NULL;
        }
        mt3503_module_poll_deinit();

#endif
#ifndef MT2503_POLL_MODE
        //SF32LB55x-FAQ-V0.03_1219.pdf 在 detach irq 之前，需要先关闭该 pin 的中断
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_DISABLE);
        rt_pin_detach_irq(MT2503_MOTION_INT_PIN);
        gpio_dlps_ctrl(true);  /* mt3503会自己进入休眠，这里只处理INT */
#endif

        mt3503_init_flag = false;
    }
    return RT_EOK;
}

static rt_err_t mt3503_start(void)
{
    rt_err_t ret = RT_EOK;
    //MT3503_LOG_D("mt3503_start , mt3503_iswork= %d mt3503_ispoll = %d mxs_res_x_calib = 0x%04x\n",mt3503_iswork,mt3503_ispoll,mxs_res_x_calib);
    if(!mt3503_iswork)
    {
        #ifdef DRV_MT3503_TEST
        gpio_dlps_ctrl(false);
        rt_pin_mode(MT2503_MOTION_INT_PIN, PIN_MODE_INPUT_PULLUP);
        #ifndef MT2503_POLL_MODE
        rt_pin_attach_irq(MT2503_MOTION_INT_PIN, PIN_IRQ_MODE_FALLING, mt3503_int_callback, (void *)(uintptr_t)mt3503_int_pin);
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_ENABLE);
        #endif
        #endif

        ret = mt3503_mode_enter(&dev_ctx, MT3503_MODE_RESET);
        dev_ctx.mdelay(10);
        mt3503_state_init(&dev_ctx);
        //ret = mt3503_mode_enter(&dev_ctx, MT3503_MODE_SLEEP1);
        mt3503_res_x_set(&dev_ctx, mxs_res_x_calib); // 写入校正值
        rt_thread_mdelay(1);

        #ifdef DRV_MT3503_TEST
        #ifndef MT2503_POLL_MODE
            ret = mt3503_fifo_clear(); // 读一段清除历史缓存
        #else
            mt3503_module_poll_resume();
        #endif
        #endif
        mt3503_iswork = true;
    }

    mt3503_poll_init();        // 清除速度,和累计角度值
    mt3053_data.resx_clib = mxs_res_x_calib;

    //MT3503_LOG_D("mt3503_start\n");
    return RT_EOK;
}
static rt_err_t mt3503_stop(void)
{
    //MT3503_LOG_D("mt3503_stop , mt3503_iswork= %d mt3503_ispoll = %d mxs_res_x_calib = 0x%04x\n",mt3503_iswork,mt3503_ispoll,mxs_res_x_calib);
    if(mt3503_iswork)
    {
#ifdef DRV_MT3503_TEST
#ifndef MT2503_POLL_MODE
        //SF32LB55x-FAQ-V0.03_1219.pdf 在 detach irq 之前，需要先关闭该 pin 的中断
        rt_pin_irq_enable(MT2503_MOTION_INT_PIN, PIN_IRQ_DISABLE);
        rt_pin_detach_irq(MT2503_MOTION_INT_PIN);
#endif
        gpio_dlps_ctrl(true);
        mt3503_module_poll_suspend();
        mt3503_poll_init();
#endif
        //mt3503_mode_enter(&dev_ctx, MT3503_MODE_POWER_DOWN); // cut power supply derectly, no need to enter power down mode
        mt3503_iswork = false;
    }
    //MT3503_LOG_D("mt3503_stop");
    return RT_EOK;
}
int16_t mt3503_read_x(void)
{
    if(mt3503_iswork){
        return data_xy.x;
    }
    return 0;
}
int16_t mt3503_read_y(void)
{
    if(mt3503_iswork){
        return data_xy.y;
    }
    return 0;
}
static rt_err_t mt3503_calib(uint16_t val) // 恢复出厂设置时使用这个接口,写入默认校正值,
{
    if(mxs_res_x_calib != val){
        mxs_res_x_calib = val;
        return (rt_err_t)mt3503_res_x_set(&dev_ctx, mxs_res_x_calib);
    }
    return RT_EOK;
}
/* 光旋校正开始 */
static rt_err_t mt3503_calib_start(void)
{
    times = 0;
    lasttime_start = 0;
    lasttime_end = 0;

    mxs_calib = true;
    mxs_calib_counter = 0;
    mt3503_res_x_get(&dev_ctx, &mxs_res_x);              // 读取 RES_X 寄存器的值
    //MT3503_LOG_D("mxs_res_x = 0x%04x",mxs_res_x);
    mt3053_data.resx_clib = mxs_res_x;

    //mt3503_start();
    return RT_EOK;
}

/* 光旋校正结束
*/
static rt_err_t mt3503_calib_stop(void)
{
    //mt3503_stop();

    mxs_calib = false;
    if(mxs_calib_counter != 0){
        uint16_t res_x_calib = (mxs_res_x*mxs_target)/abs(mxs_calib_counter);
        //MT3503_LOG_D("mxs_res_x_set_calib = 0x%04x",res_x_calib);
        //return mt3503_calib(res_x_calib);   // 设置 RES_X 寄存器的值
        mt3053_data.resx_clib = res_x_calib;
        return RT_EOK;
    }
    return RT_ERROR;
}

/* 光旋校正X增量数据累计
*/
static int mxs_calib_deal(int16_t dx)
{
    if(mxs_calib){
        mxs_calib_counter += dx;  // 累计
        //MT3503_LOG_D("mxs_calib_counter = %d",mxs_calib_counter);
    }
    return mxs_calib_counter;
}

int mt3503_control(mt3503_cmd_type cmd, void *argv)
{
    switch (cmd)
    {
    case MT3503_CMD_STOP:       // 停止光旋钮
    case MT3503_CMD_POWER_DOWN: // 进入低功耗
        //MT3503_LOG_D("MT3503_CMD_STOP\n");
        #ifdef DRV_MT3503_TEST
            if (mt3503_thread && mt3503_event) rt_event_send(mt3503_event, MT3503_EVENT_STOP);  //发送STOP事件标志位
        #else
            mt3503_stop();
        #endif
        break;
    case MT3503_CMD_START:      // 启动光旋钮
        //MT3503_LOG_D("MT3503_CMD_START\n");
        #ifdef DRV_MT3503_TEST
            if (mt3503_thread && mt3503_event) rt_event_send(mt3503_event, MT3503_EVENT_START);  //发送START事件标志位
        #else
            mt3503_start();
        #endif
        break;
    case MT3503_CMD_CALIB_STOP: // 结束光旋钮校正
       #ifdef DRV_MT3503_TEST
            if (mt3503_thread && mt3503_event) rt_event_send(mt3503_event, MT3503_EVENT_CALIB_STOP); // 发送CALIB_STOP事件标志位
       #else
            mt3503_calib_stop();
       #endif
       break;
    case MT3503_CMD_CALIB_START:// 启动光旋钮校正
       #ifdef DRV_MT3503_TEST
            if (mt3503_thread && mt3503_event) rt_event_send(mt3503_event,MT3503_EVENT_CALIB_START); // 发送CALIB_START事件标志位
       #else
            mt3503_calib_start();
       #endif
       break;
    // 下列是可能带参数的，阻塞访问的
    case MT3503_CMD_SLEEP_MOD_SET:
        mt3503_sleep_mode_set(&dev_ctx, (mt3503_sleep_cfg_t *)argv);
        break;
    case MT3503_CMD_MOD_ENTER:
        mt3503_mode_enter(&dev_ctx, *(mt3503_mode_e *)argv);
        break;
    case MT3503_CMD_SET_RES_X:  // 校正
        mt3503_calib(*(uint16_t *)argv);
    case MT3503_CMD_READ_INFO:  // 单次读取数据
        mt3503_data_get(&dev_ctx, &data_xy);
        memcpy((mt3503_delta_xy_t*)argv,&data_xy,sizeof(mt3503_delta_xy_t));
        break;
    case MT3503_CMD_GET_DATA:   // 读取算法输出数据
        memcpy((mxs_algo_data_t)argv,&mt3053_data,sizeof(struct mxs_algo_data));
        break;
    case MT3503_CMD_GET_STATE:   // 读取工作状态
        *(bool*)argv = mt3503_iswork;
        break;
    default:
        break;
    }
    return RT_EOK;
}
//-------------------------------cmd测试函数------------------------------------------------
#include "bsp_board.h"
//测试所有对外接口
int cmd_mt3503(int argc, char **argv)
{
    int ret = 0;
    mt3503_delta_xy_t data_xy;
    if (argc < 2)
    {
        return -1;
    }
    if (strcmp(argv[1], "init") == 0)
    {
        ret = mt3503_init();
        if (ret != 0)
        {
            MT3503_LOG_D("mt3503_init init failed!");
        }
    }
    if (strcmp(argv[1], "deinit") == 0)
    {
        ret = mt3503_deinit();
        if (ret != 0)
        {
            MT3503_LOG_D("mt3503_deinit init failed!");
        }
    }
    else if (strcmp(argv[1], "start") == 0)
    {
        mt3503_control(MT3503_CMD_START, NULL);
    }
    else if (strcmp(argv[1], "stop") == 0)
    {
        mt3503_control(MT3503_CMD_STOP, NULL);
    }
    else if (strcmp(argv[1], "calib_start") == 0)
    {
        mt3503_control(MT3503_CMD_CALIB_START, NULL);
    }
    else if (strcmp(argv[1], "calib_stop") == 0)
    {
        mt3503_control(MT3503_CMD_CALIB_STOP, NULL);
    }
    else if (strcmp(argv[1], "powerdown") == 0)
    {
        mt3503_control(MT3503_CMD_POWER_DOWN, NULL);
    }
    else if (strcmp(argv[1], "mode_enter") == 0)
    {
        mt3503_cmd_type mode = atoi(argv[2]);
        mt3503_control(MT3503_CMD_MOD_ENTER, (mt3503_cmd_type *)&mode);
    }
    else if (strcmp(argv[1], "sleep_mode_set") == 0)
    {
        mt3503_sleep_cfg_t mode;
        mode.change_cfg = atoi(argv[2]);
        mode.sleep_enable.level = atoi(argv[3]);
        mode.sleep_enable.enanle = atoi(argv[4]);
        mode.sleep_cfg.etm = atoi(argv[5]);
        mode.sleep_cfg.freq = atoi(argv[6]);
        mt3503_control(MT3503_CMD_SLEEP_MOD_SET, (mt3503_sleep_cfg_t *)&mode);
    }
    else if (strcmp(argv[1], "read") == 0)
    {
        mt3503_delta_xy_t data;
        ret = mt3503_control(MT3503_CMD_READ_INFO, (void *)&data);
        if (ret != 0)
        {
            MT3503_LOG_D("mt3503 read xy data failed!");
        }
        else
        {
            MT3503_LOG_D("mt3503 data: X=%d,Y=%d", data.x, data.y);
        }
    }
    else if (strcmp(argv[1], "getdata") == 0)
    {
       struct mxs_algo_data data;
        ret = mt3503_control(MT3503_CMD_GET_DATA, (void *)&data);
        if (ret != 0)
        {
            MT3503_LOG_D("mt3503 read angle and speed data failed!");
        }
        else
        {
            MT3503_LOG_D("mt3503 data: angle=%d,speed=%d", data.angle, data.speed);
        }
    }
    else if (strcmp(argv[1], "calib") == 0)
    {
       uint16_t res_x = atoi(argv[2]);
       ret = mt3503_control(MT3503_CMD_SET_RES_X, (void *)&res_x);
       if (ret != 0)
       {
           MT3503_LOG_D("mt3503_res_x_set failed!");
       }
       else
       {
           MT3503_LOG_D("mt3503_res_x_set: res_x=%d", res_x);
       }
    }
    else
    {
        MT3503_LOG_D("Unsupported command: %s", argv[1]);
    }
    return 0;
}
FINSH_FUNCTION_EXPORT(cmd_mt3503, mt3503 test);
MSH_CMD_EXPORT(cmd_mt3503, mt3503 test);
#ifndef DRV_MT3503_TEST
/* 注册MT3503为操作系统KNOB设备 */
static struct rt_i2c_bus_device *ft_bus = NULL;
static struct knob_drivers mt3505_driver;
static rt_bool_t probe(void)
{
    rt_err_t err;
    ft_bus = (struct rt_i2c_bus_device *)rt_device_find(KNOB_DEVICE_NAME);
    if (RT_Device_Class_I2CBUS != ft_bus->parent.type)
    {
        ft_bus = NULL;
    }
    if (ft_bus)
    {
        rt_device_open((rt_device_t)ft_bus, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
    }
    else
    {
        LOG_I("bus not find\n");
        return RT_FALSE;
    }
    {
        struct rt_i2c_configuration configuration =
        {
            .mode = 0,
            .addr = 0,
            .timeout = 500,
            .max_hz  = 400000,
        };
        rt_i2c_configure(ft_bus, &configuration);
    }
    LOG_I("mt3505 probe OK");
    return RT_TRUE;
}
static struct knob_ops ops =
{
····mt3503_poll_init,
    mt3503_poll,
    mt3503_init,
    mt3503_deinit,
    mt3503_start,
    mt3503_stop,
    mt3503_calib,
    mt3503_calib_start,
    mt3503_calib_stop
};
static int rt_mt3505_init(void)
{
    mt3505_driver.probe = probe;
    mt3505_driver.ops = &ops;
    mt3505_driver.user_data = RT_NULL;
    rt_knob_drivers_register(&mt3505_driver);
    return 0;
}
INIT_COMPONENT_EXPORT(rt_mt3505_init);
#endif
#endif //BSP_USING_MT3503