#ifndef __AUDPRC_H
#define __AUDPRC_H

typedef struct
{
    __IO uint32_t ID;
    __IO uint32_t CFG;
    __IO uint32_t STB;
    __IO uint32_t IRQ;
    __IO uint32_t TX_CH0_CFG;
    __IO uint32_t TX_CH0_ENTRY;
    __IO uint32_t TX_CH1_CFG;
    __IO uint32_t TX_CH1_ENTRY;
    __IO uint32_t TX_CH2_CFG;
    __IO uint32_t TX_CH2_ENTRY;
    __IO uint32_t TX_CH3_CFG;
    __IO uint32_t TX_CH3_ENTRY;
    __IO uint32_t RX_CH0_CFG;
    __IO uint32_t RX_CH0_ENTRY;
    __IO uint32_t RX_CH1_CFG;
    __IO uint32_t RX_CH1_ENTRY;
    __IO uint32_t TX_OUT_CH0_CFG;
    __IO uint32_t TX_OUT_CH0_ENTRY;
    __IO uint32_t TX_OUT_CH1_CFG;
    __IO uint32_t TX_OUT_CH1_ENTRY;
    __IO uint32_t DAC_PATH_CFG0;
    __IO uint32_t DAC_PATH_CFG1;
    __IO uint32_t DAC_PATH_CFG2;
    __IO uint32_t DAC_PATH_CFG3;
    __IO uint32_t ADC_PATH_CFG0;
    __IO uint32_t RSVD1[3];
    __IO uint32_t DAC_EQ_CFG0;
    __IO uint32_t DAC_EQ_CFG1;
    __IO uint32_t DAC_EQ_CFG2;
    __IO uint32_t DAC_EQ_CFG3;
    __IO uint32_t DAC_EQ_CFG4;
    __IO uint32_t DAC_EQ_CFG5;
    __IO uint32_t DAC_EQ_CFG6;
    __IO uint32_t DAC_EQ_CFG7;
    __IO uint32_t DAC_EQ_CFG8;
    __IO uint32_t DAC_EQ_CFG9;
    __IO uint32_t DAC_EQ_CFG10;
    __IO uint32_t DAC_EQ_CFG11;
    __IO uint32_t DAC_EQ_CFG12;
    __IO uint32_t DAC_EQ_CFG13;
    __IO uint32_t DAC_EQ_CFG14;
    __IO uint32_t DAC_EQ_CFG15;
    __IO uint32_t DAC_EQ_CFG16;
    __IO uint32_t DAC_EQ_CFG17;
    __IO uint32_t DAC_EQ_CFG18;
    __IO uint32_t DAC_EQ_CFG19;
    __IO uint32_t DAC_EQ_CFG20;
    __IO uint32_t DAC_EQ_CFG21;
    __IO uint32_t DAC_EQ_CFG22;
    __IO uint32_t DAC_EQ_CFG23;
    __IO uint32_t DAC_EQ_CFG24;
    __IO uint32_t DAC_EQ_CFG25;
    __IO uint32_t DAC_EQ_CFG26;
    __IO uint32_t DAC_EQ_CFG27;
    __IO uint32_t DAC_EQ_CFG28;
    __IO uint32_t DAC_EQ_CFG29;
    __IO uint32_t DAC_EQ_CFG30;
    __IO uint32_t DAC_EQ_CFG31;
    __IO uint32_t DAC_EQ_CFG32;
    __IO uint32_t DAC_EQ_CFG33;
    __IO uint32_t DAC_EQ_CFG34;
    __IO uint32_t DAC_EQ_CFG35;
    __IO uint32_t DAC_EQ_CFG36;
    __IO uint32_t DAC_EQ_CFG37;
    __IO uint32_t DAC_EQ_CFG38;
    __IO uint32_t DAC_EQ_CFG39;
    __IO uint32_t DAC_EQ_CFG40;
    __IO uint32_t DAC_EQ_CFG41;
    __IO uint32_t DAC_EQ_CFG42;
    __IO uint32_t DAC_EQ_CFG43;
    __IO uint32_t DAC_EQ_CFG44;
    __IO uint32_t DAC_EQ_CFG45;
    __IO uint32_t DAC_EQ_CFG46;
    __IO uint32_t DAC_EQ_CFG47;
    __IO uint32_t DAC_EQ_CFG48;
    __IO uint32_t DAC_EQ_CFG49;
    __IO uint32_t RESERVED_IN;
    __IO uint32_t RESERVED_OUT;
} AUDPRC_TypeDef;


/******************* Bit definition for AUDPRC_ID register ********************/
#define AUDPRC_ID_REV_Pos               (0U)
#define AUDPRC_ID_REV_Msk               (0xFFFFFFFFUL << AUDPRC_ID_REV_Pos)
#define AUDPRC_ID_REV                   AUDPRC_ID_REV_Msk

/******************* Bit definition for AUDPRC_CFG register *******************/
#define AUDPRC_CFG_ENABLE_Pos           (0U)
#define AUDPRC_CFG_ENABLE_Msk           (0x1UL << AUDPRC_CFG_ENABLE_Pos)
#define AUDPRC_CFG_ENABLE               AUDPRC_CFG_ENABLE_Msk
#define AUDPRC_CFG_SRESET_Pos           (1U)
#define AUDPRC_CFG_SRESET_Msk           (0x1UL << AUDPRC_CFG_SRESET_Pos)
#define AUDPRC_CFG_SRESET               AUDPRC_CFG_SRESET_Msk
#define AUDPRC_CFG_DAC_PATH_FLUSH_Pos   (2U)
#define AUDPRC_CFG_DAC_PATH_FLUSH_Msk   (0x1UL << AUDPRC_CFG_DAC_PATH_FLUSH_Pos)
#define AUDPRC_CFG_DAC_PATH_FLUSH       AUDPRC_CFG_DAC_PATH_FLUSH_Msk
#define AUDPRC_CFG_ADC_PATH_FLUSH_Pos   (3U)
#define AUDPRC_CFG_ADC_PATH_FLUSH_Msk   (0x1UL << AUDPRC_CFG_ADC_PATH_FLUSH_Pos)
#define AUDPRC_CFG_ADC_PATH_FLUSH       AUDPRC_CFG_ADC_PATH_FLUSH_Msk
#define AUDPRC_CFG_DAC_PATH_SRESET_Pos  (4U)
#define AUDPRC_CFG_DAC_PATH_SRESET_Msk  (0x1UL << AUDPRC_CFG_DAC_PATH_SRESET_Pos)
#define AUDPRC_CFG_DAC_PATH_SRESET      AUDPRC_CFG_DAC_PATH_SRESET_Msk
#define AUDPRC_CFG_ADC_PATH_SRESET_Pos  (5U)
#define AUDPRC_CFG_ADC_PATH_SRESET_Msk  (0x1UL << AUDPRC_CFG_ADC_PATH_SRESET_Pos)
#define AUDPRC_CFG_ADC_PATH_SRESET      AUDPRC_CFG_ADC_PATH_SRESET_Msk
#define AUDPRC_CFG_DAC_PATH_EN_Pos      (6U)
#define AUDPRC_CFG_DAC_PATH_EN_Msk      (0x1UL << AUDPRC_CFG_DAC_PATH_EN_Pos)
#define AUDPRC_CFG_DAC_PATH_EN          AUDPRC_CFG_DAC_PATH_EN_Msk
#define AUDPRC_CFG_ADC_PATH_EN_Pos      (7U)
#define AUDPRC_CFG_ADC_PATH_EN_Msk      (0x1UL << AUDPRC_CFG_ADC_PATH_EN_Pos)
#define AUDPRC_CFG_ADC_PATH_EN          AUDPRC_CFG_ADC_PATH_EN_Msk
#define AUDPRC_CFG_AUTO_GATE_EN_Pos     (8U)
#define AUDPRC_CFG_AUTO_GATE_EN_Msk     (0x1UL << AUDPRC_CFG_AUTO_GATE_EN_Pos)
#define AUDPRC_CFG_AUTO_GATE_EN         AUDPRC_CFG_AUTO_GATE_EN_Msk
#define AUDPRC_CFG_STB_CLK_SEL_Pos      (9U)
#define AUDPRC_CFG_STB_CLK_SEL_Msk      (0x1UL << AUDPRC_CFG_STB_CLK_SEL_Pos)
#define AUDPRC_CFG_STB_CLK_SEL          AUDPRC_CFG_STB_CLK_SEL_Msk
#define AUDPRC_CFG_AUDCLK_DIV_Pos       (16U)
#define AUDPRC_CFG_AUDCLK_DIV_Msk       (0xFUL << AUDPRC_CFG_AUDCLK_DIV_Pos)
#define AUDPRC_CFG_AUDCLK_DIV           AUDPRC_CFG_AUDCLK_DIV_Msk
#define AUDPRC_CFG_AUDCLK_DIV_UPDATE_Pos  (20U)
#define AUDPRC_CFG_AUDCLK_DIV_UPDATE_Msk  (0x1UL << AUDPRC_CFG_AUDCLK_DIV_UPDATE_Pos)
#define AUDPRC_CFG_AUDCLK_DIV_UPDATE    AUDPRC_CFG_AUDCLK_DIV_UPDATE_Msk

/******************* Bit definition for AUDPRC_STB register *******************/
#define AUDPRC_STB_DAC_DIV_Pos          (0U)
#define AUDPRC_STB_DAC_DIV_Msk          (0xFFFFUL << AUDPRC_STB_DAC_DIV_Pos)
#define AUDPRC_STB_DAC_DIV              AUDPRC_STB_DAC_DIV_Msk
#define AUDPRC_STB_ADC_DIV_Pos          (16U)
#define AUDPRC_STB_ADC_DIV_Msk          (0xFFFFUL << AUDPRC_STB_ADC_DIV_Pos)
#define AUDPRC_STB_ADC_DIV              AUDPRC_STB_ADC_DIV_Msk

/******************* Bit definition for AUDPRC_IRQ register *******************/
#define AUDPRC_IRQ_TX0_FIFO_OF_Pos      (0U)
#define AUDPRC_IRQ_TX0_FIFO_OF_Msk      (0x1UL << AUDPRC_IRQ_TX0_FIFO_OF_Pos)
#define AUDPRC_IRQ_TX0_FIFO_OF          AUDPRC_IRQ_TX0_FIFO_OF_Msk
#define AUDPRC_IRQ_TX1_FIFO_OF_Pos      (1U)
#define AUDPRC_IRQ_TX1_FIFO_OF_Msk      (0x1UL << AUDPRC_IRQ_TX1_FIFO_OF_Pos)
#define AUDPRC_IRQ_TX1_FIFO_OF          AUDPRC_IRQ_TX1_FIFO_OF_Msk
#define AUDPRC_IRQ_TX2_FIFO_OF_Pos      (2U)
#define AUDPRC_IRQ_TX2_FIFO_OF_Msk      (0x1UL << AUDPRC_IRQ_TX2_FIFO_OF_Pos)
#define AUDPRC_IRQ_TX2_FIFO_OF          AUDPRC_IRQ_TX2_FIFO_OF_Msk
#define AUDPRC_IRQ_TX3_FIFO_OF_Pos      (3U)
#define AUDPRC_IRQ_TX3_FIFO_OF_Msk      (0x1UL << AUDPRC_IRQ_TX3_FIFO_OF_Pos)
#define AUDPRC_IRQ_TX3_FIFO_OF          AUDPRC_IRQ_TX3_FIFO_OF_Msk
#define AUDPRC_IRQ_RX0_FIFO_UF_Pos      (4U)
#define AUDPRC_IRQ_RX0_FIFO_UF_Msk      (0x1UL << AUDPRC_IRQ_RX0_FIFO_UF_Pos)
#define AUDPRC_IRQ_RX0_FIFO_UF          AUDPRC_IRQ_RX0_FIFO_UF_Msk
#define AUDPRC_IRQ_RX1_FIFO_UF_Pos      (5U)
#define AUDPRC_IRQ_RX1_FIFO_UF_Msk      (0x1UL << AUDPRC_IRQ_RX1_FIFO_UF_Pos)
#define AUDPRC_IRQ_RX1_FIFO_UF          AUDPRC_IRQ_RX1_FIFO_UF_Msk
#define AUDPRC_IRQ_TX_OUT_FIFO_UF_Pos   (6U)
#define AUDPRC_IRQ_TX_OUT_FIFO_UF_Msk   (0x1UL << AUDPRC_IRQ_TX_OUT_FIFO_UF_Pos)
#define AUDPRC_IRQ_TX_OUT_FIFO_UF       AUDPRC_IRQ_TX_OUT_FIFO_UF_Msk
#define AUDPRC_IRQ_RX_IN_FIFO_OF_Pos    (7U)
#define AUDPRC_IRQ_RX_IN_FIFO_OF_Msk    (0x1UL << AUDPRC_IRQ_RX_IN_FIFO_OF_Pos)
#define AUDPRC_IRQ_RX_IN_FIFO_OF        AUDPRC_IRQ_RX_IN_FIFO_OF_Msk
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF_Pos  (8U)
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF_Msk  (0x1UL << AUDPRC_IRQ_TX_OUT0_FIFO_UF_Pos)
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF      AUDPRC_IRQ_TX_OUT0_FIFO_UF_Msk
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF_Pos  (9U)
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF_Msk  (0x1UL << AUDPRC_IRQ_TX_OUT1_FIFO_UF_Pos)
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF      AUDPRC_IRQ_TX_OUT1_FIFO_UF_Msk
#define AUDPRC_IRQ_TX0_FIFO_OF_MASK_Pos  (16U)
#define AUDPRC_IRQ_TX0_FIFO_OF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX0_FIFO_OF_MASK_Pos)
#define AUDPRC_IRQ_TX0_FIFO_OF_MASK     AUDPRC_IRQ_TX0_FIFO_OF_MASK_Msk
#define AUDPRC_IRQ_TX1_FIFO_OF_MASK_Pos  (17U)
#define AUDPRC_IRQ_TX1_FIFO_OF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX1_FIFO_OF_MASK_Pos)
#define AUDPRC_IRQ_TX1_FIFO_OF_MASK     AUDPRC_IRQ_TX1_FIFO_OF_MASK_Msk
#define AUDPRC_IRQ_TX2_FIFO_OF_MASK_Pos  (18U)
#define AUDPRC_IRQ_TX2_FIFO_OF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX2_FIFO_OF_MASK_Pos)
#define AUDPRC_IRQ_TX2_FIFO_OF_MASK     AUDPRC_IRQ_TX2_FIFO_OF_MASK_Msk
#define AUDPRC_IRQ_TX3_FIFO_OF_MASK_Pos  (19U)
#define AUDPRC_IRQ_TX3_FIFO_OF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX3_FIFO_OF_MASK_Pos)
#define AUDPRC_IRQ_TX3_FIFO_OF_MASK     AUDPRC_IRQ_TX3_FIFO_OF_MASK_Msk
#define AUDPRC_IRQ_RX0_FIFO_UF_MASK_Pos  (20U)
#define AUDPRC_IRQ_RX0_FIFO_UF_MASK_Msk  (0x1UL << AUDPRC_IRQ_RX0_FIFO_UF_MASK_Pos)
#define AUDPRC_IRQ_RX0_FIFO_UF_MASK     AUDPRC_IRQ_RX0_FIFO_UF_MASK_Msk
#define AUDPRC_IRQ_RX1_FIFO_UF_MASK_Pos  (21U)
#define AUDPRC_IRQ_RX1_FIFO_UF_MASK_Msk  (0x1UL << AUDPRC_IRQ_RX1_FIFO_UF_MASK_Pos)
#define AUDPRC_IRQ_RX1_FIFO_UF_MASK     AUDPRC_IRQ_RX1_FIFO_UF_MASK_Msk
#define AUDPRC_IRQ_TX_OUT_FIFO_UF_MASK_Pos  (22U)
#define AUDPRC_IRQ_TX_OUT_FIFO_UF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX_OUT_FIFO_UF_MASK_Pos)
#define AUDPRC_IRQ_TX_OUT_FIFO_UF_MASK  AUDPRC_IRQ_TX_OUT_FIFO_UF_MASK_Msk
#define AUDPRC_IRQ_RX_IN_FIFO_OF_MASK_Pos  (23U)
#define AUDPRC_IRQ_RX_IN_FIFO_OF_MASK_Msk  (0x1UL << AUDPRC_IRQ_RX_IN_FIFO_OF_MASK_Pos)
#define AUDPRC_IRQ_RX_IN_FIFO_OF_MASK   AUDPRC_IRQ_RX_IN_FIFO_OF_MASK_Msk
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF_MASK_Pos  (24U)
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX_OUT0_FIFO_UF_MASK_Pos)
#define AUDPRC_IRQ_TX_OUT0_FIFO_UF_MASK  AUDPRC_IRQ_TX_OUT0_FIFO_UF_MASK_Msk
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF_MASK_Pos  (25U)
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF_MASK_Msk  (0x1UL << AUDPRC_IRQ_TX_OUT1_FIFO_UF_MASK_Pos)
#define AUDPRC_IRQ_TX_OUT1_FIFO_UF_MASK  AUDPRC_IRQ_TX_OUT1_FIFO_UF_MASK_Msk

/*************** Bit definition for AUDPRC_TX_CH0_CFG register ****************/
#define AUDPRC_TX_CH0_CFG_ENABLE_Pos    (0U)
#define AUDPRC_TX_CH0_CFG_ENABLE_Msk    (0x1UL << AUDPRC_TX_CH0_CFG_ENABLE_Pos)
#define AUDPRC_TX_CH0_CFG_ENABLE        AUDPRC_TX_CH0_CFG_ENABLE_Msk
#define AUDPRC_TX_CH0_CFG_FORMAT_Pos    (1U)
#define AUDPRC_TX_CH0_CFG_FORMAT_Msk    (0x1UL << AUDPRC_TX_CH0_CFG_FORMAT_Pos)
#define AUDPRC_TX_CH0_CFG_FORMAT        AUDPRC_TX_CH0_CFG_FORMAT_Msk
#define AUDPRC_TX_CH0_CFG_MODE_Pos      (2U)
#define AUDPRC_TX_CH0_CFG_MODE_Msk      (0x1UL << AUDPRC_TX_CH0_CFG_MODE_Pos)
#define AUDPRC_TX_CH0_CFG_MODE          AUDPRC_TX_CH0_CFG_MODE_Msk
#define AUDPRC_TX_CH0_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_TX_CH0_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_TX_CH0_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_CH0_CFG_DMA_MSK       AUDPRC_TX_CH0_CFG_DMA_MSK_Msk
#define AUDPRC_TX_CH0_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_CH0_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_CH0_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_CH0_CFG_FIFO_CNT      AUDPRC_TX_CH0_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_TX_CH0_ENTRY register ***************/
#define AUDPRC_TX_CH0_ENTRY_DATA_Pos    (0U)
#define AUDPRC_TX_CH0_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_TX_CH0_ENTRY_DATA_Pos)
#define AUDPRC_TX_CH0_ENTRY_DATA        AUDPRC_TX_CH0_ENTRY_DATA_Msk

/*************** Bit definition for AUDPRC_TX_CH1_CFG register ****************/
#define AUDPRC_TX_CH1_CFG_ENABLE_Pos    (0U)
#define AUDPRC_TX_CH1_CFG_ENABLE_Msk    (0x1UL << AUDPRC_TX_CH1_CFG_ENABLE_Pos)
#define AUDPRC_TX_CH1_CFG_ENABLE        AUDPRC_TX_CH1_CFG_ENABLE_Msk
#define AUDPRC_TX_CH1_CFG_FORMAT_Pos    (1U)
#define AUDPRC_TX_CH1_CFG_FORMAT_Msk    (0x1UL << AUDPRC_TX_CH1_CFG_FORMAT_Pos)
#define AUDPRC_TX_CH1_CFG_FORMAT        AUDPRC_TX_CH1_CFG_FORMAT_Msk
#define AUDPRC_TX_CH1_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_TX_CH1_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_TX_CH1_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_CH1_CFG_DMA_MSK       AUDPRC_TX_CH1_CFG_DMA_MSK_Msk
#define AUDPRC_TX_CH1_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_CH1_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_CH1_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_CH1_CFG_FIFO_CNT      AUDPRC_TX_CH1_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_TX_CH1_ENTRY register ***************/
#define AUDPRC_TX_CH1_ENTRY_DATA_Pos    (0U)
#define AUDPRC_TX_CH1_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_TX_CH1_ENTRY_DATA_Pos)
#define AUDPRC_TX_CH1_ENTRY_DATA        AUDPRC_TX_CH1_ENTRY_DATA_Msk

/*************** Bit definition for AUDPRC_TX_CH2_CFG register ****************/
#define AUDPRC_TX_CH2_CFG_ENABLE_Pos    (0U)
#define AUDPRC_TX_CH2_CFG_ENABLE_Msk    (0x1UL << AUDPRC_TX_CH2_CFG_ENABLE_Pos)
#define AUDPRC_TX_CH2_CFG_ENABLE        AUDPRC_TX_CH2_CFG_ENABLE_Msk
#define AUDPRC_TX_CH2_CFG_FORMAT_Pos    (1U)
#define AUDPRC_TX_CH2_CFG_FORMAT_Msk    (0x1UL << AUDPRC_TX_CH2_CFG_FORMAT_Pos)
#define AUDPRC_TX_CH2_CFG_FORMAT        AUDPRC_TX_CH2_CFG_FORMAT_Msk
#define AUDPRC_TX_CH2_CFG_MODE_Pos      (2U)
#define AUDPRC_TX_CH2_CFG_MODE_Msk      (0x1UL << AUDPRC_TX_CH2_CFG_MODE_Pos)
#define AUDPRC_TX_CH2_CFG_MODE          AUDPRC_TX_CH2_CFG_MODE_Msk
#define AUDPRC_TX_CH2_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_TX_CH2_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_TX_CH2_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_CH2_CFG_DMA_MSK       AUDPRC_TX_CH2_CFG_DMA_MSK_Msk
#define AUDPRC_TX_CH2_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_CH2_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_CH2_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_CH2_CFG_FIFO_CNT      AUDPRC_TX_CH2_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_TX_CH2_ENTRY register ***************/
#define AUDPRC_TX_CH2_ENTRY_DATA_Pos    (0U)
#define AUDPRC_TX_CH2_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_TX_CH2_ENTRY_DATA_Pos)
#define AUDPRC_TX_CH2_ENTRY_DATA        AUDPRC_TX_CH2_ENTRY_DATA_Msk

/*************** Bit definition for AUDPRC_TX_CH3_CFG register ****************/
#define AUDPRC_TX_CH3_CFG_ENABLE_Pos    (0U)
#define AUDPRC_TX_CH3_CFG_ENABLE_Msk    (0x1UL << AUDPRC_TX_CH3_CFG_ENABLE_Pos)
#define AUDPRC_TX_CH3_CFG_ENABLE        AUDPRC_TX_CH3_CFG_ENABLE_Msk
#define AUDPRC_TX_CH3_CFG_FORMAT_Pos    (1U)
#define AUDPRC_TX_CH3_CFG_FORMAT_Msk    (0x1UL << AUDPRC_TX_CH3_CFG_FORMAT_Pos)
#define AUDPRC_TX_CH3_CFG_FORMAT        AUDPRC_TX_CH3_CFG_FORMAT_Msk
#define AUDPRC_TX_CH3_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_TX_CH3_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_TX_CH3_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_CH3_CFG_DMA_MSK       AUDPRC_TX_CH3_CFG_DMA_MSK_Msk
#define AUDPRC_TX_CH3_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_CH3_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_CH3_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_CH3_CFG_FIFO_CNT      AUDPRC_TX_CH3_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_TX_CH3_ENTRY register ***************/
#define AUDPRC_TX_CH3_ENTRY_DATA_Pos    (0U)
#define AUDPRC_TX_CH3_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_TX_CH3_ENTRY_DATA_Pos)
#define AUDPRC_TX_CH3_ENTRY_DATA        AUDPRC_TX_CH3_ENTRY_DATA_Msk

/*************** Bit definition for AUDPRC_RX_CH0_CFG register ****************/
#define AUDPRC_RX_CH0_CFG_ENABLE_Pos    (0U)
#define AUDPRC_RX_CH0_CFG_ENABLE_Msk    (0x1UL << AUDPRC_RX_CH0_CFG_ENABLE_Pos)
#define AUDPRC_RX_CH0_CFG_ENABLE        AUDPRC_RX_CH0_CFG_ENABLE_Msk
#define AUDPRC_RX_CH0_CFG_FORMAT_Pos    (1U)
#define AUDPRC_RX_CH0_CFG_FORMAT_Msk    (0x1UL << AUDPRC_RX_CH0_CFG_FORMAT_Pos)
#define AUDPRC_RX_CH0_CFG_FORMAT        AUDPRC_RX_CH0_CFG_FORMAT_Msk
#define AUDPRC_RX_CH0_CFG_MODE_Pos      (2U)
#define AUDPRC_RX_CH0_CFG_MODE_Msk      (0x1UL << AUDPRC_RX_CH0_CFG_MODE_Pos)
#define AUDPRC_RX_CH0_CFG_MODE          AUDPRC_RX_CH0_CFG_MODE_Msk
#define AUDPRC_RX_CH0_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_RX_CH0_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_RX_CH0_CFG_DMA_MSK_Pos)
#define AUDPRC_RX_CH0_CFG_DMA_MSK       AUDPRC_RX_CH0_CFG_DMA_MSK_Msk
#define AUDPRC_RX_CH0_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_RX_CH0_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_RX_CH0_CFG_FIFO_CNT_Pos)
#define AUDPRC_RX_CH0_CFG_FIFO_CNT      AUDPRC_RX_CH0_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_RX_CH0_ENTRY register ***************/
#define AUDPRC_RX_CH0_ENTRY_DATA_Pos    (0U)
#define AUDPRC_RX_CH0_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_RX_CH0_ENTRY_DATA_Pos)
#define AUDPRC_RX_CH0_ENTRY_DATA        AUDPRC_RX_CH0_ENTRY_DATA_Msk

/*************** Bit definition for AUDPRC_RX_CH1_CFG register ****************/
#define AUDPRC_RX_CH1_CFG_ENABLE_Pos    (0U)
#define AUDPRC_RX_CH1_CFG_ENABLE_Msk    (0x1UL << AUDPRC_RX_CH1_CFG_ENABLE_Pos)
#define AUDPRC_RX_CH1_CFG_ENABLE        AUDPRC_RX_CH1_CFG_ENABLE_Msk
#define AUDPRC_RX_CH1_CFG_FORMAT_Pos    (1U)
#define AUDPRC_RX_CH1_CFG_FORMAT_Msk    (0x1UL << AUDPRC_RX_CH1_CFG_FORMAT_Pos)
#define AUDPRC_RX_CH1_CFG_FORMAT        AUDPRC_RX_CH1_CFG_FORMAT_Msk
#define AUDPRC_RX_CH1_CFG_DMA_MSK_Pos   (3U)
#define AUDPRC_RX_CH1_CFG_DMA_MSK_Msk   (0x1UL << AUDPRC_RX_CH1_CFG_DMA_MSK_Pos)
#define AUDPRC_RX_CH1_CFG_DMA_MSK       AUDPRC_RX_CH1_CFG_DMA_MSK_Msk
#define AUDPRC_RX_CH1_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_RX_CH1_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_RX_CH1_CFG_FIFO_CNT_Pos)
#define AUDPRC_RX_CH1_CFG_FIFO_CNT      AUDPRC_RX_CH1_CFG_FIFO_CNT_Msk

/************** Bit definition for AUDPRC_RX_CH1_ENTRY register ***************/
#define AUDPRC_RX_CH1_ENTRY_DATA_Pos    (0U)
#define AUDPRC_RX_CH1_ENTRY_DATA_Msk    (0xFFFFFFFFUL << AUDPRC_RX_CH1_ENTRY_DATA_Pos)
#define AUDPRC_RX_CH1_ENTRY_DATA        AUDPRC_RX_CH1_ENTRY_DATA_Msk

/************* Bit definition for AUDPRC_TX_OUT_CH0_CFG register **************/
#define AUDPRC_TX_OUT_CH0_CFG_ENABLE_Pos  (0U)
#define AUDPRC_TX_OUT_CH0_CFG_ENABLE_Msk  (0x1UL << AUDPRC_TX_OUT_CH0_CFG_ENABLE_Pos)
#define AUDPRC_TX_OUT_CH0_CFG_ENABLE    AUDPRC_TX_OUT_CH0_CFG_ENABLE_Msk
#define AUDPRC_TX_OUT_CH0_CFG_FORMAT_Pos  (1U)
#define AUDPRC_TX_OUT_CH0_CFG_FORMAT_Msk  (0x1UL << AUDPRC_TX_OUT_CH0_CFG_FORMAT_Pos)
#define AUDPRC_TX_OUT_CH0_CFG_FORMAT    AUDPRC_TX_OUT_CH0_CFG_FORMAT_Msk
#define AUDPRC_TX_OUT_CH0_CFG_MODE_Pos  (2U)
#define AUDPRC_TX_OUT_CH0_CFG_MODE_Msk  (0x1UL << AUDPRC_TX_OUT_CH0_CFG_MODE_Pos)
#define AUDPRC_TX_OUT_CH0_CFG_MODE      AUDPRC_TX_OUT_CH0_CFG_MODE_Msk
#define AUDPRC_TX_OUT_CH0_CFG_DMA_MSK_Pos  (3U)
#define AUDPRC_TX_OUT_CH0_CFG_DMA_MSK_Msk  (0x1UL << AUDPRC_TX_OUT_CH0_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_OUT_CH0_CFG_DMA_MSK   AUDPRC_TX_OUT_CH0_CFG_DMA_MSK_Msk
#define AUDPRC_TX_OUT_CH0_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_OUT_CH0_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_OUT_CH0_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_OUT_CH0_CFG_FIFO_CNT  AUDPRC_TX_OUT_CH0_CFG_FIFO_CNT_Msk

/************ Bit definition for AUDPRC_TX_OUT_CH0_ENTRY register *************/
#define AUDPRC_TX_OUT_CH0_ENTRY_DATA_Pos  (0U)
#define AUDPRC_TX_OUT_CH0_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDPRC_TX_OUT_CH0_ENTRY_DATA_Pos)
#define AUDPRC_TX_OUT_CH0_ENTRY_DATA    AUDPRC_TX_OUT_CH0_ENTRY_DATA_Msk

/************* Bit definition for AUDPRC_TX_OUT_CH1_CFG register **************/
#define AUDPRC_TX_OUT_CH1_CFG_ENABLE_Pos  (0U)
#define AUDPRC_TX_OUT_CH1_CFG_ENABLE_Msk  (0x1UL << AUDPRC_TX_OUT_CH1_CFG_ENABLE_Pos)
#define AUDPRC_TX_OUT_CH1_CFG_ENABLE    AUDPRC_TX_OUT_CH1_CFG_ENABLE_Msk
#define AUDPRC_TX_OUT_CH1_CFG_FORMAT_Pos  (1U)
#define AUDPRC_TX_OUT_CH1_CFG_FORMAT_Msk  (0x1UL << AUDPRC_TX_OUT_CH1_CFG_FORMAT_Pos)
#define AUDPRC_TX_OUT_CH1_CFG_FORMAT    AUDPRC_TX_OUT_CH1_CFG_FORMAT_Msk
#define AUDPRC_TX_OUT_CH1_CFG_DMA_MSK_Pos  (3U)
#define AUDPRC_TX_OUT_CH1_CFG_DMA_MSK_Msk  (0x1UL << AUDPRC_TX_OUT_CH1_CFG_DMA_MSK_Pos)
#define AUDPRC_TX_OUT_CH1_CFG_DMA_MSK   AUDPRC_TX_OUT_CH1_CFG_DMA_MSK_Msk
#define AUDPRC_TX_OUT_CH1_CFG_FIFO_CNT_Pos  (4U)
#define AUDPRC_TX_OUT_CH1_CFG_FIFO_CNT_Msk  (0xFUL << AUDPRC_TX_OUT_CH1_CFG_FIFO_CNT_Pos)
#define AUDPRC_TX_OUT_CH1_CFG_FIFO_CNT  AUDPRC_TX_OUT_CH1_CFG_FIFO_CNT_Msk

/************ Bit definition for AUDPRC_TX_OUT_CH1_ENTRY register *************/
#define AUDPRC_TX_OUT_CH1_ENTRY_DATA_Pos  (0U)
#define AUDPRC_TX_OUT_CH1_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDPRC_TX_OUT_CH1_ENTRY_DATA_Pos)
#define AUDPRC_TX_OUT_CH1_ENTRY_DATA    AUDPRC_TX_OUT_CH1_ENTRY_DATA_Msk

/************** Bit definition for AUDPRC_DAC_PATH_CFG0 register **************/
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_L_Pos  (0U)
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_L_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_L_Pos)
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_L  AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_L_Msk
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_L_Pos  (4U)
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_L_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG0_FINE_VOL_L_Pos)
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_L  AUDPRC_DAC_PATH_CFG0_FINE_VOL_L_Msk
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_R_Pos  (8U)
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_R_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_R_Pos)
#define AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_R  AUDPRC_DAC_PATH_CFG0_ROUGH_VOL_R_Msk
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_R_Pos  (12U)
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_R_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG0_FINE_VOL_R_Pos)
#define AUDPRC_DAC_PATH_CFG0_FINE_VOL_R  AUDPRC_DAC_PATH_CFG0_FINE_VOL_R_Msk
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC0_Pos  (16U)
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC0_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG0_MIXLSRC0_Pos)
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC0   AUDPRC_DAC_PATH_CFG0_MIXLSRC0_Msk
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC1_Pos  (19U)
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC1_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG0_MIXLSRC1_Pos)
#define AUDPRC_DAC_PATH_CFG0_MIXLSRC1   AUDPRC_DAC_PATH_CFG0_MIXLSRC1_Msk
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC0_Pos  (22U)
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC0_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG0_MIXRSRC0_Pos)
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC0   AUDPRC_DAC_PATH_CFG0_MIXRSRC0_Msk
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC1_Pos  (25U)
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC1_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG0_MIXRSRC1_Pos)
#define AUDPRC_DAC_PATH_CFG0_MIXRSRC1   AUDPRC_DAC_PATH_CFG0_MIXRSRC1_Msk
#define AUDPRC_DAC_PATH_CFG0_DST_SEL_Pos  (28U)
#define AUDPRC_DAC_PATH_CFG0_DST_SEL_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG0_DST_SEL_Pos)
#define AUDPRC_DAC_PATH_CFG0_DST_SEL    AUDPRC_DAC_PATH_CFG0_DST_SEL_Msk

/************** Bit definition for AUDPRC_DAC_PATH_CFG1 register **************/
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC0_Pos  (0U)
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC0_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG1_MUXLSRC0_Pos)
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC0   AUDPRC_DAC_PATH_CFG1_MUXLSRC0_Msk
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC1_Pos  (3U)
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC1_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG1_MUXLSRC1_Pos)
#define AUDPRC_DAC_PATH_CFG1_MUXLSRC1   AUDPRC_DAC_PATH_CFG1_MUXLSRC1_Msk
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC0_Pos  (6U)
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC0_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG1_MUXRSRC0_Pos)
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC0   AUDPRC_DAC_PATH_CFG1_MUXRSRC0_Msk
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC1_Pos  (9U)
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC1_Msk  (0x7UL << AUDPRC_DAC_PATH_CFG1_MUXRSRC1_Pos)
#define AUDPRC_DAC_PATH_CFG1_MUXRSRC1   AUDPRC_DAC_PATH_CFG1_MUXRSRC1_Msk
#define AUDPRC_DAC_PATH_CFG1_EQ_CH_EN_Pos  (12U)
#define AUDPRC_DAC_PATH_CFG1_EQ_CH_EN_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG1_EQ_CH_EN_Pos)
#define AUDPRC_DAC_PATH_CFG1_EQ_CH_EN   AUDPRC_DAC_PATH_CFG1_EQ_CH_EN_Msk
#define AUDPRC_DAC_PATH_CFG1_EQ_STAGE_Pos  (14U)
#define AUDPRC_DAC_PATH_CFG1_EQ_STAGE_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG1_EQ_STAGE_Pos)
#define AUDPRC_DAC_PATH_CFG1_EQ_STAGE   AUDPRC_DAC_PATH_CFG1_EQ_STAGE_Msk
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR_DONE_Pos  (18U)
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR_DONE_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_EQ_CLR_DONE_Pos)
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR_DONE  AUDPRC_DAC_PATH_CFG1_EQ_CLR_DONE_Msk
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR_Pos  (19U)
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_EQ_CLR_Pos)
#define AUDPRC_DAC_PATH_CFG1_EQ_CLR     AUDPRC_DAC_PATH_CFG1_EQ_CLR_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_EN_Pos  (20U)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_EN_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG1_SRC_CH_EN_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_EN  AUDPRC_DAC_PATH_CFG1_SRC_CH_EN_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_EN_Pos  (22U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_EN_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF1_EN_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_EN  AUDPRC_DAC_PATH_CFG1_SRC_HBF1_EN_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_MODE_Pos  (23U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_MODE_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF1_MODE_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF1_MODE  AUDPRC_DAC_PATH_CFG1_SRC_HBF1_MODE_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_EN_Pos  (24U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_EN_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF2_EN_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_EN  AUDPRC_DAC_PATH_CFG1_SRC_HBF2_EN_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_MODE_Pos  (25U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_MODE_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF2_MODE_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF2_MODE  AUDPRC_DAC_PATH_CFG1_SRC_HBF2_MODE_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_EN_Pos  (26U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_EN_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF3_EN_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_EN  AUDPRC_DAC_PATH_CFG1_SRC_HBF3_EN_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_MODE_Pos  (27U)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_MODE_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG1_SRC_HBF3_MODE_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_HBF3_MODE  AUDPRC_DAC_PATH_CFG1_SRC_HBF3_MODE_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_DONE_Pos  (28U)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_DONE_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_DONE_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_DONE  AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_DONE_Msk
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_Pos  (30U)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_Pos)
#define AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR  AUDPRC_DAC_PATH_CFG1_SRC_CH_CLR_Msk

/************** Bit definition for AUDPRC_DAC_PATH_CFG2 register **************/
#define AUDPRC_DAC_PATH_CFG2_SINC_RATIO_Pos  (0U)
#define AUDPRC_DAC_PATH_CFG2_SINC_RATIO_Msk  (0x7FFFFFFFUL << AUDPRC_DAC_PATH_CFG2_SINC_RATIO_Pos)
#define AUDPRC_DAC_PATH_CFG2_SINC_RATIO  AUDPRC_DAC_PATH_CFG2_SINC_RATIO_Msk
#define AUDPRC_DAC_PATH_CFG2_SRC_SINC_EN_Pos  (31U)
#define AUDPRC_DAC_PATH_CFG2_SRC_SINC_EN_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG2_SRC_SINC_EN_Pos)
#define AUDPRC_DAC_PATH_CFG2_SRC_SINC_EN  AUDPRC_DAC_PATH_CFG2_SRC_SINC_EN_Msk

/************** Bit definition for AUDPRC_DAC_PATH_CFG3 register **************/
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_L_Pos  (0U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_L_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_RAMP_EN_L_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_L  AUDPRC_DAC_PATH_CFG3_RAMP_EN_L_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_L_Pos  (1U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_L_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_RAMP_MODE_L_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_L  AUDPRC_DAC_PATH_CFG3_RAMP_MODE_L_Msk
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_L_Pos  (2U)
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_L_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_L_Pos)
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_L  AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_L_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_L_Pos  (3U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_L_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_L_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_L  AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_L_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_L_Pos  (7U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_L_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG3_RAMP_STAT_L_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_L  AUDPRC_DAC_PATH_CFG3_RAMP_STAT_L_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_R_Pos  (9U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_R_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_RAMP_EN_R_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_EN_R  AUDPRC_DAC_PATH_CFG3_RAMP_EN_R_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_R_Pos  (10U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_R_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_RAMP_MODE_R_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_MODE_R  AUDPRC_DAC_PATH_CFG3_RAMP_MODE_R_Msk
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_R_Pos  (11U)
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_R_Msk  (0x1UL << AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_R_Pos)
#define AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_R  AUDPRC_DAC_PATH_CFG3_ZERO_ADJUST_EN_R_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_R_Pos  (12U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_R_Msk  (0xFUL << AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_R_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_R  AUDPRC_DAC_PATH_CFG3_RAMP_INTERVAL_R_Msk
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_R_Pos  (16U)
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_R_Msk  (0x3UL << AUDPRC_DAC_PATH_CFG3_RAMP_STAT_R_Pos)
#define AUDPRC_DAC_PATH_CFG3_RAMP_STAT_R  AUDPRC_DAC_PATH_CFG3_RAMP_STAT_R_Msk

/************** Bit definition for AUDPRC_ADC_PATH_CFG0 register **************/
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_L_Pos  (0U)
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_L_Msk  (0xFUL << AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_L_Pos)
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_L  AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_L_Msk
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_L_Pos  (4U)
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_L_Msk  (0xFUL << AUDPRC_ADC_PATH_CFG0_FINE_VOL_L_Pos)
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_L  AUDPRC_ADC_PATH_CFG0_FINE_VOL_L_Msk
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_R_Pos  (8U)
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_R_Msk  (0xFUL << AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_R_Pos)
#define AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_R  AUDPRC_ADC_PATH_CFG0_ROUGH_VOL_R_Msk
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_R_Pos  (12U)
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_R_Msk  (0xFUL << AUDPRC_ADC_PATH_CFG0_FINE_VOL_R_Pos)
#define AUDPRC_ADC_PATH_CFG0_FINE_VOL_R  AUDPRC_ADC_PATH_CFG0_FINE_VOL_R_Msk
#define AUDPRC_ADC_PATH_CFG0_SRC_SEL_Pos  (16U)
#define AUDPRC_ADC_PATH_CFG0_SRC_SEL_Msk  (0x1UL << AUDPRC_ADC_PATH_CFG0_SRC_SEL_Pos)
#define AUDPRC_ADC_PATH_CFG0_SRC_SEL    AUDPRC_ADC_PATH_CFG0_SRC_SEL_Msk
#define AUDPRC_ADC_PATH_CFG0_DATA_SWAP_Pos  (17U)
#define AUDPRC_ADC_PATH_CFG0_DATA_SWAP_Msk  (0x1UL << AUDPRC_ADC_PATH_CFG0_DATA_SWAP_Pos)
#define AUDPRC_ADC_PATH_CFG0_DATA_SWAP  AUDPRC_ADC_PATH_CFG0_DATA_SWAP_Msk
#define AUDPRC_ADC_PATH_CFG0_RX2TX_LOOPBACK_Pos  (18U)
#define AUDPRC_ADC_PATH_CFG0_RX2TX_LOOPBACK_Msk  (0x1UL << AUDPRC_ADC_PATH_CFG0_RX2TX_LOOPBACK_Pos)
#define AUDPRC_ADC_PATH_CFG0_RX2TX_LOOPBACK  AUDPRC_ADC_PATH_CFG0_RX2TX_LOOPBACK_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG0 register ***************/
#define AUDPRC_DAC_EQ_CFG0_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG0_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG0_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG0_COEF         AUDPRC_DAC_EQ_CFG0_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG1 register ***************/
#define AUDPRC_DAC_EQ_CFG1_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG1_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG1_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG1_COEF         AUDPRC_DAC_EQ_CFG1_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG2 register ***************/
#define AUDPRC_DAC_EQ_CFG2_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG2_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG2_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG2_COEF         AUDPRC_DAC_EQ_CFG2_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG3 register ***************/
#define AUDPRC_DAC_EQ_CFG3_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG3_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG3_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG3_COEF         AUDPRC_DAC_EQ_CFG3_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG4 register ***************/
#define AUDPRC_DAC_EQ_CFG4_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG4_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG4_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG4_COEF         AUDPRC_DAC_EQ_CFG4_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG5 register ***************/
#define AUDPRC_DAC_EQ_CFG5_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG5_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG5_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG5_COEF         AUDPRC_DAC_EQ_CFG5_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG6 register ***************/
#define AUDPRC_DAC_EQ_CFG6_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG6_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG6_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG6_COEF         AUDPRC_DAC_EQ_CFG6_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG7 register ***************/
#define AUDPRC_DAC_EQ_CFG7_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG7_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG7_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG7_COEF         AUDPRC_DAC_EQ_CFG7_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG8 register ***************/
#define AUDPRC_DAC_EQ_CFG8_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG8_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG8_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG8_COEF         AUDPRC_DAC_EQ_CFG8_COEF_Msk

/*************** Bit definition for AUDPRC_DAC_EQ_CFG9 register ***************/
#define AUDPRC_DAC_EQ_CFG9_COEF_Pos     (0U)
#define AUDPRC_DAC_EQ_CFG9_COEF_Msk     (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG9_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG9_COEF         AUDPRC_DAC_EQ_CFG9_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG10 register ***************/
#define AUDPRC_DAC_EQ_CFG10_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG10_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG10_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG10_COEF        AUDPRC_DAC_EQ_CFG10_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG11 register ***************/
#define AUDPRC_DAC_EQ_CFG11_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG11_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG11_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG11_COEF        AUDPRC_DAC_EQ_CFG11_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG12 register ***************/
#define AUDPRC_DAC_EQ_CFG12_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG12_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG12_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG12_COEF        AUDPRC_DAC_EQ_CFG12_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG13 register ***************/
#define AUDPRC_DAC_EQ_CFG13_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG13_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG13_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG13_COEF        AUDPRC_DAC_EQ_CFG13_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG14 register ***************/
#define AUDPRC_DAC_EQ_CFG14_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG14_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG14_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG14_COEF        AUDPRC_DAC_EQ_CFG14_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG15 register ***************/
#define AUDPRC_DAC_EQ_CFG15_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG15_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG15_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG15_COEF        AUDPRC_DAC_EQ_CFG15_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG16 register ***************/
#define AUDPRC_DAC_EQ_CFG16_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG16_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG16_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG16_COEF        AUDPRC_DAC_EQ_CFG16_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG17 register ***************/
#define AUDPRC_DAC_EQ_CFG17_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG17_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG17_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG17_COEF        AUDPRC_DAC_EQ_CFG17_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG18 register ***************/
#define AUDPRC_DAC_EQ_CFG18_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG18_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG18_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG18_COEF        AUDPRC_DAC_EQ_CFG18_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG19 register ***************/
#define AUDPRC_DAC_EQ_CFG19_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG19_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG19_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG19_COEF        AUDPRC_DAC_EQ_CFG19_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG20 register ***************/
#define AUDPRC_DAC_EQ_CFG20_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG20_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG20_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG20_COEF        AUDPRC_DAC_EQ_CFG20_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG21 register ***************/
#define AUDPRC_DAC_EQ_CFG21_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG21_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG21_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG21_COEF        AUDPRC_DAC_EQ_CFG21_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG22 register ***************/
#define AUDPRC_DAC_EQ_CFG22_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG22_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG22_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG22_COEF        AUDPRC_DAC_EQ_CFG22_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG23 register ***************/
#define AUDPRC_DAC_EQ_CFG23_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG23_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG23_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG23_COEF        AUDPRC_DAC_EQ_CFG23_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG24 register ***************/
#define AUDPRC_DAC_EQ_CFG24_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG24_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG24_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG24_COEF        AUDPRC_DAC_EQ_CFG24_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG25 register ***************/
#define AUDPRC_DAC_EQ_CFG25_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG25_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG25_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG25_COEF        AUDPRC_DAC_EQ_CFG25_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG26 register ***************/
#define AUDPRC_DAC_EQ_CFG26_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG26_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG26_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG26_COEF        AUDPRC_DAC_EQ_CFG26_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG27 register ***************/
#define AUDPRC_DAC_EQ_CFG27_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG27_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG27_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG27_COEF        AUDPRC_DAC_EQ_CFG27_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG28 register ***************/
#define AUDPRC_DAC_EQ_CFG28_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG28_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG28_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG28_COEF        AUDPRC_DAC_EQ_CFG28_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG29 register ***************/
#define AUDPRC_DAC_EQ_CFG29_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG29_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG29_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG29_COEF        AUDPRC_DAC_EQ_CFG29_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG30 register ***************/
#define AUDPRC_DAC_EQ_CFG30_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG30_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG30_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG30_COEF        AUDPRC_DAC_EQ_CFG30_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG31 register ***************/
#define AUDPRC_DAC_EQ_CFG31_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG31_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG31_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG31_COEF        AUDPRC_DAC_EQ_CFG31_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG32 register ***************/
#define AUDPRC_DAC_EQ_CFG32_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG32_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG32_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG32_COEF        AUDPRC_DAC_EQ_CFG32_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG33 register ***************/
#define AUDPRC_DAC_EQ_CFG33_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG33_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG33_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG33_COEF        AUDPRC_DAC_EQ_CFG33_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG34 register ***************/
#define AUDPRC_DAC_EQ_CFG34_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG34_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG34_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG34_COEF        AUDPRC_DAC_EQ_CFG34_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG35 register ***************/
#define AUDPRC_DAC_EQ_CFG35_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG35_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG35_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG35_COEF        AUDPRC_DAC_EQ_CFG35_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG36 register ***************/
#define AUDPRC_DAC_EQ_CFG36_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG36_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG36_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG36_COEF        AUDPRC_DAC_EQ_CFG36_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG37 register ***************/
#define AUDPRC_DAC_EQ_CFG37_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG37_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG37_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG37_COEF        AUDPRC_DAC_EQ_CFG37_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG38 register ***************/
#define AUDPRC_DAC_EQ_CFG38_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG38_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG38_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG38_COEF        AUDPRC_DAC_EQ_CFG38_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG39 register ***************/
#define AUDPRC_DAC_EQ_CFG39_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG39_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG39_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG39_COEF        AUDPRC_DAC_EQ_CFG39_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG40 register ***************/
#define AUDPRC_DAC_EQ_CFG40_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG40_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG40_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG40_COEF        AUDPRC_DAC_EQ_CFG40_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG41 register ***************/
#define AUDPRC_DAC_EQ_CFG41_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG41_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG41_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG41_COEF        AUDPRC_DAC_EQ_CFG41_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG42 register ***************/
#define AUDPRC_DAC_EQ_CFG42_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG42_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG42_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG42_COEF        AUDPRC_DAC_EQ_CFG42_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG43 register ***************/
#define AUDPRC_DAC_EQ_CFG43_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG43_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG43_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG43_COEF        AUDPRC_DAC_EQ_CFG43_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG44 register ***************/
#define AUDPRC_DAC_EQ_CFG44_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG44_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG44_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG44_COEF        AUDPRC_DAC_EQ_CFG44_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG45 register ***************/
#define AUDPRC_DAC_EQ_CFG45_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG45_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG45_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG45_COEF        AUDPRC_DAC_EQ_CFG45_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG46 register ***************/
#define AUDPRC_DAC_EQ_CFG46_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG46_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG46_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG46_COEF        AUDPRC_DAC_EQ_CFG46_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG47 register ***************/
#define AUDPRC_DAC_EQ_CFG47_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG47_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG47_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG47_COEF        AUDPRC_DAC_EQ_CFG47_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG48 register ***************/
#define AUDPRC_DAC_EQ_CFG48_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG48_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG48_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG48_COEF        AUDPRC_DAC_EQ_CFG48_COEF_Msk

/************** Bit definition for AUDPRC_DAC_EQ_CFG49 register ***************/
#define AUDPRC_DAC_EQ_CFG49_COEF_Pos    (0U)
#define AUDPRC_DAC_EQ_CFG49_COEF_Msk    (0xFFFFFFUL << AUDPRC_DAC_EQ_CFG49_COEF_Pos)
#define AUDPRC_DAC_EQ_CFG49_COEF        AUDPRC_DAC_EQ_CFG49_COEF_Msk

/*************** Bit definition for AUDPRC_RESERVED_IN register ***************/
#define AUDPRC_RESERVED_IN_CTRL_0_Pos   (0U)
#define AUDPRC_RESERVED_IN_CTRL_0_Msk   (0xFFUL << AUDPRC_RESERVED_IN_CTRL_0_Pos)
#define AUDPRC_RESERVED_IN_CTRL_0       AUDPRC_RESERVED_IN_CTRL_0_Msk
#define AUDPRC_RESERVED_IN_CTRL_1_Pos   (8U)
#define AUDPRC_RESERVED_IN_CTRL_1_Msk   (0xFFUL << AUDPRC_RESERVED_IN_CTRL_1_Pos)
#define AUDPRC_RESERVED_IN_CTRL_1       AUDPRC_RESERVED_IN_CTRL_1_Msk
#define AUDPRC_RESERVED_IN_CTRL_2_Pos   (16U)
#define AUDPRC_RESERVED_IN_CTRL_2_Msk   (0xFFUL << AUDPRC_RESERVED_IN_CTRL_2_Pos)
#define AUDPRC_RESERVED_IN_CTRL_2       AUDPRC_RESERVED_IN_CTRL_2_Msk

/************** Bit definition for AUDPRC_RESERVED_OUT register ***************/
#define AUDPRC_RESERVED_OUT_STAT_Pos    (0U)
#define AUDPRC_RESERVED_OUT_STAT_Msk    (0xFFUL << AUDPRC_RESERVED_OUT_STAT_Pos)
#define AUDPRC_RESERVED_OUT_STAT        AUDPRC_RESERVED_OUT_STAT_Msk

#endif
