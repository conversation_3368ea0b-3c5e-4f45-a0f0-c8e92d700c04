#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc
#include "../rtconfig.h"
#include "../../../../drivers/cmsis/sf32lb55x/mem_map.h"


; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 PSRAM_BASE HCPU_FLASH_CODE_SIZE  {    ; load region size_region
  ER_IROM1 PSRAM_BASE HCPU_FLASH_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   *(FSymTab)
   *.o (.rodata.*)
  }
  ER_IROM1_EX HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
   ;drv_spi_flash.o (.text.*)
   ;drv_spi_flash.o (.rodata.*)
   bf0_hal_qspi.o (.text.*)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   bf0_hal_psram.o (.text.*)
   drv_psram.o (.text.*)
   drv_psram.o (.rodata.*)
   ;bf0_hal_dma.o   (.text.HAL_DMA_PollForTransfer)
   ;drv_common.o    (.text.HAL_GetTick)
   ;clock.o         (.text.rt_tick_get)
   bf0_hal_epic.o (+RO)   
   bf0_hal_hpaon.o (.text.*)
   *.o (.l1_non_ret_text_*)
   *.o (.l1_non_ret_rodata_*)
  }  


  RW_PSRAM1 PSRAM_BASE UNINIT{  ; ZI data, retained
    *.o (.l2_ret_data_*)
    *.o (.l2_ret_bss_*)
    *.o (.l2_cache_ret_data_*)
    *.o (.l2_cache_ret_bss_*)    
  }
  RW_PSRAM_NON_RET +0  UNINIT{  ; ZI data, not retained and reused by SRAM retention
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)  
  }
  ScatterAssert((ImageLength(RW_PSRAM1)+ImageLength(RW_PSRAM_NON_RET))<PSRAM_SIZE)
  
  RW_IRAM_RET HPSYS_RETM_BASE HPSYS_RETM_SIZE {  
;    .ANY2 (+RW +ZI)    

   *.o (.l1_ret_text_*)
   *.o (.l1_ret_rodata_*)
   *.o (.bss.retm_bss_*)
   *.o (.retm_data_*)

 
   idle.o (.bss.idle_thread_stack)
   bf0_hal_rcc.o   (.text.*)
#ifdef BSP_USING_PM   
   bf0_pm.o        (.text.sifli_light_handler)
   bf0_pm.o        (.text.sifli_deep_handler)
   bf0_pm.o        (.text.sifli_standby_handler)
   drv_io.o           (.text.*)
   bf0_hal_gpio.o     (.text.*)
#endif  
    
    drv_psram.o(.bss.bf0_psram_handle)
  }

  ER_ITCM HPSYS_ITCM_BASE HPSYS_ITCM_SIZE {
  }

  RW_IRAM0 HCPU_RAM_DATA_START_ADDR UNINIT {  ; ZI data, not retained
    *.o (.l1_non_ret_data_*)
    *.o (.l1_non_ret_bss_*)
#ifndef BSP_USING_PSRAM
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)
    *.o (.nand_cache)
#endif
}
  RW_IRAM1 +0  {  ; RW data  ,  retained
   *.o (Jlink_RTT, +First)
    *.o (.l1_ret_data_*)
    *.o (.l1_ret_bss_*)
   .ANY (+RW +ZI)
  }
  ScatterAssert(ImageLength(RW_IRAM0)+ImageLength(RW_IRAM1)<HCPU_RAM_DATA_SIZE)
}



