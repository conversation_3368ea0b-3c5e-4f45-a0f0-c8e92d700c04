/**
  ******************************************************************************
  * @file   dialog7212.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __DIALOG7212_H__
#define __DIALOG7212_H__


/*******************************************************************************
 * Definitions
 ******************************************************************************/

#define DA7212_INIT_SIZE (23)
#define DA7212_DAC_MAX_VOL (0x7F)
#define DA7212_DAC_MIN_VOL (0)

/*! @brief DA7212 I2C address */
#define DA7212_ADDRESS (0x1A)

#define DA7212_AUX_REGISTER_SIZE (33)
#define DA7212_MIC2_REGISTER_SIZE (33)
#define DA7212_MIC1_REGISTER_SIZE (24)

#define DA7212_DAC_MUTE_ENABLED (0xC0)
#define DA7212_DAC_MUTE_DISABLED (0x80)

#define DA7212_MAX_VOLUME_STEPS (15)
#define DA7212_MIN_VOLUME_STEPS (0)

#define DA7212_MAX_VOLUME_STEPS (15)

#define DA7212_CHANGE_FREQ_SIZE (0x05)

#define CLEAR_REGISTER (0x00)

#define DA7212_I2C_BAUDRATE (100000U)
/***************************************************************************************************/
/* * Register values. */

/******************Status registers*********************/
#define DIALOG7212_STATUS1 0x02
#define DIALOG7212_PLL_STATUS 0x03
#define DIALOG7212_AUX_L_GAIN_STATUS 0x04
#define DIALOG7212_AUX_R_GAIN_STATUS 0x05
#define DIALOG7212_MIC_1_GAIN_STATUS 0x06
#define DIALOG7212_MIC_2_GAIN_STATUS 0x07
#define DIALOG7212_MIXIN_L_GAIN_STATUS 0x08
#define DIALOG7212_MIXIN_R_GAIN_STATUS 0x09
#define DIALOG7212_ADC_L_GAIN_STATUS 0x0A
#define DIALOG7212_ADC_R_GAIN_STATUS 0x0B
#define DIALOG7212_DAC_L_GAIN_STATUS 0x0C
#define DIALOG7212_DAC_R_GAIN_STATUS 0x0D
#define DIALOG7212_HP_L_GAIN_STATUS 0x0E
#define DIALOG7212_HP_R_GAIN_STATUS 0x0F
#define DIALOG7212_LINE_GAIN_STATUS 0x10
/***************System Initialisation Registers************/
#define DIALOG7212_CIF_CTRL 0x1d
#define DIALOG7212_DIG_ROUTING_DAI 0x21
#define DIALOG7212_SR 0x22
#define DIALOG7212_REFERENCES 0x23
#define DIALOG7212_PLL_FRAC_TOP 0x24
#define DIALOG7212_PLL_FRAC_BOT 0x25
#define DIALOG7212_PLL_INTEGER 0x26
#define DIALOG7212_PLL_CTRL 0x27
#define DIALOG7212_DAI_CLK_MODE 0x28
#define DIALOG7212_DAI_CTRL 0x29
#define DIALOG7212_DIG_ROUTING_DAC 0x2A
#define DIALOG7212_ALC_CTRL1 0x2B
/************Input Gain/ Select Filter Registers**********/
#define DIALOG7212_AUX_L_GAIN 0x30
#define DIALOG7212_AUX_R_GAIN 0x31
#define DIALOG7212_MIXIN_L_SELECT 0x32
#define DIALOG7212_MIXIN_R_SELECT 0x33
#define DIALOG7212_MIXIN_L_GAIN 0x34
#define DIALOG7212_MIXIN_R_GAIN 0x35
#define DIALOG7212_ADC_L_GAIN 0x36
#define DIALOG7212_ADC_R_GAIN 0x37
#define DIALOG7212_ADC_FILTERS1 0x38
#define DIALOG7212_MIC_1_GAIN 0x39
#define DIALOG7212_MIC_2_GAIN 0x3A
/************Output Gain/ Select Filter Registers**********/
#define DIALOG7212_DAC_FILTERS5 0x40
#define DIALOG7212_DAC_FILTERS2 0x41
#define DIALOG7212_DAC_FILTERS3 0x42
#define DIALOG7212_DAC_FILTERS4 0x43
#define DIALOG7212_DAC_FILTERS1 0x44
#define DIALOG7212_DAC_L_GAIN 0x45
#define DIALOG7212_DAC_R_GAIN 0x46
#define DIALOG7212_CP_CTRL 0x47
#define DIALOG7212_HP_L_GAIN 0x48
#define DIALOG7212_HP_R_GAIN 0x49
#define DIALOG7212_LINE_GAIN 0x4A
#define DIALOG7212_MIXOUT_L_SELECT 0x4B
#define DIALOG7212_MIXOUT_R_SELECT 0x4C
/**************System Controller Registers(1)*************/
#define DIALOG7212_SYSTEM_MODES_INPUT 0x50
#define DIALOG7212_SYSTEM_MODES_OUTPUT 0x51
/*****************Control Registers(2)********************/
#define DIALOG7212_AUX_L_CTRL 0x60
#define DIALOG7212_AUX_R_CTRL 0x61
#define DIALOG7212_MICBIAS_CTRL 0x62
#define DIALOG7212_MIC_1_CTRL 0x63
#define DIALOG7212_MIC_2_CTRL 0x64
#define DIALOG7212_MIXIN_L_CTRL 0x65
#define DIALOG7212_MIXIN_R_CTRL 0x66
#define DIALOG7212_ADC_L_CTRL 0x67
#define DIALOG7212_ADC_R_CTRL 0x68
#define DIALOG7212_DAC_L_CTRL 0x69
#define DIALOG7212_DAC_R_CTRL 0x6A
#define DIALOG7212_HP_L_CTRL 0x6B
#define DIALOG7212_HP_R_CTRL 0x6C
#define DIALOG7212_LINE_CTRL 0x6D
#define DIALOG7212_MIXOUT_L_CTRL 0x6E
#define DIALOG7212_MIXOUT_R_CTRL 0x6F
/************ Registers**********/

/****************Configuration Registers*****************/
#define DIALOG7212_LDO_CTRL 0x90
#define DIALOG7212_GAIN_RAMP_CTRL 0x92
#define DIALOG7212_MIC_CONFIG 0x93
#define DIALOG7212_PC_COUNT 0x94
#define DIALOG7212_CP_VOL_THRESHOLD1 0x95
#define DIALOG7212_CP_DELAY 0x96
#define DIALOG7212_CP_DETECTOR 0x97
#define DIALOG7212_DAI_OFFSET 0x98
#define DIALOG7212_DIG_CTRL 0x99
#define DIALOG7212_ALC_CTRL2 0x9A
#define DIALOG7212_ALC_CTRL3 0x9B
#define DIALOG7212_ALC_NOISE 0x9C
#define DIALOG7212_ALC_TARGET_MIN 0x9D
#define DIALOG7212_ALC_TARGET_MAX 0x9E
#define DIALOG7212_ALC_GAIN_LIMITS 0x9F
#define DIALOG7212_ALC_ANA_GAIN_LIMITS 0xA0
#define DIALOG7212_ALC_ANTICLIP_CTRL 0xA1
#define DIALOG7212_ALC_ANTICLIP_LEVEL 0xA2
#define DIALOG7212_DAC_NG_SETUP_TIME 0xAF
#define DIALOG7212_DAC_NG_OFF_THRESH 0xB0
#define DIALOG7212_DAC_NG_ON_THRESH 0xB1
#define DIALOG7212_DAC_NG_CTRL 0xB2
//#define DIALOG7212_DAC_NG_SPARE                       0xB3
/************Tone Generation & Beep Registers************/
#define DIALOG7212_TONE_GEN_CFG1 0xB4
#define DIALOG7212_TONE_GEN_CFG2 0xB5
#define DIALOG7212_TONE_GEN_CYCLES 0xB6
#define DIALOG7212_TONE_GEN_FREQ1_L 0xB7
#define DIALOG7212_TONE_GEN_FREQ1_U 0xB8
#define DIALOG7212_TONE_GEN_FREQ2_L 0xB9
#define DIALOG7212_TONE_GEN_FREQ2_U 0xBA
#define DIALOG7212_TONE_GEN_ON_PER 0xBB
#define DIALOG7212_TONE_GEN_OFF_PER 0xBC

/************System Controller Registers(2)*************/
#define DIALOG7212_SYSTEM_STATUS 0xE0

#define DIALOG7212_SYSTEM_ACTIVE 0xFD

/******************Driver DA7212 Macros*****************/

/******************Status registers*********************/
/* DIALOG7212_PLL_STATUS                         0x03*/
#define DIALOG7212_PLL_STATUS_BYPASS_ACTIVE_MASK (1 << 3)
#define DIALOG7212_PLL_STATUS_MCLK_STATUS_MASK (1 << 2)
#define DIALOG7212_PLL_STATUS_SRM_LOCK_MASK (1 << 1)
#define DIALOG7212_PLL_STATUS_LOCK_MASK (1 << 0)

/* DIALOG7212_AUX_L_GAIN_STATUS                  0x04*/
#define DIALOG7212_AUX_L_AMP_GAIN_STATUS_MASK (0x3F)
#define DIALOG7212_AUX_L_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_AUX_L_AMP_GAIN_STATUS(x) (x << DIALOG7212_AUX_L_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_AUX_R_GAIN_STATUS                  0x05*/
#define DIALOG7212_AUX_R_AMP_GAIN_STATUS_MASK (0x3F)
#define DIALOG7212_AUX_R_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_AUX_R_AMP_GAIN_STATUS(x) (x << DIALOG7212_AUX_R_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_MIC_1_GAIN_STATUS                  0x06*/
#define DIALOG7212_MIC_1_AMP_GAIN_STATUS_MASK (0x07)
#define DIALOG7212_MIC_1_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_MIC_1_AMP_GAIN_STATUS(x) (x << DIALOG7212_MIC_1_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_MIC_2_GAIN_STATUS                  0x07*/
#define DIALOG7212_MIC_2_AMP_GAIN_STATUS_MASK (0x07)
#define DIALOG7212_MIC_2_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_MIC_2_AMP_GAIN_STATUS(x) (x << DIALOG7212_MIC_2_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_MIXIN_L_GAIN_STATUS                0x08*/
#define DIALOG7212_MIXIN_L_AMP_GAIN_STATUS_MASK (0x0F)
#define DIALOG7212_MIXIN_L_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_MIXIN_L_AMP_GAIN_STATUS(x) (x << DIALOG7212_MIXIN_L_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_MIXIN_R_GAIN_STATUS                0x09*/
#define DIALOG7212_MIXIN_R_AMP_GAIN_STATUS_MASK (0x0F)
#define DIALOG7212_MIXIN_R_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_MIXIN_R_AMP_GAIN_STATUS(x) (x << DIALOG7212_MIXIN_R_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_ADC_L_GAIN_STATUS                  0x0A*/
#define DIALOG7212_ADC_L_GAIN_STATUS_MASK (0x7F)
#define DIALOG7212_ADC_L_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_ADC_L_GAIN_STATUS_VAL(x) (x << DIALOG7212_ADC_L_GAIN_STATUS_SHIFT)

/* DIALOG7212_ADC_R_GAIN_STATUS                  0x0B*/
#define DIALOG7212_ADC_R_GAIN_STATUS_MASK (0x7F)
#define DIALOG7212_ADC_R_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_ADC_R_GAIN_STATUS_VAL(x) (x << DIALOG7212_ADC_R_GAIN_STATUS_SHIFT)

/* DIALOG7212_DAC_L_GAIN_STATUS                  0x0C*/
#define DIALOG7212_DAC_L_GAIN_STATUS_MASK (0x7F)
#define DIALOG7212_DAC_L_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_DAC_L_GAIN_STATUS_VAL(x) (x << DIALOG7212_DAC_L_GAIN_STATUS_SHIFT)

/* DIALOG7212_DAC_R_GAIN_STATUS                  0x0D*/
#define DIALOG7212_DAC_R_GAIN_STATUS_MASK (0x7F)
#define DIALOG7212_DAC_R_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_DAC_R_GAIN_STATUS_VAL(x) (x << DIALOG7212_DAC_R_GAIN_STATUS_SHIFT)

/* DIALOG7212_HP_L_GAIN_STATUS                   0x0E*/
#define DIALOG7212_HP_L_AMP_GAIN_STATUS_MASK (0x3F)
#define DIALOG7212_HP_L_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_HP_L_AMP_GAIN_STATUS(x) (x << DIALOG7212_HP_L_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_HP_R_GAIN_STATUS                   0x0F*/
#define DIALOG7212_HP_R_AMP_GAIN_STATUS_MASK (0x3F)
#define DIALOG7212_HP_R_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_HP_R_AMP_GAIN_STATUS(x) (x << DIALOG7212_HP_R_AMP_GAIN_STATUS_SHIFT)

/* DIALOG7212_LINE_GAIN_STATUS                   0x10*/
#define DIALOG7212_LINE_AMP_GAIN_STATUS_MASK (0x3F)
#define DIALOG7212_LINE_AMP_GAIN_STATUS_SHIFT (0)
#define DIALOG7212_LINE_AMP_GAIN_STATUS(x) (x << DIALOG7212_LINE_AMP_GAIN_STATUS_SHIFT)

/***************System Initialisation Registers************/

/* DIALOG7212_CIF_CTRL                           0x1d */
#define DIALOG7212_CIF_CTRL_CIF_REG_SOFT_RESET_MASK (1 << 7)
#define DIALOG7212_CIF_CTRL_CIF_I2C_WRITE_MODE_MASK (1 << 0)

/* DIALOG7212_DIG_ROUTING_DAI                    0x21 */
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_MASK (0x30)
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT (4)
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_ADC_LEFT (0 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_ADC_RIGHT (1 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_DAI_LEFT (2 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_R_SRC_DAI_RIGHT (3 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_MASK (0x03)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_SHIFT (0)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_ADC_LEFT (0 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_ADC_RIGHT (1 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_DAI_LEFT (2 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAI_L_SRC_DAI_RIGHT (3 << DIALOG7212_DIG_ROUTING_DAI_R_SRC_SHIFT)

/* DIALOG7212_SR                                 0x22 */
#define DIALOG7212_SR_MASK (0x0F)
#define DIALOG7212_SR_SHIFT (0)
#define DIALOG7212_SR_8KHZ (1 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_11_025KHZ (2 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_12KHZ (3 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_16KHZ (5 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_22KHZ (6 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_24KHZ (7 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_32KHZ (9 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_44_1KHZ (10 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_48KHZ (11 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_88_2KHZ (14 << DIALOG7212_SR_SHIFT)
#define DIALOG7212_SR_96KHZ (15 << DIALOG7212_SR_SHIFT)

/* DIALOG7212_REFERENCES                         0x23 */
#define DIALOG7212_REFERENCES_VMID_FAST_DISCHARGE_MASK (1 << 5)
#define DIALOG7212_REFERENCES_VMID_FAST_CHARGE_MASK (1 << 4)
#define DIALOG7212_REFERENCES_BIAS_EN_MASK (1 << 3)

/* DIALOG7212_PLL_FRAC_TOP                       0x24 */
#define DIALOG7212_PLL_FBDIV_FRAC_TOP_MASK (0x1F)
#define DIALOG7212_PLL_FBDIV_FRAC_TOP_SHIFT (0)
#define DIALOG7212_PLL_FBDIV_FRAC_TOP(x) (x << DIALOG7212_PLL_FBDIV_FRAC_TOP_SHIFT)

/* DIALOG7212_PLL_FRAC_BOT                       0x25 */
#define DIALOG7212_PLL_FBDIV_FRAC_BOT_MASK (0xFF)
#define DIALOG7212_PLL_FBDIV_FRAC_BOT_SHIFT (0)
#define DIALOG7212_PLL_FBDIV_FRAC_BOT(x) (x << DIALOG7212_PLL_FBDIV_FRAC_BOT_SHIFT)

/* DIALOG7212_PLL_INTEGER                        0x26 */
#define DIALOG7212_PLL_FBDIV_INTEGER_MASK (0xFF)
#define DIALOG7212_PLL_FBDIV_INTEGER_SHIFT (0)
#define DIALOG7212_PLL_FBDIV_INTEGER_BOT(x) (x << DIALOG7212_PLL_FBDIV_INTEGER_SHIFT)

/* DIALOG7212_PLL_CTRL                           0x27 */
#define DIALOG7212_PLL_EN_MASK (1 << 7)
#define DIALOG7212_PLL_SRM_EN_MASK (1 << 6)
#define DIALOG7212_PLL_32K_MODE_MASK (1 << 5)
#define DIALOG7212_PLL_MCLKSQR_EN_MASK (1 << 4)
#define DIALOG7212_PLL_INDIV_MASK (0x06)
#define DIALOG7212_PLL_INDIV_SHIFT (2)
#define DIALOG7212_PLL_INDIV_2_10MHZ (0 << DIALOG7212_PLL_INDIV_SHIFT)
#define DIALOG7212_PLL_INDIV_10_20MHZ (1 << DIALOG7212_PLL_INDIV_SHIFT)
#define DIALOG7212_PLL_INDIV_20_40MHZ (2 << DIALOG7212_PLL_INDIV_SHIFT)
#define DIALOG7212_PLL_INDIV_40_80MHZ (3 << DIALOG7212_PLL_INDIV_SHIFT)

/* DIALOG7212_DAI_CLK_MODE                       0x28 */
#define DIALOG7212_DAI_CLK_EN_MASK (1 << 7)
#define DIALOG7212_DAI_WCLK_POL_MASK (1 << 3)
#define DIALOG7212_DAI_CLK_POL_MASK (1 << 2)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_MASK (0x03)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_SHIFT (0)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_BCLK32 (0 << DIALOG7212_DAI_BCLKS_PER_WCLK_SHIFT)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_BCLK64 (1 << DIALOG7212_DAI_BCLKS_PER_WCLK_SHIFT)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_BCLK128 (2 << DIALOG7212_DAI_BCLKS_PER_WCLK_SHIFT)
#define DIALOG7212_DAI_BCLKS_PER_WCLK_BCLK256 (3 << DIALOG7212_DAI_BCLKS_PER_WCLK_SHIFT)

/* DIALOG7212_DAI_CTRL                           0x29 */
#define DIALOG7212_DAI_EN_MASK (1 << 7)
#define DIALOG7212_DAI_OE_MASK (1 << 6)
#define DIALOG7212_DAI_TDM_MODE_EN_MASK (1 << 5)
#define DIALOG7212_DAI_MONO_MODE_MASK (1 << 4)
#define DIALOG7212_DAI_WORD_LENGTH_MASK (0x0c)
#define DIALOG7212_DAI_WORD_LENGTH_SHIFT (2)
#define DIALOG7212_DAI_WORD_LENGTH_16B (0 << DIALOG7212_DAI_WORD_LENGTH_SHIFT)
#define DIALOG7212_DAI_WORD_LENGTH_20B (1 << DIALOG7212_DAI_WORD_LENGTH_SHIFT)
#define DIALOG7212_DAI_WORD_LENGTH_24B (2 << DIALOG7212_DAI_WORD_LENGTH_SHIFT)
#define DIALOG7212_DAI_WORD_LENGTH_32B (3 << DIALOG7212_DAI_WORD_LENGTH_SHIFT)
#define DIALOG7212_DAI_FORMAT_MASK (0x03)
#define DIALOG7212_DAI_FORMAT_SHIFT (0)
#define DIALOG7212_DAI_FORMAT_I2S_MODE (0 << DIALOG7212_DAI_FORMAT_SHIFT)
#define DIALOG7212_DAI_FORMAT_LEFT_JUSTIFIED (1 << DIALOG7212_DAI_FORMAT_SHIFT)
#define DIALOG7212_DAI_FORMAT_RIGTH_JUSTIFIED (2 << DIALOG7212_DAI_FORMAT_SHIFT)
#define DIALOG7212_DAI_FORMAT_DSP_MODE (3 << DIALOG7212_DAI_FORMAT_SHIFT)

/* DIALOG7212_DIG_ROUTING_DAC                    0x2A */
#define DIALOG7212_DIG_ROUTING_DAC_R_MONO_MASK (1 << 7)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_MASK (0x30)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_SHIFT (4)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_ADC_L_OUTPUT (0 << DIALOG7212_DIG_ROUTING_DAC_R_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_ADC_R_OUTPUT (1 << DIALOG7212_DIG_ROUTING_DAC_R_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_DAC_L (2 << DIALOG7212_DIG_ROUTING_DAC_R_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_R_RSC_DAC_R (3 << DIALOG7212_DIG_ROUTING_DAC_R_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_L_MONO_MASK (1 << 3)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_MASK (0x03)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_SHIFT (0)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_ADC_L_OUTPUT (0 << DIALOG7212_DIG_ROUTING_DAC_L_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_ADC_R_OUTPUT (1 << DIALOG7212_DIG_ROUTING_DAC_L_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_DAC_L (2 << DIALOG7212_DIG_ROUTING_DAC_L_RSC_SHIFT)
#define DIALOG7212_DIG_ROUTING_DAC_L_RSC_DAC_R (3 << DIALOG7212_DIG_ROUTING_DAC_L_RSC_SHIFT)

/* DIALOG7212_ALC_CTRL1                          0x2B */
#define DIALOG7212_ALC_R_EN_MASK (1 << 7)
#define DIALOG7212_ALC_CALIB_OVERFLOW_MASK (1 << 5)
#define DIALOG7212_ALC_AUTO_CALIB_EN_MASK (1 << 4)
#define DIALOG7212_ALC_L_EN_MASK (1 << 3)
#define DIALOG7212_ALC_CALIB_MODE_MASK (1 << 2)
#define DIALOG7212_ALC_SYNC_MODE_MASK (1 << 1)
#define DIALOG7212_ALC_OFFSET_EN_MASK (1 << 0)

/************Input Gain/ Select Filter Registers**********/
/* DIALOG7212_AUX_L_GAIN                         0x30 */
#define DIALOG7212_AUX_L_AMP_GAIN_MASK (0x3F)
#define DIALOG7212_AUX_L_AMP_GAIN_SHIFT (0)
#define DIALOG7212_AUX_L_AMP_GAIN(x) (x << DIALOG7212_AUX_L_AMP_GAIN_SHIFT)

/* DIALOG7212_AUX_R_GAIN                         0x31 */
#define DIALOG7212_AUX_R_AMP_GAIN_MASK (0x3F)
#define DIALOG7212_AUX_R_AMP_GAIN_SHIFT (0)
#define DIALOG7212_AUX_R_AMP_GAIN(x) (x << DIALOG7212_AUX_R_AMP_GAIN_SHIFT)

/* DIALOG7212_MIXIN_L_SELECT                     0x32 */
#define DIALOG7212_MIXIN_L_SELECT_DMIC_L_EN_MASK (1 << 7)
#define DIALOG7212_MIXIN_L_SELECT_MIXING_R_MASK (1 << 3)
#define DIALOG7212_MIXIN_L_SELECT_MIC2_SEL_MASK (1 << 2)
#define DIALOG7212_MIXIN_L_SELECT_MIC1_SEL_MASK (1 << 1)
#define DIALOG7212_MIXIN_L_SELECT_AUX_L_SEL_MASK (1 << 0)

/* DIALOG7212_MIXIN_R_SELECT                     0x33 */
#define DIALOG7212_MIXIN_R_SELECT_DMIC_R_EN_MASK (1 << 7)
#define DIALOG7212_MIXIN_R_SELECT_MIXING_L_MASK (1 << 3)
#define DIALOG7212_MIXIN_R_SELECT_MIC2_SEL_MASK (1 << 2)
#define DIALOG7212_MIXIN_R_SELECT_MIC1_SEL_MASK (1 << 1)
#define DIALOG7212_MIXIN_R_SELECT_AUX_R_SEL_MASK (1 << 0)

/* DIALOG7212_MIXIN_L_GAIN                       0x34 */
#define DIALOG7212_MIXIN_L_AMP_GAIN_MASK (0x0F)
#define DIALOG7212_MIXIN_L_AMP_GAIN_SHIFT (0)
#define DIALOG7212_MIXIN_L_AMP_GAIN(x) (x << DIALOG7212_MIXIN_L_AMP_GAIN_SHIFT)

/* DIALOG7212_MIXIN_R_GAIN                       0x35 */
#define DIALOG7212_MIXIN_R_AMP_GAIN_MASK (0x0F)
#define DIALOG7212_MIXIN_R_AMP_GAIN_SHIFT (0)
#define DIALOG7212_MIXIN_R_AMP_GAIN(x) (x << DIALOG7212_MIXIN_R_AMP_GAIN_SHIFT)

/* DIALOG7212_ADC_L_GAIN                         0x36 */
#define DIALOG7212_ADC_L_DIGITAL_GAIN_MASK (0x7F)
#define DIALOG7212_ADC_L_DIGITAL_GAIN_SHIFT (0)
#define DIALOG7212_ADC_L_DIGITAL_GAIN(x) (x << DIALOG7212_ADC_L_DIGITAL_GAIN_SHIFT)

/* DIALOG7212_ADC_R_GAIN                         0x37 */
#define DIALOG7212_ADC_R_DIGITAL_GAIN_MASK (0x7F)
#define DIALOG7212_ADC_R_DIGITAL_GAIN_SHIFT (0)
#define DIALOG7212_ADC_R_DIGITAL_GAIN(x) (x << DIALOG7212_ADC_R_DIGITAL_GAIN_SHIFT)

/* DIALOG7212_ADC_FILTERS1                       0x38 */
#define DIALOG7212_ADC_FILTERS1_ADC_HPF_EN_MASK (1 << 7)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_MASK (0x30)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_SHIFT (5)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_2HZ (0 << DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_4HZ (1 << DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_8HZ (2 << DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_16HZ (3 << DIALOG7212_ADC_FILTERS1_ADC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_EN_MASK (1 << 3)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_MASK (0x07)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT (0)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_2_5HZ (0 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_25HZ (1 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_50HZ (2 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_100HZ (3 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_150HZ (4 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_200HZ (5 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_300HZ (6 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_400HZ (7 << DIALOG7212_ADC_FILTERS1_ADC_VOICE_HPF_CORNER_SHIFT)

/* DIALOG7212_MIC_1_GAIN                         0x39 */
#define DIALOG7212_MIC_1_AMP_GAIN_MASK (0x07)
#define DIALOG7212_MIC_1_AMP_GAIN_SHIFT (0)
#define DIALOG7212_MIC_1_AMP_GAIN_N6DB (0 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_0DB (1 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P6DB (2 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P12DB (3 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P18DB (4 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P24DB (5 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P30DB (6 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_1_AMP_GAIN_P36DB (7 << DIALOG7212_MIC_1_AMP_GAIN_SHIFT)

/* DIALOG7212_MIC_2_GAIN                         0x3A */
#define DIALOG7212_MIC_2_AMP_GAIN_MASK (0x07)
#define DIALOG7212_MIC_2_AMP_GAIN_SHIFT (0)
#define DIALOG7212_MIC_2_AMP_GAIN_N6DB (0 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_0DB (1 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P6DB (2 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P12DB (3 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P18DB (4 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P24DB (5 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P30DB (6 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)
#define DIALOG7212_MIC_2_AMP_GAIN_P36DB (7 << DIALOG7212_MIC_2_AMP_GAIN_SHIFT)

/************Output Gain/ Select Filter Registers**********/
/* DIALOG7212_DAC_FILTERS5                       0x40 */
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_EN_MASK (1 << 7)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_MASK (0x07)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT (4)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_1 (0 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_2 (1 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_4 (2 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_8 (3 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_16 (4 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_32 (5 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)
#define DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_64 (6 << DIALOG7212_DAC_FILTERS5_SOFTMUTE_RATE_SHIFT)

/* DIALOG7212_DAC_FILTERS2                       0x41 */
#define DIALOG7212_DAC_FILTERS2_EQ_BAND2_MASK (0xF0)
#define DIALOG7212_DAC_FILTERS2_EQ_BAND2_SHIFT (4)
#define DIALOG7212_DAC_FILTERS2_EQ_BAND2(x) (x << DIALOG7212_DAC_FILTERS2_EQ_BAND2_SHIFT)
#define DIALOG7212_DAC_FILTERS2_EQ_BAND1_MASK (0x0F)
#define DIALOG7212_DAC_FILTERS2_EQ_BAND1_SHIFT (0)
#define DIALOG7212_DAC_FILTERS2_EQ_BAND1(x) (x << DIALOG7212_DAC_FILTERS2_EQ_BAND1_SHIFT)

/* DIALOG7212_DAC_FILTERS3                       0x42 */
#define DIALOG7212_DAC_FILTERS3_EQ_BAND4_MASK (0xF0)
#define DIALOG7212_DAC_FILTERS3_EQ_BAND4_SHIFT (4)
#define DIALOG7212_DAC_FILTERS3_EQ_BAND4(x) (x << DIALOG7212_DAC_FILTERS3_EQ_BAND4_SHIFT)
#define DIALOG7212_DAC_FILTERS3_EQ_BAND3_MASK (0x0F)
#define DIALOG7212_DAC_FILTERS3_EQ_BAND3_SHIFT (0)
#define DIALOG7212_DAC_FILTERS3_EQ_BAND3(x) (x << DIALOG7212_DAC_FILTERS3_EQ_BAND3_SHIFT)

/* DIALOG7212_DAC_FILTERS4                       0x43 */
#define DIALOG7212_DAC_FILTERS4_EQ_EN_MASK (1 << 7)
#define DIALOG7212_DAC_FILTERS4_EQ_BAND5_MASK (0x0F)
#define DIALOG7212_DAC_FILTERS4_EQ_BAND5_SHIFT (0)
#define DIALOG7212_DAC_FILTERS4_EQ_BAND5(x) (x << DIALOG7212_DAC_FILTERS4_EQ_BAND5_SHIFT)

/* DIALOG7212_DAC_FILTERS1                       0x44 */
#define DIALOG7212_DAC_FILTERS1_HPF_EN_MASK (1 << 7)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_MASK (0x30)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_SHIFT (5)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_2HZ (0 << DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_4HZ (1 << DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_8HZ (2 << DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_16HZ (3 << DIALOG7212_DAC_FILTERS1_DAC_AUDIO_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_EN_MASK (1 << 3)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_MASK (0x07)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT (0)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_2_5HZ (0 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_25HZ (1 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_50HZ (2 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_100HZ (3 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_150HZ (4 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_200HZ (5 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_300HZ (6 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)
#define DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_400HZ (7 << DIALOG7212_DAC_FILTERS1_DAC_VOICE_HPF_CORNER_SHIFT)

/* DIALOG7212_DAC_L_GAIN                         0x45 */
#define DIALOG7212_DAC_L_DIGITAL_GAIN_MASK (0x7F)
#define DIALOG7212_DAC_L_DIGITAL_GAIN_SHIFT (0)
#define DIALOG7212_DAC_L_DIGITAL_GAIN(x) (x << DIALOG7212_DAC_L_DIGITAL_GAIN_SHIFT)

/* DIALOG7212_DAC_R_GAIN                         0x46 */
#define DIALOG7212_DAC_R_DIGITAL_GAIN_MASK (0x7F)
#define DIALOG7212_DAC_R_DIGITAL_GAIN_SHIFT (0)
#define DIALOG7212_DAC_R_DIGITAL_GAIN(x) (x << DIALOG7212_DAC_R_DIGITAL_GAIN_SHIFT)

/* DIALOG7212_CP_CTRL                            0x47 */
#define DIALOG7212_CP_CTRL_EN_MASK (1 << 7)
#define DIALOG7212_CP_CTRL_SMALL_SWIT_CH_FREQ_EN_MASK (1 << 6)
#define DIALOG7212_CP_CTRL_MCHANGE_MASK (0x30)
#define DIALOG7212_CP_CTRL_MCHANGE_SHIFT (4)
#define DIALOG7212_CP_CTRL_MCHANGE_CP_MOD (0 << DIALOG7212_CP_CTRL_MCHANGE_SHIFT)
#define DIALOG7212_CP_CTRL_MCHANGE_PGA (1 << DIALOG7212_CP_CTRL_MCHANGE_SHIFT)
#define DIALOG7212_CP_CTRL_MCHANGE_DAC (2 << DIALOG7212_CP_CTRL_MCHANGE_SHIFT)
#define DIALOG7212_CP_CTRL_MCHANGE_OUTPUT (3 << DIALOG7212_CP_CTRL_MCHANGE_SHIFT)
#define DIALOG7212_CP_CTRL_MOD_MASK (0x0C)
#define DIALOG7212_CP_CTRL_MOD_SHIFT (2)
#define DIALOG7212_CP_CTRL_MOD_STANDBY (0 << DIALOG7212_CP_CTRL_MOD_SHIFT)
#define DIALOG7212_CP_CTRL_MOD_CPVDD_2 (2 << DIALOG7212_CP_CTRL_MOD_SHIFT)
#define DIALOG7212_CP_CTRL_MOD_CPVDD_1 (3 << DIALOG7212_CP_CTRL_MOD_SHIFT)
#define DIALOG7212_CP_CTRL_ANALOG_VLL_MASK (0x03)
#define DIALOG7212_CP_CTRL_ANALOG_VLL_SHIFT (0)
#define DIALOG7212_CP_CTRL_ANALOG_VLL_NO_FEEDBACK (0 << DIALOG7212_CP_CTRL_ANALOG_VLL_SHIFT)
#define DIALOG7212_CP_CTRL_ANALOG_VLL_LV_BOOSTS_CP (1 << DIALOG7212_CP_CTRL_ANALOG_VLL_SHIFT)
#define DIALOG7212_CP_CTRL_ANALOG_VLL_LV_RESTART_CP (2 << DIALOG7212_CP_CTRL_ANALOG_VLL_SHIFT)

/* DIALOG7212_HP_L_GAIN                          0x48 */
#define DIALOG7212_HP_L_AMP_GAIN_MASK (0x3F)
#define DIALOG7212_HP_L_AMP_GAIN_SHIFT (0)
#define DIALOG7212_HP_L_AMP_GAIN(x) (x << DIALOG7212_HP_L_AMP_GAIN_SHIFT)

/* DIALOG7212_HP_R_GAIN                          0x49 */
#define DIALOG7212_HP_R_AMP_GAIN_MASK (0x3F)
#define DIALOG7212_HP_R_AMP_GAIN_SHIFT (0)
#define DIALOG7212_HP_R_AMP_GAIN(x) (x << DIALOG7212_HP_R_AMP_GAIN_SHIFT)

/* DIALOG7212_LINE_GAIN                          0x4A */
#define DIALOG7212_LINE_AMP_GAIN_MASK (0x3F)
#define DIALOG7212_LINE_AMP_GAIN_SHIFT (0)
#define DIALOG7212_LINE_AMP_GAIN(x) (x << DIALOG7212_LINE_AMP_GAIN_SHIFT)

/* DIALOG7212_MIXOUT_L_SELECT                    0x4B */
#define DIALOG7212_MIXOUT_L_SELECT_MIXIN_R_INV_MASK (1 << 6)
#define DIALOG7212_MIXOUT_L_SELECT_MIXIN_L_INV_MASK (1 << 5)
#define DIALOG7212_MIXOUT_L_SELECT_AUX_L_INV_MASK (1 << 4)
#define DIALOG7212_MIXOUT_L_SELECT_DAC_L_MASK (1 << 3)
#define DIALOG7212_MIXOUT_L_SELECT_MIXIN_R_MASK (1 << 2)
#define DIALOG7212_MIXOUT_L_SELECT_MIXIN_L_MASK (1 << 1)
#define DIALOG7212_MIXOUT_L_SELECT_AUX_L_MASK (1 << 0)

/* DIALOG7212_MIXOUT_R_SELECT                    0x4C */
#define DIALOG7212_MIXOUT_R_SELECT_MIXIN_L_INV_MASK (1 << 6)
#define DIALOG7212_MIXOUT_R_SELECT_MIXIN_R_INV_MASK (1 << 5)
#define DIALOG7212_MIXOUT_R_SELECT_AUX_R_INV_MASK (1 << 4)
#define DIALOG7212_MIXOUT_R_SELECT_DAC_R_MASK (1 << 3)
#define DIALOG7212_MIXOUT_R_SELECT_MIXIN_L_MASK (1 << 2)
#define DIALOG7212_MIXOUT_R_SELECT_MIXIN_R_MASK (1 << 1)
#define DIALOG7212_MIXOUT_R_SELECT_AUX_R_MASK (1 << 0)

/**************System Controller Registers(1)*************/
/* DIALOG7212_SYSTEM_MODES_INPUT                 0x50 */
#define DIALOG7212_SYSTEM_MODES_INPUT_ADC_R_MASK (1 << 7)
#define DIALOG7212_SYSTEM_MODES_INPUT_ADC_L_MASK (1 << 6)
#define DIALOG7212_SYSTEM_MODES_INPUT_MIXIN_R_MASK (1 << 5)
#define DIALOG7212_SYSTEM_MODES_INPUT_MIXIN_L_MASK (1 << 4)
#define DIALOG7212_SYSTEM_MODES_INPUT_MIC_2_MASK (1 << 3)
#define DIALOG7212_SYSTEM_MODES_INPUT_MIC_1_MASK (1 << 2)
#define DIALOG7212_SYSTEM_MODES_INPUT_MIC_BIAS_MASK (1 << 1)
#define DIALOG7212_SYSTEM_MODES_INPUT_MODE_SUBMIT_MASK (1 << 0)

/* DIALOG7212_SYSTEM_MODES_OUTPUT                0x51 */
#define DIALOG7212_SYSTEM_MODES_OUTPUT_DAC_R_MASK (1 << 7)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_DAC_L_MASK (1 << 6)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_HP_R_MASK (1 << 5)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_HP_L_MASK (1 << 4)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_LINE_MASK (1 << 3)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_AUX_R_MASK (1 << 2)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_AUX_L_MASK (1 << 1)
#define DIALOG7212_SYSTEM_MODES_OUTPUT_MODE_SUBMIT_MASK (1 << 0)

/*****************Control Registers(2)********************/
/* DIALOG7212_AUX_L_CTRL                         0x60 */
#define DIALOG7212_AUX_L_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_AUX_L_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_AUX_L_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_MASK (0x0C)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_SHIFT (2)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_INPUT_AUX_L (0 << DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_INPUT_AUX_L_IF (1 << DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_NO_ZC_POSSBLE (2 << DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_OUTPUT_AUX_L (3 << DIALOG7212_AUX_L_CTRL_AMP_ZC_SEL_SHIFT)

/* DIALOG7212_AUX_R_CTRL                         0x61 */
#define DIALOG7212_AUX_R_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_AUX_R_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_AUX_R_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_MASK (0x0C)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_SHIFT (2)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_INPUT_AUX_R (0 << DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_INPUT_AUX_R_IF (1 << DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_NO_ZC_POSSBLE (2 << DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_SHIFT)
#define DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_OUTPUT_AUX_R (3 << DIALOG7212_AUX_R_CTRL_AMP_ZC_SEL_SHIFT)

/* DIALOG7212_MICBIAS_CTRL                       0x62 */
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_EN_MASK (1 << 7)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_LEVEL_MASK (0x30)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_SHIFT (4)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_1_6V (0 << DIALOG7212_MICBIAS_CTRL_MICBIAS2_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_2_2V (1 << DIALOG7212_MICBIAS_CTRL_MICBIAS2_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_2_5V (2 << DIALOG7212_MICBIAS_CTRL_MICBIAS2_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS2_3_0V (3 << DIALOG7212_MICBIAS_CTRL_MICBIAS2_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_EN_MASK (1 << 3)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_LEVEL_MASK (0x03)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_SHIFT (0)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_1_6V (0 << DIALOG7212_MICBIAS_CTRL_MICBIAS1_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_2_2V (1 << DIALOG7212_MICBIAS_CTRL_MICBIAS1_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_2_5V (2 << DIALOG7212_MICBIAS_CTRL_MICBIAS1_SHIFT)
#define DIALOG7212_MICBIAS_CTRL_MICBIAS1_3_0V (3 << DIALOG7212_MICBIAS_CTRL_MICBIAS1_SHIFT)

/* DIALOG7212_MIC_1_CTRL                         0x63 */
#define DIALOG7212_MIC_1_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIC_1_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_MASK (0x0C)
#define DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_SHIFT (2)
#define DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_DIFFERENTIAL (0 << DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_SHIFT)
#define DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_MIC_1_P (1 << DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_SHIFT)
#define DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_MIC_1_N (2 << DIALOG7212_MIC_1_CTRL_AMP_IN_SEL_SHIFT)

/* DIALOG7212_MIC_2_CTRL                         0x64 */
#define DIALOG7212_MIC_2_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIC_2_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_MASK (0x0C)
#define DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_SHIFT (2)
#define DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_DIFFERENTIAL (0 << DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_SHIFT)
#define DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_MIC_2_P (1 << DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_SHIFT)
#define DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_MIC_2_N (2 << DIALOG7212_MIC_2_CTRL_AMP_IN_SEL_SHIFT)

/* DIALOG7212_MIXIN_L_CTRL                       0x65 */
#define DIALOG7212_MIXIN_L_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIXIN_L_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_MIXIN_L_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_MIXIN_L_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_MIXIN_L_CTRL_AMP_MIX_EN_MASK (1 << 3)

/* DIALOG7212_MIXIN_R_CTRL                       0x66 */
#define DIALOG7212_MIXIN_R_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIXIN_R_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_MIXIN_R_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_MIXIN_R_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_MIXIN_R_CTRL_AMP_MIX_EN_MASK (1 << 3)

/* DIALOG7212_ADC_L_CTRL                         0x67 */
#define DIALOG7212_ADC_L_CTRL_ADC_EN_MASK (1 << 7)
#define DIALOG7212_ADC_L_CTRL_ADC_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_ADC_L_CTRL_ADC_RAMP_EN_MASK (1 << 5)

/* DIALOG7212_ADC_R_CTRL                         0x68 */
#define DIALOG7212_ADC_R_CTRL_ADC_EN_MASK (1 << 7)
#define DIALOG7212_ADC_R_CTRL_ADC_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_ADC_R_CTRL_ADC_RAMP_EN_MASK (1 << 5)

/* DIALOG7212_DAC_L_CTRL                         0x69 */
#define DIALOG7212_DAC_L_CTRL_ADC_EN_MASK (1 << 7)
#define DIALOG7212_DAC_L_CTRL_ADC_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_DAC_L_CTRL_ADC_RAMP_EN_MASK (1 << 5)

/* DIALOG7212_DAC_R_CTRL                         0x6A */
#define DIALOG7212_DAC_R_CTRL_ADC_EN_MASK (1 << 7)
#define DIALOG7212_DAC_R_CTRL_ADC_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_DAC_R_CTRL_ADC_RAMP_EN_MASK (1 << 5)

/* DIALOG7212_HP_L_CTRL                          0x6B */
#define DIALOG7212_HP_L_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_HP_L_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_HP_L_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_HP_L_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_HP_L_CTRL_AMP_OE_MASK (1 << 3)
#define DIALOG7212_HP_L_CTRL_AMP_MIN_GAIN_EN_MASK (1 << 2)

/* DIALOG7212_HP_R_CTRL                          0x6C */
#define DIALOG7212_HP_R_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_HP_R_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_HP_R_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_HP_R_CTRL_AMP_ZC_EN_MASK (1 << 4)
#define DIALOG7212_HP_R_CTRL_AMP_OE_MASK (1 << 3)
#define DIALOG7212_HP_R_CTRL_AMP_MIN_GAIN_EN_MASK (1 << 2)

/* DIALOG7212_LINE_CTRL                          0x6D */
#define DIALOG7212_LINE_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_LINE_CTRL_AMP_MUTE_EN_MASK (1 << 6)
#define DIALOG7212_LINE_CTRL_AMP_RAMP_EN_MASK (1 << 5)
#define DIALOG7212_LINE_CTRL_AMP_OE_MASK (1 << 3)
#define DIALOG7212_LINE_CTRL_AMP_MIN_GAIN_EN_MASK (1 << 2)

/* DIALOG7212_MIXOUT_L_CTRL                      0x6E */
#define DIALOG7212_MIXOUT_L_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIXOUT_L_CTRL_AMP_SOFT_MIX_EN_MASK (1 << 4)
#define DIALOG7212_MIXOUT_L_CTRL_AMP_MIX_EN_MASK (1 << 3)

/* DIALOG7212_MIXOUT_R_CTRL                      0x6F */
#define DIALOG7212_MIXOUT_R_CTRL_AMP_EN_MASK (1 << 7)
#define DIALOG7212_MIXOUT_R_CTRL_AMP_SOFT_MIX_EN_MASK (1 << 4)
#define DIALOG7212_MIXOUT_R_CTRL_AMP_MIX_EN_MASK (1 << 3)

/*****************Configuration Registers*****************/
/* DIALOG7212_LDO_CTRL                           0x90 */
#define DIALOG7212_LDO_CTRL_EN_MASK (1 << 7)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_MASK (0x30)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_SHIFT (4)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_1_05V (0 << DIALOG7212_LDO_CTRL_LEVEL_SELECT_SHIFT)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_1_10V (1 << DIALOG7212_LDO_CTRL_LEVEL_SELECT_SHIFT)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_1_20V (2 << DIALOG7212_LDO_CTRL_LEVEL_SELECT_SHIFT)
#define DIALOG7212_LDO_CTRL_LEVEL_SELECT_1_40V (3 << DIALOG7212_LDO_CTRL_LEVEL_SELECT_SHIFT)

/* DIALOG7212_GAIN_RAMP_CTRL                     0x92 */
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_MASK (0x03)
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_SHIFT (0)
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_NR_DIV_8 (0 << DIALOG7212_GAIN_RAMP_CTRL_RATE_SHIFT)
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_NR_DIV_16 (1 << DIALOG7212_GAIN_RAMP_CTRL_RATE_SHIFT)
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_NR_MUL_16 (2 << DIALOG7212_GAIN_RAMP_CTRL_RATE_SHIFT)
#define DIALOG7212_GAIN_RAMP_CTRL_RATE_NR_MUL_32 (3 << DIALOG7212_GAIN_RAMP_CTRL_RATE_SHIFT)

/* DIALOG7212_MIC_CONFIG                         0x93 */
#define DIALOG7212_MIC_CONFIG_DMIC_CLK_RATE_MASK (1 << 2)
#define DIALOG7212_MIC_CONFIG_DMIC_SAMPLEPHASE_MASK (1 << 1)
#define DIALOG7212_MIC_CONFIG_DMIC_DATA_SEL_MASK (1 << 0)

/* DIALOG7212_PC_COUNT                           0x94 */
#define DIALOG7212_PC_COUNT_RESYNC_MASK (1 << 1)
#define DIALOG7212_PC_COUNT_FREERU_MASK (1 << 0)

/* DIALOG7212_CP_VOL_THRESHOLD1                  0x95 */
#define DIALOG7212_CP_VOL_THRESHOLD1_VDD2_MASK (0x3F)
#define DIALOG7212_CP_VOL_THRESHOLD1_VDD2_SHIFT (0)
#define DIALOG7212_CP_VOL_THRESHOLD1_VDD2(x) (x << DIALOG7212_CP_VOL_THRESHOLD1_VDD2_SHIFT)

/* DIALOG7212_CP_DELAY                           0x96 */
#define DIALOG7212_CP_DELAY_ON_OFF_MASK (0xC0)
#define DIALOG7212_CP_DELAY_ON_OFF_SHIFT (6)
#define DIALOG7212_CP_DELAY_ON_OFF_LIMITER_ON (0 << DIALOG7212_CP_DELAY_ON_OFF_SHIFT)
#define DIALOG7212_CP_DELAY_ON_OFF_LIMITER_OFF (1 << DIALOG7212_CP_DELAY_ON_OFF_SHIFT)
#define DIALOG7212_CP_DELAY_ON_OFF_LIMITER_AUT (2 << DIALOG7212_CP_DELAY_ON_OFF_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_MASK (0x38)
#define DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT (3)
#define DIALOG7212_CP_DELAY_TAU_DELAY_0MS (0 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_2MS (1 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_4MS (2 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_16MS (3 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_64MS (4 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_128MS (5 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_256MS (6 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_TAU_DELAY_512MS (7 << DIALOG7212_CP_DELAY_TAU_DELAY_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_MASK (0x07)
#define DIALOG7212_CP_DELAY_FCONTROL_SHIFT (0)
#define DIALOG7212_CP_DELAY_FCONTROL_1MHZ (0 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_500KHZ (1 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_250KHZ (2 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_125KHZ (3 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_63KHZ (4 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)
#define DIALOG7212_CP_DELAY_FCONTROL_0HZ_OR_1MHZ (5 << DIALOG7212_CP_DELAY_FCONTROL_SHIFT)

/* DIALOG7212_CP_DETECTOR                        0x97 */
#define DIALOG7212_CP_DETECTOR_DROP_MASK (0x03)
#define DIALOG7212_CP_DETECTOR_DROP_SHIFT (0)
#define DIALOG7212_CP_DETECTOR_DROP_25MV (0 << DIALOG7212_CP_DETECTOR_DROP_SHIFT)
#define DIALOG7212_CP_DETECTOR_DROP_50MV (1 << DIALOG7212_CP_DETECTOR_DROP_SHIFT)
#define DIALOG7212_CP_DETECTOR_DROP_75MV (2 << DIALOG7212_CP_DETECTOR_DROP_SHIFT)
#define DIALOG7212_CP_DETECTOR_DROP_100MV (3 << DIALOG7212_CP_DETECTOR_DROP_SHIFT)

/* DIALOG7212_DAI_OFFSET                         0x98 */
#define DIALOG7212_DAI_OFFSET_MASK (0xFF)
#define DIALOG7212_DAI_OFFSET_SHIFT (0)
#define DIALOG7212_DAI_OFFSET_VAL(x) (x << DIALOG7212_DAI_OFFSET_SHIFT)

/* DIALOG7212_DIG_CTRL                           0x99 */
#define DIALOG7212_DIG_CTRL_R_INV_MASK (1 << 7)
#define DIALOG7212_DIG_CTRL_L_INV_MASK (1 << 3)

/* DIALOG7212_ALC_CTRL2                          0x9A */
#define DIALOG7212_ALC_CTRL2_RELEASE_MASK (0xF0)
#define DIALOG7212_ALC_CTRL2_RELEASE_SHIFT (4)
#define DIALOG7212_ALC_CTRL2_RELEASE(x) (x << DIALOG7212_ALC_CTRL2_RELEASE_SHIFT)
#define DIALOG7212_ALC_CTRL2_ATTACK_MASK (0x0F)
#define DIALOG7212_ALC_CTRL2_ATTACK_SHIFT (0)
#define DIALOG7212_ALC_CTRL2_ATTACK(x) (x << DIALOG7212_ALC_CTRL2_RELEASE_SHIFT)

/* DIALOG7212_ALC_CTRL3                          0x9B */
#define DIALOG7212_ALC_CTRL3_INTEG_RELEASE_MASK (0xC0)
#define DIALOG7212_ALC_CTRL3_INTEG_RELEASE_SHIFT (6)
#define DIALOG7212_ALC_CTRL3_INTEG_RELEASE_1DIV4 (0 << DIALOG7212_ALC_CTRL3_INTEG_RELEASE_SHIFT)
#define DIALOG7212_ALC_CTRL3_INTEG_RELEASE_1DIV16 (1 << DIALOG7212_ALC_CTRL3_INTEG_RELEASE_SHIFT)
#define DIALOG7212_ALC_CTRL3_INTEG_RELEASE_1DIV256 (2 << DIALOG7212_ALC_CTRL3_INTEG_RELEASE_SHIFT)
#define DIALOG7212_ALC_CTRL3_INTEG_ATTACK_MASK (0x30)
#define DIALOG7212_ALC_CTRL3_INTEG_ATTACK_SHIFT (4)
#define DIALOG7212_ALC_CTRL3_INTEG_ATTACK_1DIV4 (0 << DIALOG7212_ALC_CTRL3_INTEG_ATTACK_SHIFT)
#define DIALOG7212_ALC_CTRL3_INTEG_ATTACK_1DIV16 (1 << DIALOG7212_ALC_CTRL3_INTEG_ATTACK_SHIFT)
#define DIALOG7212_ALC_CTRL3_INTEG_ATTACK_1DIV256 (2 << DIALOG7212_ALC_CTRL3_INTEG_ATTACK_SHIFT)
#define DIALOG7212_ALC_CTRL3_HOLD_MASK (0x0F)
#define DIALOG7212_ALC_CTRL3_HOLD_SHIFT (0)
#define DIALOG7212_ALC_CTRL3_HOLD(x) (x << DIALOG7212_ALC_CTRL3_HOLD_SHIFT)

/* DIALOG7212_ALC_NOISE                          0x9C */
#define DIALOG7212_ALC_NOISE_MASK (0x3F)
#define DIALOG7212_ALC_NOISE_SHIFT (0)
#define DIALOG7212_ALC_NOISE_VAL(x) (x << DIALOG7212_ALC_NOISE_SHIFT)

/* DIALOG7212_ALC_TARGET_MIN                     0x9D */
#define DIALOG7212_ALC_TARGET_MIN_THRESHOLD_MIN_MASK (0x3F)
#define DIALOG7212_ALC_TARGET_MIN_THRESHOLD_MIN_SHIFT (0)
#define DIALOG7212_ALC_TARGET_MIN_THRESHOLD_MIN(x) (x << DIALOG7212_ALC_TARGET_MIN_THRESHOLD_MIN_SHIFT)

/* DIALOG7212_ALC_TARGET_MAX                     0x9E */
#define DIALOG7212_ALC_TARGET_MAX_THRESHOLD_MAX_MASK (0x3F)
#define DIALOG7212_ALC_TARGET_MAX_THRESHOLD_MAX_SHIFT (0)
#define DIALOG7212_ALC_TARGET_MAX_THRESHOLD_MAX(x) (x << DIALOG7212_ALC_TARGET_MAX_THRESHOLD_MAX_SHIFT)

/* DIALOG7212_ALC_GAIN_LIMITS                    0x9F */
#define DIALOG7212_ALC_GAIN_LIMITS_GAIN_MAX_MASK (0xF0)
#define DIALOG7212_ALC_GAIN_LIMITS_GAIN_MAX_SHIFT (4)
#define DIALOG7212_ALC_GAIN_LIMITS_GAIN_MAX(x) (x << DIALOG7212_ALC_GAIN_LIMITS_GAIN_MAX_SHIFT)
#define DIALOG7212_ALC_GAIN_LIMITS_ATTEN_MAX_MASK (0x0F)
#define DIALOG7212_ALC_GAIN_LIMITS_ATTEN_MAX_SHIFT (0)
#define DIALOG7212_ALC_GAIN_LIMITS_ATTEN_MAX(x) (x << DIALOG7212_ALC_GAIN_LIMITS_ATTEN_MAX_SHIFT)

/* DIALOG7212_ALC_ANA_GAIN_LIMITS                0xA0 */
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_MASK (0x70)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT (4)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_0DB (1 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_6DB (2 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_12DB (3 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_18DB (4 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_24DB (5 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_30DB (6 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_36DB (7 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MAX_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_MASK (0x07)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT (0)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_0DB (1 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_6DB (2 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_12DB (3 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_18DB (4 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_24DB (5 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_30DB (6 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)
#define DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_36DB (7 << DIALOG7212_ALC_ANA_GAIN_LIMITS_MIN_SHIFT)

/* DIALOG7212_ALC_ANTICLIP_CTRL                  0xA1 */
#define DIALOG7212_ALC_ANTICLIP_CTRL_EN_MASK (1 << 7)

/* DIALOG7212_ALC_ANTICLIP_LEVEL                 0xA2 */
#define DIALOG7212_ALC_ANTICLIP_LEVEL_MASK (0x7F)
#define DIALOG7212_ALC_ANTICLIP_LEVEL_SHIFT (0)
#define DIALOG7212_ALC_ANTICLIP_LEVEL_VAL(x) (x << DIALOG7212_ALC_ANTICLIP_LEVEL_SHIFT)

/* DIALOG7212_DAC_NG_SETUP_TIME                  0xAF */
#define DIALOG7212_DAC_NG_SETUP_TIME_RAMPDN_RATE_MASK (1 << 3)
#define DIALOG7212_DAC_NG_SETUP_TIME_RAMPUP_RATE_MASK (1 << 2)
#define DIALOG7212_DAC_NG_SETUP_TIME_MASK (0x03)
#define DIALOG7212_DAC_NG_SETUP_TIME_SHIFT (0)
#define DIALOG7212_DAC_NG_SETUP_TIME_256 (1 << DIALOG7212_DAC_NG_SETUP_TIME_SHIFT)
#define DIALOG7212_DAC_NG_SETUP_TIME_512 (2 << DIALOG7212_DAC_NG_SETUP_TIME_SHIFT)
#define DIALOG7212_DAC_NG_SETUP_TIME_1024 (3 << DIALOG7212_DAC_NG_SETUP_TIME_SHIFT)
#define DIALOG7212_DAC_NG_SETUP_TIME_2048 (4 << DIALOG7212_DAC_NG_SETUP_TIME_SHIFT)

/* DIALOG7212_DAC_NG_OFF_THRESH                  0xB0 */
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_MASK (0x07)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT (0)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N90DB (0 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N84DB (1 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N78DB (2 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N72DB (3 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N66DB (4 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N60DB (5 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N54DB (6 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_OFF_THRESHOLD_N48DB (7 << DIALOG7212_DAC_NG_OFF_THRESHOLD_SHIFT)

/* DIALOG7212_DAC_NG_ON_THRESH                   0xB1 */
#define DIALOG7212_DAC_NG_ON_THRESHOLD_MASK (0x07)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT (0)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N90DB (0 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N84DB (1 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N78DB (2 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N72DB (3 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N66DB (4 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N60DB (5 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N54DB (6 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)
#define DIALOG7212_DAC_NG_ON_THRESHOLD_N48DB (7 << DIALOG7212_DAC_NG_ON_THRESHOLD_SHIFT)

/* DIALOG7212_DAC_NG_CTRL                        0xB2 */
#define DIALOG7212_DAC_NG_CTRL_EN_MASK (1 << 7)

/*************Tone Generation & Beep Registers************/
/* DIALOG7212_TONE_GEN_CFG1                      0xB4 */
#define DIALOG7212_TONE_GEN_CFG1_START_STOPN_MASK (1 << 7)
#define DIALOG7212_TONE_GEN_CFG1_DMTF_EN_MASK (1 << 4)
#define DIALOG7212_TONE_GEN_CFG1_DMTF_REG_MASK (0x0F)
#define DIALOG7212_TONE_GEN_CFG1_DMTF_REG_SHIFT (0)
#define DIALOG7212_TONE_GEN_CFG1_DMTF_REG(x) (x << DIALOG7212_TONE_GEN_CFG1_DMTF_REG_SHIFT)

/* DIALOG7212_TONE_GEN_CFG2                      0xB5 */
#define DIALOG7212_TONE_GEN_CFG2_GAIN_MASK (0xF0)
#define DIALOG7212_TONE_GEN_CFG2_GAIN_SHIFT (4)
#define DIALOG7212_TONE_GEN_CFG2_GAIN(x) (x << DIALOG7212_TONE_GEN_CFG2_GAIN_SHIFT)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_MASK (0x03)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SHIFT (0)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SUM__BOTH (0 << DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SHIFT)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SWG1_ONLY (1 << DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SHIFT)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SWG2_ONLY (2 << DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SHIFT)
#define DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SUM_BOTH (3 << DIALOG7212_TONE_GEN_CFG2_SWG_SEL_SHIFT)

/* DIALOG7212_TONE_GEN_CYCLES                    0xB6 */
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_MASK (0x07)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT (0)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_1 (0 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_2 (1 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_4 (2 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_8 (3 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_16 (4 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_32 (5 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)
#define DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_INFINITE (6 << DIALOG7212_TONE_GEN_CYCLES_BEEP_CYCLES_SHIFT)

/* DIALOG7212_TONE_GEN_FREQ1_L                   0xB7 */
#define DIALOG7212_TONE_GEN_FREQ1_L_MASK (0xFF)
#define DIALOG7212_TONE_GEN_FREQ1_L_SHIFT (0)
#define DIALOG7212_TONE_GEN_FREQ1_L_VAL(x) (x << DIALOG7212_TONE_GEN_FREQ1_L_SHIFT)

/* DIALOG7212_TONE_GEN_FREQ1_U                   0xB8 */
#define DIALOG7212_TONE_GEN_FREQ1_U_MASK (0xFF)
#define DIALOG7212_TONE_GEN_FREQ1_U_SHIFT (0)
#define DIALOG7212_TONE_GEN_FREQ1_U_VAL(x) (x << DIALOG7212_TONE_GEN_FREQ1_U_SHIFT)

/* DIALOG7212_TONE_GEN_FREQ2_L                   0xB9 */
#define DIALOG7212_TONE_GEN_FREQ2_L_MASK (0xFF)
#define DIALOG7212_TONE_GEN_FREQ2_L_SHIFT (0)
#define DIALOG7212_TONE_GEN_FREQ2_L_VAL(x) (x << DIALOG7212_TONE_GEN_FREQ2_L_SHIFT)

/* DIALOG7212_TONE_GEN_FREQ2_U                   0xBA */
#define DIALOG7212_TONE_GEN_FREQ2_U_MASK (0xFF)
#define DIALOG7212_TONE_GEN_FREQ2_U_SHIFT (0)
#define DIALOG7212_TONE_GEN_FREQ2_U_VAL(x) (x << DIALOG7212_TONE_GEN_FREQ2_U_SHIFT)

/* DIALOG7212_TONE_GEN_ON_PER                    0xBB */
#define DIALOG7212_TONE_GEN_ON_PER_BEEP_ON_PER_MASK (0x3F)
#define DIALOG7212_TONE_GEN_ON_PER_BEEP_ON_PER_SHIFT (0)
#define DIALOG7212_TONE_GEN_ON_PER_BEEP_ON_PER(x) (x << DIALOG7212_TONE_GEN_ON_PER_BEEP_ON_PER_SHIFT)

/* DIALOG7212_TONE_GEN_OFF_PER                   0xBC */
#define DIALOG7212_TONE_GEN_OFF_PER_BEEP_OFF_PER_MASK (0x3F)
#define DIALOG7212_TONE_GEN_OFF_PER_BEEP_OFF_PER_SHIFT (0)
#define DIALOG7212_TONE_GEN_OFF_PER_BEEP_OFF_PER(x) (x << DIALOG7212_TONE_GEN_OFF_PER_BEEP_OFF_PER_SHIFT)

/*************System Controller Registers(2)*************/
/* DIALOG7212_SYSTEM_STATUS                      0xE0 */
#define DIALOG7212_SYSTEM_STATUS_SC2_BUSY_MASK (1 << 1)
#define DIALOG7212_SYSTEM_STATUS_SC1_BUSY_MASK (1 << 0)

/* DIALOG7212_SYSTEM_ACTIVE                      0xFD */
#define DIALOG7212_SYSTEM_ACTIVE_MASK (1 << 0)

/*! @brief DA7212 input source select */
typedef enum _da7212_Input
{
    kDA7212_Input_AUX = 0x0, /*!< Input from AUX */
    kDA7212_Input_MIC1_Dig,  /*!< Input from MIC1 Digital */
    kDA7212_Input_MIC1_An,   /*!< Input from Mic1 Analog */
    kDA7212_Input_MIC2,      /*!< Input from MIC2 */
    kDA7212_Input_MAX
} da7212_Input_t;

/*! @brief da7212 play channel  */
enum _da7212_play_channel
{
    kDA7212_HeadphoneLeft  = 1U, /*!< headphone left */
    kDA7212_HeadphoneRight = 2U, /*!< headphone right */
    kDA7212_Speaker        = 4U, /*!< speaker channel */
};

/*! @brief DA7212 output device select */
typedef enum _da7212_Output
{
    kDA7212_Output_HP = 0x0, /*!< Output to headphone */
    kDA7212_Output_SP,       /*!< Output to speaker */
    kDA7212_Output_MAX
} da7212_Output_t;

/*! @brief DA7212 module */
enum _da7212_module
{
    kDA7212_ModuleADC,       /*!< module ADC*/
    kDA7212_ModuleDAC,       /*!< module DAC */
    kDA7212_ModuleHeadphone, /*!< module headphone */
    kDA7212_ModuleSpeaker,   /*!< module speaker */
};

/*! @brief DA7212 functionality */
typedef enum _da7212_dac_source
{
    kDA7212_DACSourceADC         = 0x0U, /*!< DAC source from ADC */
    kDA7212_DACSourceInputStream = 0x3U  /*!< DAC source from  */
} da7212_dac_source_t;

/*! @brief DA7212 volume */
typedef enum _da7212_volume
{
    kDA7212_DACGainMute  = 0x7,  /*!< Mute DAC */
    kDA7212_DACGainM72DB = 0x17, /*!< DAC volume -72db */
    kDA7212_DACGainM60DB = 0x1F, /*!< DAC volume -60db */
    kDA7212_DACGainM54DB = 0x27, /*!< DAC volume -54db */
    kDA7212_DACGainM48DB = 0x2F, /*!< DAC volume -48db */
    kDA7212_DACGainM42DB = 0x37, /*!< DAC volume -42db */
    kDA7212_DACGainM36DB = 0x3F, /*!< DAC volume -36db */
    kDA7212_DACGainM30DB = 0x47, /*!< DAC volume -30db */
    kDA7212_DACGainM24DB = 0x4F, /*!< DAC volume -24db */
    kDA7212_DACGainM18DB = 0x57, /*!< DAC volume -18db */
    kDA7212_DACGainM12DB = 0x5F, /*!< DAC volume -12db */
    kDA7212_DACGainM6DB  = 0x67, /*!< DAC volume -6bb */
    kDA7212_DACGain0DB   = 0x6F, /*!< DAC volume +0db */
    kDA7212_DACGain6DB   = 0x77, /*!< DAC volume +6db */
    kDA7212_DACGain12DB  = 0x7F  /*!< DAC volume +12db */
} da7212_volume_t;

/*!
 * @brief The audio data transfer protocol choice.
 */
typedef enum _da7212_protocol
{
    kDA7212_BusI2S = 0x0,      /*!< I2S Type */
    kDA7212_BusLeftJustified,  /*!< Left justified */
    kDA7212_BusRightJustified, /*!< Right Justified */
    kDA7212_BusDSPMode,        /*!< DSP mode */
} da7212_protocol_t;

/*! @brief da7212 audio format */
typedef struct _da7212_audio_format
{
    uint32_t mclk_HZ;    /*!< master clock frequency */
    uint32_t sampleRate; /*!< sample rate */
    uint32_t bitWidth;   /*!< bit width */
} da7212_audio_format_t;

/*! @brief DA7212 configure structure */
typedef struct da7212_config
{
    uint8_t isMaster;                 /*!< If DA7212 is master, 1 means master, 0 means slave. */
    da7212_protocol_t protocol;    /*!< Audio bus format, can be I2S, LJ, RJ or DSP mode. */
    da7212_dac_source_t dacSource; /*!< DA7212 data source. */
    da7212_audio_format_t format;  /*!< audio format */
    uint8_t slaveAddress;          /*!< device address */
} da7212_config_t;

/*! @brief da7212 codec handler
 */
typedef struct _da7212_handle
{
    da7212_config_t config;                    /*!< da7212 config structure */
    uint32_t i2cHandle;                   /*!< i2c handle */
} da7212_handle_t;

/*******************************************************************************
 * API
 ******************************************************************************/

#if defined(__cplusplus)
extern "C" {
#endif

/*!
 * @brief DA7212 initialize function.
 *
 * @param handle DA7212 handle pointer.
 * @param config Codec configure structure. This parameter can be NULL, if NULL, set as default settings.
 * The default setting:
 * @return 0 if success.
 */
int DA7212_Init(da7212_handle_t *handle, da7212_config_t *config);

/*!
 * @brief Set DA7212 audio format.
 *
 * @param handle DA7212 handle pointer.
 * @param sampleRate_Hz Sample rate frequency in Hz.
 * @param masterClock_Hz Master clock frequency in Hz. If DA7212 is slave, use the frequency of master, if DA7212 as
 * master, it should be 1228000 while sample rate frequency is 8k/12K/16K/24K/32K/48K/96K, 11289600 whie sample rate is
 * 11.025K/22.05K/44.1K
 * @param dataBits How many bits in a word of a audio frame, DA7212 only supports 16/20/24/32 bits.
 * @return 0 if success.
 */
int DA7212_ConfigAudioFormat(da7212_handle_t *handle,
                             uint32_t masterClock_Hz,
                             uint32_t sampleRate_Hz,
                             uint32_t dataBits);

/*!
 * @brief Set DA7212 playback volume.
 *
 * @param handle DA7212 handle pointer.
 * @param volume The volume of playback.
 */
void DA7212_ChangeHPVolume(da7212_handle_t *handle, da7212_volume_t volume);

/*!
 * @brief Mute or unmute DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param isMuted True means mute, false means unmute.
 */
void DA7212_Mute(da7212_handle_t *handle, bool isMuted);

/*!
 * @brief Set the input data source of DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param DA7212_Input Input data source.
 */
void DA7212_ChangeInput(da7212_handle_t *handle, da7212_Input_t DA7212_Input);

/*!
 * @brief Set the output device of DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param DA7212_Output Output device of DA7212.
 */
void DA7212_ChangeOutput(da7212_handle_t *handle, da7212_Output_t DA7212_Output);

/*!
 * @brief Set module volume.
 *
 * @param handle DA7212 handle pointer.
 * @param module shoule be a value of _da7212_module
 * @param volume volume range 0 - 100, 0 is mute, 100 is the maximum value.
 * @return 0 if success.
 */
int DA7212_SetChannelVolume(da7212_handle_t *handle, uint32_t module, uint32_t volume);

/*!
 * @brief Set module mute.
 *
 * @param handle DA7212 handle pointer.
 * @param module shoule be a value of _da7212_module
 * @param isMute true is mute, false is unmute.
 * @return 0 if success.
 */
int DA7212_SetChannelMute(da7212_handle_t *handle, uint32_t module, bool isMute);

/*!
 * @brief Set protocol for DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param protocol da7212_protocol_t.
 * @return 0 if success.
 */
int DA7212_SetProtocol(da7212_handle_t *handle, da7212_protocol_t protocol);

/*!
 * @brief Write a register for DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param u8Register DA7212 register address to be written.
 * @param u8RegisterData Data to be written into regsiter
 * @return 0 if success.
 */
int DA7212_WriteRegister(da7212_handle_t *handle, uint8_t u8Register, uint8_t u8RegisterData);

/*!
 * @brief Get a register value of DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @param u8Register DA7212 register address to be read.
 * @param pu8RegisterData Pointer where the read out value to be stored.
 * @return 0 if success.
 */
int DA7212_ReadRegister(da7212_handle_t *handle, uint8_t u8Register, uint8_t *pu8RegisterData);

/*!
 * @brief Deinit DA7212.
 *
 * @param handle DA7212 handle pointer.
 * @return 0 if success.
 */
int DA7212_Deinit(da7212_handle_t *handle);

/*!
 * @brief Get DA7212 handle, it should be called before init.
 *
 * @param none.
 * @return handle DA7212 handle pointer if success.
 */
da7212_handle_t *DA7212_GetHandle(void);


#if defined(__cplusplus)
}
#endif

#endif /* __DIALOG7212_H__ */
