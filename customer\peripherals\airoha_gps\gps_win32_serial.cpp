﻿#include <rtthread.h>
#include "gps_win32_serial.h"
//#include "erpc_client_setup.h"
//#include "erpc_server_setup.h"
//#include "erpc_mbf_setup.h"
//#include "erpc_transport_setup.h"
//#include "erpc_serial_transport.hpp"
//
//using namespace erpc;
//
//static SerialTransport* serial = NULL;
//bool win32_serial_init(void)
//{
//	erpc_transport_t transport_serial = erpc_transport_serial_init("COM3", 921600);
//	serial = reinterpret_cast<SerialTransport*>(transport_serial);
//}
//
//bool win32_serial_send_data(const uint8_t* buf, uint32_t len)
//{
//	if (serial) {
//		if (kErpcStatus_Success == serial->sendData(buf, len)) {
//			return true;
//		}
//	}
//	return false;
//}
//
//bool win32_serial_recv_data(uint8_t* buf, uint32_t len)
//{
//	if (serial) {
//		if (kErpcStatus_Success == serial->receiveByte(buf)) {
//			return true;
//		}
//	}
//	return false;
//}