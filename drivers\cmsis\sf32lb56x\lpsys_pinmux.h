#ifndef __LPSYS_PINMUX_H
#define __LPSYS_PINMUX_H

typedef struct
{
    __IO uint32_t PAD_SC00;
    __IO uint32_t PAD_SC01;
    __IO uint32_t PAD_SC02;
    __IO uint32_t PAD_SC03;
    __IO uint32_t PAD_SC04;
    __IO uint32_t PAD_SC05;
    __IO uint32_t PAD_PB00;
    __IO uint32_t PAD_PB01;
    __IO uint32_t PAD_PB02;
    __IO uint32_t PAD_PB03;
    __IO uint32_t PAD_PB04;
    __IO uint32_t PAD_PB05;
    __IO uint32_t PAD_PB06;
    __IO uint32_t PAD_PB07;
    __IO uint32_t PAD_PB08;
    __IO uint32_t PAD_PB09;
    __IO uint32_t PAD_PB10;
    __IO uint32_t PAD_PB11;
    __IO uint32_t PAD_PB12;
    __IO uint32_t PAD_PB13;
    __IO uint32_t PAD_PB14;
    __IO uint32_t PAD_PB15;
    __IO uint32_t PAD_PB16;
    __IO uint32_t PAD_PB17;
    __IO uint32_t PAD_PB18;
    __IO uint32_t PAD_PB19;
    __IO uint32_t PAD_PB20;
    __IO uint32_t PAD_PB21;
    __IO uint32_t PAD_PB22;
    __IO uint32_t PAD_PB23;
    __IO uint32_t PAD_PB24;
    __IO uint32_t PAD_PB25;
    __IO uint32_t PAD_PB26;
    __IO uint32_t PAD_PB27;
    __IO uint32_t PAD_PB28;
    __IO uint32_t PAD_PB29;
    __IO uint32_t PAD_PB30;
    __IO uint32_t PAD_PB31;
    __IO uint32_t PAD_PB32;
    __IO uint32_t PAD_PB33;
    __IO uint32_t PAD_PB34;
    __IO uint32_t PAD_PB35;
    __IO uint32_t PAD_PB36;
    __IO uint32_t CR;
} LPSYS_PINMUX_TypeDef;


/************* Bit definition for LPSYS_PINMUX_PAD_SC00 register **************/
#define LPSYS_PINMUX_PAD_SC00_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC00_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC00_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC00_FSEL      LPSYS_PINMUX_PAD_SC00_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC00_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC00_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC00_PE_Pos)
#define LPSYS_PINMUX_PAD_SC00_PE        LPSYS_PINMUX_PAD_SC00_PE_Msk
#define LPSYS_PINMUX_PAD_SC00_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC00_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC00_PS_Pos)
#define LPSYS_PINMUX_PAD_SC00_PS        LPSYS_PINMUX_PAD_SC00_PS_Msk
#define LPSYS_PINMUX_PAD_SC00_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC00_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC00_IE_Pos)
#define LPSYS_PINMUX_PAD_SC00_IE        LPSYS_PINMUX_PAD_SC00_IE_Msk
#define LPSYS_PINMUX_PAD_SC00_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC00_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC00_IS_Pos)
#define LPSYS_PINMUX_PAD_SC00_IS        LPSYS_PINMUX_PAD_SC00_IS_Msk
#define LPSYS_PINMUX_PAD_SC00_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC00_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC00_SR_Pos)
#define LPSYS_PINMUX_PAD_SC00_SR        LPSYS_PINMUX_PAD_SC00_SR_Msk
#define LPSYS_PINMUX_PAD_SC00_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC00_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC00_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC00_DS0       LPSYS_PINMUX_PAD_SC00_DS0_Msk
#define LPSYS_PINMUX_PAD_SC00_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC00_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC00_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC00_DS1       LPSYS_PINMUX_PAD_SC00_DS1_Msk
#define LPSYS_PINMUX_PAD_SC00_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC00_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC00_POE_Pos)
#define LPSYS_PINMUX_PAD_SC00_POE       LPSYS_PINMUX_PAD_SC00_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_SC01 register **************/
#define LPSYS_PINMUX_PAD_SC01_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC01_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC01_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC01_FSEL      LPSYS_PINMUX_PAD_SC01_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC01_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC01_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC01_PE_Pos)
#define LPSYS_PINMUX_PAD_SC01_PE        LPSYS_PINMUX_PAD_SC01_PE_Msk
#define LPSYS_PINMUX_PAD_SC01_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC01_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC01_PS_Pos)
#define LPSYS_PINMUX_PAD_SC01_PS        LPSYS_PINMUX_PAD_SC01_PS_Msk
#define LPSYS_PINMUX_PAD_SC01_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC01_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC01_IE_Pos)
#define LPSYS_PINMUX_PAD_SC01_IE        LPSYS_PINMUX_PAD_SC01_IE_Msk
#define LPSYS_PINMUX_PAD_SC01_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC01_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC01_IS_Pos)
#define LPSYS_PINMUX_PAD_SC01_IS        LPSYS_PINMUX_PAD_SC01_IS_Msk
#define LPSYS_PINMUX_PAD_SC01_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC01_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC01_SR_Pos)
#define LPSYS_PINMUX_PAD_SC01_SR        LPSYS_PINMUX_PAD_SC01_SR_Msk
#define LPSYS_PINMUX_PAD_SC01_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC01_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC01_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC01_DS0       LPSYS_PINMUX_PAD_SC01_DS0_Msk
#define LPSYS_PINMUX_PAD_SC01_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC01_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC01_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC01_DS1       LPSYS_PINMUX_PAD_SC01_DS1_Msk
#define LPSYS_PINMUX_PAD_SC01_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC01_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC01_POE_Pos)
#define LPSYS_PINMUX_PAD_SC01_POE       LPSYS_PINMUX_PAD_SC01_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_SC02 register **************/
#define LPSYS_PINMUX_PAD_SC02_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC02_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC02_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC02_FSEL      LPSYS_PINMUX_PAD_SC02_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC02_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC02_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC02_PE_Pos)
#define LPSYS_PINMUX_PAD_SC02_PE        LPSYS_PINMUX_PAD_SC02_PE_Msk
#define LPSYS_PINMUX_PAD_SC02_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC02_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC02_PS_Pos)
#define LPSYS_PINMUX_PAD_SC02_PS        LPSYS_PINMUX_PAD_SC02_PS_Msk
#define LPSYS_PINMUX_PAD_SC02_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC02_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC02_IE_Pos)
#define LPSYS_PINMUX_PAD_SC02_IE        LPSYS_PINMUX_PAD_SC02_IE_Msk
#define LPSYS_PINMUX_PAD_SC02_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC02_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC02_IS_Pos)
#define LPSYS_PINMUX_PAD_SC02_IS        LPSYS_PINMUX_PAD_SC02_IS_Msk
#define LPSYS_PINMUX_PAD_SC02_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC02_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC02_SR_Pos)
#define LPSYS_PINMUX_PAD_SC02_SR        LPSYS_PINMUX_PAD_SC02_SR_Msk
#define LPSYS_PINMUX_PAD_SC02_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC02_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC02_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC02_DS0       LPSYS_PINMUX_PAD_SC02_DS0_Msk
#define LPSYS_PINMUX_PAD_SC02_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC02_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC02_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC02_DS1       LPSYS_PINMUX_PAD_SC02_DS1_Msk
#define LPSYS_PINMUX_PAD_SC02_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC02_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC02_POE_Pos)
#define LPSYS_PINMUX_PAD_SC02_POE       LPSYS_PINMUX_PAD_SC02_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_SC03 register **************/
#define LPSYS_PINMUX_PAD_SC03_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC03_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC03_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC03_FSEL      LPSYS_PINMUX_PAD_SC03_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC03_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC03_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC03_PE_Pos)
#define LPSYS_PINMUX_PAD_SC03_PE        LPSYS_PINMUX_PAD_SC03_PE_Msk
#define LPSYS_PINMUX_PAD_SC03_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC03_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC03_PS_Pos)
#define LPSYS_PINMUX_PAD_SC03_PS        LPSYS_PINMUX_PAD_SC03_PS_Msk
#define LPSYS_PINMUX_PAD_SC03_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC03_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC03_IE_Pos)
#define LPSYS_PINMUX_PAD_SC03_IE        LPSYS_PINMUX_PAD_SC03_IE_Msk
#define LPSYS_PINMUX_PAD_SC03_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC03_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC03_IS_Pos)
#define LPSYS_PINMUX_PAD_SC03_IS        LPSYS_PINMUX_PAD_SC03_IS_Msk
#define LPSYS_PINMUX_PAD_SC03_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC03_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC03_SR_Pos)
#define LPSYS_PINMUX_PAD_SC03_SR        LPSYS_PINMUX_PAD_SC03_SR_Msk
#define LPSYS_PINMUX_PAD_SC03_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC03_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC03_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC03_DS0       LPSYS_PINMUX_PAD_SC03_DS0_Msk
#define LPSYS_PINMUX_PAD_SC03_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC03_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC03_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC03_DS1       LPSYS_PINMUX_PAD_SC03_DS1_Msk
#define LPSYS_PINMUX_PAD_SC03_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC03_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC03_POE_Pos)
#define LPSYS_PINMUX_PAD_SC03_POE       LPSYS_PINMUX_PAD_SC03_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_SC04 register **************/
#define LPSYS_PINMUX_PAD_SC04_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC04_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC04_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC04_FSEL      LPSYS_PINMUX_PAD_SC04_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC04_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC04_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC04_PE_Pos)
#define LPSYS_PINMUX_PAD_SC04_PE        LPSYS_PINMUX_PAD_SC04_PE_Msk
#define LPSYS_PINMUX_PAD_SC04_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC04_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC04_PS_Pos)
#define LPSYS_PINMUX_PAD_SC04_PS        LPSYS_PINMUX_PAD_SC04_PS_Msk
#define LPSYS_PINMUX_PAD_SC04_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC04_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC04_IE_Pos)
#define LPSYS_PINMUX_PAD_SC04_IE        LPSYS_PINMUX_PAD_SC04_IE_Msk
#define LPSYS_PINMUX_PAD_SC04_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC04_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC04_IS_Pos)
#define LPSYS_PINMUX_PAD_SC04_IS        LPSYS_PINMUX_PAD_SC04_IS_Msk
#define LPSYS_PINMUX_PAD_SC04_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC04_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC04_SR_Pos)
#define LPSYS_PINMUX_PAD_SC04_SR        LPSYS_PINMUX_PAD_SC04_SR_Msk
#define LPSYS_PINMUX_PAD_SC04_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC04_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC04_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC04_DS0       LPSYS_PINMUX_PAD_SC04_DS0_Msk
#define LPSYS_PINMUX_PAD_SC04_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC04_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC04_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC04_DS1       LPSYS_PINMUX_PAD_SC04_DS1_Msk
#define LPSYS_PINMUX_PAD_SC04_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC04_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC04_POE_Pos)
#define LPSYS_PINMUX_PAD_SC04_POE       LPSYS_PINMUX_PAD_SC04_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_SC05 register **************/
#define LPSYS_PINMUX_PAD_SC05_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_SC05_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_SC05_FSEL_Pos)
#define LPSYS_PINMUX_PAD_SC05_FSEL      LPSYS_PINMUX_PAD_SC05_FSEL_Msk
#define LPSYS_PINMUX_PAD_SC05_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_SC05_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC05_PE_Pos)
#define LPSYS_PINMUX_PAD_SC05_PE        LPSYS_PINMUX_PAD_SC05_PE_Msk
#define LPSYS_PINMUX_PAD_SC05_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_SC05_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC05_PS_Pos)
#define LPSYS_PINMUX_PAD_SC05_PS        LPSYS_PINMUX_PAD_SC05_PS_Msk
#define LPSYS_PINMUX_PAD_SC05_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_SC05_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC05_IE_Pos)
#define LPSYS_PINMUX_PAD_SC05_IE        LPSYS_PINMUX_PAD_SC05_IE_Msk
#define LPSYS_PINMUX_PAD_SC05_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_SC05_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC05_IS_Pos)
#define LPSYS_PINMUX_PAD_SC05_IS        LPSYS_PINMUX_PAD_SC05_IS_Msk
#define LPSYS_PINMUX_PAD_SC05_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_SC05_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_SC05_SR_Pos)
#define LPSYS_PINMUX_PAD_SC05_SR        LPSYS_PINMUX_PAD_SC05_SR_Msk
#define LPSYS_PINMUX_PAD_SC05_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_SC05_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC05_DS0_Pos)
#define LPSYS_PINMUX_PAD_SC05_DS0       LPSYS_PINMUX_PAD_SC05_DS0_Msk
#define LPSYS_PINMUX_PAD_SC05_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_SC05_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC05_DS1_Pos)
#define LPSYS_PINMUX_PAD_SC05_DS1       LPSYS_PINMUX_PAD_SC05_DS1_Msk
#define LPSYS_PINMUX_PAD_SC05_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_SC05_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_SC05_POE_Pos)
#define LPSYS_PINMUX_PAD_SC05_POE       LPSYS_PINMUX_PAD_SC05_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB00 register **************/
#define LPSYS_PINMUX_PAD_PB00_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB00_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB00_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB00_FSEL      LPSYS_PINMUX_PAD_PB00_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB00_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB00_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB00_PE_Pos)
#define LPSYS_PINMUX_PAD_PB00_PE        LPSYS_PINMUX_PAD_PB00_PE_Msk
#define LPSYS_PINMUX_PAD_PB00_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB00_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB00_PS_Pos)
#define LPSYS_PINMUX_PAD_PB00_PS        LPSYS_PINMUX_PAD_PB00_PS_Msk
#define LPSYS_PINMUX_PAD_PB00_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB00_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB00_IE_Pos)
#define LPSYS_PINMUX_PAD_PB00_IE        LPSYS_PINMUX_PAD_PB00_IE_Msk
#define LPSYS_PINMUX_PAD_PB00_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB00_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB00_IS_Pos)
#define LPSYS_PINMUX_PAD_PB00_IS        LPSYS_PINMUX_PAD_PB00_IS_Msk
#define LPSYS_PINMUX_PAD_PB00_MODE_Pos  (8U)
#define LPSYS_PINMUX_PAD_PB00_MODE_Msk  (0x1UL << LPSYS_PINMUX_PAD_PB00_MODE_Pos)
#define LPSYS_PINMUX_PAD_PB00_MODE      LPSYS_PINMUX_PAD_PB00_MODE_Msk
#define LPSYS_PINMUX_PAD_PB00_DS_Pos    (10U)
#define LPSYS_PINMUX_PAD_PB00_DS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB00_DS_Pos)
#define LPSYS_PINMUX_PAD_PB00_DS        LPSYS_PINMUX_PAD_PB00_DS_Msk
#define LPSYS_PINMUX_PAD_PB00_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB00_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB00_POE_Pos)
#define LPSYS_PINMUX_PAD_PB00_POE       LPSYS_PINMUX_PAD_PB00_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB01 register **************/
#define LPSYS_PINMUX_PAD_PB01_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB01_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB01_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB01_FSEL      LPSYS_PINMUX_PAD_PB01_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB01_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB01_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB01_PE_Pos)
#define LPSYS_PINMUX_PAD_PB01_PE        LPSYS_PINMUX_PAD_PB01_PE_Msk
#define LPSYS_PINMUX_PAD_PB01_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB01_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB01_PS_Pos)
#define LPSYS_PINMUX_PAD_PB01_PS        LPSYS_PINMUX_PAD_PB01_PS_Msk
#define LPSYS_PINMUX_PAD_PB01_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB01_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB01_IE_Pos)
#define LPSYS_PINMUX_PAD_PB01_IE        LPSYS_PINMUX_PAD_PB01_IE_Msk
#define LPSYS_PINMUX_PAD_PB01_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB01_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB01_IS_Pos)
#define LPSYS_PINMUX_PAD_PB01_IS        LPSYS_PINMUX_PAD_PB01_IS_Msk
#define LPSYS_PINMUX_PAD_PB01_MODE_Pos  (8U)
#define LPSYS_PINMUX_PAD_PB01_MODE_Msk  (0x1UL << LPSYS_PINMUX_PAD_PB01_MODE_Pos)
#define LPSYS_PINMUX_PAD_PB01_MODE      LPSYS_PINMUX_PAD_PB01_MODE_Msk
#define LPSYS_PINMUX_PAD_PB01_DS_Pos    (10U)
#define LPSYS_PINMUX_PAD_PB01_DS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB01_DS_Pos)
#define LPSYS_PINMUX_PAD_PB01_DS        LPSYS_PINMUX_PAD_PB01_DS_Msk
#define LPSYS_PINMUX_PAD_PB01_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB01_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB01_POE_Pos)
#define LPSYS_PINMUX_PAD_PB01_POE       LPSYS_PINMUX_PAD_PB01_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB02 register **************/
#define LPSYS_PINMUX_PAD_PB02_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB02_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB02_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB02_FSEL      LPSYS_PINMUX_PAD_PB02_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB02_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB02_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB02_PE_Pos)
#define LPSYS_PINMUX_PAD_PB02_PE        LPSYS_PINMUX_PAD_PB02_PE_Msk
#define LPSYS_PINMUX_PAD_PB02_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB02_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB02_PS_Pos)
#define LPSYS_PINMUX_PAD_PB02_PS        LPSYS_PINMUX_PAD_PB02_PS_Msk
#define LPSYS_PINMUX_PAD_PB02_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB02_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB02_IE_Pos)
#define LPSYS_PINMUX_PAD_PB02_IE        LPSYS_PINMUX_PAD_PB02_IE_Msk
#define LPSYS_PINMUX_PAD_PB02_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB02_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB02_IS_Pos)
#define LPSYS_PINMUX_PAD_PB02_IS        LPSYS_PINMUX_PAD_PB02_IS_Msk
#define LPSYS_PINMUX_PAD_PB02_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB02_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB02_SR_Pos)
#define LPSYS_PINMUX_PAD_PB02_SR        LPSYS_PINMUX_PAD_PB02_SR_Msk
#define LPSYS_PINMUX_PAD_PB02_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB02_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB02_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB02_DS0       LPSYS_PINMUX_PAD_PB02_DS0_Msk
#define LPSYS_PINMUX_PAD_PB02_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB02_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB02_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB02_DS1       LPSYS_PINMUX_PAD_PB02_DS1_Msk
#define LPSYS_PINMUX_PAD_PB02_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB02_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB02_POE_Pos)
#define LPSYS_PINMUX_PAD_PB02_POE       LPSYS_PINMUX_PAD_PB02_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB03 register **************/
#define LPSYS_PINMUX_PAD_PB03_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB03_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB03_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB03_FSEL      LPSYS_PINMUX_PAD_PB03_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB03_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB03_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB03_PE_Pos)
#define LPSYS_PINMUX_PAD_PB03_PE        LPSYS_PINMUX_PAD_PB03_PE_Msk
#define LPSYS_PINMUX_PAD_PB03_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB03_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB03_PS_Pos)
#define LPSYS_PINMUX_PAD_PB03_PS        LPSYS_PINMUX_PAD_PB03_PS_Msk
#define LPSYS_PINMUX_PAD_PB03_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB03_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB03_IE_Pos)
#define LPSYS_PINMUX_PAD_PB03_IE        LPSYS_PINMUX_PAD_PB03_IE_Msk
#define LPSYS_PINMUX_PAD_PB03_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB03_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB03_IS_Pos)
#define LPSYS_PINMUX_PAD_PB03_IS        LPSYS_PINMUX_PAD_PB03_IS_Msk
#define LPSYS_PINMUX_PAD_PB03_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB03_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB03_SR_Pos)
#define LPSYS_PINMUX_PAD_PB03_SR        LPSYS_PINMUX_PAD_PB03_SR_Msk
#define LPSYS_PINMUX_PAD_PB03_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB03_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB03_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB03_DS0       LPSYS_PINMUX_PAD_PB03_DS0_Msk
#define LPSYS_PINMUX_PAD_PB03_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB03_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB03_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB03_DS1       LPSYS_PINMUX_PAD_PB03_DS1_Msk
#define LPSYS_PINMUX_PAD_PB03_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB03_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB03_POE_Pos)
#define LPSYS_PINMUX_PAD_PB03_POE       LPSYS_PINMUX_PAD_PB03_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB04 register **************/
#define LPSYS_PINMUX_PAD_PB04_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB04_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB04_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB04_FSEL      LPSYS_PINMUX_PAD_PB04_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB04_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB04_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB04_PE_Pos)
#define LPSYS_PINMUX_PAD_PB04_PE        LPSYS_PINMUX_PAD_PB04_PE_Msk
#define LPSYS_PINMUX_PAD_PB04_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB04_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB04_PS_Pos)
#define LPSYS_PINMUX_PAD_PB04_PS        LPSYS_PINMUX_PAD_PB04_PS_Msk
#define LPSYS_PINMUX_PAD_PB04_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB04_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB04_IE_Pos)
#define LPSYS_PINMUX_PAD_PB04_IE        LPSYS_PINMUX_PAD_PB04_IE_Msk
#define LPSYS_PINMUX_PAD_PB04_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB04_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB04_IS_Pos)
#define LPSYS_PINMUX_PAD_PB04_IS        LPSYS_PINMUX_PAD_PB04_IS_Msk
#define LPSYS_PINMUX_PAD_PB04_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB04_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB04_SR_Pos)
#define LPSYS_PINMUX_PAD_PB04_SR        LPSYS_PINMUX_PAD_PB04_SR_Msk
#define LPSYS_PINMUX_PAD_PB04_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB04_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB04_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB04_DS0       LPSYS_PINMUX_PAD_PB04_DS0_Msk
#define LPSYS_PINMUX_PAD_PB04_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB04_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB04_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB04_DS1       LPSYS_PINMUX_PAD_PB04_DS1_Msk
#define LPSYS_PINMUX_PAD_PB04_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB04_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB04_POE_Pos)
#define LPSYS_PINMUX_PAD_PB04_POE       LPSYS_PINMUX_PAD_PB04_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB05 register **************/
#define LPSYS_PINMUX_PAD_PB05_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB05_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB05_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB05_FSEL      LPSYS_PINMUX_PAD_PB05_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB05_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB05_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB05_PE_Pos)
#define LPSYS_PINMUX_PAD_PB05_PE        LPSYS_PINMUX_PAD_PB05_PE_Msk
#define LPSYS_PINMUX_PAD_PB05_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB05_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB05_PS_Pos)
#define LPSYS_PINMUX_PAD_PB05_PS        LPSYS_PINMUX_PAD_PB05_PS_Msk
#define LPSYS_PINMUX_PAD_PB05_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB05_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB05_IE_Pos)
#define LPSYS_PINMUX_PAD_PB05_IE        LPSYS_PINMUX_PAD_PB05_IE_Msk
#define LPSYS_PINMUX_PAD_PB05_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB05_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB05_IS_Pos)
#define LPSYS_PINMUX_PAD_PB05_IS        LPSYS_PINMUX_PAD_PB05_IS_Msk
#define LPSYS_PINMUX_PAD_PB05_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB05_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB05_SR_Pos)
#define LPSYS_PINMUX_PAD_PB05_SR        LPSYS_PINMUX_PAD_PB05_SR_Msk
#define LPSYS_PINMUX_PAD_PB05_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB05_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB05_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB05_DS0       LPSYS_PINMUX_PAD_PB05_DS0_Msk
#define LPSYS_PINMUX_PAD_PB05_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB05_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB05_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB05_DS1       LPSYS_PINMUX_PAD_PB05_DS1_Msk
#define LPSYS_PINMUX_PAD_PB05_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB05_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB05_POE_Pos)
#define LPSYS_PINMUX_PAD_PB05_POE       LPSYS_PINMUX_PAD_PB05_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB06 register **************/
#define LPSYS_PINMUX_PAD_PB06_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB06_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB06_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB06_FSEL      LPSYS_PINMUX_PAD_PB06_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB06_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB06_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB06_PE_Pos)
#define LPSYS_PINMUX_PAD_PB06_PE        LPSYS_PINMUX_PAD_PB06_PE_Msk
#define LPSYS_PINMUX_PAD_PB06_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB06_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB06_PS_Pos)
#define LPSYS_PINMUX_PAD_PB06_PS        LPSYS_PINMUX_PAD_PB06_PS_Msk
#define LPSYS_PINMUX_PAD_PB06_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB06_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB06_IE_Pos)
#define LPSYS_PINMUX_PAD_PB06_IE        LPSYS_PINMUX_PAD_PB06_IE_Msk
#define LPSYS_PINMUX_PAD_PB06_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB06_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB06_IS_Pos)
#define LPSYS_PINMUX_PAD_PB06_IS        LPSYS_PINMUX_PAD_PB06_IS_Msk
#define LPSYS_PINMUX_PAD_PB06_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB06_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB06_SR_Pos)
#define LPSYS_PINMUX_PAD_PB06_SR        LPSYS_PINMUX_PAD_PB06_SR_Msk
#define LPSYS_PINMUX_PAD_PB06_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB06_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB06_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB06_DS0       LPSYS_PINMUX_PAD_PB06_DS0_Msk
#define LPSYS_PINMUX_PAD_PB06_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB06_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB06_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB06_DS1       LPSYS_PINMUX_PAD_PB06_DS1_Msk
#define LPSYS_PINMUX_PAD_PB06_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB06_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB06_POE_Pos)
#define LPSYS_PINMUX_PAD_PB06_POE       LPSYS_PINMUX_PAD_PB06_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB07 register **************/
#define LPSYS_PINMUX_PAD_PB07_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB07_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB07_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB07_FSEL      LPSYS_PINMUX_PAD_PB07_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB07_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB07_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB07_PE_Pos)
#define LPSYS_PINMUX_PAD_PB07_PE        LPSYS_PINMUX_PAD_PB07_PE_Msk
#define LPSYS_PINMUX_PAD_PB07_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB07_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB07_PS_Pos)
#define LPSYS_PINMUX_PAD_PB07_PS        LPSYS_PINMUX_PAD_PB07_PS_Msk
#define LPSYS_PINMUX_PAD_PB07_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB07_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB07_IE_Pos)
#define LPSYS_PINMUX_PAD_PB07_IE        LPSYS_PINMUX_PAD_PB07_IE_Msk
#define LPSYS_PINMUX_PAD_PB07_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB07_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB07_IS_Pos)
#define LPSYS_PINMUX_PAD_PB07_IS        LPSYS_PINMUX_PAD_PB07_IS_Msk
#define LPSYS_PINMUX_PAD_PB07_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB07_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB07_SR_Pos)
#define LPSYS_PINMUX_PAD_PB07_SR        LPSYS_PINMUX_PAD_PB07_SR_Msk
#define LPSYS_PINMUX_PAD_PB07_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB07_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB07_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB07_DS0       LPSYS_PINMUX_PAD_PB07_DS0_Msk
#define LPSYS_PINMUX_PAD_PB07_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB07_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB07_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB07_DS1       LPSYS_PINMUX_PAD_PB07_DS1_Msk
#define LPSYS_PINMUX_PAD_PB07_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB07_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB07_POE_Pos)
#define LPSYS_PINMUX_PAD_PB07_POE       LPSYS_PINMUX_PAD_PB07_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB08 register **************/
#define LPSYS_PINMUX_PAD_PB08_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB08_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB08_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB08_FSEL      LPSYS_PINMUX_PAD_PB08_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB08_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB08_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB08_PE_Pos)
#define LPSYS_PINMUX_PAD_PB08_PE        LPSYS_PINMUX_PAD_PB08_PE_Msk
#define LPSYS_PINMUX_PAD_PB08_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB08_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB08_PS_Pos)
#define LPSYS_PINMUX_PAD_PB08_PS        LPSYS_PINMUX_PAD_PB08_PS_Msk
#define LPSYS_PINMUX_PAD_PB08_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB08_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB08_IE_Pos)
#define LPSYS_PINMUX_PAD_PB08_IE        LPSYS_PINMUX_PAD_PB08_IE_Msk
#define LPSYS_PINMUX_PAD_PB08_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB08_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB08_IS_Pos)
#define LPSYS_PINMUX_PAD_PB08_IS        LPSYS_PINMUX_PAD_PB08_IS_Msk
#define LPSYS_PINMUX_PAD_PB08_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB08_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB08_SR_Pos)
#define LPSYS_PINMUX_PAD_PB08_SR        LPSYS_PINMUX_PAD_PB08_SR_Msk
#define LPSYS_PINMUX_PAD_PB08_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB08_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB08_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB08_DS0       LPSYS_PINMUX_PAD_PB08_DS0_Msk
#define LPSYS_PINMUX_PAD_PB08_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB08_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB08_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB08_DS1       LPSYS_PINMUX_PAD_PB08_DS1_Msk
#define LPSYS_PINMUX_PAD_PB08_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB08_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB08_POE_Pos)
#define LPSYS_PINMUX_PAD_PB08_POE       LPSYS_PINMUX_PAD_PB08_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB09 register **************/
#define LPSYS_PINMUX_PAD_PB09_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB09_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB09_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB09_FSEL      LPSYS_PINMUX_PAD_PB09_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB09_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB09_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB09_PE_Pos)
#define LPSYS_PINMUX_PAD_PB09_PE        LPSYS_PINMUX_PAD_PB09_PE_Msk
#define LPSYS_PINMUX_PAD_PB09_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB09_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB09_PS_Pos)
#define LPSYS_PINMUX_PAD_PB09_PS        LPSYS_PINMUX_PAD_PB09_PS_Msk
#define LPSYS_PINMUX_PAD_PB09_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB09_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB09_IE_Pos)
#define LPSYS_PINMUX_PAD_PB09_IE        LPSYS_PINMUX_PAD_PB09_IE_Msk
#define LPSYS_PINMUX_PAD_PB09_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB09_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB09_IS_Pos)
#define LPSYS_PINMUX_PAD_PB09_IS        LPSYS_PINMUX_PAD_PB09_IS_Msk
#define LPSYS_PINMUX_PAD_PB09_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB09_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB09_SR_Pos)
#define LPSYS_PINMUX_PAD_PB09_SR        LPSYS_PINMUX_PAD_PB09_SR_Msk
#define LPSYS_PINMUX_PAD_PB09_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB09_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB09_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB09_DS0       LPSYS_PINMUX_PAD_PB09_DS0_Msk
#define LPSYS_PINMUX_PAD_PB09_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB09_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB09_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB09_DS1       LPSYS_PINMUX_PAD_PB09_DS1_Msk
#define LPSYS_PINMUX_PAD_PB09_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB09_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB09_POE_Pos)
#define LPSYS_PINMUX_PAD_PB09_POE       LPSYS_PINMUX_PAD_PB09_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB10 register **************/
#define LPSYS_PINMUX_PAD_PB10_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB10_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB10_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB10_FSEL      LPSYS_PINMUX_PAD_PB10_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB10_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB10_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB10_PE_Pos)
#define LPSYS_PINMUX_PAD_PB10_PE        LPSYS_PINMUX_PAD_PB10_PE_Msk
#define LPSYS_PINMUX_PAD_PB10_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB10_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB10_PS_Pos)
#define LPSYS_PINMUX_PAD_PB10_PS        LPSYS_PINMUX_PAD_PB10_PS_Msk
#define LPSYS_PINMUX_PAD_PB10_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB10_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB10_IE_Pos)
#define LPSYS_PINMUX_PAD_PB10_IE        LPSYS_PINMUX_PAD_PB10_IE_Msk
#define LPSYS_PINMUX_PAD_PB10_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB10_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB10_IS_Pos)
#define LPSYS_PINMUX_PAD_PB10_IS        LPSYS_PINMUX_PAD_PB10_IS_Msk
#define LPSYS_PINMUX_PAD_PB10_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB10_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB10_SR_Pos)
#define LPSYS_PINMUX_PAD_PB10_SR        LPSYS_PINMUX_PAD_PB10_SR_Msk
#define LPSYS_PINMUX_PAD_PB10_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB10_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB10_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB10_DS0       LPSYS_PINMUX_PAD_PB10_DS0_Msk
#define LPSYS_PINMUX_PAD_PB10_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB10_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB10_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB10_DS1       LPSYS_PINMUX_PAD_PB10_DS1_Msk
#define LPSYS_PINMUX_PAD_PB10_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB10_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB10_POE_Pos)
#define LPSYS_PINMUX_PAD_PB10_POE       LPSYS_PINMUX_PAD_PB10_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB11 register **************/
#define LPSYS_PINMUX_PAD_PB11_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB11_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB11_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB11_FSEL      LPSYS_PINMUX_PAD_PB11_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB11_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB11_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB11_PE_Pos)
#define LPSYS_PINMUX_PAD_PB11_PE        LPSYS_PINMUX_PAD_PB11_PE_Msk
#define LPSYS_PINMUX_PAD_PB11_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB11_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB11_PS_Pos)
#define LPSYS_PINMUX_PAD_PB11_PS        LPSYS_PINMUX_PAD_PB11_PS_Msk
#define LPSYS_PINMUX_PAD_PB11_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB11_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB11_IE_Pos)
#define LPSYS_PINMUX_PAD_PB11_IE        LPSYS_PINMUX_PAD_PB11_IE_Msk
#define LPSYS_PINMUX_PAD_PB11_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB11_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB11_IS_Pos)
#define LPSYS_PINMUX_PAD_PB11_IS        LPSYS_PINMUX_PAD_PB11_IS_Msk
#define LPSYS_PINMUX_PAD_PB11_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB11_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB11_SR_Pos)
#define LPSYS_PINMUX_PAD_PB11_SR        LPSYS_PINMUX_PAD_PB11_SR_Msk
#define LPSYS_PINMUX_PAD_PB11_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB11_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB11_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB11_DS0       LPSYS_PINMUX_PAD_PB11_DS0_Msk
#define LPSYS_PINMUX_PAD_PB11_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB11_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB11_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB11_DS1       LPSYS_PINMUX_PAD_PB11_DS1_Msk
#define LPSYS_PINMUX_PAD_PB11_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB11_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB11_POE_Pos)
#define LPSYS_PINMUX_PAD_PB11_POE       LPSYS_PINMUX_PAD_PB11_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB12 register **************/
#define LPSYS_PINMUX_PAD_PB12_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB12_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB12_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB12_FSEL      LPSYS_PINMUX_PAD_PB12_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB12_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB12_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB12_PE_Pos)
#define LPSYS_PINMUX_PAD_PB12_PE        LPSYS_PINMUX_PAD_PB12_PE_Msk
#define LPSYS_PINMUX_PAD_PB12_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB12_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB12_PS_Pos)
#define LPSYS_PINMUX_PAD_PB12_PS        LPSYS_PINMUX_PAD_PB12_PS_Msk
#define LPSYS_PINMUX_PAD_PB12_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB12_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB12_IE_Pos)
#define LPSYS_PINMUX_PAD_PB12_IE        LPSYS_PINMUX_PAD_PB12_IE_Msk
#define LPSYS_PINMUX_PAD_PB12_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB12_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB12_IS_Pos)
#define LPSYS_PINMUX_PAD_PB12_IS        LPSYS_PINMUX_PAD_PB12_IS_Msk
#define LPSYS_PINMUX_PAD_PB12_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB12_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB12_SR_Pos)
#define LPSYS_PINMUX_PAD_PB12_SR        LPSYS_PINMUX_PAD_PB12_SR_Msk
#define LPSYS_PINMUX_PAD_PB12_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB12_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB12_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB12_DS0       LPSYS_PINMUX_PAD_PB12_DS0_Msk
#define LPSYS_PINMUX_PAD_PB12_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB12_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB12_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB12_DS1       LPSYS_PINMUX_PAD_PB12_DS1_Msk
#define LPSYS_PINMUX_PAD_PB12_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB12_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB12_POE_Pos)
#define LPSYS_PINMUX_PAD_PB12_POE       LPSYS_PINMUX_PAD_PB12_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB13 register **************/
#define LPSYS_PINMUX_PAD_PB13_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB13_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB13_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB13_FSEL      LPSYS_PINMUX_PAD_PB13_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB13_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB13_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB13_PE_Pos)
#define LPSYS_PINMUX_PAD_PB13_PE        LPSYS_PINMUX_PAD_PB13_PE_Msk
#define LPSYS_PINMUX_PAD_PB13_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB13_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB13_PS_Pos)
#define LPSYS_PINMUX_PAD_PB13_PS        LPSYS_PINMUX_PAD_PB13_PS_Msk
#define LPSYS_PINMUX_PAD_PB13_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB13_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB13_IE_Pos)
#define LPSYS_PINMUX_PAD_PB13_IE        LPSYS_PINMUX_PAD_PB13_IE_Msk
#define LPSYS_PINMUX_PAD_PB13_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB13_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB13_IS_Pos)
#define LPSYS_PINMUX_PAD_PB13_IS        LPSYS_PINMUX_PAD_PB13_IS_Msk
#define LPSYS_PINMUX_PAD_PB13_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB13_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB13_SR_Pos)
#define LPSYS_PINMUX_PAD_PB13_SR        LPSYS_PINMUX_PAD_PB13_SR_Msk
#define LPSYS_PINMUX_PAD_PB13_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB13_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB13_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB13_DS0       LPSYS_PINMUX_PAD_PB13_DS0_Msk
#define LPSYS_PINMUX_PAD_PB13_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB13_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB13_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB13_DS1       LPSYS_PINMUX_PAD_PB13_DS1_Msk
#define LPSYS_PINMUX_PAD_PB13_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB13_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB13_POE_Pos)
#define LPSYS_PINMUX_PAD_PB13_POE       LPSYS_PINMUX_PAD_PB13_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB14 register **************/
#define LPSYS_PINMUX_PAD_PB14_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB14_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB14_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB14_FSEL      LPSYS_PINMUX_PAD_PB14_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB14_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB14_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB14_PE_Pos)
#define LPSYS_PINMUX_PAD_PB14_PE        LPSYS_PINMUX_PAD_PB14_PE_Msk
#define LPSYS_PINMUX_PAD_PB14_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB14_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB14_PS_Pos)
#define LPSYS_PINMUX_PAD_PB14_PS        LPSYS_PINMUX_PAD_PB14_PS_Msk
#define LPSYS_PINMUX_PAD_PB14_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB14_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB14_IE_Pos)
#define LPSYS_PINMUX_PAD_PB14_IE        LPSYS_PINMUX_PAD_PB14_IE_Msk
#define LPSYS_PINMUX_PAD_PB14_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB14_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB14_IS_Pos)
#define LPSYS_PINMUX_PAD_PB14_IS        LPSYS_PINMUX_PAD_PB14_IS_Msk
#define LPSYS_PINMUX_PAD_PB14_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB14_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB14_SR_Pos)
#define LPSYS_PINMUX_PAD_PB14_SR        LPSYS_PINMUX_PAD_PB14_SR_Msk
#define LPSYS_PINMUX_PAD_PB14_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB14_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB14_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB14_DS0       LPSYS_PINMUX_PAD_PB14_DS0_Msk
#define LPSYS_PINMUX_PAD_PB14_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB14_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB14_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB14_DS1       LPSYS_PINMUX_PAD_PB14_DS1_Msk
#define LPSYS_PINMUX_PAD_PB14_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB14_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB14_POE_Pos)
#define LPSYS_PINMUX_PAD_PB14_POE       LPSYS_PINMUX_PAD_PB14_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB15 register **************/
#define LPSYS_PINMUX_PAD_PB15_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB15_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB15_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB15_FSEL      LPSYS_PINMUX_PAD_PB15_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB15_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB15_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB15_PE_Pos)
#define LPSYS_PINMUX_PAD_PB15_PE        LPSYS_PINMUX_PAD_PB15_PE_Msk
#define LPSYS_PINMUX_PAD_PB15_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB15_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB15_PS_Pos)
#define LPSYS_PINMUX_PAD_PB15_PS        LPSYS_PINMUX_PAD_PB15_PS_Msk
#define LPSYS_PINMUX_PAD_PB15_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB15_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB15_IE_Pos)
#define LPSYS_PINMUX_PAD_PB15_IE        LPSYS_PINMUX_PAD_PB15_IE_Msk
#define LPSYS_PINMUX_PAD_PB15_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB15_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB15_IS_Pos)
#define LPSYS_PINMUX_PAD_PB15_IS        LPSYS_PINMUX_PAD_PB15_IS_Msk
#define LPSYS_PINMUX_PAD_PB15_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB15_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB15_SR_Pos)
#define LPSYS_PINMUX_PAD_PB15_SR        LPSYS_PINMUX_PAD_PB15_SR_Msk
#define LPSYS_PINMUX_PAD_PB15_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB15_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB15_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB15_DS0       LPSYS_PINMUX_PAD_PB15_DS0_Msk
#define LPSYS_PINMUX_PAD_PB15_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB15_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB15_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB15_DS1       LPSYS_PINMUX_PAD_PB15_DS1_Msk
#define LPSYS_PINMUX_PAD_PB15_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB15_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB15_POE_Pos)
#define LPSYS_PINMUX_PAD_PB15_POE       LPSYS_PINMUX_PAD_PB15_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB16 register **************/
#define LPSYS_PINMUX_PAD_PB16_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB16_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB16_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB16_FSEL      LPSYS_PINMUX_PAD_PB16_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB16_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB16_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB16_PE_Pos)
#define LPSYS_PINMUX_PAD_PB16_PE        LPSYS_PINMUX_PAD_PB16_PE_Msk
#define LPSYS_PINMUX_PAD_PB16_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB16_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB16_PS_Pos)
#define LPSYS_PINMUX_PAD_PB16_PS        LPSYS_PINMUX_PAD_PB16_PS_Msk
#define LPSYS_PINMUX_PAD_PB16_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB16_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB16_IE_Pos)
#define LPSYS_PINMUX_PAD_PB16_IE        LPSYS_PINMUX_PAD_PB16_IE_Msk
#define LPSYS_PINMUX_PAD_PB16_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB16_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB16_IS_Pos)
#define LPSYS_PINMUX_PAD_PB16_IS        LPSYS_PINMUX_PAD_PB16_IS_Msk
#define LPSYS_PINMUX_PAD_PB16_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB16_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB16_SR_Pos)
#define LPSYS_PINMUX_PAD_PB16_SR        LPSYS_PINMUX_PAD_PB16_SR_Msk
#define LPSYS_PINMUX_PAD_PB16_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB16_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB16_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB16_DS0       LPSYS_PINMUX_PAD_PB16_DS0_Msk
#define LPSYS_PINMUX_PAD_PB16_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB16_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB16_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB16_DS1       LPSYS_PINMUX_PAD_PB16_DS1_Msk
#define LPSYS_PINMUX_PAD_PB16_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB16_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB16_POE_Pos)
#define LPSYS_PINMUX_PAD_PB16_POE       LPSYS_PINMUX_PAD_PB16_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB17 register **************/
#define LPSYS_PINMUX_PAD_PB17_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB17_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB17_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB17_FSEL      LPSYS_PINMUX_PAD_PB17_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB17_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB17_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB17_PE_Pos)
#define LPSYS_PINMUX_PAD_PB17_PE        LPSYS_PINMUX_PAD_PB17_PE_Msk
#define LPSYS_PINMUX_PAD_PB17_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB17_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB17_PS_Pos)
#define LPSYS_PINMUX_PAD_PB17_PS        LPSYS_PINMUX_PAD_PB17_PS_Msk
#define LPSYS_PINMUX_PAD_PB17_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB17_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB17_IE_Pos)
#define LPSYS_PINMUX_PAD_PB17_IE        LPSYS_PINMUX_PAD_PB17_IE_Msk
#define LPSYS_PINMUX_PAD_PB17_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB17_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB17_IS_Pos)
#define LPSYS_PINMUX_PAD_PB17_IS        LPSYS_PINMUX_PAD_PB17_IS_Msk
#define LPSYS_PINMUX_PAD_PB17_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB17_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB17_SR_Pos)
#define LPSYS_PINMUX_PAD_PB17_SR        LPSYS_PINMUX_PAD_PB17_SR_Msk
#define LPSYS_PINMUX_PAD_PB17_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB17_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB17_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB17_DS0       LPSYS_PINMUX_PAD_PB17_DS0_Msk
#define LPSYS_PINMUX_PAD_PB17_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB17_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB17_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB17_DS1       LPSYS_PINMUX_PAD_PB17_DS1_Msk
#define LPSYS_PINMUX_PAD_PB17_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB17_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB17_POE_Pos)
#define LPSYS_PINMUX_PAD_PB17_POE       LPSYS_PINMUX_PAD_PB17_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB18 register **************/
#define LPSYS_PINMUX_PAD_PB18_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB18_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB18_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB18_FSEL      LPSYS_PINMUX_PAD_PB18_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB18_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB18_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB18_PE_Pos)
#define LPSYS_PINMUX_PAD_PB18_PE        LPSYS_PINMUX_PAD_PB18_PE_Msk
#define LPSYS_PINMUX_PAD_PB18_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB18_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB18_PS_Pos)
#define LPSYS_PINMUX_PAD_PB18_PS        LPSYS_PINMUX_PAD_PB18_PS_Msk
#define LPSYS_PINMUX_PAD_PB18_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB18_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB18_IE_Pos)
#define LPSYS_PINMUX_PAD_PB18_IE        LPSYS_PINMUX_PAD_PB18_IE_Msk
#define LPSYS_PINMUX_PAD_PB18_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB18_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB18_IS_Pos)
#define LPSYS_PINMUX_PAD_PB18_IS        LPSYS_PINMUX_PAD_PB18_IS_Msk
#define LPSYS_PINMUX_PAD_PB18_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB18_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB18_SR_Pos)
#define LPSYS_PINMUX_PAD_PB18_SR        LPSYS_PINMUX_PAD_PB18_SR_Msk
#define LPSYS_PINMUX_PAD_PB18_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB18_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB18_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB18_DS0       LPSYS_PINMUX_PAD_PB18_DS0_Msk
#define LPSYS_PINMUX_PAD_PB18_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB18_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB18_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB18_DS1       LPSYS_PINMUX_PAD_PB18_DS1_Msk
#define LPSYS_PINMUX_PAD_PB18_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB18_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB18_POE_Pos)
#define LPSYS_PINMUX_PAD_PB18_POE       LPSYS_PINMUX_PAD_PB18_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB19 register **************/
#define LPSYS_PINMUX_PAD_PB19_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB19_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB19_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB19_FSEL      LPSYS_PINMUX_PAD_PB19_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB19_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB19_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB19_PE_Pos)
#define LPSYS_PINMUX_PAD_PB19_PE        LPSYS_PINMUX_PAD_PB19_PE_Msk
#define LPSYS_PINMUX_PAD_PB19_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB19_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB19_PS_Pos)
#define LPSYS_PINMUX_PAD_PB19_PS        LPSYS_PINMUX_PAD_PB19_PS_Msk
#define LPSYS_PINMUX_PAD_PB19_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB19_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB19_IE_Pos)
#define LPSYS_PINMUX_PAD_PB19_IE        LPSYS_PINMUX_PAD_PB19_IE_Msk
#define LPSYS_PINMUX_PAD_PB19_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB19_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB19_IS_Pos)
#define LPSYS_PINMUX_PAD_PB19_IS        LPSYS_PINMUX_PAD_PB19_IS_Msk
#define LPSYS_PINMUX_PAD_PB19_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB19_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB19_SR_Pos)
#define LPSYS_PINMUX_PAD_PB19_SR        LPSYS_PINMUX_PAD_PB19_SR_Msk
#define LPSYS_PINMUX_PAD_PB19_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB19_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB19_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB19_DS0       LPSYS_PINMUX_PAD_PB19_DS0_Msk
#define LPSYS_PINMUX_PAD_PB19_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB19_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB19_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB19_DS1       LPSYS_PINMUX_PAD_PB19_DS1_Msk
#define LPSYS_PINMUX_PAD_PB19_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB19_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB19_POE_Pos)
#define LPSYS_PINMUX_PAD_PB19_POE       LPSYS_PINMUX_PAD_PB19_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB20 register **************/
#define LPSYS_PINMUX_PAD_PB20_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB20_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB20_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB20_FSEL      LPSYS_PINMUX_PAD_PB20_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB20_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB20_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB20_PE_Pos)
#define LPSYS_PINMUX_PAD_PB20_PE        LPSYS_PINMUX_PAD_PB20_PE_Msk
#define LPSYS_PINMUX_PAD_PB20_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB20_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB20_PS_Pos)
#define LPSYS_PINMUX_PAD_PB20_PS        LPSYS_PINMUX_PAD_PB20_PS_Msk
#define LPSYS_PINMUX_PAD_PB20_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB20_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB20_IE_Pos)
#define LPSYS_PINMUX_PAD_PB20_IE        LPSYS_PINMUX_PAD_PB20_IE_Msk
#define LPSYS_PINMUX_PAD_PB20_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB20_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB20_IS_Pos)
#define LPSYS_PINMUX_PAD_PB20_IS        LPSYS_PINMUX_PAD_PB20_IS_Msk
#define LPSYS_PINMUX_PAD_PB20_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB20_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB20_SR_Pos)
#define LPSYS_PINMUX_PAD_PB20_SR        LPSYS_PINMUX_PAD_PB20_SR_Msk
#define LPSYS_PINMUX_PAD_PB20_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB20_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB20_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB20_DS0       LPSYS_PINMUX_PAD_PB20_DS0_Msk
#define LPSYS_PINMUX_PAD_PB20_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB20_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB20_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB20_DS1       LPSYS_PINMUX_PAD_PB20_DS1_Msk
#define LPSYS_PINMUX_PAD_PB20_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB20_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB20_POE_Pos)
#define LPSYS_PINMUX_PAD_PB20_POE       LPSYS_PINMUX_PAD_PB20_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB21 register **************/
#define LPSYS_PINMUX_PAD_PB21_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB21_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB21_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB21_FSEL      LPSYS_PINMUX_PAD_PB21_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB21_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB21_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB21_PE_Pos)
#define LPSYS_PINMUX_PAD_PB21_PE        LPSYS_PINMUX_PAD_PB21_PE_Msk
#define LPSYS_PINMUX_PAD_PB21_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB21_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB21_PS_Pos)
#define LPSYS_PINMUX_PAD_PB21_PS        LPSYS_PINMUX_PAD_PB21_PS_Msk
#define LPSYS_PINMUX_PAD_PB21_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB21_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB21_IE_Pos)
#define LPSYS_PINMUX_PAD_PB21_IE        LPSYS_PINMUX_PAD_PB21_IE_Msk
#define LPSYS_PINMUX_PAD_PB21_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB21_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB21_IS_Pos)
#define LPSYS_PINMUX_PAD_PB21_IS        LPSYS_PINMUX_PAD_PB21_IS_Msk
#define LPSYS_PINMUX_PAD_PB21_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB21_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB21_SR_Pos)
#define LPSYS_PINMUX_PAD_PB21_SR        LPSYS_PINMUX_PAD_PB21_SR_Msk
#define LPSYS_PINMUX_PAD_PB21_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB21_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB21_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB21_DS0       LPSYS_PINMUX_PAD_PB21_DS0_Msk
#define LPSYS_PINMUX_PAD_PB21_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB21_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB21_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB21_DS1       LPSYS_PINMUX_PAD_PB21_DS1_Msk
#define LPSYS_PINMUX_PAD_PB21_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB21_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB21_POE_Pos)
#define LPSYS_PINMUX_PAD_PB21_POE       LPSYS_PINMUX_PAD_PB21_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB22 register **************/
#define LPSYS_PINMUX_PAD_PB22_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB22_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB22_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB22_FSEL      LPSYS_PINMUX_PAD_PB22_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB22_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB22_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB22_PE_Pos)
#define LPSYS_PINMUX_PAD_PB22_PE        LPSYS_PINMUX_PAD_PB22_PE_Msk
#define LPSYS_PINMUX_PAD_PB22_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB22_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB22_PS_Pos)
#define LPSYS_PINMUX_PAD_PB22_PS        LPSYS_PINMUX_PAD_PB22_PS_Msk
#define LPSYS_PINMUX_PAD_PB22_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB22_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB22_IE_Pos)
#define LPSYS_PINMUX_PAD_PB22_IE        LPSYS_PINMUX_PAD_PB22_IE_Msk
#define LPSYS_PINMUX_PAD_PB22_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB22_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB22_IS_Pos)
#define LPSYS_PINMUX_PAD_PB22_IS        LPSYS_PINMUX_PAD_PB22_IS_Msk
#define LPSYS_PINMUX_PAD_PB22_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB22_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB22_SR_Pos)
#define LPSYS_PINMUX_PAD_PB22_SR        LPSYS_PINMUX_PAD_PB22_SR_Msk
#define LPSYS_PINMUX_PAD_PB22_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB22_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB22_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB22_DS0       LPSYS_PINMUX_PAD_PB22_DS0_Msk
#define LPSYS_PINMUX_PAD_PB22_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB22_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB22_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB22_DS1       LPSYS_PINMUX_PAD_PB22_DS1_Msk
#define LPSYS_PINMUX_PAD_PB22_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB22_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB22_POE_Pos)
#define LPSYS_PINMUX_PAD_PB22_POE       LPSYS_PINMUX_PAD_PB22_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB23 register **************/
#define LPSYS_PINMUX_PAD_PB23_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB23_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB23_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB23_FSEL      LPSYS_PINMUX_PAD_PB23_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB23_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB23_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB23_PE_Pos)
#define LPSYS_PINMUX_PAD_PB23_PE        LPSYS_PINMUX_PAD_PB23_PE_Msk
#define LPSYS_PINMUX_PAD_PB23_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB23_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB23_PS_Pos)
#define LPSYS_PINMUX_PAD_PB23_PS        LPSYS_PINMUX_PAD_PB23_PS_Msk
#define LPSYS_PINMUX_PAD_PB23_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB23_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB23_IE_Pos)
#define LPSYS_PINMUX_PAD_PB23_IE        LPSYS_PINMUX_PAD_PB23_IE_Msk
#define LPSYS_PINMUX_PAD_PB23_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB23_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB23_IS_Pos)
#define LPSYS_PINMUX_PAD_PB23_IS        LPSYS_PINMUX_PAD_PB23_IS_Msk
#define LPSYS_PINMUX_PAD_PB23_MODE_Pos  (8U)
#define LPSYS_PINMUX_PAD_PB23_MODE_Msk  (0x1UL << LPSYS_PINMUX_PAD_PB23_MODE_Pos)
#define LPSYS_PINMUX_PAD_PB23_MODE      LPSYS_PINMUX_PAD_PB23_MODE_Msk
#define LPSYS_PINMUX_PAD_PB23_DS_Pos    (10U)
#define LPSYS_PINMUX_PAD_PB23_DS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB23_DS_Pos)
#define LPSYS_PINMUX_PAD_PB23_DS        LPSYS_PINMUX_PAD_PB23_DS_Msk
#define LPSYS_PINMUX_PAD_PB23_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB23_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB23_POE_Pos)
#define LPSYS_PINMUX_PAD_PB23_POE       LPSYS_PINMUX_PAD_PB23_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB24 register **************/
#define LPSYS_PINMUX_PAD_PB24_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB24_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB24_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB24_FSEL      LPSYS_PINMUX_PAD_PB24_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB24_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB24_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB24_PE_Pos)
#define LPSYS_PINMUX_PAD_PB24_PE        LPSYS_PINMUX_PAD_PB24_PE_Msk
#define LPSYS_PINMUX_PAD_PB24_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB24_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB24_PS_Pos)
#define LPSYS_PINMUX_PAD_PB24_PS        LPSYS_PINMUX_PAD_PB24_PS_Msk
#define LPSYS_PINMUX_PAD_PB24_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB24_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB24_IE_Pos)
#define LPSYS_PINMUX_PAD_PB24_IE        LPSYS_PINMUX_PAD_PB24_IE_Msk
#define LPSYS_PINMUX_PAD_PB24_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB24_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB24_IS_Pos)
#define LPSYS_PINMUX_PAD_PB24_IS        LPSYS_PINMUX_PAD_PB24_IS_Msk
#define LPSYS_PINMUX_PAD_PB24_MODE_Pos  (8U)
#define LPSYS_PINMUX_PAD_PB24_MODE_Msk  (0x1UL << LPSYS_PINMUX_PAD_PB24_MODE_Pos)
#define LPSYS_PINMUX_PAD_PB24_MODE      LPSYS_PINMUX_PAD_PB24_MODE_Msk
#define LPSYS_PINMUX_PAD_PB24_DS_Pos    (10U)
#define LPSYS_PINMUX_PAD_PB24_DS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB24_DS_Pos)
#define LPSYS_PINMUX_PAD_PB24_DS        LPSYS_PINMUX_PAD_PB24_DS_Msk
#define LPSYS_PINMUX_PAD_PB24_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB24_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB24_POE_Pos)
#define LPSYS_PINMUX_PAD_PB24_POE       LPSYS_PINMUX_PAD_PB24_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB25 register **************/
#define LPSYS_PINMUX_PAD_PB25_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB25_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB25_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB25_FSEL      LPSYS_PINMUX_PAD_PB25_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB25_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB25_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB25_PE_Pos)
#define LPSYS_PINMUX_PAD_PB25_PE        LPSYS_PINMUX_PAD_PB25_PE_Msk
#define LPSYS_PINMUX_PAD_PB25_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB25_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB25_PS_Pos)
#define LPSYS_PINMUX_PAD_PB25_PS        LPSYS_PINMUX_PAD_PB25_PS_Msk
#define LPSYS_PINMUX_PAD_PB25_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB25_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB25_IE_Pos)
#define LPSYS_PINMUX_PAD_PB25_IE        LPSYS_PINMUX_PAD_PB25_IE_Msk
#define LPSYS_PINMUX_PAD_PB25_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB25_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB25_IS_Pos)
#define LPSYS_PINMUX_PAD_PB25_IS        LPSYS_PINMUX_PAD_PB25_IS_Msk
#define LPSYS_PINMUX_PAD_PB25_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB25_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB25_SR_Pos)
#define LPSYS_PINMUX_PAD_PB25_SR        LPSYS_PINMUX_PAD_PB25_SR_Msk
#define LPSYS_PINMUX_PAD_PB25_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB25_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB25_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB25_DS0       LPSYS_PINMUX_PAD_PB25_DS0_Msk
#define LPSYS_PINMUX_PAD_PB25_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB25_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB25_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB25_DS1       LPSYS_PINMUX_PAD_PB25_DS1_Msk
#define LPSYS_PINMUX_PAD_PB25_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB25_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB25_POE_Pos)
#define LPSYS_PINMUX_PAD_PB25_POE       LPSYS_PINMUX_PAD_PB25_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB26 register **************/
#define LPSYS_PINMUX_PAD_PB26_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB26_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB26_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB26_FSEL      LPSYS_PINMUX_PAD_PB26_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB26_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB26_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB26_PE_Pos)
#define LPSYS_PINMUX_PAD_PB26_PE        LPSYS_PINMUX_PAD_PB26_PE_Msk
#define LPSYS_PINMUX_PAD_PB26_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB26_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB26_PS_Pos)
#define LPSYS_PINMUX_PAD_PB26_PS        LPSYS_PINMUX_PAD_PB26_PS_Msk
#define LPSYS_PINMUX_PAD_PB26_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB26_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB26_IE_Pos)
#define LPSYS_PINMUX_PAD_PB26_IE        LPSYS_PINMUX_PAD_PB26_IE_Msk
#define LPSYS_PINMUX_PAD_PB26_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB26_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB26_IS_Pos)
#define LPSYS_PINMUX_PAD_PB26_IS        LPSYS_PINMUX_PAD_PB26_IS_Msk
#define LPSYS_PINMUX_PAD_PB26_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB26_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB26_SR_Pos)
#define LPSYS_PINMUX_PAD_PB26_SR        LPSYS_PINMUX_PAD_PB26_SR_Msk
#define LPSYS_PINMUX_PAD_PB26_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB26_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB26_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB26_DS0       LPSYS_PINMUX_PAD_PB26_DS0_Msk
#define LPSYS_PINMUX_PAD_PB26_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB26_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB26_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB26_DS1       LPSYS_PINMUX_PAD_PB26_DS1_Msk
#define LPSYS_PINMUX_PAD_PB26_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB26_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB26_POE_Pos)
#define LPSYS_PINMUX_PAD_PB26_POE       LPSYS_PINMUX_PAD_PB26_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB27 register **************/
#define LPSYS_PINMUX_PAD_PB27_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB27_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB27_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB27_FSEL      LPSYS_PINMUX_PAD_PB27_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB27_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB27_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB27_PE_Pos)
#define LPSYS_PINMUX_PAD_PB27_PE        LPSYS_PINMUX_PAD_PB27_PE_Msk
#define LPSYS_PINMUX_PAD_PB27_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB27_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB27_PS_Pos)
#define LPSYS_PINMUX_PAD_PB27_PS        LPSYS_PINMUX_PAD_PB27_PS_Msk
#define LPSYS_PINMUX_PAD_PB27_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB27_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB27_IE_Pos)
#define LPSYS_PINMUX_PAD_PB27_IE        LPSYS_PINMUX_PAD_PB27_IE_Msk
#define LPSYS_PINMUX_PAD_PB27_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB27_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB27_IS_Pos)
#define LPSYS_PINMUX_PAD_PB27_IS        LPSYS_PINMUX_PAD_PB27_IS_Msk
#define LPSYS_PINMUX_PAD_PB27_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB27_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB27_SR_Pos)
#define LPSYS_PINMUX_PAD_PB27_SR        LPSYS_PINMUX_PAD_PB27_SR_Msk
#define LPSYS_PINMUX_PAD_PB27_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB27_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB27_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB27_DS0       LPSYS_PINMUX_PAD_PB27_DS0_Msk
#define LPSYS_PINMUX_PAD_PB27_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB27_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB27_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB27_DS1       LPSYS_PINMUX_PAD_PB27_DS1_Msk
#define LPSYS_PINMUX_PAD_PB27_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB27_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB27_POE_Pos)
#define LPSYS_PINMUX_PAD_PB27_POE       LPSYS_PINMUX_PAD_PB27_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB28 register **************/
#define LPSYS_PINMUX_PAD_PB28_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB28_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB28_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB28_FSEL      LPSYS_PINMUX_PAD_PB28_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB28_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB28_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB28_PE_Pos)
#define LPSYS_PINMUX_PAD_PB28_PE        LPSYS_PINMUX_PAD_PB28_PE_Msk
#define LPSYS_PINMUX_PAD_PB28_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB28_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB28_PS_Pos)
#define LPSYS_PINMUX_PAD_PB28_PS        LPSYS_PINMUX_PAD_PB28_PS_Msk
#define LPSYS_PINMUX_PAD_PB28_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB28_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB28_IE_Pos)
#define LPSYS_PINMUX_PAD_PB28_IE        LPSYS_PINMUX_PAD_PB28_IE_Msk
#define LPSYS_PINMUX_PAD_PB28_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB28_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB28_IS_Pos)
#define LPSYS_PINMUX_PAD_PB28_IS        LPSYS_PINMUX_PAD_PB28_IS_Msk
#define LPSYS_PINMUX_PAD_PB28_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB28_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB28_SR_Pos)
#define LPSYS_PINMUX_PAD_PB28_SR        LPSYS_PINMUX_PAD_PB28_SR_Msk
#define LPSYS_PINMUX_PAD_PB28_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB28_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB28_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB28_DS0       LPSYS_PINMUX_PAD_PB28_DS0_Msk
#define LPSYS_PINMUX_PAD_PB28_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB28_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB28_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB28_DS1       LPSYS_PINMUX_PAD_PB28_DS1_Msk
#define LPSYS_PINMUX_PAD_PB28_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB28_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB28_POE_Pos)
#define LPSYS_PINMUX_PAD_PB28_POE       LPSYS_PINMUX_PAD_PB28_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB29 register **************/
#define LPSYS_PINMUX_PAD_PB29_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB29_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB29_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB29_FSEL      LPSYS_PINMUX_PAD_PB29_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB29_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB29_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB29_PE_Pos)
#define LPSYS_PINMUX_PAD_PB29_PE        LPSYS_PINMUX_PAD_PB29_PE_Msk
#define LPSYS_PINMUX_PAD_PB29_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB29_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB29_PS_Pos)
#define LPSYS_PINMUX_PAD_PB29_PS        LPSYS_PINMUX_PAD_PB29_PS_Msk
#define LPSYS_PINMUX_PAD_PB29_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB29_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB29_IE_Pos)
#define LPSYS_PINMUX_PAD_PB29_IE        LPSYS_PINMUX_PAD_PB29_IE_Msk
#define LPSYS_PINMUX_PAD_PB29_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB29_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB29_IS_Pos)
#define LPSYS_PINMUX_PAD_PB29_IS        LPSYS_PINMUX_PAD_PB29_IS_Msk
#define LPSYS_PINMUX_PAD_PB29_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB29_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB29_SR_Pos)
#define LPSYS_PINMUX_PAD_PB29_SR        LPSYS_PINMUX_PAD_PB29_SR_Msk
#define LPSYS_PINMUX_PAD_PB29_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB29_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB29_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB29_DS0       LPSYS_PINMUX_PAD_PB29_DS0_Msk
#define LPSYS_PINMUX_PAD_PB29_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB29_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB29_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB29_DS1       LPSYS_PINMUX_PAD_PB29_DS1_Msk
#define LPSYS_PINMUX_PAD_PB29_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB29_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB29_POE_Pos)
#define LPSYS_PINMUX_PAD_PB29_POE       LPSYS_PINMUX_PAD_PB29_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB30 register **************/
#define LPSYS_PINMUX_PAD_PB30_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB30_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB30_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB30_FSEL      LPSYS_PINMUX_PAD_PB30_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB30_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB30_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB30_PE_Pos)
#define LPSYS_PINMUX_PAD_PB30_PE        LPSYS_PINMUX_PAD_PB30_PE_Msk
#define LPSYS_PINMUX_PAD_PB30_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB30_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB30_PS_Pos)
#define LPSYS_PINMUX_PAD_PB30_PS        LPSYS_PINMUX_PAD_PB30_PS_Msk
#define LPSYS_PINMUX_PAD_PB30_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB30_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB30_IE_Pos)
#define LPSYS_PINMUX_PAD_PB30_IE        LPSYS_PINMUX_PAD_PB30_IE_Msk
#define LPSYS_PINMUX_PAD_PB30_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB30_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB30_IS_Pos)
#define LPSYS_PINMUX_PAD_PB30_IS        LPSYS_PINMUX_PAD_PB30_IS_Msk
#define LPSYS_PINMUX_PAD_PB30_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB30_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB30_SR_Pos)
#define LPSYS_PINMUX_PAD_PB30_SR        LPSYS_PINMUX_PAD_PB30_SR_Msk
#define LPSYS_PINMUX_PAD_PB30_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB30_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB30_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB30_DS0       LPSYS_PINMUX_PAD_PB30_DS0_Msk
#define LPSYS_PINMUX_PAD_PB30_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB30_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB30_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB30_DS1       LPSYS_PINMUX_PAD_PB30_DS1_Msk
#define LPSYS_PINMUX_PAD_PB30_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB30_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB30_POE_Pos)
#define LPSYS_PINMUX_PAD_PB30_POE       LPSYS_PINMUX_PAD_PB30_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB31 register **************/
#define LPSYS_PINMUX_PAD_PB31_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB31_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB31_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB31_FSEL      LPSYS_PINMUX_PAD_PB31_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB31_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB31_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB31_PE_Pos)
#define LPSYS_PINMUX_PAD_PB31_PE        LPSYS_PINMUX_PAD_PB31_PE_Msk
#define LPSYS_PINMUX_PAD_PB31_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB31_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB31_PS_Pos)
#define LPSYS_PINMUX_PAD_PB31_PS        LPSYS_PINMUX_PAD_PB31_PS_Msk
#define LPSYS_PINMUX_PAD_PB31_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB31_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB31_IE_Pos)
#define LPSYS_PINMUX_PAD_PB31_IE        LPSYS_PINMUX_PAD_PB31_IE_Msk
#define LPSYS_PINMUX_PAD_PB31_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB31_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB31_IS_Pos)
#define LPSYS_PINMUX_PAD_PB31_IS        LPSYS_PINMUX_PAD_PB31_IS_Msk
#define LPSYS_PINMUX_PAD_PB31_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB31_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB31_SR_Pos)
#define LPSYS_PINMUX_PAD_PB31_SR        LPSYS_PINMUX_PAD_PB31_SR_Msk
#define LPSYS_PINMUX_PAD_PB31_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB31_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB31_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB31_DS0       LPSYS_PINMUX_PAD_PB31_DS0_Msk
#define LPSYS_PINMUX_PAD_PB31_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB31_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB31_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB31_DS1       LPSYS_PINMUX_PAD_PB31_DS1_Msk
#define LPSYS_PINMUX_PAD_PB31_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB31_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB31_POE_Pos)
#define LPSYS_PINMUX_PAD_PB31_POE       LPSYS_PINMUX_PAD_PB31_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB32 register **************/
#define LPSYS_PINMUX_PAD_PB32_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB32_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB32_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB32_FSEL      LPSYS_PINMUX_PAD_PB32_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB32_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB32_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB32_PE_Pos)
#define LPSYS_PINMUX_PAD_PB32_PE        LPSYS_PINMUX_PAD_PB32_PE_Msk
#define LPSYS_PINMUX_PAD_PB32_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB32_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB32_PS_Pos)
#define LPSYS_PINMUX_PAD_PB32_PS        LPSYS_PINMUX_PAD_PB32_PS_Msk
#define LPSYS_PINMUX_PAD_PB32_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB32_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB32_IE_Pos)
#define LPSYS_PINMUX_PAD_PB32_IE        LPSYS_PINMUX_PAD_PB32_IE_Msk
#define LPSYS_PINMUX_PAD_PB32_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB32_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB32_IS_Pos)
#define LPSYS_PINMUX_PAD_PB32_IS        LPSYS_PINMUX_PAD_PB32_IS_Msk
#define LPSYS_PINMUX_PAD_PB32_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB32_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB32_SR_Pos)
#define LPSYS_PINMUX_PAD_PB32_SR        LPSYS_PINMUX_PAD_PB32_SR_Msk
#define LPSYS_PINMUX_PAD_PB32_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB32_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB32_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB32_DS0       LPSYS_PINMUX_PAD_PB32_DS0_Msk
#define LPSYS_PINMUX_PAD_PB32_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB32_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB32_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB32_DS1       LPSYS_PINMUX_PAD_PB32_DS1_Msk
#define LPSYS_PINMUX_PAD_PB32_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB32_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB32_POE_Pos)
#define LPSYS_PINMUX_PAD_PB32_POE       LPSYS_PINMUX_PAD_PB32_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB33 register **************/
#define LPSYS_PINMUX_PAD_PB33_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB33_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB33_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB33_FSEL      LPSYS_PINMUX_PAD_PB33_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB33_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB33_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB33_PE_Pos)
#define LPSYS_PINMUX_PAD_PB33_PE        LPSYS_PINMUX_PAD_PB33_PE_Msk
#define LPSYS_PINMUX_PAD_PB33_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB33_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB33_PS_Pos)
#define LPSYS_PINMUX_PAD_PB33_PS        LPSYS_PINMUX_PAD_PB33_PS_Msk
#define LPSYS_PINMUX_PAD_PB33_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB33_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB33_IE_Pos)
#define LPSYS_PINMUX_PAD_PB33_IE        LPSYS_PINMUX_PAD_PB33_IE_Msk
#define LPSYS_PINMUX_PAD_PB33_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB33_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB33_IS_Pos)
#define LPSYS_PINMUX_PAD_PB33_IS        LPSYS_PINMUX_PAD_PB33_IS_Msk
#define LPSYS_PINMUX_PAD_PB33_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB33_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB33_SR_Pos)
#define LPSYS_PINMUX_PAD_PB33_SR        LPSYS_PINMUX_PAD_PB33_SR_Msk
#define LPSYS_PINMUX_PAD_PB33_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB33_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB33_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB33_DS0       LPSYS_PINMUX_PAD_PB33_DS0_Msk
#define LPSYS_PINMUX_PAD_PB33_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB33_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB33_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB33_DS1       LPSYS_PINMUX_PAD_PB33_DS1_Msk
#define LPSYS_PINMUX_PAD_PB33_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB33_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB33_POE_Pos)
#define LPSYS_PINMUX_PAD_PB33_POE       LPSYS_PINMUX_PAD_PB33_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB34 register **************/
#define LPSYS_PINMUX_PAD_PB34_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB34_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB34_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB34_FSEL      LPSYS_PINMUX_PAD_PB34_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB34_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB34_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB34_PE_Pos)
#define LPSYS_PINMUX_PAD_PB34_PE        LPSYS_PINMUX_PAD_PB34_PE_Msk
#define LPSYS_PINMUX_PAD_PB34_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB34_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB34_PS_Pos)
#define LPSYS_PINMUX_PAD_PB34_PS        LPSYS_PINMUX_PAD_PB34_PS_Msk
#define LPSYS_PINMUX_PAD_PB34_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB34_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB34_IE_Pos)
#define LPSYS_PINMUX_PAD_PB34_IE        LPSYS_PINMUX_PAD_PB34_IE_Msk
#define LPSYS_PINMUX_PAD_PB34_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB34_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB34_IS_Pos)
#define LPSYS_PINMUX_PAD_PB34_IS        LPSYS_PINMUX_PAD_PB34_IS_Msk
#define LPSYS_PINMUX_PAD_PB34_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB34_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB34_SR_Pos)
#define LPSYS_PINMUX_PAD_PB34_SR        LPSYS_PINMUX_PAD_PB34_SR_Msk
#define LPSYS_PINMUX_PAD_PB34_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB34_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB34_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB34_DS0       LPSYS_PINMUX_PAD_PB34_DS0_Msk
#define LPSYS_PINMUX_PAD_PB34_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB34_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB34_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB34_DS1       LPSYS_PINMUX_PAD_PB34_DS1_Msk
#define LPSYS_PINMUX_PAD_PB34_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB34_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB34_POE_Pos)
#define LPSYS_PINMUX_PAD_PB34_POE       LPSYS_PINMUX_PAD_PB34_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB35 register **************/
#define LPSYS_PINMUX_PAD_PB35_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB35_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB35_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB35_FSEL      LPSYS_PINMUX_PAD_PB35_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB35_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB35_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB35_PE_Pos)
#define LPSYS_PINMUX_PAD_PB35_PE        LPSYS_PINMUX_PAD_PB35_PE_Msk
#define LPSYS_PINMUX_PAD_PB35_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB35_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB35_PS_Pos)
#define LPSYS_PINMUX_PAD_PB35_PS        LPSYS_PINMUX_PAD_PB35_PS_Msk
#define LPSYS_PINMUX_PAD_PB35_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB35_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB35_IE_Pos)
#define LPSYS_PINMUX_PAD_PB35_IE        LPSYS_PINMUX_PAD_PB35_IE_Msk
#define LPSYS_PINMUX_PAD_PB35_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB35_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB35_IS_Pos)
#define LPSYS_PINMUX_PAD_PB35_IS        LPSYS_PINMUX_PAD_PB35_IS_Msk
#define LPSYS_PINMUX_PAD_PB35_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB35_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB35_SR_Pos)
#define LPSYS_PINMUX_PAD_PB35_SR        LPSYS_PINMUX_PAD_PB35_SR_Msk
#define LPSYS_PINMUX_PAD_PB35_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB35_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB35_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB35_DS0       LPSYS_PINMUX_PAD_PB35_DS0_Msk
#define LPSYS_PINMUX_PAD_PB35_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB35_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB35_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB35_DS1       LPSYS_PINMUX_PAD_PB35_DS1_Msk
#define LPSYS_PINMUX_PAD_PB35_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB35_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB35_POE_Pos)
#define LPSYS_PINMUX_PAD_PB35_POE       LPSYS_PINMUX_PAD_PB35_POE_Msk

/************* Bit definition for LPSYS_PINMUX_PAD_PB36 register **************/
#define LPSYS_PINMUX_PAD_PB36_FSEL_Pos  (0U)
#define LPSYS_PINMUX_PAD_PB36_FSEL_Msk  (0x7UL << LPSYS_PINMUX_PAD_PB36_FSEL_Pos)
#define LPSYS_PINMUX_PAD_PB36_FSEL      LPSYS_PINMUX_PAD_PB36_FSEL_Msk
#define LPSYS_PINMUX_PAD_PB36_PE_Pos    (4U)
#define LPSYS_PINMUX_PAD_PB36_PE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB36_PE_Pos)
#define LPSYS_PINMUX_PAD_PB36_PE        LPSYS_PINMUX_PAD_PB36_PE_Msk
#define LPSYS_PINMUX_PAD_PB36_PS_Pos    (5U)
#define LPSYS_PINMUX_PAD_PB36_PS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB36_PS_Pos)
#define LPSYS_PINMUX_PAD_PB36_PS        LPSYS_PINMUX_PAD_PB36_PS_Msk
#define LPSYS_PINMUX_PAD_PB36_IE_Pos    (6U)
#define LPSYS_PINMUX_PAD_PB36_IE_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB36_IE_Pos)
#define LPSYS_PINMUX_PAD_PB36_IE        LPSYS_PINMUX_PAD_PB36_IE_Msk
#define LPSYS_PINMUX_PAD_PB36_IS_Pos    (7U)
#define LPSYS_PINMUX_PAD_PB36_IS_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB36_IS_Pos)
#define LPSYS_PINMUX_PAD_PB36_IS        LPSYS_PINMUX_PAD_PB36_IS_Msk
#define LPSYS_PINMUX_PAD_PB36_SR_Pos    (8U)
#define LPSYS_PINMUX_PAD_PB36_SR_Msk    (0x1UL << LPSYS_PINMUX_PAD_PB36_SR_Pos)
#define LPSYS_PINMUX_PAD_PB36_SR        LPSYS_PINMUX_PAD_PB36_SR_Msk
#define LPSYS_PINMUX_PAD_PB36_DS0_Pos   (9U)
#define LPSYS_PINMUX_PAD_PB36_DS0_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB36_DS0_Pos)
#define LPSYS_PINMUX_PAD_PB36_DS0       LPSYS_PINMUX_PAD_PB36_DS0_Msk
#define LPSYS_PINMUX_PAD_PB36_DS1_Pos   (10U)
#define LPSYS_PINMUX_PAD_PB36_DS1_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB36_DS1_Pos)
#define LPSYS_PINMUX_PAD_PB36_DS1       LPSYS_PINMUX_PAD_PB36_DS1_Msk
#define LPSYS_PINMUX_PAD_PB36_POE_Pos   (11U)
#define LPSYS_PINMUX_PAD_PB36_POE_Msk   (0x1UL << LPSYS_PINMUX_PAD_PB36_POE_Pos)
#define LPSYS_PINMUX_PAD_PB36_POE       LPSYS_PINMUX_PAD_PB36_POE_Msk

/**************** Bit definition for LPSYS_PINMUX_CR register *****************/
#define LPSYS_PINMUX_CR_CTRL_Pos        (0U)
#define LPSYS_PINMUX_CR_CTRL_Msk        (0xFFUL << LPSYS_PINMUX_CR_CTRL_Pos)
#define LPSYS_PINMUX_CR_CTRL            LPSYS_PINMUX_CR_CTRL_Msk

#endif
