import os
from building import *

cwd = GetCurrentDir()
objs = []
list = os.listdir(cwd)

#include all subfolder
for d in list:
    path = os.path.join(cwd, d)
    if os.path.isfile(os.path.join(path, 'SConscript')):
        objs = objs + SConscript(os.path.join(d, 'SConscript'))

# Add SDK
Import('SIFLI_SDK')
objs.extend(SConscript(os.path.join(SIFLI_SDK, 'SConscript'), variant_dir="sifli_sdk", duplicate=0))

Return('objs')
