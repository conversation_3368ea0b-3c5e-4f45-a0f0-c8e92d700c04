#ifndef __BLE_PHY_H
#define __BLE_PHY_H

typedef struct
{
    __IO uint32_t RX_CTRL1;
    __IO uint32_t RX_RCC_CTRL1;
    __IO uint32_t NOTCH_CFG1;
    __IO uint32_t NOTCH_CFG2;
    __IO uint32_t NOTCH_CFG3;
    __IO uint32_t NOTCH_CFG4;
    __IO uint32_t INTERP_CFG1;
    __IO uint32_t MIXER_CFG1;
    __IO uint32_t PKTDET_CFG1;
    __IO uint32_t DEMOD_CFG1;
    __IO uint32_t DEMOD_CFG2;
    __IO uint32_t DEMOD_CFG3;
    __IO uint32_t DEMOD_CFG4;
    __IO uint32_t DEMOD_CFG5;
    __IO uint32_t DEMOD_CFG6;
    __IO uint32_t DEMOD_CFG7;
    __IO uint32_t CODED_CFG1;
    __IO uint32_t CODED_CFG2;
    __IO uint32_t CODED_CFG3;
    __IO uint32_t CODED_CFG4;
    __IO uint32_t RX_STATUS1;
    __IO uint32_t AGC_CTRL;
    __IO uint32_t AGC_CFG1;
    __IO uint32_t AGC_CFG2;
    __IO uint32_t AGC_CFG3;
    __IO uint32_t AGC_CFG4;
    __IO uint32_t AGC_CFG5;
    __IO uint32_t AGC_CFG6;
    __IO uint32_t AGC_CFG7;
    __IO uint32_t AGC_CFG8;
    __IO uint32_t AGC_CFG9;
    __IO uint32_t AGC_CFG10;
    __IO uint32_t AGC_CFG11;
    __IO uint32_t AGC_CFG12;
    __IO uint32_t RSSI_CFG1;
    __IO uint32_t AGC_STATUS;
    __IO uint32_t TX_CTRL;
    __IO uint32_t TX_RCC_CTRL;
    __IO uint32_t TX_GAUSSFLT_CFG;
    __IO uint32_t TX_IF_MOD_CFG;
    __IO uint32_t TX_HFP_CFG;
    __IO uint32_t TX_LFP_CFG;
    __IO uint32_t TX_PA_CFG;
    __IO uint32_t LFP_MMDIV_CFG0;
    __IO uint32_t LFP_MMDIV_CFG1;
    __IO uint32_t LFP_MMDIV_CFG2;
    __IO uint32_t LFP_MMDIV_CFG3;
    __IO uint32_t RX_HFP_CFG;
    __IO uint32_t LNA_GAIN_TBL0;
    __IO uint32_t LNA_GAIN_TBL1;
    __IO uint32_t LNA_GAIN_TBL2;
    __IO uint32_t LNA_GAIN_TBL3;
    __IO uint32_t LNA_GAIN_TBL4;
    __IO uint32_t LNA_GAIN_TBL5;
    __IO uint32_t MP_TEST_CFG;
} BLE_PHY_TypeDef;


/**************** Bit definition for BLE_PHY_RX_CTRL1 register ****************/
#define BLE_PHY_RX_CTRL1_ADC_SIGN_Pos   (0U)
#define BLE_PHY_RX_CTRL1_ADC_SIGN_Msk   (0x1UL << BLE_PHY_RX_CTRL1_ADC_SIGN_Pos)
#define BLE_PHY_RX_CTRL1_ADC_SIGN       BLE_PHY_RX_CTRL1_ADC_SIGN_Msk
#define BLE_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Pos  (1U)
#define BLE_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Msk  (0x1UL << BLE_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Pos)
#define BLE_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN  BLE_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Msk
#define BLE_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Pos  (2U)
#define BLE_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Msk  (0x1UL << BLE_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Pos)
#define BLE_PHY_RX_CTRL1_ADC_IQ_SWAP_EN  BLE_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Msk
#define BLE_PHY_RX_CTRL1_FORCE_RX_ON_Pos  (3U)
#define BLE_PHY_RX_CTRL1_FORCE_RX_ON_Msk  (0x1UL << BLE_PHY_RX_CTRL1_FORCE_RX_ON_Pos)
#define BLE_PHY_RX_CTRL1_FORCE_RX_ON    BLE_PHY_RX_CTRL1_FORCE_RX_ON_Msk
#define BLE_PHY_RX_CTRL1_ADC_Q_EN_Pos   (4U)
#define BLE_PHY_RX_CTRL1_ADC_Q_EN_Msk   (0x1UL << BLE_PHY_RX_CTRL1_ADC_Q_EN_Pos)
#define BLE_PHY_RX_CTRL1_ADC_Q_EN       BLE_PHY_RX_CTRL1_ADC_Q_EN_Msk
#define BLE_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Pos  (5U)
#define BLE_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Msk  (0x1UL << BLE_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Pos)
#define BLE_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL  BLE_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Msk
#define BLE_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Pos  (6U)
#define BLE_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Msk  (0x1UL << BLE_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RSSI_SAMPLE_SEL  BLE_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Msk
#define BLE_PHY_RX_CTRL1_DEMOD_METHOD_Pos  (7U)
#define BLE_PHY_RX_CTRL1_DEMOD_METHOD_Msk  (0x1UL << BLE_PHY_RX_CTRL1_DEMOD_METHOD_Pos)
#define BLE_PHY_RX_CTRL1_DEMOD_METHOD   BLE_PHY_RX_CTRL1_DEMOD_METHOD_Msk
#define BLE_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Pos  (8U)
#define BLE_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Msk  (0x1UL << BLE_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Pos)
#define BLE_PHY_RX_CTRL1_PHY_RX_DUMP_EN  BLE_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Msk
#define BLE_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Pos  (9U)
#define BLE_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Msk  (0x3UL << BLE_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RX_DUMP_CLK_SEL  BLE_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Msk
#define BLE_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Pos  (11U)
#define BLE_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Msk  (0xFUL << BLE_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RX_DUMP_DATA_SEL  BLE_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Msk
#define BLE_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Pos  (15U)
#define BLE_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Msk  (0x3UL << BLE_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RX_DBG_TRIG_SEL  BLE_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Msk
#define BLE_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Pos  (17U)
#define BLE_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Msk  (0xFUL << BLE_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RX_DBG_DATA_SEL  BLE_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Msk
#define BLE_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Pos  (21U)
#define BLE_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Msk  (0x1UL << BLE_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Pos)
#define BLE_PHY_RX_CTRL1_RX_LOOPBACK_MODE  BLE_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Msk
#define BLE_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Pos  (22U)
#define BLE_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Msk  (0x1UL << BLE_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Pos)
#define BLE_PHY_RX_CTRL1_RX_DUMP_Q_SEL  BLE_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Msk

/************** Bit definition for BLE_PHY_RX_RCC_CTRL1 register **************/
#define BLE_PHY_RX_RCC_CTRL1_FORCE_CLK_ON_AGC_Pos  (0U)
#define BLE_PHY_RX_RCC_CTRL1_FORCE_CLK_ON_AGC_Msk  (0x1UL << BLE_PHY_RX_RCC_CTRL1_FORCE_CLK_ON_AGC_Pos)
#define BLE_PHY_RX_RCC_CTRL1_FORCE_CLK_ON_AGC  BLE_PHY_RX_RCC_CTRL1_FORCE_CLK_ON_AGC_Msk
#define BLE_PHY_RX_RCC_CTRL1_FORCE_RX_RESET_Pos  (1U)
#define BLE_PHY_RX_RCC_CTRL1_FORCE_RX_RESET_Msk  (0x1UL << BLE_PHY_RX_RCC_CTRL1_FORCE_RX_RESET_Pos)
#define BLE_PHY_RX_RCC_CTRL1_FORCE_RX_RESET  BLE_PHY_RX_RCC_CTRL1_FORCE_RX_RESET_Msk

/*************** Bit definition for BLE_PHY_NOTCH_CFG1 register ***************/
#define BLE_PHY_NOTCH_CFG1_NOTCH_B0_Pos  (0U)
#define BLE_PHY_NOTCH_CFG1_NOTCH_B0_Msk  (0x3FFFUL << BLE_PHY_NOTCH_CFG1_NOTCH_B0_Pos)
#define BLE_PHY_NOTCH_CFG1_NOTCH_B0     BLE_PHY_NOTCH_CFG1_NOTCH_B0_Msk
#define BLE_PHY_NOTCH_CFG1_NOTCH_B1_Pos  (14U)
#define BLE_PHY_NOTCH_CFG1_NOTCH_B1_Msk  (0x3FFFUL << BLE_PHY_NOTCH_CFG1_NOTCH_B1_Pos)
#define BLE_PHY_NOTCH_CFG1_NOTCH_B1     BLE_PHY_NOTCH_CFG1_NOTCH_B1_Msk

/*************** Bit definition for BLE_PHY_NOTCH_CFG2 register ***************/
#define BLE_PHY_NOTCH_CFG2_NOTCH_A2_Pos  (0U)
#define BLE_PHY_NOTCH_CFG2_NOTCH_A2_Msk  (0x3FFFUL << BLE_PHY_NOTCH_CFG2_NOTCH_A2_Pos)
#define BLE_PHY_NOTCH_CFG2_NOTCH_A2     BLE_PHY_NOTCH_CFG2_NOTCH_A2_Msk

/*************** Bit definition for BLE_PHY_NOTCH_CFG3 register ***************/
#define BLE_PHY_NOTCH_CFG3_CHNL_NOTCH_EN0_Pos  (0U)
#define BLE_PHY_NOTCH_CFG3_CHNL_NOTCH_EN0_Msk  (0xFFFFFFFFUL << BLE_PHY_NOTCH_CFG3_CHNL_NOTCH_EN0_Pos)
#define BLE_PHY_NOTCH_CFG3_CHNL_NOTCH_EN0  BLE_PHY_NOTCH_CFG3_CHNL_NOTCH_EN0_Msk

/*************** Bit definition for BLE_PHY_NOTCH_CFG4 register ***************/
#define BLE_PHY_NOTCH_CFG4_CHNL_NOTCH_EN1_Pos  (0U)
#define BLE_PHY_NOTCH_CFG4_CHNL_NOTCH_EN1_Msk  (0xFFUL << BLE_PHY_NOTCH_CFG4_CHNL_NOTCH_EN1_Pos)
#define BLE_PHY_NOTCH_CFG4_CHNL_NOTCH_EN1  BLE_PHY_NOTCH_CFG4_CHNL_NOTCH_EN1_Msk
#define BLE_PHY_NOTCH_CFG4_NOTCH_RSSI_THD_Pos  (8U)
#define BLE_PHY_NOTCH_CFG4_NOTCH_RSSI_THD_Msk  (0xFFUL << BLE_PHY_NOTCH_CFG4_NOTCH_RSSI_THD_Pos)
#define BLE_PHY_NOTCH_CFG4_NOTCH_RSSI_THD  BLE_PHY_NOTCH_CFG4_NOTCH_RSSI_THD_Msk

/************** Bit definition for BLE_PHY_INTERP_CFG1 register ***************/
#define BLE_PHY_INTERP_CFG1_INTERP_EN_U_Pos  (0U)
#define BLE_PHY_INTERP_CFG1_INTERP_EN_U_Msk  (0x1UL << BLE_PHY_INTERP_CFG1_INTERP_EN_U_Pos)
#define BLE_PHY_INTERP_CFG1_INTERP_EN_U  BLE_PHY_INTERP_CFG1_INTERP_EN_U_Msk
#define BLE_PHY_INTERP_CFG1_TIMING_FACTOR_Pos  (1U)
#define BLE_PHY_INTERP_CFG1_TIMING_FACTOR_Msk  (0x7FUL << BLE_PHY_INTERP_CFG1_TIMING_FACTOR_Pos)
#define BLE_PHY_INTERP_CFG1_TIMING_FACTOR  BLE_PHY_INTERP_CFG1_TIMING_FACTOR_Msk
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_U_Pos  (8U)
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_U_Msk  (0x1UL << BLE_PHY_INTERP_CFG1_INTERP_METHOD_U_Pos)
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_U  BLE_PHY_INTERP_CFG1_INTERP_METHOD_U_Msk
#define BLE_PHY_INTERP_CFG1_TED_MU_P_U_Pos  (9U)
#define BLE_PHY_INTERP_CFG1_TED_MU_P_U_Msk  (0xFUL << BLE_PHY_INTERP_CFG1_TED_MU_P_U_Pos)
#define BLE_PHY_INTERP_CFG1_TED_MU_P_U  BLE_PHY_INTERP_CFG1_TED_MU_P_U_Msk
#define BLE_PHY_INTERP_CFG1_TED_MU_F_U_Pos  (13U)
#define BLE_PHY_INTERP_CFG1_TED_MU_F_U_Msk  (0xFUL << BLE_PHY_INTERP_CFG1_TED_MU_F_U_Pos)
#define BLE_PHY_INTERP_CFG1_TED_MU_F_U  BLE_PHY_INTERP_CFG1_TED_MU_F_U_Msk
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_C_Pos  (17U)
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_C_Msk  (0x1UL << BLE_PHY_INTERP_CFG1_INTERP_METHOD_C_Pos)
#define BLE_PHY_INTERP_CFG1_INTERP_METHOD_C  BLE_PHY_INTERP_CFG1_INTERP_METHOD_C_Msk
#define BLE_PHY_INTERP_CFG1_TED_MU_P_C_Pos  (18U)
#define BLE_PHY_INTERP_CFG1_TED_MU_P_C_Msk  (0xFUL << BLE_PHY_INTERP_CFG1_TED_MU_P_C_Pos)
#define BLE_PHY_INTERP_CFG1_TED_MU_P_C  BLE_PHY_INTERP_CFG1_TED_MU_P_C_Msk
#define BLE_PHY_INTERP_CFG1_TED_MU_F_C_Pos  (22U)
#define BLE_PHY_INTERP_CFG1_TED_MU_F_C_Msk  (0xFUL << BLE_PHY_INTERP_CFG1_TED_MU_F_C_Pos)
#define BLE_PHY_INTERP_CFG1_TED_MU_F_C  BLE_PHY_INTERP_CFG1_TED_MU_F_C_Msk
#define BLE_PHY_INTERP_CFG1_INTERP_EN_C_Pos  (26U)
#define BLE_PHY_INTERP_CFG1_INTERP_EN_C_Msk  (0x1UL << BLE_PHY_INTERP_CFG1_INTERP_EN_C_Pos)
#define BLE_PHY_INTERP_CFG1_INTERP_EN_C  BLE_PHY_INTERP_CFG1_INTERP_EN_C_Msk

/*************** Bit definition for BLE_PHY_MIXER_CFG1 register ***************/
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Pos  (0U)
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Msk  (0x3FFUL << BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Pos)
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_1  BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Msk
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Pos  (10U)
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Msk  (0x3FFUL << BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Pos)
#define BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_2  BLE_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Msk

/************** Bit definition for BLE_PHY_PKTDET_CFG1 register ***************/
#define BLE_PHY_PKTDET_CFG1_PKTDET_THD_Pos  (0U)
#define BLE_PHY_PKTDET_CFG1_PKTDET_THD_Msk  (0xFFFFUL << BLE_PHY_PKTDET_CFG1_PKTDET_THD_Pos)
#define BLE_PHY_PKTDET_CFG1_PKTDET_THD  BLE_PHY_PKTDET_CFG1_PKTDET_THD_Msk
#define BLE_PHY_PKTDET_CFG1_PKT_CNT_THD_Pos  (16U)
#define BLE_PHY_PKTDET_CFG1_PKT_CNT_THD_Msk  (0xFFUL << BLE_PHY_PKTDET_CFG1_PKT_CNT_THD_Pos)
#define BLE_PHY_PKTDET_CFG1_PKT_CNT_THD  BLE_PHY_PKTDET_CFG1_PKT_CNT_THD_Msk
#define BLE_PHY_PKTDET_CFG1_HARD_CORR_THD_Pos  (24U)
#define BLE_PHY_PKTDET_CFG1_HARD_CORR_THD_Msk  (0xFUL << BLE_PHY_PKTDET_CFG1_HARD_CORR_THD_Pos)
#define BLE_PHY_PKTDET_CFG1_HARD_CORR_THD  BLE_PHY_PKTDET_CFG1_HARD_CORR_THD_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG1 register ***************/
#define BLE_PHY_DEMOD_CFG1_MU_ERR_Pos   (0U)
#define BLE_PHY_DEMOD_CFG1_MU_ERR_Msk   (0x3FFUL << BLE_PHY_DEMOD_CFG1_MU_ERR_Pos)
#define BLE_PHY_DEMOD_CFG1_MU_ERR       BLE_PHY_DEMOD_CFG1_MU_ERR_Msk
#define BLE_PHY_DEMOD_CFG1_MU_DC_Pos    (10U)
#define BLE_PHY_DEMOD_CFG1_MU_DC_Msk    (0x3FFUL << BLE_PHY_DEMOD_CFG1_MU_DC_Pos)
#define BLE_PHY_DEMOD_CFG1_MU_DC        BLE_PHY_DEMOD_CFG1_MU_DC_Msk
#define BLE_PHY_DEMOD_CFG1_DEMOD_G_Pos  (20U)
#define BLE_PHY_DEMOD_CFG1_DEMOD_G_Msk  (0x7FFUL << BLE_PHY_DEMOD_CFG1_DEMOD_G_Pos)
#define BLE_PHY_DEMOD_CFG1_DEMOD_G      BLE_PHY_DEMOD_CFG1_DEMOD_G_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG2 register ***************/
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_0_Pos  (0U)
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_0_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_0_Pos)
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_0  BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_0_Msk
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_1_Pos  (12U)
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_1_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_1_Pos)
#define BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_1  BLE_PHY_DEMOD_CFG2_DEMOD_PHASE_1_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG3 register ***************/
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_2_Pos  (0U)
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_2_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_2_Pos)
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_2  BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_2_Msk
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_3_Pos  (12U)
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_3_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_3_Pos)
#define BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_3  BLE_PHY_DEMOD_CFG3_DEMOD_PHASE_3_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG4 register ***************/
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_4_Pos  (0U)
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_4_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_4_Pos)
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_4  BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_4_Msk
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_5_Pos  (12U)
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_5_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_5_Pos)
#define BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_5  BLE_PHY_DEMOD_CFG4_DEMOD_PHASE_5_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG5 register ***************/
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_6_Pos  (0U)
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_6_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_6_Pos)
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_6  BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_6_Msk
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_7_Pos  (12U)
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_7_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_7_Pos)
#define BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_7  BLE_PHY_DEMOD_CFG5_DEMOD_PHASE_7_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG6 register ***************/
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_0_Pos  (0U)
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_0_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_0_Pos)
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_0  BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_0_Msk
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_1_Pos  (12U)
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_1_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_1_Pos)
#define BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_1  BLE_PHY_DEMOD_CFG6_DEMOD_PHASE_IDEAL_1_Msk

/*************** Bit definition for BLE_PHY_DEMOD_CFG7 register ***************/
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_2_Pos  (0U)
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_2_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_2_Pos)
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_2  BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_2_Msk
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_3_Pos  (12U)
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_3_Msk  (0xFFFUL << BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_3_Pos)
#define BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_3  BLE_PHY_DEMOD_CFG7_DEMOD_PHASE_IDEAL_3_Msk


/*************** Bit definition for BLE_PHY_CODED_CFG1 register ***************/
#define BLE_PHY_CODED_CFG1_HARD_ACCESS_CORR_THD_Pos  (0U)
#define BLE_PHY_CODED_CFG1_HARD_ACCESS_CORR_THD_Msk  (0xFFUL << BLE_PHY_CODED_CFG1_HARD_ACCESS_CORR_THD_Pos)
#define BLE_PHY_CODED_CFG1_HARD_ACCESS_CORR_THD  BLE_PHY_CODED_CFG1_HARD_ACCESS_CORR_THD_Msk
#define BLE_PHY_CODED_CFG1_SOFT_ACCESS_CORR_THD_Pos  (8U)
#define BLE_PHY_CODED_CFG1_SOFT_ACCESS_CORR_THD_Msk  (0xFFFUL << BLE_PHY_CODED_CFG1_SOFT_ACCESS_CORR_THD_Pos)
#define BLE_PHY_CODED_CFG1_SOFT_ACCESS_CORR_THD  BLE_PHY_CODED_CFG1_SOFT_ACCESS_CORR_THD_Msk
#define BLE_PHY_CODED_CFG1_PHASE_UNWRAP_THD_Pos  (20U)
#define BLE_PHY_CODED_CFG1_PHASE_UNWRAP_THD_Msk  (0x1FUL << BLE_PHY_CODED_CFG1_PHASE_UNWRAP_THD_Pos)
#define BLE_PHY_CODED_CFG1_PHASE_UNWRAP_THD  BLE_PHY_CODED_CFG1_PHASE_UNWRAP_THD_Msk
#define BLE_PHY_CODED_CFG1_PREAMBLE_LEN_SEL_Pos  (25U)
#define BLE_PHY_CODED_CFG1_PREAMBLE_LEN_SEL_Msk  (0x7UL << BLE_PHY_CODED_CFG1_PREAMBLE_LEN_SEL_Pos)
#define BLE_PHY_CODED_CFG1_PREAMBLE_LEN_SEL  BLE_PHY_CODED_CFG1_PREAMBLE_LEN_SEL_Msk
#define BLE_PHY_CODED_CFG1_PHASE_DIFF_FP_SEL_Pos  (28U)
#define BLE_PHY_CODED_CFG1_PHASE_DIFF_FP_SEL_Msk  (0x3UL << BLE_PHY_CODED_CFG1_PHASE_DIFF_FP_SEL_Pos)
#define BLE_PHY_CODED_CFG1_PHASE_DIFF_FP_SEL  BLE_PHY_CODED_CFG1_PHASE_DIFF_FP_SEL_Msk

/*************** Bit definition for BLE_PHY_CODED_CFG2 register ***************/
#define BLE_PHY_CODED_CFG2_HARD_CORR_THD_CODED_Pos  (0U)
#define BLE_PHY_CODED_CFG2_HARD_CORR_THD_CODED_Msk  (0x1FFUL << BLE_PHY_CODED_CFG2_HARD_CORR_THD_CODED_Pos)
#define BLE_PHY_CODED_CFG2_HARD_CORR_THD_CODED  BLE_PHY_CODED_CFG2_HARD_CORR_THD_CODED_Msk
#define BLE_PHY_CODED_CFG2_SOFT_CORR_THD_CODED_Pos  (9U)
#define BLE_PHY_CODED_CFG2_SOFT_CORR_THD_CODED_Msk  (0xFFFUL << BLE_PHY_CODED_CFG2_SOFT_CORR_THD_CODED_Pos)
#define BLE_PHY_CODED_CFG2_SOFT_CORR_THD_CODED  BLE_PHY_CODED_CFG2_SOFT_CORR_THD_CODED_Msk

/*************** Bit definition for BLE_PHY_CODED_CFG3 register ***************/
#define BLE_PHY_CODED_CFG3_CI_G_Pos     (0U)
#define BLE_PHY_CODED_CFG3_CI_G_Msk     (0x3FFUL << BLE_PHY_CODED_CFG3_CI_G_Pos)
#define BLE_PHY_CODED_CFG3_CI_G         BLE_PHY_CODED_CFG3_CI_G_Msk
#define BLE_PHY_CODED_CFG3_CI_MU_DC_Pos  (10U)
#define BLE_PHY_CODED_CFG3_CI_MU_DC_Msk  (0x3FFUL << BLE_PHY_CODED_CFG3_CI_MU_DC_Pos)
#define BLE_PHY_CODED_CFG3_CI_MU_DC     BLE_PHY_CODED_CFG3_CI_MU_DC_Msk
#define BLE_PHY_CODED_CFG3_CI_MU_ERR_Pos  (20U)
#define BLE_PHY_CODED_CFG3_CI_MU_ERR_Msk  (0x3FFUL << BLE_PHY_CODED_CFG3_CI_MU_ERR_Pos)
#define BLE_PHY_CODED_CFG3_CI_MU_ERR    BLE_PHY_CODED_CFG3_CI_MU_ERR_Msk

/*************** Bit definition for BLE_PHY_CODED_CFG4 register ***************/
#define BLE_PHY_CODED_CFG4_DEC_G_Pos    (0U)
#define BLE_PHY_CODED_CFG4_DEC_G_Msk    (0x3FFUL << BLE_PHY_CODED_CFG4_DEC_G_Pos)
#define BLE_PHY_CODED_CFG4_DEC_G        BLE_PHY_CODED_CFG4_DEC_G_Msk
#define BLE_PHY_CODED_CFG4_DEC_MU_DC_Pos  (10U)
#define BLE_PHY_CODED_CFG4_DEC_MU_DC_Msk  (0x3FFUL << BLE_PHY_CODED_CFG4_DEC_MU_DC_Pos)
#define BLE_PHY_CODED_CFG4_DEC_MU_DC    BLE_PHY_CODED_CFG4_DEC_MU_DC_Msk
#define BLE_PHY_CODED_CFG4_DEC_MU_ERR_Pos  (20U)
#define BLE_PHY_CODED_CFG4_DEC_MU_ERR_Msk  (0x3FFUL << BLE_PHY_CODED_CFG4_DEC_MU_ERR_Pos)
#define BLE_PHY_CODED_CFG4_DEC_MU_ERR   BLE_PHY_CODED_CFG4_DEC_MU_ERR_Msk

/*************** Bit definition for BLE_PHY_RX_STATUS1 register ***************/
#define BLE_PHY_RX_STATUS1_PKT_DETECTED_Pos  (0U)
#define BLE_PHY_RX_STATUS1_PKT_DETECTED_Msk  (0x1UL << BLE_PHY_RX_STATUS1_PKT_DETECTED_Pos)
#define BLE_PHY_RX_STATUS1_PKT_DETECTED  BLE_PHY_RX_STATUS1_PKT_DETECTED_Msk
#define BLE_PHY_RX_STATUS1_CFO_PHASE_Pos  (1U)
#define BLE_PHY_RX_STATUS1_CFO_PHASE_Msk  (0xFFFUL << BLE_PHY_RX_STATUS1_CFO_PHASE_Pos)
#define BLE_PHY_RX_STATUS1_CFO_PHASE    BLE_PHY_RX_STATUS1_CFO_PHASE_Msk

/**************** Bit definition for BLE_PHY_AGC_CTRL register ****************/
#define BLE_PHY_AGC_CTRL_AGC_ENABLE_Pos  (0U)
#define BLE_PHY_AGC_CTRL_AGC_ENABLE_Msk  (0x1UL << BLE_PHY_AGC_CTRL_AGC_ENABLE_Pos)
#define BLE_PHY_AGC_CTRL_AGC_ENABLE     BLE_PHY_AGC_CTRL_AGC_ENABLE_Msk
#define BLE_PHY_AGC_CTRL_AGC_MODE_Pos   (1U)
#define BLE_PHY_AGC_CTRL_AGC_MODE_Msk   (0x1UL << BLE_PHY_AGC_CTRL_AGC_MODE_Pos)
#define BLE_PHY_AGC_CTRL_AGC_MODE       BLE_PHY_AGC_CTRL_AGC_MODE_Msk
#define BLE_PHY_AGC_CTRL_DIG_GAIN_EN_Pos  (2U)
#define BLE_PHY_AGC_CTRL_DIG_GAIN_EN_Msk  (0x1UL << BLE_PHY_AGC_CTRL_DIG_GAIN_EN_Pos)
#define BLE_PHY_AGC_CTRL_DIG_GAIN_EN    BLE_PHY_AGC_CTRL_DIG_GAIN_EN_Msk
#define BLE_PHY_AGC_CTRL_AGC_VGAADJ_EN_Pos  (3U)
#define BLE_PHY_AGC_CTRL_AGC_VGAADJ_EN_Msk  (0x1UL << BLE_PHY_AGC_CTRL_AGC_VGAADJ_EN_Pos)
#define BLE_PHY_AGC_CTRL_AGC_VGAADJ_EN  BLE_PHY_AGC_CTRL_AGC_VGAADJ_EN_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG1 register ****************/
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD0_Pos  (0U)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD0_Msk  (0x3FFUL << BLE_PHY_AGC_CFG1_ADC_MAG_THD0_Pos)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD0   BLE_PHY_AGC_CFG1_ADC_MAG_THD0_Msk
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD1_Pos  (10U)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD1_Msk  (0x3FFUL << BLE_PHY_AGC_CFG1_ADC_MAG_THD1_Pos)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD1   BLE_PHY_AGC_CFG1_ADC_MAG_THD1_Msk
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD2_Pos  (20U)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD2_Msk  (0x3FFUL << BLE_PHY_AGC_CFG1_ADC_MAG_THD2_Pos)
#define BLE_PHY_AGC_CFG1_ADC_MAG_THD2   BLE_PHY_AGC_CFG1_ADC_MAG_THD2_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG2 register ****************/
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Pos  (0U)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Msk  (0xFUL << BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Pos)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD0  BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Msk
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Pos  (4U)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Msk  (0xFUL << BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Pos)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD1  BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Msk
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Pos  (8U)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Msk  (0xFUL << BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Pos)
#define BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD2  BLE_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Msk
#define BLE_PHY_AGC_CFG2_ADC_MAG_SET_Pos  (12U)
#define BLE_PHY_AGC_CFG2_ADC_MAG_SET_Msk  (0x3FFUL << BLE_PHY_AGC_CFG2_ADC_MAG_SET_Pos)
#define BLE_PHY_AGC_CFG2_ADC_MAG_SET    BLE_PHY_AGC_CFG2_ADC_MAG_SET_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG3 register ****************/
#define BLE_PHY_AGC_CFG3_ADC_SAT_THD_Pos  (0U)
#define BLE_PHY_AGC_CFG3_ADC_SAT_THD_Msk  (0x3FFUL << BLE_PHY_AGC_CFG3_ADC_SAT_THD_Pos)
#define BLE_PHY_AGC_CFG3_ADC_SAT_THD    BLE_PHY_AGC_CFG3_ADC_SAT_THD_Msk
#define BLE_PHY_AGC_CFG3_ADC_SAT_NUM_Pos  (10U)
#define BLE_PHY_AGC_CFG3_ADC_SAT_NUM_Msk  (0xFUL << BLE_PHY_AGC_CFG3_ADC_SAT_NUM_Pos)
#define BLE_PHY_AGC_CFG3_ADC_SAT_NUM    BLE_PHY_AGC_CFG3_ADC_SAT_NUM_Msk
#define BLE_PHY_AGC_CFG3_DIG_GAIN_LOW_Pos  (14U)
#define BLE_PHY_AGC_CFG3_DIG_GAIN_LOW_Msk  (0x3FUL << BLE_PHY_AGC_CFG3_DIG_GAIN_LOW_Pos)
#define BLE_PHY_AGC_CFG3_DIG_GAIN_LOW   BLE_PHY_AGC_CFG3_DIG_GAIN_LOW_Msk
#define BLE_PHY_AGC_CFG3_DIG_GAIN_HIGH_Pos  (20U)
#define BLE_PHY_AGC_CFG3_DIG_GAIN_HIGH_Msk  (0x3FUL << BLE_PHY_AGC_CFG3_DIG_GAIN_HIGH_Pos)
#define BLE_PHY_AGC_CFG3_DIG_GAIN_HIGH  BLE_PHY_AGC_CFG3_DIG_GAIN_HIGH_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG4 register ****************/
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Pos  (0U)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Msk  (0xFUL << BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Pos)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD  BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Msk
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Pos  (4U)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Msk  (0x3UL << BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Pos)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD  BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Msk
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Pos  (6U)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Msk  (0xFUL << BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Pos)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD  BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Msk
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Pos  (10U)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Msk  (0xFUL << BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Pos)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT  BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Msk
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Pos  (14U)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Msk  (0x3UL << BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Pos)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT  BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Msk
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Pos  (16U)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Msk  (0xFUL << BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Pos)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT  BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Msk
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Pos  (20U)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Msk  (0xFUL << BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Pos)
#define BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP  BLE_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Msk
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Pos  (24U)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Msk  (0x3UL << BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Pos)
#define BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP  BLE_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Msk
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Pos  (26U)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Msk  (0xFUL << BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Pos)
#define BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP  BLE_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG5 register ****************/
#define BLE_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Pos  (0U)
#define BLE_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Msk  (0x3UL << BLE_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Pos)
#define BLE_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0  BLE_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Msk
#define BLE_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Pos  (2U)
#define BLE_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Msk  (0xFUL << BLE_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Pos)
#define BLE_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0  BLE_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG6 register ****************/
#define BLE_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Pos  (0U)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Pos)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_RESET_1  BLE_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Msk
#define BLE_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Pos  (7U)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Pos)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_PKDET_1  BLE_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Msk
#define BLE_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Pos  (14U)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Pos)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_LNA_1  BLE_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Msk
#define BLE_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Pos  (21U)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Pos)
#define BLE_PHY_AGC_CFG6_AGC_DELAY_DIG_1  BLE_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG7 register ****************/
#define BLE_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Pos  (0U)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Pos)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_RESET_2  BLE_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Msk
#define BLE_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Pos  (7U)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Pos)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_PKDET_2  BLE_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Msk
#define BLE_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Pos  (14U)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Pos)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_LNA_2  BLE_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Msk
#define BLE_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Pos  (21U)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Pos)
#define BLE_PHY_AGC_CFG7_AGC_DELAY_DIG_2  BLE_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG8 register ****************/
#define BLE_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Pos  (0U)
#define BLE_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Pos)
#define BLE_PHY_AGC_CFG8_AGC_DELAY_CBPF_1  BLE_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Msk
#define BLE_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Pos  (7U)
#define BLE_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Pos)
#define BLE_PHY_AGC_CFG8_AGC_DELAY_ADC_1  BLE_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Msk
#define BLE_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Pos  (14U)
#define BLE_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Pos)
#define BLE_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1  BLE_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Msk
#define BLE_PHY_AGC_CFG8_ADC_POWER_TARGET_Pos  (21U)
#define BLE_PHY_AGC_CFG8_ADC_POWER_TARGET_Msk  (0x7FUL << BLE_PHY_AGC_CFG8_ADC_POWER_TARGET_Pos)
#define BLE_PHY_AGC_CFG8_ADC_POWER_TARGET  BLE_PHY_AGC_CFG8_ADC_POWER_TARGET_Msk

/**************** Bit definition for BLE_PHY_AGC_CFG9 register ****************/
#define BLE_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Pos  (0U)
#define BLE_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Pos)
#define BLE_PHY_AGC_CFG9_AGC_DELAY_CBPF_2  BLE_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Msk
#define BLE_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Pos  (7U)
#define BLE_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Pos)
#define BLE_PHY_AGC_CFG9_AGC_DELAY_ADC_2  BLE_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Msk
#define BLE_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Pos  (14U)
#define BLE_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Pos)
#define BLE_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2  BLE_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Msk

/*************** Bit definition for BLE_PHY_AGC_CFG10 register ****************/
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Pos  (0U)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Pos)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1  BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Msk
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Pos  (7U)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Pos)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1  BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Msk
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Pos  (14U)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Pos)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1  BLE_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Msk
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Pos  (21U)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Pos)
#define BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1  BLE_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Msk

/*************** Bit definition for BLE_PHY_AGC_CFG11 register ****************/
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Pos  (0U)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Pos)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2  BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Msk
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Pos  (7U)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Pos)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2  BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Msk
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Pos  (14U)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Pos)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2  BLE_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Msk
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Pos  (21U)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Pos)
#define BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2  BLE_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Msk

/*************** Bit definition for BLE_PHY_AGC_CFG12 register ****************/
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Pos  (0U)
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Msk  (0x7FUL << BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Pos)
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_1  BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Msk
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Pos  (7U)
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Msk  (0x7FUL << BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Pos)
#define BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_2  BLE_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Msk
#define BLE_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Pos  (14U)
#define BLE_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Msk  (0x7FUL << BLE_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Pos)
#define BLE_PHY_AGC_CFG12_ADC_POWER_URUN_THD  BLE_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Msk

/*************** Bit definition for BLE_PHY_RSSI_CFG1 register ****************/
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Pos  (0U)
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Msk  (0x7FUL << BLE_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Pos)
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB  BLE_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Msk
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Pos  (7U)
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Msk  (0x7FUL << BLE_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Pos)
#define BLE_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB  BLE_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Msk
#define BLE_PHY_RSSI_CFG1_RSSI_MU_Pos   (14U)
#define BLE_PHY_RSSI_CFG1_RSSI_MU_Msk   (0x7UL << BLE_PHY_RSSI_CFG1_RSSI_MU_Pos)
#define BLE_PHY_RSSI_CFG1_RSSI_MU       BLE_PHY_RSSI_CFG1_RSSI_MU_Msk
#define BLE_PHY_RSSI_CFG1_RSSI_OFFSET_Pos  (17U)
#define BLE_PHY_RSSI_CFG1_RSSI_OFFSET_Msk  (0x3FUL << BLE_PHY_RSSI_CFG1_RSSI_OFFSET_Pos)
#define BLE_PHY_RSSI_CFG1_RSSI_OFFSET   BLE_PHY_RSSI_CFG1_RSSI_OFFSET_Msk

/*************** Bit definition for BLE_PHY_AGC_STATUS register ***************/
#define BLE_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Pos  (0U)
#define BLE_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Msk  (0xFUL << BLE_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Pos)
#define BLE_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX  BLE_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Msk
#define BLE_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Pos  (4U)
#define BLE_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Msk  (0x3UL << BLE_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Pos)
#define BLE_PHY_AGC_STATUS_CBPF_GAIN_INDEX  BLE_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Msk
#define BLE_PHY_AGC_STATUS_VGA_GAIN_INDEX_Pos  (6U)
#define BLE_PHY_AGC_STATUS_VGA_GAIN_INDEX_Msk  (0xFUL << BLE_PHY_AGC_STATUS_VGA_GAIN_INDEX_Pos)
#define BLE_PHY_AGC_STATUS_VGA_GAIN_INDEX  BLE_PHY_AGC_STATUS_VGA_GAIN_INDEX_Msk
#define BLE_PHY_AGC_STATUS_ADC_DIG_GAIN_Pos  (10U)
#define BLE_PHY_AGC_STATUS_ADC_DIG_GAIN_Msk  (0x3FUL << BLE_PHY_AGC_STATUS_ADC_DIG_GAIN_Pos)
#define BLE_PHY_AGC_STATUS_ADC_DIG_GAIN  BLE_PHY_AGC_STATUS_ADC_DIG_GAIN_Msk
#define BLE_PHY_AGC_STATUS_RSSI_Pos     (16U)
#define BLE_PHY_AGC_STATUS_RSSI_Msk     (0xFFUL << BLE_PHY_AGC_STATUS_RSSI_Pos)
#define BLE_PHY_AGC_STATUS_RSSI         BLE_PHY_AGC_STATUS_RSSI_Msk

/**************** Bit definition for BLE_PHY_TX_CTRL register *****************/
#define BLE_PHY_TX_CTRL_FORCE_TX_ON_Pos  (0U)
#define BLE_PHY_TX_CTRL_FORCE_TX_ON_Msk  (0x1UL << BLE_PHY_TX_CTRL_FORCE_TX_ON_Pos)
#define BLE_PHY_TX_CTRL_FORCE_TX_ON     BLE_PHY_TX_CTRL_FORCE_TX_ON_Msk
#define BLE_PHY_TX_CTRL_TX_LOOPBACK_MODE_Pos  (1U)
#define BLE_PHY_TX_CTRL_TX_LOOPBACK_MODE_Msk  (0x1UL << BLE_PHY_TX_CTRL_TX_LOOPBACK_MODE_Pos)
#define BLE_PHY_TX_CTRL_TX_LOOPBACK_MODE  BLE_PHY_TX_CTRL_TX_LOOPBACK_MODE_Msk
#define BLE_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Pos  (2U)
#define BLE_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Msk  (0x1UL << BLE_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Pos)
#define BLE_PHY_TX_CTRL_MAC_PWR_CTRL_EN  BLE_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Msk

/************** Bit definition for BLE_PHY_TX_RCC_CTRL register ***************/
#define BLE_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Pos  (0U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON  BLE_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Pos  (1U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_LFP_ON  BLE_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Pos  (2U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_HFP_ON  BLE_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Pos  (3U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON  BLE_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_RC_ON_Pos  (4U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_RC_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_RC_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_RC_ON  BLE_PHY_TX_RCC_CTRL_FORCE_RC_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Pos  (5U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON  BLE_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Msk
#define BLE_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Pos  (6U)
#define BLE_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Msk  (0x1UL << BLE_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Pos)
#define BLE_PHY_TX_RCC_CTRL_FORCE_TX_RESET  BLE_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Msk

/************ Bit definition for BLE_PHY_TX_GAUSSFLT_CFG register *************/
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Pos  (0U)
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Msk  (0x1FFUL << BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Pos)
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1  BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Msk
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Pos  (9U)
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Msk  (0x1FFUL << BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Pos)
#define BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2  BLE_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Msk

/************* Bit definition for BLE_PHY_TX_IF_MOD_CFG register **************/
#define BLE_PHY_TX_IF_MOD_CFG_TX_MOD_GAIN_Pos  (0U)
#define BLE_PHY_TX_IF_MOD_CFG_TX_MOD_GAIN_Msk  (0xFFUL << BLE_PHY_TX_IF_MOD_CFG_TX_MOD_GAIN_Pos)
#define BLE_PHY_TX_IF_MOD_CFG_TX_MOD_GAIN  BLE_PHY_TX_IF_MOD_CFG_TX_MOD_GAIN_Msk
#define BLE_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_Pos  (8U)
#define BLE_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_Msk  (0x3FFUL << BLE_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_Pos)
#define BLE_PHY_TX_IF_MOD_CFG_TX_IF_PHASE  BLE_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_Msk

/*************** Bit definition for BLE_PHY_TX_HFP_CFG register ***************/
#define BLE_PHY_TX_HFP_CFG_TX_KCAL_COEF_Pos  (0U)
#define BLE_PHY_TX_HFP_CFG_TX_KCAL_COEF_Msk  (0x1FFUL << BLE_PHY_TX_HFP_CFG_TX_KCAL_COEF_Pos)
#define BLE_PHY_TX_HFP_CFG_TX_KCAL_COEF  BLE_PHY_TX_HFP_CFG_TX_KCAL_COEF_Msk
#define BLE_PHY_TX_HFP_CFG_TX_KCAL_Pos  (9U)
#define BLE_PHY_TX_HFP_CFG_TX_KCAL_Msk  (0xFFFUL << BLE_PHY_TX_HFP_CFG_TX_KCAL_Pos)
#define BLE_PHY_TX_HFP_CFG_TX_KCAL      BLE_PHY_TX_HFP_CFG_TX_KCAL_Msk
#define BLE_PHY_TX_HFP_CFG_HFP_FCW_SEL_Pos  (21U)
#define BLE_PHY_TX_HFP_CFG_HFP_FCW_SEL_Msk  (0x1UL << BLE_PHY_TX_HFP_CFG_HFP_FCW_SEL_Pos)
#define BLE_PHY_TX_HFP_CFG_HFP_FCW_SEL  BLE_PHY_TX_HFP_CFG_HFP_FCW_SEL_Msk
#define BLE_PHY_TX_HFP_CFG_HFP_FCW_Pos  (22U)
#define BLE_PHY_TX_HFP_CFG_HFP_FCW_Msk  (0x3FUL << BLE_PHY_TX_HFP_CFG_HFP_FCW_Pos)
#define BLE_PHY_TX_HFP_CFG_HFP_FCW      BLE_PHY_TX_HFP_CFG_HFP_FCW_Msk
#define BLE_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Pos  (28U)
#define BLE_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Msk  (0x7UL << BLE_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Pos)
#define BLE_PHY_TX_HFP_CFG_HFP_DELAY_SEL  BLE_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Msk

/*************** Bit definition for BLE_PHY_TX_LFP_CFG register ***************/
#define BLE_PHY_TX_LFP_CFG_LFP_FCW_SEL_Pos  (0U)
#define BLE_PHY_TX_LFP_CFG_LFP_FCW_SEL_Msk  (0x1UL << BLE_PHY_TX_LFP_CFG_LFP_FCW_SEL_Pos)
#define BLE_PHY_TX_LFP_CFG_LFP_FCW_SEL  BLE_PHY_TX_LFP_CFG_LFP_FCW_SEL_Msk
#define BLE_PHY_TX_LFP_CFG_LFP_FCW_Pos  (1U)
#define BLE_PHY_TX_LFP_CFG_LFP_FCW_Msk  (0x3FFUL << BLE_PHY_TX_LFP_CFG_LFP_FCW_Pos)
#define BLE_PHY_TX_LFP_CFG_LFP_FCW      BLE_PHY_TX_LFP_CFG_LFP_FCW_Msk
#define BLE_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Pos  (11U)
#define BLE_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Msk  (0x1UL << BLE_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Pos)
#define BLE_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN  BLE_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Msk
#define BLE_PHY_TX_LFP_CFG_TX_SDM_SEL_Pos  (12U)
#define BLE_PHY_TX_LFP_CFG_TX_SDM_SEL_Msk  (0x1UL << BLE_PHY_TX_LFP_CFG_TX_SDM_SEL_Pos)
#define BLE_PHY_TX_LFP_CFG_TX_SDM_SEL   BLE_PHY_TX_LFP_CFG_TX_SDM_SEL_Msk

/*************** Bit definition for BLE_PHY_TX_PA_CFG register ****************/
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FORCE_Pos  (0U)
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FORCE_Msk  (0x3UL << BLE_PHY_TX_PA_CFG_PA_RAMP_FORCE_Pos)
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FORCE  BLE_PHY_TX_PA_CFG_PA_RAMP_FORCE_Msk
#define BLE_PHY_TX_PA_CFG_PA_CTRL_TARGET_Pos  (2U)
#define BLE_PHY_TX_PA_CFG_PA_CTRL_TARGET_Msk  (0x3FUL << BLE_PHY_TX_PA_CFG_PA_CTRL_TARGET_Pos)
#define BLE_PHY_TX_PA_CFG_PA_CTRL_TARGET  BLE_PHY_TX_PA_CFG_PA_CTRL_TARGET_Msk
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Pos  (8U)
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Msk  (0x7UL << BLE_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Pos)
#define BLE_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX  BLE_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Msk

/************* Bit definition for BLE_PHY_LFP_MMDIV_CFG0 register *************/
#define BLE_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Pos  (0U)
#define BLE_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Msk  (0x1FFFFUL << BLE_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Pos)
#define BLE_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M  BLE_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Msk

/************* Bit definition for BLE_PHY_LFP_MMDIV_CFG1 register *************/
#define BLE_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Pos  (0U)
#define BLE_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Msk  (0x1FFFFUL << BLE_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Pos)
#define BLE_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M  BLE_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Msk

/************* Bit definition for BLE_PHY_LFP_MMDIV_CFG2 register *************/
#define BLE_PHY_LFP_MMDIV_CFG2_TX_MMDIV_OFFSET_Pos  (0U)
#define BLE_PHY_LFP_MMDIV_CFG2_TX_MMDIV_OFFSET_Msk  (0x1FFFFUL << BLE_PHY_LFP_MMDIV_CFG2_TX_MMDIV_OFFSET_Pos)
#define BLE_PHY_LFP_MMDIV_CFG2_TX_MMDIV_OFFSET  BLE_PHY_LFP_MMDIV_CFG2_TX_MMDIV_OFFSET_Msk

/************* Bit definition for BLE_PHY_LFP_MMDIV_CFG3 register *************/
#define BLE_PHY_LFP_MMDIV_CFG3_RF_MMDIV_TEST_Pos  (0U)
#define BLE_PHY_LFP_MMDIV_CFG3_RF_MMDIV_TEST_Msk  (0x1FFFFFFUL << BLE_PHY_LFP_MMDIV_CFG3_RF_MMDIV_TEST_Pos)
#define BLE_PHY_LFP_MMDIV_CFG3_RF_MMDIV_TEST  BLE_PHY_LFP_MMDIV_CFG3_RF_MMDIV_TEST_Msk
#define BLE_PHY_LFP_MMDIV_CFG3_RF_TEST_MODE_Pos  (25U)
#define BLE_PHY_LFP_MMDIV_CFG3_RF_TEST_MODE_Msk  (0x1UL << BLE_PHY_LFP_MMDIV_CFG3_RF_TEST_MODE_Pos)
#define BLE_PHY_LFP_MMDIV_CFG3_RF_TEST_MODE  BLE_PHY_LFP_MMDIV_CFG3_RF_TEST_MODE_Msk

/*************** Bit definition for BLE_PHY_RX_HFP_CFG register ***************/
#define BLE_PHY_RX_HFP_CFG_RX_HFP_FCW_Pos  (0U)
#define BLE_PHY_RX_HFP_CFG_RX_HFP_FCW_Msk  (0x3FUL << BLE_PHY_RX_HFP_CFG_RX_HFP_FCW_Pos)
#define BLE_PHY_RX_HFP_CFG_RX_HFP_FCW   BLE_PHY_RX_HFP_CFG_RX_HFP_FCW_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL0 register **************/
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Pos)
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_0  BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Msk
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Pos)
#define BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_1  BLE_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL1 register **************/
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Pos)
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_2  BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Msk
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Pos)
#define BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_3  BLE_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL2 register **************/
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Pos)
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_4  BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Msk
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Pos)
#define BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_5  BLE_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL3 register **************/
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Pos)
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_6  BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Msk
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Pos)
#define BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_7  BLE_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL4 register **************/
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Pos)
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_8  BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Msk
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Pos)
#define BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_9  BLE_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Msk

/************* Bit definition for BLE_PHY_LNA_GAIN_TBL5 register **************/
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Pos  (0U)
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Pos)
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_10  BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Msk
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Pos  (11U)
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Msk  (0x7FFUL << BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Pos)
#define BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_11  BLE_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Msk

/************** Bit definition for BLE_PHY_MP_TEST_CFG register ***************/
#define BLE_PHY_MP_TEST_CFG_DC_EST_EN_Pos  (0U)
#define BLE_PHY_MP_TEST_CFG_DC_EST_EN_Msk  (0x1UL << BLE_PHY_MP_TEST_CFG_DC_EST_EN_Pos)
#define BLE_PHY_MP_TEST_CFG_DC_EST_EN   BLE_PHY_MP_TEST_CFG_DC_EST_EN_Msk
#define BLE_PHY_MP_TEST_CFG_DC_EST_MU_Pos  (1U)
#define BLE_PHY_MP_TEST_CFG_DC_EST_MU_Msk  (0x7UL << BLE_PHY_MP_TEST_CFG_DC_EST_MU_Pos)
#define BLE_PHY_MP_TEST_CFG_DC_EST_MU   BLE_PHY_MP_TEST_CFG_DC_EST_MU_Msk
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_LOW_Pos  (4U)
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_LOW_Msk  (0xFUL << BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_LOW_Pos)
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_LOW  BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_LOW_Msk
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_HIGH_Pos  (8U)
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_HIGH_Msk  (0xFUL << BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_HIGH_Pos)
#define BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_HIGH  BLE_PHY_MP_TEST_CFG_MP_LNA_GAIN_INDEX_HIGH_Msk
#define BLE_PHY_MP_TEST_CFG_DC_EST_BYPASS_Pos  (12U)
#define BLE_PHY_MP_TEST_CFG_DC_EST_BYPASS_Msk  (0x1UL << BLE_PHY_MP_TEST_CFG_DC_EST_BYPASS_Pos)
#define BLE_PHY_MP_TEST_CFG_DC_EST_BYPASS  BLE_PHY_MP_TEST_CFG_DC_EST_BYPASS_Msk
#endif
