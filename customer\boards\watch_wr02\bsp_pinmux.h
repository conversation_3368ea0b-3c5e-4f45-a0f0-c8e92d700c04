/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   bsp_pinmux.h
@Time    :   2025/02/19 19:46:45
*
**************************************************************************/
#ifndef __BSP_PINMUX_H__
#define __BSP_PINMUX_H__
#ifdef SF32LB55X
#include "bf0_hal_pinmux.h"
#include "bf0_hal_gpio.h"

/************************************************************************
 *@function:pad_pinmux_set
 *@brief:Set pin function,set PINMUX_PAD register
 * @param  pad: physical pin,PAD_PAXX~PAD_PBXX
 * @param  func: Pin function.
 * @param  mode: flag of the pin (pullup/pulldown/irq rising/irq falling )
 * mode enum as GPIO_mode,GPIO_MODE_INPUT~GPIO_MODE_IT_LOW_LEVEL,
 * refer to HAL_PIN_Set && sifli_pin_irq_enable
 * @param  is_hcpu: 1: pin for hcpu; 0: pin for lcpu
 *************************************************************************/
#define sf_pinmux_set(pad, func, pullmode, is_hcpu) HAL_PIN_Set(pad, func, pullmode, is_hcpu)

/************************************************************************
 *@function:qw_gpio_init(handle, Pin, dir, pullmode)
 *@brief:see HAL_GPIO_Init
 *@param:handle:GPIO_TypeDef*,hwp_gpio1 or hwp_gpio2,
 @param: pin,pin id of GPIO1 or GPIO2, starting from 0
 @param：dir,This parameter can be a value of ref GPIO_mode
 @param：Pull,only for assert check,not used,ref IS_GPIO_PULL
 *************************************************************************/
#define sf_gpio_init(handle, idx, dir, pullmode)                 \
    do                                                           \
    {                                                            \
        GPIO_InitTypeDef GPIO_InitStruct;                        \
        GPIO_InitStruct.Pin = idx;                               \
        GPIO_InitStruct.Mode = dir;                              \
        GPIO_InitStruct.Pull = pullmode;                         \
        HAL_GPIO_Init((GPIO_TypeDef *)handle, &GPIO_InitStruct); \
    } while (0)


/************************************************************************
 *@function:qw_gpio_set(pin, dir, pullmode)
 *@brief:just only for set pin to gpio function
 *@param:pin: HCPU:0~95 LCPU:96 ~ 96+32
 *@param:dir: direction,
    GPIO_MODE_OUTPUT/GPIO_MODE_INPUT/GPIO_MODE_OUTPUT_OD/GPIO_MODE_IT_XXX
 *@param:pullmode: PIN_PULLUP/PIN_PULLDOWN/PIN_NOPULL
*************************************************************************/
#define qw_gpio_set(pin, dir, pullmode)                 \
    do                                                  \
    {                                                   \
        int pad = PAD_PA00, func = GPIO_A0;             \
        int sub_pin = pin;                              \
        int is_hcpu = false;                            \
        GPIO_TypeDef *gphandle = NULL;                  \
        if (pin < GPIO1_PIN_NUM)                        \
        {                                               \
            sub_pin = pin;                              \
            pad = PAD_PA00 + sub_pin;                       \
            func = GPIO_A0 + sub_pin;                   \
            is_hcpu = true;                             \
            gphandle = (GPIO_TypeDef *)hwp_gpio1;       \
        }                                               \
        else                                            \
        {                                               \
            sub_pin = pin - GPIO1_PIN_NUM;              \
            pad = PAD_PB00 + sub_pin;                   \
            func = GPIO_B0 + sub_pin;                   \
            gphandle = (GPIO_TypeDef *)hwp_gpio2;       \
        }                                               \
        sf_pinmux_set(pad, func, pullmode, is_hcpu);    \
        sf_gpio_init(gphandle, sub_pin, dir, GPIO_NOPULL); \
    } while (0);

/************************************************************************
 *@function:qw_special_pin_set(pin, func, pullmode)
 *@brief:特殊功能PIN不需要执行HAL_GPIO_Init，忽略方向
 *@param:pin: HCPU:0~95 LCPU:96 ~ 96+32
 *@param:func: pinmux function
 *@param:pullmode: PIN_PULLUP/PIN_PULLDOWN/PIN_NOPULL
 *************************************************************************/
#define qw_special_pin_set(pin, func, pullmode)      \
    do                                               \
    {                                                \
        int pad = 0;                                 \
        int is_hcpu = false;                         \
        if (pin < GPIO1_PIN_NUM)                     \
        {                                            \
            is_hcpu = true;                          \
            pad = PAD_PA00 + pin;                    \
        }                                            \
        else                                         \
        {                                            \
            pad = PAD_PB00 + (pin - GPIO1_PIN_NUM);  \
        }                                            \
        sf_pinmux_set(pad, func, pullmode, is_hcpu); \
    } while (0);

    // A3和A4板的差异部分放这里
void BSP_PIN_CustomInit_V2(int hw_ver);

#else // SF32LB55X,other platform not support.

#error "SF32LB55X,other platform not support"

#endif /*SF32LB55X*/

#endif /*__BSP_PINMUX_H__*/
