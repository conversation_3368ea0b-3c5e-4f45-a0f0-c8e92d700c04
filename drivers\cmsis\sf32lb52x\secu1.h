#ifndef __SECU1_H
#define __SECU1_H

typedef struct
{
    __IO uint32_t SECU_CTRL;
    __IO uint32_t IRQ_STAT0;
    __IO uint32_t IRQ_STAT1;
    __IO uint32_t IRQ_CFG0;
    __IO uint32_t IRQ_CFG1;
    __IO uint32_t HPMST_ATTR_CFG;
    __IO uint32_t HPSLV_ATTR_CFG;
    __IO uint32_t MPI1_PRIV_CFG0;
    __IO uint32_t MPI1_PRIV_CFG1;
    __IO uint32_t MPI1_PRIV_CFG2;
    __IO uint32_t MPI1_PRIV_CFG3;
    __IO uint32_t MPI1_SEC_CFG0;
    __IO uint32_t MPI1_SEC_CFG1;
    __IO uint32_t MPI1_SEC_CFG2;
    __IO uint32_t MPI1_SEC_CFG3;
    __IO uint32_t MPI2_PRIV_CFG0;
    __IO uint32_t MPI2_PRIV_CFG1;
    __IO uint32_t MPI2_PRIV_CFG2;
    __IO uint32_t MPI2_PRIV_CFG3;
    __IO uint32_t MPI2_SEC_CFG0;
    __IO uint32_t MPI2_SEC_CFG1;
    __IO uint32_t MPI2_SEC_CFG2;
    __IO uint32_t MPI2_SEC_CFG3;
    __IO uint32_t RAM0_PRIV_CFG0;
    __IO uint32_t RAM0_PRIV_CFG1;
    __IO uint32_t RAM0_SEC_CFG0;
    __IO uint32_t RAM0_SEC_CFG1;
    __IO uint32_t RAM1_PRIV_CFG0;
    __IO uint32_t RAM1_PRIV_CFG1;
    __IO uint32_t RAM1_SEC_CFG0;
    __IO uint32_t RAM1_SEC_CFG1;
    __IO uint32_t RAM2_PRIV_CFG0;
    __IO uint32_t RAM2_PRIV_CFG1;
    __IO uint32_t RAM2_SEC_CFG0;
    __IO uint32_t RAM2_SEC_CFG1;
} SECU1_TypeDef;


/**************** Bit definition for SECU1_SECU_CTRL register *****************/
#define SECU1_SECU_CTRL_HPMST_LOCK_Pos  (0U)
#define SECU1_SECU_CTRL_HPMST_LOCK_Msk  (0x1UL << SECU1_SECU_CTRL_HPMST_LOCK_Pos)
#define SECU1_SECU_CTRL_HPMST_LOCK      SECU1_SECU_CTRL_HPMST_LOCK_Msk
#define SECU1_SECU_CTRL_HPSLV_LOCK_Pos  (1U)
#define SECU1_SECU_CTRL_HPSLV_LOCK_Msk  (0x1UL << SECU1_SECU_CTRL_HPSLV_LOCK_Pos)
#define SECU1_SECU_CTRL_HPSLV_LOCK      SECU1_SECU_CTRL_HPSLV_LOCK_Msk
#define SECU1_SECU_CTRL_MPI1_LOCK_Pos   (2U)
#define SECU1_SECU_CTRL_MPI1_LOCK_Msk   (0x1UL << SECU1_SECU_CTRL_MPI1_LOCK_Pos)
#define SECU1_SECU_CTRL_MPI1_LOCK       SECU1_SECU_CTRL_MPI1_LOCK_Msk
#define SECU1_SECU_CTRL_MPI2_LOCK_Pos   (3U)
#define SECU1_SECU_CTRL_MPI2_LOCK_Msk   (0x1UL << SECU1_SECU_CTRL_MPI2_LOCK_Pos)
#define SECU1_SECU_CTRL_MPI2_LOCK       SECU1_SECU_CTRL_MPI2_LOCK_Msk
#define SECU1_SECU_CTRL_RAM_LOCK_Pos    (4U)
#define SECU1_SECU_CTRL_RAM_LOCK_Msk    (0x1UL << SECU1_SECU_CTRL_RAM_LOCK_Pos)
#define SECU1_SECU_CTRL_RAM_LOCK        SECU1_SECU_CTRL_RAM_LOCK_Msk
#define SECU1_SECU_CTRL_HPMST_ATTR_UPDATE_Pos  (8U)
#define SECU1_SECU_CTRL_HPMST_ATTR_UPDATE_Msk  (0x1UL << SECU1_SECU_CTRL_HPMST_ATTR_UPDATE_Pos)
#define SECU1_SECU_CTRL_HPMST_ATTR_UPDATE  SECU1_SECU_CTRL_HPMST_ATTR_UPDATE_Msk

/**************** Bit definition for SECU1_IRQ_STAT0 register *****************/
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_W_Pos  (0U)
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_PTC1_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_W  SECU1_IRQ_STAT0_ILL_PTC1_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_R_Pos  (1U)
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_PTC1_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_PTC1_PRIV_R  SECU1_IRQ_STAT0_ILL_PTC1_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_W_Pos  (2U)
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_PTC1_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_W  SECU1_IRQ_STAT0_ILL_PTC1_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_R_Pos  (3U)
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_PTC1_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_PTC1_SEC_R  SECU1_IRQ_STAT0_ILL_PTC1_SEC_R_Msk
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_W_Pos  (4U)
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_TRNG_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_W  SECU1_IRQ_STAT0_ILL_TRNG_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_R_Pos  (5U)
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_TRNG_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_TRNG_PRIV_R  SECU1_IRQ_STAT0_ILL_TRNG_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_W_Pos  (6U)
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_TRNG_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_W  SECU1_IRQ_STAT0_ILL_TRNG_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_R_Pos  (7U)
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_TRNG_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_TRNG_SEC_R  SECU1_IRQ_STAT0_ILL_TRNG_SEC_R_Msk
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_W_Pos  (8U)
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_W  SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_R_Pos  (9U)
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_R  SECU1_IRQ_STAT0_ILL_EFUSE_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_W_Pos  (10U)
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_EFUSE_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_W  SECU1_IRQ_STAT0_ILL_EFUSE_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_R_Pos  (11U)
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_EFUSE_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_EFUSE_SEC_R  SECU1_IRQ_STAT0_ILL_EFUSE_SEC_R_Msk
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_W_Pos  (12U)
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_AES_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_W  SECU1_IRQ_STAT0_ILL_AES_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_R_Pos  (13U)
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_AES_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_AES_PRIV_R  SECU1_IRQ_STAT0_ILL_AES_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_AES_SEC_W_Pos  (14U)
#define SECU1_IRQ_STAT0_ILL_AES_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_AES_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_AES_SEC_W   SECU1_IRQ_STAT0_ILL_AES_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_AES_SEC_R_Pos  (15U)
#define SECU1_IRQ_STAT0_ILL_AES_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_AES_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_AES_SEC_R   SECU1_IRQ_STAT0_ILL_AES_SEC_R_Msk
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_W_Pos  (16U)
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_W  SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_R_Pos  (17U)
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_R  SECU1_IRQ_STAT0_ILL_DMAC1_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_W_Pos  (18U)
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_DMAC1_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_W  SECU1_IRQ_STAT0_ILL_DMAC1_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_R_Pos  (19U)
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_DMAC1_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_DMAC1_SEC_R  SECU1_IRQ_STAT0_ILL_DMAC1_SEC_R_Msk
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_W_Pos  (20U)
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_SECU1_PRIV_W_Pos)
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_W  SECU1_IRQ_STAT0_ILL_SECU1_PRIV_W_Msk
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_R_Pos  (21U)
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_SECU1_PRIV_R_Pos)
#define SECU1_IRQ_STAT0_ILL_SECU1_PRIV_R  SECU1_IRQ_STAT0_ILL_SECU1_PRIV_R_Msk
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_W_Pos  (22U)
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_SECU1_SEC_W_Pos)
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_W  SECU1_IRQ_STAT0_ILL_SECU1_SEC_W_Msk
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_R_Pos  (23U)
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT0_ILL_SECU1_SEC_R_Pos)
#define SECU1_IRQ_STAT0_ILL_SECU1_SEC_R  SECU1_IRQ_STAT0_ILL_SECU1_SEC_R_Msk

/**************** Bit definition for SECU1_IRQ_STAT1 register *****************/
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_W_Pos  (0U)
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI1_PRIV_W_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_W  SECU1_IRQ_STAT1_ILL_MPI1_PRIV_W_Msk
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_R_Pos  (1U)
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI1_PRIV_R_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI1_PRIV_R  SECU1_IRQ_STAT1_ILL_MPI1_PRIV_R_Msk
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_W_Pos  (2U)
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI1_SEC_W_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_W  SECU1_IRQ_STAT1_ILL_MPI1_SEC_W_Msk
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_R_Pos  (3U)
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI1_SEC_R_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI1_SEC_R  SECU1_IRQ_STAT1_ILL_MPI1_SEC_R_Msk
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_W_Pos  (4U)
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI2_PRIV_W_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_W  SECU1_IRQ_STAT1_ILL_MPI2_PRIV_W_Msk
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_R_Pos  (5U)
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI2_PRIV_R_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI2_PRIV_R  SECU1_IRQ_STAT1_ILL_MPI2_PRIV_R_Msk
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_W_Pos  (6U)
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI2_SEC_W_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_W  SECU1_IRQ_STAT1_ILL_MPI2_SEC_W_Msk
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_R_Pos  (7U)
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_MPI2_SEC_R_Pos)
#define SECU1_IRQ_STAT1_ILL_MPI2_SEC_R  SECU1_IRQ_STAT1_ILL_MPI2_SEC_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_W_Pos  (8U)
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM0_PRIV_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_W  SECU1_IRQ_STAT1_ILL_RAM0_PRIV_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_R_Pos  (9U)
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM0_PRIV_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM0_PRIV_R  SECU1_IRQ_STAT1_ILL_RAM0_PRIV_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_W_Pos  (10U)
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM0_SEC_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_W  SECU1_IRQ_STAT1_ILL_RAM0_SEC_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_R_Pos  (11U)
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM0_SEC_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM0_SEC_R  SECU1_IRQ_STAT1_ILL_RAM0_SEC_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_W_Pos  (12U)
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM1_PRIV_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_W  SECU1_IRQ_STAT1_ILL_RAM1_PRIV_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_R_Pos  (13U)
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM1_PRIV_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM1_PRIV_R  SECU1_IRQ_STAT1_ILL_RAM1_PRIV_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_W_Pos  (14U)
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM1_SEC_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_W  SECU1_IRQ_STAT1_ILL_RAM1_SEC_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_R_Pos  (15U)
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM1_SEC_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM1_SEC_R  SECU1_IRQ_STAT1_ILL_RAM1_SEC_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_W_Pos  (16U)
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM2_PRIV_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_W  SECU1_IRQ_STAT1_ILL_RAM2_PRIV_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_R_Pos  (17U)
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM2_PRIV_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM2_PRIV_R  SECU1_IRQ_STAT1_ILL_RAM2_PRIV_R_Msk
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_W_Pos  (18U)
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_W_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM2_SEC_W_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_W  SECU1_IRQ_STAT1_ILL_RAM2_SEC_W_Msk
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_R_Pos  (19U)
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_R_Msk  (0x1UL << SECU1_IRQ_STAT1_ILL_RAM2_SEC_R_Pos)
#define SECU1_IRQ_STAT1_ILL_RAM2_SEC_R  SECU1_IRQ_STAT1_ILL_RAM2_SEC_R_Msk

/***************** Bit definition for SECU1_IRQ_CFG0 register *****************/
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_W_MASK_Pos  (0U)
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_PTC1_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_PTC1_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_R_MASK_Pos  (1U)
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_PTC1_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_PTC1_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_PTC1_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_W_MASK_Pos  (2U)
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_PTC1_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_PTC1_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_R_MASK_Pos  (3U)
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_PTC1_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_PTC1_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_PTC1_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_W_MASK_Pos  (4U)
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_TRNG_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_TRNG_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_R_MASK_Pos  (5U)
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_TRNG_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_TRNG_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_TRNG_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_W_MASK_Pos  (6U)
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_TRNG_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_TRNG_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_R_MASK_Pos  (7U)
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_TRNG_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_TRNG_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_TRNG_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_W_MASK_Pos  (8U)
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_R_MASK_Pos  (9U)
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_EFUSE_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_W_MASK_Pos  (10U)
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_EFUSE_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_EFUSE_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_R_MASK_Pos  (11U)
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_EFUSE_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_EFUSE_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_EFUSE_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_W_MASK_Pos  (12U)
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_AES_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_AES_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_R_MASK_Pos  (13U)
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_AES_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_AES_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_AES_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_AES_SEC_W_MASK_Pos  (14U)
#define SECU1_IRQ_CFG0_ILL_AES_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_AES_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_AES_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_AES_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_AES_SEC_R_MASK_Pos  (15U)
#define SECU1_IRQ_CFG0_ILL_AES_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_AES_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_AES_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_AES_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_W_MASK_Pos  (16U)
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_R_MASK_Pos  (17U)
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_DMAC1_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_W_MASK_Pos  (18U)
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_DMAC1_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_DMAC1_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_R_MASK_Pos  (19U)
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_DMAC1_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_DMAC1_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_DMAC1_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_W_MASK_Pos  (20U)
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_SECU1_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_W_MASK  SECU1_IRQ_CFG0_ILL_SECU1_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_R_MASK_Pos  (21U)
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_SECU1_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_SECU1_PRIV_R_MASK  SECU1_IRQ_CFG0_ILL_SECU1_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_W_MASK_Pos  (22U)
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_SECU1_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_W_MASK  SECU1_IRQ_CFG0_ILL_SECU1_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_R_MASK_Pos  (23U)
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG0_ILL_SECU1_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG0_ILL_SECU1_SEC_R_MASK  SECU1_IRQ_CFG0_ILL_SECU1_SEC_R_MASK_Msk

/***************** Bit definition for SECU1_IRQ_CFG1 register *****************/
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_W_MASK_Pos  (0U)
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI1_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_W_MASK  SECU1_IRQ_CFG1_ILL_MPI1_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_R_MASK_Pos  (1U)
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI1_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI1_PRIV_R_MASK  SECU1_IRQ_CFG1_ILL_MPI1_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_W_MASK_Pos  (2U)
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI1_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_W_MASK  SECU1_IRQ_CFG1_ILL_MPI1_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_R_MASK_Pos  (3U)
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI1_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI1_SEC_R_MASK  SECU1_IRQ_CFG1_ILL_MPI1_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_W_MASK_Pos  (4U)
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI2_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_W_MASK  SECU1_IRQ_CFG1_ILL_MPI2_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_R_MASK_Pos  (5U)
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI2_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI2_PRIV_R_MASK  SECU1_IRQ_CFG1_ILL_MPI2_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_W_MASK_Pos  (6U)
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI2_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_W_MASK  SECU1_IRQ_CFG1_ILL_MPI2_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_R_MASK_Pos  (7U)
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_MPI2_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_MPI2_SEC_R_MASK  SECU1_IRQ_CFG1_ILL_MPI2_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_W_MASK_Pos  (8U)
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM0_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_W_MASK  SECU1_IRQ_CFG1_ILL_RAM0_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_R_MASK_Pos  (9U)
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM0_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM0_PRIV_R_MASK  SECU1_IRQ_CFG1_ILL_RAM0_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_W_MASK_Pos  (10U)
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM0_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_W_MASK  SECU1_IRQ_CFG1_ILL_RAM0_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_R_MASK_Pos  (11U)
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM0_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM0_SEC_R_MASK  SECU1_IRQ_CFG1_ILL_RAM0_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_W_MASK_Pos  (12U)
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM1_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_W_MASK  SECU1_IRQ_CFG1_ILL_RAM1_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_R_MASK_Pos  (13U)
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM1_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM1_PRIV_R_MASK  SECU1_IRQ_CFG1_ILL_RAM1_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_W_MASK_Pos  (14U)
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM1_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_W_MASK  SECU1_IRQ_CFG1_ILL_RAM1_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_R_MASK_Pos  (15U)
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM1_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM1_SEC_R_MASK  SECU1_IRQ_CFG1_ILL_RAM1_SEC_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_W_MASK_Pos  (16U)
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM2_PRIV_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_W_MASK  SECU1_IRQ_CFG1_ILL_RAM2_PRIV_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_R_MASK_Pos  (17U)
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM2_PRIV_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM2_PRIV_R_MASK  SECU1_IRQ_CFG1_ILL_RAM2_PRIV_R_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_W_MASK_Pos  (18U)
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_W_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM2_SEC_W_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_W_MASK  SECU1_IRQ_CFG1_ILL_RAM2_SEC_W_MASK_Msk
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_R_MASK_Pos  (19U)
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_R_MASK_Msk  (0x1UL << SECU1_IRQ_CFG1_ILL_RAM2_SEC_R_MASK_Pos)
#define SECU1_IRQ_CFG1_ILL_RAM2_SEC_R_MASK  SECU1_IRQ_CFG1_ILL_RAM2_SEC_R_MASK_Msk

/************** Bit definition for SECU1_HPMST_ATTR_CFG register **************/
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC_Pos  (0U)
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_HCPU_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC   SECU1_HPMST_ATTR_CFG_HCPU_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC_USE_Pos  (2U)
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_HCPU_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_HCPU_SEC_USE  SECU1_HPMST_ATTR_CFG_HCPU_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_Pos  (4U)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC  SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_Pos  (5U)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV  SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_USE_Pos  (6U)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_USE  SECU1_HPMST_ATTR_CFG_EXTDMA_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_USE_Pos  (7U)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_USE  SECU1_HPMST_ATTR_CFG_EXTDMA_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC_Pos  (8U)
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EPIC_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC   SECU1_HPMST_ATTR_CFG_EPIC_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV_Pos  (9U)
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EPIC_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV  SECU1_HPMST_ATTR_CFG_EPIC_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC_USE_Pos  (10U)
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EPIC_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_EPIC_SEC_USE  SECU1_HPMST_ATTR_CFG_EPIC_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV_USE_Pos  (11U)
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_EPIC_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_EPIC_PRIV_USE  SECU1_HPMST_ATTR_CFG_EPIC_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC_Pos  (12U)
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_LCDC_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC   SECU1_HPMST_ATTR_CFG_LCDC_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV_Pos  (13U)
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_LCDC_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV  SECU1_HPMST_ATTR_CFG_LCDC_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC_USE_Pos  (14U)
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_LCDC_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_LCDC_SEC_USE  SECU1_HPMST_ATTR_CFG_LCDC_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV_USE_Pos  (15U)
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_LCDC_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_LCDC_PRIV_USE  SECU1_HPMST_ATTR_CFG_LCDC_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_AES_SEC_Pos  (16U)
#define SECU1_HPMST_ATTR_CFG_AES_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_AES_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_AES_SEC    SECU1_HPMST_ATTR_CFG_AES_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_AES_PRIV_Pos  (17U)
#define SECU1_HPMST_ATTR_CFG_AES_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_AES_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_AES_PRIV   SECU1_HPMST_ATTR_CFG_AES_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_AES_SEC_USE_Pos  (18U)
#define SECU1_HPMST_ATTR_CFG_AES_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_AES_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_AES_SEC_USE  SECU1_HPMST_ATTR_CFG_AES_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_AES_PRIV_USE_Pos  (19U)
#define SECU1_HPMST_ATTR_CFG_AES_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_AES_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_AES_PRIV_USE  SECU1_HPMST_ATTR_CFG_AES_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_USB_SEC_Pos  (20U)
#define SECU1_HPMST_ATTR_CFG_USB_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_USB_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_USB_SEC    SECU1_HPMST_ATTR_CFG_USB_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_USB_PRIV_Pos  (21U)
#define SECU1_HPMST_ATTR_CFG_USB_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_USB_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_USB_PRIV   SECU1_HPMST_ATTR_CFG_USB_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_USB_SEC_USE_Pos  (22U)
#define SECU1_HPMST_ATTR_CFG_USB_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_USB_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_USB_SEC_USE  SECU1_HPMST_ATTR_CFG_USB_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_USB_PRIV_USE_Pos  (23U)
#define SECU1_HPMST_ATTR_CFG_USB_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_USB_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_USB_PRIV_USE  SECU1_HPMST_ATTR_CFG_USB_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC_Pos  (24U)
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_DMAC1_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC  SECU1_HPMST_ATTR_CFG_DMAC1_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_Pos  (25U)
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV  SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC_USE_Pos  (26U)
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_DMAC1_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_DMAC1_SEC_USE  SECU1_HPMST_ATTR_CFG_DMAC1_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_USE_Pos  (27U)
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_USE  SECU1_HPMST_ATTR_CFG_DMAC1_PRIV_USE_Msk
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC_Pos  (28U)
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_PTC1_SEC_Pos)
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC   SECU1_HPMST_ATTR_CFG_PTC1_SEC_Msk
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV_Pos  (29U)
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_PTC1_PRIV_Pos)
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV  SECU1_HPMST_ATTR_CFG_PTC1_PRIV_Msk
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC_USE_Pos  (30U)
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_PTC1_SEC_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_PTC1_SEC_USE  SECU1_HPMST_ATTR_CFG_PTC1_SEC_USE_Msk
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV_USE_Pos  (31U)
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV_USE_Msk  (0x1UL << SECU1_HPMST_ATTR_CFG_PTC1_PRIV_USE_Pos)
#define SECU1_HPMST_ATTR_CFG_PTC1_PRIV_USE  SECU1_HPMST_ATTR_CFG_PTC1_PRIV_USE_Msk

/************** Bit definition for SECU1_HPSLV_ATTR_CFG register **************/
#define SECU1_HPSLV_ATTR_CFG_DMAC1_SEC_Pos  (0U)
#define SECU1_HPSLV_ATTR_CFG_DMAC1_SEC_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_DMAC1_SEC_Pos)
#define SECU1_HPSLV_ATTR_CFG_DMAC1_SEC  SECU1_HPSLV_ATTR_CFG_DMAC1_SEC_Msk
#define SECU1_HPSLV_ATTR_CFG_DMAC1_PRIV_Pos  (1U)
#define SECU1_HPSLV_ATTR_CFG_DMAC1_PRIV_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_DMAC1_PRIV_Pos)
#define SECU1_HPSLV_ATTR_CFG_DMAC1_PRIV  SECU1_HPSLV_ATTR_CFG_DMAC1_PRIV_Msk
#define SECU1_HPSLV_ATTR_CFG_AES_SEC_Pos  (2U)
#define SECU1_HPSLV_ATTR_CFG_AES_SEC_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_AES_SEC_Pos)
#define SECU1_HPSLV_ATTR_CFG_AES_SEC    SECU1_HPSLV_ATTR_CFG_AES_SEC_Msk
#define SECU1_HPSLV_ATTR_CFG_AES_PRIV_Pos  (3U)
#define SECU1_HPSLV_ATTR_CFG_AES_PRIV_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_AES_PRIV_Pos)
#define SECU1_HPSLV_ATTR_CFG_AES_PRIV   SECU1_HPSLV_ATTR_CFG_AES_PRIV_Msk
#define SECU1_HPSLV_ATTR_CFG_EFUSE_SEC_Pos  (4U)
#define SECU1_HPSLV_ATTR_CFG_EFUSE_SEC_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_EFUSE_SEC_Pos)
#define SECU1_HPSLV_ATTR_CFG_EFUSE_SEC  SECU1_HPSLV_ATTR_CFG_EFUSE_SEC_Msk
#define SECU1_HPSLV_ATTR_CFG_EFUSE_PRIV_Pos  (5U)
#define SECU1_HPSLV_ATTR_CFG_EFUSE_PRIV_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_EFUSE_PRIV_Pos)
#define SECU1_HPSLV_ATTR_CFG_EFUSE_PRIV  SECU1_HPSLV_ATTR_CFG_EFUSE_PRIV_Msk
#define SECU1_HPSLV_ATTR_CFG_TRNG_SEC_Pos  (6U)
#define SECU1_HPSLV_ATTR_CFG_TRNG_SEC_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_TRNG_SEC_Pos)
#define SECU1_HPSLV_ATTR_CFG_TRNG_SEC   SECU1_HPSLV_ATTR_CFG_TRNG_SEC_Msk
#define SECU1_HPSLV_ATTR_CFG_TRNG_PRIV_Pos  (7U)
#define SECU1_HPSLV_ATTR_CFG_TRNG_PRIV_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_TRNG_PRIV_Pos)
#define SECU1_HPSLV_ATTR_CFG_TRNG_PRIV  SECU1_HPSLV_ATTR_CFG_TRNG_PRIV_Msk
#define SECU1_HPSLV_ATTR_CFG_PTC1_SEC_Pos  (8U)
#define SECU1_HPSLV_ATTR_CFG_PTC1_SEC_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_PTC1_SEC_Pos)
#define SECU1_HPSLV_ATTR_CFG_PTC1_SEC   SECU1_HPSLV_ATTR_CFG_PTC1_SEC_Msk
#define SECU1_HPSLV_ATTR_CFG_PTC1_PRIV_Pos  (9U)
#define SECU1_HPSLV_ATTR_CFG_PTC1_PRIV_Msk  (0x1UL << SECU1_HPSLV_ATTR_CFG_PTC1_PRIV_Pos)
#define SECU1_HPSLV_ATTR_CFG_PTC1_PRIV  SECU1_HPSLV_ATTR_CFG_PTC1_PRIV_Msk

/************** Bit definition for SECU1_MPI1_PRIV_CFG0 register **************/
#define SECU1_MPI1_PRIV_CFG0_ST_ADDR0_Pos  (10U)
#define SECU1_MPI1_PRIV_CFG0_ST_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI1_PRIV_CFG0_ST_ADDR0_Pos)
#define SECU1_MPI1_PRIV_CFG0_ST_ADDR0   SECU1_MPI1_PRIV_CFG0_ST_ADDR0_Msk

/************** Bit definition for SECU1_MPI1_PRIV_CFG1 register **************/
#define SECU1_MPI1_PRIV_CFG1_END_ADDR0_Pos  (10U)
#define SECU1_MPI1_PRIV_CFG1_END_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI1_PRIV_CFG1_END_ADDR0_Pos)
#define SECU1_MPI1_PRIV_CFG1_END_ADDR0  SECU1_MPI1_PRIV_CFG1_END_ADDR0_Msk

/************** Bit definition for SECU1_MPI1_PRIV_CFG2 register **************/
#define SECU1_MPI1_PRIV_CFG2_ST_ADDR1_Pos  (10U)
#define SECU1_MPI1_PRIV_CFG2_ST_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI1_PRIV_CFG2_ST_ADDR1_Pos)
#define SECU1_MPI1_PRIV_CFG2_ST_ADDR1   SECU1_MPI1_PRIV_CFG2_ST_ADDR1_Msk

/************** Bit definition for SECU1_MPI1_PRIV_CFG3 register **************/
#define SECU1_MPI1_PRIV_CFG3_END_ADDR1_Pos  (10U)
#define SECU1_MPI1_PRIV_CFG3_END_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI1_PRIV_CFG3_END_ADDR1_Pos)
#define SECU1_MPI1_PRIV_CFG3_END_ADDR1  SECU1_MPI1_PRIV_CFG3_END_ADDR1_Msk

/************** Bit definition for SECU1_MPI1_SEC_CFG0 register ***************/
#define SECU1_MPI1_SEC_CFG0_ST_ADDR0_Pos  (10U)
#define SECU1_MPI1_SEC_CFG0_ST_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI1_SEC_CFG0_ST_ADDR0_Pos)
#define SECU1_MPI1_SEC_CFG0_ST_ADDR0    SECU1_MPI1_SEC_CFG0_ST_ADDR0_Msk

/************** Bit definition for SECU1_MPI1_SEC_CFG1 register ***************/
#define SECU1_MPI1_SEC_CFG1_END_ADDR0_Pos  (10U)
#define SECU1_MPI1_SEC_CFG1_END_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI1_SEC_CFG1_END_ADDR0_Pos)
#define SECU1_MPI1_SEC_CFG1_END_ADDR0   SECU1_MPI1_SEC_CFG1_END_ADDR0_Msk

/************** Bit definition for SECU1_MPI1_SEC_CFG2 register ***************/
#define SECU1_MPI1_SEC_CFG2_ST_ADDR1_Pos  (10U)
#define SECU1_MPI1_SEC_CFG2_ST_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI1_SEC_CFG2_ST_ADDR1_Pos)
#define SECU1_MPI1_SEC_CFG2_ST_ADDR1    SECU1_MPI1_SEC_CFG2_ST_ADDR1_Msk

/************** Bit definition for SECU1_MPI1_SEC_CFG3 register ***************/
#define SECU1_MPI1_SEC_CFG3_END_ADDR1_Pos  (10U)
#define SECU1_MPI1_SEC_CFG3_END_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI1_SEC_CFG3_END_ADDR1_Pos)
#define SECU1_MPI1_SEC_CFG3_END_ADDR1   SECU1_MPI1_SEC_CFG3_END_ADDR1_Msk

/************** Bit definition for SECU1_MPI2_PRIV_CFG0 register **************/
#define SECU1_MPI2_PRIV_CFG0_ST_ADDR0_Pos  (10U)
#define SECU1_MPI2_PRIV_CFG0_ST_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI2_PRIV_CFG0_ST_ADDR0_Pos)
#define SECU1_MPI2_PRIV_CFG0_ST_ADDR0   SECU1_MPI2_PRIV_CFG0_ST_ADDR0_Msk

/************** Bit definition for SECU1_MPI2_PRIV_CFG1 register **************/
#define SECU1_MPI2_PRIV_CFG1_END_ADDR0_Pos  (10U)
#define SECU1_MPI2_PRIV_CFG1_END_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI2_PRIV_CFG1_END_ADDR0_Pos)
#define SECU1_MPI2_PRIV_CFG1_END_ADDR0  SECU1_MPI2_PRIV_CFG1_END_ADDR0_Msk

/************** Bit definition for SECU1_MPI2_PRIV_CFG2 register **************/
#define SECU1_MPI2_PRIV_CFG2_ST_ADDR1_Pos  (10U)
#define SECU1_MPI2_PRIV_CFG2_ST_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI2_PRIV_CFG2_ST_ADDR1_Pos)
#define SECU1_MPI2_PRIV_CFG2_ST_ADDR1   SECU1_MPI2_PRIV_CFG2_ST_ADDR1_Msk

/************** Bit definition for SECU1_MPI2_PRIV_CFG3 register **************/
#define SECU1_MPI2_PRIV_CFG3_END_ADDR1_Pos  (10U)
#define SECU1_MPI2_PRIV_CFG3_END_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI2_PRIV_CFG3_END_ADDR1_Pos)
#define SECU1_MPI2_PRIV_CFG3_END_ADDR1  SECU1_MPI2_PRIV_CFG3_END_ADDR1_Msk

/************** Bit definition for SECU1_MPI2_SEC_CFG0 register ***************/
#define SECU1_MPI2_SEC_CFG0_ST_ADDR0_Pos  (10U)
#define SECU1_MPI2_SEC_CFG0_ST_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI2_SEC_CFG0_ST_ADDR0_Pos)
#define SECU1_MPI2_SEC_CFG0_ST_ADDR0    SECU1_MPI2_SEC_CFG0_ST_ADDR0_Msk

/************** Bit definition for SECU1_MPI2_SEC_CFG1 register ***************/
#define SECU1_MPI2_SEC_CFG1_END_ADDR0_Pos  (10U)
#define SECU1_MPI2_SEC_CFG1_END_ADDR0_Msk  (0x3FFFFFUL << SECU1_MPI2_SEC_CFG1_END_ADDR0_Pos)
#define SECU1_MPI2_SEC_CFG1_END_ADDR0   SECU1_MPI2_SEC_CFG1_END_ADDR0_Msk

/************** Bit definition for SECU1_MPI2_SEC_CFG2 register ***************/
#define SECU1_MPI2_SEC_CFG2_ST_ADDR1_Pos  (10U)
#define SECU1_MPI2_SEC_CFG2_ST_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI2_SEC_CFG2_ST_ADDR1_Pos)
#define SECU1_MPI2_SEC_CFG2_ST_ADDR1    SECU1_MPI2_SEC_CFG2_ST_ADDR1_Msk

/************** Bit definition for SECU1_MPI2_SEC_CFG3 register ***************/
#define SECU1_MPI2_SEC_CFG3_END_ADDR1_Pos  (10U)
#define SECU1_MPI2_SEC_CFG3_END_ADDR1_Msk  (0x3FFFFFUL << SECU1_MPI2_SEC_CFG3_END_ADDR1_Pos)
#define SECU1_MPI2_SEC_CFG3_END_ADDR1   SECU1_MPI2_SEC_CFG3_END_ADDR1_Msk

/************** Bit definition for SECU1_RAM0_PRIV_CFG0 register **************/
#define SECU1_RAM0_PRIV_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM0_PRIV_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM0_PRIV_CFG0_ST_ADDR_Pos)
#define SECU1_RAM0_PRIV_CFG0_ST_ADDR    SECU1_RAM0_PRIV_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM0_PRIV_CFG1 register **************/
#define SECU1_RAM0_PRIV_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM0_PRIV_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM0_PRIV_CFG1_END_ADDR_Pos)
#define SECU1_RAM0_PRIV_CFG1_END_ADDR   SECU1_RAM0_PRIV_CFG1_END_ADDR_Msk

/************** Bit definition for SECU1_RAM0_SEC_CFG0 register ***************/
#define SECU1_RAM0_SEC_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM0_SEC_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM0_SEC_CFG0_ST_ADDR_Pos)
#define SECU1_RAM0_SEC_CFG0_ST_ADDR     SECU1_RAM0_SEC_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM0_SEC_CFG1 register ***************/
#define SECU1_RAM0_SEC_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM0_SEC_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM0_SEC_CFG1_END_ADDR_Pos)
#define SECU1_RAM0_SEC_CFG1_END_ADDR    SECU1_RAM0_SEC_CFG1_END_ADDR_Msk

/************** Bit definition for SECU1_RAM1_PRIV_CFG0 register **************/
#define SECU1_RAM1_PRIV_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM1_PRIV_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM1_PRIV_CFG0_ST_ADDR_Pos)
#define SECU1_RAM1_PRIV_CFG0_ST_ADDR    SECU1_RAM1_PRIV_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM1_PRIV_CFG1 register **************/
#define SECU1_RAM1_PRIV_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM1_PRIV_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM1_PRIV_CFG1_END_ADDR_Pos)
#define SECU1_RAM1_PRIV_CFG1_END_ADDR   SECU1_RAM1_PRIV_CFG1_END_ADDR_Msk

/************** Bit definition for SECU1_RAM1_SEC_CFG0 register ***************/
#define SECU1_RAM1_SEC_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM1_SEC_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM1_SEC_CFG0_ST_ADDR_Pos)
#define SECU1_RAM1_SEC_CFG0_ST_ADDR     SECU1_RAM1_SEC_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM1_SEC_CFG1 register ***************/
#define SECU1_RAM1_SEC_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM1_SEC_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM1_SEC_CFG1_END_ADDR_Pos)
#define SECU1_RAM1_SEC_CFG1_END_ADDR    SECU1_RAM1_SEC_CFG1_END_ADDR_Msk

/************** Bit definition for SECU1_RAM2_PRIV_CFG0 register **************/
#define SECU1_RAM2_PRIV_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM2_PRIV_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM2_PRIV_CFG0_ST_ADDR_Pos)
#define SECU1_RAM2_PRIV_CFG0_ST_ADDR    SECU1_RAM2_PRIV_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM2_PRIV_CFG1 register **************/
#define SECU1_RAM2_PRIV_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM2_PRIV_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM2_PRIV_CFG1_END_ADDR_Pos)
#define SECU1_RAM2_PRIV_CFG1_END_ADDR   SECU1_RAM2_PRIV_CFG1_END_ADDR_Msk

/************** Bit definition for SECU1_RAM2_SEC_CFG0 register ***************/
#define SECU1_RAM2_SEC_CFG0_ST_ADDR_Pos  (10U)
#define SECU1_RAM2_SEC_CFG0_ST_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM2_SEC_CFG0_ST_ADDR_Pos)
#define SECU1_RAM2_SEC_CFG0_ST_ADDR     SECU1_RAM2_SEC_CFG0_ST_ADDR_Msk

/************** Bit definition for SECU1_RAM2_SEC_CFG1 register ***************/
#define SECU1_RAM2_SEC_CFG1_END_ADDR_Pos  (10U)
#define SECU1_RAM2_SEC_CFG1_END_ADDR_Msk  (0x3FFFFFUL << SECU1_RAM2_SEC_CFG1_END_ADDR_Pos)
#define SECU1_RAM2_SEC_CFG1_END_ADDR    SECU1_RAM2_SEC_CFG1_END_ADDR_Msk

#endif
