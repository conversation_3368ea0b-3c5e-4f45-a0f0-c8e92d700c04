#ifndef __DSI_PHY_H
#define __DSI_PHY_H

typedef struct
{
    __IO uint32_t PHY_CFG;
    __IO uint32_t CK_LANE_CFG0;
    __IO uint32_t CK_LANE_CFG1;
    __IO uint32_t CK_LANE_CFG2;
    __IO uint32_t CK_LANE_CTRL;
    __IO uint32_t CK_LANE_TEST_CTRL;
    __IO uint32_t CK_LANE_STAT;
    __IO uint32_t D_LANE0_CFG0;
    __IO uint32_t D_LANE0_CFG1;
    __IO uint32_t D_LANE0_CTRL;
    __IO uint32_t D_LANE0_TEST_CTRL;
    __IO uint32_t D_LANE0_STAT;
    __IO uint32_t D_LANE1_CFG0;
    __IO uint32_t D_LANE1_CFG1;
    __IO uint32_t D_LANE1_CTRL;
    __IO uint32_t D_LANE1_TEST_CTRL;
    __IO uint32_t D_LANE1_STAT;
    __IO uint32_t DLL_CFG;
    __IO uint32_t DLL_CTRL;
    __IO uint32_t DLL_TEST;
    __IO uint32_t DSI_ANA_CTRL;
    __IO uint32_t BIAS_CTRL;
    __IO uint32_t BIAS_CAL;
    __IO uint32_t LOOPBACK_CFG;
    __IO uint32_t MISC_CFG;
    __IO uint32_t RESERVED_IN;
    __IO uint32_t RESERVED_OUT;
} DSI_PHY_TypeDef;


/**************** Bit definition for DSI_PHY_PHY_CFG register *****************/
#define DSI_PHY_PHY_CFG_RESETN_Pos      (0U)
#define DSI_PHY_PHY_CFG_RESETN_Msk      (0x1UL << DSI_PHY_PHY_CFG_RESETN_Pos)
#define DSI_PHY_PHY_CFG_RESETN          DSI_PHY_PHY_CFG_RESETN_Msk
#define DSI_PHY_PHY_CFG_ENABLE_Pos      (1U)
#define DSI_PHY_PHY_CFG_ENABLE_Msk      (0x1UL << DSI_PHY_PHY_CFG_ENABLE_Pos)
#define DSI_PHY_PHY_CFG_ENABLE          DSI_PHY_PHY_CFG_ENABLE_Msk
#define DSI_PHY_PHY_CFG_INIT_CNT_Pos    (2U)
#define DSI_PHY_PHY_CFG_INIT_CNT_Msk    (0xFFFFUL << DSI_PHY_PHY_CFG_INIT_CNT_Pos)
#define DSI_PHY_PHY_CFG_INIT_CNT        DSI_PHY_PHY_CFG_INIT_CNT_Msk

/************** Bit definition for DSI_PHY_CK_LANE_CFG0 register **************/
#define DSI_PHY_CK_LANE_CFG0_PHASE_SEL_Pos  (0U)
#define DSI_PHY_CK_LANE_CFG0_PHASE_SEL_Msk  (0x3UL << DSI_PHY_CK_LANE_CFG0_PHASE_SEL_Pos)
#define DSI_PHY_CK_LANE_CFG0_PHASE_SEL  DSI_PHY_CK_LANE_CFG0_PHASE_SEL_Msk
#define DSI_PHY_CK_LANE_CFG0_TLPX_CNT_Pos  (8U)
#define DSI_PHY_CK_LANE_CFG0_TLPX_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG0_TLPX_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG0_TLPX_CNT   DSI_PHY_CK_LANE_CFG0_TLPX_CNT_Msk
#define DSI_PHY_CK_LANE_CFG0_TCLKPREP_CNT_Pos  (16U)
#define DSI_PHY_CK_LANE_CFG0_TCLKPREP_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG0_TCLKPREP_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG0_TCLKPREP_CNT  DSI_PHY_CK_LANE_CFG0_TCLKPREP_CNT_Msk
#define DSI_PHY_CK_LANE_CFG0_TCLKZERO_CNT_Pos  (24U)
#define DSI_PHY_CK_LANE_CFG0_TCLKZERO_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG0_TCLKZERO_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG0_TCLKZERO_CNT  DSI_PHY_CK_LANE_CFG0_TCLKZERO_CNT_Msk

/************** Bit definition for DSI_PHY_CK_LANE_CFG1 register **************/
#define DSI_PHY_CK_LANE_CFG1_TCLKPRE_CNT_Pos  (0U)
#define DSI_PHY_CK_LANE_CFG1_TCLKPRE_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG1_TCLKPRE_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG1_TCLKPRE_CNT  DSI_PHY_CK_LANE_CFG1_TCLKPRE_CNT_Msk
#define DSI_PHY_CK_LANE_CFG1_TCLKPOST_CNT_Pos  (8U)
#define DSI_PHY_CK_LANE_CFG1_TCLKPOST_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG1_TCLKPOST_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG1_TCLKPOST_CNT  DSI_PHY_CK_LANE_CFG1_TCLKPOST_CNT_Msk
#define DSI_PHY_CK_LANE_CFG1_TCLKTRAIL_CNT_Pos  (16U)
#define DSI_PHY_CK_LANE_CFG1_TCLKTRAIL_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG1_TCLKTRAIL_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG1_TCLKTRAIL_CNT  DSI_PHY_CK_LANE_CFG1_TCLKTRAIL_CNT_Msk
#define DSI_PHY_CK_LANE_CFG1_THSEXIT_CNT_Pos  (24U)
#define DSI_PHY_CK_LANE_CFG1_THSEXIT_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG1_THSEXIT_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG1_THSEXIT_CNT  DSI_PHY_CK_LANE_CFG1_THSEXIT_CNT_Msk

/************** Bit definition for DSI_PHY_CK_LANE_CFG2 register **************/
#define DSI_PHY_CK_LANE_CFG2_TWAKEUP_CNT_Pos  (0U)
#define DSI_PHY_CK_LANE_CFG2_TWAKEUP_CNT_Msk  (0xFFUL << DSI_PHY_CK_LANE_CFG2_TWAKEUP_CNT_Pos)
#define DSI_PHY_CK_LANE_CFG2_TWAKEUP_CNT  DSI_PHY_CK_LANE_CFG2_TWAKEUP_CNT_Msk

/************** Bit definition for DSI_PHY_CK_LANE_CTRL register **************/
#define DSI_PHY_CK_LANE_CTRL_HSTXPON_Pos  (0U)
#define DSI_PHY_CK_LANE_CTRL_HSTXPON_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_HSTXPON_Pos)
#define DSI_PHY_CK_LANE_CTRL_HSTXPON    DSI_PHY_CK_LANE_CTRL_HSTXPON_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXPON_Pos  (1U)
#define DSI_PHY_CK_LANE_CTRL_LPTXPON_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXPON_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXPON    DSI_PHY_CK_LANE_CTRL_LPTXPON_Msk
#define DSI_PHY_CK_LANE_CTRL_LPRXPONLP_Pos  (2U)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONLP_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPRXPONLP_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONLP  DSI_PHY_CK_LANE_CTRL_LPRXPONLP_Msk
#define DSI_PHY_CK_LANE_CTRL_LPRXPONULP_Pos  (3U)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONULP_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPRXPONULP_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONULP  DSI_PHY_CK_LANE_CTRL_LPRXPONULP_Msk
#define DSI_PHY_CK_LANE_CTRL_LPRXPONCD_Pos  (4U)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONCD_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPRXPONCD_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPRXPONCD  DSI_PHY_CK_LANE_CTRL_LPRXPONCD_Msk
#define DSI_PHY_CK_LANE_CTRL_BIASPON_Pos  (5U)
#define DSI_PHY_CK_LANE_CTRL_BIASPON_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_BIASPON_Pos)
#define DSI_PHY_CK_LANE_CTRL_BIASPON    DSI_PHY_CK_LANE_CTRL_BIASPON_Msk
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_SET_Pos  (6U)
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_SET_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_HSTXDIN_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_SET  DSI_PHY_CK_LANE_CTRL_HSTXDIN_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_BYPASS_Pos  (7U)
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_HSTXDIN_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_HSTXDIN_BYPASS  DSI_PHY_CK_LANE_CTRL_HSTXDIN_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_SET_Pos  (8U)
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_SET_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_HSTXENA_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_SET  DSI_PHY_CK_LANE_CTRL_HSTXENA_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_BYPASS_Pos  (9U)
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_HSTXENA_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_HSTXENA_BYPASS  DSI_PHY_CK_LANE_CTRL_HSTXENA_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_SET_Pos  (10U)
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_SET_Msk  (0x3UL << DSI_PHY_CK_LANE_CTRL_LPTXDIN_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_SET  DSI_PHY_CK_LANE_CTRL_LPTXDIN_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_BYPASS_Pos  (12U)
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXDIN_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXDIN_BYPASS  DSI_PHY_CK_LANE_CTRL_LPTXDIN_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_SET_Pos  (13U)
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_SET_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXPD_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_SET  DSI_PHY_CK_LANE_CTRL_LPTXPD_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_BYPASS_Pos  (14U)
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXPD_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXPD_BYPASS  DSI_PHY_CK_LANE_CTRL_LPTXPD_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_SET_Pos  (15U)
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_SET_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXPU_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_SET  DSI_PHY_CK_LANE_CTRL_LPTXPU_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_BYPASS_Pos  (16U)
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXPU_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXPU_BYPASS  DSI_PHY_CK_LANE_CTRL_LPTXPU_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_SET_Pos  (17U)
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_SET_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXENA_SET_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_SET  DSI_PHY_CK_LANE_CTRL_LPTXENA_SET_Msk
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_BYPASS_Pos  (18U)
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_LPTXENA_BYPASS_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPTXENA_BYPASS  DSI_PHY_CK_LANE_CTRL_LPTXENA_BYPASS_Msk
#define DSI_PHY_CK_LANE_CTRL_LPRXULPDOUT_Pos  (19U)
#define DSI_PHY_CK_LANE_CTRL_LPRXULPDOUT_Msk  (0x3UL << DSI_PHY_CK_LANE_CTRL_LPRXULPDOUT_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPRXULPDOUT  DSI_PHY_CK_LANE_CTRL_LPRXULPDOUT_Msk
#define DSI_PHY_CK_LANE_CTRL_LPRXLPDOUT_Pos  (21U)
#define DSI_PHY_CK_LANE_CTRL_LPRXLPDOUT_Msk  (0x3UL << DSI_PHY_CK_LANE_CTRL_LPRXLPDOUT_Pos)
#define DSI_PHY_CK_LANE_CTRL_LPRXLPDOUT  DSI_PHY_CK_LANE_CTRL_LPRXLPDOUT_Msk
#define DSI_PHY_CK_LANE_CTRL_CDDOUT_Pos  (23U)
#define DSI_PHY_CK_LANE_CTRL_CDDOUT_Msk  (0x3UL << DSI_PHY_CK_LANE_CTRL_CDDOUT_Pos)
#define DSI_PHY_CK_LANE_CTRL_CDDOUT     DSI_PHY_CK_LANE_CTRL_CDDOUT_Msk
#define DSI_PHY_CK_LANE_CTRL_PN_SWAP_Pos  (25U)
#define DSI_PHY_CK_LANE_CTRL_PN_SWAP_Msk  (0x1UL << DSI_PHY_CK_LANE_CTRL_PN_SWAP_Pos)
#define DSI_PHY_CK_LANE_CTRL_PN_SWAP    DSI_PHY_CK_LANE_CTRL_PN_SWAP_Msk

/*********** Bit definition for DSI_PHY_CK_LANE_TEST_CTRL register ************/
#define DSI_PHY_CK_LANE_TEST_CTRL_SYN_OUT_ENB_Pos  (0U)
#define DSI_PHY_CK_LANE_TEST_CTRL_SYN_OUT_ENB_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_SYN_OUT_ENB_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_SYN_OUT_ENB  DSI_PHY_CK_LANE_TEST_CTRL_SYN_OUT_ENB_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_OS_SET_Pos  (1U)
#define DSI_PHY_CK_LANE_TEST_CTRL_OS_SET_Msk  (0x1FUL << DSI_PHY_CK_LANE_TEST_CTRL_OS_SET_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_OS_SET  DSI_PHY_CK_LANE_TEST_CTRL_OS_SET_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_CAL_EN_Pos  (6U)
#define DSI_PHY_CK_LANE_TEST_CTRL_CAL_EN_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_CAL_EN_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_CAL_EN  DSI_PHY_CK_LANE_TEST_CTRL_CAL_EN_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_INMATCH_EN_Pos  (7U)
#define DSI_PHY_CK_LANE_TEST_CTRL_INMATCH_EN_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_INMATCH_EN_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_INMATCH_EN  DSI_PHY_CK_LANE_TEST_CTRL_INMATCH_EN_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LBMODE_EN_Pos  (8U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LBMODE_EN_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LBMODE_EN_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LBMODE_EN  DSI_PHY_CK_LANE_TEST_CTRL_LBMODE_EN_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_HSRXPON_Pos  (9U)
#define DSI_PHY_CK_LANE_TEST_CTRL_HSRXPON_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_HSRXPON_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_HSRXPON  DSI_PHY_CK_LANE_TEST_CTRL_HSRXPON_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_HS_TEST_STAT_Pos  (10U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_HS_TEST_STAT_Msk  (0x3UL << DSI_PHY_CK_LANE_TEST_CTRL_LB_HS_TEST_STAT_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_HS_TEST_STAT  DSI_PHY_CK_LANE_TEST_CTRL_LB_HS_TEST_STAT_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_LP_TEST_STAT_Pos  (12U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_LP_TEST_STAT_Msk  (0x3UL << DSI_PHY_CK_LANE_TEST_CTRL_LB_LP_TEST_STAT_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_LP_TEST_STAT  DSI_PHY_CK_LANE_TEST_CTRL_LB_LP_TEST_STAT_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_DONE_Pos  (14U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_DONE_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LB_DONE_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_DONE  DSI_PHY_CK_LANE_TEST_CTRL_LB_DONE_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_BUSY_Pos  (15U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_BUSY_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LB_BUSY_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LB_BUSY  DSI_PHY_CK_LANE_TEST_CTRL_LB_BUSY_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_RX_CLK_POL_Pos  (16U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LP_RX_CLK_POL_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_RX_CLK_POL  DSI_PHY_CK_LANE_TEST_CTRL_LP_RX_CLK_POL_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_RX_CLK_POL_Pos  (17U)
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_HS_RX_CLK_POL_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_RX_CLK_POL  DSI_PHY_CK_LANE_TEST_CTRL_HS_RX_CLK_POL_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_DLY_SEL_Pos  (18U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_DLY_SEL_Msk  (0xFUL << DSI_PHY_CK_LANE_TEST_CTRL_LP_DLY_SEL_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LP_DLY_SEL  DSI_PHY_CK_LANE_TEST_CTRL_LP_DLY_SEL_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_DLY_SEL_Pos  (22U)
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_DLY_SEL_Msk  (0x3FUL << DSI_PHY_CK_LANE_TEST_CTRL_HS_DLY_SEL_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_HS_DLY_SEL  DSI_PHY_CK_LANE_TEST_CTRL_HS_DLY_SEL_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LPRX_MODE_SEL_Pos  (28U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LPRX_MODE_SEL_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LPRX_MODE_SEL_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LPRX_MODE_SEL  DSI_PHY_CK_LANE_TEST_CTRL_LPRX_MODE_SEL_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_MODE_Pos  (29U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_MODE_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_MODE_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_MODE  DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_MODE_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_EN_Pos  (30U)
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_EN_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_EN_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_EN  DSI_PHY_CK_LANE_TEST_CTRL_LOOPBACK_EN_Msk
#define DSI_PHY_CK_LANE_TEST_CTRL_RUN_LB_TEST_Pos  (31U)
#define DSI_PHY_CK_LANE_TEST_CTRL_RUN_LB_TEST_Msk  (0x1UL << DSI_PHY_CK_LANE_TEST_CTRL_RUN_LB_TEST_Pos)
#define DSI_PHY_CK_LANE_TEST_CTRL_RUN_LB_TEST  DSI_PHY_CK_LANE_TEST_CTRL_RUN_LB_TEST_Msk

/************** Bit definition for DSI_PHY_CK_LANE_STAT register **************/
#define DSI_PHY_CK_LANE_STAT_ASY_DOUT_Pos  (0U)
#define DSI_PHY_CK_LANE_STAT_ASY_DOUT_Msk  (0x3UL << DSI_PHY_CK_LANE_STAT_ASY_DOUT_Pos)
#define DSI_PHY_CK_LANE_STAT_ASY_DOUT   DSI_PHY_CK_LANE_STAT_ASY_DOUT_Msk
#define DSI_PHY_CK_LANE_STAT_ASY_CLKOUT_Pos  (2U)
#define DSI_PHY_CK_LANE_STAT_ASY_CLKOUT_Msk  (0x3UL << DSI_PHY_CK_LANE_STAT_ASY_CLKOUT_Pos)
#define DSI_PHY_CK_LANE_STAT_ASY_CLKOUT  DSI_PHY_CK_LANE_STAT_ASY_CLKOUT_Msk

/************** Bit definition for DSI_PHY_D_LANE0_CFG0 register **************/
#define DSI_PHY_D_LANE0_CFG0_TLPX_CNT_Pos  (0U)
#define DSI_PHY_D_LANE0_CFG0_TLPX_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG0_TLPX_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG0_TLPX_CNT   DSI_PHY_D_LANE0_CFG0_TLPX_CNT_Msk
#define DSI_PHY_D_LANE0_CFG0_THSPREP_CNT_Pos  (8U)
#define DSI_PHY_D_LANE0_CFG0_THSPREP_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG0_THSPREP_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG0_THSPREP_CNT  DSI_PHY_D_LANE0_CFG0_THSPREP_CNT_Msk
#define DSI_PHY_D_LANE0_CFG0_THSZERO_CNT_Pos  (16U)
#define DSI_PHY_D_LANE0_CFG0_THSZERO_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG0_THSZERO_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG0_THSZERO_CNT  DSI_PHY_D_LANE0_CFG0_THSZERO_CNT_Msk
#define DSI_PHY_D_LANE0_CFG0_THSTRAIL_CNT_Pos  (24U)
#define DSI_PHY_D_LANE0_CFG0_THSTRAIL_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG0_THSTRAIL_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG0_THSTRAIL_CNT  DSI_PHY_D_LANE0_CFG0_THSTRAIL_CNT_Msk

/************** Bit definition for DSI_PHY_D_LANE0_CFG1 register **************/
#define DSI_PHY_D_LANE0_CFG1_TTASURE_CNT_Pos  (0U)
#define DSI_PHY_D_LANE0_CFG1_TTASURE_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG1_TTASURE_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG1_TTASURE_CNT  DSI_PHY_D_LANE0_CFG1_TTASURE_CNT_Msk
#define DSI_PHY_D_LANE0_CFG1_TTAGET_CNT_Pos  (8U)
#define DSI_PHY_D_LANE0_CFG1_TTAGET_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG1_TTAGET_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG1_TTAGET_CNT  DSI_PHY_D_LANE0_CFG1_TTAGET_CNT_Msk
#define DSI_PHY_D_LANE0_CFG1_TTAGO_CNT_Pos  (16U)
#define DSI_PHY_D_LANE0_CFG1_TTAGO_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG1_TTAGO_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG1_TTAGO_CNT  DSI_PHY_D_LANE0_CFG1_TTAGO_CNT_Msk
#define DSI_PHY_D_LANE0_CFG1_TWAKEUP_CNT_Pos  (24U)
#define DSI_PHY_D_LANE0_CFG1_TWAKEUP_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE0_CFG1_TWAKEUP_CNT_Pos)
#define DSI_PHY_D_LANE0_CFG1_TWAKEUP_CNT  DSI_PHY_D_LANE0_CFG1_TWAKEUP_CNT_Msk

/************** Bit definition for DSI_PHY_D_LANE0_CTRL register **************/
#define DSI_PHY_D_LANE0_CTRL_HSTXPON_Pos  (0U)
#define DSI_PHY_D_LANE0_CTRL_HSTXPON_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_HSTXPON_Pos)
#define DSI_PHY_D_LANE0_CTRL_HSTXPON    DSI_PHY_D_LANE0_CTRL_HSTXPON_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXPON_Pos  (1U)
#define DSI_PHY_D_LANE0_CTRL_LPTXPON_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXPON_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXPON    DSI_PHY_D_LANE0_CTRL_LPTXPON_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRXPONLP_Pos  (2U)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONLP_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPRXPONLP_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONLP  DSI_PHY_D_LANE0_CTRL_LPRXPONLP_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRXPONULP_Pos  (3U)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONULP_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPRXPONULP_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONULP  DSI_PHY_D_LANE0_CTRL_LPRXPONULP_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRXPONCD_Pos  (4U)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONCD_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPRXPONCD_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRXPONCD  DSI_PHY_D_LANE0_CTRL_LPRXPONCD_Msk
#define DSI_PHY_D_LANE0_CTRL_BIASPON_Pos  (5U)
#define DSI_PHY_D_LANE0_CTRL_BIASPON_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_BIASPON_Pos)
#define DSI_PHY_D_LANE0_CTRL_BIASPON    DSI_PHY_D_LANE0_CTRL_BIASPON_Msk
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_SET_Pos  (6U)
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_SET_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_HSTXDIN_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_SET  DSI_PHY_D_LANE0_CTRL_HSTXDIN_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_BYPASS_Pos  (7U)
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_HSTXDIN_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_HSTXDIN_BYPASS  DSI_PHY_D_LANE0_CTRL_HSTXDIN_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_SET_Pos  (8U)
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_SET_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_HSTXENA_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_SET  DSI_PHY_D_LANE0_CTRL_HSTXENA_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_BYPASS_Pos  (9U)
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_HSTXENA_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_HSTXENA_BYPASS  DSI_PHY_D_LANE0_CTRL_HSTXENA_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_SET_Pos  (10U)
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_SET_Msk  (0x3UL << DSI_PHY_D_LANE0_CTRL_LPTXDIN_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_SET  DSI_PHY_D_LANE0_CTRL_LPTXDIN_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_BYPASS_Pos  (12U)
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXDIN_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXDIN_BYPASS  DSI_PHY_D_LANE0_CTRL_LPTXDIN_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_SET_Pos  (13U)
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_SET_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXPD_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_SET  DSI_PHY_D_LANE0_CTRL_LPTXPD_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_BYPASS_Pos  (14U)
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXPD_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXPD_BYPASS  DSI_PHY_D_LANE0_CTRL_LPTXPD_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_SET_Pos  (15U)
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_SET_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXPU_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_SET  DSI_PHY_D_LANE0_CTRL_LPTXPU_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_BYPASS_Pos  (16U)
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXPU_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXPU_BYPASS  DSI_PHY_D_LANE0_CTRL_LPTXPU_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_SET_Pos  (17U)
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_SET_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXENA_SET_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_SET  DSI_PHY_D_LANE0_CTRL_LPTXENA_SET_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_BYPASS_Pos  (18U)
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPTXENA_BYPASS_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTXENA_BYPASS  DSI_PHY_D_LANE0_CTRL_LPTXENA_BYPASS_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRXULPDOUT_Pos  (19U)
#define DSI_PHY_D_LANE0_CTRL_LPRXULPDOUT_Msk  (0x3UL << DSI_PHY_D_LANE0_CTRL_LPRXULPDOUT_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRXULPDOUT  DSI_PHY_D_LANE0_CTRL_LPRXULPDOUT_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRXLPDOUT_Pos  (21U)
#define DSI_PHY_D_LANE0_CTRL_LPRXLPDOUT_Msk  (0x3UL << DSI_PHY_D_LANE0_CTRL_LPRXLPDOUT_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRXLPDOUT  DSI_PHY_D_LANE0_CTRL_LPRXLPDOUT_Msk
#define DSI_PHY_D_LANE0_CTRL_CDDOUT_Pos  (23U)
#define DSI_PHY_D_LANE0_CTRL_CDDOUT_Msk  (0x3UL << DSI_PHY_D_LANE0_CTRL_CDDOUT_Pos)
#define DSI_PHY_D_LANE0_CTRL_CDDOUT     DSI_PHY_D_LANE0_CTRL_CDDOUT_Msk
#define DSI_PHY_D_LANE0_CTRL_PN_SWAP_Pos  (25U)
#define DSI_PHY_D_LANE0_CTRL_PN_SWAP_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_PN_SWAP_Pos)
#define DSI_PHY_D_LANE0_CTRL_PN_SWAP    DSI_PHY_D_LANE0_CTRL_PN_SWAP_Msk
#define DSI_PHY_D_LANE0_CTRL_CONTENTION_DET_EN_Pos  (26U)
#define DSI_PHY_D_LANE0_CTRL_CONTENTION_DET_EN_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_CONTENTION_DET_EN_Pos)
#define DSI_PHY_D_LANE0_CTRL_CONTENTION_DET_EN  DSI_PHY_D_LANE0_CTRL_CONTENTION_DET_EN_Msk
#define DSI_PHY_D_LANE0_CTRL_LPTX_FIFO_TH_Pos  (27U)
#define DSI_PHY_D_LANE0_CTRL_LPTX_FIFO_TH_Msk  (0x7UL << DSI_PHY_D_LANE0_CTRL_LPTX_FIFO_TH_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPTX_FIFO_TH  DSI_PHY_D_LANE0_CTRL_LPTX_FIFO_TH_Msk
#define DSI_PHY_D_LANE0_CTRL_LPRX_CLK_MODE_Pos  (30U)
#define DSI_PHY_D_LANE0_CTRL_LPRX_CLK_MODE_Msk  (0x1UL << DSI_PHY_D_LANE0_CTRL_LPRX_CLK_MODE_Pos)
#define DSI_PHY_D_LANE0_CTRL_LPRX_CLK_MODE  DSI_PHY_D_LANE0_CTRL_LPRX_CLK_MODE_Msk

/*********** Bit definition for DSI_PHY_D_LANE0_TEST_CTRL register ************/
#define DSI_PHY_D_LANE0_TEST_CTRL_SYN_OUT_ENB_Pos  (0U)
#define DSI_PHY_D_LANE0_TEST_CTRL_SYN_OUT_ENB_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_SYN_OUT_ENB_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_SYN_OUT_ENB  DSI_PHY_D_LANE0_TEST_CTRL_SYN_OUT_ENB_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_OS_SET_Pos  (1U)
#define DSI_PHY_D_LANE0_TEST_CTRL_OS_SET_Msk  (0x1FUL << DSI_PHY_D_LANE0_TEST_CTRL_OS_SET_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_OS_SET  DSI_PHY_D_LANE0_TEST_CTRL_OS_SET_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_CAL_EN_Pos  (6U)
#define DSI_PHY_D_LANE0_TEST_CTRL_CAL_EN_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_CAL_EN_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_CAL_EN  DSI_PHY_D_LANE0_TEST_CTRL_CAL_EN_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_INMATCH_EN_Pos  (7U)
#define DSI_PHY_D_LANE0_TEST_CTRL_INMATCH_EN_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_INMATCH_EN_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_INMATCH_EN  DSI_PHY_D_LANE0_TEST_CTRL_INMATCH_EN_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LBMODE_EN_Pos  (8U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LBMODE_EN_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LBMODE_EN_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LBMODE_EN  DSI_PHY_D_LANE0_TEST_CTRL_LBMODE_EN_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_HSRXPON_Pos  (9U)
#define DSI_PHY_D_LANE0_TEST_CTRL_HSRXPON_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_HSRXPON_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_HSRXPON  DSI_PHY_D_LANE0_TEST_CTRL_HSRXPON_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_HS_TEST_STAT_Pos  (10U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_HS_TEST_STAT_Msk  (0x3UL << DSI_PHY_D_LANE0_TEST_CTRL_LB_HS_TEST_STAT_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_HS_TEST_STAT  DSI_PHY_D_LANE0_TEST_CTRL_LB_HS_TEST_STAT_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_LP_TEST_STAT_Pos  (12U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_LP_TEST_STAT_Msk  (0x3UL << DSI_PHY_D_LANE0_TEST_CTRL_LB_LP_TEST_STAT_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_LP_TEST_STAT  DSI_PHY_D_LANE0_TEST_CTRL_LB_LP_TEST_STAT_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_DONE_Pos  (14U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_DONE_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LB_DONE_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_DONE  DSI_PHY_D_LANE0_TEST_CTRL_LB_DONE_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_BUSY_Pos  (15U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_BUSY_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LB_BUSY_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LB_BUSY  DSI_PHY_D_LANE0_TEST_CTRL_LB_BUSY_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_RX_CLK_POL_Pos  (16U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LP_RX_CLK_POL_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_RX_CLK_POL  DSI_PHY_D_LANE0_TEST_CTRL_LP_RX_CLK_POL_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_RX_CLK_POL_Pos  (17U)
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_HS_RX_CLK_POL_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_RX_CLK_POL  DSI_PHY_D_LANE0_TEST_CTRL_HS_RX_CLK_POL_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_DLY_SEL_Pos  (18U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_DLY_SEL_Msk  (0xFUL << DSI_PHY_D_LANE0_TEST_CTRL_LP_DLY_SEL_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LP_DLY_SEL  DSI_PHY_D_LANE0_TEST_CTRL_LP_DLY_SEL_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_DLY_SEL_Pos  (22U)
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_DLY_SEL_Msk  (0x3FUL << DSI_PHY_D_LANE0_TEST_CTRL_HS_DLY_SEL_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_HS_DLY_SEL  DSI_PHY_D_LANE0_TEST_CTRL_HS_DLY_SEL_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LPRX_MODE_SEL_Pos  (28U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LPRX_MODE_SEL_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LPRX_MODE_SEL_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LPRX_MODE_SEL  DSI_PHY_D_LANE0_TEST_CTRL_LPRX_MODE_SEL_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_MODE_Pos  (29U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_MODE_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_MODE_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_MODE  DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_MODE_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_EN_Pos  (30U)
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_EN_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_EN_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_EN  DSI_PHY_D_LANE0_TEST_CTRL_LOOPBACK_EN_Msk
#define DSI_PHY_D_LANE0_TEST_CTRL_RUN_LB_TEST_Pos  (31U)
#define DSI_PHY_D_LANE0_TEST_CTRL_RUN_LB_TEST_Msk  (0x1UL << DSI_PHY_D_LANE0_TEST_CTRL_RUN_LB_TEST_Pos)
#define DSI_PHY_D_LANE0_TEST_CTRL_RUN_LB_TEST  DSI_PHY_D_LANE0_TEST_CTRL_RUN_LB_TEST_Msk

/************** Bit definition for DSI_PHY_D_LANE0_STAT register **************/
#define DSI_PHY_D_LANE0_STAT_ASY_DOUT_Pos  (0U)
#define DSI_PHY_D_LANE0_STAT_ASY_DOUT_Msk  (0x3UL << DSI_PHY_D_LANE0_STAT_ASY_DOUT_Pos)
#define DSI_PHY_D_LANE0_STAT_ASY_DOUT   DSI_PHY_D_LANE0_STAT_ASY_DOUT_Msk
#define DSI_PHY_D_LANE0_STAT_ASY_CLKOUT_Pos  (2U)
#define DSI_PHY_D_LANE0_STAT_ASY_CLKOUT_Msk  (0x3UL << DSI_PHY_D_LANE0_STAT_ASY_CLKOUT_Pos)
#define DSI_PHY_D_LANE0_STAT_ASY_CLKOUT  DSI_PHY_D_LANE0_STAT_ASY_CLKOUT_Msk

/************** Bit definition for DSI_PHY_D_LANE1_CFG0 register **************/
#define DSI_PHY_D_LANE1_CFG0_TLPX_CNT_Pos  (0U)
#define DSI_PHY_D_LANE1_CFG0_TLPX_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE1_CFG0_TLPX_CNT_Pos)
#define DSI_PHY_D_LANE1_CFG0_TLPX_CNT   DSI_PHY_D_LANE1_CFG0_TLPX_CNT_Msk
#define DSI_PHY_D_LANE1_CFG0_THSPREP_CNT_Pos  (8U)
#define DSI_PHY_D_LANE1_CFG0_THSPREP_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE1_CFG0_THSPREP_CNT_Pos)
#define DSI_PHY_D_LANE1_CFG0_THSPREP_CNT  DSI_PHY_D_LANE1_CFG0_THSPREP_CNT_Msk
#define DSI_PHY_D_LANE1_CFG0_THSZERO_CNT_Pos  (16U)
#define DSI_PHY_D_LANE1_CFG0_THSZERO_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE1_CFG0_THSZERO_CNT_Pos)
#define DSI_PHY_D_LANE1_CFG0_THSZERO_CNT  DSI_PHY_D_LANE1_CFG0_THSZERO_CNT_Msk
#define DSI_PHY_D_LANE1_CFG0_THSTRAIL_CNT_Pos  (24U)
#define DSI_PHY_D_LANE1_CFG0_THSTRAIL_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE1_CFG0_THSTRAIL_CNT_Pos)
#define DSI_PHY_D_LANE1_CFG0_THSTRAIL_CNT  DSI_PHY_D_LANE1_CFG0_THSTRAIL_CNT_Msk

/************** Bit definition for DSI_PHY_D_LANE1_CFG1 register **************/
#define DSI_PHY_D_LANE1_CFG1_TWAKEUP_CNT_Pos  (0U)
#define DSI_PHY_D_LANE1_CFG1_TWAKEUP_CNT_Msk  (0xFFUL << DSI_PHY_D_LANE1_CFG1_TWAKEUP_CNT_Pos)
#define DSI_PHY_D_LANE1_CFG1_TWAKEUP_CNT  DSI_PHY_D_LANE1_CFG1_TWAKEUP_CNT_Msk

/************** Bit definition for DSI_PHY_D_LANE1_CTRL register **************/
#define DSI_PHY_D_LANE1_CTRL_HSTXPON_Pos  (0U)
#define DSI_PHY_D_LANE1_CTRL_HSTXPON_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_HSTXPON_Pos)
#define DSI_PHY_D_LANE1_CTRL_HSTXPON    DSI_PHY_D_LANE1_CTRL_HSTXPON_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXPON_Pos  (1U)
#define DSI_PHY_D_LANE1_CTRL_LPTXPON_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXPON_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXPON    DSI_PHY_D_LANE1_CTRL_LPTXPON_Msk
#define DSI_PHY_D_LANE1_CTRL_LPRXPONLP_Pos  (2U)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONLP_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPRXPONLP_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONLP  DSI_PHY_D_LANE1_CTRL_LPRXPONLP_Msk
#define DSI_PHY_D_LANE1_CTRL_LPRXPONULP_Pos  (3U)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONULP_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPRXPONULP_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONULP  DSI_PHY_D_LANE1_CTRL_LPRXPONULP_Msk
#define DSI_PHY_D_LANE1_CTRL_LPRXPONCD_Pos  (4U)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONCD_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPRXPONCD_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPRXPONCD  DSI_PHY_D_LANE1_CTRL_LPRXPONCD_Msk
#define DSI_PHY_D_LANE1_CTRL_BIASPON_Pos  (5U)
#define DSI_PHY_D_LANE1_CTRL_BIASPON_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_BIASPON_Pos)
#define DSI_PHY_D_LANE1_CTRL_BIASPON    DSI_PHY_D_LANE1_CTRL_BIASPON_Msk
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_SET_Pos  (6U)
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_SET_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_HSTXDIN_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_SET  DSI_PHY_D_LANE1_CTRL_HSTXDIN_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_BYPASS_Pos  (7U)
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_HSTXDIN_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_HSTXDIN_BYPASS  DSI_PHY_D_LANE1_CTRL_HSTXDIN_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_SET_Pos  (8U)
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_SET_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_HSTXENA_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_SET  DSI_PHY_D_LANE1_CTRL_HSTXENA_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_BYPASS_Pos  (9U)
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_HSTXENA_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_HSTXENA_BYPASS  DSI_PHY_D_LANE1_CTRL_HSTXENA_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_SET_Pos  (10U)
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_SET_Msk  (0x3UL << DSI_PHY_D_LANE1_CTRL_LPTXDIN_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_SET  DSI_PHY_D_LANE1_CTRL_LPTXDIN_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_BYPASS_Pos  (12U)
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXDIN_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXDIN_BYPASS  DSI_PHY_D_LANE1_CTRL_LPTXDIN_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_SET_Pos  (13U)
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_SET_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXPD_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_SET  DSI_PHY_D_LANE1_CTRL_LPTXPD_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_BYPASS_Pos  (14U)
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXPD_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXPD_BYPASS  DSI_PHY_D_LANE1_CTRL_LPTXPD_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_SET_Pos  (15U)
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_SET_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXPU_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_SET  DSI_PHY_D_LANE1_CTRL_LPTXPU_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_BYPASS_Pos  (16U)
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXPU_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXPU_BYPASS  DSI_PHY_D_LANE1_CTRL_LPTXPU_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_SET_Pos  (17U)
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_SET_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXENA_SET_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_SET  DSI_PHY_D_LANE1_CTRL_LPTXENA_SET_Msk
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_BYPASS_Pos  (18U)
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_BYPASS_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_LPTXENA_BYPASS_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPTXENA_BYPASS  DSI_PHY_D_LANE1_CTRL_LPTXENA_BYPASS_Msk
#define DSI_PHY_D_LANE1_CTRL_LPRXULPDOUT_Pos  (19U)
#define DSI_PHY_D_LANE1_CTRL_LPRXULPDOUT_Msk  (0x3UL << DSI_PHY_D_LANE1_CTRL_LPRXULPDOUT_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPRXULPDOUT  DSI_PHY_D_LANE1_CTRL_LPRXULPDOUT_Msk
#define DSI_PHY_D_LANE1_CTRL_LPRXLPDOUT_Pos  (21U)
#define DSI_PHY_D_LANE1_CTRL_LPRXLPDOUT_Msk  (0x3UL << DSI_PHY_D_LANE1_CTRL_LPRXLPDOUT_Pos)
#define DSI_PHY_D_LANE1_CTRL_LPRXLPDOUT  DSI_PHY_D_LANE1_CTRL_LPRXLPDOUT_Msk
#define DSI_PHY_D_LANE1_CTRL_CDDOUT_Pos  (23U)
#define DSI_PHY_D_LANE1_CTRL_CDDOUT_Msk  (0x3UL << DSI_PHY_D_LANE1_CTRL_CDDOUT_Pos)
#define DSI_PHY_D_LANE1_CTRL_CDDOUT     DSI_PHY_D_LANE1_CTRL_CDDOUT_Msk
#define DSI_PHY_D_LANE1_CTRL_PN_SWAP_Pos  (25U)
#define DSI_PHY_D_LANE1_CTRL_PN_SWAP_Msk  (0x1UL << DSI_PHY_D_LANE1_CTRL_PN_SWAP_Pos)
#define DSI_PHY_D_LANE1_CTRL_PN_SWAP    DSI_PHY_D_LANE1_CTRL_PN_SWAP_Msk

/*********** Bit definition for DSI_PHY_D_LANE1_TEST_CTRL register ************/
#define DSI_PHY_D_LANE1_TEST_CTRL_SYN_OUT_ENB_Pos  (0U)
#define DSI_PHY_D_LANE1_TEST_CTRL_SYN_OUT_ENB_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_SYN_OUT_ENB_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_SYN_OUT_ENB  DSI_PHY_D_LANE1_TEST_CTRL_SYN_OUT_ENB_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_OS_SET_Pos  (1U)
#define DSI_PHY_D_LANE1_TEST_CTRL_OS_SET_Msk  (0x1FUL << DSI_PHY_D_LANE1_TEST_CTRL_OS_SET_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_OS_SET  DSI_PHY_D_LANE1_TEST_CTRL_OS_SET_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_CAL_EN_Pos  (6U)
#define DSI_PHY_D_LANE1_TEST_CTRL_CAL_EN_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_CAL_EN_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_CAL_EN  DSI_PHY_D_LANE1_TEST_CTRL_CAL_EN_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_INMATCH_EN_Pos  (7U)
#define DSI_PHY_D_LANE1_TEST_CTRL_INMATCH_EN_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_INMATCH_EN_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_INMATCH_EN  DSI_PHY_D_LANE1_TEST_CTRL_INMATCH_EN_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LBMODE_EN_Pos  (8U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LBMODE_EN_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LBMODE_EN_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LBMODE_EN  DSI_PHY_D_LANE1_TEST_CTRL_LBMODE_EN_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_HSRXPON_Pos  (9U)
#define DSI_PHY_D_LANE1_TEST_CTRL_HSRXPON_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_HSRXPON_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_HSRXPON  DSI_PHY_D_LANE1_TEST_CTRL_HSRXPON_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_HS_TEST_STAT_Pos  (10U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_HS_TEST_STAT_Msk  (0x3UL << DSI_PHY_D_LANE1_TEST_CTRL_LB_HS_TEST_STAT_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_HS_TEST_STAT  DSI_PHY_D_LANE1_TEST_CTRL_LB_HS_TEST_STAT_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_LP_TEST_STAT_Pos  (12U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_LP_TEST_STAT_Msk  (0x3UL << DSI_PHY_D_LANE1_TEST_CTRL_LB_LP_TEST_STAT_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_LP_TEST_STAT  DSI_PHY_D_LANE1_TEST_CTRL_LB_LP_TEST_STAT_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_DONE_Pos  (14U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_DONE_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LB_DONE_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_DONE  DSI_PHY_D_LANE1_TEST_CTRL_LB_DONE_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_BUSY_Pos  (15U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_BUSY_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LB_BUSY_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LB_BUSY  DSI_PHY_D_LANE1_TEST_CTRL_LB_BUSY_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_RX_CLK_POL_Pos  (16U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LP_RX_CLK_POL_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_RX_CLK_POL  DSI_PHY_D_LANE1_TEST_CTRL_LP_RX_CLK_POL_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_RX_CLK_POL_Pos  (17U)
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_RX_CLK_POL_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_HS_RX_CLK_POL_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_RX_CLK_POL  DSI_PHY_D_LANE1_TEST_CTRL_HS_RX_CLK_POL_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_DLY_SEL_Pos  (18U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_DLY_SEL_Msk  (0xFUL << DSI_PHY_D_LANE1_TEST_CTRL_LP_DLY_SEL_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LP_DLY_SEL  DSI_PHY_D_LANE1_TEST_CTRL_LP_DLY_SEL_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_DLY_SEL_Pos  (22U)
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_DLY_SEL_Msk  (0x3FUL << DSI_PHY_D_LANE1_TEST_CTRL_HS_DLY_SEL_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_HS_DLY_SEL  DSI_PHY_D_LANE1_TEST_CTRL_HS_DLY_SEL_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LPRX_MODE_SEL_Pos  (28U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LPRX_MODE_SEL_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LPRX_MODE_SEL_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LPRX_MODE_SEL  DSI_PHY_D_LANE1_TEST_CTRL_LPRX_MODE_SEL_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_MODE_Pos  (29U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_MODE_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_MODE_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_MODE  DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_MODE_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_EN_Pos  (30U)
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_EN_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_EN_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_EN  DSI_PHY_D_LANE1_TEST_CTRL_LOOPBACK_EN_Msk
#define DSI_PHY_D_LANE1_TEST_CTRL_RUN_LB_TEST_Pos  (31U)
#define DSI_PHY_D_LANE1_TEST_CTRL_RUN_LB_TEST_Msk  (0x1UL << DSI_PHY_D_LANE1_TEST_CTRL_RUN_LB_TEST_Pos)
#define DSI_PHY_D_LANE1_TEST_CTRL_RUN_LB_TEST  DSI_PHY_D_LANE1_TEST_CTRL_RUN_LB_TEST_Msk

/************** Bit definition for DSI_PHY_D_LANE1_STAT register **************/
#define DSI_PHY_D_LANE1_STAT_ASY_DOUT_Pos  (0U)
#define DSI_PHY_D_LANE1_STAT_ASY_DOUT_Msk  (0x3UL << DSI_PHY_D_LANE1_STAT_ASY_DOUT_Pos)
#define DSI_PHY_D_LANE1_STAT_ASY_DOUT   DSI_PHY_D_LANE1_STAT_ASY_DOUT_Msk
#define DSI_PHY_D_LANE1_STAT_ASY_CLKOUT_Pos  (2U)
#define DSI_PHY_D_LANE1_STAT_ASY_CLKOUT_Msk  (0x3UL << DSI_PHY_D_LANE1_STAT_ASY_CLKOUT_Pos)
#define DSI_PHY_D_LANE1_STAT_ASY_CLKOUT  DSI_PHY_D_LANE1_STAT_ASY_CLKOUT_Msk

/**************** Bit definition for DSI_PHY_DLL_CFG register *****************/
#define DSI_PHY_DLL_CFG_DLL0_LDO_VREF_Pos  (0U)
#define DSI_PHY_DLL_CFG_DLL0_LDO_VREF_Msk  (0xFUL << DSI_PHY_DLL_CFG_DLL0_LDO_VREF_Pos)
#define DSI_PHY_DLL_CFG_DLL0_LDO_VREF   DSI_PHY_DLL_CFG_DLL0_LDO_VREF_Msk
#define DSI_PHY_DLL_CFG_DLL0_48M_EN_Pos  (4U)
#define DSI_PHY_DLL_CFG_DLL0_48M_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_48M_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_48M_EN     DSI_PHY_DLL_CFG_DLL0_48M_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_STG_Pos    (5U)
#define DSI_PHY_DLL_CFG_DLL0_STG_Msk    (0xFUL << DSI_PHY_DLL_CFG_DLL0_STG_Pos)
#define DSI_PHY_DLL_CFG_DLL0_STG        DSI_PHY_DLL_CFG_DLL0_STG_Msk
#define DSI_PHY_DLL_CFG_DLL0_VST_SEL_Pos  (9U)
#define DSI_PHY_DLL_CFG_DLL0_VST_SEL_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_VST_SEL_Pos)
#define DSI_PHY_DLL_CFG_DLL0_VST_SEL    DSI_PHY_DLL_CFG_DLL0_VST_SEL_Msk
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_Pos  (10U)
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_Pos)
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG  DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_Msk
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_EN_Pos  (11U)
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_EN  DSI_PHY_DLL_CFG_DLL0_MCU_PRCHG_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EN_Pos  (12U)
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_PRCHG_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EN   DSI_PHY_DLL_CFG_DLL0_PRCHG_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EXT_Pos  (13U)
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EXT_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_PRCHG_EXT_Pos)
#define DSI_PHY_DLL_CFG_DLL0_PRCHG_EXT  DSI_PHY_DLL_CFG_DLL0_PRCHG_EXT_Msk
#define DSI_PHY_DLL_CFG_DLL0_XTALIN_EN_Pos  (14U)
#define DSI_PHY_DLL_CFG_DLL0_XTALIN_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_XTALIN_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_XTALIN_EN  DSI_PHY_DLL_CFG_DLL0_XTALIN_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_IN_DIV2_EN_Pos  (15U)
#define DSI_PHY_DLL_CFG_DLL0_IN_DIV2_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_IN_DIV2_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_IN_DIV2_EN  DSI_PHY_DLL_CFG_DLL0_IN_DIV2_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_OUT_DIV2_EN_Pos  (16U)
#define DSI_PHY_DLL_CFG_DLL0_OUT_DIV2_EN_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_OUT_DIV2_EN_Pos)
#define DSI_PHY_DLL_CFG_DLL0_OUT_DIV2_EN  DSI_PHY_DLL_CFG_DLL0_OUT_DIV2_EN_Msk
#define DSI_PHY_DLL_CFG_DLL0_BYPASS_Pos  (17U)
#define DSI_PHY_DLL_CFG_DLL0_BYPASS_Msk  (0x1UL << DSI_PHY_DLL_CFG_DLL0_BYPASS_Pos)
#define DSI_PHY_DLL_CFG_DLL0_BYPASS     DSI_PHY_DLL_CFG_DLL0_BYPASS_Msk
#define DSI_PHY_DLL_CFG_DLL0_OUT_STR_Pos  (18U)
#define DSI_PHY_DLL_CFG_DLL0_OUT_STR_Msk  (0x3UL << DSI_PHY_DLL_CFG_DLL0_OUT_STR_Pos)
#define DSI_PHY_DLL_CFG_DLL0_OUT_STR    DSI_PHY_DLL_CFG_DLL0_OUT_STR_Msk

/**************** Bit definition for DSI_PHY_DLL_CTRL register ****************/
#define DSI_PHY_DLL_CTRL_DLL0_OUT_EN_Pos  (0U)
#define DSI_PHY_DLL_CTRL_DLL0_OUT_EN_Msk  (0x1UL << DSI_PHY_DLL_CTRL_DLL0_OUT_EN_Pos)
#define DSI_PHY_DLL_CTRL_DLL0_OUT_EN    DSI_PHY_DLL_CTRL_DLL0_OUT_EN_Msk
#define DSI_PHY_DLL_CTRL_DLL0_OUT_RSTB_Pos  (1U)
#define DSI_PHY_DLL_CTRL_DLL0_OUT_RSTB_Msk  (0x1UL << DSI_PHY_DLL_CTRL_DLL0_OUT_RSTB_Pos)
#define DSI_PHY_DLL_CTRL_DLL0_OUT_RSTB  DSI_PHY_DLL_CTRL_DLL0_OUT_RSTB_Msk
#define DSI_PHY_DLL_CTRL_DLL0_LOOP_EN_Pos  (2U)
#define DSI_PHY_DLL_CTRL_DLL0_LOOP_EN_Msk  (0x1UL << DSI_PHY_DLL_CTRL_DLL0_LOOP_EN_Pos)
#define DSI_PHY_DLL_CTRL_DLL0_LOOP_EN   DSI_PHY_DLL_CTRL_DLL0_LOOP_EN_Msk
#define DSI_PHY_DLL_CTRL_DLL0_LDO_EN_Pos  (3U)
#define DSI_PHY_DLL_CTRL_DLL0_LDO_EN_Msk  (0x1UL << DSI_PHY_DLL_CTRL_DLL0_LDO_EN_Pos)
#define DSI_PHY_DLL_CTRL_DLL0_LDO_EN    DSI_PHY_DLL_CTRL_DLL0_LDO_EN_Msk

/**************** Bit definition for DSI_PHY_DLL_TEST register ****************/
#define DSI_PHY_DLL_TEST_DLL0_DTEST_TR_Pos  (0U)
#define DSI_PHY_DLL_TEST_DLL0_DTEST_TR_Msk  (0xFUL << DSI_PHY_DLL_TEST_DLL0_DTEST_TR_Pos)
#define DSI_PHY_DLL_TEST_DLL0_DTEST_TR  DSI_PHY_DLL_TEST_DLL0_DTEST_TR_Msk
#define DSI_PHY_DLL_TEST_DLL0_DTEST_EN_Pos  (4U)
#define DSI_PHY_DLL_TEST_DLL0_DTEST_EN_Msk  (0x1UL << DSI_PHY_DLL_TEST_DLL0_DTEST_EN_Pos)
#define DSI_PHY_DLL_TEST_DLL0_DTEST_EN  DSI_PHY_DLL_TEST_DLL0_DTEST_EN_Msk

/************** Bit definition for DSI_PHY_DSI_ANA_CTRL register **************/
#define DSI_PHY_DSI_ANA_CTRL_DC_TR_Pos  (0U)
#define DSI_PHY_DSI_ANA_CTRL_DC_TR_Msk  (0x7UL << DSI_PHY_DSI_ANA_CTRL_DC_TR_Pos)
#define DSI_PHY_DSI_ANA_CTRL_DC_TR      DSI_PHY_DSI_ANA_CTRL_DC_TR_Msk
#define DSI_PHY_DSI_ANA_CTRL_DC_BR_Pos  (3U)
#define DSI_PHY_DSI_ANA_CTRL_DC_BR_Msk  (0x7UL << DSI_PHY_DSI_ANA_CTRL_DC_BR_Pos)
#define DSI_PHY_DSI_ANA_CTRL_DC_BR      DSI_PHY_DSI_ANA_CTRL_DC_BR_Msk
#define DSI_PHY_DSI_ANA_CTRL_DC_TE_Pos  (6U)
#define DSI_PHY_DSI_ANA_CTRL_DC_TE_Msk  (0x1UL << DSI_PHY_DSI_ANA_CTRL_DC_TE_Pos)
#define DSI_PHY_DSI_ANA_CTRL_DC_TE      DSI_PHY_DSI_ANA_CTRL_DC_TE_Msk

/*************** Bit definition for DSI_PHY_BIAS_CTRL register ****************/
#define DSI_PHY_BIAS_CTRL_BIAS_PROG_Pos  (0U)
#define DSI_PHY_BIAS_CTRL_BIAS_PROG_Msk  (0xFFFFUL << DSI_PHY_BIAS_CTRL_BIAS_PROG_Pos)
#define DSI_PHY_BIAS_CTRL_BIAS_PROG     DSI_PHY_BIAS_CTRL_BIAS_PROG_Msk
#define DSI_PHY_BIAS_CTRL_BIAS_PROG_EN_Pos  (16U)
#define DSI_PHY_BIAS_CTRL_BIAS_PROG_EN_Msk  (0x1UL << DSI_PHY_BIAS_CTRL_BIAS_PROG_EN_Pos)
#define DSI_PHY_BIAS_CTRL_BIAS_PROG_EN  DSI_PHY_BIAS_CTRL_BIAS_PROG_EN_Msk
#define DSI_PHY_BIAS_CTRL_BIAS_MASTER_Pos  (17U)
#define DSI_PHY_BIAS_CTRL_BIAS_MASTER_Msk  (0x1UL << DSI_PHY_BIAS_CTRL_BIAS_MASTER_Pos)
#define DSI_PHY_BIAS_CTRL_BIAS_MASTER   DSI_PHY_BIAS_CTRL_BIAS_MASTER_Msk
#define DSI_PHY_BIAS_CTRL_PSW_EN_Pos    (18U)
#define DSI_PHY_BIAS_CTRL_PSW_EN_Msk    (0x1UL << DSI_PHY_BIAS_CTRL_PSW_EN_Pos)
#define DSI_PHY_BIAS_CTRL_PSW_EN        DSI_PHY_BIAS_CTRL_PSW_EN_Msk
#define DSI_PHY_BIAS_CTRL_BIAS_EN_Pos   (19U)
#define DSI_PHY_BIAS_CTRL_BIAS_EN_Msk   (0x1UL << DSI_PHY_BIAS_CTRL_BIAS_EN_Pos)
#define DSI_PHY_BIAS_CTRL_BIAS_EN       DSI_PHY_BIAS_CTRL_BIAS_EN_Msk
#define DSI_PHY_BIAS_CTRL_RSET_EN_Pos   (20U)
#define DSI_PHY_BIAS_CTRL_RSET_EN_Msk   (0x1UL << DSI_PHY_BIAS_CTRL_RSET_EN_Pos)
#define DSI_PHY_BIAS_CTRL_RSET_EN       DSI_PHY_BIAS_CTRL_RSET_EN_Msk

/**************** Bit definition for DSI_PHY_BIAS_CAL register ****************/
#define DSI_PHY_BIAS_CAL_RCAL_UP_Pos    (0U)
#define DSI_PHY_BIAS_CAL_RCAL_UP_Msk    (0x1UL << DSI_PHY_BIAS_CAL_RCAL_UP_Pos)
#define DSI_PHY_BIAS_CAL_RCAL_UP        DSI_PHY_BIAS_CAL_RCAL_UP_Msk
#define DSI_PHY_BIAS_CAL_RSET_MANUAL_Pos  (1U)
#define DSI_PHY_BIAS_CAL_RSET_MANUAL_Msk  (0x7UL << DSI_PHY_BIAS_CAL_RSET_MANUAL_Pos)
#define DSI_PHY_BIAS_CAL_RSET_MANUAL    DSI_PHY_BIAS_CAL_RSET_MANUAL_Msk
#define DSI_PHY_BIAS_CAL_RCAL_EN_MANUAL_Pos  (4U)
#define DSI_PHY_BIAS_CAL_RCAL_EN_MANUAL_Msk  (0x1UL << DSI_PHY_BIAS_CAL_RCAL_EN_MANUAL_Pos)
#define DSI_PHY_BIAS_CAL_RCAL_EN_MANUAL  DSI_PHY_BIAS_CAL_RCAL_EN_MANUAL_Msk
#define DSI_PHY_BIAS_CAL_RESULT_Pos     (5U)
#define DSI_PHY_BIAS_CAL_RESULT_Msk     (0x7UL << DSI_PHY_BIAS_CAL_RESULT_Pos)
#define DSI_PHY_BIAS_CAL_RESULT         DSI_PHY_BIAS_CAL_RESULT_Msk
#define DSI_PHY_BIAS_CAL_RCAL_POL_Pos   (8U)
#define DSI_PHY_BIAS_CAL_RCAL_POL_Msk   (0x1UL << DSI_PHY_BIAS_CAL_RCAL_POL_Pos)
#define DSI_PHY_BIAS_CAL_RCAL_POL       DSI_PHY_BIAS_CAL_RCAL_POL_Msk
#define DSI_PHY_BIAS_CAL_DONE_Pos       (9U)
#define DSI_PHY_BIAS_CAL_DONE_Msk       (0x1UL << DSI_PHY_BIAS_CAL_DONE_Pos)
#define DSI_PHY_BIAS_CAL_DONE           DSI_PHY_BIAS_CAL_DONE_Msk
#define DSI_PHY_BIAS_CAL_START_Pos      (10U)
#define DSI_PHY_BIAS_CAL_START_Msk      (0x1UL << DSI_PHY_BIAS_CAL_START_Pos)
#define DSI_PHY_BIAS_CAL_START          DSI_PHY_BIAS_CAL_START_Msk
#define DSI_PHY_BIAS_CAL_AUTO_CLK_DIV_Pos  (11U)
#define DSI_PHY_BIAS_CAL_AUTO_CLK_DIV_Msk  (0xFUL << DSI_PHY_BIAS_CAL_AUTO_CLK_DIV_Pos)
#define DSI_PHY_BIAS_CAL_AUTO_CLK_DIV   DSI_PHY_BIAS_CAL_AUTO_CLK_DIV_Msk
#define DSI_PHY_BIAS_CAL_MODE_Pos       (15U)
#define DSI_PHY_BIAS_CAL_MODE_Msk       (0x1UL << DSI_PHY_BIAS_CAL_MODE_Pos)
#define DSI_PHY_BIAS_CAL_MODE           DSI_PHY_BIAS_CAL_MODE_Msk

/************** Bit definition for DSI_PHY_LOOPBACK_CFG register **************/
#define DSI_PHY_LOOPBACK_CFG_LFSR_SEED_Pos  (0U)
#define DSI_PHY_LOOPBACK_CFG_LFSR_SEED_Msk  (0xFFFFUL << DSI_PHY_LOOPBACK_CFG_LFSR_SEED_Pos)
#define DSI_PHY_LOOPBACK_CFG_LFSR_SEED  DSI_PHY_LOOPBACK_CFG_LFSR_SEED_Msk
#define DSI_PHY_LOOPBACK_CFG_LB_CLK_DIV_Pos  (16U)
#define DSI_PHY_LOOPBACK_CFG_LB_CLK_DIV_Msk  (0xFFUL << DSI_PHY_LOOPBACK_CFG_LB_CLK_DIV_Pos)
#define DSI_PHY_LOOPBACK_CFG_LB_CLK_DIV  DSI_PHY_LOOPBACK_CFG_LB_CLK_DIV_Msk

/**************** Bit definition for DSI_PHY_MISC_CFG register ****************/
#define DSI_PHY_MISC_CFG_TPULLUP_CNT_Pos  (0U)
#define DSI_PHY_MISC_CFG_TPULLUP_CNT_Msk  (0xFFUL << DSI_PHY_MISC_CFG_TPULLUP_CNT_Pos)
#define DSI_PHY_MISC_CFG_TPULLUP_CNT    DSI_PHY_MISC_CFG_TPULLUP_CNT_Msk

/************** Bit definition for DSI_PHY_RESERVED_IN register ***************/
#define DSI_PHY_RESERVED_IN_CTRL_0_Pos  (0U)
#define DSI_PHY_RESERVED_IN_CTRL_0_Msk  (0xFFUL << DSI_PHY_RESERVED_IN_CTRL_0_Pos)
#define DSI_PHY_RESERVED_IN_CTRL_0      DSI_PHY_RESERVED_IN_CTRL_0_Msk
#define DSI_PHY_RESERVED_IN_CTRL_1_Pos  (8U)
#define DSI_PHY_RESERVED_IN_CTRL_1_Msk  (0xFFUL << DSI_PHY_RESERVED_IN_CTRL_1_Pos)
#define DSI_PHY_RESERVED_IN_CTRL_1      DSI_PHY_RESERVED_IN_CTRL_1_Msk
#define DSI_PHY_RESERVED_IN_CTRL_2_Pos  (16U)
#define DSI_PHY_RESERVED_IN_CTRL_2_Msk  (0xFFUL << DSI_PHY_RESERVED_IN_CTRL_2_Pos)
#define DSI_PHY_RESERVED_IN_CTRL_2      DSI_PHY_RESERVED_IN_CTRL_2_Msk

/************** Bit definition for DSI_PHY_RESERVED_OUT register **************/
#define DSI_PHY_RESERVED_OUT_STAT_Pos   (0U)
#define DSI_PHY_RESERVED_OUT_STAT_Msk   (0xFFUL << DSI_PHY_RESERVED_OUT_STAT_Pos)
#define DSI_PHY_RESERVED_OUT_STAT       DSI_PHY_RESERVED_OUT_STAT_Msk

#endif
