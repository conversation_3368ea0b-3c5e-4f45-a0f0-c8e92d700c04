/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hdl_ports/hdl_uart.h"
#include <rtdevice.h>
#include "airoha_ag333x.h"
#include "board.h"

#define GPS_TX_PIN          PAD_PB06
#define GPS_RX_PIN          PAD_PB11

#ifdef HDL_VIA_UART
static rt_device_t gnss_serial = NULL;
bool hdl_uart_init()
{
    if (!gnss_serial) {
        struct serial_configure config = UART5_SERIAL_CONFIG;
        config.baud_rate = 115200;
        // HAL_PIN_SetMode (GPS_TX_PIN, 0, PIN_DIGITAL_IO_NORMAL);
        // HAL_PIN_SetMode (GPS_RX_PIN, 0, PIN_DIGITAL_IO_NORMAL);
        // HAL_PIN_Set(GPS_TX_PIN, USART5_TXD, PIN_NOPULL, 0);             
        // HAL_PIN_Set(GPS_RX_PIN, USART5_RXD, PIN_PULLUP, 0);

        gnss_serial = rt_device_find(GPS_AG335X_UART_NAME);
        rt_err_t err = rt_device_open(gnss_serial, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_DMA_RX);

        if(err == RT_EOK){
            rt_device_control(gnss_serial, RT_DEVICE_CTRL_CONFIG, &config);
            return true;
        }
    }
    return false;
}

bool hdl_uart_deinit()
{
    if(gnss_serial){
        rt_device_close(gnss_serial);
        // HAL_PIN_Set_Analog(GPS_TX_PIN,0);
        // HAL_PIN_Set_Analog(GPS_RX_PIN,0);
        gnss_serial = NULL;
    }
    return true;
}

uint8_t hdl_uart_get_byte()
{
    uint8_t data = 0;
    while(rt_device_read(gnss_serial, -1, &data, 1) != 1){
        rt_thread_delay(1);
    }
    return data;
}

bool hdl_uart_get_byte_status(uint8_t *data)
{
    while(rt_device_read(gnss_serial, -1, data, 1) != 1){
        rt_thread_delay(1);
    }
    return TRUE;
}

uint32_t hdl_uart_get_byte_buffer(uint8_t *buf, uint32_t length)
{
    uint32_t leave_length = length;
    uint8_t *readBuf = buf;
    int received_len = 0;

    do{
        received_len = rt_device_read(gnss_serial, -1, readBuf, leave_length);
        if(received_len > 0){
            readBuf += received_len;
            leave_length -= received_len;
        }else{
            if(leave_length){
                rt_thread_delay(1);
            }
        }
    }while(leave_length > 0);

    return length;
}

void hdl_uart_put_byte(uint8_t data)
{
    rt_device_write(gnss_serial, 0, &data, 1);
}

uint32_t hdl_uart_put_byte_buffer(uint8_t *buf, uint32_t length)
{
    uint32_t leave_length = length;
    uint8_t *writeBuf = buf;
    int tx_len = 0;
    do{
        tx_len = rt_device_write(gnss_serial, 0, writeBuf, leave_length);
        if(tx_len > 0){
            writeBuf += tx_len;
            leave_length -= tx_len;
        }else{
            if(leave_length){
                rt_thread_delay(2);
            }
        }
    }while(leave_length > 0);
    return length;
}

void hdl_uart_put_byte_complete(uint8_t data)
{
    rt_device_write(gnss_serial, 0, &data, 1);
}

uint16_t hdl_uart_get_data16()
{
    uint16_t result = 0;
    uint8_t tmp1 = (uint8_t)hdl_uart_get_byte();
    uint8_t tmp2 = (uint8_t)hdl_uart_get_byte();
    result = ((uint16_t)(tmp1 << 8)) | tmp2;
    HDL_LOGI("hdl_uart_get_data16 0x%02X 0x%02X -> 0x%04X\n", tmp1, tmp2, result);
    return result;
}

void hdl_uart_put_data16(uint16_t data)
{
    uint8_t tmp1 = (uint8_t)(data >> 8);
    uint8_t tmp2 = (uint8_t)(data & 0x00FF);
    hdl_uart_put_byte(tmp1);
    hdl_uart_put_byte(tmp2);
    HDL_LOGI("hdl_uart_put_data16 0x%04X -> 0x%02X 0x%02X\n", data, tmp1, tmp2);
}

uint32_t hdl_uart_get_data32()
{
    uint8_t tmp[4] = {0};
    uint32_t result = 0;
    for (int i = 0; i < 4; i++) {
        tmp[i] = (uint8_t)hdl_uart_get_byte();
    }
    HDL_LOGI("get[%x:%x:%x:%x]\n",tmp[0],tmp[1],tmp[2],tmp[3]);
    result = ((uint32_t)(tmp[0]<<24))|(uint32_t)(tmp[1]<<16)|(uint32_t)(tmp[2]<<8)|tmp[3];
    HDL_LOGI("hdl_uart_get_data32 0x%08X\n", result);
    return result;
}

void hdl_uart_put_data32(uint32_t data)
{
    uint32_t tmp32 = 0;
    for (int i = 0; i < 4; i++) {
        tmp32 = (data >> (24 - 8 * i))&0xff;
        hdl_uart_put_byte((char)tmp32);
    }
    HDL_LOGI("hdl_uart_put_data32 0x%08X\n", data);
}

void hdl_uart_purge_fifo()
{
    HDL_LOGI("hdl_uart_purge_fifo: empty");
    /*
    #define   UART_FCR_FIFOINI          0x0007
    #define   UART_FCR_RX62Byte_Level   0x00c0
    #define   UART_FCR_TX62Byte_Level   0x0030
    #define   UART_FCR_MaxFIFO          (UART_FCR_RX62Byte_Level | UART_FCR_TX62Byte_Level | UART_FCR_FIFOINI)
    *(volatile uint8_t *)((0xA0100000) + 0x0014) = (uint8_t)UART_FCR_MaxFIFO;
    */
}

void hdl_uart_set_baudrate(uint32_t baud_rate)
{
    if (gnss_serial) {
        struct serial_configure config = UART5_SERIAL_CONFIG;
        config.baud_rate = baud_rate;
        rt_device_control(gnss_serial, RT_DEVICE_CTRL_CONFIG, &config);
    }
}

void hdl_uart_read_buffer_clear(void)
{
    if (gnss_serial) {
        uint8_t readBuf;
        int received_len = 0;

        do{
            received_len = rt_device_read(gnss_serial, -1, &readBuf, 1);
        }while(received_len);   
    }
}

#endif
