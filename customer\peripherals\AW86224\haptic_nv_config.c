/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   haptic_nv_config.c
@Time    :   2025/03/12 09:13:16
@Brief   :
@Details :
*
************************************************************/

#include "haptic_nv.h"
#include "AW86224.h"
#include "bf0_hal.h"

//定时器句柄
//extern rt_device_t timer_dev;

//i2c句柄
extern struct rt_i2c_bus_device *aw86224_bus;

/*****************************************************
 * @brief i2c 读取函数
 * @param reg_addr: 寄存器地址
 * @param reg_data: 寄存器数据
 * @param len: 读取寄存器的数量
 * @retval i2c 读取状态: 0->成功, 1->错误
 *****************************************************/
int haptic_nv_i2c_reads(uint8_t reg_addr, uint8_t *reg_data, uint16_t len)
{
	struct rt_i2c_msg msgs[2];
    uint32_t res;
	uint8_t value[1];
	if (aw86224_bus) {
		value[0] = reg_addr;
		msgs[0].addr = AW86224_ADDRESS;
		msgs[0].flags = RT_I2C_WR;
		msgs[0].buf = value;
		msgs[0].len = 1;

		msgs[1].addr = AW86224_ADDRESS;
		msgs[1].flags = RT_I2C_RD;
		msgs[1].buf = reg_data;
		msgs[1].len = len;

		if (rt_i2c_transfer(aw86224_bus, msgs, 2) == 2) {
			return RT_EOK;
		}
		else
		{
			AW_LOGE("haptic_nv_i2c read err!");
		}
	}
	else
	{
		AW_LOGE("haptic_nv_i2c NULL!");
	}

	return RT_ERROR;

}

/*****************************************************
 * @brief i2c 写入函数
 * @param reg_addr: 寄存器地址
 * @param reg_data: 寄存器数据
 * @param len: 写入寄存器的数量
 * @retval i2c 写入状态: 0->成功, 1->错误
 *****************************************************/
int haptic_nv_i2c_writes(uint8_t reg_addr, uint8_t *reg_data, uint16_t len)
{
	struct rt_i2c_msg msgs[1];
	uint8_t value[1 + len];
	uint32_t res;

	if (aw86224_bus) {
		value[0] = reg_addr;
		memcpy(&value[1], reg_data, len);

		msgs[0].addr = AW86224_ADDRESS;
		msgs[0].flags = RT_I2C_WR;
		msgs[0].buf = value;
		msgs[0].len = 1 + len;

		if (rt_i2c_transfer(aw86224_bus, msgs, 1) == 1) {
			return RT_EOK;
		}
		else
		{
			AW_LOGE("haptic_nv_i2c write err!");
		}
	}
	else
	{
		AW_LOGE("haptic_nv_i2c NULL!");
	}

	return RT_ERROR;
}

/*****************************************************
 * @brief i2c 写入位函数
 * @param reg_addr: 寄存器地址
 * @param reg_addr: 寄存器掩码
 * @param reg_data: 寄存器数据
 * @retval NULL
 *****************************************************/
void haptic_nv_i2c_write_bits(uint8_t reg_addr, uint32_t mask, uint8_t reg_data)
{
	uint8_t reg_val = 0;
	uint8_t reg_mask = (uint8_t)mask;

	haptic_nv_i2c_reads(reg_addr, &reg_val, AW_I2C_BYTE_ONE);
	reg_val &= reg_mask;
	reg_val |= (reg_data & (~reg_mask));
	haptic_nv_i2c_writes(reg_addr, &reg_val, AW_I2C_BYTE_ONE);
}

/*****************************************************
 * @brief 读取芯片ID函数
 * @param reg_addr: 芯片ID
 * @param type: 0->第一次尝试, 1->最后一次尝试
 * @retval 0->成功, 1->错误
 *****************************************************/
int haptic_nv_read_chipid(uint8_t *val, uint8_t type)
{
	uint8_t cnt = 0;
	int ret = AW_ERROR;

	while (cnt < AW_I2C_RETRIES) {
		ret = haptic_nv_i2c_reads(AW_REG_CHIPIDH, val, AW_I2C_BYTE_ONE);
		if (*val == AW8623X_CHIP_ID_H)
			ret = haptic_nv_i2c_reads(AW_REG_CHIPIDL, val, AW_I2C_BYTE_ONE);
		else
			ret = haptic_nv_i2c_reads(AW_REG_ID, val, AW_I2C_BYTE_ONE);
		if (ret == AW_ERROR) {
			if (type == AW_FIRST_TRY)
				AW_LOGI("reading chip id");
			else if (type == AW_LAST_TRY)
				AW_LOGE("i2c_read cnt=%d error=%d", cnt, ret);
			else
				AW_LOGE("type is error");
		} else {
			break;
		}
		cnt++;
	}

	return ret;
}
#if 0
/*****************************************************
 * @brief 停止高精度定时器
 * @param 无
 * @retval 无
 *****************************************************/
void haptic_nv_stop_hrtimer(void)
{
	int ret = 0;
	rt_hwtimerval_t t={0,0};
	ret = rt_device_write(timer_dev, 0, &t, sizeof(t));
	if (ret != sizeof(t))
	{
		LOG_E("Failed to stop timer\n");
	}
}

/*****************************************************
 * @brief 启动高精度定时器
 * @param 无
 * @retval 无
 *****************************************************/
void haptic_nv_start_hrtimer(void)
{
	int ret = 0;
	rt_hwtimerval_t t={0,1000};
	ret = rt_device_write(timer_dev, 0, &t, sizeof(t));
	if (ret != sizeof(t))
	{
		LOG_E("Failed to start timer\n");
	}
}

/*****************************************************
 * @brief 高精度定时器回调函数，用于长时间振动器停止。应由 HAL_TIM_PeriodElapsedCallback 调用
 * @param htim: 高精度定时器
 * @retval 无
 *****************************************************/
/**
 * @brief Haptic NV定时器中断回调函数
 * @param htim 定时器句柄
 */
void haptic_nv_tim_periodelapsedcallback(GPT_HandleTypeDef *htim)
{
	// 计时器毫秒计数增加
	g_haptic_nv->timer_ms_cnt++;
	if (g_haptic_nv->timer_ms_cnt == g_haptic_nv->duration) {
		AW_LOGI("timer over, g_haptic_nv->duration:%d", g_haptic_nv->duration);
		// 清零持续时间和计时器毫秒计数
		g_haptic_nv->duration = 0;
		g_haptic_nv->timer_ms_cnt = 0;
		// 停止播放
		g_func_haptic_nv->play_stop();
		// 停止高精度定时器
		haptic_nv_stop_hrtimer();
	}
}
#endif

/*****************************************************
 * @brief 延迟函数
 * @param ms: 毫秒
 * @retval 无
 *****************************************************/
void haptic_nv_mdelay(uint32_t ms)
{
	rt_thread_mdelay(ms);
}

/*****************************************************
 * @brief 设置校准数据到flash
 * @param 无
 * @retval  void
 *****************************************************/
void haptic_nv_set_cali_to_flash(void)
{
	//AW_LOGI("f0 cali data is 0x%02x", g_haptic_nv->f0_cali_data);
#ifndef IGS_BOOT
    set_f0_calib_value((uint16_t)g_haptic_nv->f0);
    //rt_kprintf("haptic_nv_set_cali_to_flash f0 = %d\n",g_haptic_nv->f0);
#endif
}

/*****************************************************
 * @brief 读取校准数据到flash
 * @param 无
 * @retval 	void
 *****************************************************/
void haptic_nv_get_cali_from_flash(void)
{
	//AW_LOGI("f0 cali data is 0x%02x", g_haptic_nv->f0_cali_data);
	/* g_haptic_nv->f0_cali_data = val; */
#ifndef IGS_BOOT
    uint16_t vib_f0_calib = 0;
    if (get_f0_calib_value(&vib_f0_calib))
    {
        g_haptic_nv->f0 = vib_f0_calib;
    }
#endif
    //rt_kprintf("haptic_nv_get_cali_from_flash f0 = %d,f0_pre = %d\n",g_haptic_nv->f0,g_haptic_nv->info->f0_pre);
    g_func_haptic_nv->calculate_cali_data(g_haptic_nv->f0,g_haptic_nv->info->f0_pre);
}


#ifdef AW_IRQ_CONFIG
/*****************************************************
 * @brief 中断回调函数，应该由HAL_GPIO_EXTI_Callback调用
 * @param GPIO_Pin: irq gpio pin
 * @retval None
 *****************************************************/
// void haptic_nv_gpio_exti_callback(uint16_t GPIO_Pin)
// {
// 	if (GPIO_Pin == AW86224_EXT_INTERRUPT_PIN)
// 		g_haptic_nv->irq_handle = AW_IRQ_ON;
// }
// void haptic_nv_gpio_exti_callback(void* arg)
// {
//     g_haptic_nv->irq_handle = AW_IRQ_ON;
//     rt_kprintf("###aw86224 haptic_nv_gpio_exti_callback\n");
// }
#endif

/*****************************************************
 * @brief 禁用中断gpio函数
 * @param None
 * @retval None
 *****************************************************/
void haptic_nv_disable_irq(void)
{
	rt_pin_irq_enable(AW86224_EXT_INTERRUPT_PIN, 0);
}

/*****************************************************
 * @brief 启用中断gpio函数
 * @param None
 * @retval None
 *****************************************************/
void haptic_nv_enable_irq(void)
{
	rt_pin_irq_enable(AW86224_EXT_INTERRUPT_PIN, 1);
}

/*****************************************************
 * @brief 控制haptic_nv的pin电平
 * @param status: 0->低电平, 1->高电平
 * @retval None
 *****************************************************/
void haptic_nv_pin_control(uint16_t GPIO_Pin, uint8_t status)
{
	rt_pin_write(GPIO_Pin, status);
}
