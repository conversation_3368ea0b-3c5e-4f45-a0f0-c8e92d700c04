/**
  ******************************************************************************
  * @file   st7797.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the st7797.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __ST7797_H__
#define __ST7797_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7797
  * @{
  */

/** @defgroup ST7797_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ST7797_Exported_Constants
  * @{
  */

/**
  * @brief ST7797 chip IDs
  */
#define ST7797_ID                  0x85

/**
  * @brief  ST7797 Size
  */
#define  ST7797_LCD_PIXEL_WIDTH    ((uint16_t)400)
#define  ST7797_LCD_PIXEL_HEIGHT   ((uint16_t)400)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ST7797_ORIENTATION_PORTRAIT         ((uint32_t)0x00) /* Portrait orientation choice of LCD screen  */
#define ST7797_ORIENTATION_LANDSCAPE        ((uint32_t)0x01) /* Landscape orientation choice of LCD screen */
#define ST7797_ORIENTATION_LANDSCAPE_ROT180 ((uint32_t)0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ST7797 Registers
  */
#define ST7797_LCD_ID             0x04
#define ST7797_SLEEP_IN           0x10
#define ST7797_SLEEP_OUT          0x11
#define ST7797_PARTIAL_DISPLAY    0x12
#define ST7797_DISPLAY_INVERSION  0x21
#define ST7797_DISPLAY_OFF        0x28
#define ST7797_DISPLAY_ON         0x29
#define ST7797_WRITE_RAM          0x2C
#define ST7797_READ_RAM           0x2E
#define ST7797_CASET              0x2A
#define ST7797_RASET              0x2B
#define ST7797_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define ST7797_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define ST7797_TEARING_EFFECT     0x35
#define ST7797_NORMAL_DISPLAY     0x36
#define ST7797_IDLE_MODE_OFF      0x38
#define ST7797_IDLE_MODE_ON       0x39
#define ST7797_COLOR_MODE         0x3A
#define ST7797_WBRIGHT            0x51
#define ST7797_WCTRL              0x53
#define ST7797_PORCH_CTRL         0xB2
#define ST7797_FRAME_CTRL         0xB3
#define ST7797_GATE_CTRL          0xB7
#define ST7797_VCOM_SET           0xBB
#define ST7797_LCM_CTRL           0xC0
#define ST7797_VDV_VRH_EN         0xC2
#define ST7797_VDV_SET            0xC4
#define ST7797_VCOMH_OFFSET_SET   0xC5
#define ST7797_FR_CTRL            0xC6
#define ST7797_POWER_CTRL         0xD0
#define ST7797_PV_GAMMA_CTRL      0xE0
#define ST7797_NV_GAMMA_CTRL      0xE1
#define ST7797_SPI2EN             0xE7


/**
  * @}
  */

/** @defgroup ST7797_Exported_Functions
  * @{
  */
void     ST7797_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ST7797_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ST7797_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ST7797_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ST7797_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ST7797_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ST7797_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ST7797_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ST7797_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ST7797_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */



#ifdef __cplusplus
}
#endif

#endif /* __ST7797_H__ */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
