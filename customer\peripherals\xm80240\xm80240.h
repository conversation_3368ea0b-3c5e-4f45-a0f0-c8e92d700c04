/**
  ******************************************************************************
  * @file   xm80240.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the rm69090.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __XM80240_H
#define __XM80240_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup XM80240
  * @{
  */

/** @defgroup XM80240_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup XM80240_Exported_Constants
  * @{
  */

/**
  * @brief XM80240 chip IDs
  */
#define XM80240_ID                  0x1190a7

/**
  * @brief  XM80240 Size
  */
#define  XM80240_LCD_PIXEL_WIDTH    (390)
#define  XM80240_LCD_PIXEL_HEIGHT   (450)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define XM80240_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define XM80240_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define XM80240_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  XM80240 Registers
  */
//#define XM80240_SW_RESET           0x01
#define XM80240_LCD_ID             0xA1
//#define XM80240_DSI_ERR            0x05
//#define XM80240_POWER_MODE         0x0A
#define XM80240_SLEEP_IN           0x10
#define XM80240_SLEEP_OUT          0x11
//#define XM80240_PARTIAL_DISPLAY    0x12
//#define XM80240_DISPLAY_INVERSION  0x21
#define XM80240_DISPLAY_OFF        0x28
#define XM80240_DISPLAY_ON         0x29
#define XM80240_WRITE_RAM          0x2C
#define XM80240_READ_RAM           0x2E
#define XM80240_CASET              0x2A
#define XM80240_RASET              0x2B
//#define XM80240_PART_CASET              0x30
//#define XM80240_PART_RASET              0x31
//#define XM80240_VSCRDEF            0x33 /* Vertical Scroll Definition */
//#define XM80240_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
//#define XM80240_TEARING_EFFECT     0x35
//#define XM80240_NORMAL_DISPLAY     0x36
//#define XM80240_IDLE_MODE_OFF      0x38
//#define XM80240_IDLE_MODE_ON       0x39
#define XM80240_COLOR_MODE         0x3A
#define XM80240_CONTINUE_WRITE_RAM 0x3C
#define XM80240_WBRIGHT            0x51 /* Write brightness*/
//#define XM80240_RBRIGHT            0x53 /* Read brightness*/
//#define XM80240_PORCH_CTRL         0xB2
//#define XM80240_FRAME_CTRL         0xB3
//#define XM80240_GATE_CTRL          0xB7
//#define XM80240_VCOM_SET           0xBB
//#define XM80240_LCM_CTRL           0xC0
//#define XM80240_SET_TIME_SRC       0xC2
//#define XM80240_SET_DISP_MODE      0xC4
//#define XM80240_VCOMH_OFFSET_SET   0xC5
//#define XM80240_FR_CTRL            0xC6
//#define XM80240_POWER_CTRL         0xD0
//#define XM80240_PV_GAMMA_CTRL      0xE0
//#define XM80240_NV_GAMMA_CTRL      0xE1
//#define XM80240_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup XM80240_Exported_Functions
  * @{
  */
void     XM80240_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t XM80240_ReadID(LCDC_HandleTypeDef *hlcdc);

void     XM80240_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     XM80240_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void XM80240_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void XM80240_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void XM80240_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t XM80240_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void XM80240_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void XM80240_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __XM80240_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/

