/**
  ******************************************************************************
  * @file   lcd_if.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __LCD_IF_H
#define __LCD_IF_H

typedef struct
{
    __IO uint32_t CONFIG;
    __IO uint32_t TL_POS;
    __IO uint32_t BR_POS;
    __IO uint32_t FILTER;
    __IO uint32_t SRC;
    __IO uint32_t FILL;
} LCDC_LayerxTypeDef;

typedef struct
{
    __IO uint32_t COMMAND;
    __IO uint32_t STATUS;
    __IO uint32_t IRQ;
    __IO uint32_t SETTING;
    __IO uint32_t CANVAS_TL_POS;
    __IO uint32_t CANVAS_BR_POS;
    __IO uint32_t CANVAS_BG;
    __IO uint32_t LAYER0_CONFIG;
    __IO uint32_t LAYER0_TL_POS;
    __IO uint32_t LAYER0_BR_POS;
    __IO uint32_t LAYER0_FILTER;
    __IO uint32_t LAYER0_SRC;
    __IO uint32_t LAYER0_FILL;
    __IO uint32_t LAYER0_DECOMP;
    __IO uint32_t LAYER0_DECOMP_CFG0;
    __IO uint32_t LAYER0_DECOMP_CFG1;
    __IO uint32_t LAYER0_DECOMP_STAT;
    __IO uint32_t RSVD2[7];
    __IO uint32_t LAYER1_CONFIG;
    __IO uint32_t LAYER1_TL_POS;
    __IO uint32_t LAYER1_BR_POS;
    __IO uint32_t LAYER1_FILTER;
    __IO uint32_t LAYER1_SRC;
    __IO uint32_t LAYER1_FILL;
    __IO uint32_t DITHER_CONF;
    __IO uint32_t DITHER_LFSR;
    __IO uint32_t LCD_CONF;
    __IO uint32_t LCD_IF_CONF;
    __IO uint32_t LCD_MEM;
    __IO uint32_t LCD_O_WIDTH;
    __IO uint32_t LCD_SINGLE;
    __IO uint32_t LCD_WR;
    __IO uint32_t LCD_RD;
    __IO uint32_t SPI_IF_CONF;
    __IO uint32_t TE_CONF;
    __IO uint32_t TE_CONF2;
    __IO uint32_t DPI_IF_CONF1;
    __IO uint32_t DPI_IF_CONF2;
    __IO uint32_t DPI_IF_CONF3;
    __IO uint32_t DPI_IF_CONF4;
    __IO uint32_t DPI_IF_CONF5;
    __IO uint32_t DPI_CTRL;
    __IO uint32_t DPI_STAT;
    __IO uint32_t JDI_SER_CONF1;
    __IO uint32_t JDI_SER_CONF2;
    __IO uint32_t JDI_SER_CTRL;
    __IO uint32_t JDI_PAR_CONF1;
    __IO uint32_t JDI_PAR_CONF2;
    __IO uint32_t JDI_PAR_CONF3;
    __IO uint32_t JDI_PAR_CONF4;
    __IO uint32_t JDI_PAR_CONF5;
    __IO uint32_t JDI_PAR_CONF6;
    __IO uint32_t JDI_PAR_CONF7;
    __IO uint32_t JDI_PAR_CTRL;
    __IO uint32_t JDI_PAR_STAT;
    __IO uint32_t JDI_PAR_EX_CTRL;
    __IO uint32_t JDI_PAR_CONF8;
    __IO uint32_t JDI_PAR_CONF9;
    __IO uint32_t JDI_PAR_CONF10;
    __IO uint32_t RSVD1[3];
    __IO uint32_t CANVAS_STAT0;
    __IO uint32_t CANVAS_STAT1;
    __IO uint32_t OL0_STAT;
    __IO uint32_t OL1_STAT;
    __IO uint32_t MEM_IF_STAT;
    __IO uint32_t PERF_CNT;
} LCD_IF_TypeDef;


/***************** Bit definition for LCD_IF_COMMAND register *****************/
#define LCD_IF_COMMAND_START_Pos        (0U)
#define LCD_IF_COMMAND_START_Msk        (0x1UL << LCD_IF_COMMAND_START_Pos)
#define LCD_IF_COMMAND_START            LCD_IF_COMMAND_START_Msk
#define LCD_IF_COMMAND_RESET_Pos        (1U)
#define LCD_IF_COMMAND_RESET_Msk        (0x1UL << LCD_IF_COMMAND_RESET_Pos)
#define LCD_IF_COMMAND_RESET            LCD_IF_COMMAND_RESET_Msk

/***************** Bit definition for LCD_IF_STATUS register ******************/
#define LCD_IF_STATUS_LCD_BUSY_Pos      (0U)
#define LCD_IF_STATUS_LCD_BUSY_Msk      (0x1UL << LCD_IF_STATUS_LCD_BUSY_Pos)
#define LCD_IF_STATUS_LCD_BUSY          LCD_IF_STATUS_LCD_BUSY_Msk
#define LCD_IF_STATUS_DPI_RUN_Pos       (1U)
#define LCD_IF_STATUS_DPI_RUN_Msk       (0x1UL << LCD_IF_STATUS_DPI_RUN_Pos)
#define LCD_IF_STATUS_DPI_RUN           LCD_IF_STATUS_DPI_RUN_Msk
#define LCD_IF_STATUS_JDI_PAR_RUN_Pos   (2U)
#define LCD_IF_STATUS_JDI_PAR_RUN_Msk   (0x1UL << LCD_IF_STATUS_JDI_PAR_RUN_Pos)
#define LCD_IF_STATUS_JDI_PAR_RUN       LCD_IF_STATUS_JDI_PAR_RUN_Msk

/******************* Bit definition for LCD_IF_IRQ register *******************/
#define LCD_IF_IRQ_EOF_STAT_Pos         (0U)
#define LCD_IF_IRQ_EOF_STAT_Msk         (0x1UL << LCD_IF_IRQ_EOF_STAT_Pos)
#define LCD_IF_IRQ_EOF_STAT             LCD_IF_IRQ_EOF_STAT_Msk
#define LCD_IF_IRQ_ICB_OF_STAT_Pos      (1U)
#define LCD_IF_IRQ_ICB_OF_STAT_Msk      (0x1UL << LCD_IF_IRQ_ICB_OF_STAT_Pos)
#define LCD_IF_IRQ_ICB_OF_STAT          LCD_IF_IRQ_ICB_OF_STAT_Msk
#define LCD_IF_IRQ_DPIL_INTR_STAT_Pos   (2U)
#define LCD_IF_IRQ_DPIL_INTR_STAT_Msk   (0x1UL << LCD_IF_IRQ_DPIL_INTR_STAT_Pos)
#define LCD_IF_IRQ_DPIL_INTR_STAT       LCD_IF_IRQ_DPIL_INTR_STAT_Msk
#define LCD_IF_IRQ_DPI_UDR_STAT_Pos     (3U)
#define LCD_IF_IRQ_DPI_UDR_STAT_Msk     (0x1UL << LCD_IF_IRQ_DPI_UDR_STAT_Pos)
#define LCD_IF_IRQ_DPI_UDR_STAT         LCD_IF_IRQ_DPI_UDR_STAT_Msk
#define LCD_IF_IRQ_JDI_PARL_INTR_STAT_Pos  (4U)
#define LCD_IF_IRQ_JDI_PARL_INTR_STAT_Msk  (0x1UL << LCD_IF_IRQ_JDI_PARL_INTR_STAT_Pos)
#define LCD_IF_IRQ_JDI_PARL_INTR_STAT   LCD_IF_IRQ_JDI_PARL_INTR_STAT_Msk
#define LCD_IF_IRQ_JDI_PAR_UDR_STAT_Pos  (5U)
#define LCD_IF_IRQ_JDI_PAR_UDR_STAT_Msk  (0x1UL << LCD_IF_IRQ_JDI_PAR_UDR_STAT_Pos)
#define LCD_IF_IRQ_JDI_PAR_UDR_STAT     LCD_IF_IRQ_JDI_PAR_UDR_STAT_Msk
#define LCD_IF_IRQ_LINE_DONE_STAT_Pos   (6U)
#define LCD_IF_IRQ_LINE_DONE_STAT_Msk   (0x1UL << LCD_IF_IRQ_LINE_DONE_STAT_Pos)
#define LCD_IF_IRQ_LINE_DONE_STAT       LCD_IF_IRQ_LINE_DONE_STAT_Msk
#define LCD_IF_IRQ_EOF_RAW_STAT_Pos     (16U)
#define LCD_IF_IRQ_EOF_RAW_STAT_Msk     (0x1UL << LCD_IF_IRQ_EOF_RAW_STAT_Pos)
#define LCD_IF_IRQ_EOF_RAW_STAT         LCD_IF_IRQ_EOF_RAW_STAT_Msk
#define LCD_IF_IRQ_ICB_OF_RAW_STAT_Pos  (17U)
#define LCD_IF_IRQ_ICB_OF_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_ICB_OF_RAW_STAT_Pos)
#define LCD_IF_IRQ_ICB_OF_RAW_STAT      LCD_IF_IRQ_ICB_OF_RAW_STAT_Msk
#define LCD_IF_IRQ_DPIL_INTR_RAW_STAT_Pos  (18U)
#define LCD_IF_IRQ_DPIL_INTR_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_DPIL_INTR_RAW_STAT_Pos)
#define LCD_IF_IRQ_DPIL_INTR_RAW_STAT   LCD_IF_IRQ_DPIL_INTR_RAW_STAT_Msk
#define LCD_IF_IRQ_DPI_UDR_RAW_STAT_Pos  (19U)
#define LCD_IF_IRQ_DPI_UDR_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_DPI_UDR_RAW_STAT_Pos)
#define LCD_IF_IRQ_DPI_UDR_RAW_STAT     LCD_IF_IRQ_DPI_UDR_RAW_STAT_Msk
#define LCD_IF_IRQ_JDI_PARL_INTR_RAW_STAT_Pos  (20U)
#define LCD_IF_IRQ_JDI_PARL_INTR_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_JDI_PARL_INTR_RAW_STAT_Pos)
#define LCD_IF_IRQ_JDI_PARL_INTR_RAW_STAT  LCD_IF_IRQ_JDI_PARL_INTR_RAW_STAT_Msk
#define LCD_IF_IRQ_JDI_PAR_UDR_RAW_STAT_Pos  (21U)
#define LCD_IF_IRQ_JDI_PAR_UDR_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_JDI_PAR_UDR_RAW_STAT_Pos)
#define LCD_IF_IRQ_JDI_PAR_UDR_RAW_STAT  LCD_IF_IRQ_JDI_PAR_UDR_RAW_STAT_Msk
#define LCD_IF_IRQ_LINE_DONE_RAW_STAT_Pos  (22U)
#define LCD_IF_IRQ_LINE_DONE_RAW_STAT_Msk  (0x1UL << LCD_IF_IRQ_LINE_DONE_RAW_STAT_Pos)
#define LCD_IF_IRQ_LINE_DONE_RAW_STAT   LCD_IF_IRQ_LINE_DONE_RAW_STAT_Msk

/***************** Bit definition for LCD_IF_SETTING register *****************/
#define LCD_IF_SETTING_EOF_MASK_Pos     (0U)
#define LCD_IF_SETTING_EOF_MASK_Msk     (0x1UL << LCD_IF_SETTING_EOF_MASK_Pos)
#define LCD_IF_SETTING_EOF_MASK         LCD_IF_SETTING_EOF_MASK_Msk
#define LCD_IF_SETTING_ICB_OF_MASK_Pos  (1U)
#define LCD_IF_SETTING_ICB_OF_MASK_Msk  (0x1UL << LCD_IF_SETTING_ICB_OF_MASK_Pos)
#define LCD_IF_SETTING_ICB_OF_MASK      LCD_IF_SETTING_ICB_OF_MASK_Msk
#define LCD_IF_SETTING_DPIL_INTR_MASK_Pos  (2U)
#define LCD_IF_SETTING_DPIL_INTR_MASK_Msk  (0x1UL << LCD_IF_SETTING_DPIL_INTR_MASK_Pos)
#define LCD_IF_SETTING_DPIL_INTR_MASK   LCD_IF_SETTING_DPIL_INTR_MASK_Msk
#define LCD_IF_SETTING_DPI_UDR_MASK_Pos  (3U)
#define LCD_IF_SETTING_DPI_UDR_MASK_Msk  (0x1UL << LCD_IF_SETTING_DPI_UDR_MASK_Pos)
#define LCD_IF_SETTING_DPI_UDR_MASK     LCD_IF_SETTING_DPI_UDR_MASK_Msk
#define LCD_IF_SETTING_JDI_PARL_INTR_MASK_Pos  (4U)
#define LCD_IF_SETTING_JDI_PARL_INTR_MASK_Msk  (0x1UL << LCD_IF_SETTING_JDI_PARL_INTR_MASK_Pos)
#define LCD_IF_SETTING_JDI_PARL_INTR_MASK  LCD_IF_SETTING_JDI_PARL_INTR_MASK_Msk
#define LCD_IF_SETTING_JDI_PAR_UDR_MASK_Pos  (5U)
#define LCD_IF_SETTING_JDI_PAR_UDR_MASK_Msk  (0x1UL << LCD_IF_SETTING_JDI_PAR_UDR_MASK_Pos)
#define LCD_IF_SETTING_JDI_PAR_UDR_MASK  LCD_IF_SETTING_JDI_PAR_UDR_MASK_Msk
#define LCD_IF_SETTING_LINE_DONE_MASK_Pos  (6U)
#define LCD_IF_SETTING_LINE_DONE_MASK_Msk  (0x1UL << LCD_IF_SETTING_LINE_DONE_MASK_Pos)
#define LCD_IF_SETTING_LINE_DONE_MASK   LCD_IF_SETTING_LINE_DONE_MASK_Msk
#define LCD_IF_SETTING_AUTO_GATE_EN_Pos  (8U)
#define LCD_IF_SETTING_AUTO_GATE_EN_Msk  (0x1UL << LCD_IF_SETTING_AUTO_GATE_EN_Pos)
#define LCD_IF_SETTING_AUTO_GATE_EN     LCD_IF_SETTING_AUTO_GATE_EN_Msk
#define LCD_IF_SETTING_LINE_DONE_NUM_Pos  (16U)
#define LCD_IF_SETTING_LINE_DONE_NUM_Msk  (0x7FFUL << LCD_IF_SETTING_LINE_DONE_NUM_Pos)
#define LCD_IF_SETTING_LINE_DONE_NUM    LCD_IF_SETTING_LINE_DONE_NUM_Msk

/************** Bit definition for LCD_IF_CANVAS_TL_POS register **************/
#define LCD_IF_CANVAS_TL_POS_X0_Pos     (0U)
#define LCD_IF_CANVAS_TL_POS_X0_Msk     (0x7FFUL << LCD_IF_CANVAS_TL_POS_X0_Pos)
#define LCD_IF_CANVAS_TL_POS_X0         LCD_IF_CANVAS_TL_POS_X0_Msk
#define LCD_IF_CANVAS_TL_POS_Y0_Pos     (16U)
#define LCD_IF_CANVAS_TL_POS_Y0_Msk     (0x7FFUL << LCD_IF_CANVAS_TL_POS_Y0_Pos)
#define LCD_IF_CANVAS_TL_POS_Y0         LCD_IF_CANVAS_TL_POS_Y0_Msk

/************** Bit definition for LCD_IF_CANVAS_BR_POS register **************/
#define LCD_IF_CANVAS_BR_POS_X1_Pos     (0U)
#define LCD_IF_CANVAS_BR_POS_X1_Msk     (0x7FFUL << LCD_IF_CANVAS_BR_POS_X1_Pos)
#define LCD_IF_CANVAS_BR_POS_X1         LCD_IF_CANVAS_BR_POS_X1_Msk
#define LCD_IF_CANVAS_BR_POS_Y1_Pos     (16U)
#define LCD_IF_CANVAS_BR_POS_Y1_Msk     (0x7FFUL << LCD_IF_CANVAS_BR_POS_Y1_Pos)
#define LCD_IF_CANVAS_BR_POS_Y1         LCD_IF_CANVAS_BR_POS_Y1_Msk

/**************** Bit definition for LCD_IF_CANVAS_BG register ****************/
#define LCD_IF_CANVAS_BG_BLUE_Pos       (0U)
#define LCD_IF_CANVAS_BG_BLUE_Msk       (0xFFUL << LCD_IF_CANVAS_BG_BLUE_Pos)
#define LCD_IF_CANVAS_BG_BLUE           LCD_IF_CANVAS_BG_BLUE_Msk
#define LCD_IF_CANVAS_BG_GREEN_Pos      (8U)
#define LCD_IF_CANVAS_BG_GREEN_Msk      (0xFFUL << LCD_IF_CANVAS_BG_GREEN_Pos)
#define LCD_IF_CANVAS_BG_GREEN          LCD_IF_CANVAS_BG_GREEN_Msk
#define LCD_IF_CANVAS_BG_RED_Pos        (16U)
#define LCD_IF_CANVAS_BG_RED_Msk        (0xFFUL << LCD_IF_CANVAS_BG_RED_Pos)
#define LCD_IF_CANVAS_BG_RED            LCD_IF_CANVAS_BG_RED_Msk
#define LCD_IF_CANVAS_BG_BG_BLENDING_BYPASS_Pos  (24U)
#define LCD_IF_CANVAS_BG_BG_BLENDING_BYPASS_Msk  (0x1UL << LCD_IF_CANVAS_BG_BG_BLENDING_BYPASS_Pos)
#define LCD_IF_CANVAS_BG_BG_BLENDING_BYPASS  LCD_IF_CANVAS_BG_BG_BLENDING_BYPASS_Msk
#define LCD_IF_CANVAS_BG_ALL_BLENDING_BYPASS_Pos  (25U)
#define LCD_IF_CANVAS_BG_ALL_BLENDING_BYPASS_Msk  (0x1UL << LCD_IF_CANVAS_BG_ALL_BLENDING_BYPASS_Pos)
#define LCD_IF_CANVAS_BG_ALL_BLENDING_BYPASS  LCD_IF_CANVAS_BG_ALL_BLENDING_BYPASS_Msk
#define LCD_IF_CANVAS_BG_LB_BYPASS_Pos  (26U)
#define LCD_IF_CANVAS_BG_LB_BYPASS_Msk  (0x1UL << LCD_IF_CANVAS_BG_LB_BYPASS_Pos)
#define LCD_IF_CANVAS_BG_LB_BYPASS      LCD_IF_CANVAS_BG_LB_BYPASS_Msk
#define LCD_IF_CANVAS_BG_H_MIRROR_Pos   (27U)
#define LCD_IF_CANVAS_BG_H_MIRROR_Msk   (0x1UL << LCD_IF_CANVAS_BG_H_MIRROR_Pos)
#define LCD_IF_CANVAS_BG_H_MIRROR       LCD_IF_CANVAS_BG_H_MIRROR_Msk

/************** Bit definition for LCD_IF_LAYER0_CONFIG register **************/
#define LCD_IF_LAYER0_CONFIG_FORMAT_Pos  (0U)
#define LCD_IF_LAYER0_CONFIG_FORMAT_Msk  (0x7UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT     LCD_IF_LAYER0_CONFIG_FORMAT_Msk
#define LCD_IF_LAYER0_CONFIG_FORMAT_RGB565    (0UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_RGB888    (1UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_ARGB8888  (2UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_ARGB8565  (3UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_RGB8332   (4UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_A8        (5UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_FORMAT_L8        (6UL << LCD_IF_LAYER0_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER0_CONFIG_ALPHA_SEL_Pos  (3U)
#define LCD_IF_LAYER0_CONFIG_ALPHA_SEL_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_ALPHA_SEL_Pos)
#define LCD_IF_LAYER0_CONFIG_ALPHA_SEL  LCD_IF_LAYER0_CONFIG_ALPHA_SEL_Msk
#define LCD_IF_LAYER0_CONFIG_ALPHA_Pos  (4U)
#define LCD_IF_LAYER0_CONFIG_ALPHA_Msk  (0xFFUL << LCD_IF_LAYER0_CONFIG_ALPHA_Pos)
#define LCD_IF_LAYER0_CONFIG_ALPHA      LCD_IF_LAYER0_CONFIG_ALPHA_Msk
#define LCD_IF_LAYER0_CONFIG_FILTER_EN_Pos  (12U)
#define LCD_IF_LAYER0_CONFIG_FILTER_EN_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_FILTER_EN_Pos)
#define LCD_IF_LAYER0_CONFIG_FILTER_EN  LCD_IF_LAYER0_CONFIG_FILTER_EN_Msk
#define LCD_IF_LAYER0_CONFIG_WIDTH_Pos  (13U)
#define LCD_IF_LAYER0_CONFIG_WIDTH_Msk  (0x1FFFUL << LCD_IF_LAYER0_CONFIG_WIDTH_Pos)
#define LCD_IF_LAYER0_CONFIG_WIDTH      LCD_IF_LAYER0_CONFIG_WIDTH_Msk
#define LCD_IF_LAYER0_CONFIG_PREFETCH_EN_Pos  (26U)
#define LCD_IF_LAYER0_CONFIG_PREFETCH_EN_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_PREFETCH_EN_Pos)
#define LCD_IF_LAYER0_CONFIG_PREFETCH_EN  LCD_IF_LAYER0_CONFIG_PREFETCH_EN_Msk
#define LCD_IF_LAYER0_CONFIG_LINE_FETCH_MODE_Pos  (27U)
#define LCD_IF_LAYER0_CONFIG_LINE_FETCH_MODE_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_LINE_FETCH_MODE_Pos)
#define LCD_IF_LAYER0_CONFIG_LINE_FETCH_MODE  LCD_IF_LAYER0_CONFIG_LINE_FETCH_MODE_Msk
#define LCD_IF_LAYER0_CONFIG_ACTIVE_Pos  (28U)
#define LCD_IF_LAYER0_CONFIG_ACTIVE_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_ACTIVE_Pos)
#define LCD_IF_LAYER0_CONFIG_ACTIVE     LCD_IF_LAYER0_CONFIG_ACTIVE_Msk
#define LCD_IF_LAYER0_CONFIG_ALPHA_BLEND_Pos  (29U)
#define LCD_IF_LAYER0_CONFIG_ALPHA_BLEND_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_ALPHA_BLEND_Pos)
#define LCD_IF_LAYER0_CONFIG_ALPHA_BLEND  LCD_IF_LAYER0_CONFIG_ALPHA_BLEND_Msk
#define LCD_IF_LAYER0_CONFIG_V_MIRROR_Pos  (30U)
#define LCD_IF_LAYER0_CONFIG_V_MIRROR_Msk  (0x1UL << LCD_IF_LAYER0_CONFIG_V_MIRROR_Pos)
#define LCD_IF_LAYER0_CONFIG_V_MIRROR   LCD_IF_LAYER0_CONFIG_V_MIRROR_Msk

/************** Bit definition for LCD_IF_LAYER0_TL_POS register **************/
#define LCD_IF_LAYER0_TL_POS_X0_Pos     (0U)
#define LCD_IF_LAYER0_TL_POS_X0_Msk     (0x7FFUL << LCD_IF_LAYER0_TL_POS_X0_Pos)
#define LCD_IF_LAYER0_TL_POS_X0         LCD_IF_LAYER0_TL_POS_X0_Msk
#define LCD_IF_LAYER0_TL_POS_Y0_Pos     (16U)
#define LCD_IF_LAYER0_TL_POS_Y0_Msk     (0x7FFUL << LCD_IF_LAYER0_TL_POS_Y0_Pos)
#define LCD_IF_LAYER0_TL_POS_Y0         LCD_IF_LAYER0_TL_POS_Y0_Msk

/************** Bit definition for LCD_IF_LAYER0_BR_POS register **************/
#define LCD_IF_LAYER0_BR_POS_X1_Pos     (0U)
#define LCD_IF_LAYER0_BR_POS_X1_Msk     (0x7FFUL << LCD_IF_LAYER0_BR_POS_X1_Pos)
#define LCD_IF_LAYER0_BR_POS_X1         LCD_IF_LAYER0_BR_POS_X1_Msk
#define LCD_IF_LAYER0_BR_POS_Y1_Pos     (16U)
#define LCD_IF_LAYER0_BR_POS_Y1_Msk     (0x7FFUL << LCD_IF_LAYER0_BR_POS_Y1_Pos)
#define LCD_IF_LAYER0_BR_POS_Y1         LCD_IF_LAYER0_BR_POS_Y1_Msk

/************** Bit definition for LCD_IF_LAYER0_FILTER register **************/
#define LCD_IF_LAYER0_FILTER_FILTER_B_Pos  (0U)
#define LCD_IF_LAYER0_FILTER_FILTER_B_Msk  (0xFFUL << LCD_IF_LAYER0_FILTER_FILTER_B_Pos)
#define LCD_IF_LAYER0_FILTER_FILTER_B   LCD_IF_LAYER0_FILTER_FILTER_B_Msk
#define LCD_IF_LAYER0_FILTER_FILTER_G_Pos  (8U)
#define LCD_IF_LAYER0_FILTER_FILTER_G_Msk  (0xFFUL << LCD_IF_LAYER0_FILTER_FILTER_G_Pos)
#define LCD_IF_LAYER0_FILTER_FILTER_G   LCD_IF_LAYER0_FILTER_FILTER_G_Msk
#define LCD_IF_LAYER0_FILTER_FILTER_R_Pos  (16U)
#define LCD_IF_LAYER0_FILTER_FILTER_R_Msk  (0xFFUL << LCD_IF_LAYER0_FILTER_FILTER_R_Pos)
#define LCD_IF_LAYER0_FILTER_FILTER_R   LCD_IF_LAYER0_FILTER_FILTER_R_Msk
#define LCD_IF_LAYER0_FILTER_FILTER_MASK_Pos  (24U)
#define LCD_IF_LAYER0_FILTER_FILTER_MASK_Msk  (0xFFUL << LCD_IF_LAYER0_FILTER_FILTER_MASK_Pos)
#define LCD_IF_LAYER0_FILTER_FILTER_MASK  LCD_IF_LAYER0_FILTER_FILTER_MASK_Msk

/*************** Bit definition for LCD_IF_LAYER0_SRC register ****************/
#define LCD_IF_LAYER0_SRC_ADDR_Pos      (0U)
#define LCD_IF_LAYER0_SRC_ADDR_Msk      (0xFFFFFFFFUL << LCD_IF_LAYER0_SRC_ADDR_Pos)
#define LCD_IF_LAYER0_SRC_ADDR          LCD_IF_LAYER0_SRC_ADDR_Msk

/*************** Bit definition for LCD_IF_LAYER0_FILL register ***************/
#define LCD_IF_LAYER0_FILL_BG_B_Pos     (0U)
#define LCD_IF_LAYER0_FILL_BG_B_Msk     (0xFFUL << LCD_IF_LAYER0_FILL_BG_B_Pos)
#define LCD_IF_LAYER0_FILL_BG_B         LCD_IF_LAYER0_FILL_BG_B_Msk
#define LCD_IF_LAYER0_FILL_BG_G_Pos     (8U)
#define LCD_IF_LAYER0_FILL_BG_G_Msk     (0xFFUL << LCD_IF_LAYER0_FILL_BG_G_Pos)
#define LCD_IF_LAYER0_FILL_BG_G         LCD_IF_LAYER0_FILL_BG_G_Msk
#define LCD_IF_LAYER0_FILL_BG_R_Pos     (16U)
#define LCD_IF_LAYER0_FILL_BG_R_Msk     (0xFFUL << LCD_IF_LAYER0_FILL_BG_R_Pos)
#define LCD_IF_LAYER0_FILL_BG_R         LCD_IF_LAYER0_FILL_BG_R_Msk
#define LCD_IF_LAYER0_FILL_BG_MODE_Pos  (24U)
#define LCD_IF_LAYER0_FILL_BG_MODE_Msk  (0x1UL << LCD_IF_LAYER0_FILL_BG_MODE_Pos)
#define LCD_IF_LAYER0_FILL_BG_MODE      LCD_IF_LAYER0_FILL_BG_MODE_Msk
#define LCD_IF_LAYER0_FILL_ENDIAN_Pos   (25U)
#define LCD_IF_LAYER0_FILL_ENDIAN_Msk   (0x1UL << LCD_IF_LAYER0_FILL_ENDIAN_Pos)
#define LCD_IF_LAYER0_FILL_ENDIAN       LCD_IF_LAYER0_FILL_ENDIAN_Msk

/************** Bit definition for LCD_IF_LAYER0_DECOMP register **************/
#define LCD_IF_LAYER0_DECOMP_ENABLE_Pos  (0U)
#define LCD_IF_LAYER0_DECOMP_ENABLE_Msk  (0x1UL << LCD_IF_LAYER0_DECOMP_ENABLE_Pos)
#define LCD_IF_LAYER0_DECOMP_ENABLE     LCD_IF_LAYER0_DECOMP_ENABLE_Msk
#define LCD_IF_LAYER0_DECOMP_TARGET_WORDS_Pos  (1U)
#define LCD_IF_LAYER0_DECOMP_TARGET_WORDS_Msk  (0xFFFUL << LCD_IF_LAYER0_DECOMP_TARGET_WORDS_Pos)
#define LCD_IF_LAYER0_DECOMP_TARGET_WORDS  LCD_IF_LAYER0_DECOMP_TARGET_WORDS_Msk
#define LCD_IF_LAYER0_DECOMP_COL_SIZE_Pos  (13U)
#define LCD_IF_LAYER0_DECOMP_COL_SIZE_Msk  (0x7FFUL << LCD_IF_LAYER0_DECOMP_COL_SIZE_Pos)
#define LCD_IF_LAYER0_DECOMP_COL_SIZE   LCD_IF_LAYER0_DECOMP_COL_SIZE_Msk

/*********** Bit definition for LCD_IF_LAYER0_DECOMP_CFG0 register ************/
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_HIGH_Pos  (0U)
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_HIGH_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_HIGH_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_HIGH  LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_HIGH_Msk
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_THRESHOLD_Pos  (4U)
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_THRESHOLD_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_THRESHOLD_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_THRESHOLD  LCD_IF_LAYER0_DECOMP_CFG0_EXTRA_THRESHOLD_Msk
#define LCD_IF_LAYER0_DECOMP_CFG0_USE_LOSSLESS_QIDX_Pos  (8U)
#define LCD_IF_LAYER0_DECOMP_CFG0_USE_LOSSLESS_QIDX_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG0_USE_LOSSLESS_QIDX_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_USE_LOSSLESS_QIDX  LCD_IF_LAYER0_DECOMP_CFG0_USE_LOSSLESS_QIDX_Msk
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX1_Pos  (12U)
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX1_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX1_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX1  LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX1_Msk
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX2_Pos  (16U)
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX2_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX2_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX2  LCD_IF_LAYER0_DECOMP_CFG0_LOSSLESS_QIDX2_Msk
#define LCD_IF_LAYER0_DECOMP_CFG0_CFG0_RESERVED_Pos  (20U)
#define LCD_IF_LAYER0_DECOMP_CFG0_CFG0_RESERVED_Msk  (0xFFFUL << LCD_IF_LAYER0_DECOMP_CFG0_CFG0_RESERVED_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG0_CFG0_RESERVED  LCD_IF_LAYER0_DECOMP_CFG0_CFG0_RESERVED_Msk

/*********** Bit definition for LCD_IF_LAYER0_DECOMP_CFG1 register ************/
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_WIDTH_Pos  (0U)
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_WIDTH_Msk  (0x1UL << LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_WIDTH_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_WIDTH  LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_WIDTH_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_DITHER_Pos  (1U)
#define LCD_IF_LAYER0_DECOMP_CFG1_DITHER_Msk  (0x1UL << LCD_IF_LAYER0_DECOMP_CFG1_DITHER_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_DITHER  LCD_IF_LAYER0_DECOMP_CFG1_DITHER_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_CFG1_RESERVED_Pos  (2U)
#define LCD_IF_LAYER0_DECOMP_CFG1_CFG1_RESERVED_Msk  (0x3FUL << LCD_IF_LAYER0_DECOMP_CFG1_CFG1_RESERVED_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_CFG1_RESERVED  LCD_IF_LAYER0_DECOMP_CFG1_CFG1_RESERVED_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_R_Pos  (8U)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_R_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_R_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_R  LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_R_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_G_Pos  (12U)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_G_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_G_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_G  LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_G_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_B_Pos  (16U)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_B_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_B_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_B  LCD_IF_LAYER0_DECOMP_CFG1_FAILOVER_BITS_B_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_LINE_MIN_QIDX_Pos  (20U)
#define LCD_IF_LAYER0_DECOMP_CFG1_LINE_MIN_QIDX_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_LINE_MIN_QIDX_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_LINE_MIN_QIDX  LCD_IF_LAYER0_DECOMP_CFG1_LINE_MIN_QIDX_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_MIN_QIDX_Pos  (24U)
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_MIN_QIDX_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_MIN_QIDX_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_MIN_QIDX  LCD_IF_LAYER0_DECOMP_CFG1_BLOCK_MIN_QIDX_Msk
#define LCD_IF_LAYER0_DECOMP_CFG1_EXTRA_LOW_Pos  (28U)
#define LCD_IF_LAYER0_DECOMP_CFG1_EXTRA_LOW_Msk  (0xFUL << LCD_IF_LAYER0_DECOMP_CFG1_EXTRA_LOW_Pos)
#define LCD_IF_LAYER0_DECOMP_CFG1_EXTRA_LOW  LCD_IF_LAYER0_DECOMP_CFG1_EXTRA_LOW_Msk

/*********** Bit definition for LCD_IF_LAYER0_DECOMP_STAT register ************/
#define LCD_IF_LAYER0_DECOMP_STAT_BUF_MAX_DEPTH_Pos  (0U)
#define LCD_IF_LAYER0_DECOMP_STAT_BUF_MAX_DEPTH_Msk  (0x7FUL << LCD_IF_LAYER0_DECOMP_STAT_BUF_MAX_DEPTH_Pos)
#define LCD_IF_LAYER0_DECOMP_STAT_BUF_MAX_DEPTH  LCD_IF_LAYER0_DECOMP_STAT_BUF_MAX_DEPTH_Msk

/************** Bit definition for LCD_IF_LAYER1_CONFIG register **************/
#define LCD_IF_LAYER1_CONFIG_FORMAT_Pos  (0U)
#define LCD_IF_LAYER1_CONFIG_FORMAT_Msk  (0x7UL << LCD_IF_LAYER1_CONFIG_FORMAT_Pos)
#define LCD_IF_LAYER1_CONFIG_FORMAT     LCD_IF_LAYER1_CONFIG_FORMAT_Msk
#define LCD_IF_LAYER1_CONFIG_ALPHA_SEL_Pos  (3U)
#define LCD_IF_LAYER1_CONFIG_ALPHA_SEL_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_ALPHA_SEL_Pos)
#define LCD_IF_LAYER1_CONFIG_ALPHA_SEL  LCD_IF_LAYER1_CONFIG_ALPHA_SEL_Msk
#define LCD_IF_LAYER1_CONFIG_ALPHA_Pos  (4U)
#define LCD_IF_LAYER1_CONFIG_ALPHA_Msk  (0xFFUL << LCD_IF_LAYER1_CONFIG_ALPHA_Pos)
#define LCD_IF_LAYER1_CONFIG_ALPHA      LCD_IF_LAYER1_CONFIG_ALPHA_Msk
#define LCD_IF_LAYER1_CONFIG_FILTER_EN_Pos  (12U)
#define LCD_IF_LAYER1_CONFIG_FILTER_EN_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_FILTER_EN_Pos)
#define LCD_IF_LAYER1_CONFIG_FILTER_EN  LCD_IF_LAYER1_CONFIG_FILTER_EN_Msk
#define LCD_IF_LAYER1_CONFIG_WIDTH_Pos  (13U)
#define LCD_IF_LAYER1_CONFIG_WIDTH_Msk  (0x1FFFUL << LCD_IF_LAYER1_CONFIG_WIDTH_Pos)
#define LCD_IF_LAYER1_CONFIG_WIDTH      LCD_IF_LAYER1_CONFIG_WIDTH_Msk
#define LCD_IF_LAYER1_CONFIG_PREFETCH_EN_Pos  (26U)
#define LCD_IF_LAYER1_CONFIG_PREFETCH_EN_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_PREFETCH_EN_Pos)
#define LCD_IF_LAYER1_CONFIG_PREFETCH_EN  LCD_IF_LAYER1_CONFIG_PREFETCH_EN_Msk
#define LCD_IF_LAYER1_CONFIG_LINE_FETCH_MODE_Pos  (27U)
#define LCD_IF_LAYER1_CONFIG_LINE_FETCH_MODE_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_LINE_FETCH_MODE_Pos)
#define LCD_IF_LAYER1_CONFIG_LINE_FETCH_MODE  LCD_IF_LAYER1_CONFIG_LINE_FETCH_MODE_Msk
#define LCD_IF_LAYER1_CONFIG_ACTIVE_Pos  (28U)
#define LCD_IF_LAYER1_CONFIG_ACTIVE_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_ACTIVE_Pos)
#define LCD_IF_LAYER1_CONFIG_ACTIVE     LCD_IF_LAYER1_CONFIG_ACTIVE_Msk
#define LCD_IF_LAYER1_CONFIG_ALPHA_BLEND_Pos  (29U)
#define LCD_IF_LAYER1_CONFIG_ALPHA_BLEND_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_ALPHA_BLEND_Pos)
#define LCD_IF_LAYER1_CONFIG_ALPHA_BLEND  LCD_IF_LAYER1_CONFIG_ALPHA_BLEND_Msk
#define LCD_IF_LAYER1_CONFIG_V_MIRROR_Pos  (30U)
#define LCD_IF_LAYER1_CONFIG_V_MIRROR_Msk  (0x1UL << LCD_IF_LAYER1_CONFIG_V_MIRROR_Pos)
#define LCD_IF_LAYER1_CONFIG_V_MIRROR   LCD_IF_LAYER1_CONFIG_V_MIRROR_Msk

/************** Bit definition for LCD_IF_LAYER1_TL_POS register **************/
#define LCD_IF_LAYER1_TL_POS_X0_Pos     (0U)
#define LCD_IF_LAYER1_TL_POS_X0_Msk     (0x7FFUL << LCD_IF_LAYER1_TL_POS_X0_Pos)
#define LCD_IF_LAYER1_TL_POS_X0         LCD_IF_LAYER1_TL_POS_X0_Msk
#define LCD_IF_LAYER1_TL_POS_Y0_Pos     (16U)
#define LCD_IF_LAYER1_TL_POS_Y0_Msk     (0x7FFUL << LCD_IF_LAYER1_TL_POS_Y0_Pos)
#define LCD_IF_LAYER1_TL_POS_Y0         LCD_IF_LAYER1_TL_POS_Y0_Msk

/************** Bit definition for LCD_IF_LAYER1_BR_POS register **************/
#define LCD_IF_LAYER1_BR_POS_X1_Pos     (0U)
#define LCD_IF_LAYER1_BR_POS_X1_Msk     (0x7FFUL << LCD_IF_LAYER1_BR_POS_X1_Pos)
#define LCD_IF_LAYER1_BR_POS_X1         LCD_IF_LAYER1_BR_POS_X1_Msk
#define LCD_IF_LAYER1_BR_POS_Y1_Pos     (16U)
#define LCD_IF_LAYER1_BR_POS_Y1_Msk     (0x7FFUL << LCD_IF_LAYER1_BR_POS_Y1_Pos)
#define LCD_IF_LAYER1_BR_POS_Y1         LCD_IF_LAYER1_BR_POS_Y1_Msk

/************** Bit definition for LCD_IF_LAYER1_FILTER register **************/
#define LCD_IF_LAYER1_FILTER_FILTER_B_Pos  (0U)
#define LCD_IF_LAYER1_FILTER_FILTER_B_Msk  (0xFFUL << LCD_IF_LAYER1_FILTER_FILTER_B_Pos)
#define LCD_IF_LAYER1_FILTER_FILTER_B   LCD_IF_LAYER1_FILTER_FILTER_B_Msk
#define LCD_IF_LAYER1_FILTER_FILTER_G_Pos  (8U)
#define LCD_IF_LAYER1_FILTER_FILTER_G_Msk  (0xFFUL << LCD_IF_LAYER1_FILTER_FILTER_G_Pos)
#define LCD_IF_LAYER1_FILTER_FILTER_G   LCD_IF_LAYER1_FILTER_FILTER_G_Msk
#define LCD_IF_LAYER1_FILTER_FILTER_R_Pos  (16U)
#define LCD_IF_LAYER1_FILTER_FILTER_R_Msk  (0xFFUL << LCD_IF_LAYER1_FILTER_FILTER_R_Pos)
#define LCD_IF_LAYER1_FILTER_FILTER_R   LCD_IF_LAYER1_FILTER_FILTER_R_Msk
#define LCD_IF_LAYER1_FILTER_FILTER_MASK_Pos  (24U)
#define LCD_IF_LAYER1_FILTER_FILTER_MASK_Msk  (0xFFUL << LCD_IF_LAYER1_FILTER_FILTER_MASK_Pos)
#define LCD_IF_LAYER1_FILTER_FILTER_MASK  LCD_IF_LAYER1_FILTER_FILTER_MASK_Msk

/*************** Bit definition for LCD_IF_LAYER1_SRC register ****************/
#define LCD_IF_LAYER1_SRC_ADDR_Pos      (0U)
#define LCD_IF_LAYER1_SRC_ADDR_Msk      (0xFFFFFFFFUL << LCD_IF_LAYER1_SRC_ADDR_Pos)
#define LCD_IF_LAYER1_SRC_ADDR          LCD_IF_LAYER1_SRC_ADDR_Msk

/*************** Bit definition for LCD_IF_LAYER1_FILL register ***************/
#define LCD_IF_LAYER1_FILL_BG_B_Pos     (0U)
#define LCD_IF_LAYER1_FILL_BG_B_Msk     (0xFFUL << LCD_IF_LAYER1_FILL_BG_B_Pos)
#define LCD_IF_LAYER1_FILL_BG_B         LCD_IF_LAYER1_FILL_BG_B_Msk
#define LCD_IF_LAYER1_FILL_BG_G_Pos     (8U)
#define LCD_IF_LAYER1_FILL_BG_G_Msk     (0xFFUL << LCD_IF_LAYER1_FILL_BG_G_Pos)
#define LCD_IF_LAYER1_FILL_BG_G         LCD_IF_LAYER1_FILL_BG_G_Msk
#define LCD_IF_LAYER1_FILL_BG_R_Pos     (16U)
#define LCD_IF_LAYER1_FILL_BG_R_Msk     (0xFFUL << LCD_IF_LAYER1_FILL_BG_R_Pos)
#define LCD_IF_LAYER1_FILL_BG_R         LCD_IF_LAYER1_FILL_BG_R_Msk
#define LCD_IF_LAYER1_FILL_BG_MODE_Pos  (24U)
#define LCD_IF_LAYER1_FILL_BG_MODE_Msk  (0x1UL << LCD_IF_LAYER1_FILL_BG_MODE_Pos)
#define LCD_IF_LAYER1_FILL_BG_MODE      LCD_IF_LAYER1_FILL_BG_MODE_Msk
#define LCD_IF_LAYER1_FILL_ENDIAN_Pos   (25U)
#define LCD_IF_LAYER1_FILL_ENDIAN_Msk   (0x1UL << LCD_IF_LAYER1_FILL_ENDIAN_Pos)
#define LCD_IF_LAYER1_FILL_ENDIAN       LCD_IF_LAYER1_FILL_ENDIAN_Msk

/*************** Bit definition for LCD_IF_DITHER_CONF register ***************/
#define LCD_IF_DITHER_CONF_EN_Pos       (0U)
#define LCD_IF_DITHER_CONF_EN_Msk       (0x1UL << LCD_IF_DITHER_CONF_EN_Pos)
#define LCD_IF_DITHER_CONF_EN           LCD_IF_DITHER_CONF_EN_Msk
#define LCD_IF_DITHER_CONF_W_B_Pos      (1U)
#define LCD_IF_DITHER_CONF_W_B_Msk      (0x7UL << LCD_IF_DITHER_CONF_W_B_Pos)
#define LCD_IF_DITHER_CONF_W_B          LCD_IF_DITHER_CONF_W_B_Msk
#define LCD_IF_DITHER_CONF_W_G_Pos      (4U)
#define LCD_IF_DITHER_CONF_W_G_Msk      (0x7UL << LCD_IF_DITHER_CONF_W_G_Pos)
#define LCD_IF_DITHER_CONF_W_G          LCD_IF_DITHER_CONF_W_G_Msk
#define LCD_IF_DITHER_CONF_W_R_Pos      (7U)
#define LCD_IF_DITHER_CONF_W_R_Msk      (0x7UL << LCD_IF_DITHER_CONF_W_R_Pos)
#define LCD_IF_DITHER_CONF_W_R          LCD_IF_DITHER_CONF_W_R_Msk
#define LCD_IF_DITHER_CONF_LFSR_LOAD_SEL_Pos  (10U)
#define LCD_IF_DITHER_CONF_LFSR_LOAD_SEL_Msk  (0x3UL << LCD_IF_DITHER_CONF_LFSR_LOAD_SEL_Pos)
#define LCD_IF_DITHER_CONF_LFSR_LOAD_SEL  LCD_IF_DITHER_CONF_LFSR_LOAD_SEL_Msk
#define LCD_IF_DITHER_CONF_LFSR_LOAD_Pos  (12U)
#define LCD_IF_DITHER_CONF_LFSR_LOAD_Msk  (0x1UL << LCD_IF_DITHER_CONF_LFSR_LOAD_Pos)
#define LCD_IF_DITHER_CONF_LFSR_LOAD    LCD_IF_DITHER_CONF_LFSR_LOAD_Msk

/*************** Bit definition for LCD_IF_DITHER_LFSR register ***************/
#define LCD_IF_DITHER_LFSR_INIT_VAL_Pos  (0U)
#define LCD_IF_DITHER_LFSR_INIT_VAL_Msk  (0xFFFFFFFFUL << LCD_IF_DITHER_LFSR_INIT_VAL_Pos)
#define LCD_IF_DITHER_LFSR_INIT_VAL     LCD_IF_DITHER_LFSR_INIT_VAL_Msk

/**************** Bit definition for LCD_IF_LCD_CONF register *****************/
#define LCD_IF_LCD_CONF_TARGET_LCD_Pos  (0U)
#define LCD_IF_LCD_CONF_TARGET_LCD_Msk  (0x3UL << LCD_IF_LCD_CONF_TARGET_LCD_Pos)
#define LCD_IF_LCD_CONF_TARGET_LCD      LCD_IF_LCD_CONF_TARGET_LCD_Msk
#define LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos  (2U)
#define LCD_IF_LCD_CONF_LCD_INTF_SEL_Msk  (0x7UL << LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos)
#define LCD_IF_LCD_CONF_LCD_INTF_SEL    LCD_IF_LCD_CONF_LCD_INTF_SEL_Msk
#define LCD_IF_LCD_CONF_DBI_LCD_SEL     (0x0UL << LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos)
#define LCD_IF_LCD_CONF_SPI_LCD_SEL     (0x1UL << LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos)
#define LCD_IF_LCD_CONF_DSI_LCD_SEL     (0x2UL << LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos)
#define LCD_IF_LCD_CONF_DPI_LCD_SEL     (0x3UL << LCD_IF_LCD_CONF_LCD_INTF_SEL_Pos)
#define LCD_IF_LCD_CONF_LCD_FORMAT_Pos  (5U)
#define LCD_IF_LCD_CONF_LCD_FORMAT_Msk  (0x7UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)
#define LCD_IF_LCD_CONF_LCD_FORMAT      LCD_IF_LCD_CONF_LCD_FORMAT_Msk
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB332            (0UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //8bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_8BIT_RGB565       (1UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //8bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB444            (2UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //16bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB565            (3UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //16bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB666            (4UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //24bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB888            (5UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //24bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB888_OVER16BUS  (6UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //16bit
#define LCD_IF_LCD_CONF_LCD_FORMAT_RGB888_OVER8BUS   (7UL << LCD_IF_LCD_CONF_LCD_FORMAT_Pos)  //8bit
#define LCD_IF_LCD_CONF_AHB_FORMAT_Pos  (8U)
#define LCD_IF_LCD_CONF_AHB_FORMAT_Msk  (0x3UL << LCD_IF_LCD_CONF_AHB_FORMAT_Pos)
#define LCD_IF_LCD_CONF_AHB_FORMAT      LCD_IF_LCD_CONF_AHB_FORMAT_Msk
#define LCD_IF_LCD_CONF_SPI_LCD_FORMAT_Pos  (10U)
#define LCD_IF_LCD_CONF_SPI_LCD_FORMAT_Msk  (0x3UL << LCD_IF_LCD_CONF_SPI_LCD_FORMAT_Pos)
#define LCD_IF_LCD_CONF_SPI_LCD_FORMAT  LCD_IF_LCD_CONF_SPI_LCD_FORMAT_Msk
#define LCD_IF_LCD_CONF_DPI_LCD_FORMAT_Pos  (12U)
#define LCD_IF_LCD_CONF_DPI_LCD_FORMAT_Msk  (0x7UL << LCD_IF_LCD_CONF_DPI_LCD_FORMAT_Pos)
#define LCD_IF_LCD_CONF_DPI_LCD_FORMAT  LCD_IF_LCD_CONF_DPI_LCD_FORMAT_Msk
#define LCD_IF_LCD_CONF_JDI_SER_FORMAT_Pos  (15U)
#define LCD_IF_LCD_CONF_JDI_SER_FORMAT_Msk  (0x3UL << LCD_IF_LCD_CONF_JDI_SER_FORMAT_Pos)
#define LCD_IF_LCD_CONF_JDI_SER_FORMAT  LCD_IF_LCD_CONF_JDI_SER_FORMAT_Msk
#define LCD_IF_LCD_CONF_DIRECT_INTF_EN_Pos  (17U)
#define LCD_IF_LCD_CONF_DIRECT_INTF_EN_Msk  (0x1UL << LCD_IF_LCD_CONF_DIRECT_INTF_EN_Pos)
#define LCD_IF_LCD_CONF_DIRECT_INTF_EN  LCD_IF_LCD_CONF_DIRECT_INTF_EN_Msk
#define LCD_IF_LCD_CONF_ENDIAN_Pos      (18U)
#define LCD_IF_LCD_CONF_ENDIAN_Msk      (0x1UL << LCD_IF_LCD_CONF_ENDIAN_Pos)
#define LCD_IF_LCD_CONF_ENDIAN          LCD_IF_LCD_CONF_ENDIAN_Msk

/*************** Bit definition for LCD_IF_LCD_IF_CONF register ***************/
#define LCD_IF_LCD_IF_CONF_TAS_Pos      (0U)
#define LCD_IF_LCD_IF_CONF_TAS_Msk      (0x7UL << LCD_IF_LCD_IF_CONF_TAS_Pos)
#define LCD_IF_LCD_IF_CONF_TAS          LCD_IF_LCD_IF_CONF_TAS_Msk
#define LCD_IF_LCD_IF_CONF_TAH_Pos      (3U)
#define LCD_IF_LCD_IF_CONF_TAH_Msk      (0x7UL << LCD_IF_LCD_IF_CONF_TAH_Pos)
#define LCD_IF_LCD_IF_CONF_TAH          LCD_IF_LCD_IF_CONF_TAH_Msk
#define LCD_IF_LCD_IF_CONF_PWL_Pos      (6U)
#define LCD_IF_LCD_IF_CONF_PWL_Msk      (0x3FUL << LCD_IF_LCD_IF_CONF_PWL_Pos)
#define LCD_IF_LCD_IF_CONF_PWL          LCD_IF_LCD_IF_CONF_PWL_Msk
#define LCD_IF_LCD_IF_CONF_PWH_Pos      (12U)
#define LCD_IF_LCD_IF_CONF_PWH_Msk      (0x3FUL << LCD_IF_LCD_IF_CONF_PWH_Pos)
#define LCD_IF_LCD_IF_CONF_PWH          LCD_IF_LCD_IF_CONF_PWH_Msk
#define LCD_IF_LCD_IF_CONF_CS0_POL_Pos  (18U)
#define LCD_IF_LCD_IF_CONF_CS0_POL_Msk  (0x1UL << LCD_IF_LCD_IF_CONF_CS0_POL_Pos)
#define LCD_IF_LCD_IF_CONF_CS0_POL      LCD_IF_LCD_IF_CONF_CS0_POL_Msk
#define LCD_IF_LCD_IF_CONF_CS1_POL_Pos  (19U)
#define LCD_IF_LCD_IF_CONF_CS1_POL_Msk  (0x1UL << LCD_IF_LCD_IF_CONF_CS1_POL_Pos)
#define LCD_IF_LCD_IF_CONF_CS1_POL      LCD_IF_LCD_IF_CONF_CS1_POL_Msk
#define LCD_IF_LCD_IF_CONF_RS_POL_Pos   (20U)
#define LCD_IF_LCD_IF_CONF_RS_POL_Msk   (0x1UL << LCD_IF_LCD_IF_CONF_RS_POL_Pos)
#define LCD_IF_LCD_IF_CONF_RS_POL       LCD_IF_LCD_IF_CONF_RS_POL_Msk
#define LCD_IF_LCD_IF_CONF_WR_POL_Pos   (21U)
#define LCD_IF_LCD_IF_CONF_WR_POL_Msk   (0x1UL << LCD_IF_LCD_IF_CONF_WR_POL_Pos)
#define LCD_IF_LCD_IF_CONF_WR_POL       LCD_IF_LCD_IF_CONF_WR_POL_Msk
#define LCD_IF_LCD_IF_CONF_RD_POL_Pos   (22U)
#define LCD_IF_LCD_IF_CONF_RD_POL_Msk   (0x1UL << LCD_IF_LCD_IF_CONF_RD_POL_Pos)
#define LCD_IF_LCD_IF_CONF_RD_POL       LCD_IF_LCD_IF_CONF_RD_POL_Msk
#define LCD_IF_LCD_IF_CONF_LCD_RSTB_Pos  (23U)
#define LCD_IF_LCD_IF_CONF_LCD_RSTB_Msk  (0x1UL << LCD_IF_LCD_IF_CONF_LCD_RSTB_Pos)
#define LCD_IF_LCD_IF_CONF_LCD_RSTB     LCD_IF_LCD_IF_CONF_LCD_RSTB_Msk
#define LCD_IF_LCD_IF_CONF_DO_DLY_SET_Pos  (24U)
#define LCD_IF_LCD_IF_CONF_DO_DLY_SET_Msk  (0x1UL << LCD_IF_LCD_IF_CONF_DO_DLY_SET_Pos)
#define LCD_IF_LCD_IF_CONF_DO_DLY_SET   LCD_IF_LCD_IF_CONF_DO_DLY_SET_Msk
#define LCD_IF_LCD_IF_CONF_CTRL_DLY_SET_Pos  (25U)
#define LCD_IF_LCD_IF_CONF_CTRL_DLY_SET_Msk  (0x1UL << LCD_IF_LCD_IF_CONF_CTRL_DLY_SET_Pos)
#define LCD_IF_LCD_IF_CONF_CTRL_DLY_SET  LCD_IF_LCD_IF_CONF_CTRL_DLY_SET_Msk

/***************** Bit definition for LCD_IF_LCD_MEM register *****************/
#define LCD_IF_LCD_MEM_ADDR_Pos         (0U)
#define LCD_IF_LCD_MEM_ADDR_Msk         (0xFFFFFFFFUL << LCD_IF_LCD_MEM_ADDR_Pos)
#define LCD_IF_LCD_MEM_ADDR             LCD_IF_LCD_MEM_ADDR_Msk

/*************** Bit definition for LCD_IF_LCD_O_WIDTH register ***************/
#define LCD_IF_LCD_O_WIDTH_OFFSET_Pos   (0U)
#define LCD_IF_LCD_O_WIDTH_OFFSET_Msk   (0xFFFFUL << LCD_IF_LCD_O_WIDTH_OFFSET_Pos)
#define LCD_IF_LCD_O_WIDTH_OFFSET       LCD_IF_LCD_O_WIDTH_OFFSET_Msk

/*************** Bit definition for LCD_IF_LCD_SINGLE register ****************/
#define LCD_IF_LCD_SINGLE_TYPE_Pos      (0U)
#define LCD_IF_LCD_SINGLE_TYPE_Msk      (0x1UL << LCD_IF_LCD_SINGLE_TYPE_Pos)
#define LCD_IF_LCD_SINGLE_TYPE          LCD_IF_LCD_SINGLE_TYPE_Msk
#define LCD_IF_LCD_SINGLE_WR_TRIG_Pos   (1U)
#define LCD_IF_LCD_SINGLE_WR_TRIG_Msk   (0x1UL << LCD_IF_LCD_SINGLE_WR_TRIG_Pos)
#define LCD_IF_LCD_SINGLE_WR_TRIG       LCD_IF_LCD_SINGLE_WR_TRIG_Msk
#define LCD_IF_LCD_SINGLE_RD_TRIG_Pos   (2U)
#define LCD_IF_LCD_SINGLE_RD_TRIG_Msk   (0x1UL << LCD_IF_LCD_SINGLE_RD_TRIG_Pos)
#define LCD_IF_LCD_SINGLE_RD_TRIG       LCD_IF_LCD_SINGLE_RD_TRIG_Msk
#define LCD_IF_LCD_SINGLE_LCD_BUSY_Pos  (3U)
#define LCD_IF_LCD_SINGLE_LCD_BUSY_Msk  (0x1UL << LCD_IF_LCD_SINGLE_LCD_BUSY_Pos)
#define LCD_IF_LCD_SINGLE_LCD_BUSY      LCD_IF_LCD_SINGLE_LCD_BUSY_Msk

/***************** Bit definition for LCD_IF_LCD_WR register ******************/
#define LCD_IF_LCD_WR_DATA_Pos          (0U)
#define LCD_IF_LCD_WR_DATA_Msk          (0xFFFFFFFFUL << LCD_IF_LCD_WR_DATA_Pos)
#define LCD_IF_LCD_WR_DATA              LCD_IF_LCD_WR_DATA_Msk

/***************** Bit definition for LCD_IF_LCD_RD register ******************/
#define LCD_IF_LCD_RD_DATA_Pos          (0U)
#define LCD_IF_LCD_RD_DATA_Msk          (0xFFFFFFFFUL << LCD_IF_LCD_RD_DATA_Pos)
#define LCD_IF_LCD_RD_DATA              LCD_IF_LCD_RD_DATA_Msk

/*************** Bit definition for LCD_IF_SPI_IF_CONF register ***************/
#define LCD_IF_SPI_IF_CONF_WAIT_CYCLE_Pos  (0U)
#define LCD_IF_SPI_IF_CONF_WAIT_CYCLE_Msk  (0x3FUL << LCD_IF_SPI_IF_CONF_WAIT_CYCLE_Pos)
#define LCD_IF_SPI_IF_CONF_WAIT_CYCLE   LCD_IF_SPI_IF_CONF_WAIT_CYCLE_Msk
#define LCD_IF_SPI_IF_CONF_CLK_DIV_Pos  (6U)
#define LCD_IF_SPI_IF_CONF_CLK_DIV_Msk  (0xFFUL << LCD_IF_SPI_IF_CONF_CLK_DIV_Pos)
#define LCD_IF_SPI_IF_CONF_CLK_DIV      LCD_IF_SPI_IF_CONF_CLK_DIV_Msk
#define LCD_IF_SPI_IF_CONF_DUMMY_CYCLE_Pos  (14U)
#define LCD_IF_SPI_IF_CONF_DUMMY_CYCLE_Msk  (0x7UL << LCD_IF_SPI_IF_CONF_DUMMY_CYCLE_Pos)
#define LCD_IF_SPI_IF_CONF_DUMMY_CYCLE  LCD_IF_SPI_IF_CONF_DUMMY_CYCLE_Msk
#define LCD_IF_SPI_IF_CONF_LINE_Pos     (17U)
#define LCD_IF_SPI_IF_CONF_LINE_Msk     (0x7UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_LINE         LCD_IF_SPI_IF_CONF_LINE_Msk
#define LCD_IF_SPI_IF_CONF_4LINE                   (0UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_4LINE_2_DATA_LINE       (1UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_4LINE_4_DATA_LINE       (2UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_3LINE                   (4UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_3LINE_2_DATA_LINE       (5UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_3LINE_4_DATA_LINE       (6UL << LCD_IF_SPI_IF_CONF_LINE_Pos)
#define LCD_IF_SPI_IF_CONF_RD_LEN_Pos   (20U)
#define LCD_IF_SPI_IF_CONF_RD_LEN_Msk   (0x3UL << LCD_IF_SPI_IF_CONF_RD_LEN_Pos)
#define LCD_IF_SPI_IF_CONF_RD_LEN       LCD_IF_SPI_IF_CONF_RD_LEN_Msk
#define LCD_IF_SPI_IF_CONF_WR_LEN_Pos   (22U)
#define LCD_IF_SPI_IF_CONF_WR_LEN_Msk   (0x3UL << LCD_IF_SPI_IF_CONF_WR_LEN_Pos)
#define LCD_IF_SPI_IF_CONF_WR_LEN       LCD_IF_SPI_IF_CONF_WR_LEN_Msk
#define LCD_IF_SPI_IF_CONF_SPI_RD_MODE_Pos  (24U)
#define LCD_IF_SPI_IF_CONF_SPI_RD_MODE_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_RD_MODE_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_RD_MODE  LCD_IF_SPI_IF_CONF_SPI_RD_MODE_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CLK_AUTO_DIS_Pos  (25U)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_AUTO_DIS_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CLK_AUTO_DIS_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_AUTO_DIS  LCD_IF_SPI_IF_CONF_SPI_CLK_AUTO_DIS_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CS_NO_IDLE_Pos  (26U)
#define LCD_IF_SPI_IF_CONF_SPI_CS_NO_IDLE_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CS_NO_IDLE_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CS_NO_IDLE  LCD_IF_SPI_IF_CONF_SPI_CS_NO_IDLE_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CS_AUTO_DIS_Pos  (27U)
#define LCD_IF_SPI_IF_CONF_SPI_CS_AUTO_DIS_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CS_AUTO_DIS_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CS_AUTO_DIS  LCD_IF_SPI_IF_CONF_SPI_CS_AUTO_DIS_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CS_POL_Pos  (28U)
#define LCD_IF_SPI_IF_CONF_SPI_CS_POL_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CS_POL_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CS_POL   LCD_IF_SPI_IF_CONF_SPI_CS_POL_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CLK_POL_Pos  (29U)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_POL_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CLK_POL_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_POL  LCD_IF_SPI_IF_CONF_SPI_CLK_POL_Msk
#define LCD_IF_SPI_IF_CONF_SPI_CLK_INIT_Pos  (30U)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_INIT_Msk  (0x1UL << LCD_IF_SPI_IF_CONF_SPI_CLK_INIT_Pos)
#define LCD_IF_SPI_IF_CONF_SPI_CLK_INIT  LCD_IF_SPI_IF_CONF_SPI_CLK_INIT_Msk

/***************** Bit definition for LCD_IF_TE_CONF register *****************/
#define LCD_IF_TE_CONF_ENABLE_Pos       (0U)
#define LCD_IF_TE_CONF_ENABLE_Msk       (0x1UL << LCD_IF_TE_CONF_ENABLE_Pos)
#define LCD_IF_TE_CONF_ENABLE           LCD_IF_TE_CONF_ENABLE_Msk
#define LCD_IF_TE_CONF_FMARK_POL_Pos    (1U)
#define LCD_IF_TE_CONF_FMARK_POL_Msk    (0x1UL << LCD_IF_TE_CONF_FMARK_POL_Pos)
#define LCD_IF_TE_CONF_FMARK_POL        LCD_IF_TE_CONF_FMARK_POL_Msk
#define LCD_IF_TE_CONF_MODE_Pos         (2U)
#define LCD_IF_TE_CONF_MODE_Msk         (0x1UL << LCD_IF_TE_CONF_MODE_Pos)
#define LCD_IF_TE_CONF_MODE             LCD_IF_TE_CONF_MODE_Msk
#define LCD_IF_TE_CONF_VSYNC_DET_CNT_Pos  (3U)
#define LCD_IF_TE_CONF_VSYNC_DET_CNT_Msk  (0xFFFFUL << LCD_IF_TE_CONF_VSYNC_DET_CNT_Pos)
#define LCD_IF_TE_CONF_VSYNC_DET_CNT    LCD_IF_TE_CONF_VSYNC_DET_CNT_Msk
#define LCD_IF_TE_CONF_FMARK_MODE_Pos   (19U)
#define LCD_IF_TE_CONF_FMARK_MODE_Msk   (0x1UL << LCD_IF_TE_CONF_FMARK_MODE_Pos)
#define LCD_IF_TE_CONF_FMARK_MODE       LCD_IF_TE_CONF_FMARK_MODE_Msk
#define LCD_IF_TE_CONF_FMARK_SOURCE_Pos  (20U)
#define LCD_IF_TE_CONF_FMARK_SOURCE_Msk  (0x1UL << LCD_IF_TE_CONF_FMARK_SOURCE_Pos)
#define LCD_IF_TE_CONF_FMARK_SOURCE     LCD_IF_TE_CONF_FMARK_SOURCE_Msk

/**************** Bit definition for LCD_IF_TE_CONF2 register *****************/
#define LCD_IF_TE_CONF2_DLY_CNT_Pos     (0U)
#define LCD_IF_TE_CONF2_DLY_CNT_Msk     (0xFFFFFFFFUL << LCD_IF_TE_CONF2_DLY_CNT_Pos)
#define LCD_IF_TE_CONF2_DLY_CNT         LCD_IF_TE_CONF2_DLY_CNT_Msk

/************** Bit definition for LCD_IF_DPI_IF_CONF1 register ***************/
#define LCD_IF_DPI_IF_CONF1_VSH_Pos     (0U)
#define LCD_IF_DPI_IF_CONF1_VSH_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF1_VSH_Pos)
#define LCD_IF_DPI_IF_CONF1_VSH         LCD_IF_DPI_IF_CONF1_VSH_Msk
#define LCD_IF_DPI_IF_CONF1_HSW_Pos     (16U)
#define LCD_IF_DPI_IF_CONF1_HSW_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF1_HSW_Pos)
#define LCD_IF_DPI_IF_CONF1_HSW         LCD_IF_DPI_IF_CONF1_HSW_Msk

/************** Bit definition for LCD_IF_DPI_IF_CONF2 register ***************/
#define LCD_IF_DPI_IF_CONF2_VBP_Pos     (0U)
#define LCD_IF_DPI_IF_CONF2_VBP_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF2_VBP_Pos)
#define LCD_IF_DPI_IF_CONF2_VBP         LCD_IF_DPI_IF_CONF2_VBP_Msk
#define LCD_IF_DPI_IF_CONF2_HBP_Pos     (16U)
#define LCD_IF_DPI_IF_CONF2_HBP_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF2_HBP_Pos)
#define LCD_IF_DPI_IF_CONF2_HBP         LCD_IF_DPI_IF_CONF2_HBP_Msk

/************** Bit definition for LCD_IF_DPI_IF_CONF3 register ***************/
#define LCD_IF_DPI_IF_CONF3_VFP_Pos     (0U)
#define LCD_IF_DPI_IF_CONF3_VFP_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF3_VFP_Pos)
#define LCD_IF_DPI_IF_CONF3_VFP         LCD_IF_DPI_IF_CONF3_VFP_Msk
#define LCD_IF_DPI_IF_CONF3_HFP_Pos     (16U)
#define LCD_IF_DPI_IF_CONF3_HFP_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF3_HFP_Pos)
#define LCD_IF_DPI_IF_CONF3_HFP         LCD_IF_DPI_IF_CONF3_HFP_Msk

/************** Bit definition for LCD_IF_DPI_IF_CONF4 register ***************/
#define LCD_IF_DPI_IF_CONF4_VAH_Pos     (0U)
#define LCD_IF_DPI_IF_CONF4_VAH_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF4_VAH_Pos)
#define LCD_IF_DPI_IF_CONF4_VAH         LCD_IF_DPI_IF_CONF4_VAH_Msk
#define LCD_IF_DPI_IF_CONF4_HAW_Pos     (16U)
#define LCD_IF_DPI_IF_CONF4_HAW_Msk     (0x7FFUL << LCD_IF_DPI_IF_CONF4_HAW_Pos)
#define LCD_IF_DPI_IF_CONF4_HAW         LCD_IF_DPI_IF_CONF4_HAW_Msk

/************** Bit definition for LCD_IF_DPI_IF_CONF5 register ***************/
#define LCD_IF_DPI_IF_CONF5_PCLK_DIV_Pos  (0U)
#define LCD_IF_DPI_IF_CONF5_PCLK_DIV_Msk  (0xFFUL << LCD_IF_DPI_IF_CONF5_PCLK_DIV_Pos)
#define LCD_IF_DPI_IF_CONF5_PCLK_DIV    LCD_IF_DPI_IF_CONF5_PCLK_DIV_Msk
#define LCD_IF_DPI_IF_CONF5_PCLKPOL_Pos  (8U)
#define LCD_IF_DPI_IF_CONF5_PCLKPOL_Msk  (0x1UL << LCD_IF_DPI_IF_CONF5_PCLKPOL_Pos)
#define LCD_IF_DPI_IF_CONF5_PCLKPOL     LCD_IF_DPI_IF_CONF5_PCLKPOL_Msk
#define LCD_IF_DPI_IF_CONF5_DEPOL_Pos   (9U)
#define LCD_IF_DPI_IF_CONF5_DEPOL_Msk   (0x1UL << LCD_IF_DPI_IF_CONF5_DEPOL_Pos)
#define LCD_IF_DPI_IF_CONF5_DEPOL       LCD_IF_DPI_IF_CONF5_DEPOL_Msk
#define LCD_IF_DPI_IF_CONF5_VSPOL_Pos   (10U)
#define LCD_IF_DPI_IF_CONF5_VSPOL_Msk   (0x1UL << LCD_IF_DPI_IF_CONF5_VSPOL_Pos)
#define LCD_IF_DPI_IF_CONF5_VSPOL       LCD_IF_DPI_IF_CONF5_VSPOL_Msk
#define LCD_IF_DPI_IF_CONF5_HSPOL_Pos   (11U)
#define LCD_IF_DPI_IF_CONF5_HSPOL_Msk   (0x1UL << LCD_IF_DPI_IF_CONF5_HSPOL_Pos)
#define LCD_IF_DPI_IF_CONF5_HSPOL       LCD_IF_DPI_IF_CONF5_HSPOL_Msk
#define LCD_IF_DPI_IF_CONF5_INT_LINE_NUM_Pos  (12U)
#define LCD_IF_DPI_IF_CONF5_INT_LINE_NUM_Msk  (0x7FFUL << LCD_IF_DPI_IF_CONF5_INT_LINE_NUM_Pos)
#define LCD_IF_DPI_IF_CONF5_INT_LINE_NUM  LCD_IF_DPI_IF_CONF5_INT_LINE_NUM_Msk
#define LCD_IF_DPI_IF_CONF5_CLK_FORCE_ON_Pos  (23U)
#define LCD_IF_DPI_IF_CONF5_CLK_FORCE_ON_Msk  (0x1UL << LCD_IF_DPI_IF_CONF5_CLK_FORCE_ON_Pos)
#define LCD_IF_DPI_IF_CONF5_CLK_FORCE_ON  LCD_IF_DPI_IF_CONF5_CLK_FORCE_ON_Msk

/**************** Bit definition for LCD_IF_DPI_CTRL register *****************/
#define LCD_IF_DPI_CTRL_DPI_EN_Pos      (0U)
#define LCD_IF_DPI_CTRL_DPI_EN_Msk      (0x1UL << LCD_IF_DPI_CTRL_DPI_EN_Pos)
#define LCD_IF_DPI_CTRL_DPI_EN          LCD_IF_DPI_CTRL_DPI_EN_Msk
#define LCD_IF_DPI_CTRL_DPI_CM_Pos      (1U)
#define LCD_IF_DPI_CTRL_DPI_CM_Msk      (0x1UL << LCD_IF_DPI_CTRL_DPI_CM_Pos)
#define LCD_IF_DPI_CTRL_DPI_CM          LCD_IF_DPI_CTRL_DPI_CM_Msk
#define LCD_IF_DPI_CTRL_DPI_SD_Pos      (2U)
#define LCD_IF_DPI_CTRL_DPI_SD_Msk      (0x1UL << LCD_IF_DPI_CTRL_DPI_SD_Pos)
#define LCD_IF_DPI_CTRL_DPI_SD          LCD_IF_DPI_CTRL_DPI_SD_Msk
#define LCD_IF_DPI_CTRL_DPI_UC_Pos      (3U)
#define LCD_IF_DPI_CTRL_DPI_UC_Msk      (0x1UL << LCD_IF_DPI_CTRL_DPI_UC_Pos)
#define LCD_IF_DPI_CTRL_DPI_UC          LCD_IF_DPI_CTRL_DPI_UC_Msk

/**************** Bit definition for LCD_IF_DPI_STAT register *****************/
#define LCD_IF_DPI_STAT_HPOS_Pos        (0U)
#define LCD_IF_DPI_STAT_HPOS_Msk        (0x7FFUL << LCD_IF_DPI_STAT_HPOS_Pos)
#define LCD_IF_DPI_STAT_HPOS            LCD_IF_DPI_STAT_HPOS_Msk
#define LCD_IF_DPI_STAT_HSTAT_Pos       (11U)
#define LCD_IF_DPI_STAT_HSTAT_Msk       (0x7UL << LCD_IF_DPI_STAT_HSTAT_Pos)
#define LCD_IF_DPI_STAT_HSTAT           LCD_IF_DPI_STAT_HSTAT_Msk
#define LCD_IF_DPI_STAT_VPOS_Pos        (16U)
#define LCD_IF_DPI_STAT_VPOS_Msk        (0xFFFFUL << LCD_IF_DPI_STAT_VPOS_Pos)
#define LCD_IF_DPI_STAT_VPOS            LCD_IF_DPI_STAT_VPOS_Msk

/************** Bit definition for LCD_IF_JDI_SER_CONF1 register **************/
#define LCD_IF_JDI_SER_CONF1_WR_LEN_Pos  (0U)
#define LCD_IF_JDI_SER_CONF1_WR_LEN_Msk  (0x1FUL << LCD_IF_JDI_SER_CONF1_WR_LEN_Pos)
#define LCD_IF_JDI_SER_CONF1_WR_LEN     LCD_IF_JDI_SER_CONF1_WR_LEN_Msk
#define LCD_IF_JDI_SER_CONF1_CLK_DIV_Pos  (8U)
#define LCD_IF_JDI_SER_CONF1_CLK_DIV_Msk  (0xFFUL << LCD_IF_JDI_SER_CONF1_CLK_DIV_Pos)
#define LCD_IF_JDI_SER_CONF1_CLK_DIV    LCD_IF_JDI_SER_CONF1_CLK_DIV_Msk

/************** Bit definition for LCD_IF_JDI_SER_CONF2 register **************/
#define LCD_IF_JDI_SER_CONF2_WR_CMD_Pos  (0U)
#define LCD_IF_JDI_SER_CONF2_WR_CMD_Msk  (0xFFFFUL << LCD_IF_JDI_SER_CONF2_WR_CMD_Pos)
#define LCD_IF_JDI_SER_CONF2_WR_CMD     LCD_IF_JDI_SER_CONF2_WR_CMD_Msk
#define LCD_IF_JDI_SER_CONF2_INIT_LINE_CNT_Pos  (16U)
#define LCD_IF_JDI_SER_CONF2_INIT_LINE_CNT_Msk  (0xFFFFUL << LCD_IF_JDI_SER_CONF2_INIT_LINE_CNT_Pos)
#define LCD_IF_JDI_SER_CONF2_INIT_LINE_CNT  LCD_IF_JDI_SER_CONF2_INIT_LINE_CNT_Msk

/************** Bit definition for LCD_IF_JDI_SER_CTRL register ***************/
#define LCD_IF_JDI_SER_CTRL_DISP_Pos    (0U)
#define LCD_IF_JDI_SER_CTRL_DISP_Msk    (0x1UL << LCD_IF_JDI_SER_CTRL_DISP_Pos)
#define LCD_IF_JDI_SER_CTRL_DISP        LCD_IF_JDI_SER_CTRL_DISP_Msk
#define LCD_IF_JDI_SER_CTRL_EXTCOMIN_Pos  (1U)
#define LCD_IF_JDI_SER_CTRL_EXTCOMIN_Msk  (0x1UL << LCD_IF_JDI_SER_CTRL_EXTCOMIN_Pos)
#define LCD_IF_JDI_SER_CTRL_EXTCOMIN    LCD_IF_JDI_SER_CTRL_EXTCOMIN_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF1 register **************/
#define LCD_IF_JDI_PAR_CONF1_MAX_COL_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF1_MAX_COL_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF1_MAX_COL_Pos)
#define LCD_IF_JDI_PAR_CONF1_MAX_COL    LCD_IF_JDI_PAR_CONF1_MAX_COL_Msk
#define LCD_IF_JDI_PAR_CONF1_MAX_LINE_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF1_MAX_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF1_MAX_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF1_MAX_LINE   LCD_IF_JDI_PAR_CONF1_MAX_LINE_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF2 register **************/
#define LCD_IF_JDI_PAR_CONF2_END_LINE_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF2_END_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF2_END_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF2_END_LINE   LCD_IF_JDI_PAR_CONF2_END_LINE_Msk
#define LCD_IF_JDI_PAR_CONF2_ST_LINE_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF2_ST_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF2_ST_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF2_ST_LINE    LCD_IF_JDI_PAR_CONF2_ST_LINE_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF3 register **************/
#define LCD_IF_JDI_PAR_CONF3_END_COL_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF3_END_COL_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF3_END_COL_Pos)
#define LCD_IF_JDI_PAR_CONF3_END_COL    LCD_IF_JDI_PAR_CONF3_END_COL_Msk
#define LCD_IF_JDI_PAR_CONF3_ST_COL_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF3_ST_COL_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF3_ST_COL_Pos)
#define LCD_IF_JDI_PAR_CONF3_ST_COL     LCD_IF_JDI_PAR_CONF3_ST_COL_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF4 register **************/
#define LCD_IF_JDI_PAR_CONF4_HST_WIDTH_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF4_HST_WIDTH_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF4_HST_WIDTH_Pos)
#define LCD_IF_JDI_PAR_CONF4_HST_WIDTH  LCD_IF_JDI_PAR_CONF4_HST_WIDTH_Msk
#define LCD_IF_JDI_PAR_CONF4_HCK_WIDTH_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF4_HCK_WIDTH_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF4_HCK_WIDTH_Pos)
#define LCD_IF_JDI_PAR_CONF4_HCK_WIDTH  LCD_IF_JDI_PAR_CONF4_HCK_WIDTH_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF5 register **************/
#define LCD_IF_JDI_PAR_CONF5_VST_WIDTH_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF5_VST_WIDTH_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF5_VST_WIDTH_Pos)
#define LCD_IF_JDI_PAR_CONF5_VST_WIDTH  LCD_IF_JDI_PAR_CONF5_VST_WIDTH_Msk
#define LCD_IF_JDI_PAR_CONF5_VCK_WIDTH_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF5_VCK_WIDTH_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF5_VCK_WIDTH_Pos)
#define LCD_IF_JDI_PAR_CONF5_VCK_WIDTH  LCD_IF_JDI_PAR_CONF5_VCK_WIDTH_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF6 register **************/
#define LCD_IF_JDI_PAR_CONF6_HST_DLY_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF6_HST_DLY_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF6_HST_DLY_Pos)
#define LCD_IF_JDI_PAR_CONF6_HST_DLY    LCD_IF_JDI_PAR_CONF6_HST_DLY_Msk
#define LCD_IF_JDI_PAR_CONF6_VCK_DLY_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF6_VCK_DLY_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF6_VCK_DLY_Pos)
#define LCD_IF_JDI_PAR_CONF6_VCK_DLY    LCD_IF_JDI_PAR_CONF6_VCK_DLY_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF7 register **************/
#define LCD_IF_JDI_PAR_CONF7_HCK_DLY_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF7_HCK_DLY_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF7_HCK_DLY_Pos)
#define LCD_IF_JDI_PAR_CONF7_HCK_DLY    LCD_IF_JDI_PAR_CONF7_HCK_DLY_Msk
#define LCD_IF_JDI_PAR_CONF7_DP_MODE_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF7_DP_MODE_Msk  (0x1UL << LCD_IF_JDI_PAR_CONF7_DP_MODE_Pos)
#define LCD_IF_JDI_PAR_CONF7_DP_MODE    LCD_IF_JDI_PAR_CONF7_DP_MODE_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CTRL register ***************/
#define LCD_IF_JDI_PAR_CTRL_ENABLE_Pos  (0U)
#define LCD_IF_JDI_PAR_CTRL_ENABLE_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_ENABLE_Pos)
#define LCD_IF_JDI_PAR_CTRL_ENABLE      LCD_IF_JDI_PAR_CTRL_ENABLE_Msk
#define LCD_IF_JDI_PAR_CTRL_XRST_Pos    (4U)
#define LCD_IF_JDI_PAR_CTRL_XRST_Msk    (0x1UL << LCD_IF_JDI_PAR_CTRL_XRST_Pos)
#define LCD_IF_JDI_PAR_CTRL_XRST        LCD_IF_JDI_PAR_CTRL_XRST_Msk
#define LCD_IF_JDI_PAR_CTRL_ENBPOL_Pos  (5U)
#define LCD_IF_JDI_PAR_CTRL_ENBPOL_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_ENBPOL_Pos)
#define LCD_IF_JDI_PAR_CTRL_ENBPOL      LCD_IF_JDI_PAR_CTRL_ENBPOL_Msk
#define LCD_IF_JDI_PAR_CTRL_HCKPOL_Pos  (6U)
#define LCD_IF_JDI_PAR_CTRL_HCKPOL_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_HCKPOL_Pos)
#define LCD_IF_JDI_PAR_CTRL_HCKPOL      LCD_IF_JDI_PAR_CTRL_HCKPOL_Msk
#define LCD_IF_JDI_PAR_CTRL_HSTPOL_Pos  (7U)
#define LCD_IF_JDI_PAR_CTRL_HSTPOL_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_HSTPOL_Pos)
#define LCD_IF_JDI_PAR_CTRL_HSTPOL      LCD_IF_JDI_PAR_CTRL_HSTPOL_Msk
#define LCD_IF_JDI_PAR_CTRL_VCKPOL_Pos  (8U)
#define LCD_IF_JDI_PAR_CTRL_VCKPOL_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_VCKPOL_Pos)
#define LCD_IF_JDI_PAR_CTRL_VCKPOL      LCD_IF_JDI_PAR_CTRL_VCKPOL_Msk
#define LCD_IF_JDI_PAR_CTRL_VSTPOL_Pos  (9U)
#define LCD_IF_JDI_PAR_CTRL_VSTPOL_Msk  (0x1UL << LCD_IF_JDI_PAR_CTRL_VSTPOL_Pos)
#define LCD_IF_JDI_PAR_CTRL_VSTPOL      LCD_IF_JDI_PAR_CTRL_VSTPOL_Msk
#define LCD_IF_JDI_PAR_CTRL_INT_LINE_NUM_Pos  (16U)
#define LCD_IF_JDI_PAR_CTRL_INT_LINE_NUM_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CTRL_INT_LINE_NUM_Pos)
#define LCD_IF_JDI_PAR_CTRL_INT_LINE_NUM  LCD_IF_JDI_PAR_CTRL_INT_LINE_NUM_Msk

/************** Bit definition for LCD_IF_JDI_PAR_STAT register ***************/
#define LCD_IF_JDI_PAR_STAT_HPOS_Pos    (0U)
#define LCD_IF_JDI_PAR_STAT_HPOS_Msk    (0xFFFFUL << LCD_IF_JDI_PAR_STAT_HPOS_Pos)
#define LCD_IF_JDI_PAR_STAT_HPOS        LCD_IF_JDI_PAR_STAT_HPOS_Msk
#define LCD_IF_JDI_PAR_STAT_VPOS_Pos    (16U)
#define LCD_IF_JDI_PAR_STAT_VPOS_Msk    (0xFFFFUL << LCD_IF_JDI_PAR_STAT_VPOS_Pos)
#define LCD_IF_JDI_PAR_STAT_VPOS        LCD_IF_JDI_PAR_STAT_VPOS_Msk

/************* Bit definition for LCD_IF_JDI_PAR_EX_CTRL register *************/
#define LCD_IF_JDI_PAR_EX_CTRL_MAX_CNT_Pos  (0U)
#define LCD_IF_JDI_PAR_EX_CTRL_MAX_CNT_Msk  (0xFFFFFFUL << LCD_IF_JDI_PAR_EX_CTRL_MAX_CNT_Pos)
#define LCD_IF_JDI_PAR_EX_CTRL_MAX_CNT  LCD_IF_JDI_PAR_EX_CTRL_MAX_CNT_Msk
#define LCD_IF_JDI_PAR_EX_CTRL_CNT_EN_Pos  (28U)
#define LCD_IF_JDI_PAR_EX_CTRL_CNT_EN_Msk  (0x1UL << LCD_IF_JDI_PAR_EX_CTRL_CNT_EN_Pos)
#define LCD_IF_JDI_PAR_EX_CTRL_CNT_EN   LCD_IF_JDI_PAR_EX_CTRL_CNT_EN_Msk
#define LCD_IF_JDI_PAR_EX_CTRL_XFRP_Pos  (29U)
#define LCD_IF_JDI_PAR_EX_CTRL_XFRP_Msk  (0x1UL << LCD_IF_JDI_PAR_EX_CTRL_XFRP_Pos)
#define LCD_IF_JDI_PAR_EX_CTRL_XFRP     LCD_IF_JDI_PAR_EX_CTRL_XFRP_Msk
#define LCD_IF_JDI_PAR_EX_CTRL_FRP_Pos  (30U)
#define LCD_IF_JDI_PAR_EX_CTRL_FRP_Msk  (0x1UL << LCD_IF_JDI_PAR_EX_CTRL_FRP_Pos)
#define LCD_IF_JDI_PAR_EX_CTRL_FRP      LCD_IF_JDI_PAR_EX_CTRL_FRP_Msk
#define LCD_IF_JDI_PAR_EX_CTRL_VCOM_Pos  (31U)
#define LCD_IF_JDI_PAR_EX_CTRL_VCOM_Msk  (0x1UL << LCD_IF_JDI_PAR_EX_CTRL_VCOM_Pos)
#define LCD_IF_JDI_PAR_EX_CTRL_VCOM     LCD_IF_JDI_PAR_EX_CTRL_VCOM_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF8 register **************/
#define LCD_IF_JDI_PAR_CONF8_ENB_END_COL_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF8_ENB_END_COL_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF8_ENB_END_COL_Pos)
#define LCD_IF_JDI_PAR_CONF8_ENB_END_COL  LCD_IF_JDI_PAR_CONF8_ENB_END_COL_Msk
#define LCD_IF_JDI_PAR_CONF8_ENB_ST_COL_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF8_ENB_ST_COL_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF8_ENB_ST_COL_Pos)
#define LCD_IF_JDI_PAR_CONF8_ENB_ST_COL  LCD_IF_JDI_PAR_CONF8_ENB_ST_COL_Msk

/************** Bit definition for LCD_IF_JDI_PAR_CONF9 register **************/
#define LCD_IF_JDI_PAR_CONF9_ENB_END_LINE_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF9_ENB_END_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF9_ENB_END_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF9_ENB_END_LINE  LCD_IF_JDI_PAR_CONF9_ENB_END_LINE_Msk
#define LCD_IF_JDI_PAR_CONF9_ENB_ST_LINE_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF9_ENB_ST_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF9_ENB_ST_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF9_ENB_ST_LINE  LCD_IF_JDI_PAR_CONF9_ENB_ST_LINE_Msk

/************* Bit definition for LCD_IF_JDI_PAR_CONF10 register **************/
#define LCD_IF_JDI_PAR_CONF10_HC_END_LINE_Pos  (0U)
#define LCD_IF_JDI_PAR_CONF10_HC_END_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF10_HC_END_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF10_HC_END_LINE  LCD_IF_JDI_PAR_CONF10_HC_END_LINE_Msk
#define LCD_IF_JDI_PAR_CONF10_HC_ST_LINE_Pos  (16U)
#define LCD_IF_JDI_PAR_CONF10_HC_ST_LINE_Msk  (0xFFFFUL << LCD_IF_JDI_PAR_CONF10_HC_ST_LINE_Pos)
#define LCD_IF_JDI_PAR_CONF10_HC_ST_LINE  LCD_IF_JDI_PAR_CONF10_HC_ST_LINE_Msk

/************** Bit definition for LCD_IF_CANVAS_STAT0 register ***************/
#define LCD_IF_CANVAS_STAT0_X_COR_Pos   (0U)
#define LCD_IF_CANVAS_STAT0_X_COR_Msk   (0x7FFUL << LCD_IF_CANVAS_STAT0_X_COR_Pos)
#define LCD_IF_CANVAS_STAT0_X_COR       LCD_IF_CANVAS_STAT0_X_COR_Msk
#define LCD_IF_CANVAS_STAT0_Y_COR_Pos   (16U)
#define LCD_IF_CANVAS_STAT0_Y_COR_Msk   (0x7FFUL << LCD_IF_CANVAS_STAT0_Y_COR_Pos)
#define LCD_IF_CANVAS_STAT0_Y_COR       LCD_IF_CANVAS_STAT0_Y_COR_Msk

/************** Bit definition for LCD_IF_CANVAS_STAT1 register ***************/
#define LCD_IF_CANVAS_STAT1_FIFO_CNT_Pos  (0U)
#define LCD_IF_CANVAS_STAT1_FIFO_CNT_Msk  (0x7UL << LCD_IF_CANVAS_STAT1_FIFO_CNT_Pos)
#define LCD_IF_CANVAS_STAT1_FIFO_CNT    LCD_IF_CANVAS_STAT1_FIFO_CNT_Msk
#define LCD_IF_CANVAS_STAT1_POSTC_STAT_Pos  (3U)
#define LCD_IF_CANVAS_STAT1_POSTC_STAT_Msk  (0x7UL << LCD_IF_CANVAS_STAT1_POSTC_STAT_Pos)
#define LCD_IF_CANVAS_STAT1_POSTC_STAT  LCD_IF_CANVAS_STAT1_POSTC_STAT_Msk
#define LCD_IF_CANVAS_STAT1_PREC_STAT_Pos  (6U)
#define LCD_IF_CANVAS_STAT1_PREC_STAT_Msk  (0x7UL << LCD_IF_CANVAS_STAT1_PREC_STAT_Pos)
#define LCD_IF_CANVAS_STAT1_PREC_STAT   LCD_IF_CANVAS_STAT1_PREC_STAT_Msk
#define LCD_IF_CANVAS_STAT1_FETCH_STAT_Pos  (9U)
#define LCD_IF_CANVAS_STAT1_FETCH_STAT_Msk  (0x7UL << LCD_IF_CANVAS_STAT1_FETCH_STAT_Pos)
#define LCD_IF_CANVAS_STAT1_FETCH_STAT  LCD_IF_CANVAS_STAT1_FETCH_STAT_Msk

/**************** Bit definition for LCD_IF_OL0_STAT register *****************/
#define LCD_IF_OL0_STAT_DONE_REQ_Pos    (0U)
#define LCD_IF_OL0_STAT_DONE_REQ_Msk    (0x1UL << LCD_IF_OL0_STAT_DONE_REQ_Pos)
#define LCD_IF_OL0_STAT_DONE_REQ        LCD_IF_OL0_STAT_DONE_REQ_Msk
#define LCD_IF_OL0_STAT_PREFETCH_OUT_Pos  (1U)
#define LCD_IF_OL0_STAT_PREFETCH_OUT_Msk  (0x1UL << LCD_IF_OL0_STAT_PREFETCH_OUT_Pos)
#define LCD_IF_OL0_STAT_PREFETCH_OUT    LCD_IF_OL0_STAT_PREFETCH_OUT_Msk
#define LCD_IF_OL0_STAT_PREFETCH_READ_Pos  (2U)
#define LCD_IF_OL0_STAT_PREFETCH_READ_Msk  (0x3UL << LCD_IF_OL0_STAT_PREFETCH_READ_Pos)
#define LCD_IF_OL0_STAT_PREFETCH_READ   LCD_IF_OL0_STAT_PREFETCH_READ_Msk
#define LCD_IF_OL0_STAT_DATA_CONV_Pos   (4U)
#define LCD_IF_OL0_STAT_DATA_CONV_Msk   (0x3UL << LCD_IF_OL0_STAT_DATA_CONV_Pos)
#define LCD_IF_OL0_STAT_DATA_CONV       LCD_IF_OL0_STAT_DATA_CONV_Msk
#define LCD_IF_OL0_STAT_PF_DF_Pos       (6U)
#define LCD_IF_OL0_STAT_PF_DF_Msk       (0x3UL << LCD_IF_OL0_STAT_PF_DF_Pos)
#define LCD_IF_OL0_STAT_PF_DF           LCD_IF_OL0_STAT_PF_DF_Msk
#define LCD_IF_OL0_STAT_PF_PR_Pos       (8U)
#define LCD_IF_OL0_STAT_PF_PR_Msk       (0x7UL << LCD_IF_OL0_STAT_PF_PR_Pos)
#define LCD_IF_OL0_STAT_PF_PR           LCD_IF_OL0_STAT_PF_PR_Msk
#define LCD_IF_OL0_STAT_SC_OUT_Pos      (11U)
#define LCD_IF_OL0_STAT_SC_OUT_Msk      (0x3UL << LCD_IF_OL0_STAT_SC_OUT_Pos)
#define LCD_IF_OL0_STAT_SC_OUT          LCD_IF_OL0_STAT_SC_OUT_Msk
#define LCD_IF_OL0_STAT_SC_BE_Pos       (13U)
#define LCD_IF_OL0_STAT_SC_BE_Msk       (0x7UL << LCD_IF_OL0_STAT_SC_BE_Pos)
#define LCD_IF_OL0_STAT_SC_BE           LCD_IF_OL0_STAT_SC_BE_Msk
#define LCD_IF_OL0_STAT_SC_FE_Pos       (16U)
#define LCD_IF_OL0_STAT_SC_FE_Msk       (0xFUL << LCD_IF_OL0_STAT_SC_FE_Pos)
#define LCD_IF_OL0_STAT_SC_FE           LCD_IF_OL0_STAT_SC_FE_Msk
#define LCD_IF_OL0_STAT_SC_LB1_Pos      (20U)
#define LCD_IF_OL0_STAT_SC_LB1_Msk      (0x3UL << LCD_IF_OL0_STAT_SC_LB1_Pos)
#define LCD_IF_OL0_STAT_SC_LB1          LCD_IF_OL0_STAT_SC_LB1_Msk
#define LCD_IF_OL0_STAT_SC_LB0_Pos      (22U)
#define LCD_IF_OL0_STAT_SC_LB0_Msk      (0x3UL << LCD_IF_OL0_STAT_SC_LB0_Pos)
#define LCD_IF_OL0_STAT_SC_LB0          LCD_IF_OL0_STAT_SC_LB0_Msk

/**************** Bit definition for LCD_IF_OL1_STAT register *****************/
#define LCD_IF_OL1_STAT_DONE_REQ_Pos    (0U)
#define LCD_IF_OL1_STAT_DONE_REQ_Msk    (0x1UL << LCD_IF_OL1_STAT_DONE_REQ_Pos)
#define LCD_IF_OL1_STAT_DONE_REQ        LCD_IF_OL1_STAT_DONE_REQ_Msk
#define LCD_IF_OL1_STAT_PREFETCH_OUT_Pos  (1U)
#define LCD_IF_OL1_STAT_PREFETCH_OUT_Msk  (0x1UL << LCD_IF_OL1_STAT_PREFETCH_OUT_Pos)
#define LCD_IF_OL1_STAT_PREFETCH_OUT    LCD_IF_OL1_STAT_PREFETCH_OUT_Msk
#define LCD_IF_OL1_STAT_PREFETCH_READ_Pos  (2U)
#define LCD_IF_OL1_STAT_PREFETCH_READ_Msk  (0x3UL << LCD_IF_OL1_STAT_PREFETCH_READ_Pos)
#define LCD_IF_OL1_STAT_PREFETCH_READ   LCD_IF_OL1_STAT_PREFETCH_READ_Msk
#define LCD_IF_OL1_STAT_DATA_CONV_Pos   (4U)
#define LCD_IF_OL1_STAT_DATA_CONV_Msk   (0x3UL << LCD_IF_OL1_STAT_DATA_CONV_Pos)
#define LCD_IF_OL1_STAT_DATA_CONV       LCD_IF_OL1_STAT_DATA_CONV_Msk
#define LCD_IF_OL1_STAT_PF_DF_Pos       (6U)
#define LCD_IF_OL1_STAT_PF_DF_Msk       (0x3UL << LCD_IF_OL1_STAT_PF_DF_Pos)
#define LCD_IF_OL1_STAT_PF_DF           LCD_IF_OL1_STAT_PF_DF_Msk
#define LCD_IF_OL1_STAT_PF_PR_Pos       (8U)
#define LCD_IF_OL1_STAT_PF_PR_Msk       (0x7UL << LCD_IF_OL1_STAT_PF_PR_Pos)
#define LCD_IF_OL1_STAT_PF_PR           LCD_IF_OL1_STAT_PF_PR_Msk

/*************** Bit definition for LCD_IF_MEM_IF_STAT register ***************/
#define LCD_IF_MEM_IF_STAT_AHB_Pos      (0U)
#define LCD_IF_MEM_IF_STAT_AHB_Msk      (0xFUL << LCD_IF_MEM_IF_STAT_AHB_Pos)
#define LCD_IF_MEM_IF_STAT_AHB          LCD_IF_MEM_IF_STAT_AHB_Msk
#define LCD_IF_MEM_IF_STAT_ARB_READ_PORT_Pos  (4U)
#define LCD_IF_MEM_IF_STAT_ARB_READ_PORT_Msk  (0x7UL << LCD_IF_MEM_IF_STAT_ARB_READ_PORT_Pos)
#define LCD_IF_MEM_IF_STAT_ARB_READ_PORT  LCD_IF_MEM_IF_STAT_ARB_READ_PORT_Msk
#define LCD_IF_MEM_IF_STAT_ARB_MAIN_Pos  (7U)
#define LCD_IF_MEM_IF_STAT_ARB_MAIN_Msk  (0x7UL << LCD_IF_MEM_IF_STAT_ARB_MAIN_Pos)
#define LCD_IF_MEM_IF_STAT_ARB_MAIN     LCD_IF_MEM_IF_STAT_ARB_MAIN_Msk

/**************** Bit definition for LCD_IF_PERF_CNT register *****************/
#define LCD_IF_PERF_CNT_VAL_Pos         (0U)
#define LCD_IF_PERF_CNT_VAL_Msk         (0xFFFFFFFFUL << LCD_IF_PERF_CNT_VAL_Pos)
#define LCD_IF_PERF_CNT_VAL             LCD_IF_PERF_CNT_VAL_Msk

#endif
/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/

