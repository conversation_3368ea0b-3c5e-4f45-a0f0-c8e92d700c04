
/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   bsp_board_bus.c
@Time    :   2025/03/25 14:02:39
@Brief   :
@Details :
*
************************************************************/
#include "bsp_board_bus.h"
#include "drv_io.h"
#ifndef IGS_BOOT
#include "hw_port.h"
#endif
#include "haptic_nv.h"
// #include "pm.h"
#include "rtthread.h"
#include <rtdevice.h>
#ifdef USE_BSP_BORAD_BUS_INIT
#ifdef BF0_HCPU
static rt_mutex_t i2c1_lock = RT_NULL;
#define INITIALIZE_I2C1_LOCK()                                    \
    do                                                            \
    {                                                             \
        if (NULL == i2c1_lock)                                    \
        {                                                         \
            i2c1_lock = rt_mutex_create("wr02_i2c1", RT_IPC_FLAG_FIFO); \
        }                                                         \
    } while (0);
#define LOCK_I2C1() \
    if (i2c1_lock)  \
        rt_mutex_take(i2c1_lock, RT_WAITING_FOREVER);
#define UNLOCK_I2C1() \
    if (i2c1_lock)    \
    rt_mutex_release(i2c1_lock)
static uint8_t i2c1_bus_open_times = 0;
static int bsp_i2c1_io_init(void)
{
    //马达硬复位
    haptic_reset();
#ifndef IGS_BOOT
    //支付宝硬复位
    HS_Reset();
#endif
    INITIALIZE_I2C1_LOCK();
    qw_special_pin_set(I2C1_SCL_PIN, I2C1_SCL, PIN_NOPULL); // 已有外部上拉
    qw_special_pin_set(I2C1_SDA_PIN, I2C1_SDA, PIN_NOPULL);
    return 0;
}
INIT_DEVICE_EXPORT(bsp_i2c1_io_init); //需要等待由于支付宝管脚A3A4区别需要在hw_version_init后调用

int bsp_i2c1_bus_init(void)
{
    int ret = RT_EOK;
    // BSP_BUS_D("bsp_i2c1_bus_init\n");
    /* i2c1 bus device: motor / alipay */
    LOCK_I2C1();
    struct rt_i2c_bus_device *i2c1_bus = rt_i2c_bus_device_find("i2c1");
    if (i2c1_bus)
    {
        // i2c1_bus_open_times = i2c1_bus->parent.ref_count;
        if (i2c1_bus_open_times == 0)
        {
            // bsp_i2c1_io_init();
            // rt_i2c_control(i2c1_bus,RT_DEVICE_CTRL_RESUME,(void*)PM_SLEEP_MODE_STANDBY);
            // BSP_BUS_D("rt_device_open i2c bus device %s\n", "i2c1");
            rt_device_open(&(i2c1_bus->parent), RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_RX | RT_DEVICE_FLAG_INT_TX);
#if 1
            {
                struct rt_i2c_configuration configuration =
                    {
                        .mode = 0,
                        .addr = 0,
                        .timeout = 5000,
                        .max_hz = 100000,
                    };
                rt_i2c_configure(i2c1_bus, &configuration);
            }
#endif
        }
        i2c1_bus_open_times++;
    }
    else
    {
        BSP_BUS_E("Can not found i2c bus %s, init fail\n", "i2c1");
        ret = -1;
    }
    UNLOCK_I2C1();
    return ret;
}

int bsp_i2c1_bus_uninit(void)
{
    int ret = RT_EOK;
    // BSP_BUS_D("bsp_i2c1_bus_uninit\n");
    /* i2c1 bus device: motor / alipay */
    LOCK_I2C1();
    struct rt_i2c_bus_device *i2c1_bus = rt_i2c_bus_device_find("i2c1");
    if (i2c1_bus)
    {
        if (i2c1_bus_open_times > 0)
        {
            i2c1_bus_open_times--;//i2c1_bus->parent.ref_count
            if (i2c1_bus_open_times == 0)
            {
                // rt_i2c_control(i2c1_bus,RT_DEVICE_CTRL_SUSPEND,(void*)PM_SLEEP_MODE_STANDBY);
                // rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_OUTPUT_OD); // 设置输入
                // rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_OUTPUT_OD); // 设置输入
                // qw_gpio_set(I2C1_SCL_PIN, GPIO_MODE_OUTPUT_OD, PIN_NOPULL);
                // qw_gpio_set(I2C1_SDA_PIN, GPIO_MODE_OUTPUT_OD, PIN_NOPULL);
                //  BSP_BUS_D("rt_device_close i2c bus device %s\n", "i2c1");
                rt_device_close(&(i2c1_bus->parent));
            }
        }
    }
    else
    {
        BSP_BUS_E("Can not found i2c bus %s, deinit fail\n", "i2c1");
    }
    UNLOCK_I2C1();
    return ret;
}

/************************************************************************
 *@function:void i2c1_device_rstpin_init(void)
 *@brief:I2C1总线挂载了马达和支付宝，其中马达复位异常时会导致I2C1总线异常，
 * 所以需要单独处理I2C1总线复位:这里是拉低 在bsp_i2c1_io_init里切到有效电平
 * 在马达和支付宝驱动INIT之前调用,保证I2C1总线正常
*************************************************************************/
void i2c1_device_rstpin_init(void)
{
    // 马达复位使能
    int pin = VIBRATOR_RST_PIN;
    qw_gpio_set(pin, GPIO_MODE_OUTPUT, PIN_PULLUP);     // 在BOOT里和APP复位后模块未初始化前拉低RST
    BSP_GPIO_Set(pin, 0, 1);

    //下面代码放在A3A4版差异化里 BSP_PIN_CustomInit_V2
    //int hw_ver = hw_version_get();
    //// 支付宝复位使能
    //pin = (hw_ver == HARDWARE_VERSION_A3) ? ZFB_RST_PIN : ZFB_RST_PIN_A4;
    //qw_gpio_set(pin, GPIO_MODE_OUTPUT, PIN_PULLUP);     // 在BOOT里和APP复位后模块未初始化前拉低RST
    //BSP_GPIO_Set(pin, 0, 1);
}

#endif
#endif
