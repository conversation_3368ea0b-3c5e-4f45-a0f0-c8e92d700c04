#define _ENABLE_DEBUG_
#define _FILE_TAG_ "driver"


#include "string.h"

#include <rtthread.h>


#include "chsc5816_chipsemi_default_upd.h"

#include "chsc5816.h"

extern void semi_touch_reset(void);
extern int32_t semi_touch_read_bytes(uint32_t reg, uint8_t* buffer, uint16_t len);
extern int32_t semi_touch_write_bytes(uint32_t reg, uint8_t* buffer, uint16_t len);


static const uint8_t fw_5816_burn[] = {
0x37,0x02,0x00,0x20,0x13,0x02,0x02,0x02,0x37,0x77,0x00,0x40,0x37,0x45,0x04,0x12,
0x05,0x05,0x08,0xC3,0x02,0x82,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x01,0x00,0x01,0x00,0x01,0x00,0x73,0x70,0x04,0x30,0x17,0x11,0x00,0x00,0x13,0x01,
0xE1,0x1D,0x17,0x15,0x00,0x00,0x13,0x05,0x25,0xB2,0x97,0x15,0x00,0x00,0x93,0x85,
0x65,0xFC,0x17,0x16,0x00,0x00,0x13,0x06,0x66,0xFC,0x63,0xFA,0xC5,0x00,0x83,0x22,
0x05,0x00,0x23,0xA0,0x55,0x00,0x11,0x05,0x91,0x05,0xE3,0xEA,0xC5,0xFE,0x17,0x15,
0x00,0x00,0x13,0x05,0xA5,0xFA,0x97,0x15,0x00,0x00,0x93,0x85,0x65,0x5D,0x63,0x77,
0xB5,0x00,0x23,0x20,0x05,0x00,0x11,0x05,0xE3,0x6D,0xB5,0xFE,0xEF,0x00,0x00,0x5C,
0x01,0xA0,0x37,0x17,0x00,0x20,0x83,0x26,0x07,0x62,0xFD,0x77,0x93,0x87,0x27,0x9A,
0x23,0x91,0xF6,0x00,0x83,0x27,0x07,0x62,0x05,0x67,0x13,0x07,0xD7,0x65,0x23,0x92,
0xE7,0x00,0x0D,0x67,0x13,0x07,0x27,0x23,0x23,0x93,0xE7,0x00,0x23,0x94,0x07,0x00,
0x23,0x95,0x07,0x00,0x23,0x96,0x07,0x00,0x23,0x87,0x07,0x00,0x82,0x80,0xB7,0x17,
0x00,0x20,0x83,0xA7,0x47,0x63,0x03,0xD5,0xC7,0x00,0x42,0x05,0x03,0xD7,0x27,0x00,
0x3A,0x95,0x13,0x77,0x35,0x00,0x3D,0xEF,0x51,0x11,0x06,0xC4,0x22,0xC2,0x26,0xC0,
0x03,0xD4,0x47,0x00,0x93,0x77,0x34,0x00,0xB1,0xE3,0xA9,0x67,0x63,0x78,0xF5,0x00,
0x93,0x07,0xF4,0xFF,0xAA,0x97,0x29,0x67,0x63,0xF6,0xE7,0x04,0xB7,0x24,0x00,0x20,
0x13,0x86,0x04,0x40,0xA2,0x85,0x21,0x2F,0xA2,0x85,0x13,0x85,0x04,0x40,0x31,0x25,
0xB7,0x17,0x00,0x20,0x03,0xA7,0x07,0x62,0x23,0x11,0xA7,0x00,0x83,0xA7,0x07,0x62,
0x41,0x81,0x23,0x92,0xA7,0x00,0x23,0x87,0x07,0x00,0x01,0xA8,0xB7,0x17,0x00,0x20,
0x83,0xA7,0x07,0x62,0x15,0x47,0x23,0x87,0xE7,0x00,0xA2,0x40,0x12,0x44,0x82,0x44,
0x31,0x01,0x82,0x80,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x15,0x47,0x23,0x87,
0xE7,0x00,0xE5,0xB7,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x15,0x47,0x23,0x87,
0xE7,0x00,0x82,0x80,0x51,0x11,0x06,0xC4,0x22,0xC2,0xB7,0x17,0x00,0x20,0x83,0xA7,
0x07,0x62,0x25,0x47,0x23,0x87,0xE7,0x00,0xB7,0x17,0x00,0x20,0x83,0xA7,0x47,0x63,
0x03,0xD4,0x27,0x00,0x9D,0x47,0x63,0xF6,0x87,0x00,0xA2,0x40,0x12,0x44,0x31,0x01,
0x82,0x80,0x89,0x25,0x22,0x85,0xEF,0x00,0xB0,0x0E,0xE3,0x48,0x05,0xFE,0xB7,0x17,
0x00,0x20,0x03,0xA7,0x07,0x62,0x23,0x11,0xA7,0x00,0x83,0xA7,0x07,0x62,0x23,0x87,
0x07,0x00,0xE1,0xBF,0x51,0x11,0x06,0xC4,0x22,0xC2,0x26,0xC0,0x37,0x24,0x00,0x20,
0x13,0x06,0x04,0x40,0x93,0x05,0x00,0x10,0x29,0x65,0x13,0x05,0x05,0xF0,0x81,0x2D,
0x03,0x25,0x04,0x40,0x25,0xE9,0xB7,0x27,0x00,0x20,0x93,0x87,0x47,0x40,0x37,0x27,
0x00,0x20,0x13,0x07,0x07,0x50,0x80,0x43,0x1D,0xE4,0x91,0x07,0xE3,0x9D,0xE7,0xFE,
0xB7,0x24,0x00,0x20,0x13,0x86,0x04,0x40,0x93,0x05,0x00,0x10,0x29,0x65,0x05,0x25,
0x83,0xA7,0x04,0x40,0x93,0xC6,0xF7,0xFF,0xC1,0x82,0x13,0x97,0x17,0x01,0x45,0x83,
0x22,0x85,0x63,0x87,0xE6,0x00,0xA2,0x40,0x12,0x44,0x82,0x44,0x31,0x01,0x82,0x80,
0x93,0xF7,0xF7,0x0F,0xBD,0x17,0x13,0x07,0xE0,0x0D,0xE3,0x66,0xF7,0xFE,0x37,0x26,
0x00,0x20,0x13,0x06,0x06,0x40,0x93,0x05,0x00,0x10,0x29,0x65,0x13,0x05,0x05,0xF0,
0x25,0x2D,0xD1,0xBF,0x01,0x45,0xC1,0xBF,0x11,0x11,0x06,0xCC,0x22,0xCA,0x26,0xC8,
0xB7,0x17,0x00,0x20,0x03,0xA7,0x47,0x63,0x03,0x54,0x47,0x00,0x83,0x54,0x27,0x00,
0xB3,0x67,0x94,0x00,0x8D,0x8B,0x89,0xCF,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,
0x15,0x47,0x23,0x87,0xE7,0x00,0xE2,0x40,0x52,0x44,0xC2,0x44,0x71,0x01,0x82,0x80,
0x22,0xC2,0x26,0xC0,0xB3,0x06,0x94,0x00,0xA9,0x67,0x93,0x87,0x07,0x10,0xE3,0xED,
0xD7,0xFC,0xA9,0x67,0x02,0x46,0x63,0x74,0xF6,0x00,0x63,0xEA,0xD7,0x02,0x83,0x57,
0xA7,0x00,0x3E,0xC4,0x03,0x57,0x67,0x00,0x3A,0xC6,0xA2,0x85,0x37,0x25,0x00,0x20,
0xAD,0x21,0xA2,0x47,0xC2,0x07,0x32,0x47,0xBA,0x97,0x63,0x82,0xA7,0x02,0xB7,0x17,
0x00,0x20,0x83,0xA7,0x07,0x62,0x0D,0x47,0x23,0x87,0xE7,0x00,0x6D,0xB7,0xB7,0x17,
0x00,0x20,0x83,0xA7,0x07,0x62,0x15,0x47,0x23,0x87,0xE7,0x00,0x69,0xBF,0xB7,0x17,
0x00,0x20,0x03,0xA7,0x07,0x62,0x95,0x67,0x93,0x87,0xB7,0x5B,0x23,0x16,0xF7,0x00,
0x37,0x26,0x00,0x20,0xA2,0x85,0x26,0x85,0x41,0x23,0x05,0xE5,0xA9,0x67,0x02,0x47,
0x63,0x75,0xF7,0x00,0x85,0x67,0x63,0x17,0xF7,0x00,0x93,0x07,0x00,0x08,0x12,0x47,
0x63,0xE2,0xE7,0x02,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x23,0x87,0x07,0x00,
0x99,0xBF,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x13,0x07,0x40,0xFA,0x23,0x87,
0xE7,0x00,0x91,0xB7,0x41,0x35,0x79,0xDD,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,
0x13,0x07,0x50,0xFA,0x23,0x87,0xE7,0x00,0x3D,0xB7,0x41,0x11,0x06,0xC6,0x22,0xC4,
0x26,0xC2,0x2A,0x84,0xAE,0x84,0x32,0xC0,0x71,0x99,0x8D,0x05,0xA2,0x95,0xF1,0x99,
0x37,0x16,0x00,0x20,0x13,0x06,0x86,0x21,0x89,0x8D,0x55,0x29,0x95,0xC0,0x0D,0x88,
0xB7,0x17,0x00,0x20,0x93,0x87,0x87,0x21,0x3E,0x94,0x02,0x47,0xBA,0x87,0xBA,0x94,
0x03,0x47,0x04,0x00,0x23,0x80,0xE7,0x00,0x05,0x04,0x85,0x07,0xE3,0x9A,0x97,0xFE,
0xB2,0x40,0x22,0x44,0x92,0x44,0x41,0x01,0x82,0x80,0x21,0x11,0x06,0xCA,0x22,0xC8,
0x26,0xC6,0x2A,0xC4,0x02,0xC2,0x81,0x44,0x01,0x44,0xA1,0xA8,0x82,0x47,0x93,0x85,
0x37,0x00,0xB7,0x17,0x00,0x20,0x13,0x86,0x87,0x21,0xF1,0x99,0x12,0x45,0x85,0x21,
0xA2,0x46,0xA1,0xCA,0x12,0x47,0x81,0x46,0xB7,0x17,0x00,0x20,0x13,0x86,0x87,0x21,
0x92,0x47,0xB3,0x07,0xF7,0x40,0xB2,0x97,0x83,0xC7,0x07,0x00,0x3E,0x94,0x42,0x04,
0x41,0x80,0xB3,0x87,0xE7,0x02,0xBE,0x94,0xC2,0x04,0xC1,0x80,0x85,0x06,0x05,0x07,
0x82,0x47,0xE3,0xEF,0xF6,0xFC,0xA2,0x47,0x82,0x46,0x95,0x8F,0x3E,0xC4,0x91,0xCB,
0x3A,0xC2,0x22,0x47,0x3A,0xC0,0x93,0x07,0x00,0x40,0xE3,0xF1,0xE7,0xFA,0x3E,0xC0,
0x71,0xBF,0x13,0x95,0x04,0x01,0x41,0x8D,0xD2,0x40,0x42,0x44,0xB2,0x44,0x61,0x01,
0x82,0x80,0x03,0x27,0x00,0x00,0xB7,0x17,0x00,0x20,0x23,0xAC,0xE7,0x20,0x83,0x26,
0x40,0x00,0x93,0x87,0x87,0x21,0xD4,0xC3,0x03,0x26,0x80,0x00,0x90,0xC7,0x75,0x8F,
0xFD,0x57,0x63,0x08,0xF7,0x04,0x41,0x11,0x06,0xC6,0x22,0xC4,0x26,0xC2,0x83,0x27,
0x00,0x03,0x37,0x17,0x00,0x20,0x13,0x07,0x87,0x21,0x1C,0xDB,0x03,0x24,0x40,0x03,
0x40,0xDB,0xC2,0x07,0xC1,0x83,0x19,0x67,0x13,0x07,0x67,0x81,0x63,0x97,0xE7,0x00,
0x93,0x04,0xC4,0xFF,0xC1,0x67,0x63,0xE6,0xF4,0x02,0xB7,0x17,0x00,0x20,0x83,0xA7,
0x07,0x62,0x45,0x47,0x23,0x87,0xE7,0x00,0xB2,0x40,0x22,0x44,0x92,0x44,0x41,0x01,
0x82,0x80,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x21,0x47,0x23,0x87,0xE7,0x00,
0x82,0x80,0x26,0x85,0xDD,0x3D,0x2A,0xC0,0x37,0x26,0x00,0x20,0x13,0x06,0x06,0x40,
0xA1,0x45,0x26,0x85,0x59,0x3D,0x83,0x27,0xC4,0xFF,0x02,0x47,0x63,0x0A,0xF7,0x00,
0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x0D,0x47,0x23,0x87,0xE7,0x00,0x6D,0xBF,
0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x23,0x87,0x07,0x00,0x75,0xB7,0x51,0x11,
0x06,0xC4,0x22,0xC2,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,0x25,0x47,0x23,0x87,
0xE7,0x00,0xC1,0x45,0xB7,0x17,0x00,0x20,0x03,0xA5,0x47,0x63,0xA9,0x2C,0x29,0xED,
0xB7,0x17,0x00,0x20,0x83,0xA7,0x47,0x63,0x83,0xC7,0xE7,0x00,0x13,0x07,0x00,0x03,
0x63,0x89,0xE7,0x02,0x13,0x07,0x10,0x03,0x63,0x8F,0xE7,0x00,0x13,0x07,0x30,0x03,
0x63,0x8D,0xE7,0x00,0x05,0x47,0x63,0x8C,0xE7,0x00,0x13,0x07,0x20,0x03,0x63,0x9C,
0xE7,0x02,0x91,0x47,0x01,0xA8,0x85,0x47,0x31,0xA0,0x89,0x47,0x21,0xA0,0x8D,0x47,
0x11,0xA0,0x81,0x47,0x13,0x97,0x37,0x00,0xB7,0x17,0x00,0x20,0x93,0x87,0xC7,0xB2,
0xBA,0x97,0xDC,0x43,0x82,0x97,0x01,0xA8,0xB7,0x17,0x00,0x20,0x83,0xA7,0x07,0x62,
0x09,0x47,0x23,0x87,0xE7,0x00,0xB1,0x24,0x37,0x14,0x00,0x20,0x03,0x25,0x04,0x62,
0xB7,0x17,0x00,0x20,0x83,0xA7,0x47,0x63,0x83,0xC7,0xE7,0x00,0xA3,0x07,0xF5,0x00,
0xB9,0x45,0x09,0x05,0xC9,0x2A,0x83,0x27,0x04,0x62,0x33,0x05,0xA0,0x40,0x23,0x90,
0xA7,0x00,0xA2,0x40,0x12,0x44,0x31,0x01,0x82,0x80,0xB7,0x07,0x00,0x20,0x03,0xC7,
0xF7,0x00,0x93,0x07,0x90,0x0E,0x63,0x03,0xF7,0x00,0x82,0x80,0x51,0x11,0x06,0xC4,
0x41,0x46,0xB7,0x05,0x00,0x20,0x37,0x15,0x00,0x20,0x13,0x05,0x45,0x62,0x3D,0x22,
0x39,0x3F,0x41,0x46,0xB7,0x15,0x00,0x20,0x93,0x85,0x85,0x20,0x37,0x05,0x00,0x20,
0x31,0x2A,0xA2,0x40,0x31,0x01,0x82,0x80,0xB7,0x17,0x00,0x20,0x03,0xA7,0x47,0x00,
0x93,0x06,0x10,0x0B,0x14,0xC3,0x03,0xA7,0x47,0x00,0x5C,0x47,0x85,0x8B,0xF5,0xDF,
0x83,0x26,0x00,0x00,0x93,0x07,0x00,0x0B,0x1C,0xC3,0xB7,0x17,0x00,0x20,0x03,0xA7,
0x47,0x00,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x13,0xC7,0xF6,0xFF,0x93,0x97,0x16,0x01,
0xC5,0x83,0x41,0x83,0x63,0x03,0xF7,0x00,0x82,0x80,0x37,0x16,0x00,0x20,0x03,0x27,
0x86,0x61,0x81,0x76,0x8D,0x06,0x75,0x8F,0x8A,0x07,0xD9,0x8F,0x23,0x2C,0xF6,0x60,
0x37,0x17,0x00,0x20,0x03,0x27,0x07,0x00,0x5C,0xC3,0xF9,0xBF,0x51,0x11,0x06,0xC4,
0x22,0xC2,0x26,0xC0,0xB7,0x17,0x00,0x20,0x93,0x87,0x07,0x00,0x94,0x43,0x25,0x67,
0x13,0x07,0xA7,0x25,0x98,0xCA,0x37,0x47,0x06,0x02,0x15,0x07,0x98,0xC2,0x94,0x43,
0xB7,0x37,0x49,0x13,0x93,0x87,0x97,0xE8,0xDC,0xC2,0x37,0x17,0x04,0x00,0x41,0x17,
0xD8,0xC6,0x37,0x17,0x00,0x20,0x23,0x2C,0xF7,0x60,0xB9,0x3F,0xB7,0x17,0x00,0x20,
0x03,0xA7,0x47,0x00,0x93,0x07,0x00,0x1E,0x1C,0xCB,0xB7,0x47,0x49,0x00,0x93,0x87,
0x07,0xE0,0x1C,0xD3,0x5C,0xD3,0xB7,0x07,0x00,0x20,0x37,0x17,0x00,0x20,0x23,0x2C,
0xF7,0x62,0x37,0x17,0x00,0x20,0x23,0x2E,0xF7,0x60,0xB7,0x14,0x00,0x20,0x13,0x87,
0x44,0x62,0xB7,0x17,0x00,0x20,0x23,0xAA,0xE7,0x62,0x37,0x14,0x00,0x20,0x13,0x07,
0x84,0x20,0xB7,0x17,0x00,0x20,0x23,0xA0,0xE7,0x62,0x41,0x46,0x81,0x45,0x37,0x05,
0x00,0x20,0x15,0x28,0x41,0x46,0x81,0x45,0x13,0x85,0x44,0x62,0x2D,0x20,0x41,0x46,
0x81,0x45,0x13,0x05,0x84,0x20,0x05,0x20,0x4D,0x3D,0xFD,0xBF,0x09,0x86,0x19,0xCA,
0x0A,0x06,0x2A,0x96,0x91,0x05,0x11,0x05,0x83,0xA7,0xC5,0xFF,0x23,0x2E,0xF5,0xFE,
0xE3,0x1A,0xA6,0xFE,0x82,0x80,0x09,0x86,0x01,0xCA,0x0A,0x06,0x2A,0x96,0x11,0x05,
0x23,0x2E,0xB5,0xFE,0xE3,0x1D,0xA6,0xFE,0x82,0x80,0x9D,0xC9,0x81,0x47,0x81,0x46,
0x01,0x46,0x33,0x07,0xF5,0x00,0x03,0x47,0x07,0x00,0x3A,0x96,0x42,0x06,0x41,0x82,
0x33,0x07,0xF7,0x02,0x36,0x97,0x93,0x16,0x07,0x01,0xC1,0x82,0x85,0x07,0x13,0x97,
0x07,0x01,0x41,0x83,0xE3,0x6F,0xB7,0xFC,0xC2,0x06,0x33,0xE5,0xC6,0x00,0x82,0x80,
0xAE,0x86,0x2E,0x86,0xD5,0xBF,0x85,0x81,0x95,0xC1,0xAA,0x87,0xFD,0x15,0xC2,0x05,
0xC1,0x81,0x86,0x05,0x09,0x05,0x33,0x87,0xA5,0x00,0x81,0x45,0x03,0xD5,0x07,0x00,
0xAA,0x95,0xC2,0x05,0xC1,0x81,0x89,0x07,0xE3,0x9A,0xE7,0xFE,0x2E,0x85,0x82,0x80,
0x89,0x81,0x09,0x81,0x95,0xC5,0x81,0x46,0x37,0x13,0x00,0x20,0xB3,0x07,0xD5,0x00,
0x13,0x07,0x43,0x00,0x83,0x22,0x07,0x00,0x23,0xA2,0xF2,0x00,0x23,0xA0,0xC2,0x00,
0x18,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x93,0x86,0x06,0x02,0xE3,0xE0,0xB6,0xFE,
0x82,0x80,0xB7,0x17,0x00,0x20,0x83,0xA7,0x47,0x00,0x98,0x4B,0x79,0x9B,0x98,0xCB,
0x98,0x4F,0x79,0x9B,0x98,0xCF,0xD8,0x4F,0x79,0x9B,0xD8,0xCF,0xD8,0x53,0x79,0x9B,
0xD8,0xD3,0x82,0x80,0xB7,0x17,0x00,0x20,0x83,0xA7,0x47,0x00,0x98,0x4B,0x13,0x67,
0x17,0x00,0x98,0xCB,0x98,0x4F,0x13,0x67,0x17,0x00,0x98,0xCF,0xD8,0x4F,0x13,0x67,
0x17,0x00,0xD8,0xCF,0xD8,0x53,0x13,0x67,0x17,0x00,0xD8,0xD3,0x82,0x80,0x51,0x11,
0x06,0xC4,0x13,0x06,0xA0,0x09,0xA9,0x65,0x01,0x45,0x9D,0x3F,0x13,0x06,0xB0,0x09,
0x93,0x05,0x00,0x10,0x01,0x45,0xAD,0x37,0xA2,0x40,0x31,0x01,0x82,0x80,0xA9,0x67,
0x13,0x07,0x00,0x0B,0x63,0x66,0xF5,0x00,0xD9,0x77,0x3E,0x95,0x13,0x07,0x10,0x0B,
0x89,0x81,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,0x94,0x43,0x98,0xC2,0x98,0x43,
0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x81,0xCD,0x8A,0x05,0xAA,0x95,0xAA,0x87,0x94,0x43,
0x33,0x87,0xA7,0x40,0x32,0x97,0x14,0xC3,0x91,0x07,0xE3,0x9A,0xF5,0xFE,0xB7,0x17,
0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,0x93,0x06,0x00,0x0B,0x14,0xC3,0x98,0x43,
0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x82,0x80,0x13,0x01,0xC1,0xFD,0x06,0xD0,0x22,0xCE,
0x26,0xCC,0x2A,0xC4,0x32,0xC2,0xAA,0x84,0xA9,0x67,0x63,0x65,0xF5,0x0E,0x59,0x74,
0x2A,0x94,0x93,0x07,0x10,0x09,0x3E,0xCA,0x93,0x07,0x10,0x0B,0x3E,0xC6,0x93,0x07,
0x70,0x09,0x3E,0xC8,0x09,0x80,0x93,0xD7,0x25,0x00,0x3E,0xC0,0xB7,0x17,0x00,0x20,
0x83,0xA7,0x47,0x00,0x13,0x07,0x00,0x1E,0x98,0xCB,0x29,0x3F,0x82,0x47,0x85,0xCB,
0x92,0x46,0x33,0x05,0xF4,0x00,0xB7,0x15,0x00,0x20,0x42,0x46,0x13,0x87,0x45,0x00,
0x1C,0x43,0xC0,0xC3,0x03,0xA3,0x06,0x00,0x23,0xA4,0x67,0x00,0x90,0xC3,0x18,0x43,
0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x05,0x04,0x91,0x06,0xE3,0x11,0xA4,0xFE,0xB7,0x17,
0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,0x93,0x06,0x00,0x1E,0x14,0xCB,0xB2,0x46,
0x14,0xC3,0x8C,0x43,0xDC,0x45,0x85,0x8B,0xF5,0xDF,0x82,0x46,0xD1,0xC2,0x22,0x43,
0x03,0x27,0x03,0x00,0x92,0x42,0x83,0xA7,0x02,0x00,0x63,0x10,0xF7,0x0C,0x13,0x95,
0x26,0x00,0x1A,0x95,0x9A,0x87,0x81,0x46,0x85,0x06,0x91,0x07,0x63,0x0F,0xF5,0x04,
0x90,0x43,0x33,0x87,0x67,0x40,0x16,0x97,0x18,0x43,0xE3,0x07,0xE6,0xFE,0xFD,0x57,
0x3E,0xC0,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,0x93,0x06,0x00,0x0B,
0x14,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x82,0x47,0x13,0x85,0x17,0x00,
0x13,0x35,0x15,0x00,0x33,0x05,0xA0,0x40,0x82,0x50,0x72,0x44,0xE2,0x44,0x13,0x01,
0x41,0x02,0x82,0x80,0x22,0x44,0x93,0x07,0x00,0x09,0x3E,0xCA,0x93,0x07,0x00,0x0B,
0x3E,0xC6,0x93,0x07,0x60,0x09,0x3E,0xC8,0x31,0xBF,0xFD,0x57,0x63,0x8A,0xF6,0x04,
0xD2,0x47,0x9C,0xC1,0xB7,0x17,0x00,0x20,0x03,0xA7,0x47,0x00,0x5C,0x47,0x85,0x8B,
0xF5,0xDF,0x82,0x46,0xD9,0xDE,0x22,0x46,0x18,0x42,0x12,0x45,0x1C,0x41,0x63,0x1B,
0xF7,0x02,0x8A,0x06,0xB2,0x96,0x01,0x47,0xB2,0x85,0x05,0x07,0x91,0x04,0x63,0x8C,
0x96,0x00,0x90,0x40,0xB3,0x87,0xB4,0x40,0xAA,0x97,0x9C,0x43,0xE3,0x07,0xF6,0xFE,
0xFD,0x57,0x3E,0xC0,0xBD,0xB7,0x3A,0xC0,0xAD,0xB7,0xFD,0x57,0x3E,0xC0,0x95,0xB7,
0x36,0xC0,0x85,0xB7,0xFD,0x57,0x3E,0xC0,0xA9,0xBF,0x51,0x11,0x06,0xC4,0x22,0xC2,
0x93,0x77,0x45,0x00,0x85,0xC7,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,
0x23,0x22,0x07,0x00,0x93,0x06,0x30,0x09,0x14,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,
0xF5,0xDF,0xF5,0x33,0xA2,0x40,0x12,0x44,0x31,0x01,0x82,0x80,0x93,0x76,0x15,0x00,
0x99,0xCE,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,0x23,0x22,0x07,0x00,
0x13,0x06,0x20,0x09,0x10,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x13,0x74,
0x25,0x00,0x1D,0xC4,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,0x98,0x43,0x23,0x22,
0x07,0x00,0x13,0x06,0x50,0x09,0x10,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,
0x89,0xEA,0x13,0x06,0xB0,0x09,0x93,0x05,0x00,0x10,0x01,0x45,0x11,0x3B,0x5D,0xB7,
0xD5,0xD2,0x13,0x06,0xA0,0x09,0xA9,0x65,0x01,0x45,0x19,0x33,0x41,0xDC,0xD5,0xB7,
0x41,0x11,0x06,0xC6,0x22,0xC4,0x26,0xC2,0x2A,0x84,0x02,0xC0,0xB7,0x14,0x00,0x20,
0x22,0x85,0xA1,0x3F,0x93,0x77,0x54,0x00,0x8D,0xC7,0x93,0x87,0x44,0x00,0x98,0x43,
0x93,0x06,0x00,0x09,0x14,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x83,0x27,
0x00,0x00,0xA5,0xE7,0x91,0x47,0xA9,0x66,0x98,0x43,0x25,0xE3,0x91,0x07,0xE3,0x9D,
0xD7,0xFE,0x93,0x77,0x64,0x00,0x95,0xC7,0x93,0x87,0x44,0x00,0x98,0x43,0x93,0x06,
0x10,0x09,0x14,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,0x83,0x27,0x00,0x00,
0x8D,0xEF,0x91,0x47,0x93,0x06,0x00,0x10,0x98,0x43,0x05,0xEB,0x91,0x07,0xE3,0x9D,
0xD7,0xFE,0x22,0x85,0xDD,0x3D,0x02,0x45,0xB7,0x17,0x00,0x20,0x93,0x87,0x47,0x00,
0x98,0x43,0x93,0x06,0x00,0x0B,0x14,0xC3,0x98,0x43,0x5C,0x47,0x85,0x8B,0xF5,0xDF,
0xB2,0x40,0x22,0x44,0x92,0x44,0x41,0x01,0x82,0x80,0x82,0x47,0x85,0x07,0x3E,0x87,
0x3E,0xC0,0x95,0x47,0xE3,0x16,0xF7,0xF6,0x7D,0x55,0xF9,0xB7,0x30,0x00,0x00,0x00,
0x48,0x02,0x00,0x20,0x31,0x00,0x00,0x00,0xBE,0x00,0x00,0x20,0x33,0x00,0x00,0x00,
0x22,0x04,0x00,0x20,0x01,0x00,0x00,0x00,0x82,0x00,0x00,0x20,0x32,0x00,0x00,0x00,
0x64,0x01,0x00,0x20,0x00,0x70,0x00,0x40,0x00,0x10,0x00,0x40
};

/*
    delay or sleep some time (the unit is milliseconds)
*/

struct sm_touch_dev upd_st_dev;

static uint16_t caculate_checksum_u16(uint16_t *buf, uint32_t length)
{
    uint16_t sum;
    uint32_t len, i;

    sum = 0;
    len = length >> 1;

    for(i = 0; i < len; i++){
        sum += buf[i];
    }

    return sum;
}

static uint32_t caculate_checksum_ex(uint8_t * buf, uint32_t length)
{
    uint32_t k, combchk;
    uint16_t check = 0;
    uint16_t checkex = 0;

    for(k = 0; k < length; k++){
        check   += buf[k];
        checkex += (uint16_t)(k * buf[k]);
    }

    combchk = (checkex<<16) | check;

    return combchk;
}









static int32_t semi_touch_write_core_and_check(uint32_t addr, const uint8_t* buffer, uint16_t len)
{
    int32_t ret = 0, once = 0, index = 0, retry = 0;
    uint8_t core_cmp_buffer[MAX_IO_BUFFER_LEN-4];
    const int32_t max_try = 3;

    while(len > 0){
        retry = 0;
        do{
            ret = SEMI_DRV_ERR_OK;
            once = (len<(MAX_IO_BUFFER_LEN-4))?len:(MAX_IO_BUFFER_LEN-4);
            ret = semi_touch_write_bytes(addr, (uint8_t*)buffer, once);
            ret = semi_touch_read_bytes(addr, core_cmp_buffer, once);
            for(index = 0; index < once; index++){
                if(core_cmp_buffer[index] != buffer[index]){
                    ret = -SEMI_DRV_ERR_CHECKSUM;
                    break;
                }
            }
            if(SEMI_DRV_ERR_OK == ret){
                break;
            }         
        }while(++retry < max_try);

        if(SEMI_DRV_ERR_OK != ret){
            break;
        }   
        
        addr   += once;
        buffer += once;
        len    -= once;
    }

    return ret;
}



int32_t cmd_send_to_tp(struct m_ctp_cmd_std_t *ptr_cmd, struct m_ctp_rsp_std_t *ptr_rsp, int32_t once_delay, 
                             int32_t poolgap)
{
    int32_t ret = -SEMI_DRV_ERR_HAL_IO;
    uint32_t retry = 0;
    uint32_t cmd_rsp_ok = 0;

    ptr_cmd->tag = 0xE9;
    ptr_cmd->chk = 1 + ~(caculate_checksum_u16((uint16_t*)&ptr_cmd->d0, sizeof(struct m_ctp_cmd_std_t) - 2));
    ret = semi_touch_write_bytes(0x20000000, (uint8_t*)ptr_cmd, sizeof(struct m_ctp_cmd_std_t));
    if(ret != 0){   // TODO: need confirm!!!
        return -1;
    }

    platform_delay_ms(once_delay);
    while(retry++ < 20){
	//wdg_feed();	//Feed watch dog.LYQ20220310
 	////("%s %d",__FUNCTION__,__LINE__);
        platform_delay_ms(poolgap);
        ret = semi_touch_read_bytes(0x20000000, (uint8_t*)ptr_rsp, sizeof(struct m_ctp_rsp_std_t));
        if(ret != 0){   // TODO: need confirm!!!
            return -1;
        }

        if(ptr_cmd->id != ptr_rsp->id){
            continue;
        }

        if(!caculate_checksum_u16((uint16_t*)ptr_rsp, sizeof(struct m_ctp_rsp_std_t))){
            if(0 == ptr_rsp->cc){      //success
                cmd_rsp_ok = 1;
            }
            break;
        }
    }

    if(!cmd_rsp_ok) ret = -SEMI_DRV_ERR_TIMEOUT;

    return ret;
}

/*
通过命令交互方式读取数据，一个命令单次最多1KB，超过1KB
的操作会被拆分为多次命令交互

返回值：
     0: 成功
其它值：失败
*/
int32_t semi_touch_nvm_read(uint8_t *pdes, uint32_t adr, uint32_t len)
{
    int32_t ret = -1;
    uint32_t left = len;
    uint32_t local_check, retry;
    struct m_ctp_cmd_std_t cmd_send_tp;
    struct m_ctp_rsp_std_t ack_from_tp;

    cmd_send_tp.id = CMD_MEM_RD;

    while (left) {
        len = (left > 1024) ? 1024 : left;

        cmd_send_tp.d0 = adr & 0xffff;
        cmd_send_tp.d1 = len;
        cmd_send_tp.d2 = 0;
        cmd_send_tp.d3 = NVM_R;
        cmd_send_tp.d5 = (adr >> 16) & 0xffff;

        retry = 0;
        while (retry++ < 3) {
            ack_from_tp.id = CMD_NA;
            ret = cmd_send_to_tp(&cmd_send_tp, &ack_from_tp, 20, 10);
            if(SEMI_DRV_ERR_OK != ret){
                continue;
            }

            semi_touch_read_bytes(TP_RD_BUFF_ADDR, pdes, len);

            local_check = caculate_checksum_ex(pdes, len);
            if ((ack_from_tp.d0 != (uint16_t)local_check) || 
                (ack_from_tp.d1 != (uint16_t)(local_check >> 16))){
                ret = -SEMI_DRV_ERR_CHECKSUM;
                continue;
            }
            break;
        }

        adr  += len;
        left -= len;
        pdes += len;
        if(ret != SEMI_DRV_ERR_OK){
            break;
        }
    }

    return (ret!=SEMI_DRV_ERR_OK)?-1:SEMI_DRV_ERR_OK;
}

int32_t semi_touch_nvm_write(uint8_t *psrc, uint32_t adr, uint32_t len)
{
    int32_t ret = -1;
    uint32_t left = len;
    uint32_t retry, combChk;
    struct m_ctp_cmd_std_t cmd_send_tp;
    struct m_ctp_rsp_std_t ack_from_tp;
	////("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);

    cmd_send_tp.id = CMD_MEM_WR;

    while (left) {
		//wdg_feed(); //Feed watch dog.LYQ20220310
		////("%s %d",__FUNCTION__,__LINE__);
        len = (left > 256) ? 256 : left;

        combChk = caculate_checksum_ex(psrc, len);

        cmd_send_tp.d0 = adr & 0xffff;    /* addrss space[0,64K)  */    
        cmd_send_tp.d1 = len;
        cmd_send_tp.d3 = NVM_W;
        cmd_send_tp.d2 = (uint16_t) combChk;
        cmd_send_tp.d4 = (uint16_t) (combChk >> 16);
        cmd_send_tp.d5 = (adr >> 16) & 0xffff;

        retry = 0;
        while (++retry <= 3) {
            ret = semi_touch_write_bytes(TP_WR_BUFF_ADDR, psrc, len);
            if(SEMI_DRV_ERR_OK != ret) continue;

            ack_from_tp.id = CMD_NA;
            ret = cmd_send_to_tp(&cmd_send_tp, &ack_from_tp, 200, 20);
            if(SEMI_DRV_ERR_OK != ret) continue;
            //check_break_if_fail(ret, NULL);

            break;
        }
        
        left -= len;
        adr  += len;
        psrc += len;
        if(SEMI_DRV_ERR_OK != ret){
            ret = -1;
            break;
        }
    }

    return ret;
}

int32_t semi_touch_burn_erase(void)
{
    int32_t ret = SEMI_DRV_ERR_OK;

    struct m_ctp_cmd_std_t cmd_send_tp;
    struct m_ctp_rsp_std_t ack_from_tp;

    cmd_send_tp.id = CMD_FLASH_ERASE;
    cmd_send_tp.d0 = 0x01;
 	////("%s %d",__FUNCTION__,__LINE__);

    return cmd_send_to_tp(&cmd_send_tp, &ack_from_tp, 1000, 20);
}

int32_t semi_touch_run_ram_code(const uint8_t* bin_code, uint16_t len)
{
    int32_t retry;
    int32_t ret = 0, reg_value = 0;

    for(retry = 0; retry < 5; retry++){
        //reset mcu
        semi_touch_reset();
        
 	   ////("%s %d",__FUNCTION__,__LINE__);
        //hold mcu
        reg_value = 0x12044000;
        ret = semi_touch_write_bytes(0x40007000, (uint8_t*)&reg_value, 4);
        if(ret < 0){
            continue;
        }

        //open auto feed
        reg_value = 0x0000925a;
        ret = semi_touch_write_bytes(0x40007010, (uint8_t*)&reg_value, 4);
        if(ret < 0){
            continue;
        }

        //run ramcode
        ret = semi_touch_write_core_and_check(0x20000000, bin_code, len);
        if(ret < 0){
            continue;
        }

        break;
    }
    if(ret < 0){
        return -1;
    }

    //remap
    reg_value = 0x12044002;
    ret = semi_touch_write_bytes(0x40007000, (uint8_t*)&reg_value, 4);
    if(ret < 0){
        return -1;
    }

    //release mcu
    reg_value = 0x12044003;
    ret = semi_touch_write_bytes(0x40007000, (uint8_t*)&reg_value, 4);
    if(ret < 0){
        return -1;
    }

    platform_delay_ms(30);

    return 0;
}

/*
    This function will put IC into NVM mode, call it carefully and must reset 
    the chip before entering normal mode.
*/
int32_t semi_touch_enter_burn_mode(void)
{
    struct m_ctp_cmd_std_t cmd_send_tp;
    struct m_ctp_rsp_std_t ack_from_tp;
 	////("%s %d",__FUNCTION__,__LINE__);
    ack_from_tp.d0 = 0;
    cmd_send_tp.id = CMD_IDENTITY;
    cmd_send_to_tp(&cmd_send_tp, &ack_from_tp, 20, 10);

    if((ack_from_tp.d0 == 0xE9A2) && (ack_from_tp.d1 == 0x165d)){
        upd_st_dev.ctp_run_status = CTP_UPGRAD_RUNING;
        return SEMI_DRV_ERR_OK;
    }

    if(semi_touch_run_ram_code(fw_5816_burn, sizeof(fw_5816_burn)) != 0){
        return -1;
    }

    cmd_send_tp.id = CMD_IDENTITY;
    if(cmd_send_to_tp(&cmd_send_tp, &ack_from_tp, 20, 10) != 0){
        return -1;
    }

    if((ack_from_tp.d0 == 0xE9A2) && (ack_from_tp.d1 == 0x165d)){
        upd_st_dev.ctp_run_status = CTP_UPGRAD_RUNING;
        return SEMI_DRV_ERR_OK;
    }

    upd_st_dev.ctp_run_status = CTP_POINTING_WORK;

    return -SEMI_DRV_ERR_HAL_IO;
}


int32_t semi_get_backup_pid(uint32_t *id)
{
    if(semi_touch_enter_burn_mode() != 0){
        return -1;
    }

    return semi_touch_nvm_read((uint8_t *)id, VID_PID_BACKUP_ADDR, 4);
}

int32_t semi_touch_check_boot_update(uint32_t boot_offset, uint32_t len, uint32_t match_offset, 
                                           uint32_t n_match, uint32_t force_update)
{
    uint32_t idlist;
    int32_t ret = SEMI_DRV_ERR_OK;
    int32_t k, idx_active;
    uint16_t upd_boot_ver = 0;
    const uint8_t *ptBoot;
    const uint8_t *ptMatch;

    ptBoot  = chsc_upd_data + boot_offset;
    ptMatch = chsc_upd_data + match_offset;
    if((ptBoot[0x30] != 0x16) || (ptBoot[0x31] != 0x58)){
        //("chsc::no chsc5816 found\n");
        return -1;
    }

    /*
        If we need to compare versions, only update the newer version
    */
    idx_active = -1;
    upd_boot_ver = (ptBoot[0x3f] << 8) + ptBoot[0x3e];
    for (k = 0; k < n_match; k++) {
        idlist = (uint32_t)ptMatch[3];
        idlist = (idlist<<8) + (uint32_t)ptMatch[2];
        idlist = (idlist<<8) + (uint32_t)ptMatch[1];
        idlist = (idlist<<8) + (uint32_t)ptMatch[0];
        ptMatch= ptMatch+4;
        //("chsc::pid_vid in list=0x%x\n", idlist);
        if ((idlist & 0xffffff00) == (upd_st_dev.vid_pid & 0xffffff00)) {
            if((upd_st_dev.fw_ver < upd_boot_ver) || force_update){   //版本比较
                idx_active = k;
            }
            break;
        }
    }

    if(idx_active >= 0) {
		//("%s %d",__FUNCTION__,__LINE__);
        if(semi_touch_enter_burn_mode() != 0){
            return -1;
        }

		//("%s %d",__FUNCTION__,__LINE__);
        if(semi_touch_burn_erase() != 0){
            return -1;
        }
		//("%s %d",__FUNCTION__,__LINE__);

        if(semi_touch_nvm_write((uint8_t *)ptBoot, 0x00000000, len) != 0){
            return -1;
        }

        semi_touch_setup_check();
    }
    
    return 0;
}

/*
    Return Value:
        0x01: reject caller's request
        -1  : some error
        0x00: successfull
*/
int32_t semi_touch_update_updfile(uint32_t force_update)
{
    int32_t ret = SEMI_DRV_ERR_OK;
    uint32_t boot_offset;
    uint32_t match_offset;
    uint8_t *ptHead;
    struct chsc_updfile_header upd_header;

    ptHead = (uint8_t *)&upd_header;
    for(boot_offset=0; boot_offset<sizeof(upd_header); boot_offset++){
        ptHead[boot_offset] = chsc_upd_data[boot_offset];
    }

    //("chsc::upd_header.sig=0x%x ,upd_header.n_match:%d\n",upd_header.sig,upd_header.n_match);
    if ((upd_header.sig != 0x43534843) || (upd_header.n_match == 0)) {
        return -SEMI_DRV_ERR_NOT_MATCH;
    }
	//("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);

    if ((upd_header.len_boot <= 15*1024) || (upd_header.len_boot >= 40*1024)) {
        return -SEMI_DRV_ERR_NOT_MATCH;
    }

    match_offset = sizeof(upd_header);
    boot_offset  = sizeof(upd_header) + (upd_header.n_match*4) + upd_header.len_cfg;
	//("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);

    ret = semi_touch_check_boot_update(boot_offset, upd_header.len_boot, match_offset, upd_header.n_match, force_update);

    return ret;
}

/*
    检测Chipsemi开机情况，以及版本比对，根据需要做自动更新功能
*/
int32_t semi_check_and_update(sm_touch_dev *st_dev)
{
    int32_t ret = -1; 
    uint32_t backup_vid_pid = 0;
	memcpy(&upd_st_dev,st_dev,sizeof(sm_touch_dev));
	//("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);
    if(upd_st_dev.setup_ok == 0){
        if(semi_get_backup_pid(&backup_vid_pid) != 0){
            return -1;
        }
		//("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);
        upd_st_dev.vid_pid = backup_vid_pid;
    }
	//("%s %d vid_pid:%d",__FUNCTION__,__LINE__,upd_st_dev.vid_pid);

    if((0 != upd_st_dev.vid_pid) && (0xffffffff != upd_st_dev.vid_pid)){
		//("%s %d setup_ok:%d",__FUNCTION__,__LINE__,upd_st_dev.setup_ok);
        ret = semi_touch_update_updfile(upd_st_dev.setup_ok ? 0 : 1);
    }else{ //we don't know what kind if product it is
        ret = -SEMI_DRV_ERR_NOT_MATCH;
    }

    return ret;
}











