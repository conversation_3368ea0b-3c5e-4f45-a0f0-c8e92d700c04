/**
  ******************************************************************************
  * @file   st77903.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the st77903.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __ST77903_H__
#define __ST77903_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST77903
  * @{
  */

/** @defgroup ST77903_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ST77903_Exported_Constants
  * @{
  */

/**
  * @brief ST77903 chip IDs
  */
#define ST77903_ID                  0x85

/**
  * @brief  ST77903 Size
  */
#define  ST77903_LCD_PIXEL_WIDTH    ((uint16_t)400)
#define  ST77903_LCD_PIXEL_HEIGHT   ((uint16_t)400)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ST77903_ORIENTATION_PORTRAIT         ((uint32_t)0x00) /* Portrait orientation choice of LCD screen  */
#define ST77903_ORIENTATION_LANDSCAPE        ((uint32_t)0x01) /* Landscape orientation choice of LCD screen */
#define ST77903_ORIENTATION_LANDSCAPE_ROT180 ((uint32_t)0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ST77903 Registers
  */
#define ST77903_LCD_ID             0x04
#define ST77903_POWER_MODE         0x09
#define ST77903_SLEEP_IN           0x10
#define ST77903_SLEEP_OUT          0x11
#define ST77903_PARTIAL_DISPLAY    0x12
#define ST77903_DISPLAY_INVERSION  0x21
#define ST77903_DISPLAY_OFF        0x28
#define ST77903_DISPLAY_ON         0x29
#define ST77903_WRITE_RAM          0x2C
#define ST77903_READ_RAM           0x2E
#define ST77903_CASET              0x2A
#define ST77903_RASET              0x2B
#define ST77903_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define ST77903_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define ST77903_TEARING_EFFECT     0x35
#define ST77903_NORMAL_DISPLAY     0x36
#define ST77903_IDLE_MODE_OFF      0x38
#define ST77903_IDLE_MODE_ON       0x39
#define ST77903_COLOR_MODE         0x3A
#define ST77903_WBRIGHT            0x51
#define ST77903_WCTRL              0x53
#define ST77903_PORCH_CTRL         0xB2
#define ST77903_FRAME_CTRL         0xB3
#define ST77903_GATE_CTRL          0xB7
#define ST77903_VCOM_SET           0xBB
#define ST77903_LCM_CTRL           0xC0
#define ST77903_VDV_VRH_EN         0xC2
#define ST77903_VDV_SET            0xC4
#define ST77903_VCOMH_OFFSET_SET   0xC5
#define ST77903_FR_CTRL            0xC6
#define ST77903_POWER_CTRL         0xD0
#define ST77903_PV_GAMMA_CTRL      0xE0
#define ST77903_NV_GAMMA_CTRL      0xE1
#define ST77903_SPI2EN             0xE7


/**
  * @}
  */

/** @defgroup ST77903_Exported_Functions
  * @{
  */
void     ST77903_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ST77903_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ST77903_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ST77903_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ST77903_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ST77903_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ST77903_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ST77903_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ST77903_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ST77903_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */



#ifdef __cplusplus
}
#endif

#endif /* __ST77903_H__ */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
