/*
 * Copyright (c) 2019 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#ifndef _MXS_H_
#define _MXS_H_

/*-----------------------------------------------------------------------------
 * HEARDER FILES
 *---------------------------------------------------------------------------*/
#include <stdbool.h>
#include <stdint.h>

#ifdef _WIN32
#ifdef MXS_DLL_EXPORTS
#define MXS_API __declspec(dllexport)
#else
#define MXS_API
#endif
#else
#define MXS_API
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * DATA STRUCT
 *---------------------------------------------------------------------------*/


typedef struct mxs_st mxs_t;

// 初始化参数
typedef struct mxs_init_parameter
{
    uint8_t cpix;
    uint8_t cpiy;
    bool twelve_bit_mode;
    bool swap_xy;
    bool inv_x;
    bool inv_y;
} mxs_init_parameter_t;

// motion状态信息
typedef enum mxs_motion_event
{
    MXS_MOTION_NONE = 0,
    MXS_MOTION_PRESS,
    MXS_MOTION_RELEASE,
    MXS_MOTION_ROTATE
} mxs_motion_event_t;

// motion数据
typedef struct
{
    mxs_motion_event_t motion_event;
    int displacement;
} mxs_motion_data_t;

// 定义芯片对转轴的原始检测数据
typedef struct
{
    uint8_t status;
    int16_t dx;
    int16_t dy;
} mxs_raw_data_t;

/*-----------------------------------------------------------------------------
 * FUNCTIONS DECLARATION
 *---------------------------------------------------------------------------*/
/**
 * @brief 初始化mxs实例
 * 
 * @return 返回句柄则成功，NULL则失败
 * */
MXS_API mxs_t* mxs_init(mxs_init_parameter_t* init_parameter);

/**
 * @brief 销毁mxs实例
 * 
 * @param[in] mxs       mxs实例句柄
 * */
MXS_API void mxs_deinit(mxs_t* mxs);

/**
 * @brief 获取motion数据
 * 
 * @param {mxs_t*} mxs 实例句柄
 * @param {mxs_motion_data_t*} motion_data 传感器状态
 * @param {uint32_t} timestamp 微秒级时间戳
 * 
 * @return 0即成功，其他即失败
 */
MXS_API int mxs_get_motion_data(mxs_t* mxs, mxs_motion_data_t* motion_data);

/**
 * @brief 获取原始数据
 * 
 * @param[in] mxs    mxs实例句柄
 * @param[in] raw_data     （获取结果）原始数据存储对象
 * 
 * @return 0即成功，其他即失败
 * */
MXS_API int mxs_get_raw_data(mxs_t* mxs, mxs_raw_data_t* raw_data);

/**
 * @brief PD模式使能关闭
 * 
 * @param[in] true_or_false   开关
 * 
 * @return 0即成功，其他即失败
 * */
MXS_API int mxs_set_power_up(bool true_or_false);


/**
 * @brief 注册用户回调函数
 */
int  mxs_register_trigger_callback(void (*callback)(bool step));



#ifdef __cplusplus
}
#endif

#endif  //  _MXS_H_
