#ifndef _IPC_CONFIG_H_
#define _IPC_CONFIG_H_
#include "board.h"

#ifdef __cplusplus
extern "C" {
#endif

#define IPC_HL2_QUEUE (2)

#define HL2_IPC_TX_BUF_ADDR         (HCPU2LCPU_MB_CH2_BUF_START_ADDR)   /*  */
#define HL2_IPC_TX_BUF_ADDR_ALIAS   (HCPU_ADDR_2_LCPU_ADDR(HL2_IPC_TX_BUF_ADDR))
#define HL2_IPC_RX_BUF_ADDR         LH2_IPC_TX_BUF_ADDR_ALIAS

#define LH2_IPC_TX_BUF_ADDR         (LCPU2HCPU_MB_CH2_BUF_START_ADDR)
#define LH2_IPC_TX_BUF_ADDR_ALIAS   (LCPU_ADDR_2_HCPU_ADDR(LH2_IPC_TX_BUF_ADDR))
#define LH2_IPC_RX_BUF_ADDR         HL2_IPC_TX_BUF_ADDR_ALIAS

#define IPC_HL3_QUEUE (3)

#define HL3_IPC_TX_BUF_ADDR         (HCPU2LCPU_MB_CH3_BUF_START_ADDR)   /*  */
#define HL3_IPC_TX_BUF_ADDR_ALIAS   (HCPU_ADDR_2_LCPU_ADDR(HL3_IPC_TX_BUF_ADDR))
#define HL3_IPC_RX_BUF_ADDR         LH3_IPC_TX_BUF_ADDR_ALIAS

#define LH3_IPC_TX_BUF_ADDR         (LCPU2HCPU_MB_CH3_BUF_START_ADDR)
#define LH3_IPC_TX_BUF_ADDR_ALIAS   (LCPU_ADDR_2_HCPU_ADDR(LH3_IPC_TX_BUF_ADDR))
#define LH3_IPC_RX_BUF_ADDR         HL3_IPC_TX_BUF_ADDR_ALIAS

#define IPC_HL4_QUEUE (4)           //sensorhub

#define HL4_IPC_TX_BUF_ADDR         (HCPU2LCPU_MB_CH4_BUF_START_ADDR)
#define HL4_IPC_TX_BUF_ADDR_ALIAS   (HCPU_ADDR_2_LCPU_ADDR(HL4_IPC_TX_BUF_ADDR))
#define HL4_IPC_RX_BUF_ADDR         LH4_IPC_TX_BUF_ADDR_ALIAS

#define LH4_IPC_TX_BUF_ADDR         (LCPU2HCPU_MB_CH4_BUF_START_ADDR)
#define LH4_IPC_TX_BUF_ADDR_ALIAS   (LCPU_ADDR_2_HCPU_ADDR(LH4_IPC_TX_BUF_ADDR))
#define LH4_IPC_RX_BUF_ADDR         HL4_IPC_TX_BUF_ADDR_ALIAS

#define IPC_HL5_QUEUE (5)           //sensorhub

#define HL5_IPC_TX_BUF_ADDR         (HCPU2LCPU_MB_CH5_BUF_START_ADDR)
#define HL5_IPC_TX_BUF_ADDR_ALIAS   (HCPU_ADDR_2_LCPU_ADDR(HL5_IPC_TX_BUF_ADDR))
#define HL5_IPC_RX_BUF_ADDR         LH5_IPC_TX_BUF_ADDR_ALIAS

#define LH5_IPC_TX_BUF_ADDR         (LCPU2HCPU_MB_CH5_BUF_START_ADDR)
#define LH5_IPC_TX_BUF_ADDR_ALIAS   (LCPU_ADDR_2_HCPU_ADDR(LH5_IPC_TX_BUF_ADDR))
#define LH5_IPC_RX_BUF_ADDR         HL5_IPC_TX_BUF_ADDR_ALIAS


#ifdef __cplusplus
}
#endif

#endif /* _IPC_CONFIG_H_ */

