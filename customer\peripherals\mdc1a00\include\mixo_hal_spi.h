/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
/*-----------------------------------------------------------------------------
 * DATA TYPE DECLARATION
 *---------------------------------------------------------------------------*/

/**
 * @brief GPIO simulated SPI configuration
 * @note GPIO simulated SPI support 4 different master modes by setting 2 values
 *       , CPOL and CPHA, there are
 *       - mode 0 (CPOL = 0, CPHA = 0)
 *       - mode 1 (CPOL = 0, CPHA = 1)
 *       - mode 2 (CPOL = 1, CPHA = 0)
 *       - mode 3 (CPOL = 1, CPHA = 1)
 */
typedef struct mixo_spi_st mixo_spi_t;

/*-----------------------------------------------------------------------------
 * PUBLIC FUNCTIONS DECLARATION
 *---------------------------------------------------------------------------*/

/**
 * @brief The synchronization operation when switching to 2-wire SPI
 */
void mixo_hal_spi_resync(void);

/**
 * @brief  spi读操作
 * 
 * @param addr  读地址
 * @param buffer  读buffer
 * @param size_to_transfer  读字节数
 * @return int 
 */
int mixo_hal_spi_read(uint8_t addr, uint8_t* buffer,
                      uint32_t size_to_transfer);

/**
 * @brief  spi写操作
 * 
 * @param buffer     写buffer
 * @param size_to_transfer  写字节数
 * @return int  0 on success. otherwise error code.
 */
int mixo_hal_spi_write(uint8_t* buffer,
                       uint32_t size_to_transfer);

#ifdef __cplusplus
}
#endif
