#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I $SDK_ROOT/drivers/cmsis/sf32lb56x
#include "rtconfig.h"
#include "mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE {    ; load region size_region
  ER_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   *(FSymTab)
   *.o (.l1_ret_text_*)
   *.o (.l1_ret_rodata_*)
   startup_bf0_lcpu.o (.text)
   bf0_hal_mpi.o (+RO)
   bf0_hal_mpi_ex.o (+RO)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   bf0_hal_lpaon.o (+RO)
   drv_spi_flash.o (+RO)
   system_bf0_ap.o (+RO)
   bf0_hal.o (+RO)
   
   bf0_hal_rcc.o   (+RO)

   bf0_pm.o        (.text.sifli_light_handler)
   bf0_pm.o        (.text.sifli_deep_handler)
   bf0_pm.o        (.text.sifli_standby_handler)
   bf0_pm.o        (.text.SystemPowerOnModeInit)
   bf0_pm.o        (.text.SystemPowerOnModeGet)
   bf0_pm.o        (.text.BSP_GPIO_Set)
   bsp_init.o         (+RO)
   bsp_lcd_tp.o       (+RO)
   bsp_pinmux.o       (+RO)
   bsp_power.o        (+RO)
   bf0_hal_gpio.o     (+RO)
   bf0_hal.o          (.text.HAL_Init)
   bf0_hal.o          (.text.HAL_Delay_us)
   bf0_hal.o          (.text.HAL_Delay_us_)   
   *.o                (.text.HAL_MspInit)
   bf0_hal_pinmux.o   (+RO)
   bf0_pin_const.o    (+RO)
   *.o                (.text.rt_memset)
   ;*.o                (.text.mpu_config)
   ;*.o                (.rodata.mpu_config)
   
  }
  
  RW_IRAM1 AlignExpr(LPSYS_RAM_BASE+ImageLength(ER_IROM1), 16) ALIGN 16 NOCOMPRESS  {  ; RW data
   .ANY (+RW +ZI)
  }
  ; Load Address must be equal to Exec Address
  ScatterAssert((LoadBase(RW_IRAM1) OR 0x20000000) == ImageBase(RW_IRAM1))
  ScatterAssert((ImageLength(ER_IROM1)+ImageLength(RW_IRAM1)+LCPU_MBOX_SIZE)<LPSYS_RAM_SIZE)
}

LR_IROM2 LCPU_FLASH_CODE_START_ADDR LCPU_FLASH_CODE_SIZE {
   ER_IROM2 LCPU_FLASH_CODE_START_ADDR LCPU_FLASH_CODE_SIZE {
   .ANY (+RO)
  }
}