/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>
#include "mixo_hal_spi.h"
#include "mxs_reg_access.h"

/*-----------------------------------------------------------------------------
 * DATA TYPE DECLARATION
 *---------------------------------------------------------------------------*/
typedef enum
{
    MIXO_HAL_OK = 0,
    MIXO_HAL_ETIMEOUT,
    MIXO_HAL_ENULL,
    MIXO_HAL_EINVAL,
    MIXO_HAL_EIO,
} mixo_hal_status_t;

/*-----------------------------------------------------------------------------
 * MACRO
 *---------------------------------------------------------------------------*/
#define LOG_TAG "MIXO_HAL"

/*-----------------------------------------------------------------------------
 * FUNCTIONS DECLARATION
 *---------------------------------------------------------------------------*/
/**
 * @brief MIXO HAL initialization.
 *
 * @return 0 on success. otherwise error code.
 */
int mixo_hal_init(void);

/**
 * @brief MIXO HAL layer deinitialization.
 */
void mixo_hal_deinit(void);

/**
 * @brief Read debug pin
 *
 * Only used for debugging purpose. Can be empty implementation.
 *
 * @param[in]  v         1 set pin to high;  0 set pin to low
 *
 * @return 0 on success. otherwise error code.
 */
int mixo_hal_gpio_debug_set(uint8_t v);

/**
 * @brief Display ui of crown
 *
 * @param[in]  dx         set raw data of dx
 * @param[in]  dy         set raw data of dy
 *
 * @return 0 on success. otherwise error code.
 */
int mixo_ui_display(int dx, int dy);

/**
 * @brief Inquire the button pin if was triggered
 *
 * @return true on triggered
 * @return false on not triggered
 */
bool mixo_hal_is_btn_triggered();

/**
 * @brief Inquire the mot pin if was triggered
 *
 * @return true on triggered
 * @return false on not triggered
 */
bool mixo_hal_is_mot_triggered();

/**
 * @brief To control the LED pin to blink
 *
 * @param on_off the switch to control
 */
void mixo_hal_led_control(bool on_off);

#ifdef __cplusplus
}
#endif
