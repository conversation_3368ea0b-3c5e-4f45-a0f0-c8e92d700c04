/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : swuart.c
*<AUTHOR> 
*@Date : 2025/01/15
*@Description : Software uart driver header file
************************************************************************/
#include "swuart.h"
#include <stdio.h>
#include <string.h>

#define TX_PIN 1
#define RX_PIN 3

//同步异步？
#define ASYNC 1

//发送buffer
#define TX_BUFFER_SIZE 256
//接收buffer
#define RX_BUFFER_SIZE 256

//结束位长度
#define END_LENGTH 1

//发送接口操作
#define TX_IO(stata) HAL_GPIO_WritePin(hwp_gpio1, TX_PIN, stata)
//接收接口读取
#define RX_IO() HAL_GPIO_ReadPin(hwp_gpio1, RX_PIN)

//内存分配接口
#define swuart_malloc(size)  malloc(size)
//内存释放接口
#define swuart_free(ptr)     free(ptr)

//默认波特率
#define DEFAULT_BAUD_RATE 20000

//波特率
static uint32_t g_baud_rate = DEFAULT_BAUD_RATE;

//半位时间
static uint32_t g_half_bit_time = 0;

//发送定时器中断标志位
static volatile bool tx_time_flag = false;

//硬件定时器
static rt_device_t swuart_timer_dev = NULL;

//信号量
static rt_sem_t swuart_sem = NULL;

//接收buffer
static uint8_t *rx_buffer;

// 接收缓冲区的读写指针和计数器
static uint16_t rx_write_index = 0;
static uint16_t rx_read_index = 0;
static uint16_t rx_count = 0;


// 向接收缓冲区写入数据
static int rx_buffer_write_byte(uint8_t data)
{
    // 检查缓冲区是否已满
    if (rx_count >= RX_BUFFER_SIZE) {
        return -1;  // 缓冲区已满
    }

    // 将数据写入接收缓冲区
    rx_buffer[rx_write_index] = data;

    // 更新写指针和计数器
    rx_write_index = (rx_write_index + 1) % RX_BUFFER_SIZE;  // 循环缓冲区
    rx_count++;  // 增加缓冲区中的数据计数

    return 0;  // 成功写入
}

// 从接收缓冲区读取数据
static int rx_buffer_read_byte(uint8_t *data)
{
    // 检查缓冲区是否为空
    if (rx_count == 0) {
        return -1;  // 缓冲区为空
    }

    return 0;  // 成功读取
}

//发送定时器中断函数
static void swuart_send_timer_callback(void *parameter)
{
    //发送数据

}

//接收IO中断函数: 使用边沿触发，来读取串口数据
static void swuart_recv_io_callback(void *parameter)
{
    static uint8_t current_byte = 0;  // 当前正在接收的字节
    static uint8_t bit_position = 0;  // 当前位的偏移
    static uint8_t start_bit_detected = 0;  // 起始位检测标志
    
    HAL_Delay_us(2);
    // 读取接收到的数据 (RX引脚状态)
    uint8_t bit_value = RX_IO();
    rt_kprintf("%d", bit_value);
}

//同步发送接口
static inline int swuart_async_send_byte(uint8_t *data)
{
    uint8_t byte = *data;
    uint8_t bit_mask = 0x01;
    uint8_t state;

    // 发送起始位 (低电平)
    TX_IO(0);
    HAL_Delay_us(g_half_bit_time * 2);  // 等待一个位时间
    
    // 逐位发送数据位 (8位)
    for (int i = 0; i < 8; i++) 
    {
        TX_IO(((byte & bit_mask) ? 1 : 0));
        bit_mask <<= 1;
        HAL_Delay_us(g_half_bit_time * 2);  // 等待一个位时间
    }

    // 发送停止位 (高电平)
    TX_IO(1);
    HAL_Delay_us(g_half_bit_time * 2 * END_LENGTH);  // 等待一个位时间

    return 0;  // 数据发送完成
}

static void timeout_ind(void* parameter)
{
    tx_time_flag = true;
}

// 初始化软件UART
int swuart_init(void)
{
    //tx
    GPIO_InitTypeDef GPIO_InitStruct;
    HAL_PIN_Set(PAD_PA01, GPIO_A1, PIN_PULLDOWN, 1);
    GPIO_InitStruct.Pin = 1;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);
    TX_IO(1);

    //rx
    HAL_PIN_Set(PAD_PA03, GPIO_A3, GPIO_PULLUP, 1);           
    GPIO_InitStruct.Pin = 3;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);	

    //计算半位时间 单位us
    g_half_bit_time = 1000000 / g_baud_rate / 2;
    
    //初始化发送和接收buffer
    rx_buffer = (uint8_t *)swuart_malloc(RX_BUFFER_SIZE);

    //初始化信号量
    swuart_sem = rt_sem_create("swuart_sem", true, RT_IPC_FLAG_FIFO);
    if (swuart_sem == RT_NULL)
    {
        rt_kprintf("create swuart_sem failed\n");
        return -1;
    }    

    return 0;
}
//INIT_DEVICE_EXPORT(swuart_init);

// 发送数据
int swuart_send(char data[], int len)
{
    //等待信号量
    rt_sem_take(swuart_sem, RT_WAITING_FOREVER);
//如果是同步
#if(ASYNC == 1)
    //用swuart_async_send_byte循环发送数据
    for (int i = 0; i < len; i++) 
    {
        swuart_async_send_byte((uint8_t*)&data[i]);
    }
//如果是异步
#else
    
#endif
    //释放信号量
    rt_sem_release(swuart_sem);
    return 0;
}

// 接收数据
int swuart_read(char *data, int *len)
{
    //等待信号量
    rt_sem_take(swuart_sem, RT_WAITING_FOREVER);
    
    //释放信号量
    rt_sem_release(swuart_sem);
    return 0;
}

//swprintf, 使用swuart_send封装
void swprintf(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    char buf[256];
    buf[0] = 'H';
    buf[1] = ':';
    vsnprintf(buf+2, sizeof(buf)-2, fmt, args);  // Format the string and store it in buf
    swuart_send(buf, (int32_t)strlen(buf));  // Send the formatted string through software UART
    va_end(args);
}
