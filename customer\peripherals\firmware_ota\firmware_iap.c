/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   firmware_iap.c
@Time    :   2025/03/31 17:41:20
*
**************************************************************************/
#include "firmware_iap.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "drv_flash.h"
#include "mem_map.h"
#include "ff.h"
#include "bf0_hal.h"

#ifdef ENV_OTA
#include "boot_event_task.h"
#include "periodic_task.h"
//#define USER_APP_LOAD_ADDR (FLASH_BASE_ADDR|0x80000)
#define FLASH_ERASE_SIZE   512UL
#else
//#define USER_APP_LOAD_ADDR (FLASH_BASE_ADDR|0x20000)
#define FLASH_ERASE_SIZE   512UL
#endif

#define BOOT_VERSION_ADDR  		(FLASH_BASE_ADDR + 0x80000UL - 4096UL)
#define MTD_DEV                      "sd0"
#define SDIO_LOGIC_ADDR              (0x68000000)
#define SD_PAGE_SIZE                 (512)


uint32_t update_process = 0;
//uint8_t firmware_buffer[FLASH_ERASE_SIZE] __attribute__ ((aligned (4)));

uint32_t firmware_update_check(const char *filename)
{
    uint32_t file_size = 0;
    FIL *fp = rt_malloc(sizeof(FIL));
    rt_memset(fp,0,sizeof(FIL));
    FRESULT f_ret = f_open(fp, filename, FA_READ);
    if (FR_OK == f_ret){
        rt_memcpy((void *)&file_size, &fp->obj.objsize, sizeof(uint32_t));
        f_close(fp);
    }
    rt_free(fp);

    return file_size;
}

bool boot_write_img(uint8_t *dest, uint8_t *src, uint32_t offset, uint32_t size)
{
    bool ret = true;
    static uint8_t first = 1;
    rt_off_t sec_addr = 0;
    rt_off_t addr = (rt_off_t)dest - SDIO_LOGIC_ADDR;
    sec_addr = addr / SD_PAGE_SIZE + offset - RT_MMCSD_USER_OFFSET - 63;

    rt_device_t dev = rt_device_find(MTD_DEV);
    if (dev) {
        if (first) {
            first = 0;
            rt_err_t res = rt_device_open(dev, RT_DEVICE_FLAG_RDWR);
            if (res == RT_EOK || res == -RT_EBUSY) {
                rt_kprintf("open sd0 ok\n");
            }
        }
        rt_device_write(dev, sec_addr, src, size / SD_PAGE_SIZE);
    } else {
        rt_kprintf("no found sd0\n");
        ret = false;
    }
    //SCB_CleanDCache();
    //SCB_InvalidateDCache();

    return ret;
}

bool firmware_program(const char *filename, uint32_t *addr)
{
    bool ret = true;
    FIL *fp = rt_malloc(sizeof(FIL));
    rt_memset(fp, 0, sizeof(FIL));
    unsigned int br = 0;
    uint32_t block_cnt = 0, file_size = 0, process = 0;
    uint32_t temp_process = 0;
    const uint32_t bin_buff_size = 64 * 1024;
    uint8_t *bin_buff = rt_malloc_align(bin_buff_size, 32); // Allocate 64KB buffer, align with 32 bytes
    if (!bin_buff) {
        rt_kprintf("Memory allocation failed\n");
        rt_free(fp);
        return false;
    }
    FRESULT f_ret = FR_OK;
    f_ret = f_open(fp, filename, FA_READ);
    if (FR_OK != f_ret) {
        ret = false;
        rt_free(fp);
        rt_free_align(bin_buff);
        return ret;
    } else {
#ifdef ENV_OTA
        file_size = get_hcpu_update_file_size() + get_lcpu_update_file_size();
        if (update_process == 0) {
            send_event_to_boot_draw_task(EVENT_BOOT_OTA_START);
        } else {
            temp_process = update_process;
        }
#else
        file_size = fp->obj.objsize;
#endif
        while (1) {
            if (FR_OK == f_read(fp, bin_buff, bin_buff_size, &br)) {
                if (0 == br) {
                    break;
                } else {
                    ret = boot_write_img((uint8_t *)addr, bin_buff, block_cnt, bin_buff_size);
                    if (ret) {
                        block_cnt += (br / SD_PAGE_SIZE);
                        rt_memset(bin_buff, 0x00, bin_buff_size);
                    }
                    process = (block_cnt * SD_PAGE_SIZE) * 100 / file_size;
                    if (update_process != process && (process - update_process >= 5)) {
                        if (temp_process == 0) {
                            update_process = process;
#ifdef ENV_OTA
                            send_event_to_boot_draw_task(EVENT_BOOT_OTA_UPDATE);
#endif
                        } else {
                            if (process > temp_process && (process - temp_process >= 5)) {
                                update_process = process;
#ifdef ENV_OTA
                                send_event_to_boot_draw_task(EVENT_BOOT_OTA_UPDATE);
#endif
                            }
                        }
                    }
                }
            } else {
                rt_kprintf("f_read error\n");
                ret = false;
                break;
            }
        }
    }
    f_close(fp);
    rt_free(fp);
    rt_free_align(bin_buff);
    if (false == ret) {
#ifdef ENV_OTA
        send_event_to_boot_draw_task(EVENT_BOOT_OTA_FAILED);
#endif
    } else {
        f_unlink(filename);
#ifdef ENV_OTA
        if (update_process == 100) {
            send_event_to_boot_draw_task(EVENT_BOOT_OTA_END);
        }
#endif
    }
    return ret;
}

uint32_t get_firmware_update_precent(void)
{
    return update_process;
}

uint32_t boot_version_read(void)
{
	return *(uint32_t*)BOOT_VERSION_ADDR;
}

void flash_uuid_get(uint32_t *hv,uint32_t *lv)
{
    uint8_t uuid[16];
    rt_flash_get_uid(0x10000000,uuid,16);
    *hv = ((uuid[0] + uuid[1])<<24) + ((uuid[2] + uuid[3])<<16) + ((uuid[4] + uuid[5])<<8) + ((uuid[6] + uuid[7]));
    *lv = ((uuid[8] + uuid[9])<<24) + ((uuid[10] + uuid[11])<<16) + ((uuid[12] + uuid[13])<<8) + ((uuid[14] + uuid[15]));
}

//#ifdef ENV_BOOT
#if 0

#include "user_interface.h"

#define THUMB_BIT         (0u)
#define APP_START_ADDR    (USER_APP_LOAD_ADDR)
#define APP_STACK_PTR     (*(volatile uint32_t*)(APP_START_ADDR + 0x00))
#define APP_RESET_PTR     (*(volatile uint32_t*)(APP_START_ADDR + 0x04))

void (*AppPtr)(void);
void firmware_iap_jump_reboot(void)
{
    for (uint32_t i = 0; i < 16; i++)
    {
        NVIC->ICER[i] = 0xFFFFFFFF;
        NVIC->ICPR[i] = 0xFFFFFFFF;
        __DSB();
        __ISB();
    }
    
    __disable_irq();
    AppPtr = (void (*)(void))(APP_RESET_PTR | THUMB_BIT);
    SCB->VTOR = APP_START_ADDR;
    __set_MSP(APP_STACK_PTR);  // Set main stack pointer to application initial stack value
    __set_CONTROL(0);          // Use MSP and Privileged in thread mode
    __DSB();
    __ISB();
    AppPtr();                  // Start the application, we will not return from this function
    while(1){
        
    }
}

bool app_firmware_is_valid(void)
{
    return ((APP_STACK_PTR & 0xF0000000) == 0x20000000);
}

void boot_make_app_invalid(void)
{
    rt_flash_erase(USER_APP_LOAD_ADDR, FLASH_ERASE_SIZE);
}

void boot_version_init(void)
{
	uint32_t version = *(uint32_t*)BOOT_VERSION_ADDR;
	if(version == 0xffffffff || version != BOOT_VERSION){
        if(RT_EOK == rt_flash_erase(BOOT_VERSION_ADDR, FLASH_ERASE_SIZE)){
            version = BOOT_VERSION;
            rt_flash_write(BOOT_VERSION_ADDR,(const uint8_t *)&version,sizeof(uint32_t));
        }
	}
}

#define TARGET_IMAGE_NAME        "0:/iGS800_APP.bin"
bool create_raw_firmware(void)
{
    int result = 0;
    rt_kprintf("decompress_firmware\n");
    f_unlink(TARGET_IMAGE_NAME);
    result = app_bdiff_bin_decompress(USER_APP_LOAD_ADDR, (const uint8_t *)TARGET_IMAGE_DIFF_NAME, (const uint8_t *)TARGET_IMAGE_NAME);
    f_unlink(TARGET_IMAGE_DIFF_NAME);
    if(!result)
    {
        rt_kprintf("decompress_firmware success\n");
    }
    return (result == 0);
}

//#define IAP_JUMP_TEST   1
#if IAP_JUMP_TEST
static rt_err_t reboot_test(int argc, char **argv)
{
    firmware_iap_jump_reboot();
    return 0;
}
MSH_CMD_EXPORT(reboot_test, reboot test);
#endif

#endif
