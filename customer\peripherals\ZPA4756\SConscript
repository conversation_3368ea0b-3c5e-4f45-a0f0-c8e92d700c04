# -*- coding: utf-8 -*-
import os
from building import *

# 获取当前工作目录
cwd = GetCurrentDir()

# 初始化源文件列表、头文件路径列表和库文件列表
src = []
CPPPATH = [cwd]
THPATH = " "
LIBS = []

# 获取默认的SCons环境
env = DefaultEnvironment()

# 遍历当前目录及其所有子目录
for root, dirs, files in os.walk(cwd):
    for file in files:
        # 如果文件是以.c结尾的，添加到源文件列表
        if file.endswith('.c'):
            src.append(os.path.join(root, file))
        # 如果文件是以.h结尾的，添加到头文件路径列表
        elif file.endswith('.h'):
            # 如果头文件路径不是当前目录，添加到头文件路径列表
            if root != THPATH:
                CPPPATH.append(root)
                THPATH = root
        # 如果文件是以.a或.lib结尾的，添加到库文件列表
        elif file.endswith('.a'):
            src.append(os.path.join(root, file))
        # 如果文件是以.lib结尾的，添加到库文件列表
        elif file.endswith('.lib'):
            src.append(os.path.join(root, file))

# 定义编译组
group = DefineGroup('Drivers', src, depend = ['SENSOR_USING_ZPA4756'], CPPPATH = CPPPATH)

# 返回编译组
Return('group')

