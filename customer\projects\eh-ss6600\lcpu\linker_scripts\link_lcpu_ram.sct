#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc
#include "../../../../../drivers/cmsis/sf32lb55x/mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE {    ; load region size_region
  ER_IROM1 LCPU_RAM_CODE_START_ADDR LCPU_RAM_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  RW_IRAM1 AlignExpr(LPSYS_RAM_BASE+ImageLength(ER_IROM1), 16) ALIGN 16 NOCOMPRESS  {  ; RW data
   .ANY (+RW +ZI)
  }
  ; Load Address must be equal to Exec Address
  ScatterAssert((LoadBase(RW_IRAM1) OR 0x20000000) == ImageBase(RW_IRAM1))
  ScatterAssert((ImageLength(ER_IROM1)+ImageLength(RW_IRAM1)+LCPU_MBOX_SIZE)<LPSYS_RAM_SIZE)
}

