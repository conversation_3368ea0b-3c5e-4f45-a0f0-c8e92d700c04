#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m0plus -xc
#include "../../../../../drivers/cmsis/sf32lb55x/mem_map.h"



; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 LCPU_PATCH_START_ADDR LCPU_PATCH_TOTAL_SIZE-LCPU_PATCH_RECORD_SIZE  {    ; load region size_region
  ER_IROM1 LCPU_PATCH_START_ADDR LCPU_PATCH_TOTAL_SIZE-LCPU_PATCH_RECORD_SIZE  {  ; load address = execution address
   *.o (PATCH_ENTRY, +First)
   .ANY (+RO)
  }
  RW_IRAM1 0x20000000+LCPU_PATCH_START_ADDR+ImageLength(ER_IROM1)  {  ; RW data
   .ANY (+RW +ZI)
  }
}

