/**************************************************************************//**
 * @file     startup_ARMCM33.S
 * @brief    CMSIS Core Device Startup File for
 *           ARMCM33 Device
 * @version  V5.3.1
 * @date     09. July 2018
 ******************************************************************************/
/*
 * Copyright (c) 2009-2018 Arm Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
;//-------- <<< Use Configuration Wizard in Context Menu >>> ------------------
*/

                .syntax  unified
                .arch    armv8-m.main

                .eabi_attribute Tag_ABI_align_preserved, 1

  				.thumb

				.global  __Vectors
				.global  Default_Handler

				/* start address for the initialization values of the .data section. defined in linker script */
				.word  _sidata
				/* start address for the .data section. defined in linker script */  
				.word  _sdata
				/* end address for the .data section. defined in linker script */
				.word  _edata
				/* start address for the .bss section. defined in linker script */
				.word  _sbss
				/* end address for the .bss section. defined in linker script */
				.word  _ebss
				/*stack used for SystemInit_ExtMemCtl; always internal RAM used */

				/**
				 * @brief  This is the code that gets called when the processor first
				 *          starts execution following a reset event. Only the absolutely
				 *          necessary set is performed, after which the application
				 *          supplied main() routine is called. 
				 * @param  None
				 * @retval : None
				*/

    			.section  .text.Reset_Handler
  				.weak  Reset_Handler
  				.type  Reset_Handler, %function
Reset_Handler:  
  				ldr   sp, =_estack     /* set stack pointer */
                                ldr   r0, =_sstack
				msr   msplim,r0
				/* Copy the data segment initializers from flash to SRAM */  
				movs  r1, #0
				b  LoopCopyDataInit

CopyDataInit:
				ldr  r3, =_sidata
				ldr  r3, [r3, r1]
				str  r3, [r0, r1]
				adds  r1, r1, #4
				    
LoopCopyDataInit:
				ldr  r0, =_sdata
				ldr  r3, =_edata
				adds  r2, r0, r1
				cmp  r2, r3
				bcc  CopyDataInit
				ldr  r2, =_sbss
				b  LoopFillZerobss
				/* Zero fill the bss segment. */  
FillZerobss:
				movs  r3, #0
				str  r3, [r2], #4
				    
LoopFillZerobss:
		  		ldr  r3, = _ebss
				cmp  r2, r3
				bcc  FillZerobss

				/* Call the clock system intitialization function.*/
  				bl  SystemInit   
				/* Call static constructors */
				/*     bl __libc_init_array */
				/* Call the applications main point.*/
  				bl  main
  				bx  lr    
				.size  Reset_Handler, .-Reset_Handler


/**
 * @brief  This is the code that gets called when the processor receives an 
 *         unexpected interrupt.  This simply enters an infinite loop, preserving
 *         the system state for examination by a debugger.
 * @param  None     
 * @retval None       
*/
    			.section  .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  				b  Infinite_Loop
  				.size  Default_Handler, .-Default_Handler
  				
/******************************************************************************
*
* The minimal vector table for a Cortex M3. Note that the proper constructs
* must be placed on this to ensure that it ends up at physical address
* 0x0000.0000.
* 
*******************************************************************************/
   				.section  .isr_vector,"a",%progbits
  				.type  __Vectors, %object
  				.size  __Vectors, .-__Vectors 
  
__Vectors:
                .long    _estack                       /*     Top of Stack */
                .long    Reset_Handler                      /*     Reset Handler */
                .long    NMI_Handler                        /* -14 NMI Handler */
                .long    HardFault_Handler                  /* -13 Hard Fault Handler */
                .long    MemManage_Handler                  /* -12 MPU Fault Handler */
                .long    BusFault_Handler                   /* -11 Bus Fault Handler */
                .long    UsageFault_Handler                 /* -10 Usage Fault Handler */
                .long    SecureFault_Handler                /*  -9 Secure Fault Handler */
                .long    0                                  /*     Reserved */
                .long    0                                  /*     Reserved */
                .long    0                                  /*     Reserved */
                .long    SVC_Handler                        /*  -5 SVCall Handler */
                .long    DebugMon_Handler                   /*  -4 Debug Monitor Handler */
                .long    0                                  /*     Reserved */
                .long    PendSV_Handler                     /*  -2 PendSV Handler */
                .long    SysTick_Handler                    /*  -1 SysTick Handler */

                /* Interrupts */
                .long    RCC_IRQHandler                 /*   0 Interrupt 0 */
                .long    USART1_IRQHandler                 /*   1 Interrupt 1 */
                .long    USART2_IRQHandler                 /*   0 Interrupt 0 */
                .long    USART3_IRQHandler                 /*   1 Interrupt 1 */
                .long    USART4_IRQHandler                 /*   0 Interrupt 0 */
                .long    SPI1_IRQHandler                 /*   1 Interrupt 1 */
                .long    SPI2_IRQHandler                 /*   0 Interrupt 0 */
                .long    SPI3_IRQHandler                 /*   1 Interrupt 1 */
                .long    I2C1_IRQHandler                 /*   0 Interrupt 0 */
                .long    I2C2_IRQHandler                 /*   1 Interrupt 1 */
                .long    I2C3_IRQHandler                 /*   0 Interrupt 0 */
                .long    TIM2_IRQHandler                 /*   1 Interrupt 1 */
                .long    TIM3_IRQHandler                 /*   0 Interrupt 0 */
                .long    TIM4_IRQHandler                 /*   1 Interrupt 1 */
                .long    GPIO_IRQHandler                 /*   0 Interrupt 0 */
                .long    DMAC_IRQHandler                 /*   1 Interrupt 1 */
                .long    GPADC1_IRQHandler                 /*   0 Interrupt 0 */
                .long    GPADC2_IRQHandler                 /*   1 Interrupt 1 */
                .long    FLASHC_IRQHandler                 /*   0 Interrupt 0 */
                .long    PSRAMC_IRQHandler                 /*   1 Interrupt 1 */
                .long    TRNG_IRQHandler                 /*   2 Interrupt 2 */
                .long    AES_IRQHandler                 /*   3 Interrupt 3 */
                .long    CRC_IRQHandler                 /*   4 Interrupt 4 */
                .long    TIM1_IRQHandler                 /*   5 Interrupt 5 */
                .long    WDT1_IRQHandler                 /*   6 Interrupt 6 */
                .long    I2S_IRQHandler                 /*   7 Interrupt 7 */
                .long    EPIC_IRQHandler                 /*   8 Interrupt 8 */
                .long    KEYPAD_IRQHandler                 /*   9 Interrupt 9 */
                .long    USBC_IRQHandler                 /*   5 Interrupt 5 */
                .long    GPDAC_IRQHandler                 /*   5 Interrupt 5 */
                .long    LPTIM_IRQHandler                 /*   6 Interrupt 6 */
                .long    DMAC2_IRQHandler                 /*   7 Interrupt 7 */
                .long    BLE2MCU_IRQHandler                 /*   8 Interrupt 8 */
                .long    RTC_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt34_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt35_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt36_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt37_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt38_IRQHandler                 /*   9 Interrupt 9 */
                .long    Interrupt39_IRQHandler                 /*   9 Interrupt 9 */

                .space   (440 * 4)                          /* Interrupts 10 .. 480 are left out */

/* Macro to define default exception/interrupt handlers.
 * Default handler are weak symbols with an endless loop.
 * They can be overwritten by real handlers.
 */
                .macro   Set_Default_Handler  Handler_Name
                .weak    \Handler_Name
                .set     \Handler_Name, Default_Handler
                .endm


/* Default exception/interrupt handler */

                Set_Default_Handler  NMI_Handler
                Set_Default_Handler  HardFault_Handler
                Set_Default_Handler  MemManage_Handler
                Set_Default_Handler  BusFault_Handler
                Set_Default_Handler  UsageFault_Handler
                Set_Default_Handler  SecureFault_Handler
                Set_Default_Handler  SVC_Handler
                Set_Default_Handler  DebugMon_Handler
                Set_Default_Handler  PendSV_Handler
                Set_Default_Handler  SysTick_Handler

                Set_Default_Handler  RCC_IRQHandler
                Set_Default_Handler  USART1_IRQHandler
                Set_Default_Handler  USART2_IRQHandler
                Set_Default_Handler  USART3_IRQHandler
                Set_Default_Handler  USART4_IRQHandler
                Set_Default_Handler  SPI1_IRQHandler
                Set_Default_Handler  SPI2_IRQHandler
                Set_Default_Handler  SPI3_IRQHandler
                Set_Default_Handler  I2C1_IRQHandler
                Set_Default_Handler  I2C2_IRQHandler
                Set_Default_Handler  I2C3_IRQHandler
                Set_Default_Handler  TIM2_IRQHandler
                Set_Default_Handler  TIM3_IRQHandler
                Set_Default_Handler  TIM4_IRQHandler
                Set_Default_Handler  GPIO_IRQHandler
                Set_Default_Handler  DMAC_IRQHandler
                Set_Default_Handler  GPADC1_IRQHandler
                Set_Default_Handler  GPADC2_IRQHandler
                Set_Default_Handler  FLASHC_IRQHandler
                Set_Default_Handler  PSRAMC_IRQHandler
                Set_Default_Handler  TRNG_IRQHandler
                Set_Default_Handler  AES_IRQHandler
                Set_Default_Handler  CRC_IRQHandler
                Set_Default_Handler  TIM1_IRQHandler
                Set_Default_Handler  WDT1_IRQHandler
                Set_Default_Handler  I2S_IRQHandler
                Set_Default_Handler  EPIC_IRQHandler
                Set_Default_Handler  KEYPAD_IRQHandler
                Set_Default_Handler  USBC_IRQHandler
                Set_Default_Handler  GPDAC_IRQHandler
                Set_Default_Handler  LPTIM_IRQHandler
                Set_Default_Handler  DMAC2_IRQHandler
                Set_Default_Handler  BLE2MCU_IRQHandler
                Set_Default_Handler  RTC_IRQHandler
                Set_Default_Handler  Interrupt34_IRQHandler
                Set_Default_Handler  Interrupt35_IRQHandler
                Set_Default_Handler  Interrupt36_IRQHandler
                Set_Default_Handler  Interrupt37_IRQHandler
                Set_Default_Handler  Interrupt38_IRQHandler
                Set_Default_Handler  Interrupt39_IRQHandler


/* User setup Stack & Heap */

                .global  __stack_limit
                .global  __initial_sp
                .if      Heap_Size != 0                     /* Heap is provided */
                .global  __heap_base
                .global  __heap_limit
                .endif

                .end
