/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_DA_CMD_H__
#define __HDL_DA_CMD_H__

#include "hdl_channel.h"
#include "hdl_ports/hdl_flash_api.h"

#ifdef __cplusplus
extern "C" {
#endif

// DA Return
#define DA_S_DONE               0x00

#define DA_SYNC_CHAR            0xC0
#define DA_CONT_CHAR            0x69
#define DA_STOP_CHAR            0x96
#define DA_ACK                  0x5A
#define DA_NACK                 0xA5
#define DA_UNKNOWN_CMD          0xBB
#define DA_FLUSH_CONT           0xE1
#define DA_FLUSH_DONE           0xE2

#define DA_S_IN_PROGRESS                  3021
#define DA_S_UART_GET_DATA_TIMEOUT        3061
#define DA_S_UART_GET_CHKSUM_LSB_TIMEOUT  3062
#define DA_S_UART_GET_CHKSUM_MSB_TIMEOUT  3063
#define DA_S_UART_DATA_CKSUM_ERROR        3064
#define DA_S_UART_RX_BUF_FULL             3065

// DA CMD
#define DA_FORMAT_CMD           0xD4
#define DA_NOR_WRITE_DATA       0xB2
#define DA_READ_CMD             0xD6
#define DA_FINISH_CMD           0xD9
#define DA_ENABLE_WATCHDOG_CMD  0xDB

#define DA_SEND_PACKET_LEN      (4 * 1024)
#define DA_RECV_PACKET_LEN      (4 * 1024)


bool hdl_sync_with_da(bool enable_log, hdl_da_report_t *da_report);
bool hdl_da_format(const hdl_format_arg_t *format_arg);
bool hdl_da_readback(const hdl_readback_arg_t *readback_arg);
bool hdl_da_enable_wdt(uint16_t wdt_sec);
bool hdl_da_finish(bool power_off);
bool hdl_da_download(hdl_image_t *image, hdl_download_cb download_cb, void *download_cb_arg);
void hdl_data_buf_init(void);
void hdl_data_buf_clear(void);

#ifdef __cplusplus
}
#endif

#endif //__HDL_DA_CMD_H__

