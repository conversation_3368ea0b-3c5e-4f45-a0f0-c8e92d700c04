/**************************************************************************//**
 * @file     startup_ARMCM33.S
 * @brief    CMSIS Core Device Startup File for
 *           ARMCM33 Device
 * @version  V5.3.1
 * @date     09. July 2018
 ******************************************************************************/
/*
 * Copyright (c) 2009-2018 Arm Limited. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
;//-------- <<< Use Configuration Wizard in Context Menu >>> ------------------
*/

                .syntax  unified
                .arch    armv8-m.main


/*
;<h> Stack Configuration
;  <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
;</h>
*/
                .equ     Stack_Size, 0x00000400

                .section .stack
                .align   3
	            .globl   __StackTop
	            .globl   __StackLimit
__StackLimit:
                .space   Stack_Size
                .size    __StackLimit, . - __StackLimit
__StackTop:
                .size    __StackTop, . - __StackTop


/*
;<h> Heap Configuration
;  <o> Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
;</h>
*/
                .equ     Heap_Size, 0x00000C00

                .if      Heap_Size != 0                     /* Heap is provided */
                .section .heap
                .align   3
	            .globl   __HeapBase
	            .globl   __HeapLimit
__HeapBase:
                .space   Heap_Size
                .size    __HeapBase, . - __HeapBase
__HeapLimit:
                .size    __HeapLimit, . - __HeapLimit
                .endif


                .section .vectors
                .align   2
                .globl   __Vectors
                .globl   __Vectors_End
                .globl   __Vectors_Size
__Vectors:
                .long    __StackTop                         /*     Top of Stack */
                .long    Reset_Handler                      /*     Reset Handler */
                .long    NMI_Handler                        /* -14 NMI Handler */
                .long    HardFault_Handler                  /* -13 Hard Fault Handler */
                .long    MemManage_Handler                  /* -12 MPU Fault Handler */
                .long    BusFault_Handler                   /* -11 Bus Fault Handler */
                .long    UsageFault_Handler                 /* -10 Usage Fault Handler */
                .long    SecureFault_Handler                /*  -9 Secure Fault Handler */
                .long    0                                  /*     Reserved */
                .long    0                                  /*     Reserved */
                .long    0                                  /*     Reserved */
                .long    SVC_Handler                        /*  -5 SVCall Handler */
                .long    DebugMon_Handler                   /*  -4 Debug Monitor Handler */
                .long    0                                  /*     Reserved */
                .long    PendSV_Handler                     /*  -2 PendSV Handler */
                .long    SysTick_Handler                    /*  -1 SysTick Handler */

                /* Interrupts */
                .long      AON_IRQHandler                          /*  0 Interrupt 0 */
                .long      USART1_IRQHandler                       /*  1 Interrupt 1 */
                .long      USART2_IRQHandler                       /*  2 Interrupt 2 */
                .long      USART3_IRQHandler                       /*  3 Interrupt 3 */ 
                .long      SPI1_IRQHandler                         /*  4 Interrupt 4 */
                .long      SPI2_IRQHandler                         /*  5 Interrupt 5 */
                .long      SPI3_IRQHandler                         /*  6 Interrupt 6 */
                .long      I2C1_IRQHandler                         /*  7 Interrupt 7 */
                .long      I2C2_IRQHandler                         /*  8 Interrupt 8 */
                .long      I2C3_IRQHandler                         /*  9 Interrupt 9 */
                .long      I2C4_IRQHandler                         /* 10 Interrupt 10*/
                .long      GPTIM1_IRQHandler                       /* 11 Interrupt 11*/
                .long      GPTIM2_IRQHandler                       /* 12 Interrupt 12*/
                .long      GPTIM3_IRQHandler                       /* 13 Interrupt 13*/
                .long      GPIO2_IRQHandler                        /* 14 Interrupt 14*/
                .long      TSEN_IRQHandler                         /* 15 Interrupt 15*/
                .long      DMAC2_CH1_IRQHandler                    /* 16 Interrupt 16*/
                .long      DMAC2_CH2_IRQHandler                    /* 17 Interrupt 17*/
                .long      DMAC2_CH3_IRQHandler                    /* 18 Interrupt 18*/
                .long      DMAC2_CH4_IRQHandler                    /* 19 Interrupt 19*/
                .long      DMAC2_CH5_IRQHandler                    /* 20 Interrupt 20*/
                .long      DMAC2_CH6_IRQHandler                    /* 21 Interrupt 21*/
                .long      DMAC2_CH7_IRQHandler                    /* 22 Interrupt 22*/
                .long      DMAC2_CH8_IRQHandler                    /* 23 Interrupt 23*/
                .long      GPADC1_IRQHandler                       /* 24 Interrupt 24*/
                .long      GPADC2_IRQHandler                       /* 25 Interrupt 25*/
                .long      LPCOMP1_IRQHandler                      /* 26 Interrupt 26*/
                .long      LPCOMP2_IRQHandler                      /* 27 Interrupt 27*/
                .long      BTIM1_IRQHandler                        /* 28 Interrupt 28*/
                .long      BTIM2_IRQHandler                        /* 29 Interrupt 29*/
                .long      WDT2_IRQHandler                         /* 30 Interrupt 30*/
                .long      HCPU2LCPU_IRQHandler                    /* 31 Interrupt 31*/
                .long      BCPU2LCPU_IRQHandler                    /* 32 Interrupt 32*/
                .long      LPTIM2_IRQHandler                       /* 33 Interrupt 33*/
                .long      LPTIM3_IRQHandler                       /* 34 Interrupt 34*/
                .long      LPUART2_IRQHandler                      /* 35 Interrupt 35*/
                .long      Interrupt36_IRQHandler                  /* 36 Interrupt 36*/
                .long      Interrupt37_IRQHandler                  /* 37 Interrupt 37*/
                .long      Interrupt38_IRQHandler                  /* 38 Interrupt 38*/
                .long      RTC_IRQHandler                          /* 39 Interrupt 39*/

                .space   (440 * 4)                          /* Interrupts 80 .. 479 are left out */
__Vectors_End:
                .equ     __Vectors_Size, __Vectors_End - __Vectors
                .size    __Vectors, . - __Vectors


                .thumb
                .section .text
                .align   2

                .thumb_func
                .type    Reset_Handler, %function
                .globl   Reset_Handler
                .fnstart
Reset_Handler:
/* Firstly it copies data from read only memory to RAM.
 * There are two schemes to copy. One can copy more than one sections.
 * Another can copy only one section. The former scheme needs more
 * instructions and read-only data to implement than the latter.
 * Macro __STARTUP_COPY_MULTIPLE is used to choose between two schemes.
 */

#ifdef __STARTUP_COPY_MULTIPLE
/* Multiple sections scheme.
 *
 * Between symbol address __copy_table_start__ and __copy_table_end__,
 * there are array of triplets, each of which specify:
 *   offset 0: LMA of start of a section to copy from
 *   offset 4: VMA of start of a section to copy to
 *   offset 8: size of the section to copy. Must be multiply of 4
 *
 * All addresses must be aligned to 4 bytes boundary.
 */
                ldr      r4, =__copy_table_start__
                ldr      r5, =__copy_table_end__

.L_loop0:
                cmp      r4, r5
                bge      .L_loop0_done
                ldr      r1, [r4]
                ldr      r2, [r4, #4]
                ldr      r3, [r4, #8]

.L_loop0_0:
                subs     r3, #4
                ittt     ge
                ldrge    r0, [r1, r3]
                strge    r0, [r2, r3]
                bge      .L_loop0_0

                adds     r4, #12
                b        .L_loop0

.L_loop0_done:
#else
/* Single section scheme.
 *
 * The ranges of copy from/to are specified by following symbols
 *   __etext: LMA of start of the section to copy from. Usually end of text
 *   __data_start__: VMA of start of the section to copy to
 *   __data_end__: VMA of end of the section to copy to
 *
 * All addresses must be aligned to 4 bytes boundary.
 */
                ldr      r1, =__etext
                ldr      r2, =__data_start__
                ldr      r3, =__data_end__

.L_loop1:
                cmp      r2, r3
                ittt     lt
                ldrlt    r0, [r1], #4
                strlt    r0, [r2], #4
                blt      .L_loop1
#endif /*__STARTUP_COPY_MULTIPLE */

/* This part of work usually is done in C library startup code.
 * Otherwise, define this macro to enable it in this startup.
 *
 * There are two schemes too.
 * One can clear multiple BSS sections. Another can only clear one section.
 * The former is more size expensive than the latter.
 *
 * Define macro __STARTUP_CLEAR_BSS_MULTIPLE to choose the former.
 * Otherwise define macro __STARTUP_CLEAR_BSS to choose the later.
 */
#ifdef __STARTUP_CLEAR_BSS_MULTIPLE
/* Multiple sections scheme.
 *
 * Between symbol address __copy_table_start__ and __copy_table_end__,
 * there are array of tuples specifying:
 *   offset 0: Start of a BSS section
 *   offset 4: Size of this BSS section. Must be multiply of 4
 */
                ldr      r3, =__zero_table_start__
                ldr      r4, =__zero_table_end__

.L_loop2:
                cmp      r3, r4
                bge      .L_loop2_done
                ldr      r1, [r3]
                ldr      r2, [r3, #4]
                movs     r0, 0

.L_loop2_0:
                subs     r2, #4
                itt      ge
                strge    r0, [r1, r2]
                bge      .L_loop2_0

                adds     r3, #8
                b        .L_loop2
.L_loop2_done:
#elif defined (__STARTUP_CLEAR_BSS)
/* Single BSS section scheme.
 *
 * The BSS section is specified by following symbols
 *   __bss_start__: start of the BSS section.
 *   __bss_end__: end of the BSS section.
 *
 * Both addresses must be aligned to 4 bytes boundary.
 */
                ldr      r1, =__bss_start__
                ldr      r2, =__bss_end__

                movs     r0, 0
.L_loop3:
                cmp      r1, r2
                itt      lt
                strlt    r0, [r1], #4
                blt      .L_loop3
#endif /* __STARTUP_CLEAR_BSS_MULTIPLE || __STARTUP_CLEAR_BSS */

                ldr      r0, =__StackLimit
                msr      msplim, r0

                bl       SystemInit
                bl       _start

                .fnend
                .size    Reset_Handler, . - Reset_Handler


                .thumb_func
                .type    Default_Handler, %function
                .weak    Default_Handler
                .fnstart
Default_Handler:
                b        .
                .fnend
                .size    Default_Handler, . - Default_Handler

/* Macro to define default exception/interrupt handlers.
 * Default handler are weak symbols with an endless loop.
 * They can be overwritten by real handlers.
 */
                .macro   Set_Default_Handler  Handler_Name
                .weak    \Handler_Name
                .set     \Handler_Name, Default_Handler
                .endm


/* Default exception/interrupt handler */

                Set_Default_Handler  NMI_Handler
                Set_Default_Handler  HardFault_Handler
                Set_Default_Handler  MemManage_Handler
                Set_Default_Handler  BusFault_Handler
                Set_Default_Handler  UsageFault_Handler
                Set_Default_Handler  SecureFault_Handler
                Set_Default_Handler  SVC_Handler
                Set_Default_Handler  DebugMon_Handler
                Set_Default_Handler  PendSV_Handler
                Set_Default_Handler  SysTick_Handler

                Set_Default_Handler  AON_IRQHandler
                Set_Default_Handler  USART1_IRQHandler
                Set_Default_Handler  USART2_IRQHandler
                Set_Default_Handler  USART3_IRQHandler
                Set_Default_Handler  SPI1_IRQHandler
                Set_Default_Handler  SPI2_IRQHandler
                Set_Default_Handler  SPI3_IRQHandler
                Set_Default_Handler  I2C1_IRQHandler
                Set_Default_Handler  I2C2_IRQHandler
                Set_Default_Handler  I2C3_IRQHandler
                Set_Default_Handler  I2C4_IRQHandler
                Set_Default_Handler  GPTIM1_IRQHandler
                Set_Default_Handler  GPTIM2_IRQHandler
                Set_Default_Handler  GPTIM3_IRQHandler
                Set_Default_Handler  GPIO2_IRQHandler
                Set_Default_Handler  TSEN_IRQHandler
                Set_Default_Handler  DMAC2_CH1_IRQHandler
                Set_Default_Handler  DMAC2_CH2_IRQHandler
                Set_Default_Handler  DMAC2_CH3_IRQHandler
                Set_Default_Handler  DMAC2_CH4_IRQHandler
                Set_Default_Handler  DMAC2_CH5_IRQHandler
                Set_Default_Handler  DMAC2_CH6_IRQHandler
                Set_Default_Handler  DMAC2_CH7_IRQHandler
                Set_Default_Handler  DMAC2_CH8_IRQHandler
                Set_Default_Handler  GPADC1_IRQHandler
                Set_Default_Handler  GPADC2_IRQHandler
                Set_Default_Handler  LPCOMP1_IRQHandler
                Set_Default_Handler  LPCOMP2_IRQHandler
                Set_Default_Handler  BTIM1_IRQHandler
                Set_Default_Handler  BTIM2_IRQHandler
                Set_Default_Handler  WDT2_IRQHandler
                Set_Default_Handler  HCPU2LCPU_IRQHandler
                Set_Default_Handler  BCPU2LCPU_IRQHandler
                Set_Default_Handler  LPTIM2_IRQHandler
                Set_Default_Handler  LPTIM3_IRQHandler
                Set_Default_Handler  LPUART2_IRQHandler
                Set_Default_Handler  Interrupt36_IRQHandler		                
                Set_Default_Handler  Interrupt37_IRQHandler		                
                Set_Default_Handler  Interrupt38_IRQHandler		                
                Set_Default_Handler  RTC_IRQHandler		


                .end
