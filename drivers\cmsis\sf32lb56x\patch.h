#ifndef __PATCH_H
#define __PATCH_H

typedef struct
{
    __IO uint32_t CH0;
    __IO uint32_t CH1;
    __IO uint32_t CH2;
    __IO uint32_t CH3;
    __IO uint32_t CH4;
    __IO uint32_t CH5;
    __IO uint32_t CH6;
    __IO uint32_t CH7;
    __IO uint32_t CH8;
    __IO uint32_t CH9;
    __IO uint32_t CH10;
    __IO uint32_t CH11;
    __IO uint32_t CH12;
    __IO uint32_t CH13;
    __IO uint32_t CH14;
    __IO uint32_t CH15;
    __IO uint32_t CH16;
    __IO uint32_t CH17;
    __IO uint32_t CH18;
    __IO uint32_t CH19;
    __IO uint32_t CH20;
    __IO uint32_t CH21;
    __IO uint32_t CH22;
    __IO uint32_t CH23;
    __IO uint32_t CH24;
    __IO uint32_t CH25;
    __IO uint32_t CH26;
    __IO uint32_t CH27;
    __IO uint32_t CH28;
    __IO uint32_t CH29;
    __IO uint32_t CH30;
    __IO uint32_t CH31;
    __IO uint32_t CER;
    __IO uint32_t CSR;
    __IO uint32_t CDR;
    __IO uint32_t RSVD1;
    __IO uint32_t IER;
    __IO uint32_t ISR;
    __IO uint32_t ICR;
    __IO uint32_t VER;
} PATCH_TypeDef;


/******************* Bit definition for PATCH_CH0 register ********************/
#define PATCH_CH0_ADDR_Pos              (2U)
#define PATCH_CH0_ADDR_Msk              (0x1FFFFUL << PATCH_CH0_ADDR_Pos)
#define PATCH_CH0_ADDR                  PATCH_CH0_ADDR_Msk

/******************* Bit definition for PATCH_CH1 register ********************/
#define PATCH_CH1_ADDR_Pos              (2U)
#define PATCH_CH1_ADDR_Msk              (0x1FFFFUL << PATCH_CH1_ADDR_Pos)
#define PATCH_CH1_ADDR                  PATCH_CH1_ADDR_Msk

/******************* Bit definition for PATCH_CH2 register ********************/
#define PATCH_CH2_ADDR_Pos              (2U)
#define PATCH_CH2_ADDR_Msk              (0x1FFFFUL << PATCH_CH2_ADDR_Pos)
#define PATCH_CH2_ADDR                  PATCH_CH2_ADDR_Msk

/******************* Bit definition for PATCH_CH3 register ********************/
#define PATCH_CH3_ADDR_Pos              (2U)
#define PATCH_CH3_ADDR_Msk              (0x1FFFFUL << PATCH_CH3_ADDR_Pos)
#define PATCH_CH3_ADDR                  PATCH_CH3_ADDR_Msk

/******************* Bit definition for PATCH_CH4 register ********************/
#define PATCH_CH4_ADDR_Pos              (2U)
#define PATCH_CH4_ADDR_Msk              (0x1FFFFUL << PATCH_CH4_ADDR_Pos)
#define PATCH_CH4_ADDR                  PATCH_CH4_ADDR_Msk

/******************* Bit definition for PATCH_CH5 register ********************/
#define PATCH_CH5_ADDR_Pos              (2U)
#define PATCH_CH5_ADDR_Msk              (0x1FFFFUL << PATCH_CH5_ADDR_Pos)
#define PATCH_CH5_ADDR                  PATCH_CH5_ADDR_Msk

/******************* Bit definition for PATCH_CH6 register ********************/
#define PATCH_CH6_ADDR_Pos              (2U)
#define PATCH_CH6_ADDR_Msk              (0x1FFFFUL << PATCH_CH6_ADDR_Pos)
#define PATCH_CH6_ADDR                  PATCH_CH6_ADDR_Msk

/******************* Bit definition for PATCH_CH7 register ********************/
#define PATCH_CH7_ADDR_Pos              (2U)
#define PATCH_CH7_ADDR_Msk              (0x1FFFFUL << PATCH_CH7_ADDR_Pos)
#define PATCH_CH7_ADDR                  PATCH_CH7_ADDR_Msk

/******************* Bit definition for PATCH_CH8 register ********************/
#define PATCH_CH8_ADDR_Pos              (2U)
#define PATCH_CH8_ADDR_Msk              (0x1FFFFUL << PATCH_CH8_ADDR_Pos)
#define PATCH_CH8_ADDR                  PATCH_CH8_ADDR_Msk

/******************* Bit definition for PATCH_CH9 register ********************/
#define PATCH_CH9_ADDR_Pos              (2U)
#define PATCH_CH9_ADDR_Msk              (0x1FFFFUL << PATCH_CH9_ADDR_Pos)
#define PATCH_CH9_ADDR                  PATCH_CH9_ADDR_Msk

/******************* Bit definition for PATCH_CH10 register *******************/
#define PATCH_CH10_ADDR_Pos             (2U)
#define PATCH_CH10_ADDR_Msk             (0x1FFFFUL << PATCH_CH10_ADDR_Pos)
#define PATCH_CH10_ADDR                 PATCH_CH10_ADDR_Msk

/******************* Bit definition for PATCH_CH11 register *******************/
#define PATCH_CH11_ADDR_Pos             (2U)
#define PATCH_CH11_ADDR_Msk             (0x1FFFFUL << PATCH_CH11_ADDR_Pos)
#define PATCH_CH11_ADDR                 PATCH_CH11_ADDR_Msk

/******************* Bit definition for PATCH_CH12 register *******************/
#define PATCH_CH12_ADDR_Pos             (2U)
#define PATCH_CH12_ADDR_Msk             (0x1FFFFUL << PATCH_CH12_ADDR_Pos)
#define PATCH_CH12_ADDR                 PATCH_CH12_ADDR_Msk

/******************* Bit definition for PATCH_CH13 register *******************/
#define PATCH_CH13_ADDR_Pos             (2U)
#define PATCH_CH13_ADDR_Msk             (0x1FFFFUL << PATCH_CH13_ADDR_Pos)
#define PATCH_CH13_ADDR                 PATCH_CH13_ADDR_Msk

/******************* Bit definition for PATCH_CH14 register *******************/
#define PATCH_CH14_ADDR_Pos             (2U)
#define PATCH_CH14_ADDR_Msk             (0x1FFFFUL << PATCH_CH14_ADDR_Pos)
#define PATCH_CH14_ADDR                 PATCH_CH14_ADDR_Msk

/******************* Bit definition for PATCH_CH15 register *******************/
#define PATCH_CH15_ADDR_Pos             (2U)
#define PATCH_CH15_ADDR_Msk             (0x1FFFFUL << PATCH_CH15_ADDR_Pos)
#define PATCH_CH15_ADDR                 PATCH_CH15_ADDR_Msk

/******************* Bit definition for PATCH_CH16 register *******************/
#define PATCH_CH16_ADDR_Pos             (2U)
#define PATCH_CH16_ADDR_Msk             (0x1FFFFUL << PATCH_CH16_ADDR_Pos)
#define PATCH_CH16_ADDR                 PATCH_CH16_ADDR_Msk

/******************* Bit definition for PATCH_CH17 register *******************/
#define PATCH_CH17_ADDR_Pos             (2U)
#define PATCH_CH17_ADDR_Msk             (0x1FFFFUL << PATCH_CH17_ADDR_Pos)
#define PATCH_CH17_ADDR                 PATCH_CH17_ADDR_Msk

/******************* Bit definition for PATCH_CH18 register *******************/
#define PATCH_CH18_ADDR_Pos             (2U)
#define PATCH_CH18_ADDR_Msk             (0x1FFFFUL << PATCH_CH18_ADDR_Pos)
#define PATCH_CH18_ADDR                 PATCH_CH18_ADDR_Msk

/******************* Bit definition for PATCH_CH19 register *******************/
#define PATCH_CH19_ADDR_Pos             (2U)
#define PATCH_CH19_ADDR_Msk             (0x1FFFFUL << PATCH_CH19_ADDR_Pos)
#define PATCH_CH19_ADDR                 PATCH_CH19_ADDR_Msk

/******************* Bit definition for PATCH_CH20 register *******************/
#define PATCH_CH20_ADDR_Pos             (2U)
#define PATCH_CH20_ADDR_Msk             (0x1FFFFUL << PATCH_CH20_ADDR_Pos)
#define PATCH_CH20_ADDR                 PATCH_CH20_ADDR_Msk

/******************* Bit definition for PATCH_CH21 register *******************/
#define PATCH_CH21_ADDR_Pos             (2U)
#define PATCH_CH21_ADDR_Msk             (0x1FFFFUL << PATCH_CH21_ADDR_Pos)
#define PATCH_CH21_ADDR                 PATCH_CH21_ADDR_Msk

/******************* Bit definition for PATCH_CH22 register *******************/
#define PATCH_CH22_ADDR_Pos             (2U)
#define PATCH_CH22_ADDR_Msk             (0x1FFFFUL << PATCH_CH22_ADDR_Pos)
#define PATCH_CH22_ADDR                 PATCH_CH22_ADDR_Msk

/******************* Bit definition for PATCH_CH23 register *******************/
#define PATCH_CH23_ADDR_Pos             (2U)
#define PATCH_CH23_ADDR_Msk             (0x1FFFFUL << PATCH_CH23_ADDR_Pos)
#define PATCH_CH23_ADDR                 PATCH_CH23_ADDR_Msk

/******************* Bit definition for PATCH_CH24 register *******************/
#define PATCH_CH24_ADDR_Pos             (2U)
#define PATCH_CH24_ADDR_Msk             (0x1FFFFUL << PATCH_CH24_ADDR_Pos)
#define PATCH_CH24_ADDR                 PATCH_CH24_ADDR_Msk

/******************* Bit definition for PATCH_CH25 register *******************/
#define PATCH_CH25_ADDR_Pos             (2U)
#define PATCH_CH25_ADDR_Msk             (0x1FFFFUL << PATCH_CH25_ADDR_Pos)
#define PATCH_CH25_ADDR                 PATCH_CH25_ADDR_Msk

/******************* Bit definition for PATCH_CH26 register *******************/
#define PATCH_CH26_ADDR_Pos             (2U)
#define PATCH_CH26_ADDR_Msk             (0x1FFFFUL << PATCH_CH26_ADDR_Pos)
#define PATCH_CH26_ADDR                 PATCH_CH26_ADDR_Msk

/******************* Bit definition for PATCH_CH27 register *******************/
#define PATCH_CH27_ADDR_Pos             (2U)
#define PATCH_CH27_ADDR_Msk             (0x1FFFFUL << PATCH_CH27_ADDR_Pos)
#define PATCH_CH27_ADDR                 PATCH_CH27_ADDR_Msk

/******************* Bit definition for PATCH_CH28 register *******************/
#define PATCH_CH28_ADDR_Pos             (2U)
#define PATCH_CH28_ADDR_Msk             (0x1FFFFUL << PATCH_CH28_ADDR_Pos)
#define PATCH_CH28_ADDR                 PATCH_CH28_ADDR_Msk

/******************* Bit definition for PATCH_CH29 register *******************/
#define PATCH_CH29_ADDR_Pos             (2U)
#define PATCH_CH29_ADDR_Msk             (0x1FFFFUL << PATCH_CH29_ADDR_Pos)
#define PATCH_CH29_ADDR                 PATCH_CH29_ADDR_Msk

/******************* Bit definition for PATCH_CH30 register *******************/
#define PATCH_CH30_ADDR_Pos             (2U)
#define PATCH_CH30_ADDR_Msk             (0x1FFFFUL << PATCH_CH30_ADDR_Pos)
#define PATCH_CH30_ADDR                 PATCH_CH30_ADDR_Msk

/******************* Bit definition for PATCH_CH31 register *******************/
#define PATCH_CH31_ADDR_Pos             (2U)
#define PATCH_CH31_ADDR_Msk             (0x1FFFFUL << PATCH_CH31_ADDR_Pos)
#define PATCH_CH31_ADDR                 PATCH_CH31_ADDR_Msk

/******************* Bit definition for PATCH_CER register ********************/
#define PATCH_CER_CE_Pos                (0U)
#define PATCH_CER_CE_Msk                (0xFFFFFFFFUL << PATCH_CER_CE_Pos)
#define PATCH_CER_CE                    PATCH_CER_CE_Msk

/******************* Bit definition for PATCH_CSR register ********************/
#define PATCH_CSR_CS_Pos                (0U)
#define PATCH_CSR_CS_Msk                (0xFFFFFFFFUL << PATCH_CSR_CS_Pos)
#define PATCH_CSR_CS                    PATCH_CSR_CS_Msk

/******************* Bit definition for PATCH_CDR register ********************/
#define PATCH_CDR_DATA_Pos              (0U)
#define PATCH_CDR_DATA_Msk              (0xFFFFFFFFUL << PATCH_CDR_DATA_Pos)
#define PATCH_CDR_DATA                  PATCH_CDR_DATA_Msk

/******************* Bit definition for PATCH_IER register ********************/
#define PATCH_IER_CIE_Pos               (0U)
#define PATCH_IER_CIE_Msk               (0xFFFFFFFFUL << PATCH_IER_CIE_Pos)
#define PATCH_IER_CIE                   PATCH_IER_CIE_Msk

/******************* Bit definition for PATCH_ISR register ********************/
#define PATCH_ISR_CIS_Pos               (0U)
#define PATCH_ISR_CIS_Msk               (0xFFFFFFFFUL << PATCH_ISR_CIS_Pos)
#define PATCH_ISR_CIS                   PATCH_ISR_CIS_Msk

/******************* Bit definition for PATCH_ICR register ********************/
#define PATCH_ICR_CIC_Pos               (0U)
#define PATCH_ICR_CIC_Msk               (0xFFFFFFFFUL << PATCH_ICR_CIC_Pos)
#define PATCH_ICR_CIC                   PATCH_ICR_CIC_Msk

/******************* Bit definition for PATCH_VER register ********************/
#define PATCH_VER_ID_Pos                (0U)
#define PATCH_VER_ID_Msk                (0xFFFFFFFFUL << PATCH_VER_ID_Pos)
#define PATCH_VER_ID                    PATCH_VER_ID_Msk

#endif
