/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hdl_da_cmd.h"

uint8_t *g_hdl_data_buf;

// DA HandShake
const char HDL_DA_SYNC_CMD[4] = {0xC0, 0x0C, 0x3F, 0xF3};
// [3F]->[C0] [F3]->[0C] [C0]->[3F] [0C]->[F3]
static bool hdl_da_handshake()
{
    bool success = FALSE;
    HDL_LOGI("hdl_da_handshake start");

    for (int i = 0; i < 4; i++) {
        uint8_t da_rx_data = HDL_COM_GetByte();
        if (da_rx_data == HDL_DA_SYNC_CMD[i]) {
            HDL_COM_PutByte(~da_rx_data);
            if (i == 3) {
                da_rx_data = HDL_COM_GetByte();
                if (da_rx_data == DA_ACK) {
                    success = TRUE;
                }
            }
        } else {
            break;
        }
    }
    return success;
}

static bool hdl_da_check_cmd_arg(uint32_t *error_status)
{
    uint8_t da_rx_data = HDL_COM_GetByte();
    if (da_rx_data == DA_ACK) {
        return TRUE;
    } else if (da_rx_data == DA_NACK) {
        *error_status = HDL_COM_GetData32();
        return FALSE;
    } else {
        return FALSE;
    }
}

static bool hdl_write_packet(uint8_t *packet_buf, uint32_t packet_len, uint32_t checksum)
{
    if (packet_buf == NULL || packet_len != DA_SEND_PACKET_LEN) {
        HDL_LOGE("hdl_write_packet fail, invalid parameter");
        return FALSE;
    }
    uint8_t retry_count = 0;
    while (retry_count < 3) {
        uint8_t response = 0;
        uint32_t total_sent_len = 0;
        while (total_sent_len < packet_len) {
            total_sent_len += HDL_COM_PutByte_Buffer(packet_buf + total_sent_len, packet_len - total_sent_len);
        }

        HDL_LOGI("hdl_write_packet, host checksum=0x%08X", checksum);
        HDL_COM_PutData32(checksum);

        response = HDL_COM_GetByte();
        HDL_LOGI("hdl_write_packet, response=0x%02X", response);
        if (response == DA_CONT_CHAR) {
            // hdl_wirte_packet successfully
            return TRUE;
        } else if (response == DA_STOP_CHAR) {
            return FALSE;
        } else if (response == DA_NACK) {
            uint32_t ret = HDL_COM_GetData32();
            HDL_LOGI("hdl_write_packet, NACK ret=%d", ret);
            if (ret == DA_S_UART_RX_BUF_FULL) {
                uint8_t da_flush_state = HDL_COM_GetByte();
                while (da_flush_state == DA_FLUSH_CONT) {
                    da_flush_state = HDL_COM_GetByte();
                }
            }

            if (ret == DA_S_UART_RX_BUF_FULL
                    || ret == DA_S_UART_GET_DATA_TIMEOUT
                    || ret == DA_S_UART_GET_CHKSUM_LSB_TIMEOUT
                    || ret == DA_S_UART_GET_CHKSUM_MSB_TIMEOUT
                    || ret == DA_S_UART_DATA_CKSUM_ERROR) {
                if (retry_count < 3) {
                    retry_count++;
                    uint8_t da_retry_state = HDL_COM_GetByte();
                    if (da_retry_state == DA_ACK) {
                        HDL_COM_PutByte(DA_CONT_CHAR);
                        continue;
                    } else {
                        return FALSE;
                    }
                } else {
                    HDL_COM_PutByte(DA_NACK);
                    return FALSE;
                }
            }
        }
    }

    return FALSE;
}

bool hdl_sync_with_da(bool enable_log, hdl_da_report_t *da_report)
{
    // DA HandShake
    bool success = FALSE;
    bool da_handshake = hdl_da_handshake();
    HDL_Require_Noerr_Action(da_handshake, exit, "hdl_da_handshake");

    // DA Init_logging
    HDL_COM_PutByte((enable_log ? 1 : 0));
    uint8_t da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_CONT_CHAR, exit, "Init_logging");

    // EPP Function - PMU Latch Power
    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_CONT_CHAR, exit, "DA Latch Power");
    HDL_COM_PutByte(DA_ACK);

    // EPP Function - Disable long press power key
    HDL_COM_PutByte(0);
    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_CONT_CHAR, exit, "DA Disable Long Press Power Key");
    HDL_COM_PutByte(DA_ACK);

    // EPP Function - Raise MCU Speed
    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_CONT_CHAR, exit, "DA Raise MCU Speed");
    HDL_COM_PutByte(DA_ACK);

#if defined (HDL_VIA_UART)
    // EPP Function - Setup Uart HighBaud
    hdl_delay(100);      // Wait previous DA_ACK to sent (115200)
    HDL_COM_SetBaudRate(921600);
    hdl_delay(100);      // Wait Device DA Update BaudRate 921600
    for (int i = 0; i < 20; i++) {
        HDL_COM_PutByte(DA_SYNC_CHAR);
        da_rx_data = HDL_COM_GetByte();
        if (da_rx_data == DA_SYNC_CHAR) {
            HDL_LOGE("DA Setup Uart HighBaud 1 pass");
            break;
        } else {
            hdl_delay(100);
            HDL_LOGE("DA Setup Uart HighBaud 1 fail, 0x%02X", da_rx_data);
        }
    }

    HDL_COM_PutByte(DA_ACK);
    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_ACK, exit, "DA Setup Uart HighBaud 2");

    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_CONT_CHAR, exit, "DA Setup Uart HighBaud 3");
    HDL_COM_PutByte(DA_ACK);
#endif

    // DA Mount Flash
    uint16_t manufacturer_id = HDL_COM_GetData16();
    uint16_t device_id1 = HDL_COM_GetData16();
    uint16_t device_id2 = HDL_COM_GetData16();
    HDL_LOGI("DA Flash ID: 0x%04X 0x%04X 0x%04X", manufacturer_id, device_id1, device_id2);
    uint32_t status = HDL_COM_GetData32();
    HDL_Require_Noerr_Action(status == 0, exit, "DA Mount Flash");

    uint32_t base_addr = HDL_COM_GetData32();
    uint32_t size = HDL_COM_GetData32();
    HDL_LOGI("DA Flash Base_Addr=0x%08X Size=0x%08X", base_addr, size);
    da_rx_data = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(da_rx_data == DA_ACK, exit, "DA Mount Flash");
    HDL_COM_PutByte(DA_ACK);

    // DA Report
    da_report->flash_manufacturer_id = manufacturer_id;
    da_report->flash_id1 = device_id1;
    da_report->flash_id2 = device_id2;
    da_report->flash_base_addr = base_addr;
    da_report->flash_size = size;
    success = TRUE;

exit:
    return success;
}

bool hdl_da_format(const hdl_format_arg_t *format_arg)
{
    bool success = FALSE;
    uint32_t error_status = 0;
    if(format_arg == NULL)
        return FALSE;
    HDL_COM_PutByte(DA_FORMAT_CMD);
    HDL_COM_PutByte(0);
    HDL_COM_PutData32(format_arg->format_addr);
    HDL_COM_PutData32(format_arg->format_size);

    // DA Check Format Arg
    bool da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Format CMD");
    da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Format Check Flash Base_Addr/Range");

    // DA Format Init Callback
    if (format_arg != NULL && format_arg->format_init_cb != NULL) {
        format_arg->format_init_cb(format_arg->format_init_cb_arg);
    }

    while (1) {
        uint32_t ret = HDL_COM_GetData32();
        if (ret == DA_S_DONE) {
            uint8_t progress = HDL_COM_GetByte();
            HDL_COM_PutByte(DA_ACK);
            HDL_LOGI("received S_DONE progress=%d", progress);
            if (format_arg != NULL && format_arg->format_progress_cb != NULL) {
                format_arg->format_progress_cb(format_arg->format_progress_cb_arg, 100);
            }
            break;
        } else if (ret == DA_S_IN_PROGRESS) {
            uint8_t progress = HDL_COM_GetByte();
            HDL_COM_PutByte(DA_ACK);
            if (format_arg != NULL && format_arg->format_progress_cb != NULL) {
                format_arg->format_progress_cb(format_arg->format_progress_cb_arg, progress);
            }
        } else {
            uint32_t erase_addr = HDL_COM_GetData32();
            uint8_t stop_char = HDL_COM_GetByte();
            HDL_LOGE("hdl_da_format error=%d erase_addr=0x%08X stop_char=0x%02X",
                     erase_addr, stop_char);
            goto exit;
        }
    }

    uint8_t ack = HDL_COM_GetByte();
    HDL_Require_Noerr_Action(ack == DA_ACK, exit, "DA Format End-ACK");
    success = TRUE;

exit:
    if (error_status != 0) {
        HDL_LOGE("hdl_da_format error_status=%d", error_status);
    }
    return success;
}

bool hdl_da_download(hdl_image_t *image, hdl_download_cb download_cb, void *download_cb_arg)
{
    bool success = FALSE;
    uint32_t error_status = 0;
    const uint32_t addr = image->image_slave_flash_addr;
    // image_len must 4K align
    const uint32_t image_len = image->image_len;
    const uint32_t packet_num = ((image_len - 1) / DA_SEND_PACKET_LEN) + 1;
    const uint32_t image_4k_len = packet_num * DA_SEND_PACKET_LEN;
    HDL_COM_PutByte(DA_NOR_WRITE_DATA);
    HDL_COM_PutData32(addr);
    HDL_COM_PutData32(image_4k_len);
    HDL_COM_PutData32(DA_SEND_PACKET_LEN);

    // DA Check Download Arg
    bool da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Download CMD, Check Range");

    da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Download CMD, first block erase");

    // Compute & Send Packet
    uint32_t packet_sent_num = 0;
    uint8_t *packet_buf = NULL;
    uint32_t image_checksum = 0;
    while (packet_sent_num < packet_num) {
        uint32_t packet_checksum = 0;
        const bool is_last_packet = (packet_sent_num == (packet_num - 1));
        const uint32_t start_offset = packet_sent_num * DA_SEND_PACKET_LEN;
        const uint32_t cur_packet_len = is_last_packet ? (image_len - start_offset) : DA_SEND_PACKET_LEN;
        HDL_LOGI("hdl_da_download (%s) packet %d/%d %d", image->image_name,
                 (packet_sent_num + 1), packet_num, cur_packet_len);

        bool ret = hdl_flash_read(image->image_name,image->image_host_flash_addr + start_offset,
                                  g_hdl_data_buf, cur_packet_len);
        if (ret) {
            if (is_last_packet && cur_packet_len < DA_SEND_PACKET_LEN) {
                // DA Must received whole DA_SEND_PACKET_LEN, so Fill 0xFF to packet_buf
                uint32_t fill_num = DA_SEND_PACKET_LEN - cur_packet_len;
                memset(packet_buf + cur_packet_len, 0xFF, fill_num);
            }
            packet_buf = g_hdl_data_buf;
            packet_checksum = hdl_compute_simple_checksum(packet_buf, DA_SEND_PACKET_LEN);
            image_checksum += packet_checksum;

            bool write_packet_success = hdl_write_packet(packet_buf, DA_SEND_PACKET_LEN, packet_checksum);
            HDL_Require_Noerr_Action(write_packet_success, exit, "hdl_write_packet");
            // Download Progress callback
            if (download_cb != NULL) {
                download_cb(download_cb_arg, image->image_name,
                            start_offset + cur_packet_len, image_len);
            }
        } else {
            HDL_LOGE("hdl_flash_read addr=0x%08X len=0x%08X fail",
                     image->image_host_flash_addr + start_offset, cur_packet_len);
            continue;
        }

        packet_sent_num++;
    }

    // Read ACK & Image CheckSum
    da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Write Image Packets");
    // Send image_checksum after DA compute checksum completely
    HDL_COM_PutData32(image_checksum);
    uint8_t status = HDL_COM_GetByte();
    if (status != DA_ACK) {
        uint32_t checksum_from_device = HDL_COM_GetData32();
        HDL_LOGE("DA Download Imgae (%s) CheckSum Fail, 0x%08X != 0x%08X",
                 image->image_name, image_checksum, checksum_from_device);
        return FALSE;
    }

    // For Bootloader(ACC)
    HDL_COM_PutByte((image->image_is_bootloader ? DA_ACK : DA_NACK));
    da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Download End");

    if (packet_sent_num == packet_num && error_status == 0) {
        success = TRUE;
    }

exit:
    if (error_status != 0) {
        HDL_LOGE("hdl_da_download error_status=%d", error_status);
    }
    return success;
}

bool hdl_da_readback(const hdl_readback_arg_t *readback_arg)
{
    bool success = FALSE;
    uint32_t error_status = 0;
    const uint32_t readback_flash_addr = readback_arg->readback_flash_addr;
    const uint32_t readback_total_len = readback_arg->readback_total_len;
    HDL_COM_PutByte(DA_READ_CMD);
    HDL_COM_PutData32(readback_flash_addr);
    HDL_COM_PutData32(readback_total_len);
    HDL_COM_PutData32(DA_RECV_PACKET_LEN);

    // DA Check Readback Arg
    bool da_check_success = hdl_da_check_cmd_arg(&error_status);
    HDL_Require_Noerr_Action(da_check_success, exit, "DA Readback CMD, Check Range");

    // Readback init callback
    if (readback_arg->readback_init_cb != NULL) {
        readback_arg->readback_init_cb(readback_arg->readback_init_cb_arg);
    }

    // Readback Packet(4K) & Callback
    const uint32_t packet_num = ((readback_total_len - 1) / DA_RECV_PACKET_LEN) + 1;
    uint32_t packet_read_num = 0;
    uint32_t read_total_bytes = 0;
    while (packet_read_num < packet_num) {
        const bool is_last_packet = (packet_read_num == (packet_num - 1));
        const uint32_t start_offset = packet_read_num * DA_RECV_PACKET_LEN;
        const uint32_t cur_packet_len = is_last_packet ? (readback_total_len - start_offset) : DA_RECV_PACKET_LEN;
        HDL_LOGI("hdl_da_readback packet %d/%d %d", packet_read_num + 1, packet_num, cur_packet_len);

        uint32_t packet_read_bytes = 0;
        uint8_t *packet_read_buf = g_hdl_data_buf;
        while (packet_read_bytes < cur_packet_len) {
            // Even if last_packet, should also read DA_RECV_PACKET_LEN
            packet_read_bytes += HDL_COM_GetByte_Buffer(packet_read_buf + packet_read_bytes,
                                 DA_RECV_PACKET_LEN - packet_read_bytes);
        }
        read_total_bytes += cur_packet_len;
    #if defined (HDL_VIA_I2C)
        // ToDo Note: Wait DA to compute checksum
        // hdl_delay(50);
    #endif
        uint32_t packet_checksum_from_local = hdl_compute_simple_checksum(packet_read_buf, cur_packet_len);
        uint32_t packet_checksum_from_device = HDL_COM_GetData32();
        HDL_Require_Noerr_Action(packet_checksum_from_device == packet_checksum_from_local, exit,
                                 "hdl_readback packet checksum");
        // Readback Callback
        if (readback_arg->readback_cb != NULL) {
            readback_arg->readback_cb(readback_arg->readback_cb_arg, readback_flash_addr + start_offset,
                                      packet_read_buf, cur_packet_len,
                                      read_total_bytes, readback_total_len);
        }

        // Next
        HDL_COM_PutByte(DA_ACK);

        packet_read_num++;
    }
    if (read_total_bytes == readback_total_len) {
        success = TRUE;
    }

exit:
    if (error_status != 0) {
        HDL_LOGE("hdl_da_readback error_status=%d", error_status);
    }
    return success;
}

bool hdl_da_enable_wdt(uint16_t wdt_sec)
{
    HDL_COM_PutByte(DA_ENABLE_WATCHDOG_CMD);
    HDL_COM_PutData16(wdt_sec);
    uint8_t ack = HDL_COM_GetByte();
    return (ack == DA_ACK);
}

bool hdl_da_finish(bool power_off)
{
    HDL_COM_PutByte(DA_FINISH_CMD);
    HDL_COM_PutByte((power_off ? 1 : 0));
    uint8_t ack = HDL_COM_GetByte();
    return (ack == DA_ACK);
}

void hdl_data_buf_init(void)
{
    g_hdl_data_buf = rt_malloc(4096);
    rt_memset(g_hdl_data_buf, 0 ,4096);
}

void hdl_data_buf_clear(void)
{
    rt_free(g_hdl_data_buf);
}
