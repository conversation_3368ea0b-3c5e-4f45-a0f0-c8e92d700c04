/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include "mxs_reg_access.h"

#include "mixo_hal.h"
#include "mixo_log.h"

#ifdef CONFIG_MXS_USE_SPI
#include "mixo_hal_spi.h"
#elif defined(CONFIG_MXS_USE_I2C)
#include "mixo_hal_i2c.h"
#define CALLISTO_SLAVE_ADDR 0x74
#define SLAVE_ADDR          CALLISTO_SLAVE_ADDR
#endif

extern struct rt_spi_device *spi_dev;
/*-----------------------------------------------------------------------------
 * FUNCTIONS DEFINITION
 *---------------------------------------------------------------------------*/
MXS_API void mxs_reg_init()
{
    mixo_hal_init();
}

MXS_API void mxs_reg_deinit()
{
    mixo_hal_deinit();
}

#ifdef CONFIG_MXS_USE_SPI

int mxs_reg_read(uint8_t regAddr, uint8_t* regData)
{
    int rc = 0;
    regAddr &= 0x7F;  // 设置为读取模式

    rc = mixo_hal_spi_read(regAddr, regData, 1);
    
    return rc;
}

int mxs_reg_write(uint8_t regAddr, uint8_t regData)
{
    int rc        = 0;
    uint8_t tx[2];

    tx[0] = 0x80 + (regAddr&0x7F);
    tx[1] = regData;
    rc = mixo_hal_spi_write(tx, 2);
    
    return rc;
}


#elif defined(CONFIG_MXS_USE_I2C)
int mxs_reg_read(uint8_t regAddr, uint8_t* regData)
{
    return mixo_i2c_write_read(SLAVE_ADDR, &regAddr, 1, regData, 1);
}

int mxs_reg_write(uint8_t regAddr, uint8_t regData)
{
    uint8_t tmp[2] = {regAddr, regData};
    return mixo_i2c_write_read(SLAVE_ADDR, tmp, 2, NULL, 0);
}

#else
#error "Please define CONFIG_MXS_USE_SPI or CONFIG_MXS_USE_I2C"
#endif

