/**
  ******************************************************************************
  * @file   psramc.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifndef __PSRAMC_H
#define __PSRAMC_H

typedef struct
{
    __IO uint32_t CTRL_TIME;
    __IO uint32_t READ_CTRL;
    __IO uint32_t DELAY_FINAL_ADD;
    __IO uint32_t DQS_CTRL;
    __IO uint32_t CLK_CTRL;
    __IO uint32_t POWER_UP;
    __IO uint32_t POWER_TIME;
    __IO uint32_t REG_TIME;
    __IO uint32_t IRSR;
    __IO uint32_t IMR;
    __IO uint32_t ISR;
    __IO uint32_t ICR;
    __IO uint32_t DEBUG_SEL;
    __IO uint32_t TIMEOUT_VAL;
    __IO uint32_t PSRAM_FREE;
    __IO uint32_t PSRAM_CFG;
    __IO uint32_t DELAY_TRAIN;
    __IO uint32_t DLL_STATE;
    __IO uint32_t DELAY_MAXMIN;
    __IO uint32_t ARBI_CTRL;
    __IO uint32_t CNT_TRANS_A;
    __IO uint32_t CNT_TRANS_B;
    __IO uint32_t CNT_TRANS_C;
    __IO uint32_t CNT_TRANS_D;
    __IO uint32_t CNT_WAIT_A;
    __IO uint32_t CNT_WAIT_B;
    __IO uint32_t CNT_WAIT_C;
    __IO uint32_t CNT_WAIT_D;
    __IO uint32_t CNT_CTRL;
    __IO uint32_t BURST_LENGTH;
    __IO uint32_t DATA_PINMUX;
    __IO uint32_t PSRAM_VERSION;
    __IO uint32_t MR0;
    __IO uint32_t MR1;
    __IO uint32_t MR2;
    __IO uint32_t MR3;
    __IO uint32_t MR4;
    __IO uint32_t RSVD3;
    __IO uint32_t MR6;
    __IO uint32_t RSVD2;
    __IO uint32_t MR8;
    __IO uint32_t RSVD1[22];
    __IO uint32_t CRE;
} PSRAMC_TypeDef;


/**************** Bit definition for PSRAMC_CTRL_TIME register ****************/
#define PSRAMC_CTRL_TIME_R_TCPH_Pos     (0U)
#define PSRAMC_CTRL_TIME_R_TCPH_Msk     (0x3FUL << PSRAMC_CTRL_TIME_R_TCPH_Pos)
#define PSRAMC_CTRL_TIME_R_TCPH         PSRAMC_CTRL_TIME_R_TCPH_Msk
#define PSRAMC_CTRL_TIME_W_TCPH_Pos     (8U)
#define PSRAMC_CTRL_TIME_W_TCPH_Msk     (0x3FUL << PSRAMC_CTRL_TIME_W_TCPH_Pos)
#define PSRAMC_CTRL_TIME_W_TCPH         PSRAMC_CTRL_TIME_W_TCPH_Msk
#define PSRAMC_CTRL_TIME_WL_Pos         (16U)
#define PSRAMC_CTRL_TIME_WL_Msk         (0x3FUL << PSRAMC_CTRL_TIME_WL_Pos)
#define PSRAMC_CTRL_TIME_WL             PSRAMC_CTRL_TIME_WL_Msk
#define PSRAMC_CTRL_TIME_RL_Pos         (24U)
#define PSRAMC_CTRL_TIME_RL_Msk         (0x3FUL << PSRAMC_CTRL_TIME_RL_Pos)
#define PSRAMC_CTRL_TIME_RL             PSRAMC_CTRL_TIME_RL_Msk
#define PSRAMC_CTRL_TIME_RL_TYPE_Pos    (31U)
#define PSRAMC_CTRL_TIME_RL_TYPE_Msk    (0x1UL << PSRAMC_CTRL_TIME_RL_TYPE_Pos)
#define PSRAMC_CTRL_TIME_RL_TYPE        PSRAMC_CTRL_TIME_RL_TYPE_Msk

/**************** Bit definition for PSRAMC_READ_CTRL register ****************/
#define PSRAMC_READ_CTRL_RD_START_NUM_Pos  (0U)
#define PSRAMC_READ_CTRL_RD_START_NUM_Msk  (0xFUL << PSRAMC_READ_CTRL_RD_START_NUM_Pos)
#define PSRAMC_READ_CTRL_RD_START_NUM   PSRAMC_READ_CTRL_RD_START_NUM_Msk
#define PSRAMC_READ_CTRL_OPT_LENGTH_Pos  (4U)
#define PSRAMC_READ_CTRL_OPT_LENGTH_Msk  (0xFUL << PSRAMC_READ_CTRL_OPT_LENGTH_Pos)
#define PSRAMC_READ_CTRL_OPT_LENGTH     PSRAMC_READ_CTRL_OPT_LENGTH_Msk
#define PSRAMC_READ_CTRL_RD_START_MODE_Pos  (8U)
#define PSRAMC_READ_CTRL_RD_START_MODE_Msk  (0x1UL << PSRAMC_READ_CTRL_RD_START_MODE_Pos)
#define PSRAMC_READ_CTRL_RD_START_MODE  PSRAMC_READ_CTRL_RD_START_MODE_Msk
#define PSRAMC_READ_CTRL_FIFO_RST_TIME_Pos  (12U)
#define PSRAMC_READ_CTRL_FIFO_RST_TIME_Msk  (0x3FUL << PSRAMC_READ_CTRL_FIFO_RST_TIME_Pos)
#define PSRAMC_READ_CTRL_FIFO_RST_TIME  PSRAMC_READ_CTRL_FIFO_RST_TIME_Msk

/************* Bit definition for PSRAMC_DELAY_FINAL_ADD register *************/
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_I_Pos  (0U)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_I_Msk  (0x1FUL << PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_I_Pos)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_I  PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_I_Msk
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_O_Pos  (8U)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_O_Msk  (0x1FUL << PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_O_Pos)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_O  PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_DQS_O_Msk
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_CLK_Pos  (16U)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_CLK_Msk  (0x1FUL << PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_CLK_Pos)
#define PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_CLK  PSRAMC_DELAY_FINAL_ADD_DELAY_FINAL_ADD_CLK_Msk

/**************** Bit definition for PSRAMC_DQS_CTRL register *****************/
#define PSRAMC_DQS_CTRL_I_DQS_L_DELAY_Pos  (0U)
#define PSRAMC_DQS_CTRL_I_DQS_L_DELAY_Msk  (0xFFUL << PSRAMC_DQS_CTRL_I_DQS_L_DELAY_Pos)
#define PSRAMC_DQS_CTRL_I_DQS_L_DELAY   PSRAMC_DQS_CTRL_I_DQS_L_DELAY_Msk
#define PSRAMC_DQS_CTRL_I_DQS_U_DELAY_Pos  (8U)
#define PSRAMC_DQS_CTRL_I_DQS_U_DELAY_Msk  (0xFFUL << PSRAMC_DQS_CTRL_I_DQS_U_DELAY_Pos)
#define PSRAMC_DQS_CTRL_I_DQS_U_DELAY   PSRAMC_DQS_CTRL_I_DQS_U_DELAY_Msk
#define PSRAMC_DQS_CTRL_O_DQS_L_DELAY_Pos  (16U)
#define PSRAMC_DQS_CTRL_O_DQS_L_DELAY_Msk  (0xFFUL << PSRAMC_DQS_CTRL_O_DQS_L_DELAY_Pos)
#define PSRAMC_DQS_CTRL_O_DQS_L_DELAY   PSRAMC_DQS_CTRL_O_DQS_L_DELAY_Msk
#define PSRAMC_DQS_CTRL_O_DQS_U_DELAY_Pos  (24U)
#define PSRAMC_DQS_CTRL_O_DQS_U_DELAY_Msk  (0xFFUL << PSRAMC_DQS_CTRL_O_DQS_U_DELAY_Pos)
#define PSRAMC_DQS_CTRL_O_DQS_U_DELAY   PSRAMC_DQS_CTRL_O_DQS_U_DELAY_Msk

/**************** Bit definition for PSRAMC_CLK_CTRL register *****************/
#define PSRAMC_CLK_CTRL_O_CLK_DELAY_Pos  (0U)
#define PSRAMC_CLK_CTRL_O_CLK_DELAY_Msk  (0xFFUL << PSRAMC_CLK_CTRL_O_CLK_DELAY_Pos)
#define PSRAMC_CLK_CTRL_O_CLK_DELAY     PSRAMC_CLK_CTRL_O_CLK_DELAY_Msk

/**************** Bit definition for PSRAMC_POWER_UP register *****************/
#define PSRAMC_POWER_UP_SW_POWER_LEVEL_Pos  (0U)
#define PSRAMC_POWER_UP_SW_POWER_LEVEL_Msk  (0x1UL << PSRAMC_POWER_UP_SW_POWER_LEVEL_Pos)
#define PSRAMC_POWER_UP_SW_POWER_LEVEL  PSRAMC_POWER_UP_SW_POWER_LEVEL_Msk
#define PSRAMC_POWER_UP_SW_INIT_DONE_Pos  (1U)
#define PSRAMC_POWER_UP_SW_INIT_DONE_Msk  (0x1UL << PSRAMC_POWER_UP_SW_INIT_DONE_Pos)
#define PSRAMC_POWER_UP_SW_INIT_DONE    PSRAMC_POWER_UP_SW_INIT_DONE_Msk
#define PSRAMC_POWER_UP_HW_POWER_PULSE_Pos  (4U)
#define PSRAMC_POWER_UP_HW_POWER_PULSE_Msk  (0x1UL << PSRAMC_POWER_UP_HW_POWER_PULSE_Pos)
#define PSRAMC_POWER_UP_HW_POWER_PULSE  PSRAMC_POWER_UP_HW_POWER_PULSE_Msk
#define PSRAMC_POWER_UP_INIT_DONE_STATE_Pos  (8U)
#define PSRAMC_POWER_UP_INIT_DONE_STATE_Msk  (0x1UL << PSRAMC_POWER_UP_INIT_DONE_STATE_Pos)
#define PSRAMC_POWER_UP_INIT_DONE_STATE  PSRAMC_POWER_UP_INIT_DONE_STATE_Msk
#define PSRAMC_POWER_UP_WAKE_UP_TIME_Pos  (12U)
#define PSRAMC_POWER_UP_WAKE_UP_TIME_Msk  (0xFFUL << PSRAMC_POWER_UP_WAKE_UP_TIME_Pos)
#define PSRAMC_POWER_UP_WAKE_UP_TIME    PSRAMC_POWER_UP_WAKE_UP_TIME_Msk
#define PSRAMC_POWER_UP_WAKE_UP_TRIG_Pos  (20U)
#define PSRAMC_POWER_UP_WAKE_UP_TRIG_Msk  (0x1UL << PSRAMC_POWER_UP_WAKE_UP_TRIG_Pos)
#define PSRAMC_POWER_UP_WAKE_UP_TRIG    PSRAMC_POWER_UP_WAKE_UP_TRIG_Msk

/*************** Bit definition for PSRAMC_POWER_TIME register ****************/
#define PSRAMC_POWER_TIME_RST_ACC_TIME_Pos  (0U)
#define PSRAMC_POWER_TIME_RST_ACC_TIME_Msk  (0x3FUL << PSRAMC_POWER_TIME_RST_ACC_TIME_Pos)
#define PSRAMC_POWER_TIME_RST_ACC_TIME  PSRAMC_POWER_TIME_RST_ACC_TIME_Msk
#define PSRAMC_POWER_TIME_RST_TCPH_TIME_Pos  (8U)
#define PSRAMC_POWER_TIME_RST_TCPH_TIME_Msk  (0x3FUL << PSRAMC_POWER_TIME_RST_TCPH_TIME_Pos)
#define PSRAMC_POWER_TIME_RST_TCPH_TIME  PSRAMC_POWER_TIME_RST_TCPH_TIME_Msk
#define PSRAMC_POWER_TIME_RST_WAIT_TIME_Pos  (16U)
#define PSRAMC_POWER_TIME_RST_WAIT_TIME_Msk  (0x3FFUL << PSRAMC_POWER_TIME_RST_WAIT_TIME_Pos)
#define PSRAMC_POWER_TIME_RST_WAIT_TIME  PSRAMC_POWER_TIME_RST_WAIT_TIME_Msk

/**************** Bit definition for PSRAMC_REG_TIME register *****************/
#define PSRAMC_REG_TIME_READ_REG_TIME_Pos  (0U)
#define PSRAMC_REG_TIME_READ_REG_TIME_Msk  (0x3FUL << PSRAMC_REG_TIME_READ_REG_TIME_Pos)
#define PSRAMC_REG_TIME_READ_REG_TIME   PSRAMC_REG_TIME_READ_REG_TIME_Msk
#define PSRAMC_REG_TIME_SEND_REG_TIME_Pos  (8U)
#define PSRAMC_REG_TIME_SEND_REG_TIME_Msk  (0x3FUL << PSRAMC_REG_TIME_SEND_REG_TIME_Pos)
#define PSRAMC_REG_TIME_SEND_REG_TIME   PSRAMC_REG_TIME_SEND_REG_TIME_Msk
#define PSRAMC_REG_TIME_NOP_TIME_Pos    (16U)
#define PSRAMC_REG_TIME_NOP_TIME_Msk    (0x3FUL << PSRAMC_REG_TIME_NOP_TIME_Pos)
#define PSRAMC_REG_TIME_NOP_TIME        PSRAMC_REG_TIME_NOP_TIME_Msk

/****************** Bit definition for PSRAMC_IRSR register *******************/
#define PSRAMC_IRSR_CROSS_1K_A_Pos      (0U)
#define PSRAMC_IRSR_CROSS_1K_A_Msk      (0x1UL << PSRAMC_IRSR_CROSS_1K_A_Pos)
#define PSRAMC_IRSR_CROSS_1K_A          PSRAMC_IRSR_CROSS_1K_A_Msk
#define PSRAMC_IRSR_CROSS_1K_B_Pos      (1U)
#define PSRAMC_IRSR_CROSS_1K_B_Msk      (0x1UL << PSRAMC_IRSR_CROSS_1K_B_Pos)
#define PSRAMC_IRSR_CROSS_1K_B          PSRAMC_IRSR_CROSS_1K_B_Msk
#define PSRAMC_IRSR_CROSS_1K_C_Pos      (2U)
#define PSRAMC_IRSR_CROSS_1K_C_Msk      (0x1UL << PSRAMC_IRSR_CROSS_1K_C_Pos)
#define PSRAMC_IRSR_CROSS_1K_C          PSRAMC_IRSR_CROSS_1K_C_Msk
#define PSRAMC_IRSR_CROSS_1K_D_Pos      (3U)
#define PSRAMC_IRSR_CROSS_1K_D_Msk      (0x1UL << PSRAMC_IRSR_CROSS_1K_D_Pos)
#define PSRAMC_IRSR_CROSS_1K_D          PSRAMC_IRSR_CROSS_1K_D_Msk
#define PSRAMC_IRSR_INIT_DONE_Pos       (4U)
#define PSRAMC_IRSR_INIT_DONE_Msk       (0x1UL << PSRAMC_IRSR_INIT_DONE_Pos)
#define PSRAMC_IRSR_INIT_DONE           PSRAMC_IRSR_INIT_DONE_Msk
#define PSRAMC_IRSR_RD_TIMEOUT_Pos      (5U)
#define PSRAMC_IRSR_RD_TIMEOUT_Msk      (0x1UL << PSRAMC_IRSR_RD_TIMEOUT_Pos)
#define PSRAMC_IRSR_RD_TIMEOUT          PSRAMC_IRSR_RD_TIMEOUT_Msk
#define PSRAMC_IRSR_DELAY_UPDT_Pos      (6U)
#define PSRAMC_IRSR_DELAY_UPDT_Msk      (0x1UL << PSRAMC_IRSR_DELAY_UPDT_Pos)
#define PSRAMC_IRSR_DELAY_UPDT          PSRAMC_IRSR_DELAY_UPDT_Msk
#define PSRAMC_IRSR_WRONG_DELAY_Pos     (7U)
#define PSRAMC_IRSR_WRONG_DELAY_Msk     (0x1UL << PSRAMC_IRSR_WRONG_DELAY_Pos)
#define PSRAMC_IRSR_WRONG_DELAY         PSRAMC_IRSR_WRONG_DELAY_Msk

/******************* Bit definition for PSRAMC_IMR register *******************/
#define PSRAMC_IMR_CROSS_1K_A_Pos       (0U)
#define PSRAMC_IMR_CROSS_1K_A_Msk       (0x1UL << PSRAMC_IMR_CROSS_1K_A_Pos)
#define PSRAMC_IMR_CROSS_1K_A           PSRAMC_IMR_CROSS_1K_A_Msk
#define PSRAMC_IMR_CROSS_1K_B_Pos       (1U)
#define PSRAMC_IMR_CROSS_1K_B_Msk       (0x1UL << PSRAMC_IMR_CROSS_1K_B_Pos)
#define PSRAMC_IMR_CROSS_1K_B           PSRAMC_IMR_CROSS_1K_B_Msk
#define PSRAMC_IMR_CROSS_1K_C_Pos       (2U)
#define PSRAMC_IMR_CROSS_1K_C_Msk       (0x1UL << PSRAMC_IMR_CROSS_1K_C_Pos)
#define PSRAMC_IMR_CROSS_1K_C           PSRAMC_IMR_CROSS_1K_C_Msk
#define PSRAMC_IMR_CROSS_1K_D_Pos       (3U)
#define PSRAMC_IMR_CROSS_1K_D_Msk       (0x1UL << PSRAMC_IMR_CROSS_1K_D_Pos)
#define PSRAMC_IMR_CROSS_1K_D           PSRAMC_IMR_CROSS_1K_D_Msk
#define PSRAMC_IMR_INIT_DONE_Pos        (4U)
#define PSRAMC_IMR_INIT_DONE_Msk        (0x1UL << PSRAMC_IMR_INIT_DONE_Pos)
#define PSRAMC_IMR_INIT_DONE            PSRAMC_IMR_INIT_DONE_Msk
#define PSRAMC_IMR_RD_TIMEOUT_Pos       (5U)
#define PSRAMC_IMR_RD_TIMEOUT_Msk       (0x1UL << PSRAMC_IMR_RD_TIMEOUT_Pos)
#define PSRAMC_IMR_RD_TIMEOUT           PSRAMC_IMR_RD_TIMEOUT_Msk
#define PSRAMC_IMR_DELAY_UPDT_Pos       (6U)
#define PSRAMC_IMR_DELAY_UPDT_Msk       (0x1UL << PSRAMC_IMR_DELAY_UPDT_Pos)
#define PSRAMC_IMR_DELAY_UPDT           PSRAMC_IMR_DELAY_UPDT_Msk
#define PSRAMC_IMR_WRONG_DELAY_Pos      (7U)
#define PSRAMC_IMR_WRONG_DELAY_Msk      (0x1UL << PSRAMC_IMR_WRONG_DELAY_Pos)
#define PSRAMC_IMR_WRONG_DELAY          PSRAMC_IMR_WRONG_DELAY_Msk

/******************* Bit definition for PSRAMC_ISR register *******************/
#define PSRAMC_ISR_CROSS_1K_A_Pos       (0U)
#define PSRAMC_ISR_CROSS_1K_A_Msk       (0x1UL << PSRAMC_ISR_CROSS_1K_A_Pos)
#define PSRAMC_ISR_CROSS_1K_A           PSRAMC_ISR_CROSS_1K_A_Msk
#define PSRAMC_ISR_CROSS_1K_B_Pos       (1U)
#define PSRAMC_ISR_CROSS_1K_B_Msk       (0x1UL << PSRAMC_ISR_CROSS_1K_B_Pos)
#define PSRAMC_ISR_CROSS_1K_B           PSRAMC_ISR_CROSS_1K_B_Msk
#define PSRAMC_ISR_CROSS_1K_C_Pos       (2U)
#define PSRAMC_ISR_CROSS_1K_C_Msk       (0x1UL << PSRAMC_ISR_CROSS_1K_C_Pos)
#define PSRAMC_ISR_CROSS_1K_C           PSRAMC_ISR_CROSS_1K_C_Msk
#define PSRAMC_ISR_CROSS_1K_D_Pos       (3U)
#define PSRAMC_ISR_CROSS_1K_D_Msk       (0x1UL << PSRAMC_ISR_CROSS_1K_D_Pos)
#define PSRAMC_ISR_CROSS_1K_D           PSRAMC_ISR_CROSS_1K_D_Msk
#define PSRAMC_ISR_INIT_DONE_Pos        (4U)
#define PSRAMC_ISR_INIT_DONE_Msk        (0x1UL << PSRAMC_ISR_INIT_DONE_Pos)
#define PSRAMC_ISR_INIT_DONE            PSRAMC_ISR_INIT_DONE_Msk
#define PSRAMC_ISR_RD_TIMEOUT_Pos       (5U)
#define PSRAMC_ISR_RD_TIMEOUT_Msk       (0x1UL << PSRAMC_ISR_RD_TIMEOUT_Pos)
#define PSRAMC_ISR_RD_TIMEOUT           PSRAMC_ISR_RD_TIMEOUT_Msk
#define PSRAMC_ISR_DELAY_UPDT_Pos       (6U)
#define PSRAMC_ISR_DELAY_UPDT_Msk       (0x1UL << PSRAMC_ISR_DELAY_UPDT_Pos)
#define PSRAMC_ISR_DELAY_UPDT           PSRAMC_ISR_DELAY_UPDT_Msk
#define PSRAMC_ISR_WRONG_DELAY_Pos      (7U)
#define PSRAMC_ISR_WRONG_DELAY_Msk      (0x1UL << PSRAMC_ISR_WRONG_DELAY_Pos)
#define PSRAMC_ISR_WRONG_DELAY          PSRAMC_ISR_WRONG_DELAY_Msk

/******************* Bit definition for PSRAMC_ICR register *******************/
#define PSRAMC_ICR_CROSS_1K_A_Pos       (0U)
#define PSRAMC_ICR_CROSS_1K_A_Msk       (0x1UL << PSRAMC_ICR_CROSS_1K_A_Pos)
#define PSRAMC_ICR_CROSS_1K_A           PSRAMC_ICR_CROSS_1K_A_Msk
#define PSRAMC_ICR_CROSS_1K_B_Pos       (1U)
#define PSRAMC_ICR_CROSS_1K_B_Msk       (0x1UL << PSRAMC_ICR_CROSS_1K_B_Pos)
#define PSRAMC_ICR_CROSS_1K_B           PSRAMC_ICR_CROSS_1K_B_Msk
#define PSRAMC_ICR_CROSS_1K_C_Pos       (2U)
#define PSRAMC_ICR_CROSS_1K_C_Msk       (0x1UL << PSRAMC_ICR_CROSS_1K_C_Pos)
#define PSRAMC_ICR_CROSS_1K_C           PSRAMC_ICR_CROSS_1K_C_Msk
#define PSRAMC_ICR_CROSS_1K_D_Pos       (3U)
#define PSRAMC_ICR_CROSS_1K_D_Msk       (0x1UL << PSRAMC_ICR_CROSS_1K_D_Pos)
#define PSRAMC_ICR_CROSS_1K_D           PSRAMC_ICR_CROSS_1K_D_Msk
#define PSRAMC_ICR_INIT_DONE_Pos        (4U)
#define PSRAMC_ICR_INIT_DONE_Msk        (0x1UL << PSRAMC_ICR_INIT_DONE_Pos)
#define PSRAMC_ICR_INIT_DONE            PSRAMC_ICR_INIT_DONE_Msk
#define PSRAMC_ICR_RD_TIMEOUT_Pos       (5U)
#define PSRAMC_ICR_RD_TIMEOUT_Msk       (0x1UL << PSRAMC_ICR_RD_TIMEOUT_Pos)
#define PSRAMC_ICR_RD_TIMEOUT           PSRAMC_ICR_RD_TIMEOUT_Msk
#define PSRAMC_ICR_DELAY_UPDT_Pos       (6U)
#define PSRAMC_ICR_DELAY_UPDT_Msk       (0x1UL << PSRAMC_ICR_DELAY_UPDT_Pos)
#define PSRAMC_ICR_DELAY_UPDT           PSRAMC_ICR_DELAY_UPDT_Msk
#define PSRAMC_ICR_WRONG_DELAY_Pos      (7U)
#define PSRAMC_ICR_WRONG_DELAY_Msk      (0x1UL << PSRAMC_ICR_WRONG_DELAY_Pos)
#define PSRAMC_ICR_WRONG_DELAY          PSRAMC_ICR_WRONG_DELAY_Msk

/**************** Bit definition for PSRAMC_DEBUG_SEL register ****************/
#define PSRAMC_DEBUG_SEL_DEBUG_SEL_Pos  (0U)
#define PSRAMC_DEBUG_SEL_DEBUG_SEL_Msk  (0xFFUL << PSRAMC_DEBUG_SEL_DEBUG_SEL_Pos)
#define PSRAMC_DEBUG_SEL_DEBUG_SEL      PSRAMC_DEBUG_SEL_DEBUG_SEL_Msk

/*************** Bit definition for PSRAMC_TIMEOUT_VAL register ***************/
#define PSRAMC_TIMEOUT_VAL_TIMEOUT_VAL_Pos  (0U)
#define PSRAMC_TIMEOUT_VAL_TIMEOUT_VAL_Msk  (0xFFFFFUL << PSRAMC_TIMEOUT_VAL_TIMEOUT_VAL_Pos)
#define PSRAMC_TIMEOUT_VAL_TIMEOUT_VAL  PSRAMC_TIMEOUT_VAL_TIMEOUT_VAL_Msk

/*************** Bit definition for PSRAMC_PSRAM_FREE register ****************/
#define PSRAMC_PSRAM_FREE_PHY_STATE_Pos  (0U)
#define PSRAMC_PSRAM_FREE_PHY_STATE_Msk  (0x1FUL << PSRAMC_PSRAM_FREE_PHY_STATE_Pos)
#define PSRAMC_PSRAM_FREE_PHY_STATE     PSRAMC_PSRAM_FREE_PHY_STATE_Msk
#define PSRAMC_PSRAM_FREE_PSRAM_FREE_Pos  (31U)
#define PSRAMC_PSRAM_FREE_PSRAM_FREE_Msk  (0x1UL << PSRAMC_PSRAM_FREE_PSRAM_FREE_Pos)
#define PSRAMC_PSRAM_FREE_PSRAM_FREE    PSRAMC_PSRAM_FREE_PSRAM_FREE_Msk

/**************** Bit definition for PSRAMC_PSRAM_CFG register ****************/
#define PSRAMC_PSRAM_CFG_TCE_MAX_LENGTH_Pos  (14U)
#define PSRAMC_PSRAM_CFG_TCE_MAX_LENGTH_Msk  (0xFFFUL << PSRAMC_PSRAM_CFG_TCE_MAX_LENGTH_Pos)
#define PSRAMC_PSRAM_CFG_TCE_MAX_LENGTH  PSRAMC_PSRAM_CFG_TCE_MAX_LENGTH_Msk
#define PSRAMC_PSRAM_CFG_RD_HOLD_EN_Pos  (26U)
#define PSRAMC_PSRAM_CFG_RD_HOLD_EN_Msk  (0x1UL << PSRAMC_PSRAM_CFG_RD_HOLD_EN_Pos)
#define PSRAMC_PSRAM_CFG_RD_HOLD_EN     PSRAMC_PSRAM_CFG_RD_HOLD_EN_Msk
#define PSRAMC_PSRAM_CFG_XCCELA_PSRAM_EN_Pos  (27U)
#define PSRAMC_PSRAM_CFG_XCCELA_PSRAM_EN_Msk  (0x1UL << PSRAMC_PSRAM_CFG_XCCELA_PSRAM_EN_Pos)
#define PSRAMC_PSRAM_CFG_XCCELA_PSRAM_EN  PSRAMC_PSRAM_CFG_XCCELA_PSRAM_EN_Msk
#define PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Pos  (28U)
#define PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Msk  (0x3UL << PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Pos)
#define PSRAMC_PSRAM_CFG_PACKAGE_TYPE   PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Msk
#define PSRAMC_PSRAM_CFG_SINGLE_PACKAGE_TYPE   (0UL << PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Pos)
#define PSRAMC_PSRAM_CFG_DUAL_PACKAGE_TYPE     (3UL << PSRAMC_PSRAM_CFG_PACKAGE_TYPE_Pos)
#define PSRAMC_PSRAM_CFG_PSRAM_DENSITY_Pos  (30U)
#define PSRAMC_PSRAM_CFG_PSRAM_DENSITY_Msk  (0x3UL << PSRAMC_PSRAM_CFG_PSRAM_DENSITY_Pos)
#define PSRAMC_PSRAM_CFG_PSRAM_DENSITY  PSRAMC_PSRAM_CFG_PSRAM_DENSITY_Msk

/*************** Bit definition for PSRAMC_DELAY_TRAIN register ***************/
#define PSRAMC_DELAY_TRAIN_TRAINING_EN_Pos  (1U)
#define PSRAMC_DELAY_TRAIN_TRAINING_EN_Msk  (0x1UL << PSRAMC_DELAY_TRAIN_TRAINING_EN_Pos)
#define PSRAMC_DELAY_TRAIN_TRAINING_EN  PSRAMC_DELAY_TRAIN_TRAINING_EN_Msk
#define PSRAMC_DELAY_TRAIN_DELAY_STEP_Pos  (2U)
#define PSRAMC_DELAY_TRAIN_DELAY_STEP_Msk  (0x3UL << PSRAMC_DELAY_TRAIN_DELAY_STEP_Pos)
#define PSRAMC_DELAY_TRAIN_DELAY_STEP   PSRAMC_DELAY_TRAIN_DELAY_STEP_Msk
#define PSRAMC_DELAY_TRAIN_INIT_DELAY_Pos  (4U)
#define PSRAMC_DELAY_TRAIN_INIT_DELAY_Msk  (0xFFUL << PSRAMC_DELAY_TRAIN_INIT_DELAY_Pos)
#define PSRAMC_DELAY_TRAIN_INIT_DELAY   PSRAMC_DELAY_TRAIN_INIT_DELAY_Msk
#define PSRAMC_DELAY_TRAIN_INIT_TRIM_Pos  (12U)
#define PSRAMC_DELAY_TRAIN_INIT_TRIM_Msk  (0x7UL << PSRAMC_DELAY_TRAIN_INIT_TRIM_Pos)
#define PSRAMC_DELAY_TRAIN_INIT_TRIM    PSRAMC_DELAY_TRAIN_INIT_TRIM_Msk
#define PSRAMC_DELAY_TRAIN_WAIT_DLL_VALUE_Pos  (16U)
#define PSRAMC_DELAY_TRAIN_WAIT_DLL_VALUE_Msk  (0xFUL << PSRAMC_DELAY_TRAIN_WAIT_DLL_VALUE_Pos)
#define PSRAMC_DELAY_TRAIN_WAIT_DLL_VALUE  PSRAMC_DELAY_TRAIN_WAIT_DLL_VALUE_Msk
#define PSRAMC_DELAY_TRAIN_DELAY_THRESHOLD_Pos  (24U)
#define PSRAMC_DELAY_TRAIN_DELAY_THRESHOLD_Msk  (0xFUL << PSRAMC_DELAY_TRAIN_DELAY_THRESHOLD_Pos)
#define PSRAMC_DELAY_TRAIN_DELAY_THRESHOLD  PSRAMC_DELAY_TRAIN_DELAY_THRESHOLD_Msk
#define PSRAMC_DELAY_TRAIN_DELAY_FINAL_ADD_Pos  (28U)
#define PSRAMC_DELAY_TRAIN_DELAY_FINAL_ADD_Msk  (0x7UL << PSRAMC_DELAY_TRAIN_DELAY_FINAL_ADD_Pos)
#define PSRAMC_DELAY_TRAIN_DELAY_FINAL_ADD  PSRAMC_DELAY_TRAIN_DELAY_FINAL_ADD_Msk
#define PSRAMC_DELAY_TRAIN_AUTO_CFG_Pos  (31U)
#define PSRAMC_DELAY_TRAIN_AUTO_CFG_Msk  (0x1UL << PSRAMC_DELAY_TRAIN_AUTO_CFG_Pos)
#define PSRAMC_DELAY_TRAIN_AUTO_CFG     PSRAMC_DELAY_TRAIN_AUTO_CFG_Msk

/**************** Bit definition for PSRAMC_DLL_STATE register ****************/
#define PSRAMC_DLL_STATE_TRAIN_DELAY_Pos  (0U)
#define PSRAMC_DLL_STATE_TRAIN_DELAY_Msk  (0xFFUL << PSRAMC_DLL_STATE_TRAIN_DELAY_Pos)
#define PSRAMC_DLL_STATE_TRAIN_DELAY    PSRAMC_DLL_STATE_TRAIN_DELAY_Msk
#define PSRAMC_DLL_STATE_REAL_PATH_DELAY_Pos  (8U)
#define PSRAMC_DLL_STATE_REAL_PATH_DELAY_Msk  (0xFFUL << PSRAMC_DLL_STATE_REAL_PATH_DELAY_Pos)
#define PSRAMC_DLL_STATE_REAL_PATH_DELAY  PSRAMC_DLL_STATE_REAL_PATH_DELAY_Msk
#define PSRAMC_DLL_STATE_DLL_LOCKED_Pos  (16U)
#define PSRAMC_DLL_STATE_DLL_LOCKED_Msk  (0x1UL << PSRAMC_DLL_STATE_DLL_LOCKED_Pos)
#define PSRAMC_DLL_STATE_DLL_LOCKED     PSRAMC_DLL_STATE_DLL_LOCKED_Msk

/************** Bit definition for PSRAMC_DELAY_MAXMIN register ***************/
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MIN_Pos  (0U)
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MIN_Msk  (0xFFUL << PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MIN_Pos)
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MIN  PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MIN_Msk
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MAX_Pos  (8U)
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MAX_Msk  (0xFFUL << PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MAX_Pos)
#define PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MAX  PSRAMC_DELAY_MAXMIN_TRAIN_DELAY_MAX_Msk

/**************** Bit definition for PSRAMC_ARBI_CTRL register ****************/
#define PSRAMC_ARBI_CTRL_CMD_PRIORITY_Pos  (0U)
#define PSRAMC_ARBI_CTRL_CMD_PRIORITY_Msk  (0xFFUL << PSRAMC_ARBI_CTRL_CMD_PRIORITY_Pos)
#define PSRAMC_ARBI_CTRL_CMD_PRIORITY   PSRAMC_ARBI_CTRL_CMD_PRIORITY_Msk
#define PSRAMC_ARBI_CTRL_CUR_AHB_Pos    (8U)
#define PSRAMC_ARBI_CTRL_CUR_AHB_Msk    (0x3UL << PSRAMC_ARBI_CTRL_CUR_AHB_Pos)
#define PSRAMC_ARBI_CTRL_CUR_AHB        PSRAMC_ARBI_CTRL_CUR_AHB_Msk
#define PSRAMC_ARBI_CTRL_ARBI_ALG_Pos   (31U)
#define PSRAMC_ARBI_CTRL_ARBI_ALG_Msk   (0x1UL << PSRAMC_ARBI_CTRL_ARBI_ALG_Pos)
#define PSRAMC_ARBI_CTRL_ARBI_ALG       PSRAMC_ARBI_CTRL_ARBI_ALG_Msk

/*************** Bit definition for PSRAMC_CNT_TRANS_A register ***************/
#define PSRAMC_CNT_TRANS_A_CNT_TRANS_A_Pos  (0U)
#define PSRAMC_CNT_TRANS_A_CNT_TRANS_A_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_TRANS_A_CNT_TRANS_A_Pos)
#define PSRAMC_CNT_TRANS_A_CNT_TRANS_A  PSRAMC_CNT_TRANS_A_CNT_TRANS_A_Msk

/*************** Bit definition for PSRAMC_CNT_TRANS_B register ***************/
#define PSRAMC_CNT_TRANS_B_CNT_TRANS_B_Pos  (0U)
#define PSRAMC_CNT_TRANS_B_CNT_TRANS_B_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_TRANS_B_CNT_TRANS_B_Pos)
#define PSRAMC_CNT_TRANS_B_CNT_TRANS_B  PSRAMC_CNT_TRANS_B_CNT_TRANS_B_Msk

/*************** Bit definition for PSRAMC_CNT_TRANS_C register ***************/
#define PSRAMC_CNT_TRANS_C_CNT_TRANS_C_Pos  (0U)
#define PSRAMC_CNT_TRANS_C_CNT_TRANS_C_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_TRANS_C_CNT_TRANS_C_Pos)
#define PSRAMC_CNT_TRANS_C_CNT_TRANS_C  PSRAMC_CNT_TRANS_C_CNT_TRANS_C_Msk

/*************** Bit definition for PSRAMC_CNT_TRANS_D register ***************/
#define PSRAMC_CNT_TRANS_D_CNT_TRANS_D_Pos  (0U)
#define PSRAMC_CNT_TRANS_D_CNT_TRANS_D_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_TRANS_D_CNT_TRANS_D_Pos)
#define PSRAMC_CNT_TRANS_D_CNT_TRANS_D  PSRAMC_CNT_TRANS_D_CNT_TRANS_D_Msk

/*************** Bit definition for PSRAMC_CNT_WAIT_A register ****************/
#define PSRAMC_CNT_WAIT_A_CNT_WAIT_A_Pos  (0U)
#define PSRAMC_CNT_WAIT_A_CNT_WAIT_A_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_WAIT_A_CNT_WAIT_A_Pos)
#define PSRAMC_CNT_WAIT_A_CNT_WAIT_A    PSRAMC_CNT_WAIT_A_CNT_WAIT_A_Msk

/*************** Bit definition for PSRAMC_CNT_WAIT_B register ****************/
#define PSRAMC_CNT_WAIT_B_CNT_WAIT_B_Pos  (0U)
#define PSRAMC_CNT_WAIT_B_CNT_WAIT_B_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_WAIT_B_CNT_WAIT_B_Pos)
#define PSRAMC_CNT_WAIT_B_CNT_WAIT_B    PSRAMC_CNT_WAIT_B_CNT_WAIT_B_Msk

/*************** Bit definition for PSRAMC_CNT_WAIT_C register ****************/
#define PSRAMC_CNT_WAIT_C_CNT_WAIT_C_Pos  (0U)
#define PSRAMC_CNT_WAIT_C_CNT_WAIT_C_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_WAIT_C_CNT_WAIT_C_Pos)
#define PSRAMC_CNT_WAIT_C_CNT_WAIT_C    PSRAMC_CNT_WAIT_C_CNT_WAIT_C_Msk

/*************** Bit definition for PSRAMC_CNT_WAIT_D register ****************/
#define PSRAMC_CNT_WAIT_D_CNT_WAIT_D_Pos  (0U)
#define PSRAMC_CNT_WAIT_D_CNT_WAIT_D_Msk  (0xFFFFFFFFUL << PSRAMC_CNT_WAIT_D_CNT_WAIT_D_Pos)
#define PSRAMC_CNT_WAIT_D_CNT_WAIT_D    PSRAMC_CNT_WAIT_D_CNT_WAIT_D_Msk

/**************** Bit definition for PSRAMC_CNT_CTRL register *****************/
#define PSRAMC_CNT_CTRL_CNT_START_A_Pos  (0U)
#define PSRAMC_CNT_CTRL_CNT_START_A_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_START_A_Pos)
#define PSRAMC_CNT_CTRL_CNT_START_A     PSRAMC_CNT_CTRL_CNT_START_A_Msk
#define PSRAMC_CNT_CTRL_CNT_STOP_A_Pos  (1U)
#define PSRAMC_CNT_CTRL_CNT_STOP_A_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_STOP_A_Pos)
#define PSRAMC_CNT_CTRL_CNT_STOP_A      PSRAMC_CNT_CTRL_CNT_STOP_A_Msk
#define PSRAMC_CNT_CTRL_CNT_START_B_Pos  (2U)
#define PSRAMC_CNT_CTRL_CNT_START_B_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_START_B_Pos)
#define PSRAMC_CNT_CTRL_CNT_START_B     PSRAMC_CNT_CTRL_CNT_START_B_Msk
#define PSRAMC_CNT_CTRL_CNT_STOP_B_Pos  (3U)
#define PSRAMC_CNT_CTRL_CNT_STOP_B_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_STOP_B_Pos)
#define PSRAMC_CNT_CTRL_CNT_STOP_B      PSRAMC_CNT_CTRL_CNT_STOP_B_Msk
#define PSRAMC_CNT_CTRL_CNT_START_C_Pos  (4U)
#define PSRAMC_CNT_CTRL_CNT_START_C_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_START_C_Pos)
#define PSRAMC_CNT_CTRL_CNT_START_C     PSRAMC_CNT_CTRL_CNT_START_C_Msk
#define PSRAMC_CNT_CTRL_CNT_STOP_C_Pos  (5U)
#define PSRAMC_CNT_CTRL_CNT_STOP_C_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_STOP_C_Pos)
#define PSRAMC_CNT_CTRL_CNT_STOP_C      PSRAMC_CNT_CTRL_CNT_STOP_C_Msk
#define PSRAMC_CNT_CTRL_CNT_START_D_Pos  (6U)
#define PSRAMC_CNT_CTRL_CNT_START_D_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_START_D_Pos)
#define PSRAMC_CNT_CTRL_CNT_START_D     PSRAMC_CNT_CTRL_CNT_START_D_Msk
#define PSRAMC_CNT_CTRL_CNT_STOP_D_Pos  (7U)
#define PSRAMC_CNT_CTRL_CNT_STOP_D_Msk  (0x1UL << PSRAMC_CNT_CTRL_CNT_STOP_D_Pos)
#define PSRAMC_CNT_CTRL_CNT_STOP_D      PSRAMC_CNT_CTRL_CNT_STOP_D_Msk

/************** Bit definition for PSRAMC_BURST_LENGTH register ***************/
#define PSRAMC_BURST_LENGTH_WRAP16_EN_Pos  (0U)
#define PSRAMC_BURST_LENGTH_WRAP16_EN_Msk  (0x1UL << PSRAMC_BURST_LENGTH_WRAP16_EN_Pos)
#define PSRAMC_BURST_LENGTH_WRAP16_EN   PSRAMC_BURST_LENGTH_WRAP16_EN_Msk
#define PSRAMC_BURST_LENGTH_WRAP32_EN_Pos  (1U)
#define PSRAMC_BURST_LENGTH_WRAP32_EN_Msk  (0x1UL << PSRAMC_BURST_LENGTH_WRAP32_EN_Pos)
#define PSRAMC_BURST_LENGTH_WRAP32_EN   PSRAMC_BURST_LENGTH_WRAP32_EN_Msk
#define PSRAMC_BURST_LENGTH_WRAP64_EN_Pos  (2U)
#define PSRAMC_BURST_LENGTH_WRAP64_EN_Msk  (0x1UL << PSRAMC_BURST_LENGTH_WRAP64_EN_Pos)
#define PSRAMC_BURST_LENGTH_WRAP64_EN   PSRAMC_BURST_LENGTH_WRAP64_EN_Msk
#define PSRAMC_BURST_LENGTH_WRAP512_EN_Pos  (3U)
#define PSRAMC_BURST_LENGTH_WRAP512_EN_Msk  (0x1UL << PSRAMC_BURST_LENGTH_WRAP512_EN_Pos)
#define PSRAMC_BURST_LENGTH_WRAP512_EN  PSRAMC_BURST_LENGTH_WRAP512_EN_Msk
#define PSRAMC_BURST_LENGTH_QPI_EN_Pos  (31U)
#define PSRAMC_BURST_LENGTH_QPI_EN_Msk  (0x1UL << PSRAMC_BURST_LENGTH_QPI_EN_Pos)
#define PSRAMC_BURST_LENGTH_QPI_EN      PSRAMC_BURST_LENGTH_QPI_EN_Msk

/*************** Bit definition for PSRAMC_DATA_PINMUX register ***************/
#define PSRAMC_DATA_PINMUX_PSRAM_DATA0_SEL_Pos  (0U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA0_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA0_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA0_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA0_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA1_SEL_Pos  (4U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA1_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA1_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA1_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA1_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA2_SEL_Pos  (8U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA2_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA2_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA2_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA2_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA3_SEL_Pos  (12U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA3_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA3_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA3_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA3_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA4_SEL_Pos  (16U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA4_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA4_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA4_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA4_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA5_SEL_Pos  (20U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA5_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA5_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA5_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA5_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA6_SEL_Pos  (24U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA6_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA6_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA6_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA6_SEL_Msk
#define PSRAMC_DATA_PINMUX_PSRAM_DATA7_SEL_Pos  (28U)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA7_SEL_Msk  (0x7UL << PSRAMC_DATA_PINMUX_PSRAM_DATA7_SEL_Pos)
#define PSRAMC_DATA_PINMUX_PSRAM_DATA7_SEL  PSRAMC_DATA_PINMUX_PSRAM_DATA7_SEL_Msk

/************** Bit definition for PSRAMC_PSRAM_VERSION register **************/
#define PSRAMC_PSRAM_VERSION_PSRAM_VERSION_Pos  (0U)
#define PSRAMC_PSRAM_VERSION_PSRAM_VERSION_Msk  (0xFUL << PSRAMC_PSRAM_VERSION_PSRAM_VERSION_Pos)
#define PSRAMC_PSRAM_VERSION_PSRAM_VERSION  PSRAMC_PSRAM_VERSION_PSRAM_VERSION_Msk

/******************* Bit definition for PSRAMC_MR0 register *******************/
#define PSRAMC_MR0_MR0_Pos              (0U)
#define PSRAMC_MR0_MR0_Msk              (0xFFFFFFFFUL << PSRAMC_MR0_MR0_Pos)
#define PSRAMC_MR0_MR0                  PSRAMC_MR0_MR0_Msk

/******************* Bit definition for PSRAMC_MR1 register *******************/
#define PSRAMC_MR1_MR1_Pos              (0U)
#define PSRAMC_MR1_MR1_Msk              (0xFFFFFFFFUL << PSRAMC_MR1_MR1_Pos)
#define PSRAMC_MR1_MR1                  PSRAMC_MR1_MR1_Msk

/******************* Bit definition for PSRAMC_MR2 register *******************/
#define PSRAMC_MR2_MR2_Pos              (0U)
#define PSRAMC_MR2_MR2_Msk              (0xFFFFFFFFUL << PSRAMC_MR2_MR2_Pos)
#define PSRAMC_MR2_MR2                  PSRAMC_MR2_MR2_Msk

/******************* Bit definition for PSRAMC_MR3 register *******************/
#define PSRAMC_MR3_MR3_Pos              (0U)
#define PSRAMC_MR3_MR3_Msk              (0xFFFFFFFFUL << PSRAMC_MR3_MR3_Pos)
#define PSRAMC_MR3_MR3                  PSRAMC_MR3_MR3_Msk

/******************* Bit definition for PSRAMC_MR4 register *******************/
#define PSRAMC_MR4_MR4_Pos              (0U)
#define PSRAMC_MR4_MR4_Msk              (0xFFFFFFFFUL << PSRAMC_MR4_MR4_Pos)
#define PSRAMC_MR4_MR4                  PSRAMC_MR4_MR4_Msk

/******************* Bit definition for PSRAMC_MR6 register *******************/
#define PSRAMC_MR6_MR6_Pos              (0U)
#define PSRAMC_MR6_MR6_Msk              (0xFFFFFFFFUL << PSRAMC_MR6_MR6_Pos)
#define PSRAMC_MR6_MR6                  PSRAMC_MR6_MR6_Msk

/******************* Bit definition for PSRAMC_MR8 register *******************/
#define PSRAMC_MR8_MR8_Pos              (0U)
#define PSRAMC_MR8_MR8_Msk              (0xFFFFFFFFUL << PSRAMC_MR8_MR8_Pos)
#define PSRAMC_MR8_MR8                  PSRAMC_MR8_MR8_Msk

/******************* Bit definition for PSRAMC_CRE register *******************/
#define PSRAMC_CRE_CRE_Pos              (0U)
#define PSRAMC_CRE_CRE_Msk              (0x1UL << PSRAMC_CRE_CRE_Pos)
#define PSRAMC_CRE_CRE                  PSRAMC_CRE_CRE_Msk

#endif
