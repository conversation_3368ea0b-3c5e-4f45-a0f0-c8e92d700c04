/**
  ******************************************************************************
  * @file   st7789h2.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for ST7789H2 LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st7789h2.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7789H2
  * @brief This file provides a set of functions needed to drive the
  *        ST7789H2 LCD.
  * @{
  */

/** @defgroup ST7789H2_Private_TypesDefinitions
  * @{
  */

#ifdef ROW_OFFSET_PLUS
    #define ROW_OFFSET  (ROW_OFFSET_PLUS)
#else
    #define ROW_OFFSET  (0)
#endif



/**
  * @}
  */

/** @defgroup ST7789H2_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ST7789H2_Private_Macros
  * @{
  */



//#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif
void ST7789H2_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
uint32_t ST7789H2_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);


/**
  * @}
  */

/** @defgroup ST7789H2_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ST7789H2_drv =
{
    ST7789H2_Init,
    ST7789H2_ReadID,
    ST7789H2_DisplayOn,
    ST7789H2_DisplayOff,
    ST7789H2_SetRegion,
    ST7789H2_WritePixel,
    ST7789H2_WriteMultiplePixels,
    ST7789H2_ReadPixel,
    ST7789H2_SetColorMode,
    ST7789H2_SetBrightness,
    NULL,
    NULL
};


static LCDC_InitTypeDef lcdc_int_cfg =
{
    .lcd_itf = LCDC_INTF_SPI_DCX_1DATA,
    .freq = 24000000,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,
    .cfg = {
        .spi = {

            .dummy_clock = 1,
            .syn_mode = HAL_LCDC_SYNC_DISABLE,
            .vsyn_polarity = 0,
            .vsyn_delay_us = 1000,
            .hsyn_num = 0,
        },
    },

};

LCD_DRIVER_EXPORT(st7789h2, 0x858552, &lcdc_int_cfg,
                  &ST7789H2_drv,
                  ST7789H2_LCD_PIXEL_WIDTH,
                  ST7789H2_LCD_PIXEL_HEIGHT,
                  1);
/**
  * @}
  */

/** @defgroup ST7789H2_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7789H2_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ST7789H2_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 4000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }

    }
}

/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ST7789H2_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];

    /* Initialize ST7789H2 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    HAL_Delay_us(10);
    BSP_LCD_Reset(1);

    /* Sleep In Command */
    ST7789H2_WriteReg(hlcdc, ST7789H2_SLEEP_IN, (uint8_t *)NULL, 0);
    /* Wait for 10ms */
    LCD_DRIVER_DELAY_MS(10);

    /* SW Reset Command */
    ST7789H2_WriteReg(hlcdc, 0x01, (uint8_t *)NULL, 0);
    /* Wait for 200ms */
    LCD_DRIVER_DELAY_MS(200);

    /* Sleep Out Command */
    ST7789H2_WriteReg(hlcdc, ST7789H2_SLEEP_OUT, (uint8_t *)NULL, 0);
    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);

    /* Normal display for Driver Down side */
    parameter[0] = 0x00;
    ST7789H2_WriteReg(hlcdc, ST7789H2_NORMAL_DISPLAY, parameter, 1);

    /* Color mode 16bits/pixel */
    parameter[0] = 0x55;
    ST7789H2_WriteReg(hlcdc, ST7789H2_COLOR_MODE, parameter, 1);

    /* Display inversion On */
    ST7789H2_WriteReg(hlcdc, ST7789H2_DISPLAY_INVERSION, (uint8_t *)NULL, 0);

#if 0

    /*--------------- ST7789H2 Frame rate setting -------------------------------*/
    /* PORCH control setting */
    parameter[0] = 0x0C;
    parameter[1] = 0x0C;
    parameter[2] = 0x00;
    parameter[3] = 0x33;
    parameter[4] = 0x33;
    ST7789H2_WriteReg(hlcdc, ST7789H2_PORCH_CTRL, parameter, 5);

    /* GATE control setting */
    parameter[0] = 0x35;
    ST7789H2_WriteReg(hlcdc, ST7789H2_GATE_CTRL, parameter, 1);

    /*--------------- ST7789H2 Power setting ------------------------------------*/
    /* VCOM setting */
    parameter[0] = 0x1F;
    ST7789H2_WriteReg(hlcdc, ST7789H2_VCOM_SET, parameter, 1);

    /* LCM Control setting */
    parameter[0] = 0x2C;
    ST7789H2_WriteReg(hlcdc, ST7789H2_LCM_CTRL, parameter, 1);

    /* VDV and VRH Command Enable */
    parameter[0] = 0x01;
    parameter[1] = 0xC3;
    ST7789H2_WriteReg(hlcdc, ST7789H2_VDV_VRH_EN, parameter, 2);

    /* VDV Set */
    parameter[0] = 0x20;
    ST7789H2_WriteReg(hlcdc, ST7789H2_VDV_SET, parameter, 1);

    /* Frame Rate Control in normal mode */
    parameter[0] = 0x0F;
    ST7789H2_WriteReg(hlcdc, ST7789H2_FR_CTRL, parameter, 1);

    /* Power Control */
    parameter[0] = 0xA4;
    parameter[1] = 0xA1;
    ST7789H2_WriteReg(hlcdc, ST7789H2_POWER_CTRL, parameter, 1);

#endif

    /*--------------- ST7789H2 Gamma setting ------------------------------------*/
    /* Positive Voltage Gamma Control */
    parameter[0] = 0xD0;
    parameter[1] = 0x08;
    parameter[2] = 0x11;
    parameter[3] = 0x08;
    parameter[4] = 0x0C;
    parameter[5] = 0x15;
    parameter[6] = 0x39;
    parameter[7] = 0x33;
    parameter[8] = 0x50;
    parameter[9] = 0x36;
    parameter[10] = 0x13;
    parameter[11] = 0x14;
    parameter[12] = 0x29;
    parameter[13] = 0x2D;
    ST7789H2_WriteReg(hlcdc, ST7789H2_PV_GAMMA_CTRL, parameter, 14);

    /* Negative Voltage Gamma Control */
    parameter[0] = 0xD0;
    parameter[1] = 0x08;
    parameter[2] = 0x10;
    parameter[3] = 0x08;
    parameter[4] = 0x06;
    parameter[5] = 0x06;
    parameter[6] = 0x39;
    parameter[7] = 0x44;
    parameter[8] = 0x51;
    parameter[9] = 0x0B;
    parameter[10] = 0x16;
    parameter[11] = 0x14;
    parameter[12] = 0x2F;
    parameter[13] = 0x31;
    ST7789H2_WriteReg(hlcdc, ST7789H2_NV_GAMMA_CTRL, parameter, 14);


#if 0

    /* Set Column address CASET */
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x00;
    parameter[3] = ST7789H2_LCD_PIXEL_WIDTH - 1;
    ST7789H2_WriteReg(hlcdc, ST7789H2_CASET, parameter, 4);
    /* Set Row address RASET */
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x00;
    parameter[3] = ST7789H2_LCD_PIXEL_HEIGHT - 1;
    ST7789H2_WriteReg(hlcdc, ST7789H2_RASET, parameter, 4);

    ST7789H2_WriteReg(hlcdc, ST7789H2_WRITE_RAM, (uint8_t *)NULL, 0);

    for (uint32_t i = 0; i < 240; i++)
    {
        for (uint32_t j = 0; j < 240; j++)
        {
            LCD_IO_WriteData16(0x0000);
        }
    }
#endif

    /* Set frame control*/
    parameter[0] = 0x01;
    parameter[1] = 0x0F;
    parameter[2] = 0x0F;
    ST7789H2_WriteReg(hlcdc, ST7789H2_FRAME_CTRL, parameter, 3);



    /* Display ON command */
    ST7789H2_DisplayOn(hlcdc);


    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    parameter[0] = 0x00;
    ST7789H2_WriteReg(hlcdc, ST7789H2_TEARING_EFFECT, parameter, 1);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ST7789H2_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    data = ST7789H2_ReadData(hlcdc, ST7789H2_LCD_ID, 3);

    return data;
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ST7789H2_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    ST7789H2_WriteReg(hlcdc, ST7789H2_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ST7789H2_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    ST7789H2_WriteReg(hlcdc, ST7789H2_DISPLAY_OFF, (uint8_t *)NULL, 0);
}


/**
 * @brief Set LCD & LCDC clip area
 * @param hlcdc LCD controller handle
 * @param Xpos0 - Clip area left coordinate, base on LCD top-left, same as below.
 * @param Ypos0 - Clip area top coordinate
 * @param Xpos1 - Clip area right coordinate
 * @param Ypos1 - Clip area bottom coordinate
 */
void ST7789H2_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];

    //Set LCDC clip area
    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    //Set LCD clip area
    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    ST7789H2_WriteReg(hlcdc, ST7789H2_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    ST7789H2_WriteReg(hlcdc, ST7789H2_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ST7789H2_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;

    /* Set Cursor */
    ST7789H2_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ST7789H2_WriteReg(hlcdc, ST7789H2_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

/**
 * @brief Send layer data to LCD
 * @param hlcdc LCD controller handle
 * @param RGBCode - Pointer to layer data
 * @param Xpos0 - Layer data left coordinate, base on LCD top-left, same as below.
 * @param Ypos0 - Layer data top coordinate
 * @param Xpos1 - Layer data right coordinate
 * @param Ypos1 - Layer data bottom coordinate
 */
void ST7789H2_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    //Set default layer data
    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    //Write datas to LCD register
    HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST7789H2_WRITE_RAM, 1);
}


/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ST7789H2_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ST7789H2_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ST7789H2_ReadMode(hlcdc, true);

    HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);

    ST7789H2_ReadMode(hlcdc, false);

    return rd_data;
}



uint32_t ST7789H2_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t   parameter[2];
    uint32_t c;
    uint32_t ret_v;

    parameter[0] = 0x66;
    ST7789H2_WriteReg(hlcdc, ST7789H2_COLOR_MODE, parameter, 1);

    ST7789H2_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    /*
        read ram need 8 dummy cycle, and it's result is 24bit color which format is:

        6bit red + 2bit dummy + 6bit green + 2bit dummy + 6bit blue + 2bit dummy

    */
    c =  ST7789H2_ReadData(hlcdc, ST7789H2_READ_RAM, 4);
    c >>= lcdc_int_cfg.cfg.spi.dummy_clock; //revert fixed dummy cycle

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        parameter[0] = 0x55;
        ret_v = (uint32_t)(((c >> 8) & 0xF800) | ((c >> 5) & 0x7E0) | ((c >> 3) & 0X1F));
        break;

    case LCDC_PIXEL_FORMAT_RGB666:
        parameter[0] = 0x66;
        ret_v = (uint32_t)(((c >> 6) & 0x3F000) | ((c >> 4) & 0xFC0) | ((c >> 2) & 0X3F));
        break;

    case LCDC_PIXEL_FORMAT_RGB888:
        /*
           pretend that st7789v can support RGB888,

           treated as RGB666 actually(6bit R + 2bit dummy + 6bit G + 2bit dummy + 6bit B + 2bit dummy )

           lcdc NOT support RGB666
        */
        /*
            st7789 NOT support RGB888：

            fill 2bit dummy bit with 2bit MSB of color
        */
        parameter[0] = 0x66;
        ret_v = (uint32_t)((c & 0xFCFCFC) | ((c >> 6) & 0x030303));
        break;


    default:
        RT_ASSERT(0);
        break;
    }

    //rt_kprintf("ST7789H2_ReadPixel %x -> %x\n",c, ret_v);

    ST7789H2_WriteReg(hlcdc, ST7789H2_COLOR_MODE, parameter, 1);

    return ret_v;
}


void ST7789H2_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    /*

    Control interface color format
    ‘011’ = 12bit/pixel ‘101’ = 16bit/pixel ‘110’ = 18bit/pixel ‘111’ = 16M truncated

    */
    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x55;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;

    /*
       pretend that st7789v can support RGB888,

       treated as RGB666 actually(6bit R + 2bit dummy + 6bit G + 2bit dummy + 6bit B + 2bit dummy )

       lcdc NOT support RGB666
    */
    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        /* Color mode 18bits/pixel */
        parameter[0] = 0x66;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
        break;


    default:
        RT_ASSERT(0);
        return; //unsupport
        break;

    }

    ST7789H2_WriteReg(hlcdc, ST7789H2_COLOR_MODE, parameter, 1);
    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

#define ST7789H2_BRIGHTNESS_MAX 127

void     ST7789H2_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((int)ST7789H2_BRIGHTNESS_MAX * br / 100);
    ST7789H2_WriteReg(hlcdc, ST7789H2_WBRIGHT, &bright, 1);
}

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
