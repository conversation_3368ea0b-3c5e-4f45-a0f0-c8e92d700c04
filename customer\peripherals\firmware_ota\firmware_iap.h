/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   firmware_iap.h
@Time    :   2025/05/08 16:17:35
*
**************************************************************************/

#ifndef _FIRMWARE_IAP_H_
#define _FIRMWARE_IAP_H_

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define TARGET_IMAGE_DIFF_NAME   "0:/udiff.bin"


bool firmware_program(const char *filename, uint32_t *addr);
void firmware_iap_jump_reboot(void);
uint32_t firmware_update_check(const char *filename);
uint32_t get_firmware_update_precent(void);
bool app_firmware_is_valid(void);
void boot_version_init(void);
uint32_t boot_version_read(void);
void flash_uuid_get(uint32_t *hv,uint32_t *lv);
void boot_make_app_invalid(void);
bool create_raw_firmware(void);

#ifdef __cplusplus
}
#endif

#endif
