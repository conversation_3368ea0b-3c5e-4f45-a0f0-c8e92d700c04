#include "mixo_hal.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "drv_spi.h"

#define MDC1A00_SPI_DEV_NAME "spi2"
#define MDC1A00_DEVICE_NAME "mdc1a00"
#define MDC1A00_SPI_CS_PIN  63

//波特率
#define MDC1A00_SPI_BAUDRATE       8000000       
#define MDC1A00_SPI_DATA_WIDTH     8


// SPI 设备配置参数
static struct rt_spi_device *spi_dev;
static struct rt_spi_bus_device *spi_bus;

/**
 * @brief  spi初始化
 * 
 * @param fun:引脚中断回调函数
 * 
 * @return int 
 */
int mixo_hal_spi_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct;
    // 查找SPI总线设备
    rt_err_t result;
    HAL_PIN_Set(PAD_PA77, SPI2_CLK, PIN_NOPULL, 1);             
    HAL_PIN_Set(PAD_PA66, SPI2_DO, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA65, SPI2_DI, PIN_NOPULL, 1);
	HAL_PIN_Set(PAD_PA63, SPI2_CS, PIN_NOPULL, 1);

    GPIO_InitStruct.Pin = 77;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);	

    GPIO_InitStruct.Pin = 66;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = 65;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = 63;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio1, &GPIO_InitStruct);


    if(spi_bus == RT_NULL){
        spi_bus = (struct rt_spi_bus_device*)rt_device_find(MDC1A00_SPI_DEV_NAME);
        if (spi_bus)
        {
            rt_kprintf("%s bus finded\n", MDC1A00_SPI_DEV_NAME);
            rt_hw_spi_device_attach(MDC1A00_SPI_DEV_NAME, MDC1A00_DEVICE_NAME);
        }
        else
        {
            rt_kprintf("%s bus not find \n", MDC1A00_SPI_DEV_NAME);
            return -1;
        }
    }

    if (spi_dev == RT_NULL){
        spi_dev = (struct rt_spi_device*)rt_device_find(MDC1A00_DEVICE_NAME);
    }
    if (spi_dev)
    {
        rt_device_open((rt_device_t)spi_dev, RT_DEVICE_FLAG_RDWR);
        struct rt_spi_configuration cfg;
        cfg.data_width = 8;
        cfg.max_hz = 8*1000*1000;
        cfg.mode = RT_SPI_MASTER | RT_SPI_MODE_0 | RT_SPI_MSB;// | RT_SPI_3WIRE;
        cfg.frameMode = RT_SPI_MOTO; //RT_SPI_TI;
        rt_spi_configure(spi_dev, &cfg);
    }

    return RT_EOK;
}

/**
 * @brief spi反初始化
 * @return 0 on success. otherwise error code.
 */
void mixo_hal_spi_deinit(void)
{
    // 卸载SPI设备
    rt_device_close((rt_device_t)spi_dev);

}

/**
 * @brief  spi读操作
 * 
 * @param addr 
 * @param buffer 
 * @param size_to_transfer 
 * @return int 
 */
int mixo_hal_spi_read(uint8_t addr, uint8_t* buffer,
                      uint32_t size_to_transfer)
{
    struct rt_spi_message msg1, msg2;
    msg1.send_buf = &addr;
    msg1.recv_buf = RT_NULL;
    msg1.length = 1;
    msg1.cs_take = 1;
    msg1.cs_release = 0;
    msg1.next = &msg2;

    msg2.send_buf = RT_NULL;
    msg2.recv_buf = buffer;
    msg2.length = 1;
    msg2.cs_take = 0;
    msg2.cs_release = 1;
    msg2.next = RT_NULL;

    rt_spi_transfer_message(spi_dev, &msg1);
    return MIXO_HAL_OK;
}

/**
 * @brief  spi写操作
 * 
 * @param buffer 
 * @param size_to_transfer 
 * @return int 
 */
int mixo_hal_spi_write(uint8_t* buffer,
                       uint32_t size_to_transfer)
{
    rt_size_t ret = rt_spi_send(spi_dev, buffer, size_to_transfer);
    if (ret != size_to_transfer)
    {
        rt_kprintf("spi send failed\n");
        return MIXO_HAL_EIO;
    }
	return MIXO_HAL_OK;
}

