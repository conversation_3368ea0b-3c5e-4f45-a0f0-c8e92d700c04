import os
import rtconfig

# Check SDK 
SIFLI_SDK = os.getenv('SIFLI_SDK')
if not SIFLI_SDK:
    print("Please run set_env.bat in root folder of SIFLI SDK to set environment.")
    exit()
from building import *

# Set default compile options
SifliEnv()
os.system("prebuild.bat")

################################## change rtconfig.xxx to customize build ########################################
# print (rtconfig.OUTPUT_DIR)

OUTPUT_DIR=rtconfig.OUTPUT_DIR
TARGET = OUTPUT_DIR + rtconfig.TARGET_NAME + '.' + rtconfig.TARGET_EXT
env = Environment(tools = ['mingw'],
    AS = rtconfig.AS, ASFLAGS = rtconfig.AFLAGS,
    CC = rtconfig.CC, CCFLAGS = rtconfig.CFLAGS,
    AR = rtconfig.AR, ARFLAGS = '-rc',
    LINK = rtconfig.LINK, LINKFLAGS = rtconfig.LFLAGS)
env.PrependENVPath('PATH', rtconfig.EXEC_PATH)

# prepare building environment
objs = PrepareBuilding(env)

# make a building
DoBuilding(TARGET, objs)
