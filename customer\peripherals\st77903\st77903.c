/**
  ******************************************************************************
  * @file   st77903.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for st77903 LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st77903.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST77903
  * @brief This file provides a set of functions needed to drive the
  *        ST77903 LCD.
  * @{
  */

/** @defgroup ST77903_Private_TypesDefinitions
  * @{
  */

#ifdef ROW_OFFSET_PLUS
    #define ROW_OFFSET  (ROW_OFFSET_PLUS)
#else
    #define ROW_OFFSET  (0)
#endif


/**
  * @}
  */

/** @defgroup ST77903_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ST77903_Private_Macros
  * @{
  */


#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

void ST77903_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
uint32_t ST77903_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);


/**
  * @}
  */

/** @defgroup ST77903_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ST77903_drv =
{
    ST77903_Init,
    ST77903_ReadID,
    ST77903_DisplayOn,
    ST77903_DisplayOff,

    ST77903_SetRegion,
    ST77903_WritePixel,
    ST77903_WriteMultiplePixels,

    ST77903_ReadPixel,

    ST77903_SetColorMode,
    ST77903_SetBrightness,
    NULL,
    NULL
};

#define QAD_SPI_ITF LCDC_INTF_SPI_DCX_4DATA

static LCDC_InitTypeDef lcdc_int_cfg =
{
    .lcd_itf = QAD_SPI_ITF,
    .freq = 48000000,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,

    .cfg = {
        .spi = {
            .dummy_clock = 0,
            .syn_mode = HAL_LCDC_SYNC_DISABLE,
            .vsyn_polarity = 0,
            //default_vbp=2, frame rate=82, delay=115us,
            //TODO: use us to define delay instead of cycle, delay_cycle=115*48
            .vsyn_delay_us = 100,
            .hsyn_num = 100,
        },
    },

};


#define MAX_CMD_LEN 16
#if 1//defined(LCD_USING_ED_LB55SPI17601_QADSPI_SS6600)
static const uint8_t lcd_init_cmds[][MAX_CMD_LEN] =
{
    { 0xf0, 1,  0xc3},
    { 0xf0, 1,  0x96},
    { 0xf0, 1,  0xa5},
    //{ 0xe9, 1,  0x20},
    { 0xe7, 4,  0x80, 0x77, 0x1f, 0xcc},
    { 0xc1, 4,  0x11, 0x09, 0xaa, 0x11},
    { 0xc2, 4,  0x11, 0x09, 0xaa, 0x11},
    { 0xc3, 4,  0x44, 0x03, 0x33, 0x04},
    { 0xc4, 4,  0x44, 0x03, 0x33, 0x04},
    { 0xc5, 1,  0x4d},
    { 0xd6, 1,  0x00},
    { 0xd7, 1,  0x00},
    { 0xe0, 14, 0xf0, 0x0d, 0x14, 0x0a, 0x09, 0x06, 0x3a, 0x43, 0x51, 0x07, 0x14, 0x15, 0x30, 0x35},
    { 0xe1, 14, 0xf0, 0x0d, 0x13, 0x0b, 0x09, 0x07, 0x3b, 0x43, 0x50, 0x08, 0x14, 0x15, 0x30, 0x35},
    { 0xe5, 14, 0xb9, 0xf5, 0xb5, 0x55, 0x22, 0x25, 0x10, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22},
    { 0xe6, 14, 0xb9, 0xf5, 0xb5, 0x55, 0x22, 0x25, 0x10, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22},
    { 0xe7, 1,  0x80},
    { 0xec, 6,  0x00, 0x55, 0x00, 0x00, 0x00, 0x88},
    { 0x36, 1,  0x08},
    { 0x3a, 1,  0x05},  //0x07-rgb888  05-rgb565
    { 0xb1, 2,  0xfe, 0xdf},
    { 0xb2, 1,  0x09},
    { 0xb3, 1,  0x01},
    { 0xb4, 1,  0x01},
    { 0xb5, 4,  0x00, 0x08, 0x00, 0x08},
    { 0xb6, 2,  0xbd, 0x27},
    { 0xa4, 2,  0xc0, 0x6b},
    { 0xa5, 9,  0x11, 0x53, 0x00, 0x00, 0x20, 0x15, 0x2a, 0xba, 0x02},
    { 0xa6, 9,  0x11, 0x53, 0x00, 0x00, 0x20, 0x15, 0x2a, 0xba, 0x02},
    { 0xba, 7,  0x58, 0x0a, 0x34, 0x10, 0x22, 0x01, 0x00},
    { 0xbb, 8,  0x00, 0x33, 0x00, 0x2c, 0x83, 0x07, 0x18, 0x00},
    { 0xbc, 8,  0x00, 0x33, 0x00, 0x2c, 0x83, 0x07, 0x18, 0x00},
    { 0xbd, 11, 0x21, 0x12, 0xff, 0xff, 0x67, 0x58, 0x85, 0x76, 0xab, 0xff, 0x03},
    { 0x35, 1,  0x00},
    { 0xed, 1,  0xc3},
    { 0xd9, 1,  0x22},
    { 0x36, 1,  0x0c}
};

//Display  color bar to check LCD ok.
static const uint8_t bist_cmds[][MAX_CMD_LEN] =
{
    {0xf0, 1,  0xa5},
    {0xb0, 1,  0xa5},
    {0xcc, 9, 0x40, 0x00, 0x3f, 0x01, 0x06, 0x06, 0x55, 0x55, 0x00},
};
#else
static const uint8_t lcd_init_cmds[][MAX_CMD_LEN] =
{
    { 0xf0, 1,  0xc3},
    { 0xf0, 1,  0x96},
    { 0xf0, 1,  0xa5},
    { 0xe9, 1,  0x20},
    { 0xe7, 4,  0x80, 0x77, 0x1f, 0xcc},
    { 0xc1, 4,  0x77, 0x07, 0xcf, 0x16},
    { 0xc2, 4,  0x77, 0x07, 0xcf, 0x16},
    { 0xc3, 4,  0x22, 0x02, 0x22, 0x04},
    { 0xc4, 4,  0x22, 0x02, 0x22, 0x04},
    { 0xc5, 1,  0xed},
    { 0xe0, 14, 0x87, 0x09, 0x0c, 0x06, 0x05, 0x03, 0x29, 0x32, 0x49, 0x0f, 0x1b, 0x17, 0x2a, 0x2f},
    { 0xe1, 14, 0x87, 0x09, 0x0c, 0x06, 0x05, 0x03, 0x29, 0x32, 0x49, 0x0f, 0x1b, 0x17, 0x2a, 0x2f},
    { 0xe5, 14, 0xbe, 0xf5, 0xb1, 0x22, 0x22, 0x25, 0x10, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22},
    { 0xe6, 14, 0xbe, 0xf5, 0xb1, 0x22, 0x22, 0x25, 0x10, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22},
    { 0xec, 2,  0x40, 0x03},
    { 0x36, 1,  0x0c},
    { 0x3a, 1,  0x05},  //0x07-rgb888  05-rgb565
    { 0xb2, 1,  0x00},
    { 0xb3, 1,  0x01},
    { 0xb4, 1,  0x00},
    { 0xb5, 4,  0x00, 0x08, 0x00, 0x08},
    { 0xa5, 9,  0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x2a, 0x8a, 0x02},
    { 0xa6, 9,  0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x2a, 0x8a, 0x02},
    { 0xba, 7,  0x0a, 0x5a, 0x23, 0x10, 0x25, 0x02, 0x00},
    { 0xbb, 8,  0x00, 0x30, 0x00, 0x2c, 0x82, 0x87, 0x18, 0x00},
    { 0xbc, 8,  0x00, 0x30, 0x00, 0x2c, 0x82, 0x87, 0x18, 0x00},
    { 0xbd, 11, 0xa1, 0xb2, 0x2b, 0x1a, 0x56, 0x43, 0x34, 0x65, 0xff, 0xff, 0x0f}



};

//Display  color bar to check LCD ok.
static const uint8_t bist_cmds[][MAX_CMD_LEN] =
{
    {0xb0, 1,  0xa5},
    {0xcc, 9, 0x40, 0x00, 0x3f, 0x00, 0x14, 0x14, 0x20, 0x20, 0x03},
};

#endif


/*

    1line data   ----(delay > 40us)---------------> 1line data  (Vporch line same too)

    1 frame      ------(delay > 1ms)--------------->   1 frame

*/
static RAMLESS_CFG_T config =
{
    .frame_cmd = 0xD8006100,
    .frame_gap = 1100,     //In microsecond
    .porch_cmd = 0xD8006000,
    .line_cmd = 0xDE006000,
#if 1//defined(LCD_USING_ED_LB55_77903_LHZL_320X320_QADSPI_LB551)
    .porch_line_gap = 40, //55,      //In microsecond
    .data_line_gap  = 11, //55,     //In microsecond
    .front_porch = 8,   //Front porch line
    .back_porch = 8,    //Back porch line
#else
    .porch_line_gap = 40,      //In microsecond
    .data_line_gap  = 5,     //In microsecond
    .front_porch = 8,   //Front porch line
    .back_porch = 8,    //Back porch line
#endif
    .backlight_pwm = 0,
    .backlight_polarity = 1,

};

LCD_DRIVER_EXPORT(st77903, ST77903_ID, &lcdc_int_cfg,
                  &ST77903_drv,
                  ST77903_LCD_PIXEL_WIDTH,
                  ST77903_LCD_PIXEL_HEIGHT,
                  1);
/**
  * @}
  */

/** @defgroup ST77903_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ST77903_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ST77903_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 4000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }
    }
}

/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ST77903_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];

    /* Initialize ST77903 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(30);
    BSP_LCD_Reset(1);

    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);


    for (int i = 0; i < sizeof(lcd_init_cmds) / MAX_CMD_LEN; i++)
    {
        //rt_kprintf("write %d,cmd=0x%x,len=%d\n",i,(int)lcd_init_cmds[i][0], (int)lcd_init_cmds[i][1]);
        //HAL_DBG_print_data((char*)&(lcd_init_cmds[i][2]),0,(int)lcd_init_cmds[i][1]);
        ST77903_WriteReg(hlcdc, lcd_init_cmds[i][0], (uint8_t *)&lcd_init_cmds[i][2], lcd_init_cmds[i][1]);

        //__asm("B .");
    }


    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    parameter[0] = 0x00;
    ST77903_WriteReg(hlcdc, ST77903_TEARING_EFFECT, parameter, 1);


    ST77903_WriteReg(hlcdc, ST77903_DISPLAY_INVERSION, (uint8_t *)NULL, 0);
    ST77903_WriteReg(hlcdc, ST77903_SLEEP_OUT, (uint8_t *)NULL, 0);
    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);

    /* Display ON command */
    ST77903_WriteReg(hlcdc, ST77903_DISPLAY_ON, (uint8_t *)NULL, 0);
    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);



#if 0
    for (int i = 0; i < sizeof(bist_cmds) / MAX_CMD_LEN; i++)
    {
        ST77903_WriteReg(hlcdc, bist_cmds[i][0], (uint8_t *)&bist_cmds[i][2], bist_cmds[i][1]);

    }
    __asm("B .");
#endif /* 1 */

    {
        //Calculate data line gap
        uint32_t cycle_per_us = lcdc_int_cfg.freq / 1000000;
        uint32_t data_cost_us = ((LCD_HOR_RES_MAX * 4 /*RGB565*/) + 32 /*32bit cmd*/) / cycle_per_us;

        config.data_line_gap = (data_cost_us > 41) ? 0 : (41 - data_cost_us);
    }

    HAL_LCDC_SetROIArea(hlcdc, 0, 0, LCD_HOR_RES_MAX - 1, LCD_VER_RES_MAX - 1);

    {
        uint32_t data;

        data = ST77903_ReadData(hlcdc, ST77903_LCD_ID, 4);
        DEBUG_PRINTF("ST77903_ReadID 0x%x \n", data);


        data = ST77903_ReadData(hlcdc, ST77903_POWER_MODE, 4);
        DEBUG_PRINTF("ST77903_ReadPowerMode 0x%x \n", data);
    }
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ST77903_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    data = ST77903_ReadData(hlcdc, ST77903_LCD_ID, 4);
    DEBUG_PRINTF("ST77903_ReadID 0x%x \n", data);
    data = ((data << 1) >> 8) & 0xFFFFFF;

    return ST77903_ID;
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ST77903_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    //ST77903_WriteReg(hlcdc, ST77903_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ST77903_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    //ST77903_WriteReg(hlcdc, ST77903_DISPLAY_OFF, (uint8_t *)NULL, 0);

    HAL_LCDC_RAMLESS_Stop(hlcdc);
}

void ST77903_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    /*
        0x2A & 0X2B is invalid for ST77903
    */
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ST77903_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;

    /* Set Cursor */
    ST77903_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ST77903_WriteReg(hlcdc, ST77903_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

#if 0

void ST77903_WriteMultiplePixels_mcu(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;
    if ((Xpos0 >= ST77903_LCD_PIXEL_WIDTH) || (Ypos0 >= ST77903_LCD_PIXEL_HEIGHT)
            || (Xpos1 >= ST77903_LCD_PIXEL_WIDTH) || (Ypos1 >= ST77903_LCD_PIXEL_HEIGHT))
    {
        return;
    }

    if ((Xpos0 > Xpos1) || (Ypos0 > Ypos1))
    {
        return;
    }
    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);


        while (1)
        {
            /*

                1line data   ----(must > 40us, include Vporch line)------> 1line data

                1 frame      ------(must > 1ms)--------------->   1 frame

            */
            HAL_LCDC_WriteU32Reg(hlcdc, 0xDE006100, 0, 0);
            HAL_Delay_us(40); //Must delay 40us

            for (uint32_t back_porch = 7; back_porch > 0; back_porch--)
            {
                HAL_LCDC_WriteU32Reg(hlcdc, 0xDE006000, 0, 0);
                HAL_Delay_us(40);//Must delay 40us
            }

            for (uint16_t row = Ypos0; row < Ypos1; row++)
            {
                HAL_LCDC_SetROIArea(hlcdc, Xpos0, row, Xpos1, row);
                HAL_LCDC_SendLayerData2Reg_IT(hlcdc, 0xDE006000, 4);
                //Must delay 40us
            }


            for (uint32_t front_porch = 8; front_porch > 0; front_porch--)
            {
                HAL_LCDC_WriteU32Reg(hlcdc, 0xDE006000, 0, 0);
                HAL_Delay_us(40);  //Must delay 40us
            }

            LCD_DRIVER_DELAY_MS(1); //Must delay 1ms
        }

    }
    else
    {
        HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST77903_WRITE_RAM, 1);
    }

}
#endif

/*
void HAL_DBG_printf(const char *fmt, ...)
{
    va_list args;
    static char rt_log_buf[128];
    extern void rt_kputs(const char *str);

    va_start(args, fmt);
    rt_vsnprintf(rt_log_buf, sizeof(rt_log_buf) - 1, fmt, args);
    rt_kputs(rt_log_buf);
    rt_kputs("\r\n");
    va_end(args);
}
*/
//use ptc
void ST77903_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    //RT_ASSERT((Xpos1 - Xpos0 + 1) == ST77903_LCD_PIXEL_WIDTH);

    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        rt_base_t level = rt_hw_interrupt_disable(); //In case RAMLESS FSM is running
        HAL_LCDC_RAMLESS_UpdateData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
        HAL_LCDC_RAMLESS_Start(hlcdc, &config);
        rt_hw_interrupt_enable(level);

    }
    else
    {
        HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST77903_WRITE_RAM, 1);
    }

}



/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ST77903_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        uint32_t cmd;


        cmd = (0xDE << 24) | (LCD_Reg << 8);

        HAL_LCDC_WriteU32Reg(hlcdc, cmd, Parameters, NbParameters);
    }
    else
    {
        HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
    }

}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ST77903_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ST77903_ReadMode(hlcdc, true);

    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        HAL_LCDC_ReadU32Reg(hlcdc, ((0xDD << 24) | (RegValue << 8)), (uint8_t *)&rd_data, ReadSize);
    }
    else
    {
        HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    }

    ST77903_ReadMode(hlcdc, false);

    return rd_data;
}



uint32_t ST77903_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    return 0;
}


void ST77903_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{

}

void     ST77903_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{

    LOG_I("Set lcdlight %d", br);
    uint8_t bright = (uint8_t)((uint16_t)UINT8_MAX * br / 100);
    //ST77903_WriteReg(hlcdc, ST77903_WBRIGHT, &br, 1);

    rt_device_t device = rt_device_find("lcdlight");
    if (device)
    {
        rt_err_t err = rt_device_open(device, RT_DEVICE_OFLAG_RDWR);
        uint8_t val = br;
        rt_device_write(device, 0, &val, 1);
        rt_device_close(device);
    }
    else
    {
        LOG_E("Can't find device lcdlight!");
    }

}

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
