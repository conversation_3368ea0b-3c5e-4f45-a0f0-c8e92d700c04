
/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   hw_port.c
@Time    :   2025/02/13 20:35:06
@Brief   :   SE HSC32I1硬件接口模块
@Details :
*
************************************************************/

#include "hw_port.h"
#include "bsp_board_bus.h"
#include "version_check_api.h"

static struct rt_i2c_bus_device *se_i2c_bus = NULL;
static unsigned char *i2c_buf = NULL;

unsigned char * IIC_Master_Init(void)
{
    se_i2c_bus = (struct rt_i2c_bus_device *)rt_device_find(SE_I2C_NAME);
    int ret = 0;

    if (RT_Device_Class_I2CBUS != se_i2c_bus->parent.type)
    {
        se_i2c_bus = NULL;
    }

    if (se_i2c_bus)
    {
#ifndef USE_BSP_BORAD_BUS_INIT
        qw_special_pin_set(I2C1_SCL_PIN, I2C1_SCL, PIN_PULLUP);
        qw_special_pin_set(I2C1_SDA_PIN, I2C1_SDA, PIN_PULLUP);
        ret = rt_device_open((rt_device_t)se_i2c_bus, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
        struct rt_i2c_configuration configuration =
        {
            .mode = 0,
            .addr = 0,
            .timeout = 500,
            .max_hz  = 100000,
        };
        ret = rt_i2c_configure(se_i2c_bus, &configuration);
#else
        bsp_i2c1_bus_init();
#endif
    }
    else
    {
        HW_PORT_E("bus not find\n");
        return NULL;
    }
    if (!i2c_buf)
    {
        i2c_buf = rt_calloc(1, 2048);
        RT_ASSERT(i2c_buf);
    }

    return i2c_buf;
}

void IIC_Master_Send(unsigned char byAddr, unsigned char *pData, unsigned short wLen)
{
    rt_int8_t ret = 0;

    ret = rt_i2c_master_send(se_i2c_bus, byAddr >> 1, 0, pData, wLen);
    if (ret != wLen)
        HW_PORT_E("IIC_Master_Send err");
}
void IIC_Master_Receive(unsigned char byAddr, unsigned char *pData, unsigned short wLen)
{
    rt_err_t ret = 0;

    ret = rt_i2c_master_recv(se_i2c_bus, (rt_uint16_t)(byAddr >> 1), 0, pData, (rt_uint32_t)wLen);
    if (ret != wLen)
        HW_PORT_E("IIC_Master_Receive err");
}

void IIC_Master_DeInit(void)
{
    if (se_i2c_bus)
    {
#ifndef USE_BSP_BORAD_BUS_INIT
        rt_err_t ret = 0;
        ret = rt_device_close((rt_device_t)se_i2c_bus);
        //rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_INPUT);//设置输入
        //rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_INPUT);//设置输入
        //qw_gpio_set(I2C1_SCL_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
        //qw_gpio_set(I2C1_SDA_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
#else
        bsp_i2c1_bus_uninit();
#endif
        se_i2c_bus = RT_NULL;
    }
}

void Delay_Ms(unsigned char byMilliSec)
{
    HAL_Delay(byMilliSec);
}

void Set_GPIO_State(unsigned char byState)
{
    int pin = (hw_version_get() == HARDWARE_VERSION_A3) ? ZFB_RST_PIN : ZFB_RST_PIN_A4;
    BSP_GPIO_Set(pin , byState, 1);
}

int HS_Reset(void)
{
    // 硬复位
    Set_GPIO_State(0);
    HAL_Delay(2);
    Set_GPIO_State(1);
    HAL_Delay(5);
    return 0;
}

