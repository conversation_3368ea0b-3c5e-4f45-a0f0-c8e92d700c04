/**
 * @file ZPA4756.c
 * <AUTHOR>
 * @brief 
 * @version 0.1
 * @date 2024-04-15
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#include "ZPA4756.h"

#define DRV_DEBUG
#define LOG_TAG              "drv.zpa4756"
#define ZPA4756_I2C_BUS      "i2c4"
#include <drv_log.h>




static struct rt_i2c_bus_device *zpa4756_bus= RT_NULL;

void init_continuous_mode(void);
/**
 * @brief  ZPA4756的写寄存器函数
 * 
 * @param address 
 * @param tx_cmd 
 * @return uint8_t 
 */
static uint8_t zpa4756_write_reg(uint8_t address, uint8_t tx_cmd)
{
    struct rt_i2c_msg msgs[1];
    uint8_t value[2];
    uint32_t res;

    if (zpa4756_bus) {

        value[0] = address;
        value[1] = tx_cmd;

        msgs[0].addr =ZPA4756_ADDRESS;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = value;
        msgs[0].len = 2;

            
        if (rt_i2c_transfer(zpa4756_bus, msgs, 1) == 1)
        {
            return RT_EOK;
        }
    }


    return RT_ERROR;
}


//ZPA4756硬件init
static int zpa4756_hw_init(void)
{
	  GPIO_InitTypeDef GPIO_InitStruct;
    //上电使能

    //中断引脚初始化
	
    return 0;
}

INIT_DEVICE_EXPORT(zpa4756_hw_init);

/**
 * @brief  ZPA4756的读取寄存器函数
 * 
 * @param address 
 * @param rx_buffer 
 * @param len 
 * @return uint8_t 
 */
static int zpa4756_read_reg(uint8_t address, uint8_t *rx_buffer, const uint8_t len)
{
    uint32_t res;
    if (zpa4756_bus)
    {
        // LOG_I("I2c mread dev 0x%x, addr 0x%x size %d\n", i2c_add, reg_add, num);
        res = rt_i2c_mem_read(zpa4756_bus, ZPA4756_ADDRESS, address, 8, rx_buffer, len);
        // for(uint8_t i=0;i<0xff;i++)
        // {
        //     res = rt_i2c_mem_read(zpa4756_bus, i, address, 8, rx_buffer, len);
        //     rt_kprintf("addr 0x%x, data %d\r\n",i,rx_buffer[0]);
        // }
        if (res <= 0)
        {
            // LOG_I("I2C_MultiRead_Reg fail %d\n", res);
        }
    }
		return 0;
}

/**
 * @brief ZPA4756的初始化函数
 * 
 * @param void
 * @return int32_t 
 */
int32_t zpa4756_init(void)
{
	
    /* get i2c bus device */
    zpa4756_bus = rt_i2c_bus_device_find(ZPA4756_I2C_BUS);
    if (zpa4756_bus)
    {
        LOG_D("Find i2c bus device %s\n",ZPA4756_I2C_BUS);
		rt_device_open(&(zpa4756_bus->parent), RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_RX | RT_DEVICE_FLAG_INT_TX);
        struct rt_i2c_configuration configuration = 
        {
            .mode = 0,
            .addr = 0,
            .timeout = 5000,
            .max_hz  = 400000,
        };
        rt_i2c_configure(zpa4756_bus, &configuration);
    }
    else
    {
        LOG_E("Can not found i2c bus %s, init fail\n",ZPA4756_I2C_BUS);
        return -1;
    }
		init_continuous_mode();
    return 0;
}


/*****************************************************************
Initialize for Continuous mode.
If you want to change a ODR, please refer to the command table(table26).
******************************************************************/
void init_continuous_mode(void) 
{
  zpa4756_write_reg(CTR_REG0, 0B00000100);        //Device Enable
  rt_thread_mdelay(5);  
  zpa4756_write_reg(CTR_REG2, 0B00000100);        //SW reset.
  rt_thread_mdelay(5);                                     
  //ODR setting:32S/s. Plesse change a below data of register.
  zpa4756_write_reg(OPTN_REG0, 0x89);       //OPTN_REG0:0x89
  zpa4756_write_reg(OPTN_REG1, 0x02);       //OPTN_REG1:0x02
  zpa4756_write_reg(CTR_REG3, 0xC0);        //CTR_REG3:0xC0
  zpa4756_write_reg(RES_CONF, 0);        //RES_CONF:0x00. AVGP&AVGT setting.
//   zpa4756_write_reg(ARB_CTRL, 0x20);        //ARB_CTRL:0x20.
  //Continuous mode start.
  zpa4756_write_reg(CTR_REG0, 0B00000110);        //Continuous mode ENABLE=1 ENABLE_MEAS=1
}

/*****************************************************************
Initialize for Oneshot mode.
If you want to change a ODR, please refer to the command table(table27).
******************************************************************/
void init_lowpower_mode(void) {
  zpa4756_write_reg(CTR_REG0, 0B00000100);        //Device Enable
  rt_thread_mdelay(5);
  zpa4756_write_reg( CTR_REG2, 0B00000100);        //SW reset.
  rt_thread_mdelay(5);
  //ODR setting:7.58S/s. Plesse change a below data of register.
  zpa4756_write_reg(OPTN_REG0, 0x00);       //OPTN_REG0:0x87
  zpa4756_write_reg(OPTN_REG1, 0x00);       //OPTN_REG1:0x02
  zpa4756_write_reg(CTR_REG3, 0x00);        //CTR_REG3:0xC0
  //Oneshot mode start.
}


int32_t zpa4756_deinit(void)
{
    init_lowpower_mode();
	
	return 0;
}
/**
 * @brief ZPA4756的id检查函数
 * 
 * @param void
 * @return uint8_t 
 */
uint8_t zpa4756_checkID(void)
{
    uint8_t whoami = 0;
	zpa4756_read_reg(DEVICE_ID,&whoami,1);
    rt_kprintf("whoami = %d\n",whoami);
	if(ZPA4756_PART_ID == whoami)return true;
	return false;
}



/**
 * @brief ZPA4756的读取结果函数
 * 
 * @param bmp: 气压值
 * @param temperature: 温度
 * @return int 
 */
int32_t zpa4756_data_read(int32_t *bmp, int32_t *temperature)
{
    double press;
    double temp;
		uint8_t data[5] = {0};


    zpa4756_read_reg(TEMP_OUT_L, &data[0], 1);
    zpa4756_read_reg(TEMP_OUT_H, &data[1], 1);
    //rt_kprintf("TEMP_OUT_L = %d, TEMP_OUT_H = %d\n", data[0], data[1]);
		temp = (data[1] * 256.0 + data[0]) * 0.0078125 - 273.0;
    *temperature = (int32_t)(temp * 100);
	
    memset(data, 0, sizeof(data));

		zpa4756_read_reg(PRESS_OUT_XL, &data[0], 1);
    zpa4756_read_reg(PRESS_OUT_L, &data[1], 1);
    zpa4756_read_reg(PRESS_OUT_H, &data[2], 1);

	 // 组合数据
    int32_t pressure_raw = (int32_t)data[2] << 16 | (int32_t)data[1] << 8 | data[0];
    // 处理2的补码
    if (pressure_raw & 0x00800000) {
        pressure_raw |= 0xFF000000; // 扩展为32位2的补码
    }
		press = pressure_raw / 64.0;

    *bmp = (int32_t)(press*100);

    return 0;
}


/**
 * @brief ZPA4756的模式切换函数
 * 
 * @param mode
 * @return int 
 */
int32_t zpa4756_switch_powermode(uint8_t power_mode)
{
    if (power_mode ==ZPA4756_ONESHOT_MODE) //
    {
        rt_kprintf("[zpa4756_switch_powermode] ONESHOT_MODE\r\n");
        //Init_OneshotMode();
        init_lowpower_mode();
    }
    else if (power_mode ==ZPA4756_CONTINUOUS_MODE) 
    {
		rt_kprintf("[zpa4756_switch_powermode] CONTINUOUS_MODE\r\n");
        init_continuous_mode();
    }
    else
    {
        return RT_ERROR; // Invalid mode
    }
    return RT_EOK;
}


//#define ZPA4756_FUNC_TEST
#ifdef ZPA4756_FUNC_TEST
int cmd_zpa4756(int argc, char *argv[])
{    
    uint16_t als_data = 0;
	int mode = 0;
    int ret = 0;
    if (argc >= 2)
    {
        if (strcmp(argv[1], "init") == 0)
        {
            ret = zpa4756_init();
            if (ret == 0)
            {
                rt_kprintf("ZPA4756 init success\n");
            }
            else
            {
                rt_kprintf("ZPA4756 init fail\n");
            }
        }
        else if (strcmp(argv[1], "checkid") == 0)
        {
            ret = zpa4756_checkID();
            if (ret == 1)
            {
                rt_kprintf("ZPA4756 check id success\n");
            }
            else
            {
                rt_kprintf("ZPA4756 check id fail\n");
            }
        }
        else if (strcmp(argv[1], "read") == 0)
        {
            int32_t bmp = 0;
            int32_t temperature = 0;
            ret = zpa4756_data_read(&bmp, &temperature);
            if (ret == SUCCEED)
            {
                rt_kprintf("bmp = %d, temperature = %d\n", bmp, temperature);
            }
            else
            {
                rt_kprintf("ZPA4756 read fail\n");
            }
        }
        else if (strcmp(argv[1], "mode") == 0)
        {
            if (argc >= 3)
            {
                mode = atoi(argv[2]);
                ret = zpa4756_switch_powermode(mode);
                if (ret == RT_EOK)
                {
                    rt_kprintf("ZPA4756 switch mode success\n");
                }
                else
                {
                    rt_kprintf("ZPA4756 switch mode fail\n");
                }
            }
        }
        //写寄存器
        else if (strcmp(argv[1], "write") == 0)
        {
            if (argc >= 4)
            {
                uint8_t address = atoi(argv[2]);
                uint8_t tx_cmd = atoi(argv[3]);
                ret = zpa4756_write_reg(address, tx_cmd);
                if (ret == RT_EOK)
                {
                    rt_kprintf("ZPA4756 write reg success\n");
                }
                else
                {
                    rt_kprintf("ZPA4756 write reg fail\n");
                }
            }
        }
        else
        {
            rt_kprintf("Invalid command\n");
        }
    }
    else
    {
        rt_kprintf("Invalid command\n");
    }
    return 0;
}

FINSH_FUNCTION_EXPORT(cmd_zpa4756, zpa4756 function test);
MSH_CMD_EXPORT(cmd_zpa4756, zpa4756 function test);
#endif

/**
 * 命令行进行测试应该发送这些
 * cmd_zpa4756 init   // 初始化
 * cmd_zpa4756 checkid  // 检查id
 * cmd_zpa4756 read  // 读取数据
 * cmd_zpa4756 mode 0  // 0:单次采集 1:连续采集
 * cmd_zpa4756 write 0x00 0x00  // 写寄存器
 * cmd_zpa4756 read 0x00  // 读寄存器
 */
