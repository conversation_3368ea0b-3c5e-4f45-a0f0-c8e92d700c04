/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : swuart.h
*<AUTHOR> 
*@Date : 2025/01/15
*@Description : Software uart driver header file
************************************************************************/
#ifndef SWUART_H
#define SWUART_H

#include "bsp_board.h"
#include <rtthread.h>
#include <rtdevice.h>
#include <rthw.h>


// 初始化软件UART
int swuart_init(void);

// 发送数据
int swuart_send(char data[], int len);

// 接收数据
int swuart_read(char *data, int *len);

// 格式化打印
void swprintf(const char *fmt, ...);

#endif // SWUART_H
