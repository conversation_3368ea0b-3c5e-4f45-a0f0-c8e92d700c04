#include <stdint.h>
#include <string.h>
#include "bf0_hal.h"
#include "mem_map.h"
#include "register.h"
#include "bf0_hal_patch.h"
#ifdef HAL_LCPU_PATCH_MODULE
const unsigned int g_lcpu_patch_list[] =
{
    0x50544348, 0x00000070, 0x0004D8A8, 0xBCAAF3C9,
    0x00011864, 0xB4D2F005, 0x00023504, 0x21011A40,
    0x00054FC8, 0xB922F3C2, 0x0001D0A4, 0xB8B6F3FA,
    0x000274C8, 0xF8842102, 0x0001801C, 0xB904F3FF,
    0x0004770C, 0xBD8EF3CF, 0x00013F7C, 0xB158F003,
    0x00054F2C, 0xBF00BD7C, 0x00054F70, 0x81FCE8BD,
    0x00055EAC, 0x01F4012C, 0x0005CEF8, 0x087808A0,
    0x00007708, 0xB594F00F,
};

const unsigned int g_lcpu_patch_bin[] =
{
    0xF7FA4801, 0xBF00BE59, 0x204147E8, 0xB872F7F9,
    0xB9C2F7FB, 0xBAA2F7F9, 0x68014802, 0x0101F041,
    0x47706001, 0x50000010, 0xB99EF7F9, 0xB918F7FA,
    0xB8B6F7F9, 0x00004770, 0x4A19B5BF, 0x4021F852,
    0xF894B35C, 0x1A0910B6, 0x0103F001, 0x6860180D,
    0x1B401A29, 0x4170F021, 0x4070F020, 0x6F00F1B1,
    0x4241BF88, 0xDB182903, 0xF43EA801, 0x9801FF71,
    0x30014A0C, 0xF0211B41, 0x42914170, 0x4068D807,
    0xD1060100, 0xF8BD8920, 0x42881008, 0x2001D901,
    0x4805E004, 0x31016801, 0x20006001, 0xBDB0B004,
    0x20414798, 0x07FFFFFE, 0x2041401C, 0x43F8E92D,
    0x8078F8DF, 0xEB004689, 0x46050140, 0xF8B84F1C,
    0x014C0026, 0x1241EB00, 0x8856443A, 0x5F80F5B6,
    0x4818D307, 0xF240A118, 0xF43D728F, 0xF8B8FE15,
    0x19E10026, 0x42E1F240, 0x49175A08, 0x1025F851,
    0x42916F09, 0xEA40D813, 0xEBA04006, 0xF0210109,
    0xF1B14170, 0xD90A6F00, 0x0000EBA9, 0x4070F020,
    0x480FB128, 0x9000F8C0, 0xE8BD2001, 0x480B83F8,
    0xF8802114, 0x2000129B, 0x83F8E8BD, 0x204157AC,
    0x20418052, 0x00412E50, 0x5F676572, 0x625F6D65,
    0x73635F74, 0x0000682E, 0x20414798, 0x204167D8,
    0x20414020, 0x21FA4802, 0x129BF880, 0x47702001,
    0x204167D8,
};
const unsigned int g_lcpu_patch_ram_bin[] =
{
    0xF000B580, 0xF002F94D, 0xF001FE01, 0x480AFAD3,
    0x6001490A, 0x490B480A, 0x480B6001, 0x6001490B,
    0x490C480B, 0x480C6001, 0x6001490C, 0xE8BDA00C,
    0xF43F4080, 0xBF00BB7F, 0x20414494, 0x00410491,
    0x20414424, 0x0041009D, 0x20414498, 0x00411391,
    0x20414480, 0x00412B29, 0x2041442C, 0x00410079,
    0x63746150, 0x6F682068, 0x69206B6F, 0x6174736E,
    0x2E2E6C6C, 0x0000000A, 0x2801B580, 0xB920D003,
    0x20004905, 0x4903E001, 0xF7F22001, 0x2000F7F1,
    0xBF00BD80, 0x00410465, 0x004102C1, 0xBF042803,
    0x47704809, 0xBF04283C, 0x4770480A, 0xBF042817,
    0x47704807, 0xBF042818, 0x47704804, 0xBF14280B,
    0x48052000, 0xBF004770, 0x00412E10, 0x00412EC4,
    0x00412E44, 0x00412E38, 0x00412E1C, 0xF001B580,
    0xF448FDE3, 0x4809FF1F, 0xF4416801, 0x60017180,
    0xF4416801, 0x60017100, 0xF4416801, 0x60016180,
    0xF4416801, 0x60016100, 0xBF00BD80, 0x50090400,
    0x490DB580, 0x0020F851, 0xF8D0B198, 0xB18000FC,
    0x284079C0, 0x2178D207, 0x0050EA01, 0xFA214909,
    0xB2C0F000, 0x4805BD80, 0xF2404905, 0xF44422F2,
    0x2000FEE3, 0xBF00BD80, 0x20414798, 0x00412E50,
    0x00412E80, 0x01010000, 0x4C58B5B0, 0xB15078A0,
    0x28057820, 0x2500D309, 0xF8214608, 0xF4035C06,
    0x2001F9CD, 0x2000E003, 0x1C45BDB0, 0x70252000,
    0xBF00BDB0, 0xF64FB5BC, 0x460C4085, 0xD0144283,
    0x4086F64F, 0xD0664283, 0x408AF64F, 0xD11E4283,
    0x5088F44F, 0xF64F2100, 0x2301428A, 0xFD9AF406,
    0x78204605, 0x2012B340, 0xF44FE051, 0x21005088,
    0x4285F64F, 0x25002301, 0xFD8CF406, 0x4A3B7821,
    0x0181EB02, 0xF8C16822, 0x70052001, 0x4926E041,
    0xF5B02000, 0xD03E7F84, 0x30085A0D, 0xD1F8429D,
    0xF8504408, 0xF1BCCC04, 0xD0340F00, 0x46214618,
    0xE8BDB002, 0x476040B0, 0x28027860, 0x2801D015,
    0x2800D01A, 0x2006D1CF, 0xF10D4C14, 0xF88D0107,
    0x48130007, 0x68034622, 0x47982001, 0x4811B188,
    0x68008881, 0x602080A1, 0x88E0E00B, 0x8088490B,
    0x0002F8D4, 0xE0046008, 0x78014808, 0x0155F081,
    0x48067001, 0x0119F100, 0xFC42F41B, 0x70282000,
    0xF4034628, 0x2000F953, 0xBF00BDBC, 0x20415CD8,
    0x204147DC, 0x0005A2B0, 0x0005A3C4, 0x1105F241,
    0xF2414A05, 0x42881309, 0xBF184904, 0x42982200,
    0x4611BF18, 0x47704608, 0x00410185, 0x00410159,
    0x21004806, 0x2A042200, 0xEB00D005, 0x32010382,
    0x1001F8C3, 0x2100E7F7, 0x47707001, 0x20414008,
    0xF7F7B580, 0x2001F751, 0x4080E8BD, 0xBC70F443,
    0xEB004A06, 0x8CD20040, 0x1040EB02, 0x5A834A04,
    0x03FFF023, 0x52814419, 0xBF004770, 0x204157AC,
    0x20418014, 0x28304904, 0xBF047008, 0x47704804,
    0x22024902, 0xBABAF401, 0x20414204, 0x0005B514,
    0x00412E30, 0x460AB580, 0x5088F44F, 0x230A2100,
    0xFCE0F406, 0x7101210C, 0xF8C04904, 0x49041006,
    0xF4036001, 0x2000F8EB, 0xBF00BD80, 0x02490A4C,
    0x02000C00, 0x4610B570, 0xF406460C, 0x2819FD93,
    0x2101D811, 0xF000FA01, 0x42084910, 0x7861D00B,
    0x46041D20, 0x010FF001, 0x88211E4E, 0xF7F5B2F0,
    0x2000F1A7, 0xF834BD70, 0xF8141F04, 0xF0000C03,
    0x1E46000F, 0x4628B2F5, 0xF16EF7F5, 0x46284601,
    0xFB0EF419, 0xD1E82800, 0xBF00E7EB, 0x03C00021,
    0xF640B570, 0x0B8D70FE, 0x020E4614, 0x0300EA01,
    0xF285FAB5, 0x2B0A2000, 0x2001BF38, 0x40020952,
    0x00FFF04F, 0x1D30BF1C, 0x260122FB, 0x3F91EBB6,
    0xB92DD00A, 0xD1032B80, 0x32FDF240, 0xE0062001,
    0x29FFB281, 0xE004D103, 0x32FDF240, 0x42A22002,
    0x2000D201, 0xF04FBD70, 0xFA5131FF, 0x2902F180,
    0xF7F4D202, 0xE004F77F, 0x2804B2C0, 0xF7F4D105,
    0xB140F25F, 0x4408490D, 0xA00DBD70, 0xF240A10D,
    0xF4441209, 0xF241FD71, 0x21001002, 0x2301221A,
    0xF4062501, 0x7005FC57, 0xF868F403, 0x21014620,
    0xFC10F406, 0xF8814906, 0xBD705056, 0x20418000,
    0x00535341, 0x5F696368, 0x63746170, 0x00632E68,
    0x20415918, 0x4C08B510, 0xF4066820, 0xB130FAFF,
    0x68014806, 0x47882001, 0x60202000, 0xE8BDBD10,
    0xF4064010, 0xBF00B975, 0x20416514, 0x20414430,
    0x2906B2C9, 0xE8DFD809, 0x0604F001, 0x080A0808,
    0xF001000C, 0xF000B829, 0x2000B9AD, 0xF0004770,
    0xF7FFBD47, 0x0000BEE3, 0x4607B5F8, 0x46154808,
    0x461C460E, 0x68024629, 0x60C4F44F, 0x46384790,
    0x462A4631, 0xF4074623, 0x2000F8A7, 0xBF00BDF8,
    0x20414450, 0x880CB510, 0xE8BDB11C, 0xF4074010,
    0x2000B967, 0x0000BD10, 0x4604B570, 0x88454895,
    0x00FFF025, 0x7F80F5B0, 0x4893D123, 0x7FC06800,
    0xD1272804, 0x20007821, 0xF092F7F5, 0x4606B310,
    0x782248B6, 0x44306861, 0xF27AF7F0, 0x0001F248,
    0x23062200, 0x2005EA80, 0xF241B281, 0xF4061007,
    0x7821FBD1, 0x80058086, 0xE8BD7081, 0xF4064070,
    0x4AABBC01, 0x46214628, 0x7354F44F, 0x4070E8BD,
    0xBCF0F444, 0xBF00BD70, 0x43F0E92D, 0x4604B085,
    0x5D00483C, 0x28043816, 0x4865D370, 0x2101460D,
    0x6024F850, 0x284F2000, 0xEB06D014, 0xF00002D0,
    0xF8920306, 0x40DA223C, 0x0203F002, 0xD1072A03,
    0xF00008C2, 0x5CAB0707, 0xF707FA01, 0x54AB43BB,
    0xE7E83001, 0xF4344628, 0xF506FED5, 0x4629780A,
    0x4640220A, 0xF271F7F0, 0xD0472800, 0x003CF896,
    0xD1132802, 0xF10D2000, 0xF10D0112, 0xF8AD0211,
    0xF88D0012, 0x46200011, 0xFAC6F40D, 0x0011F89D,
    0x1012F8BD, 0xFB003006, 0xE005F701, 0xF4134620,
    0xEB00FE1B, 0x00470040, 0xFF00F418, 0xA8014681,
    0xF40F4621, 0x2F64F8AB, 0x2764BF98, 0x0047EB09,
    0x22019901, 0xF04F462B, 0x44080901, 0x005AF3C0,
    0xF0203001, 0x46200701, 0xF40E4639, 0xB968FDA7,
    0x46294640, 0xF7F0220A, 0xF896F1EB, 0x46203036,
    0x22014639, 0xFF1EF40C, 0x924CF886, 0xE8BDB005,
    0xBF0083F0, 0x20415CD0, 0x4D29B5B0, 0xF8550A14,
    0xB11C4024, 0x40B0E8BD, 0xBE00F408, 0xBDB02000,
    0x4D23B5B0, 0xF8550A14, 0xB11C4024, 0x40B0E8BD,
    0xBE22F408, 0xBDB02000, 0xF408B510, 0xF000FF23,
    0xB138FFB5, 0x79004604, 0xF0001D61, 0x4620F857,
    0xF8AAF406, 0xBD102000, 0x4915B5B0, 0x46140A10,
    0x5020F851, 0xF4064610, 0x2819FBD5, 0x2101D807,
    0xF000FA01, 0x4208490F, 0x2000D001, 0x2001BDB0,
    0x0044F885, 0x003CF895, 0xD1032802, 0xF40D4620,
    0xE00BFE1B, 0x1037F895, 0xF40B4620, 0xB128F96D,
    0x1037F895, 0xF4074620, 0xE7E6FF91, 0xBDB02002,
    0x20415C60, 0x03C00001, 0x4601B510, 0xB1007800,
    0x4C0CBD10, 0x46202214, 0xF17AF7F0, 0x6848490A,
    0xD0F52800, 0x7F896809, 0xD1F12900, 0x0055F890,
    0x280272E0, 0xF04FBF14, 0xF04F101E, 0xF8C4100A,
    0xBD10000E, 0x204147C4, 0x20414778, 0x460CB57C,
    0xF7F44605, 0xB1F0F695, 0x21144606, 0xF8AD7820,
    0x217F1006, 0x0F50EBB1, 0x7860BF07, 0x0840491E,
    0xEB00491C, 0xEB010040, 0x68400080, 0x9000B188,
    0x0206F10D, 0x46214620, 0xF7F52314, 0xB180F545,
    0x1006F8BD, 0x4628E007, 0xE8BD4621, 0xF000407C,
    0x2004BF3B, 0x4A122114, 0x7327F44F, 0xFBC2F444,
    0x88B0BD7C, 0xF8BD490D, 0x44082006, 0xF7F04621,
    0xF89DF127, 0x46310006, 0x462871B0, 0xF91EF410,
    0x2006F89D, 0x0180F105, 0x46232017, 0xF648F7F6,
    0xBF00BD7C, 0x0005C9C0, 0x0005CCE4, 0x20418000,
    0x00412E75, 0x611EF2A0, 0xD8092908, 0xE8DF480E,
    0x1305F001, 0x17141513, 0x00191313, 0x4770480B,
    0x6101F240, 0xBF044288, 0x4770480A, 0x1107F241,
    0xBF044288, 0x4770480A, 0x47702000, 0x47704804,
    0x47704806, 0x47704804, 0x00410699, 0x004106B9,
    0x00410681, 0x00410669, 0x004104B9, 0x004104E5,
    0x00410345, 0x4604B570, 0x7A424817, 0xD10A2A01,
    0x42887A80, 0x2800BF18, 0x4A98D105, 0xF2404620,
    0xF4443386, 0x48B0FB5F, 0x0024F850, 0x2100B110,
    0xFBF4F446, 0xF0014620, 0x4620F9BD, 0xFE90F000,
    0xF7EF2600, 0x4605F79B, 0xD8072E03, 0xF4184620,
    0xB118FEE7, 0xFF18F418, 0xE7F53601, 0xE8BD4628,
    0xF7EF4070, 0xBF00B78F, 0x20414009, 0xB81CF000,
    0xB2CCB510, 0xD3072C02, 0x46114B80, 0x461A4620,
    0x5337F240, 0xFB2EF444, 0xF8504897, 0xB1080024,
    0xBD102000, 0x46204A79, 0xF2402100, 0xF444533C,
    0x2001FB45, 0xBF00BD10, 0x4601B570, 0x00AEF890,
    0x20BFF891, 0xF891B1C2, 0x4C3820BD, 0x0240EB02,
    0x005BB2D3, 0xF382FA53, 0x8D524A36, 0x0583EB02,
    0x88EE4425, 0x88ADB9C6, 0xF891B1B5, 0xB19910BB,
    0x43210099, 0xE00F5A89, 0x10FCF8D1, 0x4909B161,
    0x03896809, 0xEB00D508, 0x4A280140, 0x1141EA42,
    0x8CD24A28, 0x88894411, 0xE8BD2100, 0xF0014070,
    0xBF00B963, 0x500904D4, 0xBFC6F7FF, 0x4FF0E92D,
    0x4C1EB085, 0xF8DF4E1E, 0x469B8734, 0x4607468A,
    0x92042500, 0x0902F104, 0xD8182D03, 0xF4184638,
    0xB1A0FE6F, 0x1021F896, 0x0028F8B8, 0x1201EA44,
    0x07525A12, 0x0109D107, 0x0209EA41, 0xF0025A12,
    0x2A100278, 0xF418D007, 0x3501FE8F, 0x2000E7E4,
    0xE8BDB005, 0xF1048FF0, 0x2300020A, 0x9A0E4311,
    0x21005A08, 0xB000F8CD, 0x2101E9CD, 0x46519A04,
    0xFA1AF41B, 0xFE78F418, 0xE7E92001, 0x20418002,
    0x20415CD8, 0x204157AC, 0x41F0E92D, 0xF7FF4604,
    0x2C00FF7B, 0xF894D042, 0x280100AF, 0x6FE0D13E,
    0x10C8F8D4, 0xD0394281, 0xF8D46861, 0xF8B46084,
    0x1A088096, 0x4770F020, 0xF894F445, 0xF8944605,
    0xF41400AE, 0xB108FC89, 0xE0012014, 0x00B2F894,
    0xF085FA50, 0x2171F240, 0x43484378, 0xFBB04910,
    0xEBA8F0F1, 0xB2010000, 0x3FFFF1B1, 0x1C71DC05,
    0x2071F200, 0x4670F021, 0xF8D4E7F5, 0x1B8100D0,
    0xF0211A30, 0xF0204170, 0xF1B14070, 0xBF886F00,
    0x29044241, 0x6FE0BF24, 0x00C8F8C4, 0x81F0E8BD,
    0x000F4240, 0xB2CCB510, 0xD3072C02, 0x46114B9E,
    0x461A4620, 0x5327F240, 0xFA44F444, 0xF8504822,
    0xB1080024, 0xBD102000, 0x46204A97, 0xF2402100,
    0xF444532C, 0x2001FA5B, 0xBF00BD10, 0x00412E80,
    0x461CB5F8, 0x460E4615, 0xF7EF4607, 0x4916F677,
    0x1027F851, 0xF891B1A1, 0x270C20B1, 0xD1102A01,
    0x10AFF891, 0x0306EA45, 0x43239A06, 0xF04F2900,
    0xBF08010C, 0x31FFF04F, 0xBF08431A, 0xE000460F,
    0xF7EF270C, 0xB2F8F65F, 0xBF00BDF8, 0xF001B580,
    0x2000F871, 0x0000BD80, 0xF8514903, 0x28000020,
    0x2001BF18, 0xBF004770, 0x20414798, 0x4FF0E92D,
    0x4604B085, 0xF8504897, 0xB39F7024, 0x005BF897,
    0x4870B128, 0xF2404970, 0xF44442AF, 0xF107F9CD,
    0xF7F5005C, 0x4680F01B, 0x21004620, 0xF522F7F4,
    0x48814684, 0xEA4F9404, 0x4B681B44, 0xF1004D68,
    0xF8500908, 0xF859600B, 0xF897400B, 0xF0811059,
    0xEBB10101, 0xD1167FD6, 0x010BEB00, 0x2312469A,
    0xF8510C24, 0x93032F0C, 0xEA4F4653, 0xF04F4E12,
    0xE01152F8, 0x49ED4857, 0x5212F240, 0xE8BDB005,
    0xF4444FF0, 0xEB00B999, 0x2310010B, 0x52F8F44F,
    0xEF0CF851, 0x462B9303, 0x0A0BEB00, 0x0F00F1B8,
    0x0004F8DA, 0xF85B9002, 0xEA000009, 0xEA400003,
    0xF84B0002, 0x68080009, 0x0003EA00, 0x60084B47,
    0xF8B8D021, 0x07810004, 0x4842D00E, 0xF24049D7,
    0xE9CD42E2, 0x46F08C00, 0xF96EF444, 0xF8DD9800,
    0x46C6C004, 0x88804B3D, 0xF1B64629, 0xEA4F3FFF,
    0xBFC80090, 0xF8594619, 0xBFC8200B, 0x40110400,
    0xF8494308, 0xF8DD000B, 0x00A48010, 0x0F00F1BC,
    0xF8BCD01F, 0x07810004, 0x482ED00D, 0xF24049C3,
    0x46A942EE, 0x467546E3, 0xF946F444, 0x0004F8BB,
    0x4B2A46AE, 0xF1B6464D, 0xBFC83FFF, 0xF8DA461D,
    0xEA4F100C, 0xBFC80090, 0x40290400, 0xF8CA4308,
    0xB2A5000C, 0x068EEA4F, 0xF5B5B1AD, 0xD0124FF8,
    0x0056F897, 0x230122FF, 0x30010200, 0xF44FB281,
    0xF40660C6, 0xF880F80F, 0xF4068000, 0x4640F843,
    0xF7F44629, 0x0430F4F5, 0xB005BF04, 0x8FF0E8BD,
    0x1002E9DD, 0x230622FF, 0xF000FA21, 0x0403F000,
    0x0056F897, 0x30010200, 0xF240B281, 0xF405602F,
    0xF897FFF1, 0xF8972070, 0x80061040, 0x8004F880,
    0x320170C4, 0x70814351, 0xE8BDB005, 0xF4064FF0,
    0xBF00B819, 0x00412E50, 0x00412E80, 0xC000FFFF,
    0xFFFFC000, 0xF890B140, 0xF890104E, 0x39020056,
    0x2101BF18, 0xBF78F000, 0x00004770, 0x1107F3C1,
    0xF2404818, 0xF85012FD, 0x01490021, 0x0052F890,
    0x0040EB02, 0xF3C04A08, 0x281F0047, 0x201FBF28,
    0xF3C35853, 0x42832304, 0x588BD005, 0x53F8F423,
    0x2000EA43, 0x47705088, 0x50090610, 0x460CB570,
    0xF524F7EF, 0x46054E07, 0x0024F856, 0xF405B120,
    0x2000FD33, 0x0024F846, 0xF7EF4628, 0x2000F51B,
    0xBF00BD70, 0x20414790, 0x4614B570, 0x4606460D,
    0xF98EF000, 0x202AB108, 0x4630E004, 0x46224629,
    0xF8F2F41B, 0xBD70B2C0, 0x43F8E92D, 0x88884681,
    0xF640460D, 0x2620437A, 0x42991F81, 0x88E9D879,
    0x2B7C088B, 0x892BD875, 0x4476F640, 0x070AF1A3,
    0xD86E42A7, 0x0080EB00, 0x08403101, 0xEB034348,
    0x00490183, 0xD2014281, 0xE0622620, 0xF850483E,
    0xF8B00029, 0x07C00096, 0x8968D15A, 0x71FFF647,
    0xB2801A10, 0xD2014288, 0xE0522628, 0x21004648,
    0xF980F41F, 0xD1052805, 0x21014648, 0xF97AF41F,
    0xD1492805, 0x21014648, 0xF974F41F, 0x2709B108,
    0xF44FE018, 0x21047086, 0x23302204, 0xFF3AF405,
    0x21054A2A, 0xF41F4606, 0x2000F979, 0x46322101,
    0x002DF8A6, 0xF64F6130, 0x82F070FF, 0xF41F4648,
    0x2707F973, 0x21014648, 0x0801F04F, 0xF952F41F,
    0xD11D2805, 0x21014648, 0xF938F41F, 0x463A4649,
    0xF41F4606, 0x88A8F999, 0x802CF886, 0x22004639,
    0xF8D58530, 0x60F00006, 0x84B08968, 0x84708868,
    0xF8867868, 0x46480026, 0xF41F2600, 0xE000FBA3,
    0x46302624, 0x83F8E8BD, 0x21004648, 0xF41F2600,
    0x88A9F915, 0x85012200, 0x1006F8D5, 0x896960C1,
    0x88698481, 0x78698441, 0x1026F880, 0x21034648,
    0xFCC0F41D, 0xBF00E7E5, 0x20415D10, 0x00030851,
    0x4614B570, 0x4606460D, 0xF8DAF000, 0x202AB108,
    0x4630E004, 0x46224629, 0xFE54F41B, 0xBD70B2C0,
    0xF5B04903, 0xBF187F05, 0x46082100, 0xBF004770,
    0x004111D1, 0x7B4B4908, 0xD10B2B01, 0x42917B89,
    0x2900BF18, 0x4B05D106, 0x461A4611, 0x33DFF240,
    0xBFE8F443, 0xBF004770, 0x20414009, 0x00412E80,
    0x460FB5F8, 0xF7EF4605, 0x4604F429, 0xF85048D4,
    0xB1D66025, 0x0092F8B6, 0x0010F020, 0x1004F367,
    0x0092F8A6, 0xF4244628, 0xB167FCAB, 0xF4274628,
    0xF896FA49, 0xB13000A6, 0x46314628, 0x27002200,
    0xF990F000, 0x2700E002, 0x270CE000, 0xF7EF4620,
    0x4638F409, 0xBF00BDF8, 0x41F0E92D, 0x46044D7B,
    0xF8954688, 0x28FF0022, 0xF7F4D00A, 0xF895F38F,
    0x22021022, 0xFA0200C9, 0x4308F101, 0xF394F7F4,
    0x0023F895, 0xD00A28FF, 0xF380F7F4, 0x1023F895,
    0x00C92202, 0xF101FA02, 0xF7F44308, 0xF7EFF385,
    0x4606F3DD, 0xF85048D9, 0xB1A77024, 0x002CF107,
    0xF7F44641, 0xF8B7F5CD, 0xF0400092, 0xF8A70008,
    0x46200092, 0xF9FEF427, 0x46394620, 0xF0002201,
    0x2400F949, 0x240CE000, 0xF7EF4630, 0xF895F3C3,
    0x28FF0022, 0xF7F4D00A, 0xF895F351, 0x22021022,
    0xFA0200C9, 0x4388F101, 0xF356F7F4, 0x0023F895,
    0xD00A28FF, 0xF342F7F4, 0x1023F895, 0x00C92202,
    0xF101FA02, 0xF7F44388, 0x4620F347, 0x81F0E8BD,
    0x4906B158, 0x00A2F890, 0x88492294, 0x1002FB10,
    0x5A424903, 0x0220F042, 0x47705242, 0x204157AC,
    0x20418002, 0xF85149B1, 0xB1300020, 0x00A3F890,
    0xFAB03801, 0x0940F080, 0x20004770, 0xBF004770,
    0xF85149AA, 0xB1280020, 0x0056F890, 0xBF182800,
    0x47702001, 0x47702000, 0x41F0E92D, 0x46044D33,
    0xF8954688, 0x28FF0022, 0xF7F4D00A, 0xF895F2FF,
    0x22011022, 0xFA0200C9, 0x4308F101, 0xF304F7F4,
    0x0023F895, 0xD00A28FF, 0xF2F0F7F4, 0x1023F895,
    0x00C92201, 0xF101FA02, 0xF7F44308, 0xF7EFF2F5,
    0x4606F34D, 0xF8504891, 0xB1CF7024, 0xB1286AB8,
    0x49A748A6, 0x2277F240, 0xFED6F443, 0x0092F8B7,
    0x8028F8C7, 0x0008F040, 0x0092F8A7, 0xF4274620,
    0x4620F969, 0x22014639, 0xF8B4F000, 0xE0002400,
    0x4630240C, 0xF32EF7EF, 0x0022F895, 0xD00A28FF,
    0xF2BCF7F4, 0x1022F895, 0x00C92201, 0xF101FA02,
    0xF7F44388, 0xF895F2C1, 0x28FF0023, 0xF7F4D00A,
    0xF895F2AD, 0x22011023, 0xFA0200C9, 0x4388F101,
    0xF2B2F7F4, 0xE8BD4620, 0xBF0081F0, 0x20414674,
    0x41F0E92D, 0x46070A14, 0x46154698, 0x4620460E,
    0xFFECF432, 0xF851490D, 0xB1591024, 0x88328900,
    0xD30A4282, 0xD8052C09, 0x0098F891, 0x0003F000,
    0xD1022803, 0xE8BD2000, 0x463881F0, 0x462A4631,
    0xE8BD4643, 0xF42541F0, 0xBF00BCEF, 0x20415D10,
    0x47F0E92D, 0x4614461E, 0x4681460F, 0xF2D6F7EF,
    0xA158F8DF, 0xF85A4680, 0xB14D5029, 0x0056F895,
    0x4A6BB140, 0xF44F2100, 0xF4437344, 0xE020FE7B,
    0xE033260C, 0xE9DD980A, 0xF8A52108, 0x20010054,
    0x204EF8A5, 0xF88500B2, 0x00A00056, 0x004CF8A5,
    0xF8A500B8, 0x01480080, 0x2011E9C5, 0xEBB06F68,
    0xD9060F86, 0x10A8F895, 0xD3022902, 0xF4264648,
    0xF895FF7F, 0x260000A3, 0x6050F885, 0xF42CB970,
    0x4604FA3D, 0x0029F85A, 0x26002100, 0xFEE6F445,
    0x00A2F895, 0x22014621, 0xF9F6F426, 0xF7EF4640,
    0x4630F291, 0x87F0E8BD, 0xF8514930, 0xB1280020,
    0x00A3F890, 0xBF183802, 0x47702001, 0x47702000,
    0x20415D6C, 0x47F0E92D, 0xF8914605, 0x280000A3,
    0xF8B1D149, 0x460E0092, 0xD04407C0, 0x00C2F896,
    0xD0402800, 0x46144821, 0x8025F850, 0xFA06F42C,
    0x1056F896, 0x6DB04681, 0xD1022901, 0x1050F896,
    0xEBA0BB89, 0xF0210109, 0x6F714270, 0xD92A428A,
    0xF7F1FBB2, 0x208AF8B6, 0x7CFFF64F, 0x429AB2BB,
    0x4467BF08, 0xFA87FA1F, 0xD3094592, 0x49284827,
    0x7216F44F, 0xFDD8F443, 0x208AF8B6, 0x6F716DB0,
    0x001AFB01, 0x21001BD2, 0x208AF8A6, 0x4070F020,
    0x464065B0, 0xFE82F445, 0x46494628, 0xE8BD4622,
    0xF42647F0, 0xE8BDB991, 0xBF0087F0, 0x20415D6C,
    0xBF042818, 0x47704806, 0xBF042801, 0x47704803,
    0x4804B908, 0x20004770, 0xBF004770, 0x00410DB9,
    0x00410F21, 0x00410DD9, 0x4C0BB5B0, 0xB1817FA1,
    0xF2A1FA91, 0xF582FAB2, 0x4808B930, 0xF44F4908,
    0xF443720D, 0x7FA1FD99, 0x40A82001, 0x0000EA21,
    0x200077A0, 0xBF00BDB0, 0x20414720, 0x00412E50,
    0x00412EB7, 0xF241B510, 0x42830001, 0x4608D106,
    0x0101F241, 0x4010E8BD, 0xBF84F7FE, 0x20004A0A,
    0xD00E285D, 0x4030F832, 0xD001429C, 0xE7F73001,
    0x00C0EB02, 0xB1226842, 0x46194608, 0x4010E8BD,
    0x20004710, 0xBF00BD10, 0x0005C584, 0x4C29B5B0,
    0x504CF894, 0x7CE4B135, 0xD1032C02, 0xB10C7B4C,
    0xB11C7B8C, 0x40B0E8BD, 0xB862F435, 0xBDB02000,
    0x2302B109, 0x2802700B, 0x4B1DD80F, 0xF8532802,
    0xEB031030, 0x685B03C0, 0x60536011, 0x2801D005,
    0xBB00D004, 0x79404818, 0x4770B310, 0xF8904815,
    0xF003302A, 0x43190301, 0xF8907011, 0xF003302C,
    0xEA410301, 0x70110143, 0x302DF890, 0x0301F003,
    0x0183EA41, 0xF8907011, 0xF000002B, 0xEA410001,
    0x701000C0, 0xA2094770, 0x23812102, 0xBD3AF443,
    0xEA400E08, 0xF0002003, 0x70D000FB, 0xBF004770,
    0x0005C86C, 0x20416360, 0x20414009, 0x705F6D6C,
    0x68637461, 0x0000632E, 0x1105F241, 0xBF044288,
    0x47704804, 0x5101F240, 0xBF0C4288, 0x20004802,
    0xBF004770, 0x004113F5, 0x0041143D, 0x4605B5B0,
    0x460C7840, 0xD10E2818, 0x0A2049AB, 0x0020F851,
    0x0036F890, 0xD1062801, 0xF4054620, 0x2801FC9B,
    0x2000BF04, 0x4628BDB0, 0xE8BD4621, 0xF43740B0,
    0xBF00B8B3, 0x4606B5F8, 0x0A0C489F, 0xF850460D,
    0x46087024, 0xFC86F405, 0xD1022825, 0x0036F897,
    0x4630B128, 0xE8BD4629, 0xF43740F8, 0xF897BCF9,
    0x46203039, 0x2223210B, 0xFF02F40B, 0xBDF82000,
    0xF7EFB5B0, 0x4D38F123, 0x46284604, 0xF306F7F4,
    0xF405B110, 0xE7F8F931, 0xE8BD4620, 0xF7EF40B0,
    0xBF00B119, 0x21004830, 0x1100E9C0, 0xBF004770,
    0xB085B5F0, 0x4605466E, 0xF7F44630, 0xF7EFF269,
    0x4F29F105, 0x46384604, 0xF2E8F7F4, 0x4601B158,
    0x42A87900, 0x4608D103, 0xF90EF405, 0x4630E7F3,
    0xF2EEF7F4, 0x9800E7EF, 0x481FB118, 0xF7F44669,
    0x4620F2A1, 0xF0EEF7EF, 0xBDF0B005, 0xF7EFB5B0,
    0x4604F0E5, 0xF7F44818, 0x4605F2C9, 0xF7EF4620,
    0x4628F0E1, 0xBF00BDB0, 0x4605B5F8, 0x460C4812,
    0xF32AF7F4, 0xD8162804, 0x2100201C, 0xFB12F405,
    0xF7EF4606, 0x4607F0CB, 0x4631480B, 0xF2C0F7F4,
    0xF7EF4638, 0x7135F0C7, 0x46211D70, 0xE8BD2214,
    0xF7EF40F8, 0x4906B1DD, 0xF240A004, 0xE8BD2265,
    0xF44340F8, 0xBF00BC49, 0x204141F0, 0x00535341,
    0x00412E75, 0x4605B5F8, 0x0A0C4853, 0x7024F850,
    0xF4054608, 0x2819FBEF, 0x2101D807, 0xF000FA01,
    0x4208491A, 0x2000D001, 0x7828BDF8, 0x0301F000,
    0x0036F897, 0x3039F887, 0xD11C2801, 0x0609F105,
    0xF7F44630, 0x2814F3F1, 0x7A28D317, 0xD2142802,
    0x700AF507, 0x220A4631, 0xFD8FF43E, 0x7A2A6869,
    0x46334620, 0xFDB2F40D, 0x28007A28, 0x2001BF18,
    0x024CF887, 0x2224E7D7, 0xF897E002, 0x221E3039,
    0x213C4620, 0xFE44F40B, 0xBF00E7CD, 0x03C00001,
    0x4606B5F8, 0x0A0D4830, 0xF850460C, 0x46087025,
    0xFBA8F405, 0x0116F1A0, 0xD3152904, 0x282EB1A0,
    0x481AD10D, 0xFB152134, 0x7A400001, 0xF897B128,
    0xB9100036, 0x07C07830, 0x88F0D11F, 0x7830B150,
    0x0001F000, 0x0039F887, 0x46214630, 0x40F8E8BD,
    0xBC9AF43A, 0x4621481D, 0xF2406802, 0x47906004,
    0x21177830, 0xF0002212, 0x46280301, 0xFE08F40B,
    0x21204620, 0xFA0CF40C, 0x4628E005, 0x22232117,
    0xF40B2301, 0x2000FDFD, 0xBF00BDF8, 0x20415C68,
    0x4605B570, 0x0A08460C, 0xF851490B, 0x46206020,
    0xFB60F405, 0xD1092832, 0x0036F896, 0xD1052801,
    0x46214806, 0xF2406802, 0x47906004, 0x46214628,
    0x4070E8BD, 0xBCE8F43B, 0x20415C60, 0x20414450,
    0x4614B510, 0xD0072821, 0xF5B1B970, 0xD10B0F80,
    0xF7FFB298, 0xE008FDE1, 0xD1052920, 0xF0014618,
    0x2101F8CB, 0xE0012000, 0x21002000, 0xBD107021,
    0x4614B510, 0x0212F1A0, 0xD90F2A03, 0xD0182807,
    0xD0332820, 0xD01B2823, 0xD1442829, 0xD03B2902,
    0xD1402908, 0xF7FE4618, 0xE03CFE5F, 0xF002E8DF,
    0x20172C02, 0x7F80F1B1, 0x4618D135, 0xF83EF7FF,
    0xF5B1E028, 0xD12E2F00, 0xF7FF4618, 0xE021FC21,
    0x7F00F5B1, 0xB2D8D127, 0xF8A6F001, 0xF1B1E023,
    0xD01D5F00, 0xD11E2980, 0xF7FF4618, 0xE011F8C5,
    0xD1182902, 0xF7FF4618, 0xE00BFA55, 0xD1122908,
    0xF0004618, 0xE00EFF6B, 0x4F80F5B1, 0x4618D10B,
    0xF86AF7FF, 0xE0072001, 0xF7FE4618, 0xE002FF35,
    0xF7FFB2D8, 0x2000F94B, 0xBD107020, 0x4605B5B0,
    0x460A4614, 0x20009904, 0x70202D07, 0x2D13D00F,
    0xF1B2D127, 0xD0176F00, 0x5F80F1B2, 0xB2D8D119,
    0xFCE4F005, 0x2000B198, 0x20017020, 0xF5B2BDB0,
    0xD0105F80, 0x4F80F5B2, 0xF1B2D014, 0xD1086F00,
    0xF7FFB2D8, 0xBDB0FBF9, 0xF005B2D8, 0x2001FD23,
    0x20007020, 0xB2D8BDB0, 0xBF182900, 0xF7FF2101,
    0xBDB0FB2F, 0xF7FFB2D8, 0xBDB0FB57, 0x4614B510,
    0xD10F2812, 0xF5B19A02, 0xD00D0F80, 0x5F80F5B1,
    0xB2D1D108, 0xF0054618, 0x3801FC77, 0xF080FAB0,
    0xE0060940, 0xE0042000, 0xB2D1B2D8, 0xFF82F7FE,
    0x70202001, 0xB510BD10, 0x22004614, 0x70222814,
    0xF5B1BF08, 0xD1063F80, 0x1202E9DD, 0xB2C9B2D8,
    0xF8D4F7FF, 0x20002201, 0xBD107022, 0x3807B570,
    0x460A4614, 0x280E2100, 0xD8297021, 0x1504E9DD,
    0xF000E8DF, 0x26261008, 0x26262626, 0x17262626,
    0x0031271F, 0xD11B2A40, 0xB2C9B2D8, 0xF7FFB2EA,
    0xE026FAC9, 0x6F80F5B2, 0xB2D8D112, 0xFC7CF7FF,
    0xF1B2E00D, 0xD10B4F80, 0x4618B2EA, 0xFF78F7FE,
    0xF5B2E00E, 0xD1033F80, 0xF7FFB2C8, 0xB980F8A5,
    0xF5B2BD70, 0xD1FB6F80, 0x4618B2EA, 0xF852F7FF,
    0xD1F52800, 0x2A04E005, 0xB2EAD1F2, 0xF7FF4618,
    0x2001F99D, 0xBD707020, 0x4614B5B0, 0x28152200,
    0xBF087022, 0x7F00F5B1, 0x2000D001, 0xAD04BDB0,
    0xCD26B2D8, 0xB2C9462B, 0xF9A8F7FF, 0x70212101,
    0xB5B0BDB0, 0x22004614, 0x70222826, 0x2940BF08,
    0xBDB0D000, 0xB2D8AD04, 0x462BCD26, 0xB2C9B2D2,
    0xFE4AF000, 0x70202001, 0x2000BDB0, 0x20007010,
    0x00004770, 0x4614B510, 0x0221F1A0, 0x2A072000,
    0xE8DFD843, 0x4204F002, 0x42094242, 0x2908360F,
    0xF000BF08, 0xE033FE9F, 0x5F80F5B1, 0xF7FFD130,
    0xE031FD77, 0x7F80F5B1, 0x4819D12A, 0xF8D06801,
    0x43080288, 0x4817D024, 0xF0416801, 0x60010104,
    0x29006801, 0x6801D403, 0x3FFFF1B1, 0xF005DCFB,
    0x2002FBA3, 0xF0C4F7F0, 0xFBB0490F, 0x490FF0F1,
    0xF36F680A, 0xEA424218, 0xF4404000, 0x60080000,
    0xF5B1E00A, 0xD0052F80, 0xD1012908, 0xFAEEF7FE,
    0xE0022000, 0xFFEEF000, 0x70202001, 0xBF00BD10,
    0x204167D8, 0x5004000C, 0x007A1200, 0x50000014,
    0xB086B570, 0x46024614, 0x2A142000, 0xD01D7020,
    0xD0372A13, 0xD1472A07, 0x4F80F1B1, 0xA80AD143,
    0x3034F8BD, 0x2030F8BD, 0x4038F8BD, 0x503CF8BD,
    0x6040F8BD, 0x011CF100, 0xF8109105, 0xF89D1F04,
    0xE88D0028, 0xF7FF0070, 0xE02DFB5B, 0x5F00F5B1,
    0xA80AD129, 0x3034F8BD, 0x2030F8BD, 0x5038F8BD,
    0x603CF89D, 0x0118F100, 0xF8309104, 0xF89D1F04,
    0xE9CD0028, 0xF7FE5600, 0x28FFFFA3, 0x2101BF1C,
    0xE0117021, 0x4F00F5B1, 0xA80AD10D, 0xF100AC0C,
    0x91030114, 0x1F04F850, 0x0028F89D, 0x9400CC1C,
    0xFEDCF7FE, 0x2000E000, 0xBD70B006, 0x2300B51C,
    0x70132825, 0x2908BF08, 0xBD1CD000, 0xF89DA804,
    0xF1004020, 0x91010114, 0x1F04F850, 0x2306E9DD,
    0x94009804, 0xFD06F000, 0x0000BD1C, 0x21004A9F,
    0xD0042907, 0x42835653, 0x3101DA01, 0x48B3E7F8,
    0x47707001, 0x48F7B510, 0x68014CF7, 0x0110F021,
    0xF9946001, 0xF7FF0002, 0x7820FFE9, 0x560849F3,
    0x4010E8BD, 0xBA78F000, 0x4EEFB570, 0x4604460D,
    0x783049EE, 0xB2405C08, 0x2101B11D, 0xFBBCF000,
    0xF000E001, 0x1F02F825, 0xFAB27871, 0x2900F282,
    0x2101BF18, 0x40110952, 0xBF182D00, 0x40292501,
    0xB2C14408, 0xE8BD4620, 0xF7FE4070, 0xBF00BB29,
    0x00004770, 0xA1054804, 0xF3C06800, 0x28030041,
    0x2002BF08, 0x47705C08, 0x50082850, 0x00020001,
    0xF0002100, 0x0000BC5D, 0xF000B510, 0x48D2F9B9,
    0x290078C1, 0x496FD16B, 0xF2404A6F, 0xF8C133FF,
    0x680A2100, 0x0210F042, 0x680A600A, 0x7280F022,
    0x680A600A, 0x0280F022, 0xF8D1600A, 0xF04220CC,
    0xF8C16280, 0xF8D120CC, 0xF36F20D4, 0xF8C12293,
    0xF8D120D4, 0xF44220D4, 0xF8C13280, 0xF8D120D4,
    0x439A20D4, 0x20D4F8C1, 0x20D4F8D1, 0x7280F442,
    0x20D4F8C1, 0x20D8F8D1, 0x2293F36F, 0x20D8F8C1,
    0x20D8F8D1, 0x3280F442, 0x20D8F8C1, 0x20D8F8D1,
    0x0209F36F, 0x20D8F8C1, 0x20D8F8D1, 0x72A0F442,
    0x20D8F8C1, 0xF8C122A6, 0xF8D120DC, 0xF42220DC,
    0xF8C16270, 0xF8D120DC, 0xF44220DC, 0xF8C162C0,
    0xF8D120DC, 0xF42220DC, 0xF8C14270, 0xF8D120DC,
    0xF44220DC, 0xF8C14220, 0x6C0A20DC, 0x0280F042,
    0xF8D1640A, 0xF02220E0, 0xF8C10204, 0x780020E0,
    0x5608499A, 0xF9C8F000, 0x483A4C39, 0x4A40493B,
    0x48396020, 0x13ECF8C0, 0xF8C04939, 0x493913F4,
    0x13F8F8C0, 0xF8C04938, 0xF24013FC, 0xF8C07155,
    0x49361400, 0x4A37600A, 0x4A37604A, 0x4A37608A,
    0x220D60CA, 0x1390F8D4, 0x0105F362, 0xF8C42237,
    0xF8D01390, 0xF36210A8, 0x22500105, 0x10A8F8C0,
    0x10A8F8D0, 0x5180F441, 0x10A8F8C0, 0x10A8F8D0,
    0x5100F441, 0x10A8F8C0, 0xF3626801, 0x60014117,
    0xF3626801, 0x22400107, 0xF8D46001, 0xF3621408,
    0x220B010D, 0x1408F8C4, 0xF3626841, 0x2203210E,
    0x68416041, 0x0106F362, 0x68606041, 0x5000F420,
    0x68606060, 0xD5050480, 0x491A4819, 0x42DDF240,
    0xF85AF443, 0xF4406860, 0x60605000, 0x04806860,
    0xBD10BF48, 0x49134812, 0x42E2F240, 0x4010E8BD,
    0xB84AF443, 0x50084000, 0x68555555, 0x00412DAC,
    0x50090070, 0x20201000, 0x5009048C, 0x08C00040,
    0x003C0540, 0x00500350, 0x00500755, 0x50090890,
    0x00090907, 0x00070503, 0x1CA80903, 0x17280003,
    0x00412E50, 0x00412E54, 0x4604B510, 0xF7EF2007,
    0x4A85F5CF, 0x70D10FC1, 0x6006F3C0, 0xF7FF7050,
    0x4620FEEB, 0x4010E8BD, 0xB802F000, 0x20414000,
    0xB096B570, 0x20014604, 0x0007F88D, 0xF7EF2007,
    0x4605F5B7, 0x493B483A, 0x1008E9C4, 0xF8842011,
    0x4839002C, 0xF3456160, 0xF0004007, 0xF884F90D,
    0x4836002D, 0x62A00A29, 0x60204835, 0x06020C28,
    0x6F20F1B2, 0x4A33DD04, 0xF0436813, 0x60130308,
    0x70B04E69, 0x71B1B240, 0xF7FF7175, 0x492FFE5F,
    0x2000B26A, 0xD0042807, 0x4293560B, 0x3001DA01,
    0x71F0E7F8, 0x492B482A, 0x4B2E4A2D, 0x1006E9C4,
    0x492A4829, 0x3201E9C4, 0x1003E9C4, 0xA8022100,
    0xD0032928, 0x5442004A, 0xE7F93101, 0xF06F2128,
    0x2950024E, 0xEB02D004, 0x54430341, 0xE7F83101,
    0xA9024821, 0xF7EE2250, 0x4E20F543, 0x052EF104,
    0x0107F10D, 0x462A203A, 0x47986833, 0x20D8B108,
    0xF1047028, 0x6833052F, 0x0107F10D, 0x462A203B,
    0xB1084798, 0x702820C4, 0x68333430, 0x0107F10D,
    0x4622203C, 0xB1084798, 0x702020BA, 0xBD70B016,
    0x00412123, 0x0041211F, 0x00412435, 0x004123B1,
    0x0041211D, 0x500840E0, 0x20414000, 0x00412DAC,
    0x00412125, 0x004123C9, 0x00412D71, 0x004124F5,
    0x00412499, 0x00411C81, 0x20418100, 0x204147DC,
    0x21014829, 0x60014BD0, 0x680A49C0, 0x62F0F422,
    0x680A600A, 0x62A0F442, 0x684A600A, 0x4B23401A,
    0x684A604A, 0x4B22431A, 0x6B4A604A, 0x4B21401A,
    0x6B4A634A, 0x634A431A, 0x78D24ABE, 0x6B4AB91A,
    0x6220F042, 0x491D634A, 0x65421D0A, 0x65824A1C,
    0x491C65C1, 0x66014A1D, 0x6641491B, 0x6682311B,
    0x491B66C1, 0x491B6701, 0x491B6741, 0x491B6781,
    0x491B67C1, 0x1080F8C0, 0xF8C0491A, 0x491A1084,
    0x1088F8C0, 0xF8C04919, 0x4919108C, 0x1090F8C0,
    0xF8C04918, 0x49181094, 0x1098F8C0, 0x618EF44F,
    0x109CF8C0, 0xBF004770, 0x5008412C, 0x003A82A0,
    0xF0F3FFFC, 0x00080001, 0x20414000, 0x00FFEFFC,
    0x00FFCFFD, 0x00010005, 0x0002B01D, 0x0003C037,
    0x00FF8015, 0x00FAFFD5, 0x00F69F8A, 0x00F48F51,
    0x00F6DF50, 0x00FECFA1, 0x000C304E, 0x001D5148,
    0x002F1265, 0x003DB36F, 0x0045F42C, 0x20004770,
    0x47704770, 0x4770B240, 0x41F0E92D, 0x46044E7E,
    0x4D7F4F7E, 0x0C08F856, 0xDB0F2C0B, 0x0010F040,
    0x0C08F846, 0x40286830, 0x68306030, 0x702EF040,
    0x46206030, 0xFDA4F7FF, 0xE0624680, 0x0010F020,
    0xF8462C00, 0x6AF80C08, 0x0006F020, 0x6AF862F8,
    0x0001F020, 0x6B3862F8, 0x4078F420, 0x6B386338,
    0x6080F420, 0xF04F6338, 0xBFC80000, 0x2C042001,
    0x0080EA4F, 0x200ABFC8, 0xD010280A, 0xD0142804,
    0x6AF8BB10, 0x0002F040, 0x6AF862F8, 0x0001F040,
    0x6B3862F8, 0x6000F440, 0x6B386338, 0x6AF8E012,
    0x0004F040, 0x6AF862F8, 0x6AF8E004, 0x6AF862F8,
    0x0001F040, 0x6B3862F8, 0x4078F440, 0x6B386338,
    0x6080F440, 0xE0056338, 0xA153A052, 0x32A6F240,
    0xFDFAF43B, 0x21014620, 0xF8E6F000, 0x6AF04680,
    0x00FCF020, 0x6AF062F0, 0x0088EA40, 0x683062F0,
    0x60304028, 0xF0406830, 0x60307021, 0xF7FF4620,
    0x4A4CFD3F, 0x78D1A54E, 0xF0236B7B, 0x2900037C,
    0xA349637B, 0x461DBF08, 0x6B7B5C28, 0x0080EA43,
    0xB1496378, 0x2C0D6B78, 0x6070F020, 0xD0036378,
    0xF0406B78, 0x63786020, 0x28007850, 0x2C0DD060,
    0x7910D15C, 0xD0592800, 0x4A3F2000, 0x68386470,
    0x60F0F420, 0x68386038, 0x6080F440, 0x68786038,
    0x0002EA00, 0x60784A39, 0xEA406878, 0x60780002,
    0x6B78B939, 0x40084936, 0x6B786378, 0x007EF040,
    0xF44F6378, 0x49385080, 0x0098F8C6, 0x0001F242,
    0x009CF8C6, 0xF8C6482F, 0x482F00A0, 0x00A4F8C6,
    0xF8C6482E, 0x482E00A8, 0x00ACF8C6, 0xF8C6482D,
    0x301900B0, 0x10B4F8C6, 0x00B8F8C6, 0xF8C6482B,
    0x482B00BC, 0x00C0F8C6, 0xF8C6482A, 0x482A00C4,
    0x00C8F8C6, 0xF8C64829, 0x482900CC, 0x00D0F8C6,
    0xF8C64828, 0x482800D4, 0x00D8F8C6, 0xF8C64827,
    0xF24000DC, 0xF8C640C4, 0xE00100E0, 0xFE80F7FF,
    0xE8BD4640, 0xBF0081F0, 0x500840E8, 0x50082824,
    0xF803FFFF, 0x00000030, 0x745F6672, 0x6F705F78,
    0x5F726577, 0x69736162, 0x6F635F63, 0x6769666E,
    0x00000000, 0x20414000, 0x0A070400, 0x1F1F1F12,
    0x0E080500, 0x1F1F1B1B, 0xFF807C0F, 0x00100100,
    0xF0F3FF80, 0x00FFD000, 0x00FF1FF7, 0x00FECFED,
    0x00FF8FEF, 0x00011004, 0x0001F01B, 0x00FDFFFC,
    0x00FA0FBF, 0x00F7FF88, 0x00FB2F8B, 0x0005BFF7,
    0x001740DC, 0x002C621B, 0x003F7368, 0x004AC465,
    0x68014804, 0x0101F041, 0x68016001, 0xD5FC06C9,
    0xBF004770, 0x5004003C, 0x460CB5B0, 0x280B4605,
    0x4628DB05, 0xE8BD4621, 0xF00040B0, 0xF7FFB8C1,
    0xEBC0FC51, 0x492B1000, 0x0040EB01, 0x29102100,
    0xF910D007, 0x2A7F2011, 0x42AAD005, 0x3101DA04,
    0x2110E7F5, 0x3901E000, 0xB2EDB2CA, 0xF484FAB4,
    0x3012F810, 0x1B5B0964, 0x2301BF18, 0xBF182A00,
    0x40232201, 0x1A89401A, 0xEB00B2C9, 0x78400041,
    0xBF00BDB0, 0x460DB5B0, 0xF7FF4604, 0x2D01FC23,
    0x4945D115, 0x1005F991, 0xDA10290B, 0x1200EBC0,
    0xEB014910, 0x22000342, 0xD0102A10, 0x0542EB03,
    0x2DFF786D, 0x42A5D00B, 0x3201D00B, 0x1C60E7F4,
    0xD8042C07, 0xD1FB3801, 0x5D004846, 0x2000E007,
    0xEBC0E005, 0xEB011000, 0xF8100040, 0xB2400012,
    0xBF00BDB0, 0x00412DB3, 0x4D2FB570, 0x78684604,
    0x4620B118, 0xFE34F7FD, 0x78287128, 0x4E2BB300,
    0x2006F995, 0x42915631, 0x3801DD1A, 0xB2C07028,
    0xF7FF5630, 0x7828FE31, 0xF7FF5630, 0x4601FBE9,
    0xF7FD4620, 0x7828FEFD, 0x2006F995, 0x56312300,
    0xF080FAB0, 0x42910940, 0x2301BFD8, 0xBD704318,
    0xBD702001, 0x4D18B570, 0x78684604, 0x4620B118,
    0xFE06F7FD, 0x78287128, 0xD8222806, 0xF9954E13,
    0x56312005, 0xDA1C4291, 0x70283001, 0xF7FF5630,
    0x7828FE03, 0xF7FF5630, 0x4601FBBB, 0xF7FD4620,
    0x7828FECF, 0x2005F995, 0x56312300, 0xF04F4291,
    0xBFA80100, 0x28062101, 0x2301BF88, 0x0001EA43,
    0x2001BD70, 0xBF00BD70, 0x20414000, 0x00412DAC,
    0x4B0CB510, 0x2A082200, 0x5C9CD004, 0xDA024284,
    0xE7F83201, 0x1A202400, 0xF181FAB1, 0x2001BF18,
    0x40080949, 0x2900B2D1, 0x2101BF18, 0x1A104008,
    0xBD10B2C0, 0x00412E28, 0xB085B5F0, 0x461C2000,
    0x4615AF02, 0x2B02460E, 0xF04FA212, 0xF8AD010A,
    0xE9CD0010, 0xBF280002, 0x46382402, 0xF4404623,
    0x2014FE75, 0x6300F44F, 0x462A4631, 0x90012C00,
    0xF810480B, 0x90000024, 0xBF084638, 0x7300F44F,
    0xF87CF441, 0xB1104604, 0xF4414620, 0x4620FBC9,
    0xBDF0B005, 0x455F454B, 0x64255456, 0x00000000,
    0x00055028, 0x47FFE92D, 0x20014604, 0x4690469A,
    0xF10D460D, 0xF10D010E, 0xF88D020F, 0x4824000E,
    0x20866803, 0xF8DD4798, 0xB1A89030, 0xF88D2000,
    0xF1B9000F, 0xD1380F01, 0x46212001, 0x4643462A,
    0xA000E9CD, 0xF43DA01B, 0xA029F875, 0x462A4621,
    0xFBCAF43B, 0x87FFE8BD, 0x000FF89D, 0xD0E82800,
    0xF7EE4620, 0x2104F21B, 0xFA112200, 0x2100F080,
    0x0703F020, 0x0020F107, 0xF241B283, 0xF4041009,
    0xF107FB31, 0x46060110, 0xF1F1F7EE, 0x46211D30,
    0x0520E880, 0xF8862002, 0x70309001, 0x0010F106,
    0xF210F7EE, 0xF4004630, 0xE8BDFF31, 0xBF0087FF,
    0x204147DC, 0x70697772, 0x7373615F, 0x3A747265,
    0x656C6966, 0x7325203A, 0x696C202C, 0x2520656E,
    0x70202C64, 0x6D617261, 0x64253D30, 0x7261702C,
    0x3D316D61, 0x742C6425, 0x3D657079, 0x000A6425,
    0x45535341, 0x00005452, 0x4919B5BC, 0xF10D2000,
    0x23000206, 0xD0102802, 0x4020F851, 0xF894B14C,
    0x2D9750AA, 0xF9B4D305, 0xFB9330A8, 0x2301F4F5,
    0x2400E000, 0x30015414, 0xB1BBE7EC, 0x1009F241,
    0x22002106, 0xF40423A4, 0x4604FAD5, 0x49092030,
    0x7020224F, 0x0006F8BD, 0x710EF501, 0x0001F8A4,
    0xF7EE1CE0, 0x4620F165, 0xFAFCF404, 0xBF00BDBC,
    0x20414798, 0x204167D8, 0xBF12F7FE, 0x4605B5F8,
    0xF034F7EE, 0x46044E16, 0x46304629, 0xF186F7F3,
    0x4813B118, 0xF7F34629, 0x2100F0DD, 0x6836487F,
    0x6872B176, 0x1A9F686B, 0x4770F027, 0xD8074287,
    0xD1034293, 0x892B8932, 0xD9014293, 0xE7EE4631,
    0xB1194807, 0xF7F3462A, 0xE004F17D, 0xF7F34629,
    0xF443F249, 0x4620FEFD, 0x40F8E8BD, 0xB00AF7EE,
    0x20416AC4, 0x47FFE92D, 0x91A4F8DF, 0xF8D9A801,
    0xF4437000, 0xF8DFFCB5, 0xF898819C, 0x28FF002F,
    0xF7F2D00A, 0xF898F78B, 0x2208102F, 0xFA0200C9,
    0x4308F101, 0xF790F7F2, 0xF8D9B1D7, 0xB1B80014,
    0x9A016879, 0x1A511A8B, 0x4370F023, 0x4170F021,
    0x6F00F1B3, 0x424BBF88, 0x7EC24957, 0x44117809,
    0xDC05428B, 0xF8C92100, 0x6A011014, 0x4788B101,
    0xF0002F00, 0x89388085, 0xF5A06879, 0xF1B26287,
    0xDC053FFF, 0xF2021E48, 0xF0202271, 0xE7F64170,
    0x4C4A9801, 0x1A0B46A2, 0x4370F023, 0xD80642A3,
    0xD12F4281, 0x0008F8BD, 0x46084282, 0x4648DA2A,
    0xF194F7F3, 0x0518F109, 0x46284601, 0xF1A0F7F3,
    0x4E3F4C3D, 0x7000F8D9, 0xD0592F00, 0x68797820,
    0x1A084653, 0x98011981, 0xF0221A42, 0x45524270,
    0xF021D810, 0x42814170, 0x8939D104, 0x2008F8BD,
    0xD907428A, 0xF7F34648, 0x4601F171, 0xF7F34628,
    0xE7DFF17F, 0xF06F492C, 0x78094270, 0x687A4051,
    0x1A0A4411, 0x4270F022, 0xD8084552, 0x4170F021,
    0xD12D4288, 0xF8BD8938, 0x42881008, 0x4648D828,
    0xF154F7F3, 0xF8D94605, 0xB1100014, 0xB1016A01,
    0x69E84788, 0x5014F8C9, 0x2020B130, 0xF7F44629,
    0x69E9F687, 0x47884628, 0x0000F8D9, 0xF8D9B180,
    0x68401014, 0x1A406849, 0x4178F06F, 0x4070F020,
    0xD3054288, 0xA013491B, 0x1209F240, 0xFADCF442,
    0x0018F8D9, 0xF443B108, 0xF444FBB9, 0xF898FB11,
    0x28FF002F, 0xF7F2D00A, 0xF898F6D1, 0x2208102F,
    0xFA0200C9, 0x4388F101, 0xF6D6F7F2, 0x87FFE8BD,
    0x20416AD8, 0x20414674, 0x20416A78, 0x07FFFFFE,
    0xF0000001, 0x00535341, 0xB140B580, 0x29FF7E81,
    0x4A04D105, 0xF2402100, 0xF4421321, 0x2000FACB,
    0xBF00BD80, 0x00412E8F, 0x4D3FB5F8, 0x7FA84604,
    0xD00928FF, 0xF6A2F7F2, 0x22087FA9, 0xFA0200C9,
    0x4308F101, 0xF6A8F7F2, 0xF8964E38, 0xB9200102,
    0x49384837, 0xF44222A2, 0xEB06FA8F, 0x01201704,
    0xF8174A35, 0x5A101F0C, 0xF000B169, 0x28300030,
    0xEB06D109, 0x684B1104, 0x0120B15B, 0x22046889,
    0x47985830, 0x482AE00A, 0x22C3492A, 0xFA74F442,
    0x4827E028, 0x22B24927, 0xFA6EF442, 0x0100F896,
    0xBF0242A0, 0xF0001C60, 0xF886000F, 0x20000100,
    0x70387FA9, 0x0102F896, 0xF1A029FF, 0xF8860001,
    0xD0110102, 0xF65AF7F2, 0x22F07FA9, 0x408A00C9,
    0xF8964390, 0x01122102, 0xFA02B2D2, 0x4308F101,
    0xF65AF7F2, 0x0102F896, 0xB148B2C0, 0xD10A2801,
    0x0100F896, 0xF0003001, 0xF886000F, 0xE0020101,
    0xF4432040, 0x7FA8FA79, 0xBF0828FF, 0xF7F2BDF8,
    0x7FA9F635, 0x00C92208, 0xF101FA02, 0xE8BD4388,
    0xF7F240F8, 0xBF00B639, 0x20414674, 0x20416B08,
    0x00412E50, 0x00412E9F, 0x20418000, 0xF43CA001,
    0xBF00BE31, 0x62616E65, 0x4C20656C, 0x20555043,
    0x002E2E32, 0x48444948, 0xF5006101, 0x61412103,
    0x60C12173, 0x4180F44F, 0x68816081, 0x6108F441,
    0x68816081, 0x7110F441, 0x68816081, 0x0101F041,
    0x68016081, 0xD5FC0789, 0x60412101, 0xF0216881,
    0x60810101, 0xBF004770, 0xF446B580, 0x4832FAB9,
    0x22332101, 0x68816041, 0x0101F021, 0x492F6081,
    0x492F6101, 0x60C26141, 0x0210F244, 0x68826082,
    0x6208F442, 0x68826082, 0x7210F442, 0x68826082,
    0x0201F042, 0x68026082, 0xD5FC0792, 0x60422201,
    0xF0226882, 0x60820201, 0x61024A22, 0x2201F500,
    0xF44F6142, 0x60C27200, 0x0210F244, 0x68826082,
    0x6208F442, 0x68826082, 0x7210F442, 0x68826082,
    0x0201F042, 0x68026082, 0xD5FC0792, 0xF5012201,
    0x604251C0, 0xF0226882, 0x60820201, 0x61024A12,
    0x21736141, 0xF24460C1, 0x60810110, 0xF4416881,
    0x60816108, 0xF4416881, 0x60817110, 0xF0416881,
    0x60810101, 0x07896801, 0x2101D5FC, 0x68816041,
    0x0101F021, 0xBD806081, 0x50001000, 0x204156E0,
    0x50082800, 0x20414D0C, 0x20414024, 0x483AB5F0,
    0xF240493A, 0x4E512506, 0x48396001, 0x60014939,
    0x493A4839, 0x483A6001, 0x6001493A, 0x493B483A,
    0x483B6001, 0x6001493B, 0x493C483B, 0x483C6001,
    0x6001493C, 0x493D483C, 0x483D6001, 0x6001493D,
    0x493E483D, 0x483E6001, 0x6001493E, 0xE9D0483E,
    0xE9D03414, 0xF8D02C12, 0xF0411080, 0x432C0108,
    0x50A0F8D0, 0xF8D06544, 0x43354094, 0x50A0F8C0,
    0x509CF8D0, 0x7580F445, 0x509CF8C0, 0x508CF8D0,
    0x7500F445, 0x508CF8C0, 0x5098F8D0, 0x0540F045,
    0x5098F8C0, 0x50A4F8D0, 0x050AF045, 0x50A4F8C0,
    0xE9D06805, 0xF8C06707, 0xF4451080, 0xF6410180,
    0x60010508, 0x1084F8D0, 0xF8C0432C, 0x4C244094,
    0x0108F041, 0xF8C04334, 0xF4471084, 0xE9C06180,
    0x49204107, 0x65014319, 0x4311491F, 0x491F6481,
    0x010CEA41, 0xBDF064C1, 0x204144C0, 0x00411A6B,
    0x204144C4, 0x00411A75, 0x204144A4, 0x00411811,
    0x204144A0, 0x004117E1, 0x204144AC, 0x0041192D,
    0x204144A8, 0x004118BD, 0x204144B4, 0x0041198D,
    0x204144B0, 0x00411967, 0x204144BC, 0x00411A43,
    0x204144B8, 0x00411A19, 0x204144CC, 0x00411BCD,
    0x204144C8, 0x00411B21, 0x2041560C, 0x00040008,
    0x48085040, 0x20012480, 0x41401000, 0x1801C000,
    0x4D0CB570, 0x78684604, 0x4620B118, 0xF9C8F7FD,
    0x79E87128, 0x70284E08, 0xF7FF5630, 0x7828F9CD,
    0xF7FE5630, 0x4601FF85, 0xE8BD4620, 0xF7FD4070,
    0xBF00BA97, 0x20414000, 0x00412DAC, 0x0A070400,
    0xEC13100D, 0xF215EF13, 0xF71BF617, 0xF91EF81D,
    0xFB22FA20, 0xFD27FC24, 0xFF2FFE2B, 0x7F3E0034,
    0xF011ECFF, 0xF717F313, 0xFD1BFA18, 0x0125001E,
    0x032F022B, 0x7F3D0434, 0x000000FF, 0xE6000000,
    0xF911F010, 0x0113FE12, 0x051A0415, 0x07240620,
    0x092E0828, 0x7F3E0A34, 0x000000FF, 0x00000000,
    0x0041151D, 0x00412E6B, 0x00000082, 0x00411555,
    0x00412E66, 0x000000D1, 0x0A060300, 0x13100D0D,
    0x00000030, 0x00412EB0, 0x00411685, 0x00412E5F,
    0x000000D0, 0x00411711, 0x00412E6E, 0x0000004A,
    0x00535341, 0x705F6672, 0x68637461, 0x4200632E,
    0x3031424C, 0x31420042, 0x42004236, 0x42420042,
    0x48484848, 0x5F636C00, 0x63746170, 0x00632E68,
    0x615F646C, 0x705F6C63, 0x68637461, 0x7300632E,
    0x615F6863, 0x705F6272, 0x68637461, 0x7300632E,
    0x705F6863, 0x5F676F72, 0x63746170, 0x00632E68,
    0x37423242, 0x6C004239, 0x705F646C, 0x68637461,
    0x0000632E, 0x004117A1, 0x00412E6C, 0x00000041,
};
void lcpu_patch_install()
{
#ifdef SOC_BF0_HCPU
    memset((void *)(LCPU_PATCH_RAM_START_ADDR_S), 0, LCPU_PATCH_RAM_SIZE);

    memcpy((void *)(LCPU_PATCH_RAM_EXTRA_START_ADDR_S), g_lcpu_patch_bin, sizeof(g_lcpu_patch_bin));

    memset((void *)(LCPU_PATCH_ROM_RAM_START_ADDR_S), 0, sizeof(g_lcpu_patch_ram_bin));

    memcpy((void *)(LCPU_PATCH_ROM_RAM_START_ADDR_S), g_lcpu_patch_ram_bin, sizeof(g_lcpu_patch_ram_bin));

#else
    memset((void *)(LCPU_PATCH_RAM_START_ADDR), 0, LCPU_PATCH_RAM_SIZE);
    memcpy((void *)(LCPU_PATCH_RAM_EXTRA_START_ADDR), g_lcpu_patch_bin, sizeof(g_lcpu_patch_bin));

#endif
    memcpy((void *)(LCPU_PATCH_RECORD_ADDR), g_lcpu_patch_list, sizeof(g_lcpu_patch_list));
    HAL_PATCH_install();
}

#endif
