choice
    prompt "Select board"
    default BSP_USING_BOARD_EC_LB555XXX
    config BSP_USING_BOARD_EH_SS6500XXX
        depends on SOC_SF32LB52X
        bool "SS6500XXX HDK"    
    config BSP_USING_BOARD_EH_LB523XXX
        depends on SOC_SF32LB52X
        bool "SF32LB523XXX HDK"    
    config BSP_USING_BOARD_EH_SS6600XXX
        depends on SOC_SF32LB55X
        bool "SS6600XXX HDK"
    config BSP_USING_BOARD_EC_LB551XXX
        depends on SOC_SF32LB55X
        bool "SF32LB551XXX EVB"
    config BSP_USING_BOARD_EC_LB555XXX
        depends on SOC_SF32LB55X
        bool "SF32LB555XXX EVB"
    config BSP_USING_BOARD_EH_LB555
        depends on SOC_SF32LB55X
        bool "SF32LB555 HDK"
    config BSP_USING_BOARD_EC_LB557XXX
        depends on SOC_SF32LB55X
        bool "SF32LB557XXX EVB"
    config BSP_USING_BOARD_EC_LB561XXX
        depends on SOC_SF32LB56X
        bool "SF32LB561XXX EVB"
    config BSP_USING_BOARD_EH_LB561XXX
        depends on SOC_SF32LB56X
        bool "SF32LB561XXX HDK"    
    config BSP_USING_BOARD_EC_LB563XXX
        depends on SOC_SF32LB56X
        bool "SF32LB563XXX EVB"
    config BSP_USING_BOARD_EH_LB563XXX
        depends on SOC_SF32LB56X
        bool "SF32LB563XXX HDK" 
    config BSP_USING_BOARD_EC_LB567XXX
        depends on SOC_SF32LB56X
        bool "SF32LB567XXX EVB"
    config BSP_USING_BOARD_EC_SS6700XXX
        select BPS_V33
        depends on SOC_SF32LB56X
        bool "SS6700XXX EVB"
    config BSP_USING_BOARD_EH_SS6700XXX
        select BPS_V33
        depends on SOC_SF32LB56X
        bool "SS6700XXX HDK" 
    config BSP_USING_BOARD_EC_LB583XXX
        depends on SOC_SF32LB58X
        bool "SF32LB583XXX EVB"
    config BSP_USING_BOARD_EC_LB587XXX
        depends on SOC_SF32LB58X
        bool "SF32LB587XXX EVB"
    config BSP_USING_PC_SIMULATOR
        depends on SOC_SIMULATOR
        bool "PC Simulator"
    config BSP_USING_BOARD_CUSTOMER
        bool "Sifli Solution customer board"
    config BSP_USING_NON_SIFLI_BOARD
        bool "non-SiFli board"
endchoice

if !BSP_USING_PC_SIMULATOR

    menu "Board Config"
        config LXT_DISABLE
        bool "Low power crystal disabled"
        default n

        if !LXT_DISABLE
            config LXT_FREQ
            int "Low power crystal frequency"
            default 32768
        endif

        config BPS_V33
            bool "Board with Power Supply 3.3 V for VDD1"
            depends on !SOC_SF32LB52X
            default n

        config LXT_LP_CYCLE
        int
        default 200

        menuconfig BT_TX_POWER_VAL
            bool "Select BT RF TX power. Range: 0dbm to 13dbm"
            default y
            depends on (BF0_HCPU && (SOC_SF32LB52X || SOC_SF32LB56X || SOC_SF32LB58X))
            config BT_TX_POWER_VAL_MAX
                int "Select MAXIMUM TX power."
                depends on (BT_TX_POWER_VAL && (SOC_SF32LB56X || SOC_SF32LB58X))
                default 10
            config BT_TX_POWER_VAL_MAX
                int "Select BLE MAX TX power."
                depends on (BT_TX_POWER_VAL && (SOC_SF32LB52X))
                default 10
            config BT_TX_POWER_VAL_MIN
                int "Select MINIMUM TX power."
                depends on BT_TX_POWER_VAL
                default 0
            config BT_TX_POWER_VAL_INIT
                int "Select INIT TX power."
                depends on (BT_TX_POWER_VAL && (SOC_SF32LB56X || SOC_SF32LB58X))
                default 0
            config BT_TX_POWER_VAL_INIT
                int "Select classic BT MAX TX power.IF this value less than BLE MAX TX power,than classic BT max power is equal to BLE MAX TX power"
                depends on (BT_TX_POWER_VAL && (SOC_SF32LB52X))
                default 0
        config BLE_TX_POWER_VAL
            int "Select BLE TX power. Range: -10dbm to 10dbm"
            depends on !(SOC_SF32LB52X || SOC_SF32LB56X || SOC_SF32LB58X)
            default 0

        config BSP_CHIP_ID_COMPATIBLE
            depends on SOC_SF32LB55X
            bool "Support working with different LB55X chip ID"
            default n

        config BSP_LB55X_CHIP_ID
            depends on SOC_SF32LB55X
            int "LB55x CHIP ID"
            default 3
    endmenu
endif

if BSP_USING_BOARD_EH_LB523XXX ||  BSP_USING_BOARD_EH_SS6500XXX
    source "$SIFLI_SDK/customer/boards/eh-lb52xu/Kconfig"
endif

if BSP_USING_BOARD_EH_SS6600XXX
    source "$SIFLI_SDK/customer/boards/eh-ss6600xxx/Kconfig"
endif
if BSP_USING_BOARD_EC_LB551XXX || SIFLI_LB551
    source "$SIFLI_SDK/customer/boards/ec-lb551xxx/Kconfig"
endif
if BSP_USING_BOARD_EC_LB555XXX 
    source "$SIFLI_SDK/customer/boards/ec-lb555XXX/Kconfig"
endif
if BSP_USING_BOARD_EH_LB555 
    source "$SIFLI_SDK/customer/boards/eh-lb555/Kconfig"
endif
if BSP_USING_BOARD_EC_LB557XXX || SIFLI_LB557
    source "$SIFLI_SDK/customer/boards/ec-lb557XXX/Kconfig"
endif

if BSP_USING_BOARD_EC_LB561XXX || SIFLI_LB561
    source "$SIFLI_SDK/customer/boards/ec-lb561XXX/Kconfig"
endif
if BSP_USING_BOARD_EC_LB563XXX || SIFLI_LB563 || BSP_USING_BOARD_EC_SS6700XXX
    source "$SIFLI_SDK/customer/boards/ec-lb563XXX/Kconfig"
endif

if BSP_USING_BOARD_EH_LB563XXX ||  BSP_USING_BOARD_EH_SS6700XXX || BSP_USING_BOARD_EH_LB561XXX || BSP_USING_BOARD_EH_LB564XXX
    source "$SIFLI_SDK/customer/boards/eh-lb56xu/Kconfig"
endif

if BSP_USING_BOARD_EC_LB567XXX || SIFLI_LB567
    source "$SIFLI_SDK/customer/boards/ec-lb567XXX/Kconfig"
endif

if BSP_USING_BOARD_EC_LB583XXX || SIFLI_LB583
    source "$SIFLI_SDK/customer/boards/ec-lb583XXX/Kconfig"
endif
if BSP_USING_BOARD_EC_LB587XXX || SIFLI_LB587
    source "$SIFLI_SDK/customer/boards/ec-lb587XXX/Kconfig"
endif


if BSP_USING_PC_SIMULATOR
    source "$SIFLI_SDK/customer/boards/pc/Kconfig"
endif
if !BSP_USING_PC_SIMULATOR
    config MEMCPY_NON_DMA
        bool "Do not use DMA in memcpy/memset"
        default n
    config PSRAM_CACHE_WB
        bool "Use PSRAM Cache write back"
        select MEMCPY_NON_DMA if PSRAM_CACHE_WB
        default n        
    config BSP_USING_LCD
        bool "Enable LCD on the board"
            select BSP_USING_EPIC
            select BSP_USING_LCDC
            select BSP_USING_TOUCHD if BSP_USING_TOUCH
            default n
    if !FPGA
        source "$SIFLI_SDK/customer/boards/Kconfig_lcd"
    endif
    source "$SIFLI_SDK/customer/boards/Kconfig_drv"
    menu "Select board peripherals"
        source "$SIFLI_SDK/customer/peripherals/Kconfig"
    endmenu
endif
comment "------------End of Board configuration-----------"

