/**
  ******************************************************************************
  * @file   rtc.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __BLE_MAC_H
#define __BLE_MAC_H

typedef struct
{
    __IO uint32_t RWBLECNTL;
    __IO uint32_t VERSION;
    __IO uint32_t RWBLECONF;
    __IO uint32_t INTCNTL;
    __IO uint32_t INTSTAT;
    __IO uint32_t INTRAWSTAT;
    __IO uint32_t INTACK;
    __IO uint32_t SLOTCLK;
    __IO uint32_t FINETIMECNT;
    __IO uint32_t ET_CURRENTRXDESCPTR;
    __IO uint32_t RSVD10[2];
    __IO uint32_t DEEPSLCNTL;
    __IO uint32_t RSVD9[3];
    __IO uint32_t FINECNTCORR;
    __IO uint32_t CLKNCNTCORR;
    __IO uint32_t RSVD8[2];
    __IO uint32_t DIAGCNTL;
    __IO uint32_t DIAGSTAT;
    __IO uint32_t DEBUGADDMAX;
    __IO uint32_t DEBUGADDMIN;
    __IO uint32_t ERRORTYPESTAT;
    __IO uint32_t SWPROFILING;
    __IO uint32_t RSVD7[2];
    __IO uint32_t RADIOCNTL0;
    __IO uint32_t RSVD6;
    __IO uint32_t RADIOCNTL2;
    __IO uint32_t RADIOCNTL3;
    __IO uint32_t RADIOPWRUPDN0;
    __IO uint32_t RADIOPWRUPDN1;
    __IO uint32_t RADIOPWRUPDN2;
    __IO uint32_t RADIOPWRUPDN3;
    __IO uint32_t RADIOTXRXTIM0;
    __IO uint32_t RADIOTXRXTIM1;
    __IO uint32_t RADIOTXRXTIM2;
    __IO uint32_t RADIOTXRXTIM3;
    __IO uint32_t RSVD5[4];
    __IO uint32_t AESCNTL;
    __IO uint32_t AESKEY31_0;
    __IO uint32_t AESKEY63_32;
    __IO uint32_t AESKEY95_64;
    __IO uint32_t AESKEY127_96;
    __IO uint32_t AESPTR;
    __IO uint32_t TXMICVAL;
    __IO uint32_t RXMICVAL;
    __IO uint32_t RFTESTCNTL;
    __IO uint32_t RFTESTTXSTAT;
    __IO uint32_t RFTESTRXSTAT;
    __IO uint32_t RFTESTSYNCWORD;
    __IO uint32_t TIMGENCNTL;
    __IO uint32_t GROSSTIMTGT;
    __IO uint32_t FINETIMTGT;
    __IO uint32_t CLKNTGT;
    __IO uint32_t HMICROSECTGT;
    __IO uint32_t RSVD4[3];
    __IO uint32_t LESCHCNTL;
    __IO uint32_t STARTEVTCLKN;
    __IO uint32_t STARTEVTFINECNT;
    __IO uint32_t ENDEVTCLKN;
    __IO uint32_t ENDEVTFINECNT;
    __IO uint32_t SKIPEVTCLKN;
    __IO uint32_t SKIPEVTFINECNT;
    __IO uint32_t RSVD3;
    __IO uint32_t ADVTIM;
    __IO uint32_t ACTSCANCNTL;
    __IO uint32_t RSVD2[2];
    __IO uint32_t WLCNTL;
    __IO uint32_t WLCURRENT;
    __IO uint32_t PERADVLCNTL;
    __IO uint32_t PERADVLCURRENT;
    __IO uint32_t ADILCNTL;
    __IO uint32_t ADILCURRENT;
    __IO uint32_t SEARCH_TO;
    __IO uint32_t RSVD1[9];
    __IO uint32_t RALCNTL;
    __IO uint32_t RALCURRENT;
    __IO uint32_t RAL_LOCAL_RND;
    __IO uint32_t RAL_PEER_RND;
    __IO uint32_t RCCAL_CTRL;
    __IO uint32_t RCCAL_RESULT;
} BLE_MAC_TypeDef;


/*************** Bit definition for BLE_MAC_RWBLECNTL register ****************/
#define BLE_MAC_RWBLECNTL_RXWINSZDEF_Pos  (0U)
#define BLE_MAC_RWBLECNTL_RXWINSZDEF_Msk  (0xFUL << BLE_MAC_RWBLECNTL_RXWINSZDEF_Pos)
#define BLE_MAC_RWBLECNTL_RXWINSZDEF    BLE_MAC_RWBLECNTL_RXWINSZDEF_Msk
#define BLE_MAC_RWBLECNTL_RWBLE_EN_Pos  (8U)
#define BLE_MAC_RWBLECNTL_RWBLE_EN_Msk  (0x1UL << BLE_MAC_RWBLECNTL_RWBLE_EN_Pos)
#define BLE_MAC_RWBLECNTL_RWBLE_EN      BLE_MAC_RWBLECNTL_RWBLE_EN_Msk
#define BLE_MAC_RWBLECNTL_ADVERTFILT_EN_Pos  (9U)
#define BLE_MAC_RWBLECNTL_ADVERTFILT_EN_Msk  (0x1UL << BLE_MAC_RWBLECNTL_ADVERTFILT_EN_Pos)
#define BLE_MAC_RWBLECNTL_ADVERTFILT_EN  BLE_MAC_RWBLECNTL_ADVERTFILT_EN_Msk
#define BLE_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Pos  (10U)
#define BLE_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Msk  (0x1UL << BLE_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Pos)
#define BLE_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN  BLE_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Msk
#define BLE_MAC_RWBLECNTL_HOP_REMAP_DSB_Pos  (12U)
#define BLE_MAC_RWBLECNTL_HOP_REMAP_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_HOP_REMAP_DSB_Pos)
#define BLE_MAC_RWBLECNTL_HOP_REMAP_DSB  BLE_MAC_RWBLECNTL_HOP_REMAP_DSB_Msk
#define BLE_MAC_RWBLECNTL_CRC_DSB_Pos   (13U)
#define BLE_MAC_RWBLECNTL_CRC_DSB_Msk   (0x1UL << BLE_MAC_RWBLECNTL_CRC_DSB_Pos)
#define BLE_MAC_RWBLECNTL_CRC_DSB       BLE_MAC_RWBLECNTL_CRC_DSB_Msk
#define BLE_MAC_RWBLECNTL_WHIT_DSB_Pos  (14U)
#define BLE_MAC_RWBLECNTL_WHIT_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_WHIT_DSB_Pos)
#define BLE_MAC_RWBLECNTL_WHIT_DSB      BLE_MAC_RWBLECNTL_WHIT_DSB_Msk
#define BLE_MAC_RWBLECNTL_LRFEC_DSB_Pos  (15U)
#define BLE_MAC_RWBLECNTL_LRFEC_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_LRFEC_DSB_Pos)
#define BLE_MAC_RWBLECNTL_LRFEC_DSB     BLE_MAC_RWBLECNTL_LRFEC_DSB_Msk
#define BLE_MAC_RWBLECNTL_LRPMAP_DSB_Pos  (16U)
#define BLE_MAC_RWBLECNTL_LRPMAP_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_LRPMAP_DSB_Pos)
#define BLE_MAC_RWBLECNTL_LRPMAP_DSB    BLE_MAC_RWBLECNTL_LRPMAP_DSB_Msk
#define BLE_MAC_RWBLECNTL_CRYPT_DSB_Pos  (17U)
#define BLE_MAC_RWBLECNTL_CRYPT_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_CRYPT_DSB_Pos)
#define BLE_MAC_RWBLECNTL_CRYPT_DSB     BLE_MAC_RWBLECNTL_CRYPT_DSB_Msk
#define BLE_MAC_RWBLECNTL_NESN_DSB_Pos  (18U)
#define BLE_MAC_RWBLECNTL_NESN_DSB_Msk  (0x1UL << BLE_MAC_RWBLECNTL_NESN_DSB_Pos)
#define BLE_MAC_RWBLECNTL_NESN_DSB      BLE_MAC_RWBLECNTL_NESN_DSB_Msk
#define BLE_MAC_RWBLECNTL_SN_DSB_Pos    (19U)
#define BLE_MAC_RWBLECNTL_SN_DSB_Msk    (0x1UL << BLE_MAC_RWBLECNTL_SN_DSB_Pos)
#define BLE_MAC_RWBLECNTL_SN_DSB        BLE_MAC_RWBLECNTL_SN_DSB_Msk
#define BLE_MAC_RWBLECNTL_MD_DSB_Pos    (20U)
#define BLE_MAC_RWBLECNTL_MD_DSB_Msk    (0x1UL << BLE_MAC_RWBLECNTL_MD_DSB_Pos)
#define BLE_MAC_RWBLECNTL_MD_DSB        BLE_MAC_RWBLECNTL_MD_DSB_Msk
#define BLE_MAC_RWBLECNTL_SCAN_ABORT_Pos  (24U)
#define BLE_MAC_RWBLECNTL_SCAN_ABORT_Msk  (0x1UL << BLE_MAC_RWBLECNTL_SCAN_ABORT_Pos)
#define BLE_MAC_RWBLECNTL_SCAN_ABORT    BLE_MAC_RWBLECNTL_SCAN_ABORT_Msk
#define BLE_MAC_RWBLECNTL_ADVERT_ABORT_Pos  (25U)
#define BLE_MAC_RWBLECNTL_ADVERT_ABORT_Msk  (0x1UL << BLE_MAC_RWBLECNTL_ADVERT_ABORT_Pos)
#define BLE_MAC_RWBLECNTL_ADVERT_ABORT  BLE_MAC_RWBLECNTL_ADVERT_ABORT_Msk
#define BLE_MAC_RWBLECNTL_RFTEST_ABORT_Pos  (26U)
#define BLE_MAC_RWBLECNTL_RFTEST_ABORT_Msk  (0x1UL << BLE_MAC_RWBLECNTL_RFTEST_ABORT_Pos)
#define BLE_MAC_RWBLECNTL_RFTEST_ABORT  BLE_MAC_RWBLECNTL_RFTEST_ABORT_Msk
#define BLE_MAC_RWBLECNTL_SWINT_REQ_Pos  (27U)
#define BLE_MAC_RWBLECNTL_SWINT_REQ_Msk  (0x1UL << BLE_MAC_RWBLECNTL_SWINT_REQ_Pos)
#define BLE_MAC_RWBLECNTL_SWINT_REQ     BLE_MAC_RWBLECNTL_SWINT_REQ_Msk
#define BLE_MAC_RWBLECNTL_RADIOCNTL_SOFT_RST_Pos  (28U)
#define BLE_MAC_RWBLECNTL_RADIOCNTL_SOFT_RST_Msk  (0x1UL << BLE_MAC_RWBLECNTL_RADIOCNTL_SOFT_RST_Pos)
#define BLE_MAC_RWBLECNTL_RADIOCNTL_SOFT_RST  BLE_MAC_RWBLECNTL_RADIOCNTL_SOFT_RST_Msk
#define BLE_MAC_RWBLECNTL_MASTER_TGSOFT_RST_Pos  (30U)
#define BLE_MAC_RWBLECNTL_MASTER_TGSOFT_RST_Msk  (0x1UL << BLE_MAC_RWBLECNTL_MASTER_TGSOFT_RST_Pos)
#define BLE_MAC_RWBLECNTL_MASTER_TGSOFT_RST  BLE_MAC_RWBLECNTL_MASTER_TGSOFT_RST_Msk
#define BLE_MAC_RWBLECNTL_MASTER_SOFT_RST_Pos  (31U)
#define BLE_MAC_RWBLECNTL_MASTER_SOFT_RST_Msk  (0x1UL << BLE_MAC_RWBLECNTL_MASTER_SOFT_RST_Pos)
#define BLE_MAC_RWBLECNTL_MASTER_SOFT_RST  BLE_MAC_RWBLECNTL_MASTER_SOFT_RST_Msk

/**************** Bit definition for BLE_MAC_VERSION register *****************/
#define BLE_MAC_VERSION_BUILD_NUM_Pos   (0U)
#define BLE_MAC_VERSION_BUILD_NUM_Msk   (0xFFUL << BLE_MAC_VERSION_BUILD_NUM_Pos)
#define BLE_MAC_VERSION_BUILD_NUM       BLE_MAC_VERSION_BUILD_NUM_Msk
#define BLE_MAC_VERSION_UPG_Pos         (8U)
#define BLE_MAC_VERSION_UPG_Msk         (0xFFUL << BLE_MAC_VERSION_UPG_Pos)
#define BLE_MAC_VERSION_UPG             BLE_MAC_VERSION_UPG_Msk
#define BLE_MAC_VERSION_REL_Pos         (16U)
#define BLE_MAC_VERSION_REL_Msk         (0xFFUL << BLE_MAC_VERSION_REL_Pos)
#define BLE_MAC_VERSION_REL             BLE_MAC_VERSION_REL_Msk
#define BLE_MAC_VERSION_TYP_Pos         (24U)
#define BLE_MAC_VERSION_TYP_Msk         (0xFFUL << BLE_MAC_VERSION_TYP_Pos)
#define BLE_MAC_VERSION_TYP             BLE_MAC_VERSION_TYP_Msk

/*************** Bit definition for BLE_MAC_RWBLECONF register ****************/
#define BLE_MAC_RWBLECONF_ADDR_WIDTH_Pos  (0U)
#define BLE_MAC_RWBLECONF_ADDR_WIDTH_Msk  (0x1FUL << BLE_MAC_RWBLECONF_ADDR_WIDTH_Pos)
#define BLE_MAC_RWBLECONF_ADDR_WIDTH    BLE_MAC_RWBLECONF_ADDR_WIDTH_Msk
#define BLE_MAC_RWBLECONF_DATA_WIDTH_Pos  (5U)
#define BLE_MAC_RWBLECONF_DATA_WIDTH_Msk  (0x1UL << BLE_MAC_RWBLECONF_DATA_WIDTH_Pos)
#define BLE_MAC_RWBLECONF_DATA_WIDTH    BLE_MAC_RWBLECONF_DATA_WIDTH_Msk
#define BLE_MAC_RWBLECONF_BUS_TYPE_Pos  (6U)
#define BLE_MAC_RWBLECONF_BUS_TYPE_Msk  (0x1UL << BLE_MAC_RWBLECONF_BUS_TYPE_Pos)
#define BLE_MAC_RWBLECONF_BUS_TYPE      BLE_MAC_RWBLECONF_BUS_TYPE_Msk
#define BLE_MAC_RWBLECONF_INTMODE_Pos   (7U)
#define BLE_MAC_RWBLECONF_INTMODE_Msk   (0x1UL << BLE_MAC_RWBLECONF_INTMODE_Pos)
#define BLE_MAC_RWBLECONF_INTMODE       BLE_MAC_RWBLECONF_INTMODE_Msk
#define BLE_MAC_RWBLECONF_CLK_SEL_Pos   (8U)
#define BLE_MAC_RWBLECONF_CLK_SEL_Msk   (0x3FUL << BLE_MAC_RWBLECONF_CLK_SEL_Pos)
#define BLE_MAC_RWBLECONF_CLK_SEL       BLE_MAC_RWBLECONF_CLK_SEL_Msk
#define BLE_MAC_RWBLECONF_DECIPHER_Pos  (14U)
#define BLE_MAC_RWBLECONF_DECIPHER_Msk  (0x1UL << BLE_MAC_RWBLECONF_DECIPHER_Pos)
#define BLE_MAC_RWBLECONF_DECIPHER      BLE_MAC_RWBLECONF_DECIPHER_Msk
#define BLE_MAC_RWBLECONF_USEDBG_Pos    (15U)
#define BLE_MAC_RWBLECONF_USEDBG_Msk    (0x1UL << BLE_MAC_RWBLECONF_USEDBG_Pos)
#define BLE_MAC_RWBLECONF_USEDBG        BLE_MAC_RWBLECONF_USEDBG_Msk
#define BLE_MAC_RWBLECONF_WLANCOEX_Pos  (21U)
#define BLE_MAC_RWBLECONF_WLANCOEX_Msk  (0x1UL << BLE_MAC_RWBLECONF_WLANCOEX_Pos)
#define BLE_MAC_RWBLECONF_WLANCOEX      BLE_MAC_RWBLECONF_WLANCOEX_Msk
#define BLE_MAC_RWBLECONF_ISOPORTNB_Pos  (24U)
#define BLE_MAC_RWBLECONF_ISOPORTNB_Msk  (0x3UL << BLE_MAC_RWBLECONF_ISOPORTNB_Pos)
#define BLE_MAC_RWBLECONF_ISOPORTNB     BLE_MAC_RWBLECONF_ISOPORTNB_Msk
#define BLE_MAC_RWBLECONF_USETXLR_Pos   (26U)
#define BLE_MAC_RWBLECONF_USETXLR_Msk   (0x1UL << BLE_MAC_RWBLECONF_USETXLR_Pos)
#define BLE_MAC_RWBLECONF_USETXLR       BLE_MAC_RWBLECONF_USETXLR_Msk
#define BLE_MAC_RWBLECONF_USERXLR_Pos   (27U)
#define BLE_MAC_RWBLECONF_USERXLR_Msk   (0x1UL << BLE_MAC_RWBLECONF_USERXLR_Pos)
#define BLE_MAC_RWBLECONF_USERXLR       BLE_MAC_RWBLECONF_USERXLR_Msk
#define BLE_MAC_RWBLECONF_CORRELATOR_Pos  (28U)
#define BLE_MAC_RWBLECONF_CORRELATOR_Msk  (0x1UL << BLE_MAC_RWBLECONF_CORRELATOR_Pos)
#define BLE_MAC_RWBLECONF_CORRELATOR    BLE_MAC_RWBLECONF_CORRELATOR_Msk
#define BLE_MAC_RWBLECONF_DMMODE_Pos    (31U)
#define BLE_MAC_RWBLECONF_DMMODE_Msk    (0x1UL << BLE_MAC_RWBLECONF_DMMODE_Pos)
#define BLE_MAC_RWBLECONF_DMMODE        BLE_MAC_RWBLECONF_DMMODE_Msk

/**************** Bit definition for BLE_MAC_INTCNTL register *****************/
#define BLE_MAC_INTCNTL_CLKNINTMSK_Pos  (0U)
#define BLE_MAC_INTCNTL_CLKNINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_CLKNINTMSK_Pos)
#define BLE_MAC_INTCNTL_CLKNINTMSK      BLE_MAC_INTCNTL_CLKNINTMSK_Msk
#define BLE_MAC_INTCNTL_TXINTMSK_Pos    (1U)
#define BLE_MAC_INTCNTL_TXINTMSK_Msk    (0x1UL << BLE_MAC_INTCNTL_TXINTMSK_Pos)
#define BLE_MAC_INTCNTL_TXINTMSK        BLE_MAC_INTCNTL_TXINTMSK_Msk
#define BLE_MAC_INTCNTL_RXINTMSK_Pos    (2U)
#define BLE_MAC_INTCNTL_RXINTMSK_Msk    (0x1UL << BLE_MAC_INTCNTL_RXINTMSK_Pos)
#define BLE_MAC_INTCNTL_RXINTMSK        BLE_MAC_INTCNTL_RXINTMSK_Msk
#define BLE_MAC_INTCNTL_SLPINTMSK_Pos   (3U)
#define BLE_MAC_INTCNTL_SLPINTMSK_Msk   (0x1UL << BLE_MAC_INTCNTL_SLPINTMSK_Pos)
#define BLE_MAC_INTCNTL_SLPINTMSK       BLE_MAC_INTCNTL_SLPINTMSK_Msk
#define BLE_MAC_INTCNTL_STARTEVTINTMSK_Pos  (4U)
#define BLE_MAC_INTCNTL_STARTEVTINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_STARTEVTINTMSK_Pos)
#define BLE_MAC_INTCNTL_STARTEVTINTMSK  BLE_MAC_INTCNTL_STARTEVTINTMSK_Msk
#define BLE_MAC_INTCNTL_ENDEVTINTMSK_Pos  (5U)
#define BLE_MAC_INTCNTL_ENDEVTINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_ENDEVTINTMSK_Pos)
#define BLE_MAC_INTCNTL_ENDEVTINTMSK    BLE_MAC_INTCNTL_ENDEVTINTMSK_Msk
#define BLE_MAC_INTCNTL_SKIPEVTINTMSK_Pos  (6U)
#define BLE_MAC_INTCNTL_SKIPEVTINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_SKIPEVTINTMSK_Pos)
#define BLE_MAC_INTCNTL_SKIPEVTINTMSK   BLE_MAC_INTCNTL_SKIPEVTINTMSK_Msk
#define BLE_MAC_INTCNTL_CRYPTINTMSK_Pos  (7U)
#define BLE_MAC_INTCNTL_CRYPTINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_CRYPTINTMSK_Pos)
#define BLE_MAC_INTCNTL_CRYPTINTMSK     BLE_MAC_INTCNTL_CRYPTINTMSK_Msk
#define BLE_MAC_INTCNTL_ERRORINTMSK_Pos  (8U)
#define BLE_MAC_INTCNTL_ERRORINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_ERRORINTMSK_Pos)
#define BLE_MAC_INTCNTL_ERRORINTMSK     BLE_MAC_INTCNTL_ERRORINTMSK_Msk
#define BLE_MAC_INTCNTL_GROSSTGTIMINTMSK_Pos  (9U)
#define BLE_MAC_INTCNTL_GROSSTGTIMINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_GROSSTGTIMINTMSK_Pos)
#define BLE_MAC_INTCNTL_GROSSTGTIMINTMSK  BLE_MAC_INTCNTL_GROSSTGTIMINTMSK_Msk
#define BLE_MAC_INTCNTL_FINETGTIMINTMSK_Pos  (10U)
#define BLE_MAC_INTCNTL_FINETGTIMINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_FINETGTIMINTMSK_Pos)
#define BLE_MAC_INTCNTL_FINETGTIMINTMSK  BLE_MAC_INTCNTL_FINETGTIMINTMSK_Msk
#define BLE_MAC_INTCNTL_TIMESTAMPTGTINTMSK_Pos  (11U)
#define BLE_MAC_INTCNTL_TIMESTAMPTGTINTMSK_Msk  (0x1UL << BLE_MAC_INTCNTL_TIMESTAMPTGTINTMSK_Pos)
#define BLE_MAC_INTCNTL_TIMESTAMPTGTINTMSK  BLE_MAC_INTCNTL_TIMESTAMPTGTINTMSK_Msk
#define BLE_MAC_INTCNTL_SWINTMSK_Pos    (12U)
#define BLE_MAC_INTCNTL_SWINTMSK_Msk    (0x1UL << BLE_MAC_INTCNTL_SWINTMSK_Pos)
#define BLE_MAC_INTCNTL_SWINTMSK        BLE_MAC_INTCNTL_SWINTMSK_Msk
#define BLE_MAC_INTCNTL_CLKNINTSRVAL_Pos  (24U)
#define BLE_MAC_INTCNTL_CLKNINTSRVAL_Msk  (0xFUL << BLE_MAC_INTCNTL_CLKNINTSRVAL_Pos)
#define BLE_MAC_INTCNTL_CLKNINTSRVAL    BLE_MAC_INTCNTL_CLKNINTSRVAL_Msk
#define BLE_MAC_INTCNTL_CLKNINTSRMSK_Pos  (28U)
#define BLE_MAC_INTCNTL_CLKNINTSRMSK_Msk  (0x7UL << BLE_MAC_INTCNTL_CLKNINTSRMSK_Pos)
#define BLE_MAC_INTCNTL_CLKNINTSRMSK    BLE_MAC_INTCNTL_CLKNINTSRMSK_Msk

/**************** Bit definition for BLE_MAC_INTSTAT register *****************/
#define BLE_MAC_INTSTAT_CLKNINTSTAT_Pos  (0U)
#define BLE_MAC_INTSTAT_CLKNINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_CLKNINTSTAT_Pos)
#define BLE_MAC_INTSTAT_CLKNINTSTAT     BLE_MAC_INTSTAT_CLKNINTSTAT_Msk
#define BLE_MAC_INTSTAT_TXINTSTAT_Pos   (1U)
#define BLE_MAC_INTSTAT_TXINTSTAT_Msk   (0x1UL << BLE_MAC_INTSTAT_TXINTSTAT_Pos)
#define BLE_MAC_INTSTAT_TXINTSTAT       BLE_MAC_INTSTAT_TXINTSTAT_Msk
#define BLE_MAC_INTSTAT_RXINTSTAT_Pos   (2U)
#define BLE_MAC_INTSTAT_RXINTSTAT_Msk   (0x1UL << BLE_MAC_INTSTAT_RXINTSTAT_Pos)
#define BLE_MAC_INTSTAT_RXINTSTAT       BLE_MAC_INTSTAT_RXINTSTAT_Msk
#define BLE_MAC_INTSTAT_SLPINTSTAT_Pos  (3U)
#define BLE_MAC_INTSTAT_SLPINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_SLPINTSTAT_Pos)
#define BLE_MAC_INTSTAT_SLPINTSTAT      BLE_MAC_INTSTAT_SLPINTSTAT_Msk
#define BLE_MAC_INTSTAT_STARTEVTINTSTAT_Pos  (4U)
#define BLE_MAC_INTSTAT_STARTEVTINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_STARTEVTINTSTAT_Pos)
#define BLE_MAC_INTSTAT_STARTEVTINTSTAT  BLE_MAC_INTSTAT_STARTEVTINTSTAT_Msk
#define BLE_MAC_INTSTAT_ENDEVTINTSTAT_Pos  (5U)
#define BLE_MAC_INTSTAT_ENDEVTINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_ENDEVTINTSTAT_Pos)
#define BLE_MAC_INTSTAT_ENDEVTINTSTAT   BLE_MAC_INTSTAT_ENDEVTINTSTAT_Msk
#define BLE_MAC_INTSTAT_SKIPEVTINTSTAT_Pos  (6U)
#define BLE_MAC_INTSTAT_SKIPEVTINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_SKIPEVTINTSTAT_Pos)
#define BLE_MAC_INTSTAT_SKIPEVTINTSTAT  BLE_MAC_INTSTAT_SKIPEVTINTSTAT_Msk
#define BLE_MAC_INTSTAT_CRYPTINTSTAT_Pos  (7U)
#define BLE_MAC_INTSTAT_CRYPTINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_CRYPTINTSTAT_Pos)
#define BLE_MAC_INTSTAT_CRYPTINTSTAT    BLE_MAC_INTSTAT_CRYPTINTSTAT_Msk
#define BLE_MAC_INTSTAT_ERRORINTSTAT_Pos  (8U)
#define BLE_MAC_INTSTAT_ERRORINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_ERRORINTSTAT_Pos)
#define BLE_MAC_INTSTAT_ERRORINTSTAT    BLE_MAC_INTSTAT_ERRORINTSTAT_Msk
#define BLE_MAC_INTSTAT_GROSSTGTIMINTSTAT_Pos  (9U)
#define BLE_MAC_INTSTAT_GROSSTGTIMINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_GROSSTGTIMINTSTAT_Pos)
#define BLE_MAC_INTSTAT_GROSSTGTIMINTSTAT  BLE_MAC_INTSTAT_GROSSTGTIMINTSTAT_Msk
#define BLE_MAC_INTSTAT_FINETGTIMINTSTAT_Pos  (10U)
#define BLE_MAC_INTSTAT_FINETGTIMINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_FINETGTIMINTSTAT_Pos)
#define BLE_MAC_INTSTAT_FINETGTIMINTSTAT  BLE_MAC_INTSTAT_FINETGTIMINTSTAT_Msk
#define BLE_MAC_INTSTAT_TIMESTAMPTGTINTSTAT_Pos  (11U)
#define BLE_MAC_INTSTAT_TIMESTAMPTGTINTSTAT_Msk  (0x1UL << BLE_MAC_INTSTAT_TIMESTAMPTGTINTSTAT_Pos)
#define BLE_MAC_INTSTAT_TIMESTAMPTGTINTSTAT  BLE_MAC_INTSTAT_TIMESTAMPTGTINTSTAT_Msk
#define BLE_MAC_INTSTAT_SWINTSTAT_Pos   (12U)
#define BLE_MAC_INTSTAT_SWINTSTAT_Msk   (0x1UL << BLE_MAC_INTSTAT_SWINTSTAT_Pos)
#define BLE_MAC_INTSTAT_SWINTSTAT       BLE_MAC_INTSTAT_SWINTSTAT_Msk

/*************** Bit definition for BLE_MAC_INTRAWSTAT register ***************/
#define BLE_MAC_INTRAWSTAT_CLKNINTRAWSTAT_Pos  (0U)
#define BLE_MAC_INTRAWSTAT_CLKNINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_CLKNINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_CLKNINTRAWSTAT  BLE_MAC_INTRAWSTAT_CLKNINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_TXINTRAWSTAT_Pos  (1U)
#define BLE_MAC_INTRAWSTAT_TXINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_TXINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_TXINTRAWSTAT  BLE_MAC_INTRAWSTAT_TXINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_RXINTRAWSTAT_Pos  (2U)
#define BLE_MAC_INTRAWSTAT_RXINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_RXINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_RXINTRAWSTAT  BLE_MAC_INTRAWSTAT_RXINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_SLPINTRAWSTAT_Pos  (3U)
#define BLE_MAC_INTRAWSTAT_SLPINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_SLPINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_SLPINTRAWSTAT  BLE_MAC_INTRAWSTAT_SLPINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_STARTEVTINTRAWSTAT_Pos  (4U)
#define BLE_MAC_INTRAWSTAT_STARTEVTINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_STARTEVTINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_STARTEVTINTRAWSTAT  BLE_MAC_INTRAWSTAT_STARTEVTINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_ENDEVTINTRAWSTAT_Pos  (5U)
#define BLE_MAC_INTRAWSTAT_ENDEVTINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_ENDEVTINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_ENDEVTINTRAWSTAT  BLE_MAC_INTRAWSTAT_ENDEVTINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_SKIPEVTINTRAWSTAT_Pos  (6U)
#define BLE_MAC_INTRAWSTAT_SKIPEVTINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_SKIPEVTINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_SKIPEVTINTRAWSTAT  BLE_MAC_INTRAWSTAT_SKIPEVTINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_CRYPTINTRAWSTAT_Pos  (7U)
#define BLE_MAC_INTRAWSTAT_CRYPTINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_CRYPTINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_CRYPTINTRAWSTAT  BLE_MAC_INTRAWSTAT_CRYPTINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_ERRORINTRAWSTAT_Pos  (8U)
#define BLE_MAC_INTRAWSTAT_ERRORINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_ERRORINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_ERRORINTRAWSTAT  BLE_MAC_INTRAWSTAT_ERRORINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_GROSSTGTIMINTRAWSTAT_Pos  (9U)
#define BLE_MAC_INTRAWSTAT_GROSSTGTIMINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_GROSSTGTIMINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_GROSSTGTIMINTRAWSTAT  BLE_MAC_INTRAWSTAT_GROSSTGTIMINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_FINETGTIMINTRAWSTAT_Pos  (10U)
#define BLE_MAC_INTRAWSTAT_FINETGTIMINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_FINETGTIMINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_FINETGTIMINTRAWSTAT  BLE_MAC_INTRAWSTAT_FINETGTIMINTRAWSTAT_Msk
#define BLE_MAC_INTRAWSTAT_TIMESTAMPTGTIMINTSTAT_Pos  (11U)
#define BLE_MAC_INTRAWSTAT_TIMESTAMPTGTIMINTSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_TIMESTAMPTGTIMINTSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_TIMESTAMPTGTIMINTSTAT  BLE_MAC_INTRAWSTAT_TIMESTAMPTGTIMINTSTAT_Msk
#define BLE_MAC_INTRAWSTAT_SWINTRAWSTAT_Pos  (12U)
#define BLE_MAC_INTRAWSTAT_SWINTRAWSTAT_Msk  (0x1UL << BLE_MAC_INTRAWSTAT_SWINTRAWSTAT_Pos)
#define BLE_MAC_INTRAWSTAT_SWINTRAWSTAT  BLE_MAC_INTRAWSTAT_SWINTRAWSTAT_Msk

/***************** Bit definition for BLE_MAC_INTACK register *****************/
#define BLE_MAC_INTACK_CLKNTINTACK_Pos  (0U)
#define BLE_MAC_INTACK_CLKNTINTACK_Msk  (0x1UL << BLE_MAC_INTACK_CLKNTINTACK_Pos)
#define BLE_MAC_INTACK_CLKNTINTACK      BLE_MAC_INTACK_CLKNTINTACK_Msk
#define BLE_MAC_INTACK_TXINTACK_Pos     (1U)
#define BLE_MAC_INTACK_TXINTACK_Msk     (0x1UL << BLE_MAC_INTACK_TXINTACK_Pos)
#define BLE_MAC_INTACK_TXINTACK         BLE_MAC_INTACK_TXINTACK_Msk
#define BLE_MAC_INTACK_RXINTACK_Pos     (2U)
#define BLE_MAC_INTACK_RXINTACK_Msk     (0x1UL << BLE_MAC_INTACK_RXINTACK_Pos)
#define BLE_MAC_INTACK_RXINTACK         BLE_MAC_INTACK_RXINTACK_Msk
#define BLE_MAC_INTACK_SLPINTACK_Pos    (3U)
#define BLE_MAC_INTACK_SLPINTACK_Msk    (0x1UL << BLE_MAC_INTACK_SLPINTACK_Pos)
#define BLE_MAC_INTACK_SLPINTACK        BLE_MAC_INTACK_SLPINTACK_Msk
#define BLE_MAC_INTACK_STARTEVTINTACK_Pos  (4U)
#define BLE_MAC_INTACK_STARTEVTINTACK_Msk  (0x1UL << BLE_MAC_INTACK_STARTEVTINTACK_Pos)
#define BLE_MAC_INTACK_STARTEVTINTACK   BLE_MAC_INTACK_STARTEVTINTACK_Msk
#define BLE_MAC_INTACK_ENDEVTINTACK_Pos  (5U)
#define BLE_MAC_INTACK_ENDEVTINTACK_Msk  (0x1UL << BLE_MAC_INTACK_ENDEVTINTACK_Pos)
#define BLE_MAC_INTACK_ENDEVTINTACK     BLE_MAC_INTACK_ENDEVTINTACK_Msk
#define BLE_MAC_INTACK_SKIPEVTINTACK_Pos  (6U)
#define BLE_MAC_INTACK_SKIPEVTINTACK_Msk  (0x1UL << BLE_MAC_INTACK_SKIPEVTINTACK_Pos)
#define BLE_MAC_INTACK_SKIPEVTINTACK    BLE_MAC_INTACK_SKIPEVTINTACK_Msk
#define BLE_MAC_INTACK_CRYPTINTACK_Pos  (7U)
#define BLE_MAC_INTACK_CRYPTINTACK_Msk  (0x1UL << BLE_MAC_INTACK_CRYPTINTACK_Pos)
#define BLE_MAC_INTACK_CRYPTINTACK      BLE_MAC_INTACK_CRYPTINTACK_Msk
#define BLE_MAC_INTACK_ERRORINTACK_Pos  (8U)
#define BLE_MAC_INTACK_ERRORINTACK_Msk  (0x1UL << BLE_MAC_INTACK_ERRORINTACK_Pos)
#define BLE_MAC_INTACK_ERRORINTACK      BLE_MAC_INTACK_ERRORINTACK_Msk
#define BLE_MAC_INTACK_GROSSTGTIMINTACK_Pos  (9U)
#define BLE_MAC_INTACK_GROSSTGTIMINTACK_Msk  (0x1UL << BLE_MAC_INTACK_GROSSTGTIMINTACK_Pos)
#define BLE_MAC_INTACK_GROSSTGTIMINTACK  BLE_MAC_INTACK_GROSSTGTIMINTACK_Msk
#define BLE_MAC_INTACK_FINETGTIMINTACK_Pos  (10U)
#define BLE_MAC_INTACK_FINETGTIMINTACK_Msk  (0x1UL << BLE_MAC_INTACK_FINETGTIMINTACK_Pos)
#define BLE_MAC_INTACK_FINETGTIMINTACK  BLE_MAC_INTACK_FINETGTIMINTACK_Msk
#define BLE_MAC_INTACK_TIMESTAMPTGTIMINTACK_Pos  (11U)
#define BLE_MAC_INTACK_TIMESTAMPTGTIMINTACK_Msk  (0x1UL << BLE_MAC_INTACK_TIMESTAMPTGTIMINTACK_Pos)
#define BLE_MAC_INTACK_TIMESTAMPTGTIMINTACK  BLE_MAC_INTACK_TIMESTAMPTGTIMINTACK_Msk
#define BLE_MAC_INTACK_SWINTACK_Pos     (12U)
#define BLE_MAC_INTACK_SWINTACK_Msk     (0x1UL << BLE_MAC_INTACK_SWINTACK_Pos)
#define BLE_MAC_INTACK_SWINTACK         BLE_MAC_INTACK_SWINTACK_Msk

/**************** Bit definition for BLE_MAC_SLOTCLK register *****************/
#define BLE_MAC_SLOTCLK_SCLK_Pos        (0U)
#define BLE_MAC_SLOTCLK_SCLK_Msk        (0xFFFFFFFUL << BLE_MAC_SLOTCLK_SCLK_Pos)
#define BLE_MAC_SLOTCLK_SCLK            BLE_MAC_SLOTCLK_SCLK_Msk
#define BLE_MAC_SLOTCLK_CLKN_UPD_Pos    (30U)
#define BLE_MAC_SLOTCLK_CLKN_UPD_Msk    (0x1UL << BLE_MAC_SLOTCLK_CLKN_UPD_Pos)
#define BLE_MAC_SLOTCLK_CLKN_UPD        BLE_MAC_SLOTCLK_CLKN_UPD_Msk
#define BLE_MAC_SLOTCLK_SAMP_Pos        (31U)
#define BLE_MAC_SLOTCLK_SAMP_Msk        (0x1UL << BLE_MAC_SLOTCLK_SAMP_Pos)
#define BLE_MAC_SLOTCLK_SAMP            BLE_MAC_SLOTCLK_SAMP_Msk

/************** Bit definition for BLE_MAC_FINETIMECNT register ***************/
#define BLE_MAC_FINETIMECNT_FINECNT_Pos  (0U)
#define BLE_MAC_FINETIMECNT_FINECNT_Msk  (0x3FFUL << BLE_MAC_FINETIMECNT_FINECNT_Pos)
#define BLE_MAC_FINETIMECNT_FINECNT     BLE_MAC_FINETIMECNT_FINECNT_Msk

/********** Bit definition for BLE_MAC_ET_CURRENTRXDESCPTR register ***********/
#define BLE_MAC_ET_CURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos  (0U)
#define BLE_MAC_ET_CURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk  (0x7FFFUL << BLE_MAC_ET_CURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos)
#define BLE_MAC_ET_CURRENTRXDESCPTR_CURRENTRXDESCPTR  BLE_MAC_ET_CURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk
#define BLE_MAC_ET_CURRENTRXDESCPTR_ETPTR_Pos  (16U)
#define BLE_MAC_ET_CURRENTRXDESCPTR_ETPTR_Msk  (0xFFFFUL << BLE_MAC_ET_CURRENTRXDESCPTR_ETPTR_Pos)
#define BLE_MAC_ET_CURRENTRXDESCPTR_ETPTR  BLE_MAC_ET_CURRENTRXDESCPTR_ETPTR_Msk

/*************** Bit definition for BLE_MAC_DEEPSLCNTL register ***************/
#define BLE_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Pos  (3U)
#define BLE_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Msk  (0x1UL << BLE_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Pos)
#define BLE_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN  BLE_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Msk
#define BLE_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Pos  (4U)
#define BLE_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Msk  (0x1UL << BLE_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Pos)
#define BLE_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT  BLE_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Msk
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_Pos  (5U)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_Msk  (0x1UL << BLE_MAC_DEEPSLCNTL_AUTO_CORR_Pos)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR    BLE_MAC_DEEPSLCNTL_AUTO_CORR_Msk
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_CLKNINT_Pos  (6U)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_CLKNINT_Msk  (0x1UL << BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_CLKNINT_Pos)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_CLKNINT  BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_CLKNINT_Msk
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_SLPINT_Pos  (7U)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_SLPINT_Msk  (0x1UL << BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_SLPINT_Pos)
#define BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_SLPINT  BLE_MAC_DEEPSLCNTL_AUTO_CORR_MASK_SLPINT_Msk

/************** Bit definition for BLE_MAC_FINECNTCORR register ***************/
#define BLE_MAC_FINECNTCORR_FINECNTCORR_Pos  (0U)
#define BLE_MAC_FINECNTCORR_FINECNTCORR_Msk  (0x3FFUL << BLE_MAC_FINECNTCORR_FINECNTCORR_Pos)
#define BLE_MAC_FINECNTCORR_FINECNTCORR  BLE_MAC_FINECNTCORR_FINECNTCORR_Msk

/************** Bit definition for BLE_MAC_CLKNCNTCORR register ***************/
#define BLE_MAC_CLKNCNTCORR_CLKNCNTCORR_Pos  (0U)
#define BLE_MAC_CLKNCNTCORR_CLKNCNTCORR_Msk  (0xFFFFFFFUL << BLE_MAC_CLKNCNTCORR_CLKNCNTCORR_Pos)
#define BLE_MAC_CLKNCNTCORR_CLKNCNTCORR  BLE_MAC_CLKNCNTCORR_CLKNCNTCORR_Msk
#define BLE_MAC_CLKNCNTCORR_ABS_DELTA_Pos  (31U)
#define BLE_MAC_CLKNCNTCORR_ABS_DELTA_Msk  (0x1UL << BLE_MAC_CLKNCNTCORR_ABS_DELTA_Pos)
#define BLE_MAC_CLKNCNTCORR_ABS_DELTA   BLE_MAC_CLKNCNTCORR_ABS_DELTA_Msk

/**************** Bit definition for BLE_MAC_DIAGCNTL register ****************/
#define BLE_MAC_DIAGCNTL_DIAG0_Pos      (0U)
#define BLE_MAC_DIAGCNTL_DIAG0_Msk      (0x7FUL << BLE_MAC_DIAGCNTL_DIAG0_Pos)
#define BLE_MAC_DIAGCNTL_DIAG0          BLE_MAC_DIAGCNTL_DIAG0_Msk
#define BLE_MAC_DIAGCNTL_DIAG0_EN_Pos   (7U)
#define BLE_MAC_DIAGCNTL_DIAG0_EN_Msk   (0x1UL << BLE_MAC_DIAGCNTL_DIAG0_EN_Pos)
#define BLE_MAC_DIAGCNTL_DIAG0_EN       BLE_MAC_DIAGCNTL_DIAG0_EN_Msk
#define BLE_MAC_DIAGCNTL_DIAG1_Pos      (8U)
#define BLE_MAC_DIAGCNTL_DIAG1_Msk      (0x7FUL << BLE_MAC_DIAGCNTL_DIAG1_Pos)
#define BLE_MAC_DIAGCNTL_DIAG1          BLE_MAC_DIAGCNTL_DIAG1_Msk
#define BLE_MAC_DIAGCNTL_DIAG1_EN_Pos   (15U)
#define BLE_MAC_DIAGCNTL_DIAG1_EN_Msk   (0x1UL << BLE_MAC_DIAGCNTL_DIAG1_EN_Pos)
#define BLE_MAC_DIAGCNTL_DIAG1_EN       BLE_MAC_DIAGCNTL_DIAG1_EN_Msk
#define BLE_MAC_DIAGCNTL_DIAG2_Pos      (16U)
#define BLE_MAC_DIAGCNTL_DIAG2_Msk      (0x7FUL << BLE_MAC_DIAGCNTL_DIAG2_Pos)
#define BLE_MAC_DIAGCNTL_DIAG2          BLE_MAC_DIAGCNTL_DIAG2_Msk
#define BLE_MAC_DIAGCNTL_DIAG2_EN_Pos   (23U)
#define BLE_MAC_DIAGCNTL_DIAG2_EN_Msk   (0x1UL << BLE_MAC_DIAGCNTL_DIAG2_EN_Pos)
#define BLE_MAC_DIAGCNTL_DIAG2_EN       BLE_MAC_DIAGCNTL_DIAG2_EN_Msk
#define BLE_MAC_DIAGCNTL_DIAG3_Pos      (24U)
#define BLE_MAC_DIAGCNTL_DIAG3_Msk      (0x7FUL << BLE_MAC_DIAGCNTL_DIAG3_Pos)
#define BLE_MAC_DIAGCNTL_DIAG3          BLE_MAC_DIAGCNTL_DIAG3_Msk
#define BLE_MAC_DIAGCNTL_DIAG3_EN_Pos   (31U)
#define BLE_MAC_DIAGCNTL_DIAG3_EN_Msk   (0x1UL << BLE_MAC_DIAGCNTL_DIAG3_EN_Pos)
#define BLE_MAC_DIAGCNTL_DIAG3_EN       BLE_MAC_DIAGCNTL_DIAG3_EN_Msk

/**************** Bit definition for BLE_MAC_DIAGSTAT register ****************/
#define BLE_MAC_DIAGSTAT_DIAG0STAT_Pos  (0U)
#define BLE_MAC_DIAGSTAT_DIAG0STAT_Msk  (0xFFUL << BLE_MAC_DIAGSTAT_DIAG0STAT_Pos)
#define BLE_MAC_DIAGSTAT_DIAG0STAT      BLE_MAC_DIAGSTAT_DIAG0STAT_Msk
#define BLE_MAC_DIAGSTAT_DIAG1STAT_Pos  (8U)
#define BLE_MAC_DIAGSTAT_DIAG1STAT_Msk  (0xFFUL << BLE_MAC_DIAGSTAT_DIAG1STAT_Pos)
#define BLE_MAC_DIAGSTAT_DIAG1STAT      BLE_MAC_DIAGSTAT_DIAG1STAT_Msk
#define BLE_MAC_DIAGSTAT_DIAG2STAT_Pos  (16U)
#define BLE_MAC_DIAGSTAT_DIAG2STAT_Msk  (0xFFUL << BLE_MAC_DIAGSTAT_DIAG2STAT_Pos)
#define BLE_MAC_DIAGSTAT_DIAG2STAT      BLE_MAC_DIAGSTAT_DIAG2STAT_Msk
#define BLE_MAC_DIAGSTAT_DIAG3STAT_Pos  (24U)
#define BLE_MAC_DIAGSTAT_DIAG3STAT_Msk  (0xFFUL << BLE_MAC_DIAGSTAT_DIAG3STAT_Pos)
#define BLE_MAC_DIAGSTAT_DIAG3STAT      BLE_MAC_DIAGSTAT_DIAG3STAT_Msk

/************** Bit definition for BLE_MAC_DEBUGADDMAX register ***************/
#define BLE_MAC_DEBUGADDMAX_EM_ADDMAX_Pos  (0U)
#define BLE_MAC_DEBUGADDMAX_EM_ADDMAX_Msk  (0xFFFFUL << BLE_MAC_DEBUGADDMAX_EM_ADDMAX_Pos)
#define BLE_MAC_DEBUGADDMAX_EM_ADDMAX   BLE_MAC_DEBUGADDMAX_EM_ADDMAX_Msk
#define BLE_MAC_DEBUGADDMAX_REG_ADDMAX_Pos  (16U)
#define BLE_MAC_DEBUGADDMAX_REG_ADDMAX_Msk  (0xFFFFUL << BLE_MAC_DEBUGADDMAX_REG_ADDMAX_Pos)
#define BLE_MAC_DEBUGADDMAX_REG_ADDMAX  BLE_MAC_DEBUGADDMAX_REG_ADDMAX_Msk

/************** Bit definition for BLE_MAC_DEBUGADDMIN register ***************/
#define BLE_MAC_DEBUGADDMIN_EM_ADDMIN_Pos  (0U)
#define BLE_MAC_DEBUGADDMIN_EM_ADDMIN_Msk  (0xFFFFUL << BLE_MAC_DEBUGADDMIN_EM_ADDMIN_Pos)
#define BLE_MAC_DEBUGADDMIN_EM_ADDMIN   BLE_MAC_DEBUGADDMIN_EM_ADDMIN_Msk
#define BLE_MAC_DEBUGADDMIN_REG_ADDMIN_Pos  (16U)
#define BLE_MAC_DEBUGADDMIN_REG_ADDMIN_Msk  (0xFFFFUL << BLE_MAC_DEBUGADDMIN_REG_ADDMIN_Pos)
#define BLE_MAC_DEBUGADDMIN_REG_ADDMIN  BLE_MAC_DEBUGADDMIN_REG_ADDMIN_Msk

/************* Bit definition for BLE_MAC_ERRORTYPESTAT register **************/
#define BLE_MAC_ERRORTYPESTAT_TXCRYPT_ERROR_Pos  (0U)
#define BLE_MAC_ERRORTYPESTAT_TXCRYPT_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_TXCRYPT_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_TXCRYPT_ERROR  BLE_MAC_ERRORTYPESTAT_TXCRYPT_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RXCRYPT_ERROR_Pos  (1U)
#define BLE_MAC_ERRORTYPESTAT_RXCRYPT_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RXCRYPT_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_RXCRYPT_ERROR  BLE_MAC_ERRORTYPESTAT_RXCRYPT_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos  (2U)
#define BLE_MAC_ERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_PKTCNTL_EMACC_ERROR  BLE_MAC_ERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RADIO_EMACC_ERROR_Pos  (3U)
#define BLE_MAC_ERRORTYPESTAT_RADIO_EMACC_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RADIO_EMACC_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_RADIO_EMACC_ERROR  BLE_MAC_ERRORTYPESTAT_RADIO_EMACC_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_ENTRY_ERROR_Pos  (4U)
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_ENTRY_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_ENTRY_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_ENTRY_ERROR  BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_ENTRY_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_APFM_ERROR_Pos  (5U)
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_APFM_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_APFM_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_APFM_ERROR  BLE_MAC_ERRORTYPESTAT_EVT_SCHDL_APFM_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Pos  (6U)
#define BLE_MAC_ERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_EVT_CNTL_APFM_ERROR  BLE_MAC_ERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_LIST_ERROR_Pos  (7U)
#define BLE_MAC_ERRORTYPESTAT_LIST_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_LIST_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_LIST_ERROR  BLE_MAC_ERRORTYPESTAT_LIST_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_IFS_UNDERRUN_Pos  (8U)
#define BLE_MAC_ERRORTYPESTAT_IFS_UNDERRUN_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_IFS_UNDERRUN_Pos)
#define BLE_MAC_ERRORTYPESTAT_IFS_UNDERRUN  BLE_MAC_ERRORTYPESTAT_IFS_UNDERRUN_Msk
#define BLE_MAC_ERRORTYPESTAT_ADV_UNDERRUN_Pos  (9U)
#define BLE_MAC_ERRORTYPESTAT_ADV_UNDERRUN_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_ADV_UNDERRUN_Pos)
#define BLE_MAC_ERRORTYPESTAT_ADV_UNDERRUN  BLE_MAC_ERRORTYPESTAT_ADV_UNDERRUN_Msk
#define BLE_MAC_ERRORTYPESTAT_LLCHMAP_ERROR_Pos  (10U)
#define BLE_MAC_ERRORTYPESTAT_LLCHMAP_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_LLCHMAP_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_LLCHMAP_ERROR  BLE_MAC_ERRORTYPESTAT_LLCHMAP_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_CSFORMAT_ERROR_Pos  (11U)
#define BLE_MAC_ERRORTYPESTAT_CSFORMAT_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_CSFORMAT_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_CSFORMAT_ERROR  BLE_MAC_ERRORTYPESTAT_CSFORMAT_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos  (12U)
#define BLE_MAC_ERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_TXDESC_EMPTY_ERROR  BLE_MAC_ERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos  (13U)
#define BLE_MAC_ERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_RXDESC_EMPTY_ERROR  BLE_MAC_ERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_TXDATA_PTR_ERROR_Pos  (14U)
#define BLE_MAC_ERRORTYPESTAT_TXDATA_PTR_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_TXDATA_PTR_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_TXDATA_PTR_ERROR  BLE_MAC_ERRORTYPESTAT_TXDATA_PTR_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RXDATA_PTR_ERROR_Pos  (15U)
#define BLE_MAC_ERRORTYPESTAT_RXDATA_PTR_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RXDATA_PTR_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_RXDATA_PTR_ERROR  BLE_MAC_ERRORTYPESTAT_RXDATA_PTR_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RAL_ERROR_Pos  (16U)
#define BLE_MAC_ERRORTYPESTAT_RAL_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RAL_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_RAL_ERROR  BLE_MAC_ERRORTYPESTAT_RAL_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_RAL_UNDERRUN_Pos  (17U)
#define BLE_MAC_ERRORTYPESTAT_RAL_UNDERRUN_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_RAL_UNDERRUN_Pos)
#define BLE_MAC_ERRORTYPESTAT_RAL_UNDERRUN  BLE_MAC_ERRORTYPESTAT_RAL_UNDERRUN_Msk
#define BLE_MAC_ERRORTYPESTAT_TMAFS_UNDERRUN_Pos  (18U)
#define BLE_MAC_ERRORTYPESTAT_TMAFS_UNDERRUN_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_TMAFS_UNDERRUN_Pos)
#define BLE_MAC_ERRORTYPESTAT_TMAFS_UNDERRUN  BLE_MAC_ERRORTYPESTAT_TMAFS_UNDERRUN_Msk
#define BLE_MAC_ERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Pos  (19U)
#define BLE_MAC_ERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_TXAEHEADER_PTR_ERROR  BLE_MAC_ERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Msk
#define BLE_MAC_ERRORTYPESTAT_PHY_ERROR_Pos  (20U)
#define BLE_MAC_ERRORTYPESTAT_PHY_ERROR_Msk  (0x1UL << BLE_MAC_ERRORTYPESTAT_PHY_ERROR_Pos)
#define BLE_MAC_ERRORTYPESTAT_PHY_ERROR  BLE_MAC_ERRORTYPESTAT_PHY_ERROR_Msk

/************** Bit definition for BLE_MAC_SWPROFILING register ***************/
#define BLE_MAC_SWPROFILING_SWPROF_Pos  (0U)
#define BLE_MAC_SWPROFILING_SWPROF_Msk  (0xFFFFFFFFUL << BLE_MAC_SWPROFILING_SWPROF_Pos)
#define BLE_MAC_SWPROFILING_SWPROF      BLE_MAC_SWPROFILING_SWPROF_Msk

/*************** Bit definition for BLE_MAC_RADIOCNTL0 register ***************/
#define BLE_MAC_RADIOCNTL0_PHY_RF_RXOFF_DELAY_Pos  (0U)
#define BLE_MAC_RADIOCNTL0_PHY_RF_RXOFF_DELAY_Msk  (0xFFUL << BLE_MAC_RADIOCNTL0_PHY_RF_RXOFF_DELAY_Pos)
#define BLE_MAC_RADIOCNTL0_PHY_RF_RXOFF_DELAY  BLE_MAC_RADIOCNTL0_PHY_RF_RXOFF_DELAY_Msk
#define BLE_MAC_RADIOCNTL0_PHY_RF_TXOFF_DELAY_Pos  (8U)
#define BLE_MAC_RADIOCNTL0_PHY_RF_TXOFF_DELAY_Msk  (0xFFUL << BLE_MAC_RADIOCNTL0_PHY_RF_TXOFF_DELAY_Pos)
#define BLE_MAC_RADIOCNTL0_PHY_RF_TXOFF_DELAY  BLE_MAC_RADIOCNTL0_PHY_RF_TXOFF_DELAY_Msk
#define BLE_MAC_RADIOCNTL0_PHY_RXON_DELAY_Pos  (16U)
#define BLE_MAC_RADIOCNTL0_PHY_RXON_DELAY_Msk  (0xFFUL << BLE_MAC_RADIOCNTL0_PHY_RXON_DELAY_Pos)
#define BLE_MAC_RADIOCNTL0_PHY_RXON_DELAY  BLE_MAC_RADIOCNTL0_PHY_RXON_DELAY_Msk
#define BLE_MAC_RADIOCNTL0_PHY_TXON_DELAY_Pos  (24U)
#define BLE_MAC_RADIOCNTL0_PHY_TXON_DELAY_Msk  (0xFFUL << BLE_MAC_RADIOCNTL0_PHY_TXON_DELAY_Pos)
#define BLE_MAC_RADIOCNTL0_PHY_TXON_DELAY  BLE_MAC_RADIOCNTL0_PHY_TXON_DELAY_Msk

/*************** Bit definition for BLE_MAC_RADIOCNTL2 register ***************/
#define BLE_MAC_RADIOCNTL2_FREQTABLE_PTR_Pos  (0U)
#define BLE_MAC_RADIOCNTL2_FREQTABLE_PTR_Msk  (0xFFFFUL << BLE_MAC_RADIOCNTL2_FREQTABLE_PTR_Pos)
#define BLE_MAC_RADIOCNTL2_FREQTABLE_PTR  BLE_MAC_RADIOCNTL2_FREQTABLE_PTR_Msk
#define BLE_MAC_RADIOCNTL2_SYNCERR_Pos  (16U)
#define BLE_MAC_RADIOCNTL2_SYNCERR_Msk  (0x7UL << BLE_MAC_RADIOCNTL2_SYNCERR_Pos)
#define BLE_MAC_RADIOCNTL2_SYNCERR      BLE_MAC_RADIOCNTL2_SYNCERR_Msk
#define BLE_MAC_RADIOCNTL2_PHYMSK_Pos   (22U)
#define BLE_MAC_RADIOCNTL2_PHYMSK_Msk   (0x3UL << BLE_MAC_RADIOCNTL2_PHYMSK_Pos)
#define BLE_MAC_RADIOCNTL2_PHYMSK       BLE_MAC_RADIOCNTL2_PHYMSK_Msk
#define BLE_MAC_RADIOCNTL2_CHANNEL_Pos  (24U)
#define BLE_MAC_RADIOCNTL2_CHANNEL_Msk  (0x7FUL << BLE_MAC_RADIOCNTL2_CHANNEL_Pos)
#define BLE_MAC_RADIOCNTL2_CHANNEL      BLE_MAC_RADIOCNTL2_CHANNEL_Msk
#define BLE_MAC_RADIOCNTL2_FORCE_CHANNEL_Pos  (31U)
#define BLE_MAC_RADIOCNTL2_FORCE_CHANNEL_Msk  (0x1UL << BLE_MAC_RADIOCNTL2_FORCE_CHANNEL_Pos)
#define BLE_MAC_RADIOCNTL2_FORCE_CHANNEL  BLE_MAC_RADIOCNTL2_FORCE_CHANNEL_Msk

/*************** Bit definition for BLE_MAC_RADIOCNTL3 register ***************/
#define BLE_MAC_RADIOCNTL3_TXVALID_BEH_Pos  (0U)
#define BLE_MAC_RADIOCNTL3_TXVALID_BEH_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_TXVALID_BEH_Pos)
#define BLE_MAC_RADIOCNTL3_TXVALID_BEH  BLE_MAC_RADIOCNTL3_TXVALID_BEH_Msk
#define BLE_MAC_RADIOCNTL3_TXRATE0CFG_Pos  (8U)
#define BLE_MAC_RADIOCNTL3_TXRATE0CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_TXRATE0CFG_Pos)
#define BLE_MAC_RADIOCNTL3_TXRATE0CFG   BLE_MAC_RADIOCNTL3_TXRATE0CFG_Msk
#define BLE_MAC_RADIOCNTL3_TXRATE1CFG_Pos  (10U)
#define BLE_MAC_RADIOCNTL3_TXRATE1CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_TXRATE1CFG_Pos)
#define BLE_MAC_RADIOCNTL3_TXRATE1CFG   BLE_MAC_RADIOCNTL3_TXRATE1CFG_Msk
#define BLE_MAC_RADIOCNTL3_TXRATE2CFG_Pos  (12U)
#define BLE_MAC_RADIOCNTL3_TXRATE2CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_TXRATE2CFG_Pos)
#define BLE_MAC_RADIOCNTL3_TXRATE2CFG   BLE_MAC_RADIOCNTL3_TXRATE2CFG_Msk
#define BLE_MAC_RADIOCNTL3_TXRATE3CFG_Pos  (14U)
#define BLE_MAC_RADIOCNTL3_TXRATE3CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_TXRATE3CFG_Pos)
#define BLE_MAC_RADIOCNTL3_TXRATE3CFG   BLE_MAC_RADIOCNTL3_TXRATE3CFG_Msk
#define BLE_MAC_RADIOCNTL3_RXVALID_BEH_Pos  (16U)
#define BLE_MAC_RADIOCNTL3_RXVALID_BEH_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_RXVALID_BEH_Pos)
#define BLE_MAC_RADIOCNTL3_RXVALID_BEH  BLE_MAC_RADIOCNTL3_RXVALID_BEH_Msk
#define BLE_MAC_RADIOCNTL3_RXSYNC_ROUTING_Pos  (18U)
#define BLE_MAC_RADIOCNTL3_RXSYNC_ROUTING_Msk  (0x1UL << BLE_MAC_RADIOCNTL3_RXSYNC_ROUTING_Pos)
#define BLE_MAC_RADIOCNTL3_RXSYNC_ROUTING  BLE_MAC_RADIOCNTL3_RXSYNC_ROUTING_Msk
#define BLE_MAC_RADIOCNTL3_RXRATE0CFG_Pos  (24U)
#define BLE_MAC_RADIOCNTL3_RXRATE0CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_RXRATE0CFG_Pos)
#define BLE_MAC_RADIOCNTL3_RXRATE0CFG   BLE_MAC_RADIOCNTL3_RXRATE0CFG_Msk
#define BLE_MAC_RADIOCNTL3_RXRATE1CFG_Pos  (26U)
#define BLE_MAC_RADIOCNTL3_RXRATE1CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_RXRATE1CFG_Pos)
#define BLE_MAC_RADIOCNTL3_RXRATE1CFG   BLE_MAC_RADIOCNTL3_RXRATE1CFG_Msk
#define BLE_MAC_RADIOCNTL3_RXRATE2CFG_Pos  (28U)
#define BLE_MAC_RADIOCNTL3_RXRATE2CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_RXRATE2CFG_Pos)
#define BLE_MAC_RADIOCNTL3_RXRATE2CFG   BLE_MAC_RADIOCNTL3_RXRATE2CFG_Msk
#define BLE_MAC_RADIOCNTL3_RXRATE3CFG_Pos  (30U)
#define BLE_MAC_RADIOCNTL3_RXRATE3CFG_Msk  (0x3UL << BLE_MAC_RADIOCNTL3_RXRATE3CFG_Pos)
#define BLE_MAC_RADIOCNTL3_RXRATE3CFG   BLE_MAC_RADIOCNTL3_RXRATE3CFG_Msk

/************* Bit definition for BLE_MAC_RADIOPWRUPDN0 register **************/
#define BLE_MAC_RADIOPWRUPDN0_TXPWRUP0_Pos  (0U)
#define BLE_MAC_RADIOPWRUPDN0_TXPWRUP0_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN0_TXPWRUP0_Pos)
#define BLE_MAC_RADIOPWRUPDN0_TXPWRUP0  BLE_MAC_RADIOPWRUPDN0_TXPWRUP0_Msk
#define BLE_MAC_RADIOPWRUPDN0_TXPWRDN0_Pos  (8U)
#define BLE_MAC_RADIOPWRUPDN0_TXPWRDN0_Msk  (0x7FUL << BLE_MAC_RADIOPWRUPDN0_TXPWRDN0_Pos)
#define BLE_MAC_RADIOPWRUPDN0_TXPWRDN0  BLE_MAC_RADIOPWRUPDN0_TXPWRDN0_Msk
#define BLE_MAC_RADIOPWRUPDN0_RXPWRUP0_Pos  (16U)
#define BLE_MAC_RADIOPWRUPDN0_RXPWRUP0_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN0_RXPWRUP0_Pos)
#define BLE_MAC_RADIOPWRUPDN0_RXPWRUP0  BLE_MAC_RADIOPWRUPDN0_RXPWRUP0_Msk
#define BLE_MAC_RADIOPWRUPDN0_SYNC_POSITION0_Pos  (24U)
#define BLE_MAC_RADIOPWRUPDN0_SYNC_POSITION0_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN0_SYNC_POSITION0_Pos)
#define BLE_MAC_RADIOPWRUPDN0_SYNC_POSITION0  BLE_MAC_RADIOPWRUPDN0_SYNC_POSITION0_Msk

/************* Bit definition for BLE_MAC_RADIOPWRUPDN1 register **************/
#define BLE_MAC_RADIOPWRUPDN1_TXPWRUP1_Pos  (0U)
#define BLE_MAC_RADIOPWRUPDN1_TXPWRUP1_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN1_TXPWRUP1_Pos)
#define BLE_MAC_RADIOPWRUPDN1_TXPWRUP1  BLE_MAC_RADIOPWRUPDN1_TXPWRUP1_Msk
#define BLE_MAC_RADIOPWRUPDN1_TXPWRDN1_Pos  (8U)
#define BLE_MAC_RADIOPWRUPDN1_TXPWRDN1_Msk  (0x7FUL << BLE_MAC_RADIOPWRUPDN1_TXPWRDN1_Pos)
#define BLE_MAC_RADIOPWRUPDN1_TXPWRDN1  BLE_MAC_RADIOPWRUPDN1_TXPWRDN1_Msk
#define BLE_MAC_RADIOPWRUPDN1_RXPWRUP1_Pos  (16U)
#define BLE_MAC_RADIOPWRUPDN1_RXPWRUP1_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN1_RXPWRUP1_Pos)
#define BLE_MAC_RADIOPWRUPDN1_RXPWRUP1  BLE_MAC_RADIOPWRUPDN1_RXPWRUP1_Msk
#define BLE_MAC_RADIOPWRUPDN1_SYNC_POSITION1_Pos  (24U)
#define BLE_MAC_RADIOPWRUPDN1_SYNC_POSITION1_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN1_SYNC_POSITION1_Pos)
#define BLE_MAC_RADIOPWRUPDN1_SYNC_POSITION1  BLE_MAC_RADIOPWRUPDN1_SYNC_POSITION1_Msk

/************* Bit definition for BLE_MAC_RADIOPWRUPDN2 register **************/
#define BLE_MAC_RADIOPWRUPDN2_TXPWRUP2_Pos  (0U)
#define BLE_MAC_RADIOPWRUPDN2_TXPWRUP2_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN2_TXPWRUP2_Pos)
#define BLE_MAC_RADIOPWRUPDN2_TXPWRUP2  BLE_MAC_RADIOPWRUPDN2_TXPWRUP2_Msk
#define BLE_MAC_RADIOPWRUPDN2_TXPWRDN2_Pos  (8U)
#define BLE_MAC_RADIOPWRUPDN2_TXPWRDN2_Msk  (0x7FUL << BLE_MAC_RADIOPWRUPDN2_TXPWRDN2_Pos)
#define BLE_MAC_RADIOPWRUPDN2_TXPWRDN2  BLE_MAC_RADIOPWRUPDN2_TXPWRDN2_Msk
#define BLE_MAC_RADIOPWRUPDN2_RXPWRUP2_Pos  (16U)
#define BLE_MAC_RADIOPWRUPDN2_RXPWRUP2_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN2_RXPWRUP2_Pos)
#define BLE_MAC_RADIOPWRUPDN2_RXPWRUP2  BLE_MAC_RADIOPWRUPDN2_RXPWRUP2_Msk
#define BLE_MAC_RADIOPWRUPDN2_SYNC_POSITION2_Pos  (24U)
#define BLE_MAC_RADIOPWRUPDN2_SYNC_POSITION2_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN2_SYNC_POSITION2_Pos)
#define BLE_MAC_RADIOPWRUPDN2_SYNC_POSITION2  BLE_MAC_RADIOPWRUPDN2_SYNC_POSITION2_Msk

/************* Bit definition for BLE_MAC_RADIOPWRUPDN3 register **************/
#define BLE_MAC_RADIOPWRUPDN3_TXPWRUP3_Pos  (0U)
#define BLE_MAC_RADIOPWRUPDN3_TXPWRUP3_Msk  (0xFFUL << BLE_MAC_RADIOPWRUPDN3_TXPWRUP3_Pos)
#define BLE_MAC_RADIOPWRUPDN3_TXPWRUP3  BLE_MAC_RADIOPWRUPDN3_TXPWRUP3_Msk
#define BLE_MAC_RADIOPWRUPDN3_TXPWRDN3_Pos  (8U)
#define BLE_MAC_RADIOPWRUPDN3_TXPWRDN3_Msk  (0x7FUL << BLE_MAC_RADIOPWRUPDN3_TXPWRDN3_Pos)
#define BLE_MAC_RADIOPWRUPDN3_TXPWRDN3  BLE_MAC_RADIOPWRUPDN3_TXPWRDN3_Msk

/************* Bit definition for BLE_MAC_RADIOTXRXTIM0 register **************/
#define BLE_MAC_RADIOTXRXTIM0_TXPATHDLY0_Pos  (0U)
#define BLE_MAC_RADIOTXRXTIM0_TXPATHDLY0_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM0_TXPATHDLY0_Pos)
#define BLE_MAC_RADIOTXRXTIM0_TXPATHDLY0  BLE_MAC_RADIOTXRXTIM0_TXPATHDLY0_Msk
#define BLE_MAC_RADIOTXRXTIM0_RXPATHDLY0_Pos  (8U)
#define BLE_MAC_RADIOTXRXTIM0_RXPATHDLY0_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM0_RXPATHDLY0_Pos)
#define BLE_MAC_RADIOTXRXTIM0_RXPATHDLY0  BLE_MAC_RADIOTXRXTIM0_RXPATHDLY0_Msk
#define BLE_MAC_RADIOTXRXTIM0_RFRXTMDA0_Pos  (16U)
#define BLE_MAC_RADIOTXRXTIM0_RFRXTMDA0_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM0_RFRXTMDA0_Pos)
#define BLE_MAC_RADIOTXRXTIM0_RFRXTMDA0  BLE_MAC_RADIOTXRXTIM0_RFRXTMDA0_Msk

/************* Bit definition for BLE_MAC_RADIOTXRXTIM1 register **************/
#define BLE_MAC_RADIOTXRXTIM1_TXPATHDLY1_Pos  (0U)
#define BLE_MAC_RADIOTXRXTIM1_TXPATHDLY1_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM1_TXPATHDLY1_Pos)
#define BLE_MAC_RADIOTXRXTIM1_TXPATHDLY1  BLE_MAC_RADIOTXRXTIM1_TXPATHDLY1_Msk
#define BLE_MAC_RADIOTXRXTIM1_RXPATHDLY1_Pos  (8U)
#define BLE_MAC_RADIOTXRXTIM1_RXPATHDLY1_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM1_RXPATHDLY1_Pos)
#define BLE_MAC_RADIOTXRXTIM1_RXPATHDLY1  BLE_MAC_RADIOTXRXTIM1_RXPATHDLY1_Msk
#define BLE_MAC_RADIOTXRXTIM1_RFRXTMDA1_Pos  (16U)
#define BLE_MAC_RADIOTXRXTIM1_RFRXTMDA1_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM1_RFRXTMDA1_Pos)
#define BLE_MAC_RADIOTXRXTIM1_RFRXTMDA1  BLE_MAC_RADIOTXRXTIM1_RFRXTMDA1_Msk

/************* Bit definition for BLE_MAC_RADIOTXRXTIM2 register **************/
#define BLE_MAC_RADIOTXRXTIM2_TXPATHDLY2_Pos  (0U)
#define BLE_MAC_RADIOTXRXTIM2_TXPATHDLY2_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM2_TXPATHDLY2_Pos)
#define BLE_MAC_RADIOTXRXTIM2_TXPATHDLY2  BLE_MAC_RADIOTXRXTIM2_TXPATHDLY2_Msk
#define BLE_MAC_RADIOTXRXTIM2_RXPATHDLY2_Pos  (8U)
#define BLE_MAC_RADIOTXRXTIM2_RXPATHDLY2_Msk  (0xFFUL << BLE_MAC_RADIOTXRXTIM2_RXPATHDLY2_Pos)
#define BLE_MAC_RADIOTXRXTIM2_RXPATHDLY2  BLE_MAC_RADIOTXRXTIM2_RXPATHDLY2_Msk
#define BLE_MAC_RADIOTXRXTIM2_RFRXTMDA2_Pos  (16U)
#define BLE_MAC_RADIOTXRXTIM2_RFRXTMDA2_Msk  (0xFFUL << BLE_MAC_RADIOTXRXTIM2_RFRXTMDA2_Pos)
#define BLE_MAC_RADIOTXRXTIM2_RFRXTMDA2  BLE_MAC_RADIOTXRXTIM2_RFRXTMDA2_Msk
#define BLE_MAC_RADIOTXRXTIM2_RXFLUSHPATHDLY2_Pos  (24U)
#define BLE_MAC_RADIOTXRXTIM2_RXFLUSHPATHDLY2_Msk  (0xFFUL << BLE_MAC_RADIOTXRXTIM2_RXFLUSHPATHDLY2_Pos)
#define BLE_MAC_RADIOTXRXTIM2_RXFLUSHPATHDLY2  BLE_MAC_RADIOTXRXTIM2_RXFLUSHPATHDLY2_Msk

/************* Bit definition for BLE_MAC_RADIOTXRXTIM3 register **************/
#define BLE_MAC_RADIOTXRXTIM3_TXPATHDLY3_Pos  (0U)
#define BLE_MAC_RADIOTXRXTIM3_TXPATHDLY3_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM3_TXPATHDLY3_Pos)
#define BLE_MAC_RADIOTXRXTIM3_TXPATHDLY3  BLE_MAC_RADIOTXRXTIM3_TXPATHDLY3_Msk
#define BLE_MAC_RADIOTXRXTIM3_RFRXTMDA3_Pos  (16U)
#define BLE_MAC_RADIOTXRXTIM3_RFRXTMDA3_Msk  (0x7FUL << BLE_MAC_RADIOTXRXTIM3_RFRXTMDA3_Pos)
#define BLE_MAC_RADIOTXRXTIM3_RFRXTMDA3  BLE_MAC_RADIOTXRXTIM3_RFRXTMDA3_Msk
#define BLE_MAC_RADIOTXRXTIM3_RXFLUSHPATHDLY3_Pos  (24U)
#define BLE_MAC_RADIOTXRXTIM3_RXFLUSHPATHDLY3_Msk  (0xFFUL << BLE_MAC_RADIOTXRXTIM3_RXFLUSHPATHDLY3_Pos)
#define BLE_MAC_RADIOTXRXTIM3_RXFLUSHPATHDLY3  BLE_MAC_RADIOTXRXTIM3_RXFLUSHPATHDLY3_Msk

/**************** Bit definition for BLE_MAC_AESCNTL register *****************/
#define BLE_MAC_AESCNTL_AES_START_Pos   (0U)
#define BLE_MAC_AESCNTL_AES_START_Msk   (0x1UL << BLE_MAC_AESCNTL_AES_START_Pos)
#define BLE_MAC_AESCNTL_AES_START       BLE_MAC_AESCNTL_AES_START_Msk
#define BLE_MAC_AESCNTL_AES_MODE_Pos    (1U)
#define BLE_MAC_AESCNTL_AES_MODE_Msk    (0x1UL << BLE_MAC_AESCNTL_AES_MODE_Pos)
#define BLE_MAC_AESCNTL_AES_MODE        BLE_MAC_AESCNTL_AES_MODE_Msk

/*************** Bit definition for BLE_MAC_AESKEY31_0 register ***************/
#define BLE_MAC_AESKEY31_0_AESKEY31_0_Pos  (0U)
#define BLE_MAC_AESKEY31_0_AESKEY31_0_Msk  (0xFFFFFFFFUL << BLE_MAC_AESKEY31_0_AESKEY31_0_Pos)
#define BLE_MAC_AESKEY31_0_AESKEY31_0   BLE_MAC_AESKEY31_0_AESKEY31_0_Msk

/************** Bit definition for BLE_MAC_AESKEY63_32 register ***************/
#define BLE_MAC_AESKEY63_32_AESKEY63_32_Pos  (0U)
#define BLE_MAC_AESKEY63_32_AESKEY63_32_Msk  (0xFFFFFFFFUL << BLE_MAC_AESKEY63_32_AESKEY63_32_Pos)
#define BLE_MAC_AESKEY63_32_AESKEY63_32  BLE_MAC_AESKEY63_32_AESKEY63_32_Msk

/************** Bit definition for BLE_MAC_AESKEY95_64 register ***************/
#define BLE_MAC_AESKEY95_64_AESKEY95_64_Pos  (0U)
#define BLE_MAC_AESKEY95_64_AESKEY95_64_Msk  (0xFFFFFFFFUL << BLE_MAC_AESKEY95_64_AESKEY95_64_Pos)
#define BLE_MAC_AESKEY95_64_AESKEY95_64  BLE_MAC_AESKEY95_64_AESKEY95_64_Msk

/************** Bit definition for BLE_MAC_AESKEY127_96 register **************/
#define BLE_MAC_AESKEY127_96_AESKEY127_96_Pos  (0U)
#define BLE_MAC_AESKEY127_96_AESKEY127_96_Msk  (0xFFFFFFFFUL << BLE_MAC_AESKEY127_96_AESKEY127_96_Pos)
#define BLE_MAC_AESKEY127_96_AESKEY127_96  BLE_MAC_AESKEY127_96_AESKEY127_96_Msk

/***************** Bit definition for BLE_MAC_AESPTR register *****************/
#define BLE_MAC_AESPTR_AESPTR_Pos       (0U)
#define BLE_MAC_AESPTR_AESPTR_Msk       (0xFFFFUL << BLE_MAC_AESPTR_AESPTR_Pos)
#define BLE_MAC_AESPTR_AESPTR           BLE_MAC_AESPTR_AESPTR_Msk

/**************** Bit definition for BLE_MAC_TXMICVAL register ****************/
#define BLE_MAC_TXMICVAL_TXMICVAL_Pos   (0U)
#define BLE_MAC_TXMICVAL_TXMICVAL_Msk   (0xFFFFFFFFUL << BLE_MAC_TXMICVAL_TXMICVAL_Pos)
#define BLE_MAC_TXMICVAL_TXMICVAL       BLE_MAC_TXMICVAL_TXMICVAL_Msk

/**************** Bit definition for BLE_MAC_RXMICVAL register ****************/
#define BLE_MAC_RXMICVAL_RXMICVAL_Pos   (0U)
#define BLE_MAC_RXMICVAL_RXMICVAL_Msk   (0xFFFFFFFFUL << BLE_MAC_RXMICVAL_RXMICVAL_Pos)
#define BLE_MAC_RXMICVAL_RXMICVAL       BLE_MAC_RXMICVAL_RXMICVAL_Msk

/*************** Bit definition for BLE_MAC_RFTESTCNTL register ***************/
#define BLE_MAC_RFTESTCNTL_TXLENGTH_Pos  (0U)
#define BLE_MAC_RFTESTCNTL_TXLENGTH_Msk  (0x1FFUL << BLE_MAC_RFTESTCNTL_TXLENGTH_Pos)
#define BLE_MAC_RFTESTCNTL_TXLENGTH     BLE_MAC_RFTESTCNTL_TXLENGTH_Msk
#define BLE_MAC_RFTESTCNTL_TXPKTCNTEN_Pos  (11U)
#define BLE_MAC_RFTESTCNTL_TXPKTCNTEN_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_TXPKTCNTEN_Pos)
#define BLE_MAC_RFTESTCNTL_TXPKTCNTEN   BLE_MAC_RFTESTCNTL_TXPKTCNTEN_Msk
#define BLE_MAC_RFTESTCNTL_TXPLDSRC_Pos  (12U)
#define BLE_MAC_RFTESTCNTL_TXPLDSRC_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_TXPLDSRC_Pos)
#define BLE_MAC_RFTESTCNTL_TXPLDSRC     BLE_MAC_RFTESTCNTL_TXPLDSRC_Msk
#define BLE_MAC_RFTESTCNTL_PRBSTYPE_Pos  (13U)
#define BLE_MAC_RFTESTCNTL_PRBSTYPE_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_PRBSTYPE_Pos)
#define BLE_MAC_RFTESTCNTL_PRBSTYPE     BLE_MAC_RFTESTCNTL_PRBSTYPE_Msk
#define BLE_MAC_RFTESTCNTL_TXLENGTHSRC_Pos  (14U)
#define BLE_MAC_RFTESTCNTL_TXLENGTHSRC_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_TXLENGTHSRC_Pos)
#define BLE_MAC_RFTESTCNTL_TXLENGTHSRC  BLE_MAC_RFTESTCNTL_TXLENGTHSRC_Msk
#define BLE_MAC_RFTESTCNTL_INFINITETX_Pos  (15U)
#define BLE_MAC_RFTESTCNTL_INFINITETX_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_INFINITETX_Pos)
#define BLE_MAC_RFTESTCNTL_INFINITETX   BLE_MAC_RFTESTCNTL_INFINITETX_Msk
#define BLE_MAC_RFTESTCNTL_EXT_SLAVE_Pos  (16U)
#define BLE_MAC_RFTESTCNTL_EXT_SLAVE_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_EXT_SLAVE_Pos)
#define BLE_MAC_RFTESTCNTL_EXT_SLAVE    BLE_MAC_RFTESTCNTL_EXT_SLAVE_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_RATE_VAL_Pos  (17U)
#define BLE_MAC_RFTESTCNTL_FORCE_RATE_VAL_Msk  (0x3UL << BLE_MAC_RFTESTCNTL_FORCE_RATE_VAL_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_RATE_VAL  BLE_MAC_RFTESTCNTL_FORCE_RATE_VAL_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_RATE_Pos  (19U)
#define BLE_MAC_RFTESTCNTL_FORCE_RATE_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_RATE_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_RATE   BLE_MAC_RFTESTCNTL_FORCE_RATE_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_RX_VAL_Pos  (20U)
#define BLE_MAC_RFTESTCNTL_FORCE_RX_VAL_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_RX_VAL_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_RX_VAL  BLE_MAC_RFTESTCNTL_FORCE_RX_VAL_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_RX_Pos  (21U)
#define BLE_MAC_RFTESTCNTL_FORCE_RX_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_RX_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_RX     BLE_MAC_RFTESTCNTL_FORCE_RX_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_TX_VAL_Pos  (22U)
#define BLE_MAC_RFTESTCNTL_FORCE_TX_VAL_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_TX_VAL_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_TX_VAL  BLE_MAC_RFTESTCNTL_FORCE_TX_VAL_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_TX_Pos  (23U)
#define BLE_MAC_RFTESTCNTL_FORCE_TX_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_TX_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_TX     BLE_MAC_RFTESTCNTL_FORCE_TX_Msk
#define BLE_MAC_RFTESTCNTL_PERCOUNT_MODE_Pos  (24U)
#define BLE_MAC_RFTESTCNTL_PERCOUNT_MODE_Msk  (0x3UL << BLE_MAC_RFTESTCNTL_PERCOUNT_MODE_Pos)
#define BLE_MAC_RFTESTCNTL_PERCOUNT_MODE  BLE_MAC_RFTESTCNTL_PERCOUNT_MODE_Msk
#define BLE_MAC_RFTESTCNTL_FORCE_SYNCWORD_Pos  (26U)
#define BLE_MAC_RFTESTCNTL_FORCE_SYNCWORD_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_FORCE_SYNCWORD_Pos)
#define BLE_MAC_RFTESTCNTL_FORCE_SYNCWORD  BLE_MAC_RFTESTCNTL_FORCE_SYNCWORD_Msk
#define BLE_MAC_RFTESTCNTL_RXPKTCNTEN_Pos  (27U)
#define BLE_MAC_RFTESTCNTL_RXPKTCNTEN_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_RXPKTCNTEN_Pos)
#define BLE_MAC_RFTESTCNTL_RXPKTCNTEN   BLE_MAC_RFTESTCNTL_RXPKTCNTEN_Msk
#define BLE_MAC_RFTESTCNTL_DBGTRIGSEL_Pos  (28U)
#define BLE_MAC_RFTESTCNTL_DBGTRIGSEL_Msk  (0x7UL << BLE_MAC_RFTESTCNTL_DBGTRIGSEL_Pos)
#define BLE_MAC_RFTESTCNTL_DBGTRIGSEL   BLE_MAC_RFTESTCNTL_DBGTRIGSEL_Msk
#define BLE_MAC_RFTESTCNTL_INFINITERX_Pos  (31U)
#define BLE_MAC_RFTESTCNTL_INFINITERX_Msk  (0x1UL << BLE_MAC_RFTESTCNTL_INFINITERX_Pos)
#define BLE_MAC_RFTESTCNTL_INFINITERX   BLE_MAC_RFTESTCNTL_INFINITERX_Msk

/************** Bit definition for BLE_MAC_RFTESTTXSTAT register **************/
#define BLE_MAC_RFTESTTXSTAT_TXPKTCNT_Pos  (0U)
#define BLE_MAC_RFTESTTXSTAT_TXPKTCNT_Msk  (0xFFFFFFFFUL << BLE_MAC_RFTESTTXSTAT_TXPKTCNT_Pos)
#define BLE_MAC_RFTESTTXSTAT_TXPKTCNT   BLE_MAC_RFTESTTXSTAT_TXPKTCNT_Msk

/************** Bit definition for BLE_MAC_RFTESTRXSTAT register **************/
#define BLE_MAC_RFTESTRXSTAT_RXPKTCNT_Pos  (0U)
#define BLE_MAC_RFTESTRXSTAT_RXPKTCNT_Msk  (0xFFFFFFFFUL << BLE_MAC_RFTESTRXSTAT_RXPKTCNT_Pos)
#define BLE_MAC_RFTESTRXSTAT_RXPKTCNT   BLE_MAC_RFTESTRXSTAT_RXPKTCNT_Msk

/************* Bit definition for BLE_MAC_RFTESTSYNCWORD register *************/
#define BLE_MAC_RFTESTSYNCWORD_SYNCWORD_Pos  (0U)
#define BLE_MAC_RFTESTSYNCWORD_SYNCWORD_Msk  (0xFFFFFFFFUL << BLE_MAC_RFTESTSYNCWORD_SYNCWORD_Pos)
#define BLE_MAC_RFTESTSYNCWORD_SYNCWORD  BLE_MAC_RFTESTSYNCWORD_SYNCWORD_Msk

/*************** Bit definition for BLE_MAC_TIMGENCNTL register ***************/
#define BLE_MAC_TIMGENCNTL_PREFETCH_TIME_Pos  (0U)
#define BLE_MAC_TIMGENCNTL_PREFETCH_TIME_Msk  (0x1FFUL << BLE_MAC_TIMGENCNTL_PREFETCH_TIME_Pos)
#define BLE_MAC_TIMGENCNTL_PREFETCH_TIME  BLE_MAC_TIMGENCNTL_PREFETCH_TIME_Msk
#define BLE_MAC_TIMGENCNTL_PREFETCHABORT_TIME_Pos  (16U)
#define BLE_MAC_TIMGENCNTL_PREFETCHABORT_TIME_Msk  (0x3FFUL << BLE_MAC_TIMGENCNTL_PREFETCHABORT_TIME_Pos)
#define BLE_MAC_TIMGENCNTL_PREFETCHABORT_TIME  BLE_MAC_TIMGENCNTL_PREFETCHABORT_TIME_Msk

/************** Bit definition for BLE_MAC_GROSSTIMTGT register ***************/
#define BLE_MAC_GROSSTIMTGT_GROSSTARGET_Pos  (0U)
#define BLE_MAC_GROSSTIMTGT_GROSSTARGET_Msk  (0x7FFFFFUL << BLE_MAC_GROSSTIMTGT_GROSSTARGET_Pos)
#define BLE_MAC_GROSSTIMTGT_GROSSTARGET  BLE_MAC_GROSSTIMTGT_GROSSTARGET_Msk

/*************** Bit definition for BLE_MAC_FINETIMTGT register ***************/
#define BLE_MAC_FINETIMTGT_FINETARGET_Pos  (0U)
#define BLE_MAC_FINETIMTGT_FINETARGET_Msk  (0xFFFFFFFUL << BLE_MAC_FINETIMTGT_FINETARGET_Pos)
#define BLE_MAC_FINETIMTGT_FINETARGET   BLE_MAC_FINETIMTGT_FINETARGET_Msk

/**************** Bit definition for BLE_MAC_CLKNTGT register *****************/
#define BLE_MAC_CLKNTGT_CLKNTARGET_Pos  (0U)
#define BLE_MAC_CLKNTGT_CLKNTARGET_Msk  (0xFFFFFFFUL << BLE_MAC_CLKNTGT_CLKNTARGET_Pos)
#define BLE_MAC_CLKNTGT_CLKNTARGET      BLE_MAC_CLKNTGT_CLKNTARGET_Msk

/************** Bit definition for BLE_MAC_HMICROSECTGT register **************/
#define BLE_MAC_HMICROSECTGT_HMICROSECTARGET_Pos  (0U)
#define BLE_MAC_HMICROSECTGT_HMICROSECTARGET_Msk  (0x3FFUL << BLE_MAC_HMICROSECTGT_HMICROSECTARGET_Pos)
#define BLE_MAC_HMICROSECTGT_HMICROSECTARGET  BLE_MAC_HMICROSECTGT_HMICROSECTARGET_Msk

/*************** Bit definition for BLE_MAC_LESCHCNTL register ****************/
#define BLE_MAC_LESCHCNTL_ENTRY_IDX_Pos  (0U)
#define BLE_MAC_LESCHCNTL_ENTRY_IDX_Msk  (0xFUL << BLE_MAC_LESCHCNTL_ENTRY_IDX_Pos)
#define BLE_MAC_LESCHCNTL_ENTRY_IDX     BLE_MAC_LESCHCNTL_ENTRY_IDX_Msk
#define BLE_MAC_LESCHCNTL_START_EVT_Pos  (31U)
#define BLE_MAC_LESCHCNTL_START_EVT_Msk  (0x1UL << BLE_MAC_LESCHCNTL_START_EVT_Pos)
#define BLE_MAC_LESCHCNTL_START_EVT     BLE_MAC_LESCHCNTL_START_EVT_Msk

/************** Bit definition for BLE_MAC_STARTEVTCLKN register **************/
#define BLE_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Pos  (0U)
#define BLE_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Msk  (0xFFFFFFFUL << BLE_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Pos)
#define BLE_MAC_STARTEVTCLKN_STARTEVTCLKNTS  BLE_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Msk

/************ Bit definition for BLE_MAC_STARTEVTFINECNT register *************/
#define BLE_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Pos  (0U)
#define BLE_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Msk  (0x3FFUL << BLE_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Pos)
#define BLE_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS  BLE_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Msk

/*************** Bit definition for BLE_MAC_ENDEVTCLKN register ***************/
#define BLE_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Pos  (0U)
#define BLE_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Msk  (0xFFFFFFFUL << BLE_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Pos)
#define BLE_MAC_ENDEVTCLKN_ENDEVTCLKNTS  BLE_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Msk

/************* Bit definition for BLE_MAC_ENDEVTFINECNT register **************/
#define BLE_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Pos  (0U)
#define BLE_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Msk  (0x3FFUL << BLE_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Pos)
#define BLE_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS  BLE_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Msk

/************** Bit definition for BLE_MAC_SKIPEVTCLKN register ***************/
#define BLE_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Pos  (0U)
#define BLE_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Msk  (0xFFFFFFFUL << BLE_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Pos)
#define BLE_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS  BLE_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Msk

/************* Bit definition for BLE_MAC_SKIPEVTFINECNT register *************/
#define BLE_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Pos  (0U)
#define BLE_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Msk  (0x3FFUL << BLE_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Pos)
#define BLE_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS  BLE_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Msk

/***************** Bit definition for BLE_MAC_ADVTIM register *****************/
#define BLE_MAC_ADVTIM_ADVINT_Pos       (0U)
#define BLE_MAC_ADVTIM_ADVINT_Msk       (0x3FFFUL << BLE_MAC_ADVTIM_ADVINT_Pos)
#define BLE_MAC_ADVTIM_ADVINT           BLE_MAC_ADVTIM_ADVINT_Msk
#define BLE_MAC_ADVTIM_RX_AUXPTR_THR_Pos  (16U)
#define BLE_MAC_ADVTIM_RX_AUXPTR_THR_Msk  (0xFFUL << BLE_MAC_ADVTIM_RX_AUXPTR_THR_Pos)
#define BLE_MAC_ADVTIM_RX_AUXPTR_THR    BLE_MAC_ADVTIM_RX_AUXPTR_THR_Msk
#define BLE_MAC_ADVTIM_TX_AUXPTR_THR_Pos  (24U)
#define BLE_MAC_ADVTIM_TX_AUXPTR_THR_Msk  (0xFFUL << BLE_MAC_ADVTIM_TX_AUXPTR_THR_Pos)
#define BLE_MAC_ADVTIM_TX_AUXPTR_THR    BLE_MAC_ADVTIM_TX_AUXPTR_THR_Msk

/************** Bit definition for BLE_MAC_ACTSCANCNTL register ***************/
#define BLE_MAC_ACTSCANCNTL_UPPERLIMIT_Pos  (0U)
#define BLE_MAC_ACTSCANCNTL_UPPERLIMIT_Msk  (0x1FFUL << BLE_MAC_ACTSCANCNTL_UPPERLIMIT_Pos)
#define BLE_MAC_ACTSCANCNTL_UPPERLIMIT  BLE_MAC_ACTSCANCNTL_UPPERLIMIT_Msk
#define BLE_MAC_ACTSCANCNTL_BACKOFF_Pos  (16U)
#define BLE_MAC_ACTSCANCNTL_BACKOFF_Msk  (0x1FFUL << BLE_MAC_ACTSCANCNTL_BACKOFF_Pos)
#define BLE_MAC_ACTSCANCNTL_BACKOFF     BLE_MAC_ACTSCANCNTL_BACKOFF_Msk

/***************** Bit definition for BLE_MAC_WLCNTL register *****************/
#define BLE_MAC_WLCNTL_WLBASEPTR_Pos    (0U)
#define BLE_MAC_WLCNTL_WLBASEPTR_Msk    (0xFFFFUL << BLE_MAC_WLCNTL_WLBASEPTR_Pos)
#define BLE_MAC_WLCNTL_WLBASEPTR        BLE_MAC_WLCNTL_WLBASEPTR_Msk
#define BLE_MAC_WLCNTL_WLNBDEV_Pos      (16U)
#define BLE_MAC_WLCNTL_WLNBDEV_Msk      (0xFFUL << BLE_MAC_WLCNTL_WLNBDEV_Pos)
#define BLE_MAC_WLCNTL_WLNBDEV          BLE_MAC_WLCNTL_WLNBDEV_Msk

/*************** Bit definition for BLE_MAC_WLCURRENT register ****************/
#define BLE_MAC_WLCURRENT_WLCURRENTPTR_Pos  (0U)
#define BLE_MAC_WLCURRENT_WLCURRENTPTR_Msk  (0xFFFFUL << BLE_MAC_WLCURRENT_WLCURRENTPTR_Pos)
#define BLE_MAC_WLCURRENT_WLCURRENTPTR  BLE_MAC_WLCURRENT_WLCURRENTPTR_Msk

/************** Bit definition for BLE_MAC_PERADVLCNTL register ***************/
#define BLE_MAC_PERADVLCNTL_PERADVLBASEPTR_Pos  (0U)
#define BLE_MAC_PERADVLCNTL_PERADVLBASEPTR_Msk  (0xFFFFUL << BLE_MAC_PERADVLCNTL_PERADVLBASEPTR_Pos)
#define BLE_MAC_PERADVLCNTL_PERADVLBASEPTR  BLE_MAC_PERADVLCNTL_PERADVLBASEPTR_Msk
#define BLE_MAC_PERADVLCNTL_PERADVLNBDEV_Pos  (16U)
#define BLE_MAC_PERADVLCNTL_PERADVLNBDEV_Msk  (0xFFUL << BLE_MAC_PERADVLCNTL_PERADVLNBDEV_Pos)
#define BLE_MAC_PERADVLCNTL_PERADVLNBDEV  BLE_MAC_PERADVLCNTL_PERADVLNBDEV_Msk

/************* Bit definition for BLE_MAC_PERADVLCURRENT register *************/
#define BLE_MAC_PERADVLCURRENT_PERADVLCURRENTPTR_Pos  (0U)
#define BLE_MAC_PERADVLCURRENT_PERADVLCURRENTPTR_Msk  (0xFFFFUL << BLE_MAC_PERADVLCURRENT_PERADVLCURRENTPTR_Pos)
#define BLE_MAC_PERADVLCURRENT_PERADVLCURRENTPTR  BLE_MAC_PERADVLCURRENT_PERADVLCURRENTPTR_Msk

/**************** Bit definition for BLE_MAC_ADILCNTL register ****************/
#define BLE_MAC_ADILCNTL_ADILBASEPTR_Pos  (0U)
#define BLE_MAC_ADILCNTL_ADILBASEPTR_Msk  (0xFFFFUL << BLE_MAC_ADILCNTL_ADILBASEPTR_Pos)
#define BLE_MAC_ADILCNTL_ADILBASEPTR    BLE_MAC_ADILCNTL_ADILBASEPTR_Msk
#define BLE_MAC_ADILCNTL_NBADI_Pos      (16U)
#define BLE_MAC_ADILCNTL_NBADI_Msk      (0xFFUL << BLE_MAC_ADILCNTL_NBADI_Pos)
#define BLE_MAC_ADILCNTL_NBADI          BLE_MAC_ADILCNTL_NBADI_Msk

/************** Bit definition for BLE_MAC_ADILCURRENT register ***************/
#define BLE_MAC_ADILCURRENT_ADILCURRENTPTR_Pos  (0U)
#define BLE_MAC_ADILCURRENT_ADILCURRENTPTR_Msk  (0xFFFFUL << BLE_MAC_ADILCURRENT_ADILCURRENTPTR_Pos)
#define BLE_MAC_ADILCURRENT_ADILCURRENTPTR  BLE_MAC_ADILCURRENT_ADILCURRENTPTR_Msk

/*************** Bit definition for BLE_MAC_SEARCH_TO register ****************/
#define BLE_MAC_SEARCH_TO_SEARCH_TIMEOUT_Pos  (0U)
#define BLE_MAC_SEARCH_TO_SEARCH_TIMEOUT_Msk  (0x3FUL << BLE_MAC_SEARCH_TO_SEARCH_TIMEOUT_Pos)
#define BLE_MAC_SEARCH_TO_SEARCH_TIMEOUT  BLE_MAC_SEARCH_TO_SEARCH_TIMEOUT_Msk

/**************** Bit definition for BLE_MAC_RALCNTL register *****************/
#define BLE_MAC_RALCNTL_RALBASEPTR_Pos  (0U)
#define BLE_MAC_RALCNTL_RALBASEPTR_Msk  (0xFFFFUL << BLE_MAC_RALCNTL_RALBASEPTR_Pos)
#define BLE_MAC_RALCNTL_RALBASEPTR      BLE_MAC_RALCNTL_RALBASEPTR_Msk
#define BLE_MAC_RALCNTL_RALNBDEV_Pos    (16U)
#define BLE_MAC_RALCNTL_RALNBDEV_Msk    (0xFFUL << BLE_MAC_RALCNTL_RALNBDEV_Pos)
#define BLE_MAC_RALCNTL_RALNBDEV        BLE_MAC_RALCNTL_RALNBDEV_Msk

/*************** Bit definition for BLE_MAC_RALCURRENT register ***************/
#define BLE_MAC_RALCURRENT_RALCURRENTPTR_Pos  (0U)
#define BLE_MAC_RALCURRENT_RALCURRENTPTR_Msk  (0xFFFFUL << BLE_MAC_RALCURRENT_RALCURRENTPTR_Pos)
#define BLE_MAC_RALCURRENT_RALCURRENTPTR  BLE_MAC_RALCURRENT_RALCURRENTPTR_Msk

/************* Bit definition for BLE_MAC_RAL_LOCAL_RND register **************/
#define BLE_MAC_RAL_LOCAL_RND_LRND_VAL_Pos  (0U)
#define BLE_MAC_RAL_LOCAL_RND_LRND_VAL_Msk  (0x3FFFFFUL << BLE_MAC_RAL_LOCAL_RND_LRND_VAL_Pos)
#define BLE_MAC_RAL_LOCAL_RND_LRND_VAL  BLE_MAC_RAL_LOCAL_RND_LRND_VAL_Msk
#define BLE_MAC_RAL_LOCAL_RND_LRND_INIT_Pos  (31U)
#define BLE_MAC_RAL_LOCAL_RND_LRND_INIT_Msk  (0x1UL << BLE_MAC_RAL_LOCAL_RND_LRND_INIT_Pos)
#define BLE_MAC_RAL_LOCAL_RND_LRND_INIT  BLE_MAC_RAL_LOCAL_RND_LRND_INIT_Msk

/************** Bit definition for BLE_MAC_RAL_PEER_RND register **************/
#define BLE_MAC_RAL_PEER_RND_PRND_VAL_Pos  (0U)
#define BLE_MAC_RAL_PEER_RND_PRND_VAL_Msk  (0x3FFFFFUL << BLE_MAC_RAL_PEER_RND_PRND_VAL_Pos)
#define BLE_MAC_RAL_PEER_RND_PRND_VAL   BLE_MAC_RAL_PEER_RND_PRND_VAL_Msk
#define BLE_MAC_RAL_PEER_RND_PRND_INIT_Pos  (31U)
#define BLE_MAC_RAL_PEER_RND_PRND_INIT_Msk  (0x1UL << BLE_MAC_RAL_PEER_RND_PRND_INIT_Pos)
#define BLE_MAC_RAL_PEER_RND_PRND_INIT  BLE_MAC_RAL_PEER_RND_PRND_INIT_Msk

/*************** Bit definition for BLE_MAC_RCCAL_CTRL register ***************/
#define BLE_MAC_RCCAL_CTRL_RCCAL_LENGTH_Pos  (0U)
#define BLE_MAC_RCCAL_CTRL_RCCAL_LENGTH_Msk  (0xFFUL << BLE_MAC_RCCAL_CTRL_RCCAL_LENGTH_Pos)
#define BLE_MAC_RCCAL_CTRL_RCCAL_LENGTH  BLE_MAC_RCCAL_CTRL_RCCAL_LENGTH_Msk
#define BLE_MAC_RCCAL_CTRL_RCCAL_AUTO_Pos  (8U)
#define BLE_MAC_RCCAL_CTRL_RCCAL_AUTO_Msk  (0x1UL << BLE_MAC_RCCAL_CTRL_RCCAL_AUTO_Pos)
#define BLE_MAC_RCCAL_CTRL_RCCAL_AUTO   BLE_MAC_RCCAL_CTRL_RCCAL_AUTO_Msk
#define BLE_MAC_RCCAL_CTRL_RCCAL_START_Pos  (9U)
#define BLE_MAC_RCCAL_CTRL_RCCAL_START_Msk  (0x1UL << BLE_MAC_RCCAL_CTRL_RCCAL_START_Pos)
#define BLE_MAC_RCCAL_CTRL_RCCAL_START  BLE_MAC_RCCAL_CTRL_RCCAL_START_Msk

/************** Bit definition for BLE_MAC_RCCAL_RESULT register **************/
#define BLE_MAC_RCCAL_RESULT_RCCAL_RESULT_Pos  (0U)
#define BLE_MAC_RCCAL_RESULT_RCCAL_RESULT_Msk  (0x1FFFFFUL << BLE_MAC_RCCAL_RESULT_RCCAL_RESULT_Pos)
#define BLE_MAC_RCCAL_RESULT_RCCAL_RESULT  BLE_MAC_RCCAL_RESULT_RCCAL_RESULT_Msk
#define BLE_MAC_RCCAL_RESULT_RCCAL_DONE_Pos  (21U)
#define BLE_MAC_RCCAL_RESULT_RCCAL_DONE_Msk  (0x1UL << BLE_MAC_RCCAL_RESULT_RCCAL_DONE_Pos)
#define BLE_MAC_RCCAL_RESULT_RCCAL_DONE  BLE_MAC_RCCAL_RESULT_RCCAL_DONE_Msk

#endif
/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
