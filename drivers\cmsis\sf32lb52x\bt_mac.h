#ifndef __BT_MAC_H
#define __BT_MAC_H

typedef struct
{
    __IO uint32_t RWDMCNTL;
    __IO uint32_t DMVERSION;
    __IO uint32_t RSVD42;
    __IO uint32_t DMINTCNTL0;
    __IO uint32_t DMINTSTAT0;
    __IO uint32_t DMINTACK0;
    __IO uint32_t DMINTCNTL1;
    __IO uint32_t DMINTSTAT1;
    __IO uint32_t DMINTACK1;
    __IO uint32_t ACTFIFOSTAT;
    __IO uint32_t RSVD41;
    __IO uint32_t ETPTR;
    __IO uint32_t DEEPSLCNTL;
    __IO uint32_t RSVD40[3];
    __IO uint32_t FINECNTCORR;
    __IO uint32_t CLKNCNTCORR;
    __IO uint32_t RSVD39[2];
    __IO uint32_t DMDIAGCNTL;
    __IO uint32_t DMDIAGSTAT;
    __IO uint32_t DMDEBUGADDMAX;
    __IO uint32_t DMDEBUGADDMIN;
    __IO uint32_t DMERRORTYPESTAT;
    __IO uint32_t DMSWPROFILING;
    __IO uint32_t RSVD38[2];
    __IO uint32_t DMRADIOCNTL0;
    __IO uint32_t DMRADIOCNTL1;
    __IO uint32_t DMRADIOCNTL2;
    __IO uint32_t DMRADIOCNTL3;
    __IO uint32_t DMRADIOCNTL4;
    __IO uint32_t RSVD37[11];
    __IO uint32_t AESCNTL;
    __IO uint32_t AESKEY31_0;
    __IO uint32_t AESKEY63_32;
    __IO uint32_t AESKEY95_64;
    __IO uint32_t AESKEY127_96;
    __IO uint32_t DMAESPTR;
    __IO uint32_t TXMICVAL;
    __IO uint32_t RXMICVAL;
    __IO uint32_t PRIOSCHARB;
    __IO uint32_t RSVD36[3];
    __IO uint32_t DMTIMGENCNTL;
    __IO uint32_t FINETIMTGT;
    __IO uint32_t CLKNTGT1;
    __IO uint32_t HMICROSECTGT1;
    __IO uint32_t CLKNTGT2;
    __IO uint32_t HMICROSECTGT2;
    __IO uint32_t CLKNTGT3;
    __IO uint32_t HMICROSECTGT3;
    __IO uint32_t SLOTCLK;
    __IO uint32_t FINETIMECNT;
    __IO uint32_t RSVD35[2];
    __IO uint32_t ACTSCHCNTL;
    __IO uint32_t RCCAL_CTRL;
    __IO uint32_t RCCAL_RESULT;
    __IO uint32_t RSVD34[30];
    __IO uint32_t DFANCNTL;
    __IO uint32_t RSVD33[154];
    __IO uint32_t RWBTCNTL;
    __IO uint32_t BTVERSION;
    __IO uint32_t RSVD32;
    __IO uint32_t BTINTCNTL0;
    __IO uint32_t BTINTSTAT0;
    __IO uint32_t BTINTACK0;
    __IO uint32_t RSVD31[4];
    __IO uint32_t BTCURRENTRXDESCPTR;
    __IO uint32_t RSVD30[9];
    __IO uint32_t BTDIAGCNTL;
    __IO uint32_t BTDIAGSTAT;
    __IO uint32_t BTDEBUGADDMAX;
    __IO uint32_t BTDEBUGADDMIN;
    __IO uint32_t BTERRORTYPESTAT;
    __IO uint32_t BTSWPROFILING;
    __IO uint32_t RSVD29[4];
    __IO uint32_t BTRADIOCNTL2;
    __IO uint32_t BTRADIOCNTL3;
    __IO uint32_t RSVD28[3];
    __IO uint32_t BTRADIOPWRUPDN;
    __IO uint32_t BTRADIOTXRXTIM;
    __IO uint32_t RSVD27[15];
    __IO uint32_t BTRFTESTCNTL;
    __IO uint32_t BTRFTESTFREQ;
    __IO uint32_t BTRFTESTTXSTAT;
    __IO uint32_t BTRFTESTRXSTAT;
    __IO uint32_t RSVD26[13];
    __IO uint32_t STARTFRMCLKNTS;
    __IO uint32_t STARTFRMFINECNTTS;
    __IO uint32_t ENDFRMCLKNTS;
    __IO uint32_t ENDFRMFINECNTTS;
    __IO uint32_t SKIPFRMCLKNTS;
    __IO uint32_t SKIPFRMFINECNTTS;
    __IO uint32_t RSVD25;
    __IO uint32_t ABTRAINCNTL;
    __IO uint32_t EDRCNTL;
    __IO uint32_t RSVD24[2];
    __IO uint32_t PCACNTL0;
    __IO uint32_t PCACNTL1;
    __IO uint32_t PCASTAT;
    __IO uint32_t RSVD23;
    __IO uint32_t BTCOEXIFCNTL0;
    __IO uint32_t BTCOEXIFCNTL1;
    __IO uint32_t BTCOEXIFCNTL2;
    __IO uint32_t RSVD22;
    __IO uint32_t BTMPRIO0;
    __IO uint32_t BTMPRIO1;
    __IO uint32_t BTMPRIO2;
    __IO uint32_t RSVD21;
    __IO uint32_t COEXCHN0;
    __IO uint32_t COEXCHN1;
    __IO uint32_t COEXCHN2;
    __IO uint32_t RSVD20[37];
    __IO uint32_t ESCOCHANCNTL0;
    __IO uint32_t ESCOMUTECNTL0;
    __IO uint32_t ESCOCURRENTTXPTR0;
    __IO uint32_t ESCOCURRENTRXPTR0;
    __IO uint32_t ESCOLTCNTL0;
    __IO uint32_t ESCOTRCNTL0;
    __IO uint32_t ESCODAYCNT0;
    __IO uint32_t RSVD19;
    __IO uint32_t ESCOCHANCNTL1;
    __IO uint32_t ESCOMUTECNTL1;
    __IO uint32_t ESCOCURRENTTXPTR1;
    __IO uint32_t ESCOCURRENTRXPTR1;
    __IO uint32_t ESCOLTCNTL1;
    __IO uint32_t ESCOTRCNTL1;
    __IO uint32_t ESCODAYCNT1;
    __IO uint32_t RSVD18;
    __IO uint32_t ESCOCHANCNTL2;
    __IO uint32_t ESCOMUTECNTL2;
    __IO uint32_t ESCOCURRENTTXPTR2;
    __IO uint32_t ESCOCURRENTRXPTR2;
    __IO uint32_t ESCOLTCNTL2;
    __IO uint32_t ESCOTRCNTL2;
    __IO uint32_t ESCODAYCNT2;
    __IO uint32_t RSVD17;
    __IO uint32_t AUDIOCNTL0;
    __IO uint32_t AUDIOCNTL1;
    __IO uint32_t AUDIOCNTL2;
    __IO uint32_t RSVD16[97];
    __IO uint32_t RWBLECNTL;
    __IO uint32_t BLEVERSION;
    __IO uint32_t RSVD15;
    __IO uint32_t BLEINTCNTL0;
    __IO uint32_t BLEINTSTAT0;
    __IO uint32_t BLEINTACK0;
    __IO uint32_t RSVD14[4];
    __IO uint32_t BLECURRENTRXDESCPTR;
    __IO uint32_t RSVD13[9];
    __IO uint32_t BLEDIAGCNTL;
    __IO uint32_t BLEDIAGSTAT;
    __IO uint32_t BLEDEBUGADDMAX;
    __IO uint32_t BLEDEBUGADDMIN;
    __IO uint32_t BLEERRORTYPESTAT;
    __IO uint32_t BLESWPROFILING;
    __IO uint32_t RSVD12[4];
    __IO uint32_t BLERADIOCNTL2;
    __IO uint32_t BLERADIOCNTL3;
    __IO uint32_t BLERADIOPWRUPDN0;
    __IO uint32_t BLERADIOPWRUPDN1;
    __IO uint32_t BLERADIOPWRUPDN2;
    __IO uint32_t BLERADIOPWRUPDN3;
    __IO uint32_t BLERADIOTXRXTIM0;
    __IO uint32_t BLERADIOTXRXTIM1;
    __IO uint32_t BLERADIOTXRXTIM2;
    __IO uint32_t BLERADIOTXRXTIM3;
    __IO uint32_t RSVD11[12];
    __IO uint32_t BLERFTESTCNTL;
    __IO uint32_t BLERFTESTTXSTAT;
    __IO uint32_t BLERFTESTRXSTAT;
    __IO uint32_t RSVD10[14];
    __IO uint32_t STARTEVTCLKN;
    __IO uint32_t STARTEVTFINECNT;
    __IO uint32_t ENDEVTCLKN;
    __IO uint32_t ENDEVTFINECNT;
    __IO uint32_t SKIPEVTCLKN;
    __IO uint32_t SKIPEVTFINECNT;
    __IO uint32_t RSVD9;
    __IO uint32_t ADVTIM;
    __IO uint32_t ACTSCANCNTL;
    __IO uint32_t RSVD8[2];
    __IO uint32_t WPALCNTL;
    __IO uint32_t WPALCURRENT;
    __IO uint32_t SEARCH_TIMEOUT;
    __IO uint32_t RSVD7;
    __IO uint32_t BLECOEXIFCNTL0;
    __IO uint32_t BLECOEXIFCNTL1;
    __IO uint32_t BLECOEXIFCNTL2;
    __IO uint32_t RSVD6;
    __IO uint32_t BLEMPRIO0;
    __IO uint32_t BLEMPRIO1;
    __IO uint32_t BLEMPRIO2;
    __IO uint32_t RSVD5;
    __IO uint32_t RALCNTL;
    __IO uint32_t RALCURRENT;
    __IO uint32_t RAL_LOCAL_RND;
    __IO uint32_t RAL_PEER_RND;
    __IO uint32_t DFCNTL0_1US;
    __IO uint32_t DFCNTL0_2US;
    __IO uint32_t DFCNTL1_1US;
    __IO uint32_t DFCNTL1_2US;
    __IO uint32_t DFCURRENTPTR;
    __IO uint32_t DFANTCNTL;
    __IO uint32_t DFIFCNTL;
    __IO uint32_t RSVD4;
    __IO uint32_t FREQSELCNTL;
    __IO uint32_t FREQSELPTR;
    __IO uint32_t FREQSEL_CS1_SEED;
    __IO uint32_t FREQ_CS2_SEED;
    __IO uint32_t FREQSEL_LLCHMAP0;
    __IO uint32_t FREQSEL_LLCHMAP1;
    __IO uint32_t RSVD3[2];
    __IO uint32_t ISOCNTCNTL;
    __IO uint32_t ISOCNTSAMP;
    __IO uint32_t ISOCNTCORR;
    __IO uint32_t ISOINTCORR_HUS;
    __IO uint32_t ISOINTCNTL;
    __IO uint32_t ISOINTSTAT;
    __IO uint32_t ISOINTACK;
    __IO uint32_t RSVD2;
    __IO uint32_t ISOGPIOCNTL;
    __IO uint32_t RSVD1[3];
    __IO uint32_t ISOTIMERTGT0;
    __IO uint32_t ISOTIMERTGT1;
    __IO uint32_t ISOTIMERTGT2;
    __IO uint32_t ISOTIMERTGT3;
    __IO uint32_t ISOTIMERTGT4;
    __IO uint32_t ISOTIMERTGT5;
    __IO uint32_t ISOTIMERTGT6;
    __IO uint32_t ISOTIMERTGT7;
} BT_MAC_TypeDef;


/**************** Bit definition for BT_MAC_RWDMCNTL register *****************/
#define BT_MAC_RWDMCNTL_SWINT_REQ_Pos   (27U)
#define BT_MAC_RWDMCNTL_SWINT_REQ_Msk   (0x1UL << BT_MAC_RWDMCNTL_SWINT_REQ_Pos)
#define BT_MAC_RWDMCNTL_SWINT_REQ       BT_MAC_RWDMCNTL_SWINT_REQ_Msk
#define BT_MAC_RWDMCNTL_RADIOCNTL_SOFT_RST_Pos  (28U)
#define BT_MAC_RWDMCNTL_RADIOCNTL_SOFT_RST_Msk  (0x1UL << BT_MAC_RWDMCNTL_RADIOCNTL_SOFT_RST_Pos)
#define BT_MAC_RWDMCNTL_RADIOCNTL_SOFT_RST  BT_MAC_RWDMCNTL_RADIOCNTL_SOFT_RST_Msk
#define BT_MAC_RWDMCNTL_MASTER_TGSOFT_RST_Pos  (30U)
#define BT_MAC_RWDMCNTL_MASTER_TGSOFT_RST_Msk  (0x1UL << BT_MAC_RWDMCNTL_MASTER_TGSOFT_RST_Pos)
#define BT_MAC_RWDMCNTL_MASTER_TGSOFT_RST  BT_MAC_RWDMCNTL_MASTER_TGSOFT_RST_Msk
#define BT_MAC_RWDMCNTL_MASTER_SOFT_RST_Pos  (31U)
#define BT_MAC_RWDMCNTL_MASTER_SOFT_RST_Msk  (0x1UL << BT_MAC_RWDMCNTL_MASTER_SOFT_RST_Pos)
#define BT_MAC_RWDMCNTL_MASTER_SOFT_RST  BT_MAC_RWDMCNTL_MASTER_SOFT_RST_Msk

/**************** Bit definition for BT_MAC_DMVERSION register ****************/
#define BT_MAC_DMVERSION_BUILD_NUM_Pos  (0U)
#define BT_MAC_DMVERSION_BUILD_NUM_Msk  (0xFFUL << BT_MAC_DMVERSION_BUILD_NUM_Pos)
#define BT_MAC_DMVERSION_BUILD_NUM      BT_MAC_DMVERSION_BUILD_NUM_Msk
#define BT_MAC_DMVERSION_UPG_Pos        (8U)
#define BT_MAC_DMVERSION_UPG_Msk        (0xFFUL << BT_MAC_DMVERSION_UPG_Pos)
#define BT_MAC_DMVERSION_UPG            BT_MAC_DMVERSION_UPG_Msk
#define BT_MAC_DMVERSION_REL_Pos        (16U)
#define BT_MAC_DMVERSION_REL_Msk        (0xFFUL << BT_MAC_DMVERSION_REL_Pos)
#define BT_MAC_DMVERSION_REL            BT_MAC_DMVERSION_REL_Msk
#define BT_MAC_DMVERSION_TYP_Pos        (24U)
#define BT_MAC_DMVERSION_TYP_Msk        (0xFFUL << BT_MAC_DMVERSION_TYP_Pos)
#define BT_MAC_DMVERSION_TYP            BT_MAC_DMVERSION_TYP_Msk

/*************** Bit definition for BT_MAC_DMINTCNTL0 register ****************/
#define BT_MAC_DMINTCNTL0_ERRORINTMSK_Pos  (16U)
#define BT_MAC_DMINTCNTL0_ERRORINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL0_ERRORINTMSK_Pos)
#define BT_MAC_DMINTCNTL0_ERRORINTMSK   BT_MAC_DMINTCNTL0_ERRORINTMSK_Msk

/*************** Bit definition for BT_MAC_DMINTSTAT0 register ****************/
#define BT_MAC_DMINTSTAT0_ERRORINTSTAT_Pos  (16U)
#define BT_MAC_DMINTSTAT0_ERRORINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT0_ERRORINTSTAT_Pos)
#define BT_MAC_DMINTSTAT0_ERRORINTSTAT  BT_MAC_DMINTSTAT0_ERRORINTSTAT_Msk

/**************** Bit definition for BT_MAC_DMINTACK0 register ****************/
#define BT_MAC_DMINTACK0_ERRORINTACK_Pos  (16U)
#define BT_MAC_DMINTACK0_ERRORINTACK_Msk  (0x1UL << BT_MAC_DMINTACK0_ERRORINTACK_Pos)
#define BT_MAC_DMINTACK0_ERRORINTACK    BT_MAC_DMINTACK0_ERRORINTACK_Msk

/*************** Bit definition for BT_MAC_DMINTCNTL1 register ****************/
#define BT_MAC_DMINTCNTL1_CLKNINTMSK_Pos  (0U)
#define BT_MAC_DMINTCNTL1_CLKNINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_CLKNINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_CLKNINTMSK    BT_MAC_DMINTCNTL1_CLKNINTMSK_Msk
#define BT_MAC_DMINTCNTL1_SLPINTMSK_Pos  (1U)
#define BT_MAC_DMINTCNTL1_SLPINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_SLPINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_SLPINTMSK     BT_MAC_DMINTCNTL1_SLPINTMSK_Msk
#define BT_MAC_DMINTCNTL1_CRYPTINTMSK_Pos  (2U)
#define BT_MAC_DMINTCNTL1_CRYPTINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_CRYPTINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_CRYPTINTMSK   BT_MAC_DMINTCNTL1_CRYPTINTMSK_Msk
#define BT_MAC_DMINTCNTL1_SWINTMSK_Pos  (3U)
#define BT_MAC_DMINTCNTL1_SWINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_SWINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_SWINTMSK      BT_MAC_DMINTCNTL1_SWINTMSK_Msk
#define BT_MAC_DMINTCNTL1_FINETGTNTMSK_Pos  (4U)
#define BT_MAC_DMINTCNTL1_FINETGTNTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_FINETGTNTMSK_Pos)
#define BT_MAC_DMINTCNTL1_FINETGTNTMSK  BT_MAC_DMINTCNTL1_FINETGTNTMSK_Msk
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT1INTMSK_Pos  (5U)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT1INTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_TIMESTAMPTGT1INTMSK_Pos)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT1INTMSK  BT_MAC_DMINTCNTL1_TIMESTAMPTGT1INTMSK_Msk
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT2INTMSK_Pos  (6U)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT2INTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_TIMESTAMPTGT2INTMSK_Pos)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT2INTMSK  BT_MAC_DMINTCNTL1_TIMESTAMPTGT2INTMSK_Msk
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT3INTMSK_Pos  (7U)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT3INTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_TIMESTAMPTGT3INTMSK_Pos)
#define BT_MAC_DMINTCNTL1_TIMESTAMPTGT3INTMSK  BT_MAC_DMINTCNTL1_TIMESTAMPTGT3INTMSK_Msk
#define BT_MAC_DMINTCNTL1_RCCALINTMSK_Pos  (8U)
#define BT_MAC_DMINTCNTL1_RCCALINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_RCCALINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_RCCALINTMSK   BT_MAC_DMINTCNTL1_RCCALINTMSK_Msk
#define BT_MAC_DMINTCNTL1_FIFOINTMSK_Pos  (15U)
#define BT_MAC_DMINTCNTL1_FIFOINTMSK_Msk  (0x1UL << BT_MAC_DMINTCNTL1_FIFOINTMSK_Pos)
#define BT_MAC_DMINTCNTL1_FIFOINTMSK    BT_MAC_DMINTCNTL1_FIFOINTMSK_Msk
#define BT_MAC_DMINTCNTL1_CLKNINTSRVAL_Pos  (24U)
#define BT_MAC_DMINTCNTL1_CLKNINTSRVAL_Msk  (0xFUL << BT_MAC_DMINTCNTL1_CLKNINTSRVAL_Pos)
#define BT_MAC_DMINTCNTL1_CLKNINTSRVAL  BT_MAC_DMINTCNTL1_CLKNINTSRVAL_Msk
#define BT_MAC_DMINTCNTL1_CLKNINTSRMSK_Pos  (28U)
#define BT_MAC_DMINTCNTL1_CLKNINTSRMSK_Msk  (0x7UL << BT_MAC_DMINTCNTL1_CLKNINTSRMSK_Pos)
#define BT_MAC_DMINTCNTL1_CLKNINTSRMSK  BT_MAC_DMINTCNTL1_CLKNINTSRMSK_Msk

/*************** Bit definition for BT_MAC_DMINTSTAT1 register ****************/
#define BT_MAC_DMINTSTAT1_CLKNINTSTAT_Pos  (0U)
#define BT_MAC_DMINTSTAT1_CLKNINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_CLKNINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_CLKNINTSTAT   BT_MAC_DMINTSTAT1_CLKNINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_SLPINTSTAT_Pos  (1U)
#define BT_MAC_DMINTSTAT1_SLPINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_SLPINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_SLPINTSTAT    BT_MAC_DMINTSTAT1_SLPINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_CRYPTINTSTAT_Pos  (2U)
#define BT_MAC_DMINTSTAT1_CRYPTINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_CRYPTINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_CRYPTINTSTAT  BT_MAC_DMINTSTAT1_CRYPTINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_SWINTSTAT_Pos  (3U)
#define BT_MAC_DMINTSTAT1_SWINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_SWINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_SWINTSTAT     BT_MAC_DMINTSTAT1_SWINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_FINETGTINTSTAT_Pos  (4U)
#define BT_MAC_DMINTSTAT1_FINETGTINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_FINETGTINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_FINETGTINTSTAT  BT_MAC_DMINTSTAT1_FINETGTINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT1INTSTAT_Pos  (5U)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT1INTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_TIMESTAMPTGT1INTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT1INTSTAT  BT_MAC_DMINTSTAT1_TIMESTAMPTGT1INTSTAT_Msk
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT2INTSTAT_Pos  (6U)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT2INTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_TIMESTAMPTGT2INTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT2INTSTAT  BT_MAC_DMINTSTAT1_TIMESTAMPTGT2INTSTAT_Msk
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT3INTSTAT_Pos  (7U)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT3INTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_TIMESTAMPTGT3INTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_TIMESTAMPTGT3INTSTAT  BT_MAC_DMINTSTAT1_TIMESTAMPTGT3INTSTAT_Msk
#define BT_MAC_DMINTSTAT1_RCCALINTSTAT_Pos  (8U)
#define BT_MAC_DMINTSTAT1_RCCALINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_RCCALINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_RCCALINTSTAT  BT_MAC_DMINTSTAT1_RCCALINTSTAT_Msk
#define BT_MAC_DMINTSTAT1_FIFOINTSTAT_Pos  (15U)
#define BT_MAC_DMINTSTAT1_FIFOINTSTAT_Msk  (0x1UL << BT_MAC_DMINTSTAT1_FIFOINTSTAT_Pos)
#define BT_MAC_DMINTSTAT1_FIFOINTSTAT   BT_MAC_DMINTSTAT1_FIFOINTSTAT_Msk

/**************** Bit definition for BT_MAC_DMINTACK1 register ****************/
#define BT_MAC_DMINTACK1_CLKNTINTACK_Pos  (0U)
#define BT_MAC_DMINTACK1_CLKNTINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_CLKNTINTACK_Pos)
#define BT_MAC_DMINTACK1_CLKNTINTACK    BT_MAC_DMINTACK1_CLKNTINTACK_Msk
#define BT_MAC_DMINTACK1_SLPINTACK_Pos  (1U)
#define BT_MAC_DMINTACK1_SLPINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_SLPINTACK_Pos)
#define BT_MAC_DMINTACK1_SLPINTACK      BT_MAC_DMINTACK1_SLPINTACK_Msk
#define BT_MAC_DMINTACK1_CRYPTINTACK_Pos  (2U)
#define BT_MAC_DMINTACK1_CRYPTINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_CRYPTINTACK_Pos)
#define BT_MAC_DMINTACK1_CRYPTINTACK    BT_MAC_DMINTACK1_CRYPTINTACK_Msk
#define BT_MAC_DMINTACK1_SWINTACK_Pos   (3U)
#define BT_MAC_DMINTACK1_SWINTACK_Msk   (0x1UL << BT_MAC_DMINTACK1_SWINTACK_Pos)
#define BT_MAC_DMINTACK1_SWINTACK       BT_MAC_DMINTACK1_SWINTACK_Msk
#define BT_MAC_DMINTACK1_FINETGTINTACK_Pos  (4U)
#define BT_MAC_DMINTACK1_FINETGTINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_FINETGTINTACK_Pos)
#define BT_MAC_DMINTACK1_FINETGTINTACK  BT_MAC_DMINTACK1_FINETGTINTACK_Msk
#define BT_MAC_DMINTACK1_TIMESTAMPTGT1INTACK_Pos  (5U)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT1INTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_TIMESTAMPTGT1INTACK_Pos)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT1INTACK  BT_MAC_DMINTACK1_TIMESTAMPTGT1INTACK_Msk
#define BT_MAC_DMINTACK1_TIMESTAMPTGT2INTACK_Pos  (6U)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT2INTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_TIMESTAMPTGT2INTACK_Pos)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT2INTACK  BT_MAC_DMINTACK1_TIMESTAMPTGT2INTACK_Msk
#define BT_MAC_DMINTACK1_TIMESTAMPTGT3INTACK_Pos  (7U)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT3INTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_TIMESTAMPTGT3INTACK_Pos)
#define BT_MAC_DMINTACK1_TIMESTAMPTGT3INTACK  BT_MAC_DMINTACK1_TIMESTAMPTGT3INTACK_Msk
#define BT_MAC_DMINTACK1_RCCALINTACK_Pos  (8U)
#define BT_MAC_DMINTACK1_RCCALINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_RCCALINTACK_Pos)
#define BT_MAC_DMINTACK1_RCCALINTACK    BT_MAC_DMINTACK1_RCCALINTACK_Msk
#define BT_MAC_DMINTACK1_FIFOINTACK_Pos  (15U)
#define BT_MAC_DMINTACK1_FIFOINTACK_Msk  (0x1UL << BT_MAC_DMINTACK1_FIFOINTACK_Pos)
#define BT_MAC_DMINTACK1_FIFOINTACK     BT_MAC_DMINTACK1_FIFOINTACK_Msk

/*************** Bit definition for BT_MAC_ACTFIFOSTAT register ***************/
#define BT_MAC_ACTFIFOSTAT_STARTACTINTSTAT_Pos  (0U)
#define BT_MAC_ACTFIFOSTAT_STARTACTINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_STARTACTINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_STARTACTINTSTAT  BT_MAC_ACTFIFOSTAT_STARTACTINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_ENDACTINTSTAT_Pos  (1U)
#define BT_MAC_ACTFIFOSTAT_ENDACTINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_ENDACTINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_ENDACTINTSTAT  BT_MAC_ACTFIFOSTAT_ENDACTINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_SKIPACTINTSTAT_Pos  (2U)
#define BT_MAC_ACTFIFOSTAT_SKIPACTINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_SKIPACTINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_SKIPACTINTSTAT  BT_MAC_ACTFIFOSTAT_SKIPACTINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_TXINTSTAT_Pos  (3U)
#define BT_MAC_ACTFIFOSTAT_TXINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_TXINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_TXINTSTAT    BT_MAC_ACTFIFOSTAT_TXINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_RXINTSTAT_Pos  (4U)
#define BT_MAC_ACTFIFOSTAT_RXINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_RXINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_RXINTSTAT    BT_MAC_ACTFIFOSTAT_RXINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_ISOTXINTSTAT_Pos  (5U)
#define BT_MAC_ACTFIFOSTAT_ISOTXINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_ISOTXINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_ISOTXINTSTAT  BT_MAC_ACTFIFOSTAT_ISOTXINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_ISORXINTSTAT_Pos  (6U)
#define BT_MAC_ACTFIFOSTAT_ISORXINTSTAT_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_ISORXINTSTAT_Pos)
#define BT_MAC_ACTFIFOSTAT_ISORXINTSTAT  BT_MAC_ACTFIFOSTAT_ISORXINTSTAT_Msk
#define BT_MAC_ACTFIFOSTAT_ACTFLAG_Pos  (15U)
#define BT_MAC_ACTFIFOSTAT_ACTFLAG_Msk  (0x1UL << BT_MAC_ACTFIFOSTAT_ACTFLAG_Pos)
#define BT_MAC_ACTFIFOSTAT_ACTFLAG      BT_MAC_ACTFIFOSTAT_ACTFLAG_Msk
#define BT_MAC_ACTFIFOSTAT_CURRENT_ET_IDX_Pos  (24U)
#define BT_MAC_ACTFIFOSTAT_CURRENT_ET_IDX_Msk  (0xFUL << BT_MAC_ACTFIFOSTAT_CURRENT_ET_IDX_Pos)
#define BT_MAC_ACTFIFOSTAT_CURRENT_ET_IDX  BT_MAC_ACTFIFOSTAT_CURRENT_ET_IDX_Msk
#define BT_MAC_ACTFIFOSTAT_SKIP_ET_IDX_Pos  (28U)
#define BT_MAC_ACTFIFOSTAT_SKIP_ET_IDX_Msk  (0xFUL << BT_MAC_ACTFIFOSTAT_SKIP_ET_IDX_Pos)
#define BT_MAC_ACTFIFOSTAT_SKIP_ET_IDX  BT_MAC_ACTFIFOSTAT_SKIP_ET_IDX_Msk

/****************** Bit definition for BT_MAC_ETPTR register ******************/
#define BT_MAC_ETPTR_ETPTR_Pos          (0U)
#define BT_MAC_ETPTR_ETPTR_Msk          (0x3FFFUL << BT_MAC_ETPTR_ETPTR_Pos)
#define BT_MAC_ETPTR_ETPTR              BT_MAC_ETPTR_ETPTR_Msk

/*************** Bit definition for BT_MAC_DEEPSLCNTL register ****************/
#define BT_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Pos  (3U)
#define BT_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Msk  (0x1UL << BT_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Pos)
#define BT_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN  BT_MAC_DEEPSLCNTL_DEEP_SLEEP_CORR_EN_Msk
#define BT_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Pos  (4U)
#define BT_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Msk  (0x1UL << BT_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Pos)
#define BT_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT  BT_MAC_DEEPSLCNTL_CORR_MASK_CLKNINT_Msk

/*************** Bit definition for BT_MAC_FINECNTCORR register ***************/
#define BT_MAC_FINECNTCORR_FINECNTCORR_Pos  (0U)
#define BT_MAC_FINECNTCORR_FINECNTCORR_Msk  (0x3FFUL << BT_MAC_FINECNTCORR_FINECNTCORR_Pos)
#define BT_MAC_FINECNTCORR_FINECNTCORR  BT_MAC_FINECNTCORR_FINECNTCORR_Msk

/*************** Bit definition for BT_MAC_CLKNCNTCORR register ***************/
#define BT_MAC_CLKNCNTCORR_CLKNCNTCORR_Pos  (0U)
#define BT_MAC_CLKNCNTCORR_CLKNCNTCORR_Msk  (0xFFFFFFFUL << BT_MAC_CLKNCNTCORR_CLKNCNTCORR_Pos)
#define BT_MAC_CLKNCNTCORR_CLKNCNTCORR  BT_MAC_CLKNCNTCORR_CLKNCNTCORR_Msk
#define BT_MAC_CLKNCNTCORR_ABS_DELTA_Pos  (31U)
#define BT_MAC_CLKNCNTCORR_ABS_DELTA_Msk  (0x1UL << BT_MAC_CLKNCNTCORR_ABS_DELTA_Pos)
#define BT_MAC_CLKNCNTCORR_ABS_DELTA    BT_MAC_CLKNCNTCORR_ABS_DELTA_Msk

/*************** Bit definition for BT_MAC_DMDIAGCNTL register ****************/
#define BT_MAC_DMDIAGCNTL_DIAG0_Pos     (0U)
#define BT_MAC_DMDIAGCNTL_DIAG0_Msk     (0x3FUL << BT_MAC_DMDIAGCNTL_DIAG0_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG0         BT_MAC_DMDIAGCNTL_DIAG0_Msk
#define BT_MAC_DMDIAGCNTL_DIAG0_EN_Pos  (7U)
#define BT_MAC_DMDIAGCNTL_DIAG0_EN_Msk  (0x1UL << BT_MAC_DMDIAGCNTL_DIAG0_EN_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG0_EN      BT_MAC_DMDIAGCNTL_DIAG0_EN_Msk
#define BT_MAC_DMDIAGCNTL_DIAG1_Pos     (8U)
#define BT_MAC_DMDIAGCNTL_DIAG1_Msk     (0x3FUL << BT_MAC_DMDIAGCNTL_DIAG1_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG1         BT_MAC_DMDIAGCNTL_DIAG1_Msk
#define BT_MAC_DMDIAGCNTL_DIAG1_EN_Pos  (15U)
#define BT_MAC_DMDIAGCNTL_DIAG1_EN_Msk  (0x1UL << BT_MAC_DMDIAGCNTL_DIAG1_EN_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG1_EN      BT_MAC_DMDIAGCNTL_DIAG1_EN_Msk
#define BT_MAC_DMDIAGCNTL_DIAG2_Pos     (16U)
#define BT_MAC_DMDIAGCNTL_DIAG2_Msk     (0x3FUL << BT_MAC_DMDIAGCNTL_DIAG2_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG2         BT_MAC_DMDIAGCNTL_DIAG2_Msk
#define BT_MAC_DMDIAGCNTL_DIAG2_EN_Pos  (23U)
#define BT_MAC_DMDIAGCNTL_DIAG2_EN_Msk  (0x1UL << BT_MAC_DMDIAGCNTL_DIAG2_EN_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG2_EN      BT_MAC_DMDIAGCNTL_DIAG2_EN_Msk
#define BT_MAC_DMDIAGCNTL_DIAG3_Pos     (24U)
#define BT_MAC_DMDIAGCNTL_DIAG3_Msk     (0x3FUL << BT_MAC_DMDIAGCNTL_DIAG3_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG3         BT_MAC_DMDIAGCNTL_DIAG3_Msk
#define BT_MAC_DMDIAGCNTL_DIAG3_EN_Pos  (31U)
#define BT_MAC_DMDIAGCNTL_DIAG3_EN_Msk  (0x1UL << BT_MAC_DMDIAGCNTL_DIAG3_EN_Pos)
#define BT_MAC_DMDIAGCNTL_DIAG3_EN      BT_MAC_DMDIAGCNTL_DIAG3_EN_Msk

/*************** Bit definition for BT_MAC_DMDIAGSTAT register ****************/
#define BT_MAC_DMDIAGSTAT_DIAG0STAT_Pos  (0U)
#define BT_MAC_DMDIAGSTAT_DIAG0STAT_Msk  (0xFFUL << BT_MAC_DMDIAGSTAT_DIAG0STAT_Pos)
#define BT_MAC_DMDIAGSTAT_DIAG0STAT     BT_MAC_DMDIAGSTAT_DIAG0STAT_Msk
#define BT_MAC_DMDIAGSTAT_DIAG1STAT_Pos  (8U)
#define BT_MAC_DMDIAGSTAT_DIAG1STAT_Msk  (0xFFUL << BT_MAC_DMDIAGSTAT_DIAG1STAT_Pos)
#define BT_MAC_DMDIAGSTAT_DIAG1STAT     BT_MAC_DMDIAGSTAT_DIAG1STAT_Msk
#define BT_MAC_DMDIAGSTAT_DIAG2STAT_Pos  (16U)
#define BT_MAC_DMDIAGSTAT_DIAG2STAT_Msk  (0xFFUL << BT_MAC_DMDIAGSTAT_DIAG2STAT_Pos)
#define BT_MAC_DMDIAGSTAT_DIAG2STAT     BT_MAC_DMDIAGSTAT_DIAG2STAT_Msk
#define BT_MAC_DMDIAGSTAT_DIAG3STAT_Pos  (24U)
#define BT_MAC_DMDIAGSTAT_DIAG3STAT_Msk  (0xFFUL << BT_MAC_DMDIAGSTAT_DIAG3STAT_Pos)
#define BT_MAC_DMDIAGSTAT_DIAG3STAT     BT_MAC_DMDIAGSTAT_DIAG3STAT_Msk

/************** Bit definition for BT_MAC_DMDEBUGADDMAX register **************/
#define BT_MAC_DMDEBUGADDMAX_EM_ADDMAX_Pos  (0U)
#define BT_MAC_DMDEBUGADDMAX_EM_ADDMAX_Msk  (0xFFFFUL << BT_MAC_DMDEBUGADDMAX_EM_ADDMAX_Pos)
#define BT_MAC_DMDEBUGADDMAX_EM_ADDMAX  BT_MAC_DMDEBUGADDMAX_EM_ADDMAX_Msk
#define BT_MAC_DMDEBUGADDMAX_REG_ADDMAX_Pos  (16U)
#define BT_MAC_DMDEBUGADDMAX_REG_ADDMAX_Msk  (0xFFFFUL << BT_MAC_DMDEBUGADDMAX_REG_ADDMAX_Pos)
#define BT_MAC_DMDEBUGADDMAX_REG_ADDMAX  BT_MAC_DMDEBUGADDMAX_REG_ADDMAX_Msk

/************** Bit definition for BT_MAC_DMDEBUGADDMIN register **************/
#define BT_MAC_DMDEBUGADDMIN_EM_ADDMIN_Pos  (0U)
#define BT_MAC_DMDEBUGADDMIN_EM_ADDMIN_Msk  (0xFFFFUL << BT_MAC_DMDEBUGADDMIN_EM_ADDMIN_Pos)
#define BT_MAC_DMDEBUGADDMIN_EM_ADDMIN  BT_MAC_DMDEBUGADDMIN_EM_ADDMIN_Msk
#define BT_MAC_DMDEBUGADDMIN_REG_ADDMIN_Pos  (16U)
#define BT_MAC_DMDEBUGADDMIN_REG_ADDMIN_Msk  (0xFFFFUL << BT_MAC_DMDEBUGADDMIN_REG_ADDMIN_Pos)
#define BT_MAC_DMDEBUGADDMIN_REG_ADDMIN  BT_MAC_DMDEBUGADDMIN_REG_ADDMIN_Msk

/************* Bit definition for BT_MAC_DMERRORTYPESTAT register *************/
#define BT_MAC_DMERRORTYPESTAT_RADIO_EMACC_ERROR_Pos  (0U)
#define BT_MAC_DMERRORTYPESTAT_RADIO_EMACC_ERROR_Msk  (0x1UL << BT_MAC_DMERRORTYPESTAT_RADIO_EMACC_ERROR_Pos)
#define BT_MAC_DMERRORTYPESTAT_RADIO_EMACC_ERROR  BT_MAC_DMERRORTYPESTAT_RADIO_EMACC_ERROR_Msk
#define BT_MAC_DMERRORTYPESTAT_FIFOWRITEERR_Pos  (1U)
#define BT_MAC_DMERRORTYPESTAT_FIFOWRITEERR_Msk  (0x1UL << BT_MAC_DMERRORTYPESTAT_FIFOWRITEERR_Pos)
#define BT_MAC_DMERRORTYPESTAT_FIFOWRITEERR  BT_MAC_DMERRORTYPESTAT_FIFOWRITEERR_Msk
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos  (2U)
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk  (0x1UL << BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos)
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR  BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos  (3U)
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk  (0x1UL << BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos)
#define BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_APFM_ERROR  BT_MAC_DMERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk

/************** Bit definition for BT_MAC_DMSWPROFILING register **************/
#define BT_MAC_DMSWPROFILING_SWPROF_Pos  (0U)
#define BT_MAC_DMSWPROFILING_SWPROF_Msk  (0xFFFFFFFFUL << BT_MAC_DMSWPROFILING_SWPROF_Pos)
#define BT_MAC_DMSWPROFILING_SWPROF     BT_MAC_DMSWPROFILING_SWPROF_Msk

/************** Bit definition for BT_MAC_DMRADIOCNTL0 register ***************/
#define BT_MAC_DMRADIOCNTL0_PHY_RF_RXOFF_DELAY_Pos  (0U)
#define BT_MAC_DMRADIOCNTL0_PHY_RF_RXOFF_DELAY_Msk  (0xFFUL << BT_MAC_DMRADIOCNTL0_PHY_RF_RXOFF_DELAY_Pos)
#define BT_MAC_DMRADIOCNTL0_PHY_RF_RXOFF_DELAY  BT_MAC_DMRADIOCNTL0_PHY_RF_RXOFF_DELAY_Msk
#define BT_MAC_DMRADIOCNTL0_PHY_RF_TXOFF_DELAY_Pos  (8U)
#define BT_MAC_DMRADIOCNTL0_PHY_RF_TXOFF_DELAY_Msk  (0xFFUL << BT_MAC_DMRADIOCNTL0_PHY_RF_TXOFF_DELAY_Pos)
#define BT_MAC_DMRADIOCNTL0_PHY_RF_TXOFF_DELAY  BT_MAC_DMRADIOCNTL0_PHY_RF_TXOFF_DELAY_Msk
#define BT_MAC_DMRADIOCNTL0_PHY_RXON_DELAY_Pos  (16U)
#define BT_MAC_DMRADIOCNTL0_PHY_RXON_DELAY_Msk  (0xFFUL << BT_MAC_DMRADIOCNTL0_PHY_RXON_DELAY_Pos)
#define BT_MAC_DMRADIOCNTL0_PHY_RXON_DELAY  BT_MAC_DMRADIOCNTL0_PHY_RXON_DELAY_Msk
#define BT_MAC_DMRADIOCNTL0_PHY_TXON_DELAY_Pos  (24U)
#define BT_MAC_DMRADIOCNTL0_PHY_TXON_DELAY_Msk  (0xFFUL << BT_MAC_DMRADIOCNTL0_PHY_TXON_DELAY_Pos)
#define BT_MAC_DMRADIOCNTL0_PHY_TXON_DELAY  BT_MAC_DMRADIOCNTL0_PHY_TXON_DELAY_Msk

/************** Bit definition for BT_MAC_DMRADIOCNTL1 register ***************/
#define BT_MAC_DMRADIOCNTL1_RX_SAMP_DLY_Pos  (4U)
#define BT_MAC_DMRADIOCNTL1_RX_SAMP_DLY_Msk  (0xFUL << BT_MAC_DMRADIOCNTL1_RX_SAMP_DLY_Pos)
#define BT_MAC_DMRADIOCNTL1_RX_SAMP_DLY  BT_MAC_DMRADIOCNTL1_RX_SAMP_DLY_Msk
#define BT_MAC_DMRADIOCNTL1_EXT_MASTER_Pos  (9U)
#define BT_MAC_DMRADIOCNTL1_EXT_MASTER_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_EXT_MASTER_Pos)
#define BT_MAC_DMRADIOCNTL1_EXT_MASTER  BT_MAC_DMRADIOCNTL1_EXT_MASTER_Msk
#define BT_MAC_DMRADIOCNTL1_EXT_SLAVE_Pos  (10U)
#define BT_MAC_DMRADIOCNTL1_EXT_SLAVE_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_EXT_SLAVE_Pos)
#define BT_MAC_DMRADIOCNTL1_EXT_SLAVE   BT_MAC_DMRADIOCNTL1_EXT_SLAVE_Msk
#define BT_MAC_DMRADIOCNTL1_DBGTRIGSEL_Pos  (11U)
#define BT_MAC_DMRADIOCNTL1_DBGTRIGSEL_Msk  (0x7UL << BT_MAC_DMRADIOCNTL1_DBGTRIGSEL_Pos)
#define BT_MAC_DMRADIOCNTL1_DBGTRIGSEL  BT_MAC_DMRADIOCNTL1_DBGTRIGSEL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_VAL_Pos  (14U)
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_VAL_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_VAL_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_VAL  BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_VAL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_Pos  (15U)
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE  BT_MAC_DMRADIOCNTL1_FORCE_NBT_BLE_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE_VAL_Pos  (16U)
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE_VAL_Msk  (0x3UL << BT_MAC_DMRADIOCNTL1_FORCE_RATE_VAL_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE_VAL  BT_MAC_DMRADIOCNTL1_FORCE_RATE_VAL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE_Pos  (18U)
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_RATE_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_RATE  BT_MAC_DMRADIOCNTL1_FORCE_RATE_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_RX_VAL_Pos  (19U)
#define BT_MAC_DMRADIOCNTL1_FORCE_RX_VAL_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_RX_VAL_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_RX_VAL  BT_MAC_DMRADIOCNTL1_FORCE_RX_VAL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_RX_Pos  (20U)
#define BT_MAC_DMRADIOCNTL1_FORCE_RX_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_RX_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_RX    BT_MAC_DMRADIOCNTL1_FORCE_RX_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_TX_VAL_Pos  (21U)
#define BT_MAC_DMRADIOCNTL1_FORCE_TX_VAL_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_TX_VAL_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_TX_VAL  BT_MAC_DMRADIOCNTL1_FORCE_TX_VAL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_TX_Pos  (22U)
#define BT_MAC_DMRADIOCNTL1_FORCE_TX_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_TX_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_TX    BT_MAC_DMRADIOCNTL1_FORCE_TX_Msk
#define BT_MAC_DMRADIOCNTL1_CHANNEL_Pos  (23U)
#define BT_MAC_DMRADIOCNTL1_CHANNEL_Msk  (0x7FUL << BT_MAC_DMRADIOCNTL1_CHANNEL_Pos)
#define BT_MAC_DMRADIOCNTL1_CHANNEL     BT_MAC_DMRADIOCNTL1_CHANNEL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_CHANNEL_Pos  (30U)
#define BT_MAC_DMRADIOCNTL1_FORCE_CHANNEL_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_CHANNEL_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_CHANNEL  BT_MAC_DMRADIOCNTL1_FORCE_CHANNEL_Msk
#define BT_MAC_DMRADIOCNTL1_FORCE_SYNCWORD_Pos  (31U)
#define BT_MAC_DMRADIOCNTL1_FORCE_SYNCWORD_Msk  (0x1UL << BT_MAC_DMRADIOCNTL1_FORCE_SYNCWORD_Pos)
#define BT_MAC_DMRADIOCNTL1_FORCE_SYNCWORD  BT_MAC_DMRADIOCNTL1_FORCE_SYNCWORD_Msk

/************** Bit definition for BT_MAC_DMRADIOCNTL2 register ***************/
#define BT_MAC_DMRADIOCNTL2_SYNCWORD1_Pos  (0U)
#define BT_MAC_DMRADIOCNTL2_SYNCWORD1_Msk  (0xFFFFFFFFUL << BT_MAC_DMRADIOCNTL2_SYNCWORD1_Pos)
#define BT_MAC_DMRADIOCNTL2_SYNCWORD1   BT_MAC_DMRADIOCNTL2_SYNCWORD1_Msk

/************** Bit definition for BT_MAC_DMRADIOCNTL3 register ***************/
#define BT_MAC_DMRADIOCNTL3_SYNCWORD2_Pos  (0U)
#define BT_MAC_DMRADIOCNTL3_SYNCWORD2_Msk  (0xFFFFFFFFUL << BT_MAC_DMRADIOCNTL3_SYNCWORD2_Pos)
#define BT_MAC_DMRADIOCNTL3_SYNCWORD2   BT_MAC_DMRADIOCNTL3_SYNCWORD2_Msk

/************** Bit definition for BT_MAC_DMRADIOCNTL4 register ***************/
#define BT_MAC_DMRADIOCNTL4_CHPTR_Pos   (0U)
#define BT_MAC_DMRADIOCNTL4_CHPTR_Msk   (0xFFFFFFFFUL << BT_MAC_DMRADIOCNTL4_CHPTR_Pos)
#define BT_MAC_DMRADIOCNTL4_CHPTR       BT_MAC_DMRADIOCNTL4_CHPTR_Msk

/***************** Bit definition for BT_MAC_AESCNTL register *****************/
#define BT_MAC_AESCNTL_AES_START_Pos    (0U)
#define BT_MAC_AESCNTL_AES_START_Msk    (0x1UL << BT_MAC_AESCNTL_AES_START_Pos)
#define BT_MAC_AESCNTL_AES_START        BT_MAC_AESCNTL_AES_START_Msk
#define BT_MAC_AESCNTL_AES_MODE_Pos     (1U)
#define BT_MAC_AESCNTL_AES_MODE_Msk     (0x1UL << BT_MAC_AESCNTL_AES_MODE_Pos)
#define BT_MAC_AESCNTL_AES_MODE         BT_MAC_AESCNTL_AES_MODE_Msk
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR_VAL_Pos  (2U)
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR_VAL_Msk  (0x3FUL << BT_MAC_AESCNTL_FORCE_POLAR_PWR_VAL_Pos)
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR_VAL  BT_MAC_AESCNTL_FORCE_POLAR_PWR_VAL_Msk
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR_Pos  (8U)
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR_Msk  (0x1UL << BT_MAC_AESCNTL_FORCE_POLAR_PWR_Pos)
#define BT_MAC_AESCNTL_FORCE_POLAR_PWR  BT_MAC_AESCNTL_FORCE_POLAR_PWR_Msk
#define BT_MAC_AESCNTL_FORCE_IQ_PWR_VAL_Pos  (9U)
#define BT_MAC_AESCNTL_FORCE_IQ_PWR_VAL_Msk  (0x7UL << BT_MAC_AESCNTL_FORCE_IQ_PWR_VAL_Pos)
#define BT_MAC_AESCNTL_FORCE_IQ_PWR_VAL  BT_MAC_AESCNTL_FORCE_IQ_PWR_VAL_Msk
#define BT_MAC_AESCNTL_FORCE_IQ_PWR_Pos  (12U)
#define BT_MAC_AESCNTL_FORCE_IQ_PWR_Msk  (0x1UL << BT_MAC_AESCNTL_FORCE_IQ_PWR_Pos)
#define BT_MAC_AESCNTL_FORCE_IQ_PWR     BT_MAC_AESCNTL_FORCE_IQ_PWR_Msk

/*************** Bit definition for BT_MAC_AESKEY31_0 register ****************/
#define BT_MAC_AESKEY31_0_AESKEY31_0_Pos  (0U)
#define BT_MAC_AESKEY31_0_AESKEY31_0_Msk  (0xFFFFFFFFUL << BT_MAC_AESKEY31_0_AESKEY31_0_Pos)
#define BT_MAC_AESKEY31_0_AESKEY31_0    BT_MAC_AESKEY31_0_AESKEY31_0_Msk

/*************** Bit definition for BT_MAC_AESKEY63_32 register ***************/
#define BT_MAC_AESKEY63_32_AESKEY63_32_Pos  (0U)
#define BT_MAC_AESKEY63_32_AESKEY63_32_Msk  (0xFFFFFFFFUL << BT_MAC_AESKEY63_32_AESKEY63_32_Pos)
#define BT_MAC_AESKEY63_32_AESKEY63_32  BT_MAC_AESKEY63_32_AESKEY63_32_Msk

/*************** Bit definition for BT_MAC_AESKEY95_64 register ***************/
#define BT_MAC_AESKEY95_64_AESKEY95_64_Pos  (0U)
#define BT_MAC_AESKEY95_64_AESKEY95_64_Msk  (0xFFFFFFFFUL << BT_MAC_AESKEY95_64_AESKEY95_64_Pos)
#define BT_MAC_AESKEY95_64_AESKEY95_64  BT_MAC_AESKEY95_64_AESKEY95_64_Msk

/************** Bit definition for BT_MAC_AESKEY127_96 register ***************/
#define BT_MAC_AESKEY127_96_AESKEY127_96_Pos  (0U)
#define BT_MAC_AESKEY127_96_AESKEY127_96_Msk  (0xFFFFFFFFUL << BT_MAC_AESKEY127_96_AESKEY127_96_Pos)
#define BT_MAC_AESKEY127_96_AESKEY127_96  BT_MAC_AESKEY127_96_AESKEY127_96_Msk

/**************** Bit definition for BT_MAC_DMAESPTR register *****************/
#define BT_MAC_DMAESPTR_AESPTR_Pos      (0U)
#define BT_MAC_DMAESPTR_AESPTR_Msk      (0xFFFFUL << BT_MAC_DMAESPTR_AESPTR_Pos)
#define BT_MAC_DMAESPTR_AESPTR          BT_MAC_DMAESPTR_AESPTR_Msk

/**************** Bit definition for BT_MAC_TXMICVAL register *****************/
#define BT_MAC_TXMICVAL_TXMICVAL_Pos    (0U)
#define BT_MAC_TXMICVAL_TXMICVAL_Msk    (0xFFFFFFFFUL << BT_MAC_TXMICVAL_TXMICVAL_Pos)
#define BT_MAC_TXMICVAL_TXMICVAL        BT_MAC_TXMICVAL_TXMICVAL_Msk

/**************** Bit definition for BT_MAC_RXMICVAL register *****************/
#define BT_MAC_RXMICVAL_RXMICVAL_Pos    (0U)
#define BT_MAC_RXMICVAL_RXMICVAL_Msk    (0xFFFFFFFFUL << BT_MAC_RXMICVAL_RXMICVAL_Pos)
#define BT_MAC_RXMICVAL_RXMICVAL        BT_MAC_RXMICVAL_RXMICVAL_Msk

/*************** Bit definition for BT_MAC_PRIOSCHARB register ****************/
#define BT_MAC_PRIOSCHARB_BRPRIOMODE_Pos  (0U)
#define BT_MAC_PRIOSCHARB_BRPRIOMODE_Msk  (0x1UL << BT_MAC_PRIOSCHARB_BRPRIOMODE_Pos)
#define BT_MAC_PRIOSCHARB_BRPRIOMODE    BT_MAC_PRIOSCHARB_BRPRIOMODE_Msk
#define BT_MAC_PRIOSCHARB_BLEPRIOMODE_Pos  (16U)
#define BT_MAC_PRIOSCHARB_BLEPRIOMODE_Msk  (0x1UL << BT_MAC_PRIOSCHARB_BLEPRIOMODE_Pos)
#define BT_MAC_PRIOSCHARB_BLEPRIOMODE   BT_MAC_PRIOSCHARB_BLEPRIOMODE_Msk

/************** Bit definition for BT_MAC_DMTIMGENCNTL register ***************/
#define BT_MAC_DMTIMGENCNTL_PREFETCH_TIME_Pos  (0U)
#define BT_MAC_DMTIMGENCNTL_PREFETCH_TIME_Msk  (0x1FFUL << BT_MAC_DMTIMGENCNTL_PREFETCH_TIME_Pos)
#define BT_MAC_DMTIMGENCNTL_PREFETCH_TIME  BT_MAC_DMTIMGENCNTL_PREFETCH_TIME_Msk
#define BT_MAC_DMTIMGENCNTL_PREFETCHABORT_TIME_Pos  (16U)
#define BT_MAC_DMTIMGENCNTL_PREFETCHABORT_TIME_Msk  (0x3FFUL << BT_MAC_DMTIMGENCNTL_PREFETCHABORT_TIME_Pos)
#define BT_MAC_DMTIMGENCNTL_PREFETCHABORT_TIME  BT_MAC_DMTIMGENCNTL_PREFETCHABORT_TIME_Msk

/*************** Bit definition for BT_MAC_FINETIMTGT register ****************/
#define BT_MAC_FINETIMTGT_FINETARGET_Pos  (0U)
#define BT_MAC_FINETIMTGT_FINETARGET_Msk  (0xFFFFFFFUL << BT_MAC_FINETIMTGT_FINETARGET_Pos)
#define BT_MAC_FINETIMTGT_FINETARGET    BT_MAC_FINETIMTGT_FINETARGET_Msk

/**************** Bit definition for BT_MAC_CLKNTGT1 register *****************/
#define BT_MAC_CLKNTGT1_CLKNTARGET_Pos  (0U)
#define BT_MAC_CLKNTGT1_CLKNTARGET_Msk  (0xFFFFFFFUL << BT_MAC_CLKNTGT1_CLKNTARGET_Pos)
#define BT_MAC_CLKNTGT1_CLKNTARGET      BT_MAC_CLKNTGT1_CLKNTARGET_Msk

/************** Bit definition for BT_MAC_HMICROSECTGT1 register **************/
#define BT_MAC_HMICROSECTGT1_HMICROSECTARGET_Pos  (0U)
#define BT_MAC_HMICROSECTGT1_HMICROSECTARGET_Msk  (0x3FFUL << BT_MAC_HMICROSECTGT1_HMICROSECTARGET_Pos)
#define BT_MAC_HMICROSECTGT1_HMICROSECTARGET  BT_MAC_HMICROSECTGT1_HMICROSECTARGET_Msk

/**************** Bit definition for BT_MAC_CLKNTGT2 register *****************/
#define BT_MAC_CLKNTGT2_CLKNTARGET_Pos  (0U)
#define BT_MAC_CLKNTGT2_CLKNTARGET_Msk  (0xFFFFFFFUL << BT_MAC_CLKNTGT2_CLKNTARGET_Pos)
#define BT_MAC_CLKNTGT2_CLKNTARGET      BT_MAC_CLKNTGT2_CLKNTARGET_Msk

/************** Bit definition for BT_MAC_HMICROSECTGT2 register **************/
#define BT_MAC_HMICROSECTGT2_HMICROSECTARGET_Pos  (0U)
#define BT_MAC_HMICROSECTGT2_HMICROSECTARGET_Msk  (0x3FFUL << BT_MAC_HMICROSECTGT2_HMICROSECTARGET_Pos)
#define BT_MAC_HMICROSECTGT2_HMICROSECTARGET  BT_MAC_HMICROSECTGT2_HMICROSECTARGET_Msk

/**************** Bit definition for BT_MAC_CLKNTGT3 register *****************/
#define BT_MAC_CLKNTGT3_CLKNTARGET_Pos  (0U)
#define BT_MAC_CLKNTGT3_CLKNTARGET_Msk  (0xFFFFFFFUL << BT_MAC_CLKNTGT3_CLKNTARGET_Pos)
#define BT_MAC_CLKNTGT3_CLKNTARGET      BT_MAC_CLKNTGT3_CLKNTARGET_Msk

/************** Bit definition for BT_MAC_HMICROSECTGT3 register **************/
#define BT_MAC_HMICROSECTGT3_HMICROSECTARGET_Pos  (0U)
#define BT_MAC_HMICROSECTGT3_HMICROSECTARGET_Msk  (0x3FFUL << BT_MAC_HMICROSECTGT3_HMICROSECTARGET_Pos)
#define BT_MAC_HMICROSECTGT3_HMICROSECTARGET  BT_MAC_HMICROSECTGT3_HMICROSECTARGET_Msk

/***************** Bit definition for BT_MAC_SLOTCLK register *****************/
#define BT_MAC_SLOTCLK_SCLK_Pos         (0U)
#define BT_MAC_SLOTCLK_SCLK_Msk         (0xFFFFFFFUL << BT_MAC_SLOTCLK_SCLK_Pos)
#define BT_MAC_SLOTCLK_SCLK             BT_MAC_SLOTCLK_SCLK_Msk
#define BT_MAC_SLOTCLK_CLKN_UPD_Pos     (30U)
#define BT_MAC_SLOTCLK_CLKN_UPD_Msk     (0x1UL << BT_MAC_SLOTCLK_CLKN_UPD_Pos)
#define BT_MAC_SLOTCLK_CLKN_UPD         BT_MAC_SLOTCLK_CLKN_UPD_Msk
#define BT_MAC_SLOTCLK_SAMP_Pos         (31U)
#define BT_MAC_SLOTCLK_SAMP_Msk         (0x1UL << BT_MAC_SLOTCLK_SAMP_Pos)
#define BT_MAC_SLOTCLK_SAMP             BT_MAC_SLOTCLK_SAMP_Msk

/*************** Bit definition for BT_MAC_FINETIMECNT register ***************/
#define BT_MAC_FINETIMECNT_FINECNT_Pos  (0U)
#define BT_MAC_FINETIMECNT_FINECNT_Msk  (0x3FFUL << BT_MAC_FINETIMECNT_FINECNT_Pos)
#define BT_MAC_FINETIMECNT_FINECNT      BT_MAC_FINETIMECNT_FINECNT_Msk

/*************** Bit definition for BT_MAC_ACTSCHCNTL register ****************/
#define BT_MAC_ACTSCHCNTL_ENTRY_IDX_Pos  (0U)
#define BT_MAC_ACTSCHCNTL_ENTRY_IDX_Msk  (0xFUL << BT_MAC_ACTSCHCNTL_ENTRY_IDX_Pos)
#define BT_MAC_ACTSCHCNTL_ENTRY_IDX     BT_MAC_ACTSCHCNTL_ENTRY_IDX_Msk
#define BT_MAC_ACTSCHCNTL_START_ACT_Pos  (31U)
#define BT_MAC_ACTSCHCNTL_START_ACT_Msk  (0x1UL << BT_MAC_ACTSCHCNTL_START_ACT_Pos)
#define BT_MAC_ACTSCHCNTL_START_ACT     BT_MAC_ACTSCHCNTL_START_ACT_Msk

/*************** Bit definition for BT_MAC_RCCAL_CTRL register ****************/
#define BT_MAC_RCCAL_CTRL_RCCAL_LENGTH_Pos  (0U)
#define BT_MAC_RCCAL_CTRL_RCCAL_LENGTH_Msk  (0xFFFFUL << BT_MAC_RCCAL_CTRL_RCCAL_LENGTH_Pos)
#define BT_MAC_RCCAL_CTRL_RCCAL_LENGTH  BT_MAC_RCCAL_CTRL_RCCAL_LENGTH_Msk
#define BT_MAC_RCCAL_CTRL_RCCAL_AUTO_Pos  (16U)
#define BT_MAC_RCCAL_CTRL_RCCAL_AUTO_Msk  (0x1UL << BT_MAC_RCCAL_CTRL_RCCAL_AUTO_Pos)
#define BT_MAC_RCCAL_CTRL_RCCAL_AUTO    BT_MAC_RCCAL_CTRL_RCCAL_AUTO_Msk
#define BT_MAC_RCCAL_CTRL_RCCAL_START_Pos  (17U)
#define BT_MAC_RCCAL_CTRL_RCCAL_START_Msk  (0x1UL << BT_MAC_RCCAL_CTRL_RCCAL_START_Pos)
#define BT_MAC_RCCAL_CTRL_RCCAL_START   BT_MAC_RCCAL_CTRL_RCCAL_START_Msk
#define BT_MAC_RCCAL_CTRL_RCCAL_STOP_Pos  (18U)
#define BT_MAC_RCCAL_CTRL_RCCAL_STOP_Msk  (0x1UL << BT_MAC_RCCAL_CTRL_RCCAL_STOP_Pos)
#define BT_MAC_RCCAL_CTRL_RCCAL_STOP    BT_MAC_RCCAL_CTRL_RCCAL_STOP_Msk
#define BT_MAC_RCCAL_CTRL_CON_NUM_Pos   (19U)
#define BT_MAC_RCCAL_CTRL_CON_NUM_Msk   (0x3FFUL << BT_MAC_RCCAL_CTRL_CON_NUM_Pos)
#define BT_MAC_RCCAL_CTRL_CON_NUM       BT_MAC_RCCAL_CTRL_CON_NUM_Msk
#define BT_MAC_RCCAL_CTRL_CON_MODE_Pos  (29U)
#define BT_MAC_RCCAL_CTRL_CON_MODE_Msk  (0x1UL << BT_MAC_RCCAL_CTRL_CON_MODE_Pos)
#define BT_MAC_RCCAL_CTRL_CON_MODE      BT_MAC_RCCAL_CTRL_CON_MODE_Msk

/************** Bit definition for BT_MAC_RCCAL_RESULT register ***************/
#define BT_MAC_RCCAL_RESULT_RCCAL_RESULT_Pos  (0U)
#define BT_MAC_RCCAL_RESULT_RCCAL_RESULT_Msk  (0x7FFFFFFFUL << BT_MAC_RCCAL_RESULT_RCCAL_RESULT_Pos)
#define BT_MAC_RCCAL_RESULT_RCCAL_RESULT  BT_MAC_RCCAL_RESULT_RCCAL_RESULT_Msk
#define BT_MAC_RCCAL_RESULT_RCCAL_DONE_Pos  (31U)
#define BT_MAC_RCCAL_RESULT_RCCAL_DONE_Msk  (0x1UL << BT_MAC_RCCAL_RESULT_RCCAL_DONE_Pos)
#define BT_MAC_RCCAL_RESULT_RCCAL_DONE  BT_MAC_RCCAL_RESULT_RCCAL_DONE_Msk

/**************** Bit definition for BT_MAC_DFANCNTL register *****************/
#define BT_MAC_DFANCNTL_LETXPRIMANTID_Pos  (0U)
#define BT_MAC_DFANCNTL_LETXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANCNTL_LETXPRIMANTID_Pos)
#define BT_MAC_DFANCNTL_LETXPRIMANTID   BT_MAC_DFANCNTL_LETXPRIMANTID_Msk
#define BT_MAC_DFANCNTL_LETXPRIMIDCNTLEN_Pos  (7U)
#define BT_MAC_DFANCNTL_LETXPRIMIDCNTLEN_Msk  (0x1UL << BT_MAC_DFANCNTL_LETXPRIMIDCNTLEN_Pos)
#define BT_MAC_DFANCNTL_LETXPRIMIDCNTLEN  BT_MAC_DFANCNTL_LETXPRIMIDCNTLEN_Msk
#define BT_MAC_DFANCNTL_LERXPRIMANTID_Pos  (8U)
#define BT_MAC_DFANCNTL_LERXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANCNTL_LERXPRIMANTID_Pos)
#define BT_MAC_DFANCNTL_LERXPRIMANTID   BT_MAC_DFANCNTL_LERXPRIMANTID_Msk
#define BT_MAC_DFANCNTL_LERXPRIMIDCNTLEN_Pos  (15U)
#define BT_MAC_DFANCNTL_LERXPRIMIDCNTLEN_Msk  (0x1UL << BT_MAC_DFANCNTL_LERXPRIMIDCNTLEN_Pos)
#define BT_MAC_DFANCNTL_LERXPRIMIDCNTLEN  BT_MAC_DFANCNTL_LERXPRIMIDCNTLEN_Msk
#define BT_MAC_DFANCNTL_BTTXPRIMANTID_Pos  (16U)
#define BT_MAC_DFANCNTL_BTTXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANCNTL_BTTXPRIMANTID_Pos)
#define BT_MAC_DFANCNTL_BTTXPRIMANTID   BT_MAC_DFANCNTL_BTTXPRIMANTID_Msk
#define BT_MAC_DFANCNTL_BTTXPRIMIDCNTLEN_Pos  (23U)
#define BT_MAC_DFANCNTL_BTTXPRIMIDCNTLEN_Msk  (0x1UL << BT_MAC_DFANCNTL_BTTXPRIMIDCNTLEN_Pos)
#define BT_MAC_DFANCNTL_BTTXPRIMIDCNTLEN  BT_MAC_DFANCNTL_BTTXPRIMIDCNTLEN_Msk
#define BT_MAC_DFANCNTL_BTRXPRIMANTID_Pos  (24U)
#define BT_MAC_DFANCNTL_BTRXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANCNTL_BTRXPRIMANTID_Pos)
#define BT_MAC_DFANCNTL_BTRXPRIMANTID   BT_MAC_DFANCNTL_BTRXPRIMANTID_Msk
#define BT_MAC_DFANCNTL_BTRXPRIMIDCNTLEN_Pos  (31U)
#define BT_MAC_DFANCNTL_BTRXPRIMIDCNTLEN_Msk  (0x1UL << BT_MAC_DFANCNTL_BTRXPRIMIDCNTLEN_Pos)
#define BT_MAC_DFANCNTL_BTRXPRIMIDCNTLEN  BT_MAC_DFANCNTL_BTRXPRIMIDCNTLEN_Msk

/**************** Bit definition for BT_MAC_RWBTCNTL register *****************/
#define BT_MAC_RWBTCNTL_NWINSIZE_Pos    (0U)
#define BT_MAC_RWBTCNTL_NWINSIZE_Msk    (0x3FUL << BT_MAC_RWBTCNTL_NWINSIZE_Pos)
#define BT_MAC_RWBTCNTL_NWINSIZE        BT_MAC_RWBTCNTL_NWINSIZE_Msk
#define BT_MAC_RWBTCNTL_HPDLY_EN_Pos    (6U)
#define BT_MAC_RWBTCNTL_HPDLY_EN_Msk    (0x1UL << BT_MAC_RWBTCNTL_HPDLY_EN_Pos)
#define BT_MAC_RWBTCNTL_HPDLY_EN        BT_MAC_RWBTCNTL_HPDLY_EN_Msk
#define BT_MAC_RWBTCNTL_RWBTEN_Pos      (8U)
#define BT_MAC_RWBTCNTL_RWBTEN_Msk      (0x1UL << BT_MAC_RWBTCNTL_RWBTEN_Pos)
#define BT_MAC_RWBTCNTL_RWBTEN          BT_MAC_RWBTCNTL_RWBTEN_Msk
#define BT_MAC_RWBTCNTL_CX_DNABORT_Pos  (9U)
#define BT_MAC_RWBTCNTL_CX_DNABORT_Msk  (0x1UL << BT_MAC_RWBTCNTL_CX_DNABORT_Pos)
#define BT_MAC_RWBTCNTL_CX_DNABORT      BT_MAC_RWBTCNTL_CX_DNABORT_Msk
#define BT_MAC_RWBTCNTL_CX_RXBSYENA_Pos  (10U)
#define BT_MAC_RWBTCNTL_CX_RXBSYENA_Msk  (0x1UL << BT_MAC_RWBTCNTL_CX_RXBSYENA_Pos)
#define BT_MAC_RWBTCNTL_CX_RXBSYENA     BT_MAC_RWBTCNTL_CX_RXBSYENA_Msk
#define BT_MAC_RWBTCNTL_CX_TXBSYENA_Pos  (11U)
#define BT_MAC_RWBTCNTL_CX_TXBSYENA_Msk  (0x1UL << BT_MAC_RWBTCNTL_CX_TXBSYENA_Pos)
#define BT_MAC_RWBTCNTL_CX_TXBSYENA     BT_MAC_RWBTCNTL_CX_TXBSYENA_Msk
#define BT_MAC_RWBTCNTL_SEQNDSB_Pos     (12U)
#define BT_MAC_RWBTCNTL_SEQNDSB_Msk     (0x1UL << BT_MAC_RWBTCNTL_SEQNDSB_Pos)
#define BT_MAC_RWBTCNTL_SEQNDSB         BT_MAC_RWBTCNTL_SEQNDSB_Msk
#define BT_MAC_RWBTCNTL_ARQNDSB_Pos     (13U)
#define BT_MAC_RWBTCNTL_ARQNDSB_Msk     (0x1UL << BT_MAC_RWBTCNTL_ARQNDSB_Pos)
#define BT_MAC_RWBTCNTL_ARQNDSB         BT_MAC_RWBTCNTL_ARQNDSB_Msk
#define BT_MAC_RWBTCNTL_FLOWDSB_Pos     (14U)
#define BT_MAC_RWBTCNTL_FLOWDSB_Msk     (0x1UL << BT_MAC_RWBTCNTL_FLOWDSB_Pos)
#define BT_MAC_RWBTCNTL_FLOWDSB         BT_MAC_RWBTCNTL_FLOWDSB_Msk
#define BT_MAC_RWBTCNTL_HOPDSB_Pos      (15U)
#define BT_MAC_RWBTCNTL_HOPDSB_Msk      (0x1UL << BT_MAC_RWBTCNTL_HOPDSB_Pos)
#define BT_MAC_RWBTCNTL_HOPDSB          BT_MAC_RWBTCNTL_HOPDSB_Msk
#define BT_MAC_RWBTCNTL_WHITDSB_Pos     (16U)
#define BT_MAC_RWBTCNTL_WHITDSB_Msk     (0x1UL << BT_MAC_RWBTCNTL_WHITDSB_Pos)
#define BT_MAC_RWBTCNTL_WHITDSB         BT_MAC_RWBTCNTL_WHITDSB_Msk
#define BT_MAC_RWBTCNTL_CRCDSB_Pos      (17U)
#define BT_MAC_RWBTCNTL_CRCDSB_Msk      (0x1UL << BT_MAC_RWBTCNTL_CRCDSB_Pos)
#define BT_MAC_RWBTCNTL_CRCDSB          BT_MAC_RWBTCNTL_CRCDSB_Msk
#define BT_MAC_RWBTCNTL_CRYPTDSB_Pos    (18U)
#define BT_MAC_RWBTCNTL_CRYPTDSB_Msk    (0x1UL << BT_MAC_RWBTCNTL_CRYPTDSB_Pos)
#define BT_MAC_RWBTCNTL_CRYPTDSB        BT_MAC_RWBTCNTL_CRYPTDSB_Msk
#define BT_MAC_RWBTCNTL_LMPFLOWDSB_Pos  (19U)
#define BT_MAC_RWBTCNTL_LMPFLOWDSB_Msk  (0x1UL << BT_MAC_RWBTCNTL_LMPFLOWDSB_Pos)
#define BT_MAC_RWBTCNTL_LMPFLOWDSB      BT_MAC_RWBTCNTL_LMPFLOWDSB_Msk
#define BT_MAC_RWBTCNTL_SNIFF_ABORT_Pos  (20U)
#define BT_MAC_RWBTCNTL_SNIFF_ABORT_Msk  (0x1UL << BT_MAC_RWBTCNTL_SNIFF_ABORT_Pos)
#define BT_MAC_RWBTCNTL_SNIFF_ABORT     BT_MAC_RWBTCNTL_SNIFF_ABORT_Msk
#define BT_MAC_RWBTCNTL_PAGEING_ABORT_Pos  (21U)
#define BT_MAC_RWBTCNTL_PAGEING_ABORT_Msk  (0x1UL << BT_MAC_RWBTCNTL_PAGEING_ABORT_Pos)
#define BT_MAC_RWBTCNTL_PAGEING_ABORT   BT_MAC_RWBTCNTL_PAGEING_ABORT_Msk
#define BT_MAC_RWBTCNTL_RFTEST_ABORT_Pos  (22U)
#define BT_MAC_RWBTCNTL_RFTEST_ABORT_Msk  (0x1UL << BT_MAC_RWBTCNTL_RFTEST_ABORT_Pos)
#define BT_MAC_RWBTCNTL_RFTEST_ABORT    BT_MAC_RWBTCNTL_RFTEST_ABORT_Msk
#define BT_MAC_RWBTCNTL_SCAN_ABORT_Pos  (23U)
#define BT_MAC_RWBTCNTL_SCAN_ABORT_Msk  (0x1UL << BT_MAC_RWBTCNTL_SCAN_ABORT_Pos)
#define BT_MAC_RWBTCNTL_SCAN_ABORT      BT_MAC_RWBTCNTL_SCAN_ABORT_Msk
#define BT_MAC_RWBTCNTL_MASTER_SOFT_RST_Pos  (31U)
#define BT_MAC_RWBTCNTL_MASTER_SOFT_RST_Msk  (0x1UL << BT_MAC_RWBTCNTL_MASTER_SOFT_RST_Pos)
#define BT_MAC_RWBTCNTL_MASTER_SOFT_RST  BT_MAC_RWBTCNTL_MASTER_SOFT_RST_Msk

/**************** Bit definition for BT_MAC_BTVERSION register ****************/
#define BT_MAC_BTVERSION_BUILD_NUM_Pos  (0U)
#define BT_MAC_BTVERSION_BUILD_NUM_Msk  (0xFFUL << BT_MAC_BTVERSION_BUILD_NUM_Pos)
#define BT_MAC_BTVERSION_BUILD_NUM      BT_MAC_BTVERSION_BUILD_NUM_Msk
#define BT_MAC_BTVERSION_UPG_Pos        (8U)
#define BT_MAC_BTVERSION_UPG_Msk        (0xFFUL << BT_MAC_BTVERSION_UPG_Pos)
#define BT_MAC_BTVERSION_UPG            BT_MAC_BTVERSION_UPG_Msk
#define BT_MAC_BTVERSION_REL_Pos        (16U)
#define BT_MAC_BTVERSION_REL_Msk        (0xFFUL << BT_MAC_BTVERSION_REL_Pos)
#define BT_MAC_BTVERSION_REL            BT_MAC_BTVERSION_REL_Msk
#define BT_MAC_BTVERSION_TYP_Pos        (24U)
#define BT_MAC_BTVERSION_TYP_Msk        (0xFFUL << BT_MAC_BTVERSION_TYP_Pos)
#define BT_MAC_BTVERSION_TYP            BT_MAC_BTVERSION_TYP_Msk

/*************** Bit definition for BT_MAC_BTINTCNTL0 register ****************/
#define BT_MAC_BTINTCNTL0_STARTFRMINTMSK_Pos  (0U)
#define BT_MAC_BTINTCNTL0_STARTFRMINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_STARTFRMINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_STARTFRMINTMSK  BT_MAC_BTINTCNTL0_STARTFRMINTMSK_Msk
#define BT_MAC_BTINTCNTL0_ENDFRMINTMSK_Pos  (1U)
#define BT_MAC_BTINTCNTL0_ENDFRMINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_ENDFRMINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_ENDFRMINTMSK  BT_MAC_BTINTCNTL0_ENDFRMINTMSK_Msk
#define BT_MAC_BTINTCNTL0_SKIPFRMINTMSK_Pos  (2U)
#define BT_MAC_BTINTCNTL0_SKIPFRMINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_SKIPFRMINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_SKIPFRMINTMSK  BT_MAC_BTINTCNTL0_SKIPFRMINTMSK_Msk
#define BT_MAC_BTINTCNTL0_RXINTMSK_Pos  (4U)
#define BT_MAC_BTINTCNTL0_RXINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_RXINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_RXINTMSK      BT_MAC_BTINTCNTL0_RXINTMSK_Msk
#define BT_MAC_BTINTCNTL0_FRSYNCINTMSK_Pos  (8U)
#define BT_MAC_BTINTCNTL0_FRSYNCINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_FRSYNCINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_FRSYNCINTMSK  BT_MAC_BTINTCNTL0_FRSYNCINTMSK_Msk
#define BT_MAC_BTINTCNTL0_MTOFFINT0MSK_Pos  (9U)
#define BT_MAC_BTINTCNTL0_MTOFFINT0MSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_MTOFFINT0MSK_Pos)
#define BT_MAC_BTINTCNTL0_MTOFFINT0MSK  BT_MAC_BTINTCNTL0_MTOFFINT0MSK_Msk
#define BT_MAC_BTINTCNTL0_MTOFFINT1MSK_Pos  (10U)
#define BT_MAC_BTINTCNTL0_MTOFFINT1MSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_MTOFFINT1MSK_Pos)
#define BT_MAC_BTINTCNTL0_MTOFFINT1MSK  BT_MAC_BTINTCNTL0_MTOFFINT1MSK_Msk
#define BT_MAC_BTINTCNTL0_MWSWCITXINTMSK_Pos  (11U)
#define BT_MAC_BTINTCNTL0_MWSWCITXINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_MWSWCITXINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_MWSWCITXINTMSK  BT_MAC_BTINTCNTL0_MWSWCITXINTMSK_Msk
#define BT_MAC_BTINTCNTL0_MWSWCIRXINTMSK_Pos  (12U)
#define BT_MAC_BTINTCNTL0_MWSWCIRXINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_MWSWCIRXINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_MWSWCIRXINTMSK  BT_MAC_BTINTCNTL0_MWSWCIRXINTMSK_Msk
#define BT_MAC_BTINTCNTL0_AUDIO0INTMSK_Pos  (13U)
#define BT_MAC_BTINTCNTL0_AUDIO0INTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_AUDIO0INTMSK_Pos)
#define BT_MAC_BTINTCNTL0_AUDIO0INTMSK  BT_MAC_BTINTCNTL0_AUDIO0INTMSK_Msk
#define BT_MAC_BTINTCNTL0_AUDIO1INTMSK_Pos  (14U)
#define BT_MAC_BTINTCNTL0_AUDIO1INTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_AUDIO1INTMSK_Pos)
#define BT_MAC_BTINTCNTL0_AUDIO1INTMSK  BT_MAC_BTINTCNTL0_AUDIO1INTMSK_Msk
#define BT_MAC_BTINTCNTL0_AUDIO2INTMSK_Pos  (15U)
#define BT_MAC_BTINTCNTL0_AUDIO2INTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_AUDIO2INTMSK_Pos)
#define BT_MAC_BTINTCNTL0_AUDIO2INTMSK  BT_MAC_BTINTCNTL0_AUDIO2INTMSK_Msk
#define BT_MAC_BTINTCNTL0_ERRORINTMSK_Pos  (16U)
#define BT_MAC_BTINTCNTL0_ERRORINTMSK_Msk  (0x1UL << BT_MAC_BTINTCNTL0_ERRORINTMSK_Pos)
#define BT_MAC_BTINTCNTL0_ERRORINTMSK   BT_MAC_BTINTCNTL0_ERRORINTMSK_Msk

/*************** Bit definition for BT_MAC_BTINTSTAT0 register ****************/
#define BT_MAC_BTINTSTAT0_FRSYNCINTSTAT_Pos  (8U)
#define BT_MAC_BTINTSTAT0_FRSYNCINTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_FRSYNCINTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_FRSYNCINTSTAT  BT_MAC_BTINTSTAT0_FRSYNCINTSTAT_Msk
#define BT_MAC_BTINTSTAT0_MTOFFINT0STAT_Pos  (9U)
#define BT_MAC_BTINTSTAT0_MTOFFINT0STAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_MTOFFINT0STAT_Pos)
#define BT_MAC_BTINTSTAT0_MTOFFINT0STAT  BT_MAC_BTINTSTAT0_MTOFFINT0STAT_Msk
#define BT_MAC_BTINTSTAT0_MTOFFINT1STAT_Pos  (10U)
#define BT_MAC_BTINTSTAT0_MTOFFINT1STAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_MTOFFINT1STAT_Pos)
#define BT_MAC_BTINTSTAT0_MTOFFINT1STAT  BT_MAC_BTINTSTAT0_MTOFFINT1STAT_Msk
#define BT_MAC_BTINTSTAT0_MWSWCITXINTSTAT_Pos  (11U)
#define BT_MAC_BTINTSTAT0_MWSWCITXINTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_MWSWCITXINTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_MWSWCITXINTSTAT  BT_MAC_BTINTSTAT0_MWSWCITXINTSTAT_Msk
#define BT_MAC_BTINTSTAT0_MWSWCIRXINTSTAT_Pos  (12U)
#define BT_MAC_BTINTSTAT0_MWSWCIRXINTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_MWSWCIRXINTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_MWSWCIRXINTSTAT  BT_MAC_BTINTSTAT0_MWSWCIRXINTSTAT_Msk
#define BT_MAC_BTINTSTAT0_AUDIO0INTSTAT_Pos  (13U)
#define BT_MAC_BTINTSTAT0_AUDIO0INTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_AUDIO0INTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_AUDIO0INTSTAT  BT_MAC_BTINTSTAT0_AUDIO0INTSTAT_Msk
#define BT_MAC_BTINTSTAT0_AUDIO1INTSTAT_Pos  (14U)
#define BT_MAC_BTINTSTAT0_AUDIO1INTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_AUDIO1INTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_AUDIO1INTSTAT  BT_MAC_BTINTSTAT0_AUDIO1INTSTAT_Msk
#define BT_MAC_BTINTSTAT0_AUDIO2INTSTAT_Pos  (15U)
#define BT_MAC_BTINTSTAT0_AUDIO2INTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_AUDIO2INTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_AUDIO2INTSTAT  BT_MAC_BTINTSTAT0_AUDIO2INTSTAT_Msk
#define BT_MAC_BTINTSTAT0_ERRORINTSTAT_Pos  (16U)
#define BT_MAC_BTINTSTAT0_ERRORINTSTAT_Msk  (0x1UL << BT_MAC_BTINTSTAT0_ERRORINTSTAT_Pos)
#define BT_MAC_BTINTSTAT0_ERRORINTSTAT  BT_MAC_BTINTSTAT0_ERRORINTSTAT_Msk

/**************** Bit definition for BT_MAC_BTINTACK0 register ****************/
#define BT_MAC_BTINTACK0_FRSYNCINTACK_Pos  (8U)
#define BT_MAC_BTINTACK0_FRSYNCINTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_FRSYNCINTACK_Pos)
#define BT_MAC_BTINTACK0_FRSYNCINTACK   BT_MAC_BTINTACK0_FRSYNCINTACK_Msk
#define BT_MAC_BTINTACK0_MTOFFINT0ACK_Pos  (9U)
#define BT_MAC_BTINTACK0_MTOFFINT0ACK_Msk  (0x1UL << BT_MAC_BTINTACK0_MTOFFINT0ACK_Pos)
#define BT_MAC_BTINTACK0_MTOFFINT0ACK   BT_MAC_BTINTACK0_MTOFFINT0ACK_Msk
#define BT_MAC_BTINTACK0_MTOFFINT1ACK_Pos  (10U)
#define BT_MAC_BTINTACK0_MTOFFINT1ACK_Msk  (0x1UL << BT_MAC_BTINTACK0_MTOFFINT1ACK_Pos)
#define BT_MAC_BTINTACK0_MTOFFINT1ACK   BT_MAC_BTINTACK0_MTOFFINT1ACK_Msk
#define BT_MAC_BTINTACK0_MWSWCITXINTACK_Pos  (11U)
#define BT_MAC_BTINTACK0_MWSWCITXINTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_MWSWCITXINTACK_Pos)
#define BT_MAC_BTINTACK0_MWSWCITXINTACK  BT_MAC_BTINTACK0_MWSWCITXINTACK_Msk
#define BT_MAC_BTINTACK0_MWSWCIRXINTACK_Pos  (12U)
#define BT_MAC_BTINTACK0_MWSWCIRXINTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_MWSWCIRXINTACK_Pos)
#define BT_MAC_BTINTACK0_MWSWCIRXINTACK  BT_MAC_BTINTACK0_MWSWCIRXINTACK_Msk
#define BT_MAC_BTINTACK0_AUDIO0INTACK_Pos  (13U)
#define BT_MAC_BTINTACK0_AUDIO0INTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_AUDIO0INTACK_Pos)
#define BT_MAC_BTINTACK0_AUDIO0INTACK   BT_MAC_BTINTACK0_AUDIO0INTACK_Msk
#define BT_MAC_BTINTACK0_AUDIO1INTACK_Pos  (14U)
#define BT_MAC_BTINTACK0_AUDIO1INTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_AUDIO1INTACK_Pos)
#define BT_MAC_BTINTACK0_AUDIO1INTACK   BT_MAC_BTINTACK0_AUDIO1INTACK_Msk
#define BT_MAC_BTINTACK0_AUDIO2INTACK_Pos  (15U)
#define BT_MAC_BTINTACK0_AUDIO2INTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_AUDIO2INTACK_Pos)
#define BT_MAC_BTINTACK0_AUDIO2INTACK   BT_MAC_BTINTACK0_AUDIO2INTACK_Msk
#define BT_MAC_BTINTACK0_ERRORINTACK_Pos  (16U)
#define BT_MAC_BTINTACK0_ERRORINTACK_Msk  (0x1UL << BT_MAC_BTINTACK0_ERRORINTACK_Pos)
#define BT_MAC_BTINTACK0_ERRORINTACK    BT_MAC_BTINTACK0_ERRORINTACK_Msk

/*********** Bit definition for BT_MAC_BTCURRENTRXDESCPTR register ************/
#define BT_MAC_BTCURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos  (0U)
#define BT_MAC_BTCURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk  (0x3FFFUL << BT_MAC_BTCURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos)
#define BT_MAC_BTCURRENTRXDESCPTR_CURRENTRXDESCPTR  BT_MAC_BTCURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk

/*************** Bit definition for BT_MAC_BTDIAGCNTL register ****************/
#define BT_MAC_BTDIAGCNTL_DIAG0_Pos     (0U)
#define BT_MAC_BTDIAGCNTL_DIAG0_Msk     (0x7FUL << BT_MAC_BTDIAGCNTL_DIAG0_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG0         BT_MAC_BTDIAGCNTL_DIAG0_Msk
#define BT_MAC_BTDIAGCNTL_DIAG0_EN_Pos  (7U)
#define BT_MAC_BTDIAGCNTL_DIAG0_EN_Msk  (0x1UL << BT_MAC_BTDIAGCNTL_DIAG0_EN_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG0_EN      BT_MAC_BTDIAGCNTL_DIAG0_EN_Msk
#define BT_MAC_BTDIAGCNTL_DIAG1_Pos     (8U)
#define BT_MAC_BTDIAGCNTL_DIAG1_Msk     (0x7FUL << BT_MAC_BTDIAGCNTL_DIAG1_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG1         BT_MAC_BTDIAGCNTL_DIAG1_Msk
#define BT_MAC_BTDIAGCNTL_DIAG1_EN_Pos  (15U)
#define BT_MAC_BTDIAGCNTL_DIAG1_EN_Msk  (0x1UL << BT_MAC_BTDIAGCNTL_DIAG1_EN_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG1_EN      BT_MAC_BTDIAGCNTL_DIAG1_EN_Msk
#define BT_MAC_BTDIAGCNTL_DIAG2_Pos     (16U)
#define BT_MAC_BTDIAGCNTL_DIAG2_Msk     (0x7FUL << BT_MAC_BTDIAGCNTL_DIAG2_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG2         BT_MAC_BTDIAGCNTL_DIAG2_Msk
#define BT_MAC_BTDIAGCNTL_DIAG2_EN_Pos  (23U)
#define BT_MAC_BTDIAGCNTL_DIAG2_EN_Msk  (0x1UL << BT_MAC_BTDIAGCNTL_DIAG2_EN_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG2_EN      BT_MAC_BTDIAGCNTL_DIAG2_EN_Msk
#define BT_MAC_BTDIAGCNTL_DIAG3_Pos     (24U)
#define BT_MAC_BTDIAGCNTL_DIAG3_Msk     (0x7FUL << BT_MAC_BTDIAGCNTL_DIAG3_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG3         BT_MAC_BTDIAGCNTL_DIAG3_Msk
#define BT_MAC_BTDIAGCNTL_DIAG3_EN_Pos  (31U)
#define BT_MAC_BTDIAGCNTL_DIAG3_EN_Msk  (0x1UL << BT_MAC_BTDIAGCNTL_DIAG3_EN_Pos)
#define BT_MAC_BTDIAGCNTL_DIAG3_EN      BT_MAC_BTDIAGCNTL_DIAG3_EN_Msk

/*************** Bit definition for BT_MAC_BTDIAGSTAT register ****************/
#define BT_MAC_BTDIAGSTAT_DIAG0STAT_Pos  (0U)
#define BT_MAC_BTDIAGSTAT_DIAG0STAT_Msk  (0xFFUL << BT_MAC_BTDIAGSTAT_DIAG0STAT_Pos)
#define BT_MAC_BTDIAGSTAT_DIAG0STAT     BT_MAC_BTDIAGSTAT_DIAG0STAT_Msk
#define BT_MAC_BTDIAGSTAT_DIAG1STAT_Pos  (8U)
#define BT_MAC_BTDIAGSTAT_DIAG1STAT_Msk  (0xFFUL << BT_MAC_BTDIAGSTAT_DIAG1STAT_Pos)
#define BT_MAC_BTDIAGSTAT_DIAG1STAT     BT_MAC_BTDIAGSTAT_DIAG1STAT_Msk
#define BT_MAC_BTDIAGSTAT_DIAG2STAT_Pos  (16U)
#define BT_MAC_BTDIAGSTAT_DIAG2STAT_Msk  (0xFFUL << BT_MAC_BTDIAGSTAT_DIAG2STAT_Pos)
#define BT_MAC_BTDIAGSTAT_DIAG2STAT     BT_MAC_BTDIAGSTAT_DIAG2STAT_Msk
#define BT_MAC_BTDIAGSTAT_DIAG3STAT_Pos  (24U)
#define BT_MAC_BTDIAGSTAT_DIAG3STAT_Msk  (0xFFUL << BT_MAC_BTDIAGSTAT_DIAG3STAT_Pos)
#define BT_MAC_BTDIAGSTAT_DIAG3STAT     BT_MAC_BTDIAGSTAT_DIAG3STAT_Msk

/************** Bit definition for BT_MAC_BTDEBUGADDMAX register **************/
#define BT_MAC_BTDEBUGADDMAX_EM_ADDMAX_Pos  (0U)
#define BT_MAC_BTDEBUGADDMAX_EM_ADDMAX_Msk  (0xFFFFUL << BT_MAC_BTDEBUGADDMAX_EM_ADDMAX_Pos)
#define BT_MAC_BTDEBUGADDMAX_EM_ADDMAX  BT_MAC_BTDEBUGADDMAX_EM_ADDMAX_Msk
#define BT_MAC_BTDEBUGADDMAX_REG_ADDMAX_Pos  (16U)
#define BT_MAC_BTDEBUGADDMAX_REG_ADDMAX_Msk  (0xFFFFUL << BT_MAC_BTDEBUGADDMAX_REG_ADDMAX_Pos)
#define BT_MAC_BTDEBUGADDMAX_REG_ADDMAX  BT_MAC_BTDEBUGADDMAX_REG_ADDMAX_Msk

/************** Bit definition for BT_MAC_BTDEBUGADDMIN register **************/
#define BT_MAC_BTDEBUGADDMIN_EM_ADDMIN_Pos  (0U)
#define BT_MAC_BTDEBUGADDMIN_EM_ADDMIN_Msk  (0xFFFFUL << BT_MAC_BTDEBUGADDMIN_EM_ADDMIN_Pos)
#define BT_MAC_BTDEBUGADDMIN_EM_ADDMIN  BT_MAC_BTDEBUGADDMIN_EM_ADDMIN_Msk
#define BT_MAC_BTDEBUGADDMIN_REG_ADDMIN_Pos  (16U)
#define BT_MAC_BTDEBUGADDMIN_REG_ADDMIN_Msk  (0xFFFFUL << BT_MAC_BTDEBUGADDMIN_REG_ADDMIN_Pos)
#define BT_MAC_BTDEBUGADDMIN_REG_ADDMIN  BT_MAC_BTDEBUGADDMIN_REG_ADDMIN_Msk

/************* Bit definition for BT_MAC_BTERRORTYPESTAT register *************/
#define BT_MAC_BTERRORTYPESTAT_TXCRYPT_ERROR_Pos  (0U)
#define BT_MAC_BTERRORTYPESTAT_TXCRYPT_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_TXCRYPT_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_TXCRYPT_ERROR  BT_MAC_BTERRORTYPESTAT_TXCRYPT_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_RXCRYPT_ERROR_Pos  (1U)
#define BT_MAC_BTERRORTYPESTAT_RXCRYPT_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_RXCRYPT_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_RXCRYPT_ERROR  BT_MAC_BTERRORTYPESTAT_RXCRYPT_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_CRYPTMODE_ERROR_Pos  (2U)
#define BT_MAC_BTERRORTYPESTAT_CRYPTMODE_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_CRYPTMODE_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_CRYPTMODE_ERROR  BT_MAC_BTERRORTYPESTAT_CRYPTMODE_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos  (3U)
#define BT_MAC_BTERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_PKTCNTL_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_RADIO_EMACC_ERROR_Pos  (4U)
#define BT_MAC_BTERRORTYPESTAT_RADIO_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_RADIO_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_RADIO_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_RADIO_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_AUDIO_EMACC_ERROR_Pos  (5U)
#define BT_MAC_BTERRORTYPESTAT_AUDIO_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_AUDIO_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_AUDIO_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_AUDIO_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_PCM_EMACC_ERROR_Pos  (6U)
#define BT_MAC_BTERRORTYPESTAT_PCM_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_PCM_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_PCM_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_PCM_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_MWSCOEX_EMACC_ERROR_Pos  (7U)
#define BT_MAC_BTERRORTYPESTAT_MWSCOEX_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_MWSCOEX_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_MWSCOEX_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_MWSCOEX_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos  (8U)
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR  BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos  (9U)
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_APFM_ERROR  BT_MAC_BTERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_APFM_ERROR_Pos  (10U)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_APFM_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_FRM_CNTL_APFM_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_APFM_ERROR  BT_MAC_BTERRORTYPESTAT_FRM_CNTL_APFM_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_EMACC_ERROR_Pos  (11U)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_FRM_CNTL_EMACC_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_EMACC_ERROR  BT_MAC_BTERRORTYPESTAT_FRM_CNTL_EMACC_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_TIMER_ERROR_Pos  (12U)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_TIMER_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_FRM_CNTL_TIMER_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_FRM_CNTL_TIMER_ERROR  BT_MAC_BTERRORTYPESTAT_FRM_CNTL_TIMER_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_HOPUNDERRUN_ERROR_Pos  (13U)
#define BT_MAC_BTERRORTYPESTAT_HOPUNDERRUN_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_HOPUNDERRUN_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_HOPUNDERRUN_ERROR  BT_MAC_BTERRORTYPESTAT_HOPUNDERRUN_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_CHMAP_ERROR_Pos  (14U)
#define BT_MAC_BTERRORTYPESTAT_CHMAP_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_CHMAP_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_CHMAP_ERROR  BT_MAC_BTERRORTYPESTAT_CHMAP_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_CSFORMAT_ERROR_Pos  (15U)
#define BT_MAC_BTERRORTYPESTAT_CSFORMAT_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_CSFORMAT_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_CSFORMAT_ERROR  BT_MAC_BTERRORTYPESTAT_CSFORMAT_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_CSATTNB_ERROR_Pos  (16U)
#define BT_MAC_BTERRORTYPESTAT_CSATTNB_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_CSATTNB_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_CSATTNB_ERROR  BT_MAC_BTERRORTYPESTAT_CSATTNB_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos  (17U)
#define BT_MAC_BTERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_TXDESC_EMPTY_ERROR  BT_MAC_BTERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos  (18U)
#define BT_MAC_BTERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_RXDESC_EMPTY_ERROR  BT_MAC_BTERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_TXBUF_PTR_ERROR_Pos  (19U)
#define BT_MAC_BTERRORTYPESTAT_TXBUF_PTR_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_TXBUF_PTR_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_TXBUF_PTR_ERROR  BT_MAC_BTERRORTYPESTAT_TXBUF_PTR_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_RXBUF_PTR_ERROR_Pos  (20U)
#define BT_MAC_BTERRORTYPESTAT_RXBUF_PTR_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_RXBUF_PTR_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_RXBUF_PTR_ERROR  BT_MAC_BTERRORTYPESTAT_RXBUF_PTR_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_PEER_SAM_ERROR_Pos  (21U)
#define BT_MAC_BTERRORTYPESTAT_PEER_SAM_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_PEER_SAM_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_PEER_SAM_ERROR  BT_MAC_BTERRORTYPESTAT_PEER_SAM_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_LOCAL_SAM_ERROR_Pos  (22U)
#define BT_MAC_BTERRORTYPESTAT_LOCAL_SAM_ERROR_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_LOCAL_SAM_ERROR_Pos)
#define BT_MAC_BTERRORTYPESTAT_LOCAL_SAM_ERROR  BT_MAC_BTERRORTYPESTAT_LOCAL_SAM_ERROR_Msk
#define BT_MAC_BTERRORTYPESTAT_FIFOINTOVF_Pos  (23U)
#define BT_MAC_BTERRORTYPESTAT_FIFOINTOVF_Msk  (0x1UL << BT_MAC_BTERRORTYPESTAT_FIFOINTOVF_Pos)
#define BT_MAC_BTERRORTYPESTAT_FIFOINTOVF  BT_MAC_BTERRORTYPESTAT_FIFOINTOVF_Msk

/************** Bit definition for BT_MAC_BTSWPROFILING register **************/
#define BT_MAC_BTSWPROFILING_SWPROF_Pos  (0U)
#define BT_MAC_BTSWPROFILING_SWPROF_Msk  (0xFFFFFFFFUL << BT_MAC_BTSWPROFILING_SWPROF_Pos)
#define BT_MAC_BTSWPROFILING_SWPROF     BT_MAC_BTSWPROFILING_SWPROF_Msk

/************** Bit definition for BT_MAC_BTRADIOCNTL2 register ***************/
#define BT_MAC_BTRADIOCNTL2_FREQTABLE_PTR_Pos  (0U)
#define BT_MAC_BTRADIOCNTL2_FREQTABLE_PTR_Msk  (0x3FFFUL << BT_MAC_BTRADIOCNTL2_FREQTABLE_PTR_Pos)
#define BT_MAC_BTRADIOCNTL2_FREQTABLE_PTR  BT_MAC_BTRADIOCNTL2_FREQTABLE_PTR_Msk
#define BT_MAC_BTRADIOCNTL2_TRAILER_GATING_VAL_Pos  (24U)
#define BT_MAC_BTRADIOCNTL2_TRAILER_GATING_VAL_Msk  (0x7UL << BT_MAC_BTRADIOCNTL2_TRAILER_GATING_VAL_Pos)
#define BT_MAC_BTRADIOCNTL2_TRAILER_GATING_VAL  BT_MAC_BTRADIOCNTL2_TRAILER_GATING_VAL_Msk

/************** Bit definition for BT_MAC_BTRADIOCNTL3 register ***************/
#define BT_MAC_BTRADIOCNTL3_TXRATE0CFG_Pos  (8U)
#define BT_MAC_BTRADIOCNTL3_TXRATE0CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_TXRATE0CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_TXRATE0CFG  BT_MAC_BTRADIOCNTL3_TXRATE0CFG_Msk
#define BT_MAC_BTRADIOCNTL3_TXRATE1CFG_Pos  (10U)
#define BT_MAC_BTRADIOCNTL3_TXRATE1CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_TXRATE1CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_TXRATE1CFG  BT_MAC_BTRADIOCNTL3_TXRATE1CFG_Msk
#define BT_MAC_BTRADIOCNTL3_TXRATE2CFG_Pos  (12U)
#define BT_MAC_BTRADIOCNTL3_TXRATE2CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_TXRATE2CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_TXRATE2CFG  BT_MAC_BTRADIOCNTL3_TXRATE2CFG_Msk
#define BT_MAC_BTRADIOCNTL3_RXRATE0CFG_Pos  (24U)
#define BT_MAC_BTRADIOCNTL3_RXRATE0CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_RXRATE0CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_RXRATE0CFG  BT_MAC_BTRADIOCNTL3_RXRATE0CFG_Msk
#define BT_MAC_BTRADIOCNTL3_RXRATE1CFG_Pos  (26U)
#define BT_MAC_BTRADIOCNTL3_RXRATE1CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_RXRATE1CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_RXRATE1CFG  BT_MAC_BTRADIOCNTL3_RXRATE1CFG_Msk
#define BT_MAC_BTRADIOCNTL3_RXRATE2CFG_Pos  (28U)
#define BT_MAC_BTRADIOCNTL3_RXRATE2CFG_Msk  (0x3UL << BT_MAC_BTRADIOCNTL3_RXRATE2CFG_Pos)
#define BT_MAC_BTRADIOCNTL3_RXRATE2CFG  BT_MAC_BTRADIOCNTL3_RXRATE2CFG_Msk

/************* Bit definition for BT_MAC_BTRADIOPWRUPDN register **************/
#define BT_MAC_BTRADIOPWRUPDN_TXPWRUPCT_Pos  (0U)
#define BT_MAC_BTRADIOPWRUPDN_TXPWRUPCT_Msk  (0xFFUL << BT_MAC_BTRADIOPWRUPDN_TXPWRUPCT_Pos)
#define BT_MAC_BTRADIOPWRUPDN_TXPWRUPCT  BT_MAC_BTRADIOPWRUPDN_TXPWRUPCT_Msk
#define BT_MAC_BTRADIOPWRUPDN_TXPWRDNCT_Pos  (8U)
#define BT_MAC_BTRADIOPWRUPDN_TXPWRDNCT_Msk  (0x7FUL << BT_MAC_BTRADIOPWRUPDN_TXPWRDNCT_Pos)
#define BT_MAC_BTRADIOPWRUPDN_TXPWRDNCT  BT_MAC_BTRADIOPWRUPDN_TXPWRDNCT_Msk
#define BT_MAC_BTRADIOPWRUPDN_RXPWRUPCT_Pos  (16U)
#define BT_MAC_BTRADIOPWRUPDN_RXPWRUPCT_Msk  (0xFFUL << BT_MAC_BTRADIOPWRUPDN_RXPWRUPCT_Pos)
#define BT_MAC_BTRADIOPWRUPDN_RXPWRUPCT  BT_MAC_BTRADIOPWRUPDN_RXPWRUPCT_Msk

/************* Bit definition for BT_MAC_BTRADIOTXRXTIM register **************/
#define BT_MAC_BTRADIOTXRXTIM_TXPATHDLY_Pos  (0U)
#define BT_MAC_BTRADIOTXRXTIM_TXPATHDLY_Msk  (0x7FUL << BT_MAC_BTRADIOTXRXTIM_TXPATHDLY_Pos)
#define BT_MAC_BTRADIOTXRXTIM_TXPATHDLY  BT_MAC_BTRADIOTXRXTIM_TXPATHDLY_Msk
#define BT_MAC_BTRADIOTXRXTIM_RXPATHDLY_Pos  (8U)
#define BT_MAC_BTRADIOTXRXTIM_RXPATHDLY_Msk  (0x7FUL << BT_MAC_BTRADIOTXRXTIM_RXPATHDLY_Pos)
#define BT_MAC_BTRADIOTXRXTIM_RXPATHDLY  BT_MAC_BTRADIOTXRXTIM_RXPATHDLY_Msk
#define BT_MAC_BTRADIOTXRXTIM_SYNC_POSITION_Pos  (24U)
#define BT_MAC_BTRADIOTXRXTIM_SYNC_POSITION_Msk  (0xFFUL << BT_MAC_BTRADIOTXRXTIM_SYNC_POSITION_Pos)
#define BT_MAC_BTRADIOTXRXTIM_SYNC_POSITION  BT_MAC_BTRADIOTXRXTIM_SYNC_POSITION_Msk

/************** Bit definition for BT_MAC_BTRFTESTCNTL register ***************/
#define BT_MAC_BTRFTESTCNTL_TXPKTCNTEN_Pos  (11U)
#define BT_MAC_BTRFTESTCNTL_TXPKTCNTEN_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_TXPKTCNTEN_Pos)
#define BT_MAC_BTRFTESTCNTL_TXPKTCNTEN  BT_MAC_BTRFTESTCNTL_TXPKTCNTEN_Msk
#define BT_MAC_BTRFTESTCNTL_TXPLDSRC_Pos  (12U)
#define BT_MAC_BTRFTESTCNTL_TXPLDSRC_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_TXPLDSRC_Pos)
#define BT_MAC_BTRFTESTCNTL_TXPLDSRC    BT_MAC_BTRFTESTCNTL_TXPLDSRC_Msk
#define BT_MAC_BTRFTESTCNTL_PRBSTYPE_Pos  (13U)
#define BT_MAC_BTRFTESTCNTL_PRBSTYPE_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_PRBSTYPE_Pos)
#define BT_MAC_BTRFTESTCNTL_PRBSTYPE    BT_MAC_BTRFTESTCNTL_PRBSTYPE_Msk
#define BT_MAC_BTRFTESTCNTL_INFINITETX_Pos  (15U)
#define BT_MAC_BTRFTESTCNTL_INFINITETX_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_INFINITETX_Pos)
#define BT_MAC_BTRFTESTCNTL_INFINITETX  BT_MAC_BTRFTESTCNTL_INFINITETX_Msk
#define BT_MAC_BTRFTESTCNTL_HERRREN_Pos  (16U)
#define BT_MAC_BTRFTESTCNTL_HERRREN_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_HERRREN_Pos)
#define BT_MAC_BTRFTESTCNTL_HERRREN     BT_MAC_BTRFTESTCNTL_HERRREN_Msk
#define BT_MAC_BTRFTESTCNTL_SSERRREN_Pos  (17U)
#define BT_MAC_BTRFTESTCNTL_SSERRREN_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_SSERRREN_Pos)
#define BT_MAC_BTRFTESTCNTL_SSERRREN    BT_MAC_BTRFTESTCNTL_SSERRREN_Msk
#define BT_MAC_BTRFTESTCNTL_PERCOUNT_MODE_Pos  (24U)
#define BT_MAC_BTRFTESTCNTL_PERCOUNT_MODE_Msk  (0x7UL << BT_MAC_BTRFTESTCNTL_PERCOUNT_MODE_Pos)
#define BT_MAC_BTRFTESTCNTL_PERCOUNT_MODE  BT_MAC_BTRFTESTCNTL_PERCOUNT_MODE_Msk
#define BT_MAC_BTRFTESTCNTL_RXPKTCNTEN_Pos  (27U)
#define BT_MAC_BTRFTESTCNTL_RXPKTCNTEN_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_RXPKTCNTEN_Pos)
#define BT_MAC_BTRFTESTCNTL_RXPKTCNTEN  BT_MAC_BTRFTESTCNTL_RXPKTCNTEN_Msk
#define BT_MAC_BTRFTESTCNTL_INFINITERX_Pos  (31U)
#define BT_MAC_BTRFTESTCNTL_INFINITERX_Msk  (0x1UL << BT_MAC_BTRFTESTCNTL_INFINITERX_Pos)
#define BT_MAC_BTRFTESTCNTL_INFINITERX  BT_MAC_BTRFTESTCNTL_INFINITERX_Msk

/************** Bit definition for BT_MAC_BTRFTESTFREQ register ***************/
#define BT_MAC_BTRFTESTFREQ_TXFREQ_Pos  (0U)
#define BT_MAC_BTRFTESTFREQ_TXFREQ_Msk  (0x7FUL << BT_MAC_BTRFTESTFREQ_TXFREQ_Pos)
#define BT_MAC_BTRFTESTFREQ_TXFREQ      BT_MAC_BTRFTESTFREQ_TXFREQ_Msk
#define BT_MAC_BTRFTESTFREQ_RXFREQ_Pos  (8U)
#define BT_MAC_BTRFTESTFREQ_RXFREQ_Msk  (0x7FUL << BT_MAC_BTRFTESTFREQ_RXFREQ_Pos)
#define BT_MAC_BTRFTESTFREQ_RXFREQ      BT_MAC_BTRFTESTFREQ_RXFREQ_Msk
#define BT_MAC_BTRFTESTFREQ_TESTMODEEN_Pos  (16U)
#define BT_MAC_BTRFTESTFREQ_TESTMODEEN_Msk  (0x1UL << BT_MAC_BTRFTESTFREQ_TESTMODEEN_Pos)
#define BT_MAC_BTRFTESTFREQ_TESTMODEEN  BT_MAC_BTRFTESTFREQ_TESTMODEEN_Msk
#define BT_MAC_BTRFTESTFREQ_DIRECTLOOPBACKEN_Pos  (17U)
#define BT_MAC_BTRFTESTFREQ_DIRECTLOOPBACKEN_Msk  (0x1UL << BT_MAC_BTRFTESTFREQ_DIRECTLOOPBACKEN_Pos)
#define BT_MAC_BTRFTESTFREQ_DIRECTLOOPBACKEN  BT_MAC_BTRFTESTFREQ_DIRECTLOOPBACKEN_Msk
#define BT_MAC_BTRFTESTFREQ_LOOPBACK_MODE_Pos  (18U)
#define BT_MAC_BTRFTESTFREQ_LOOPBACK_MODE_Msk  (0x1UL << BT_MAC_BTRFTESTFREQ_LOOPBACK_MODE_Pos)
#define BT_MAC_BTRFTESTFREQ_LOOPBACK_MODE  BT_MAC_BTRFTESTFREQ_LOOPBACK_MODE_Msk

/************* Bit definition for BT_MAC_BTRFTESTTXSTAT register **************/
#define BT_MAC_BTRFTESTTXSTAT_TXPKTCNT_Pos  (0U)
#define BT_MAC_BTRFTESTTXSTAT_TXPKTCNT_Msk  (0xFFFFFFFFUL << BT_MAC_BTRFTESTTXSTAT_TXPKTCNT_Pos)
#define BT_MAC_BTRFTESTTXSTAT_TXPKTCNT  BT_MAC_BTRFTESTTXSTAT_TXPKTCNT_Msk

/************* Bit definition for BT_MAC_BTRFTESTRXSTAT register **************/
#define BT_MAC_BTRFTESTRXSTAT_RXPKTCNT_Pos  (0U)
#define BT_MAC_BTRFTESTRXSTAT_RXPKTCNT_Msk  (0xFFFFFFFFUL << BT_MAC_BTRFTESTRXSTAT_RXPKTCNT_Pos)
#define BT_MAC_BTRFTESTRXSTAT_RXPKTCNT  BT_MAC_BTRFTESTRXSTAT_RXPKTCNT_Msk

/************* Bit definition for BT_MAC_STARTFRMCLKNTS register **************/
#define BT_MAC_STARTFRMCLKNTS_STARTFRMCLKNTS_Pos  (0U)
#define BT_MAC_STARTFRMCLKNTS_STARTFRMCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_STARTFRMCLKNTS_STARTFRMCLKNTS_Pos)
#define BT_MAC_STARTFRMCLKNTS_STARTFRMCLKNTS  BT_MAC_STARTFRMCLKNTS_STARTFRMCLKNTS_Msk

/************ Bit definition for BT_MAC_STARTFRMFINECNTTS register ************/
#define BT_MAC_STARTFRMFINECNTTS_STARTFRMFINECNTTS_Pos  (0U)
#define BT_MAC_STARTFRMFINECNTTS_STARTFRMFINECNTTS_Msk  (0x3FFUL << BT_MAC_STARTFRMFINECNTTS_STARTFRMFINECNTTS_Pos)
#define BT_MAC_STARTFRMFINECNTTS_STARTFRMFINECNTTS  BT_MAC_STARTFRMFINECNTTS_STARTFRMFINECNTTS_Msk

/************** Bit definition for BT_MAC_ENDFRMCLKNTS register ***************/
#define BT_MAC_ENDFRMCLKNTS_ENDFRMCLKNTS_Pos  (0U)
#define BT_MAC_ENDFRMCLKNTS_ENDFRMCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_ENDFRMCLKNTS_ENDFRMCLKNTS_Pos)
#define BT_MAC_ENDFRMCLKNTS_ENDFRMCLKNTS  BT_MAC_ENDFRMCLKNTS_ENDFRMCLKNTS_Msk

/************* Bit definition for BT_MAC_ENDFRMFINECNTTS register *************/
#define BT_MAC_ENDFRMFINECNTTS_ENDFRMFINECNTTS_Pos  (0U)
#define BT_MAC_ENDFRMFINECNTTS_ENDFRMFINECNTTS_Msk  (0x3FFUL << BT_MAC_ENDFRMFINECNTTS_ENDFRMFINECNTTS_Pos)
#define BT_MAC_ENDFRMFINECNTTS_ENDFRMFINECNTTS  BT_MAC_ENDFRMFINECNTTS_ENDFRMFINECNTTS_Msk

/************** Bit definition for BT_MAC_SKIPFRMCLKNTS register **************/
#define BT_MAC_SKIPFRMCLKNTS_SKIPFRMCLKNTS_Pos  (0U)
#define BT_MAC_SKIPFRMCLKNTS_SKIPFRMCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_SKIPFRMCLKNTS_SKIPFRMCLKNTS_Pos)
#define BT_MAC_SKIPFRMCLKNTS_SKIPFRMCLKNTS  BT_MAC_SKIPFRMCLKNTS_SKIPFRMCLKNTS_Msk

/************ Bit definition for BT_MAC_SKIPFRMFINECNTTS register *************/
#define BT_MAC_SKIPFRMFINECNTTS_SKIPFRMFINECNTTS_Pos  (0U)
#define BT_MAC_SKIPFRMFINECNTTS_SKIPFRMFINECNTTS_Msk  (0x3FFUL << BT_MAC_SKIPFRMFINECNTTS_SKIPFRMFINECNTTS_Pos)
#define BT_MAC_SKIPFRMFINECNTTS_SKIPFRMFINECNTTS  BT_MAC_SKIPFRMFINECNTTS_SKIPFRMFINECNTTS_Msk

/*************** Bit definition for BT_MAC_ABTRAINCNTL register ***************/
#define BT_MAC_ABTRAINCNTL_ABTINQTIME_Pos  (0U)
#define BT_MAC_ABTRAINCNTL_ABTINQTIME_Msk  (0x7FFUL << BT_MAC_ABTRAINCNTL_ABTINQTIME_Pos)
#define BT_MAC_ABTRAINCNTL_ABTINQTIME   BT_MAC_ABTRAINCNTL_ABTINQTIME_Msk
#define BT_MAC_ABTRAINCNTL_ABTINQLOAD_Pos  (12U)
#define BT_MAC_ABTRAINCNTL_ABTINQLOAD_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTINQLOAD_Pos)
#define BT_MAC_ABTRAINCNTL_ABTINQLOAD   BT_MAC_ABTRAINCNTL_ABTINQLOAD_Msk
#define BT_MAC_ABTRAINCNTL_ABTINQSTARTVALUE_Pos  (14U)
#define BT_MAC_ABTRAINCNTL_ABTINQSTARTVALUE_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTINQSTARTVALUE_Pos)
#define BT_MAC_ABTRAINCNTL_ABTINQSTARTVALUE  BT_MAC_ABTRAINCNTL_ABTINQSTARTVALUE_Msk
#define BT_MAC_ABTRAINCNTL_ABTINQEN_Pos  (15U)
#define BT_MAC_ABTRAINCNTL_ABTINQEN_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTINQEN_Pos)
#define BT_MAC_ABTRAINCNTL_ABTINQEN     BT_MAC_ABTRAINCNTL_ABTINQEN_Msk
#define BT_MAC_ABTRAINCNTL_ABTPAGETIME_Pos  (16U)
#define BT_MAC_ABTRAINCNTL_ABTPAGETIME_Msk  (0x7FFUL << BT_MAC_ABTRAINCNTL_ABTPAGETIME_Pos)
#define BT_MAC_ABTRAINCNTL_ABTPAGETIME  BT_MAC_ABTRAINCNTL_ABTPAGETIME_Msk
#define BT_MAC_ABTRAINCNTL_ABTPAGELOAD_Pos  (28U)
#define BT_MAC_ABTRAINCNTL_ABTPAGELOAD_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTPAGELOAD_Pos)
#define BT_MAC_ABTRAINCNTL_ABTPAGELOAD  BT_MAC_ABTRAINCNTL_ABTPAGELOAD_Msk
#define BT_MAC_ABTRAINCNTL_ABTPAGESTARTVALUE_Pos  (30U)
#define BT_MAC_ABTRAINCNTL_ABTPAGESTARTVALUE_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTPAGESTARTVALUE_Pos)
#define BT_MAC_ABTRAINCNTL_ABTPAGESTARTVALUE  BT_MAC_ABTRAINCNTL_ABTPAGESTARTVALUE_Msk
#define BT_MAC_ABTRAINCNTL_ABTPAGEEN_Pos  (31U)
#define BT_MAC_ABTRAINCNTL_ABTPAGEEN_Msk  (0x1UL << BT_MAC_ABTRAINCNTL_ABTPAGEEN_Pos)
#define BT_MAC_ABTRAINCNTL_ABTPAGEEN    BT_MAC_ABTRAINCNTL_ABTPAGEEN_Msk

/***************** Bit definition for BT_MAC_EDRCNTL register *****************/
#define BT_MAC_EDRCNTL_RXGRD_TIMEOUT_Pos  (0U)
#define BT_MAC_EDRCNTL_RXGRD_TIMEOUT_Msk  (0x3FUL << BT_MAC_EDRCNTL_RXGRD_TIMEOUT_Pos)
#define BT_MAC_EDRCNTL_RXGRD_TIMEOUT    BT_MAC_EDRCNTL_RXGRD_TIMEOUT_Msk
#define BT_MAC_EDRCNTL_GB_TXQUAL_GEN_DSB_Pos  (6U)
#define BT_MAC_EDRCNTL_GB_TXQUAL_GEN_DSB_Msk  (0x1UL << BT_MAC_EDRCNTL_GB_TXQUAL_GEN_DSB_Pos)
#define BT_MAC_EDRCNTL_GB_TXQUAL_GEN_DSB  BT_MAC_EDRCNTL_GB_TXQUAL_GEN_DSB_Msk
#define BT_MAC_EDRCNTL_RXGUARDDSB_Pos   (7U)
#define BT_MAC_EDRCNTL_RXGUARDDSB_Msk   (0x1UL << BT_MAC_EDRCNTL_RXGUARDDSB_Pos)
#define BT_MAC_EDRCNTL_RXGUARDDSB       BT_MAC_EDRCNTL_RXGUARDDSB_Msk
#define BT_MAC_EDRCNTL_GUARD_BAND_TIME_Pos  (8U)
#define BT_MAC_EDRCNTL_GUARD_BAND_TIME_Msk  (0x7UL << BT_MAC_EDRCNTL_GUARD_BAND_TIME_Pos)
#define BT_MAC_EDRCNTL_GUARD_BAND_TIME  BT_MAC_EDRCNTL_GUARD_BAND_TIME_Msk
#define BT_MAC_EDRCNTL_TX_SWAP_Pos      (12U)
#define BT_MAC_EDRCNTL_TX_SWAP_Msk      (0x1UL << BT_MAC_EDRCNTL_TX_SWAP_Pos)
#define BT_MAC_EDRCNTL_TX_SWAP          BT_MAC_EDRCNTL_TX_SWAP_Msk
#define BT_MAC_EDRCNTL_RX_SWAP_Pos      (13U)
#define BT_MAC_EDRCNTL_RX_SWAP_Msk      (0x1UL << BT_MAC_EDRCNTL_RX_SWAP_Pos)
#define BT_MAC_EDRCNTL_RX_SWAP          BT_MAC_EDRCNTL_RX_SWAP_Msk
#define BT_MAC_EDRCNTL_EDRBCAST_Pos     (15U)
#define BT_MAC_EDRCNTL_EDRBCAST_Msk     (0x1UL << BT_MAC_EDRCNTL_EDRBCAST_Pos)
#define BT_MAC_EDRCNTL_EDRBCAST         BT_MAC_EDRCNTL_EDRBCAST_Msk
#define BT_MAC_EDRCNTL_TXRATE_SWINSTANT_Pos  (16U)
#define BT_MAC_EDRCNTL_TXRATE_SWINSTANT_Msk  (0x1UL << BT_MAC_EDRCNTL_TXRATE_SWINSTANT_Pos)
#define BT_MAC_EDRCNTL_TXRATE_SWINSTANT  BT_MAC_EDRCNTL_TXRATE_SWINSTANT_Msk

/**************** Bit definition for BT_MAC_PCACNTL0 register *****************/
#define BT_MAC_PCACNTL0_PHASE_SHIFT_EN_Pos  (0U)
#define BT_MAC_PCACNTL0_PHASE_SHIFT_EN_Msk  (0x1UL << BT_MAC_PCACNTL0_PHASE_SHIFT_EN_Pos)
#define BT_MAC_PCACNTL0_PHASE_SHIFT_EN  BT_MAC_PCACNTL0_PHASE_SHIFT_EN_Msk
#define BT_MAC_PCACNTL0_SYNC_SOURCE_Pos  (1U)
#define BT_MAC_PCACNTL0_SYNC_SOURCE_Msk  (0x1UL << BT_MAC_PCACNTL0_SYNC_SOURCE_Pos)
#define BT_MAC_PCACNTL0_SYNC_SOURCE     BT_MAC_PCACNTL0_SYNC_SOURCE_Msk
#define BT_MAC_PCACNTL0_FRSYNC_POL_Pos  (2U)
#define BT_MAC_PCACNTL0_FRSYNC_POL_Msk  (0x1UL << BT_MAC_PCACNTL0_FRSYNC_POL_Pos)
#define BT_MAC_PCACNTL0_FRSYNC_POL      BT_MAC_PCACNTL0_FRSYNC_POL_Msk
#define BT_MAC_PCACNTL0_BLINDCORR_EN_Pos  (3U)
#define BT_MAC_PCACNTL0_BLINDCORR_EN_Msk  (0x1UL << BT_MAC_PCACNTL0_BLINDCORR_EN_Pos)
#define BT_MAC_PCACNTL0_BLINDCORR_EN    BT_MAC_PCACNTL0_BLINDCORR_EN_Msk
#define BT_MAC_PCACNTL0_CORR_STEP_Pos   (4U)
#define BT_MAC_PCACNTL0_CORR_STEP_Msk   (0xFUL << BT_MAC_PCACNTL0_CORR_STEP_Pos)
#define BT_MAC_PCACNTL0_CORR_STEP       BT_MAC_PCACNTL0_CORR_STEP_Msk
#define BT_MAC_PCACNTL0_SLVLBL_Pos      (8U)
#define BT_MAC_PCACNTL0_SLVLBL_Msk      (0x1FUL << BT_MAC_PCACNTL0_SLVLBL_Pos)
#define BT_MAC_PCACNTL0_SLVLBL          BT_MAC_PCACNTL0_SLVLBL_Msk
#define BT_MAC_PCACNTL0_TARGET_OFFSET_Pos  (16U)
#define BT_MAC_PCACNTL0_TARGET_OFFSET_Msk  (0x7FFUL << BT_MAC_PCACNTL0_TARGET_OFFSET_Pos)
#define BT_MAC_PCACNTL0_TARGET_OFFSET   BT_MAC_PCACNTL0_TARGET_OFFSET_Msk

/**************** Bit definition for BT_MAC_PCACNTL1 register *****************/
#define BT_MAC_PCACNTL1_CLOCK_SHIFT_Pos  (0U)
#define BT_MAC_PCACNTL1_CLOCK_SHIFT_Msk  (0x7FFUL << BT_MAC_PCACNTL1_CLOCK_SHIFT_Pos)
#define BT_MAC_PCACNTL1_CLOCK_SHIFT     BT_MAC_PCACNTL1_CLOCK_SHIFT_Msk
#define BT_MAC_PCACNTL1_CLOCK_SHIFT_EN_Pos  (12U)
#define BT_MAC_PCACNTL1_CLOCK_SHIFT_EN_Msk  (0x1UL << BT_MAC_PCACNTL1_CLOCK_SHIFT_EN_Pos)
#define BT_MAC_PCACNTL1_CLOCK_SHIFT_EN  BT_MAC_PCACNTL1_CLOCK_SHIFT_EN_Msk
#define BT_MAC_PCACNTL1_CORR_INTERVAL_Pos  (16U)
#define BT_MAC_PCACNTL1_CORR_INTERVAL_Msk  (0xFFUL << BT_MAC_PCACNTL1_CORR_INTERVAL_Pos)
#define BT_MAC_PCACNTL1_CORR_INTERVAL   BT_MAC_PCACNTL1_CORR_INTERVAL_Msk

/***************** Bit definition for BT_MAC_PCASTAT register *****************/
#define BT_MAC_PCASTAT_MOMENT_OFFSET_Pos  (0U)
#define BT_MAC_PCASTAT_MOMENT_OFFSET_Msk  (0x7FFUL << BT_MAC_PCASTAT_MOMENT_OFFSET_Pos)
#define BT_MAC_PCASTAT_MOMENT_OFFSET    BT_MAC_PCASTAT_MOMENT_OFFSET_Msk
#define BT_MAC_PCASTAT_SHIFT_PHASE_Pos  (16U)
#define BT_MAC_PCASTAT_SHIFT_PHASE_Msk  (0x7FFUL << BT_MAC_PCASTAT_SHIFT_PHASE_Pos)
#define BT_MAC_PCASTAT_SHIFT_PHASE      BT_MAC_PCASTAT_SHIFT_PHASE_Msk

/************** Bit definition for BT_MAC_BTCOEXIFCNTL0 register **************/
#define BT_MAC_BTCOEXIFCNTL0_WLANCOEX_EN_Pos  (0U)
#define BT_MAC_BTCOEXIFCNTL0_WLANCOEX_EN_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL0_WLANCOEX_EN_Pos)
#define BT_MAC_BTCOEXIFCNTL0_WLANCOEX_EN  BT_MAC_BTCOEXIFCNTL0_WLANCOEX_EN_Msk
#define BT_MAC_BTCOEXIFCNTL0_SYNCGEN_EN_Pos  (1U)
#define BT_MAC_BTCOEXIFCNTL0_SYNCGEN_EN_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL0_SYNCGEN_EN_Pos)
#define BT_MAC_BTCOEXIFCNTL0_SYNCGEN_EN  BT_MAC_BTCOEXIFCNTL0_SYNCGEN_EN_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSCOEX_EN_Pos  (2U)
#define BT_MAC_BTCOEXIFCNTL0_MWSCOEX_EN_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL0_MWSCOEX_EN_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSCOEX_EN  BT_MAC_BTCOEXIFCNTL0_MWSCOEX_EN_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSWCI_EN_Pos  (3U)
#define BT_MAC_BTCOEXIFCNTL0_MWSWCI_EN_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL0_MWSWCI_EN_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSWCI_EN  BT_MAC_BTCOEXIFCNTL0_MWSWCI_EN_Msk
#define BT_MAC_BTCOEXIFCNTL0_WLANRXMSK_Pos  (4U)
#define BT_MAC_BTCOEXIFCNTL0_WLANRXMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_WLANRXMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_WLANRXMSK  BT_MAC_BTCOEXIFCNTL0_WLANRXMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_WLANTXMSK_Pos  (6U)
#define BT_MAC_BTCOEXIFCNTL0_WLANTXMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_WLANTXMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_WLANTXMSK  BT_MAC_BTCOEXIFCNTL0_WLANTXMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSRXMSK_Pos  (8U)
#define BT_MAC_BTCOEXIFCNTL0_MWSRXMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_MWSRXMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSRXMSK   BT_MAC_BTCOEXIFCNTL0_MWSRXMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSTXMSK_Pos  (10U)
#define BT_MAC_BTCOEXIFCNTL0_MWSTXMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_MWSTXMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSTXMSK   BT_MAC_BTCOEXIFCNTL0_MWSTXMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSRXFREQMSK_Pos  (12U)
#define BT_MAC_BTCOEXIFCNTL0_MWSRXFREQMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_MWSRXFREQMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSRXFREQMSK  BT_MAC_BTCOEXIFCNTL0_MWSRXFREQMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSTXFREQMSK_Pos  (14U)
#define BT_MAC_BTCOEXIFCNTL0_MWSTXFREQMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_MWSTXFREQMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSTXFREQMSK  BT_MAC_BTCOEXIFCNTL0_MWSTXFREQMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_WLCTXPRIOMODE_Pos  (16U)
#define BT_MAC_BTCOEXIFCNTL0_WLCTXPRIOMODE_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_WLCTXPRIOMODE_Pos)
#define BT_MAC_BTCOEXIFCNTL0_WLCTXPRIOMODE  BT_MAC_BTCOEXIFCNTL0_WLCTXPRIOMODE_Msk
#define BT_MAC_BTCOEXIFCNTL0_WLCRXPRIOMODE_Pos  (18U)
#define BT_MAC_BTCOEXIFCNTL0_WLCRXPRIOMODE_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_WLCRXPRIOMODE_Pos)
#define BT_MAC_BTCOEXIFCNTL0_WLCRXPRIOMODE  BT_MAC_BTCOEXIFCNTL0_WLCRXPRIOMODE_Msk
#define BT_MAC_BTCOEXIFCNTL0_MWSSCANFREQMSK_Pos  (20U)
#define BT_MAC_BTCOEXIFCNTL0_MWSSCANFREQMSK_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL0_MWSSCANFREQMSK_Pos)
#define BT_MAC_BTCOEXIFCNTL0_MWSSCANFREQMSK  BT_MAC_BTCOEXIFCNTL0_MWSSCANFREQMSK_Msk
#define BT_MAC_BTCOEXIFCNTL0_PAGEEKNUDGEINC_Pos  (24U)
#define BT_MAC_BTCOEXIFCNTL0_PAGEEKNUDGEINC_Msk  (0xFUL << BT_MAC_BTCOEXIFCNTL0_PAGEEKNUDGEINC_Pos)
#define BT_MAC_BTCOEXIFCNTL0_PAGEEKNUDGEINC  BT_MAC_BTCOEXIFCNTL0_PAGEEKNUDGEINC_Msk
#define BT_MAC_BTCOEXIFCNTL0_INQKNUDGEINC_Pos  (28U)
#define BT_MAC_BTCOEXIFCNTL0_INQKNUDGEINC_Msk  (0xFUL << BT_MAC_BTCOEXIFCNTL0_INQKNUDGEINC_Pos)
#define BT_MAC_BTCOEXIFCNTL0_INQKNUDGEINC  BT_MAC_BTCOEXIFCNTL0_INQKNUDGEINC_Msk

/************** Bit definition for BT_MAC_BTCOEXIFCNTL1 register **************/
#define BT_MAC_BTCOEXIFCNTL1_WLCPDELAY_Pos  (0U)
#define BT_MAC_BTCOEXIFCNTL1_WLCPDELAY_Msk  (0x7FUL << BT_MAC_BTCOEXIFCNTL1_WLCPDELAY_Pos)
#define BT_MAC_BTCOEXIFCNTL1_WLCPDELAY  BT_MAC_BTCOEXIFCNTL1_WLCPDELAY_Msk
#define BT_MAC_BTCOEXIFCNTL1_WLCPDURATION_Pos  (8U)
#define BT_MAC_BTCOEXIFCNTL1_WLCPDURATION_Msk  (0x7FUL << BT_MAC_BTCOEXIFCNTL1_WLCPDURATION_Pos)
#define BT_MAC_BTCOEXIFCNTL1_WLCPDURATION  BT_MAC_BTCOEXIFCNTL1_WLCPDURATION_Msk
#define BT_MAC_BTCOEXIFCNTL1_WLCPTXTHR_Pos  (16U)
#define BT_MAC_BTCOEXIFCNTL1_WLCPTXTHR_Msk  (0x1FUL << BT_MAC_BTCOEXIFCNTL1_WLCPTXTHR_Pos)
#define BT_MAC_BTCOEXIFCNTL1_WLCPTXTHR  BT_MAC_BTCOEXIFCNTL1_WLCPTXTHR_Msk
#define BT_MAC_BTCOEXIFCNTL1_WLCPRXTHR_Pos  (24U)
#define BT_MAC_BTCOEXIFCNTL1_WLCPRXTHR_Msk  (0x1FUL << BT_MAC_BTCOEXIFCNTL1_WLCPRXTHR_Pos)
#define BT_MAC_BTCOEXIFCNTL1_WLCPRXTHR  BT_MAC_BTCOEXIFCNTL1_WLCPRXTHR_Msk

/************** Bit definition for BT_MAC_BTCOEXIFCNTL2 register **************/
#define BT_MAC_BTCOEXIFCNTL2_TX_ANT_DELAY_Pos  (0U)
#define BT_MAC_BTCOEXIFCNTL2_TX_ANT_DELAY_Msk  (0xFUL << BT_MAC_BTCOEXIFCNTL2_TX_ANT_DELAY_Pos)
#define BT_MAC_BTCOEXIFCNTL2_TX_ANT_DELAY  BT_MAC_BTCOEXIFCNTL2_TX_ANT_DELAY_Msk
#define BT_MAC_BTCOEXIFCNTL2_RX_ANT_DELAY_Pos  (8U)
#define BT_MAC_BTCOEXIFCNTL2_RX_ANT_DELAY_Msk  (0xFUL << BT_MAC_BTCOEXIFCNTL2_RX_ANT_DELAY_Pos)
#define BT_MAC_BTCOEXIFCNTL2_RX_ANT_DELAY  BT_MAC_BTCOEXIFCNTL2_RX_ANT_DELAY_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_VAL_Pos  (13U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_VAL_Msk  (0x7UL << BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_VAL_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_VAL  BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_VAL_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTSEL_Pos  (16U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTSEL_Msk  (0x3UL << BT_MAC_BTCOEXIFCNTL2_PTA_ACTSEL_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTSEL  BT_MAC_BTCOEXIFCNTL2_PTA_ACTSEL_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTMODE_Pos  (18U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTMODE_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_ACTMODE_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTMODE  BT_MAC_BTCOEXIFCNTL2_PTA_ACTMODE_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTPOL_Pos  (19U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTPOL_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_ACTPOL_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ACTPOL  BT_MAC_BTCOEXIFCNTL2_PTA_ACTPOL_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_PRIOMODE_Pos  (20U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_PRIOMODE_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_PRIOMODE_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_PRIOMODE  BT_MAC_BTCOEXIFCNTL2_PTA_PRIOMODE_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTMODE_Pos  (21U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTMODE_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_ABORTMODE_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTMODE  BT_MAC_BTCOEXIFCNTL2_PTA_ABORTMODE_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANPOL_Pos  (22U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANPOL_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_WLANPOL_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANPOL  BT_MAC_BTCOEXIFCNTL2_PTA_WLANPOL_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTTX_Pos  (23U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTTX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_ABORTTX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTTX  BT_MAC_BTCOEXIFCNTL2_PTA_ABORTTX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTRX_Pos  (24U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTRX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_ABORTRX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_ABORTRX  BT_MAC_BTCOEXIFCNTL2_PTA_ABORTRX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKTX_Pos  (25U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKTX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_MASKTX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKTX  BT_MAC_BTCOEXIFCNTL2_PTA_MASKTX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKRX_Pos  (26U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKRX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_MASKRX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_MASKRX  BT_MAC_BTCOEXIFCNTL2_PTA_MASKRX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANTX_Pos  (27U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANTX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_WLANTX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANTX  BT_MAC_BTCOEXIFCNTL2_PTA_WLANTX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANRX_Pos  (28U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANRX_Msk  (0x1UL << BT_MAC_BTCOEXIFCNTL2_PTA_WLANRX_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_WLANRX  BT_MAC_BTCOEXIFCNTL2_PTA_WLANRX_Msk
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_EN_Pos  (29U)
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_EN_Msk  (0x7UL << BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_EN_Pos)
#define BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_EN  BT_MAC_BTCOEXIFCNTL2_PTA_FORCE_EN_Msk

/**************** Bit definition for BT_MAC_BTMPRIO0 register *****************/
#define BT_MAC_BTMPRIO0_BTM0_Pos        (0U)
#define BT_MAC_BTMPRIO0_BTM0_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM0_Pos)
#define BT_MAC_BTMPRIO0_BTM0            BT_MAC_BTMPRIO0_BTM0_Msk
#define BT_MAC_BTMPRIO0_BTM1_Pos        (4U)
#define BT_MAC_BTMPRIO0_BTM1_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM1_Pos)
#define BT_MAC_BTMPRIO0_BTM1            BT_MAC_BTMPRIO0_BTM1_Msk
#define BT_MAC_BTMPRIO0_BTM2_Pos        (8U)
#define BT_MAC_BTMPRIO0_BTM2_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM2_Pos)
#define BT_MAC_BTMPRIO0_BTM2            BT_MAC_BTMPRIO0_BTM2_Msk
#define BT_MAC_BTMPRIO0_BTM3_Pos        (12U)
#define BT_MAC_BTMPRIO0_BTM3_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM3_Pos)
#define BT_MAC_BTMPRIO0_BTM3            BT_MAC_BTMPRIO0_BTM3_Msk
#define BT_MAC_BTMPRIO0_BTM4_Pos        (16U)
#define BT_MAC_BTMPRIO0_BTM4_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM4_Pos)
#define BT_MAC_BTMPRIO0_BTM4            BT_MAC_BTMPRIO0_BTM4_Msk
#define BT_MAC_BTMPRIO0_BTM5_Pos        (20U)
#define BT_MAC_BTMPRIO0_BTM5_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM5_Pos)
#define BT_MAC_BTMPRIO0_BTM5            BT_MAC_BTMPRIO0_BTM5_Msk
#define BT_MAC_BTMPRIO0_BTM6_Pos        (24U)
#define BT_MAC_BTMPRIO0_BTM6_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM6_Pos)
#define BT_MAC_BTMPRIO0_BTM6            BT_MAC_BTMPRIO0_BTM6_Msk
#define BT_MAC_BTMPRIO0_BTM7_Pos        (28U)
#define BT_MAC_BTMPRIO0_BTM7_Msk        (0xFUL << BT_MAC_BTMPRIO0_BTM7_Pos)
#define BT_MAC_BTMPRIO0_BTM7            BT_MAC_BTMPRIO0_BTM7_Msk

/**************** Bit definition for BT_MAC_BTMPRIO1 register *****************/
#define BT_MAC_BTMPRIO1_BTM8_Pos        (0U)
#define BT_MAC_BTMPRIO1_BTM8_Msk        (0xFUL << BT_MAC_BTMPRIO1_BTM8_Pos)
#define BT_MAC_BTMPRIO1_BTM8            BT_MAC_BTMPRIO1_BTM8_Msk
#define BT_MAC_BTMPRIO1_BTM9_Pos        (4U)
#define BT_MAC_BTMPRIO1_BTM9_Msk        (0xFUL << BT_MAC_BTMPRIO1_BTM9_Pos)
#define BT_MAC_BTMPRIO1_BTM9            BT_MAC_BTMPRIO1_BTM9_Msk
#define BT_MAC_BTMPRIO1_BTM10_Pos       (8U)
#define BT_MAC_BTMPRIO1_BTM10_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM10_Pos)
#define BT_MAC_BTMPRIO1_BTM10           BT_MAC_BTMPRIO1_BTM10_Msk
#define BT_MAC_BTMPRIO1_BTM11_Pos       (12U)
#define BT_MAC_BTMPRIO1_BTM11_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM11_Pos)
#define BT_MAC_BTMPRIO1_BTM11           BT_MAC_BTMPRIO1_BTM11_Msk
#define BT_MAC_BTMPRIO1_BTM12_Pos       (16U)
#define BT_MAC_BTMPRIO1_BTM12_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM12_Pos)
#define BT_MAC_BTMPRIO1_BTM12           BT_MAC_BTMPRIO1_BTM12_Msk
#define BT_MAC_BTMPRIO1_BTM13_Pos       (20U)
#define BT_MAC_BTMPRIO1_BTM13_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM13_Pos)
#define BT_MAC_BTMPRIO1_BTM13           BT_MAC_BTMPRIO1_BTM13_Msk
#define BT_MAC_BTMPRIO1_BTM14_Pos       (24U)
#define BT_MAC_BTMPRIO1_BTM14_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM14_Pos)
#define BT_MAC_BTMPRIO1_BTM14           BT_MAC_BTMPRIO1_BTM14_Msk
#define BT_MAC_BTMPRIO1_BTM15_Pos       (28U)
#define BT_MAC_BTMPRIO1_BTM15_Msk       (0xFUL << BT_MAC_BTMPRIO1_BTM15_Pos)
#define BT_MAC_BTMPRIO1_BTM15           BT_MAC_BTMPRIO1_BTM15_Msk

/**************** Bit definition for BT_MAC_BTMPRIO2 register *****************/
#define BT_MAC_BTMPRIO2_BTM16_Pos       (0U)
#define BT_MAC_BTMPRIO2_BTM16_Msk       (0xFUL << BT_MAC_BTMPRIO2_BTM16_Pos)
#define BT_MAC_BTMPRIO2_BTM16           BT_MAC_BTMPRIO2_BTM16_Msk
#define BT_MAC_BTMPRIO2_BTM17_Pos       (4U)
#define BT_MAC_BTMPRIO2_BTM17_Msk       (0xFUL << BT_MAC_BTMPRIO2_BTM17_Pos)
#define BT_MAC_BTMPRIO2_BTM17           BT_MAC_BTMPRIO2_BTM17_Msk
#define BT_MAC_BTMPRIO2_BTM18_Pos       (8U)
#define BT_MAC_BTMPRIO2_BTM18_Msk       (0xFUL << BT_MAC_BTMPRIO2_BTM18_Pos)
#define BT_MAC_BTMPRIO2_BTM18           BT_MAC_BTMPRIO2_BTM18_Msk
#define BT_MAC_BTMPRIO2_BTM19_Pos       (12U)
#define BT_MAC_BTMPRIO2_BTM19_Msk       (0xFUL << BT_MAC_BTMPRIO2_BTM19_Pos)
#define BT_MAC_BTMPRIO2_BTM19           BT_MAC_BTMPRIO2_BTM19_Msk
#define BT_MAC_BTMPRIO2_BTM20_Pos       (16U)
#define BT_MAC_BTMPRIO2_BTM20_Msk       (0xFUL << BT_MAC_BTMPRIO2_BTM20_Pos)
#define BT_MAC_BTMPRIO2_BTM20           BT_MAC_BTMPRIO2_BTM20_Msk
#define BT_MAC_BTMPRIO2_BTMDEFAULT_Pos  (28U)
#define BT_MAC_BTMPRIO2_BTMDEFAULT_Msk  (0xFUL << BT_MAC_BTMPRIO2_BTMDEFAULT_Pos)
#define BT_MAC_BTMPRIO2_BTMDEFAULT      BT_MAC_BTMPRIO2_BTMDEFAULT_Msk

/**************** Bit definition for BT_MAC_COEXCHN0 register *****************/
#define BT_MAC_COEXCHN0_COEXCHN31_0_Pos  (0U)
#define BT_MAC_COEXCHN0_COEXCHN31_0_Msk  (0xFFFFFFFFUL << BT_MAC_COEXCHN0_COEXCHN31_0_Pos)
#define BT_MAC_COEXCHN0_COEXCHN31_0     BT_MAC_COEXCHN0_COEXCHN31_0_Msk

/**************** Bit definition for BT_MAC_COEXCHN1 register *****************/
#define BT_MAC_COEXCHN1_COEXCHN63_32_Pos  (0U)
#define BT_MAC_COEXCHN1_COEXCHN63_32_Msk  (0xFFFFFFFFUL << BT_MAC_COEXCHN1_COEXCHN63_32_Pos)
#define BT_MAC_COEXCHN1_COEXCHN63_32    BT_MAC_COEXCHN1_COEXCHN63_32_Msk

/**************** Bit definition for BT_MAC_COEXCHN2 register *****************/
#define BT_MAC_COEXCHN2_COEXCHN78_64_Pos  (0U)
#define BT_MAC_COEXCHN2_COEXCHN78_64_Msk  (0x7FFFUL << BT_MAC_COEXCHN2_COEXCHN78_64_Pos)
#define BT_MAC_COEXCHN2_COEXCHN78_64    BT_MAC_COEXCHN2_COEXCHN78_64_Msk

/************** Bit definition for BT_MAC_ESCOCHANCNTL0 register **************/
#define BT_MAC_ESCOCHANCNTL0_TESCO0_Pos  (0U)
#define BT_MAC_ESCOCHANCNTL0_TESCO0_Msk  (0xFFUL << BT_MAC_ESCOCHANCNTL0_TESCO0_Pos)
#define BT_MAC_ESCOCHANCNTL0_TESCO0     BT_MAC_ESCOCHANCNTL0_TESCO0_Msk
#define BT_MAC_ESCOCHANCNTL0_INTDELAY0_Pos  (8U)
#define BT_MAC_ESCOCHANCNTL0_INTDELAY0_Msk  (0x1FUL << BT_MAC_ESCOCHANCNTL0_INTDELAY0_Pos)
#define BT_MAC_ESCOCHANCNTL0_INTDELAY0  BT_MAC_ESCOCHANCNTL0_INTDELAY0_Msk
#define BT_MAC_ESCOCHANCNTL0_ITMODE0_Pos  (13U)
#define BT_MAC_ESCOCHANCNTL0_ITMODE0_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL0_ITMODE0_Pos)
#define BT_MAC_ESCOCHANCNTL0_ITMODE0    BT_MAC_ESCOCHANCNTL0_ITMODE0_Msk
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANEN0_Pos  (14U)
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANEN0_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL0_ESCOCHANEN0_Pos)
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANEN0  BT_MAC_ESCOCHANCNTL0_ESCOCHANEN0_Msk
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANSWEN0_Pos  (15U)
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANSWEN0_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL0_ESCOCHANSWEN0_Pos)
#define BT_MAC_ESCOCHANCNTL0_ESCOCHANSWEN0  BT_MAC_ESCOCHANCNTL0_ESCOCHANSWEN0_Msk
#define BT_MAC_ESCOCHANCNTL0_TOG0_Pos   (31U)
#define BT_MAC_ESCOCHANCNTL0_TOG0_Msk   (0x1UL << BT_MAC_ESCOCHANCNTL0_TOG0_Pos)
#define BT_MAC_ESCOCHANCNTL0_TOG0       BT_MAC_ESCOCHANCNTL0_TOG0_Msk

/************** Bit definition for BT_MAC_ESCOMUTECNTL0 register **************/
#define BT_MAC_ESCOMUTECNTL0_MUTEPATT0_Pos  (0U)
#define BT_MAC_ESCOMUTECNTL0_MUTEPATT0_Msk  (0xFFFFUL << BT_MAC_ESCOMUTECNTL0_MUTEPATT0_Pos)
#define BT_MAC_ESCOMUTECNTL0_MUTEPATT0  BT_MAC_ESCOMUTECNTL0_MUTEPATT0_Msk
#define BT_MAC_ESCOMUTECNTL0_INVL0_0_Pos  (16U)
#define BT_MAC_ESCOMUTECNTL0_INVL0_0_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL0_INVL0_0_Pos)
#define BT_MAC_ESCOMUTECNTL0_INVL0_0    BT_MAC_ESCOMUTECNTL0_INVL0_0_Msk
#define BT_MAC_ESCOMUTECNTL0_INVL0_1_Pos  (18U)
#define BT_MAC_ESCOMUTECNTL0_INVL0_1_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL0_INVL0_1_Pos)
#define BT_MAC_ESCOMUTECNTL0_INVL0_1    BT_MAC_ESCOMUTECNTL0_INVL0_1_Msk
#define BT_MAC_ESCOMUTECNTL0_MUTE_SOURCE0_Pos  (22U)
#define BT_MAC_ESCOMUTECNTL0_MUTE_SOURCE0_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL0_MUTE_SOURCE0_Pos)
#define BT_MAC_ESCOMUTECNTL0_MUTE_SOURCE0  BT_MAC_ESCOMUTECNTL0_MUTE_SOURCE0_Msk
#define BT_MAC_ESCOMUTECNTL0_MUTE_SINK0_Pos  (23U)
#define BT_MAC_ESCOMUTECNTL0_MUTE_SINK0_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL0_MUTE_SINK0_Pos)
#define BT_MAC_ESCOMUTECNTL0_MUTE_SINK0  BT_MAC_ESCOMUTECNTL0_MUTE_SINK0_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTTXPTR0 register ************/
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX0_Pos)
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX0  BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX0_Msk
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX1_Pos)
#define BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX1  BT_MAC_ESCOCURRENTTXPTR0_ESCO0PTRTX1_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTRXPTR0 register ************/
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX0_Pos)
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX0  BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX0_Msk
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX1_Pos)
#define BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX1  BT_MAC_ESCOCURRENTRXPTR0_ESCO0PTRRX1_Msk

/*************** Bit definition for BT_MAC_ESCOLTCNTL0 register ***************/
#define BT_MAC_ESCOLTCNTL0_SYNLTADDR0_Pos  (0U)
#define BT_MAC_ESCOLTCNTL0_SYNLTADDR0_Msk  (0x7UL << BT_MAC_ESCOLTCNTL0_SYNLTADDR0_Pos)
#define BT_MAC_ESCOLTCNTL0_SYNLTADDR0   BT_MAC_ESCOLTCNTL0_SYNLTADDR0_Msk
#define BT_MAC_ESCOLTCNTL0_SYNTYPE0_Pos  (3U)
#define BT_MAC_ESCOLTCNTL0_SYNTYPE0_Msk  (0x1UL << BT_MAC_ESCOLTCNTL0_SYNTYPE0_Pos)
#define BT_MAC_ESCOLTCNTL0_SYNTYPE0     BT_MAC_ESCOLTCNTL0_SYNTYPE0_Msk
#define BT_MAC_ESCOLTCNTL0_ESCOEDRTX0_Pos  (4U)
#define BT_MAC_ESCOLTCNTL0_ESCOEDRTX0_Msk  (0x1UL << BT_MAC_ESCOLTCNTL0_ESCOEDRTX0_Pos)
#define BT_MAC_ESCOLTCNTL0_ESCOEDRTX0   BT_MAC_ESCOLTCNTL0_ESCOEDRTX0_Msk
#define BT_MAC_ESCOLTCNTL0_ESCOEDRRX0_Pos  (5U)
#define BT_MAC_ESCOLTCNTL0_ESCOEDRRX0_Msk  (0x1UL << BT_MAC_ESCOLTCNTL0_ESCOEDRRX0_Pos)
#define BT_MAC_ESCOLTCNTL0_ESCOEDRRX0   BT_MAC_ESCOLTCNTL0_ESCOEDRRX0_Msk
#define BT_MAC_ESCOLTCNTL0_RETXNB0_Pos  (16U)
#define BT_MAC_ESCOLTCNTL0_RETXNB0_Msk  (0xFFUL << BT_MAC_ESCOLTCNTL0_RETXNB0_Pos)
#define BT_MAC_ESCOLTCNTL0_RETXNB0      BT_MAC_ESCOLTCNTL0_RETXNB0_Msk

/*************** Bit definition for BT_MAC_ESCOTRCNTL0 register ***************/
#define BT_MAC_ESCOTRCNTL0_RXTYPE0_Pos  (0U)
#define BT_MAC_ESCOTRCNTL0_RXTYPE0_Msk  (0xFUL << BT_MAC_ESCOTRCNTL0_RXTYPE0_Pos)
#define BT_MAC_ESCOTRCNTL0_RXTYPE0      BT_MAC_ESCOTRCNTL0_RXTYPE0_Msk
#define BT_MAC_ESCOTRCNTL0_RXLEN0_Pos   (4U)
#define BT_MAC_ESCOTRCNTL0_RXLEN0_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL0_RXLEN0_Pos)
#define BT_MAC_ESCOTRCNTL0_RXLEN0       BT_MAC_ESCOTRCNTL0_RXLEN0_Msk
#define BT_MAC_ESCOTRCNTL0_TXTYPE0_Pos  (16U)
#define BT_MAC_ESCOTRCNTL0_TXTYPE0_Msk  (0xFUL << BT_MAC_ESCOTRCNTL0_TXTYPE0_Pos)
#define BT_MAC_ESCOTRCNTL0_TXTYPE0      BT_MAC_ESCOTRCNTL0_TXTYPE0_Msk
#define BT_MAC_ESCOTRCNTL0_TXLEN0_Pos   (20U)
#define BT_MAC_ESCOTRCNTL0_TXLEN0_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL0_TXLEN0_Pos)
#define BT_MAC_ESCOTRCNTL0_TXLEN0       BT_MAC_ESCOTRCNTL0_TXLEN0_Msk
#define BT_MAC_ESCOTRCNTL0_TXSEQN0_Pos  (31U)
#define BT_MAC_ESCOTRCNTL0_TXSEQN0_Msk  (0x1UL << BT_MAC_ESCOTRCNTL0_TXSEQN0_Pos)
#define BT_MAC_ESCOTRCNTL0_TXSEQN0      BT_MAC_ESCOTRCNTL0_TXSEQN0_Msk

/*************** Bit definition for BT_MAC_ESCODAYCNT0 register ***************/
#define BT_MAC_ESCODAYCNT0_DAYCONTER0_Pos  (0U)
#define BT_MAC_ESCODAYCNT0_DAYCONTER0_Msk  (0x7FFUL << BT_MAC_ESCODAYCNT0_DAYCONTER0_Pos)
#define BT_MAC_ESCODAYCNT0_DAYCONTER0   BT_MAC_ESCODAYCNT0_DAYCONTER0_Msk

/************** Bit definition for BT_MAC_ESCOCHANCNTL1 register **************/
#define BT_MAC_ESCOCHANCNTL1_TESCO1_Pos  (0U)
#define BT_MAC_ESCOCHANCNTL1_TESCO1_Msk  (0xFFUL << BT_MAC_ESCOCHANCNTL1_TESCO1_Pos)
#define BT_MAC_ESCOCHANCNTL1_TESCO1     BT_MAC_ESCOCHANCNTL1_TESCO1_Msk
#define BT_MAC_ESCOCHANCNTL1_INTDELAY1_Pos  (8U)
#define BT_MAC_ESCOCHANCNTL1_INTDELAY1_Msk  (0x1FUL << BT_MAC_ESCOCHANCNTL1_INTDELAY1_Pos)
#define BT_MAC_ESCOCHANCNTL1_INTDELAY1  BT_MAC_ESCOCHANCNTL1_INTDELAY1_Msk
#define BT_MAC_ESCOCHANCNTL1_ITMODE1_Pos  (13U)
#define BT_MAC_ESCOCHANCNTL1_ITMODE1_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL1_ITMODE1_Pos)
#define BT_MAC_ESCOCHANCNTL1_ITMODE1    BT_MAC_ESCOCHANCNTL1_ITMODE1_Msk
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANEN1_Pos  (14U)
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANEN1_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL1_ESCOCHANEN1_Pos)
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANEN1  BT_MAC_ESCOCHANCNTL1_ESCOCHANEN1_Msk
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANSWEN1_Pos  (15U)
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANSWEN1_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL1_ESCOCHANSWEN1_Pos)
#define BT_MAC_ESCOCHANCNTL1_ESCOCHANSWEN1  BT_MAC_ESCOCHANCNTL1_ESCOCHANSWEN1_Msk
#define BT_MAC_ESCOCHANCNTL1_TOG1_Pos   (31U)
#define BT_MAC_ESCOCHANCNTL1_TOG1_Msk   (0x1UL << BT_MAC_ESCOCHANCNTL1_TOG1_Pos)
#define BT_MAC_ESCOCHANCNTL1_TOG1       BT_MAC_ESCOCHANCNTL1_TOG1_Msk

/************** Bit definition for BT_MAC_ESCOMUTECNTL1 register **************/
#define BT_MAC_ESCOMUTECNTL1_MUTEPATT1_Pos  (0U)
#define BT_MAC_ESCOMUTECNTL1_MUTEPATT1_Msk  (0xFFFFUL << BT_MAC_ESCOMUTECNTL1_MUTEPATT1_Pos)
#define BT_MAC_ESCOMUTECNTL1_MUTEPATT1  BT_MAC_ESCOMUTECNTL1_MUTEPATT1_Msk
#define BT_MAC_ESCOMUTECNTL1_INVL1_0_Pos  (16U)
#define BT_MAC_ESCOMUTECNTL1_INVL1_0_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL1_INVL1_0_Pos)
#define BT_MAC_ESCOMUTECNTL1_INVL1_0    BT_MAC_ESCOMUTECNTL1_INVL1_0_Msk
#define BT_MAC_ESCOMUTECNTL1_INVL1_1_Pos  (18U)
#define BT_MAC_ESCOMUTECNTL1_INVL1_1_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL1_INVL1_1_Pos)
#define BT_MAC_ESCOMUTECNTL1_INVL1_1    BT_MAC_ESCOMUTECNTL1_INVL1_1_Msk
#define BT_MAC_ESCOMUTECNTL1_MUTE_SOURCE1_Pos  (22U)
#define BT_MAC_ESCOMUTECNTL1_MUTE_SOURCE1_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL1_MUTE_SOURCE1_Pos)
#define BT_MAC_ESCOMUTECNTL1_MUTE_SOURCE1  BT_MAC_ESCOMUTECNTL1_MUTE_SOURCE1_Msk
#define BT_MAC_ESCOMUTECNTL1_MUTE_SINK1_Pos  (23U)
#define BT_MAC_ESCOMUTECNTL1_MUTE_SINK1_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL1_MUTE_SINK1_Pos)
#define BT_MAC_ESCOMUTECNTL1_MUTE_SINK1  BT_MAC_ESCOMUTECNTL1_MUTE_SINK1_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTTXPTR1 register ************/
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX0_Pos)
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX0  BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX0_Msk
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX1_Pos)
#define BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX1  BT_MAC_ESCOCURRENTTXPTR1_ESCO1PTRTX1_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTRXPTR1 register ************/
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX0_Pos)
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX0  BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX0_Msk
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX1_Pos)
#define BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX1  BT_MAC_ESCOCURRENTRXPTR1_ESCO1PTRRX1_Msk

/*************** Bit definition for BT_MAC_ESCOLTCNTL1 register ***************/
#define BT_MAC_ESCOLTCNTL1_SYNLTADDR1_Pos  (0U)
#define BT_MAC_ESCOLTCNTL1_SYNLTADDR1_Msk  (0x7UL << BT_MAC_ESCOLTCNTL1_SYNLTADDR1_Pos)
#define BT_MAC_ESCOLTCNTL1_SYNLTADDR1   BT_MAC_ESCOLTCNTL1_SYNLTADDR1_Msk
#define BT_MAC_ESCOLTCNTL1_SYNTYPE1_Pos  (3U)
#define BT_MAC_ESCOLTCNTL1_SYNTYPE1_Msk  (0x1UL << BT_MAC_ESCOLTCNTL1_SYNTYPE1_Pos)
#define BT_MAC_ESCOLTCNTL1_SYNTYPE1     BT_MAC_ESCOLTCNTL1_SYNTYPE1_Msk
#define BT_MAC_ESCOLTCNTL1_ESCOEDRTX1_Pos  (4U)
#define BT_MAC_ESCOLTCNTL1_ESCOEDRTX1_Msk  (0x1UL << BT_MAC_ESCOLTCNTL1_ESCOEDRTX1_Pos)
#define BT_MAC_ESCOLTCNTL1_ESCOEDRTX1   BT_MAC_ESCOLTCNTL1_ESCOEDRTX1_Msk
#define BT_MAC_ESCOLTCNTL1_ESCOEDRRX1_Pos  (5U)
#define BT_MAC_ESCOLTCNTL1_ESCOEDRRX1_Msk  (0x1UL << BT_MAC_ESCOLTCNTL1_ESCOEDRRX1_Pos)
#define BT_MAC_ESCOLTCNTL1_ESCOEDRRX1   BT_MAC_ESCOLTCNTL1_ESCOEDRRX1_Msk
#define BT_MAC_ESCOLTCNTL1_RETXNB1_Pos  (16U)
#define BT_MAC_ESCOLTCNTL1_RETXNB1_Msk  (0xFFUL << BT_MAC_ESCOLTCNTL1_RETXNB1_Pos)
#define BT_MAC_ESCOLTCNTL1_RETXNB1      BT_MAC_ESCOLTCNTL1_RETXNB1_Msk

/*************** Bit definition for BT_MAC_ESCOTRCNTL1 register ***************/
#define BT_MAC_ESCOTRCNTL1_RXTYPE1_Pos  (0U)
#define BT_MAC_ESCOTRCNTL1_RXTYPE1_Msk  (0xFUL << BT_MAC_ESCOTRCNTL1_RXTYPE1_Pos)
#define BT_MAC_ESCOTRCNTL1_RXTYPE1      BT_MAC_ESCOTRCNTL1_RXTYPE1_Msk
#define BT_MAC_ESCOTRCNTL1_RXLEN1_Pos   (4U)
#define BT_MAC_ESCOTRCNTL1_RXLEN1_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL1_RXLEN1_Pos)
#define BT_MAC_ESCOTRCNTL1_RXLEN1       BT_MAC_ESCOTRCNTL1_RXLEN1_Msk
#define BT_MAC_ESCOTRCNTL1_TXTYPE1_Pos  (16U)
#define BT_MAC_ESCOTRCNTL1_TXTYPE1_Msk  (0xFUL << BT_MAC_ESCOTRCNTL1_TXTYPE1_Pos)
#define BT_MAC_ESCOTRCNTL1_TXTYPE1      BT_MAC_ESCOTRCNTL1_TXTYPE1_Msk
#define BT_MAC_ESCOTRCNTL1_TXLEN1_Pos   (20U)
#define BT_MAC_ESCOTRCNTL1_TXLEN1_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL1_TXLEN1_Pos)
#define BT_MAC_ESCOTRCNTL1_TXLEN1       BT_MAC_ESCOTRCNTL1_TXLEN1_Msk
#define BT_MAC_ESCOTRCNTL1_TXSEQN1_Pos  (31U)
#define BT_MAC_ESCOTRCNTL1_TXSEQN1_Msk  (0x1UL << BT_MAC_ESCOTRCNTL1_TXSEQN1_Pos)
#define BT_MAC_ESCOTRCNTL1_TXSEQN1      BT_MAC_ESCOTRCNTL1_TXSEQN1_Msk

/*************** Bit definition for BT_MAC_ESCODAYCNT1 register ***************/
#define BT_MAC_ESCODAYCNT1_DAYCONTER1_Pos  (0U)
#define BT_MAC_ESCODAYCNT1_DAYCONTER1_Msk  (0x7FFUL << BT_MAC_ESCODAYCNT1_DAYCONTER1_Pos)
#define BT_MAC_ESCODAYCNT1_DAYCONTER1   BT_MAC_ESCODAYCNT1_DAYCONTER1_Msk

/************** Bit definition for BT_MAC_ESCOCHANCNTL2 register **************/
#define BT_MAC_ESCOCHANCNTL2_TESCO2_Pos  (0U)
#define BT_MAC_ESCOCHANCNTL2_TESCO2_Msk  (0xFFUL << BT_MAC_ESCOCHANCNTL2_TESCO2_Pos)
#define BT_MAC_ESCOCHANCNTL2_TESCO2     BT_MAC_ESCOCHANCNTL2_TESCO2_Msk
#define BT_MAC_ESCOCHANCNTL2_INTDELAY2_Pos  (8U)
#define BT_MAC_ESCOCHANCNTL2_INTDELAY2_Msk  (0x1FUL << BT_MAC_ESCOCHANCNTL2_INTDELAY2_Pos)
#define BT_MAC_ESCOCHANCNTL2_INTDELAY2  BT_MAC_ESCOCHANCNTL2_INTDELAY2_Msk
#define BT_MAC_ESCOCHANCNTL2_ITMODE2_Pos  (13U)
#define BT_MAC_ESCOCHANCNTL2_ITMODE2_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL2_ITMODE2_Pos)
#define BT_MAC_ESCOCHANCNTL2_ITMODE2    BT_MAC_ESCOCHANCNTL2_ITMODE2_Msk
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANEN2_Pos  (14U)
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANEN2_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL2_ESCOCHANEN2_Pos)
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANEN2  BT_MAC_ESCOCHANCNTL2_ESCOCHANEN2_Msk
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANSWEN2_Pos  (15U)
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANSWEN2_Msk  (0x1UL << BT_MAC_ESCOCHANCNTL2_ESCOCHANSWEN2_Pos)
#define BT_MAC_ESCOCHANCNTL2_ESCOCHANSWEN2  BT_MAC_ESCOCHANCNTL2_ESCOCHANSWEN2_Msk
#define BT_MAC_ESCOCHANCNTL2_TOG2_Pos   (31U)
#define BT_MAC_ESCOCHANCNTL2_TOG2_Msk   (0x1UL << BT_MAC_ESCOCHANCNTL2_TOG2_Pos)
#define BT_MAC_ESCOCHANCNTL2_TOG2       BT_MAC_ESCOCHANCNTL2_TOG2_Msk

/************** Bit definition for BT_MAC_ESCOMUTECNTL2 register **************/
#define BT_MAC_ESCOMUTECNTL2_MUTEPATT2_Pos  (0U)
#define BT_MAC_ESCOMUTECNTL2_MUTEPATT2_Msk  (0xFFFFUL << BT_MAC_ESCOMUTECNTL2_MUTEPATT2_Pos)
#define BT_MAC_ESCOMUTECNTL2_MUTEPATT2  BT_MAC_ESCOMUTECNTL2_MUTEPATT2_Msk
#define BT_MAC_ESCOMUTECNTL2_INVL2_0_Pos  (16U)
#define BT_MAC_ESCOMUTECNTL2_INVL2_0_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL2_INVL2_0_Pos)
#define BT_MAC_ESCOMUTECNTL2_INVL2_0    BT_MAC_ESCOMUTECNTL2_INVL2_0_Msk
#define BT_MAC_ESCOMUTECNTL2_INVL2_1_Pos  (18U)
#define BT_MAC_ESCOMUTECNTL2_INVL2_1_Msk  (0x3UL << BT_MAC_ESCOMUTECNTL2_INVL2_1_Pos)
#define BT_MAC_ESCOMUTECNTL2_INVL2_1    BT_MAC_ESCOMUTECNTL2_INVL2_1_Msk
#define BT_MAC_ESCOMUTECNTL2_MUTE_SOURCE2_Pos  (22U)
#define BT_MAC_ESCOMUTECNTL2_MUTE_SOURCE2_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL2_MUTE_SOURCE2_Pos)
#define BT_MAC_ESCOMUTECNTL2_MUTE_SOURCE2  BT_MAC_ESCOMUTECNTL2_MUTE_SOURCE2_Msk
#define BT_MAC_ESCOMUTECNTL2_MUTE_SINK2_Pos  (23U)
#define BT_MAC_ESCOMUTECNTL2_MUTE_SINK2_Msk  (0x1UL << BT_MAC_ESCOMUTECNTL2_MUTE_SINK2_Pos)
#define BT_MAC_ESCOMUTECNTL2_MUTE_SINK2  BT_MAC_ESCOMUTECNTL2_MUTE_SINK2_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTTXPTR2 register ************/
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX0_Pos)
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX0  BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX0_Msk
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX1_Pos)
#define BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX1  BT_MAC_ESCOCURRENTTXPTR2_ESCO2PTRTX1_Msk

/************ Bit definition for BT_MAC_ESCOCURRENTRXPTR2 register ************/
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX0_Pos  (0U)
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX0_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX0_Pos)
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX0  BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX0_Msk
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX1_Pos  (16U)
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX1_Msk  (0xFFFFUL << BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX1_Pos)
#define BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX1  BT_MAC_ESCOCURRENTRXPTR2_ESCO2PTRRX1_Msk

/*************** Bit definition for BT_MAC_ESCOLTCNTL2 register ***************/
#define BT_MAC_ESCOLTCNTL2_SYNLTADDR2_Pos  (0U)
#define BT_MAC_ESCOLTCNTL2_SYNLTADDR2_Msk  (0x7UL << BT_MAC_ESCOLTCNTL2_SYNLTADDR2_Pos)
#define BT_MAC_ESCOLTCNTL2_SYNLTADDR2   BT_MAC_ESCOLTCNTL2_SYNLTADDR2_Msk
#define BT_MAC_ESCOLTCNTL2_SYNTYPE2_Pos  (3U)
#define BT_MAC_ESCOLTCNTL2_SYNTYPE2_Msk  (0x1UL << BT_MAC_ESCOLTCNTL2_SYNTYPE2_Pos)
#define BT_MAC_ESCOLTCNTL2_SYNTYPE2     BT_MAC_ESCOLTCNTL2_SYNTYPE2_Msk
#define BT_MAC_ESCOLTCNTL2_ESCOEDRTX2_Pos  (4U)
#define BT_MAC_ESCOLTCNTL2_ESCOEDRTX2_Msk  (0x1UL << BT_MAC_ESCOLTCNTL2_ESCOEDRTX2_Pos)
#define BT_MAC_ESCOLTCNTL2_ESCOEDRTX2   BT_MAC_ESCOLTCNTL2_ESCOEDRTX2_Msk
#define BT_MAC_ESCOLTCNTL2_ESCOEDRRX2_Pos  (5U)
#define BT_MAC_ESCOLTCNTL2_ESCOEDRRX2_Msk  (0x1UL << BT_MAC_ESCOLTCNTL2_ESCOEDRRX2_Pos)
#define BT_MAC_ESCOLTCNTL2_ESCOEDRRX2   BT_MAC_ESCOLTCNTL2_ESCOEDRRX2_Msk
#define BT_MAC_ESCOLTCNTL2_RETXNB2_Pos  (16U)
#define BT_MAC_ESCOLTCNTL2_RETXNB2_Msk  (0xFFUL << BT_MAC_ESCOLTCNTL2_RETXNB2_Pos)
#define BT_MAC_ESCOLTCNTL2_RETXNB2      BT_MAC_ESCOLTCNTL2_RETXNB2_Msk

/*************** Bit definition for BT_MAC_ESCOTRCNTL2 register ***************/
#define BT_MAC_ESCOTRCNTL2_RXTYPE2_Pos  (0U)
#define BT_MAC_ESCOTRCNTL2_RXTYPE2_Msk  (0xFUL << BT_MAC_ESCOTRCNTL2_RXTYPE2_Pos)
#define BT_MAC_ESCOTRCNTL2_RXTYPE2      BT_MAC_ESCOTRCNTL2_RXTYPE2_Msk
#define BT_MAC_ESCOTRCNTL2_RXLEN2_Pos   (4U)
#define BT_MAC_ESCOTRCNTL2_RXLEN2_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL2_RXLEN2_Pos)
#define BT_MAC_ESCOTRCNTL2_RXLEN2       BT_MAC_ESCOTRCNTL2_RXLEN2_Msk
#define BT_MAC_ESCOTRCNTL2_TXTYPE2_Pos  (16U)
#define BT_MAC_ESCOTRCNTL2_TXTYPE2_Msk  (0xFUL << BT_MAC_ESCOTRCNTL2_TXTYPE2_Pos)
#define BT_MAC_ESCOTRCNTL2_TXTYPE2      BT_MAC_ESCOTRCNTL2_TXTYPE2_Msk
#define BT_MAC_ESCOTRCNTL2_TXLEN2_Pos   (20U)
#define BT_MAC_ESCOTRCNTL2_TXLEN2_Msk   (0x3FFUL << BT_MAC_ESCOTRCNTL2_TXLEN2_Pos)
#define BT_MAC_ESCOTRCNTL2_TXLEN2       BT_MAC_ESCOTRCNTL2_TXLEN2_Msk
#define BT_MAC_ESCOTRCNTL2_TXSEQN2_Pos  (31U)
#define BT_MAC_ESCOTRCNTL2_TXSEQN2_Msk  (0x1UL << BT_MAC_ESCOTRCNTL2_TXSEQN2_Pos)
#define BT_MAC_ESCOTRCNTL2_TXSEQN2      BT_MAC_ESCOTRCNTL2_TXSEQN2_Msk

/*************** Bit definition for BT_MAC_ESCODAYCNT2 register ***************/
#define BT_MAC_ESCODAYCNT2_DAYCONTER2_Pos  (0U)
#define BT_MAC_ESCODAYCNT2_DAYCONTER2_Msk  (0x7FFUL << BT_MAC_ESCODAYCNT2_DAYCONTER2_Pos)
#define BT_MAC_ESCODAYCNT2_DAYCONTER2   BT_MAC_ESCODAYCNT2_DAYCONTER2_Msk

/*************** Bit definition for BT_MAC_AUDIOCNTL0 register ****************/
#define BT_MAC_AUDIOCNTL0_CVSD_BITODER0_Pos  (0U)
#define BT_MAC_AUDIOCNTL0_CVSD_BITODER0_Msk  (0x1UL << BT_MAC_AUDIOCNTL0_CVSD_BITODER0_Pos)
#define BT_MAC_AUDIOCNTL0_CVSD_BITODER0  BT_MAC_AUDIOCNTL0_CVSD_BITODER0_Msk
#define BT_MAC_AUDIOCNTL0_CVSDEN0_Pos   (7U)
#define BT_MAC_AUDIOCNTL0_CVSDEN0_Msk   (0x1UL << BT_MAC_AUDIOCNTL0_CVSDEN0_Pos)
#define BT_MAC_AUDIOCNTL0_CVSDEN0       BT_MAC_AUDIOCNTL0_CVSDEN0_Msk
#define BT_MAC_AUDIOCNTL0_AULAW_CODE0_Pos  (8U)
#define BT_MAC_AUDIOCNTL0_AULAW_CODE0_Msk  (0xFUL << BT_MAC_AUDIOCNTL0_AULAW_CODE0_Pos)
#define BT_MAC_AUDIOCNTL0_AULAW_CODE0   BT_MAC_AUDIOCNTL0_AULAW_CODE0_Msk
#define BT_MAC_AUDIOCNTL0_AULAWEN0_Pos  (15U)
#define BT_MAC_AUDIOCNTL0_AULAWEN0_Msk  (0x1UL << BT_MAC_AUDIOCNTL0_AULAWEN0_Pos)
#define BT_MAC_AUDIOCNTL0_AULAWEN0      BT_MAC_AUDIOCNTL0_AULAWEN0_Msk
#define BT_MAC_AUDIOCNTL0_SAMPLE_TYPE0_Pos  (16U)
#define BT_MAC_AUDIOCNTL0_SAMPLE_TYPE0_Msk  (0x3UL << BT_MAC_AUDIOCNTL0_SAMPLE_TYPE0_Pos)
#define BT_MAC_AUDIOCNTL0_SAMPLE_TYPE0  BT_MAC_AUDIOCNTL0_SAMPLE_TYPE0_Msk
#define BT_MAC_AUDIOCNTL0_LINEAR_FORMAT0_Pos  (20U)
#define BT_MAC_AUDIOCNTL0_LINEAR_FORMAT0_Msk  (0x3UL << BT_MAC_AUDIOCNTL0_LINEAR_FORMAT0_Pos)
#define BT_MAC_AUDIOCNTL0_LINEAR_FORMAT0  BT_MAC_AUDIOCNTL0_LINEAR_FORMAT0_Msk

/*************** Bit definition for BT_MAC_AUDIOCNTL1 register ****************/
#define BT_MAC_AUDIOCNTL1_CVSD_BITODER1_Pos  (0U)
#define BT_MAC_AUDIOCNTL1_CVSD_BITODER1_Msk  (0x1UL << BT_MAC_AUDIOCNTL1_CVSD_BITODER1_Pos)
#define BT_MAC_AUDIOCNTL1_CVSD_BITODER1  BT_MAC_AUDIOCNTL1_CVSD_BITODER1_Msk
#define BT_MAC_AUDIOCNTL1_CVSDEN1_Pos   (7U)
#define BT_MAC_AUDIOCNTL1_CVSDEN1_Msk   (0x1UL << BT_MAC_AUDIOCNTL1_CVSDEN1_Pos)
#define BT_MAC_AUDIOCNTL1_CVSDEN1       BT_MAC_AUDIOCNTL1_CVSDEN1_Msk
#define BT_MAC_AUDIOCNTL1_AULAW_CODE1_Pos  (8U)
#define BT_MAC_AUDIOCNTL1_AULAW_CODE1_Msk  (0xFUL << BT_MAC_AUDIOCNTL1_AULAW_CODE1_Pos)
#define BT_MAC_AUDIOCNTL1_AULAW_CODE1   BT_MAC_AUDIOCNTL1_AULAW_CODE1_Msk
#define BT_MAC_AUDIOCNTL1_AULAWEN1_Pos  (15U)
#define BT_MAC_AUDIOCNTL1_AULAWEN1_Msk  (0x1UL << BT_MAC_AUDIOCNTL1_AULAWEN1_Pos)
#define BT_MAC_AUDIOCNTL1_AULAWEN1      BT_MAC_AUDIOCNTL1_AULAWEN1_Msk
#define BT_MAC_AUDIOCNTL1_SAMPLE_TYPE1_Pos  (16U)
#define BT_MAC_AUDIOCNTL1_SAMPLE_TYPE1_Msk  (0x3UL << BT_MAC_AUDIOCNTL1_SAMPLE_TYPE1_Pos)
#define BT_MAC_AUDIOCNTL1_SAMPLE_TYPE1  BT_MAC_AUDIOCNTL1_SAMPLE_TYPE1_Msk
#define BT_MAC_AUDIOCNTL1_LINEAR_FORMAT1_Pos  (20U)
#define BT_MAC_AUDIOCNTL1_LINEAR_FORMAT1_Msk  (0x3UL << BT_MAC_AUDIOCNTL1_LINEAR_FORMAT1_Pos)
#define BT_MAC_AUDIOCNTL1_LINEAR_FORMAT1  BT_MAC_AUDIOCNTL1_LINEAR_FORMAT1_Msk

/*************** Bit definition for BT_MAC_AUDIOCNTL2 register ****************/
#define BT_MAC_AUDIOCNTL2_CVSD_BITODER2_Pos  (0U)
#define BT_MAC_AUDIOCNTL2_CVSD_BITODER2_Msk  (0x1UL << BT_MAC_AUDIOCNTL2_CVSD_BITODER2_Pos)
#define BT_MAC_AUDIOCNTL2_CVSD_BITODER2  BT_MAC_AUDIOCNTL2_CVSD_BITODER2_Msk
#define BT_MAC_AUDIOCNTL2_CVSDEN2_Pos   (7U)
#define BT_MAC_AUDIOCNTL2_CVSDEN2_Msk   (0x1UL << BT_MAC_AUDIOCNTL2_CVSDEN2_Pos)
#define BT_MAC_AUDIOCNTL2_CVSDEN2       BT_MAC_AUDIOCNTL2_CVSDEN2_Msk
#define BT_MAC_AUDIOCNTL2_AULAW_CODE2_Pos  (8U)
#define BT_MAC_AUDIOCNTL2_AULAW_CODE2_Msk  (0xFUL << BT_MAC_AUDIOCNTL2_AULAW_CODE2_Pos)
#define BT_MAC_AUDIOCNTL2_AULAW_CODE2   BT_MAC_AUDIOCNTL2_AULAW_CODE2_Msk
#define BT_MAC_AUDIOCNTL2_AULAWEN2_Pos  (15U)
#define BT_MAC_AUDIOCNTL2_AULAWEN2_Msk  (0x1UL << BT_MAC_AUDIOCNTL2_AULAWEN2_Pos)
#define BT_MAC_AUDIOCNTL2_AULAWEN2      BT_MAC_AUDIOCNTL2_AULAWEN2_Msk
#define BT_MAC_AUDIOCNTL2_SAMPLE_TYPE2_Pos  (16U)
#define BT_MAC_AUDIOCNTL2_SAMPLE_TYPE2_Msk  (0x3UL << BT_MAC_AUDIOCNTL2_SAMPLE_TYPE2_Pos)
#define BT_MAC_AUDIOCNTL2_SAMPLE_TYPE2  BT_MAC_AUDIOCNTL2_SAMPLE_TYPE2_Msk
#define BT_MAC_AUDIOCNTL2_LINEAR_FORMAT2_Pos  (20U)
#define BT_MAC_AUDIOCNTL2_LINEAR_FORMAT2_Msk  (0x3UL << BT_MAC_AUDIOCNTL2_LINEAR_FORMAT2_Pos)
#define BT_MAC_AUDIOCNTL2_LINEAR_FORMAT2  BT_MAC_AUDIOCNTL2_LINEAR_FORMAT2_Msk

/**************** Bit definition for BT_MAC_RWBLECNTL register ****************/
#define BT_MAC_RWBLECNTL_RXWINSZDEF_Pos  (0U)
#define BT_MAC_RWBLECNTL_RXWINSZDEF_Msk  (0xFUL << BT_MAC_RWBLECNTL_RXWINSZDEF_Pos)
#define BT_MAC_RWBLECNTL_RXWINSZDEF     BT_MAC_RWBLECNTL_RXWINSZDEF_Msk
#define BT_MAC_RWBLECNTL_RWBLE_EN_Pos   (8U)
#define BT_MAC_RWBLECNTL_RWBLE_EN_Msk   (0x1UL << BT_MAC_RWBLECNTL_RWBLE_EN_Pos)
#define BT_MAC_RWBLECNTL_RWBLE_EN       BT_MAC_RWBLECNTL_RWBLE_EN_Msk
#define BT_MAC_RWBLECNTL_ADVERTFILT_EN_Pos  (9U)
#define BT_MAC_RWBLECNTL_ADVERTFILT_EN_Msk  (0x1UL << BT_MAC_RWBLECNTL_ADVERTFILT_EN_Pos)
#define BT_MAC_RWBLECNTL_ADVERTFILT_EN  BT_MAC_RWBLECNTL_ADVERTFILT_EN_Msk
#define BT_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Pos  (10U)
#define BT_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Msk  (0x1UL << BT_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Pos)
#define BT_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN  BT_MAC_RWBLECNTL_ANONYMOUS_ADV_FILT_EN_Msk
#define BT_MAC_RWBLECNTL_RXCTEERR_RETY_EN_Pos  (11U)
#define BT_MAC_RWBLECNTL_RXCTEERR_RETY_EN_Msk  (0x1UL << BT_MAC_RWBLECNTL_RXCTEERR_RETY_EN_Pos)
#define BT_MAC_RWBLECNTL_RXCTEERR_RETY_EN  BT_MAC_RWBLECNTL_RXCTEERR_RETY_EN_Msk
#define BT_MAC_RWBLECNTL_HOP_REMAP_DSB_Pos  (12U)
#define BT_MAC_RWBLECNTL_HOP_REMAP_DSB_Msk  (0x1UL << BT_MAC_RWBLECNTL_HOP_REMAP_DSB_Pos)
#define BT_MAC_RWBLECNTL_HOP_REMAP_DSB  BT_MAC_RWBLECNTL_HOP_REMAP_DSB_Msk
#define BT_MAC_RWBLECNTL_CRC_DSB_Pos    (13U)
#define BT_MAC_RWBLECNTL_CRC_DSB_Msk    (0x1UL << BT_MAC_RWBLECNTL_CRC_DSB_Pos)
#define BT_MAC_RWBLECNTL_CRC_DSB        BT_MAC_RWBLECNTL_CRC_DSB_Msk
#define BT_MAC_RWBLECNTL_WHIT_DSB_Pos   (14U)
#define BT_MAC_RWBLECNTL_WHIT_DSB_Msk   (0x1UL << BT_MAC_RWBLECNTL_WHIT_DSB_Pos)
#define BT_MAC_RWBLECNTL_WHIT_DSB       BT_MAC_RWBLECNTL_WHIT_DSB_Msk
#define BT_MAC_RWBLECNTL_LRFEC_DSB_Pos  (15U)
#define BT_MAC_RWBLECNTL_LRFEC_DSB_Msk  (0x1UL << BT_MAC_RWBLECNTL_LRFEC_DSB_Pos)
#define BT_MAC_RWBLECNTL_LRFEC_DSB      BT_MAC_RWBLECNTL_LRFEC_DSB_Msk
#define BT_MAC_RWBLECNTL_LRPMAP_DSB_Pos  (16U)
#define BT_MAC_RWBLECNTL_LRPMAP_DSB_Msk  (0x1UL << BT_MAC_RWBLECNTL_LRPMAP_DSB_Pos)
#define BT_MAC_RWBLECNTL_LRPMAP_DSB     BT_MAC_RWBLECNTL_LRPMAP_DSB_Msk
#define BT_MAC_RWBLECNTL_CRYPT_DSB_Pos  (17U)
#define BT_MAC_RWBLECNTL_CRYPT_DSB_Msk  (0x1UL << BT_MAC_RWBLECNTL_CRYPT_DSB_Pos)
#define BT_MAC_RWBLECNTL_CRYPT_DSB      BT_MAC_RWBLECNTL_CRYPT_DSB_Msk
#define BT_MAC_RWBLECNTL_NESN_DSB_Pos   (18U)
#define BT_MAC_RWBLECNTL_NESN_DSB_Msk   (0x1UL << BT_MAC_RWBLECNTL_NESN_DSB_Pos)
#define BT_MAC_RWBLECNTL_NESN_DSB       BT_MAC_RWBLECNTL_NESN_DSB_Msk
#define BT_MAC_RWBLECNTL_SN_DSB_Pos     (19U)
#define BT_MAC_RWBLECNTL_SN_DSB_Msk     (0x1UL << BT_MAC_RWBLECNTL_SN_DSB_Pos)
#define BT_MAC_RWBLECNTL_SN_DSB         BT_MAC_RWBLECNTL_SN_DSB_Msk
#define BT_MAC_RWBLECNTL_MD_DSB_Pos     (20U)
#define BT_MAC_RWBLECNTL_MD_DSB_Msk     (0x1UL << BT_MAC_RWBLECNTL_MD_DSB_Pos)
#define BT_MAC_RWBLECNTL_MD_DSB         BT_MAC_RWBLECNTL_MD_DSB_Msk
#define BT_MAC_RWBLECNTL_NPI_DSB_Pos    (21U)
#define BT_MAC_RWBLECNTL_NPI_DSB_Msk    (0x1UL << BT_MAC_RWBLECNTL_NPI_DSB_Pos)
#define BT_MAC_RWBLECNTL_NPI_DSB        BT_MAC_RWBLECNTL_NPI_DSB_Msk
#define BT_MAC_RWBLECNTL_CIE_DSB_Pos    (22U)
#define BT_MAC_RWBLECNTL_CIE_DSB_Msk    (0x1UL << BT_MAC_RWBLECNTL_CIE_DSB_Pos)
#define BT_MAC_RWBLECNTL_CIE_DSB        BT_MAC_RWBLECNTL_CIE_DSB_Msk
#define BT_MAC_RWBLECNTL_SCAN_ABORT_Pos  (24U)
#define BT_MAC_RWBLECNTL_SCAN_ABORT_Msk  (0x1UL << BT_MAC_RWBLECNTL_SCAN_ABORT_Pos)
#define BT_MAC_RWBLECNTL_SCAN_ABORT     BT_MAC_RWBLECNTL_SCAN_ABORT_Msk
#define BT_MAC_RWBLECNTL_ADVERT_ABORT_Pos  (25U)
#define BT_MAC_RWBLECNTL_ADVERT_ABORT_Msk  (0x1UL << BT_MAC_RWBLECNTL_ADVERT_ABORT_Pos)
#define BT_MAC_RWBLECNTL_ADVERT_ABORT   BT_MAC_RWBLECNTL_ADVERT_ABORT_Msk
#define BT_MAC_RWBLECNTL_RFTEST_ABORT_Pos  (26U)
#define BT_MAC_RWBLECNTL_RFTEST_ABORT_Msk  (0x1UL << BT_MAC_RWBLECNTL_RFTEST_ABORT_Pos)
#define BT_MAC_RWBLECNTL_RFTEST_ABORT   BT_MAC_RWBLECNTL_RFTEST_ABORT_Msk
#define BT_MAC_RWBLECNTL_MASTER_SOFT_RST_Pos  (31U)
#define BT_MAC_RWBLECNTL_MASTER_SOFT_RST_Msk  (0x1UL << BT_MAC_RWBLECNTL_MASTER_SOFT_RST_Pos)
#define BT_MAC_RWBLECNTL_MASTER_SOFT_RST  BT_MAC_RWBLECNTL_MASTER_SOFT_RST_Msk

/*************** Bit definition for BT_MAC_BLEVERSION register ****************/
#define BT_MAC_BLEVERSION_BUILD_NUM_Pos  (0U)
#define BT_MAC_BLEVERSION_BUILD_NUM_Msk  (0xFFUL << BT_MAC_BLEVERSION_BUILD_NUM_Pos)
#define BT_MAC_BLEVERSION_BUILD_NUM     BT_MAC_BLEVERSION_BUILD_NUM_Msk
#define BT_MAC_BLEVERSION_UPG_Pos       (8U)
#define BT_MAC_BLEVERSION_UPG_Msk       (0xFFUL << BT_MAC_BLEVERSION_UPG_Pos)
#define BT_MAC_BLEVERSION_UPG           BT_MAC_BLEVERSION_UPG_Msk
#define BT_MAC_BLEVERSION_REL_Pos       (16U)
#define BT_MAC_BLEVERSION_REL_Msk       (0xFFUL << BT_MAC_BLEVERSION_REL_Pos)
#define BT_MAC_BLEVERSION_REL           BT_MAC_BLEVERSION_REL_Msk
#define BT_MAC_BLEVERSION_TYP_Pos       (24U)
#define BT_MAC_BLEVERSION_TYP_Msk       (0xFFUL << BT_MAC_BLEVERSION_TYP_Pos)
#define BT_MAC_BLEVERSION_TYP           BT_MAC_BLEVERSION_TYP_Msk

/*************** Bit definition for BT_MAC_BLEINTCNTL0 register ***************/
#define BT_MAC_BLEINTCNTL0_STARTEVTINTMSK_Pos  (0U)
#define BT_MAC_BLEINTCNTL0_STARTEVTINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_STARTEVTINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_STARTEVTINTMSK  BT_MAC_BLEINTCNTL0_STARTEVTINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_ENDEVTINTMSK_Pos  (1U)
#define BT_MAC_BLEINTCNTL0_ENDEVTINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_ENDEVTINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_ENDEVTINTMSK  BT_MAC_BLEINTCNTL0_ENDEVTINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_SKIPEVTINTMSK_Pos  (2U)
#define BT_MAC_BLEINTCNTL0_SKIPEVTINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_SKIPEVTINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_SKIPEVTINTMSK  BT_MAC_BLEINTCNTL0_SKIPEVTINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_TXINTMSK_Pos  (3U)
#define BT_MAC_BLEINTCNTL0_TXINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_TXINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_TXINTMSK     BT_MAC_BLEINTCNTL0_TXINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_RXINTMSK_Pos  (4U)
#define BT_MAC_BLEINTCNTL0_RXINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_RXINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_RXINTMSK     BT_MAC_BLEINTCNTL0_RXINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_ISOTXINTMSK_Pos  (5U)
#define BT_MAC_BLEINTCNTL0_ISOTXINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_ISOTXINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_ISOTXINTMSK  BT_MAC_BLEINTCNTL0_ISOTXINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_ISORXINTMSK_Pos  (6U)
#define BT_MAC_BLEINTCNTL0_ISORXINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_ISORXINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_ISORXINTMSK  BT_MAC_BLEINTCNTL0_ISORXINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_HOPINTMSK_Pos  (7U)
#define BT_MAC_BLEINTCNTL0_HOPINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_HOPINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_HOPINTMSK    BT_MAC_BLEINTCNTL0_HOPINTMSK_Msk
#define BT_MAC_BLEINTCNTL0_ERRORINTMSK_Pos  (16U)
#define BT_MAC_BLEINTCNTL0_ERRORINTMSK_Msk  (0x1UL << BT_MAC_BLEINTCNTL0_ERRORINTMSK_Pos)
#define BT_MAC_BLEINTCNTL0_ERRORINTMSK  BT_MAC_BLEINTCNTL0_ERRORINTMSK_Msk

/*************** Bit definition for BT_MAC_BLEINTSTAT0 register ***************/
#define BT_MAC_BLEINTSTAT0_HOPINTSTAT_Pos  (7U)
#define BT_MAC_BLEINTSTAT0_HOPINTSTAT_Msk  (0x1UL << BT_MAC_BLEINTSTAT0_HOPINTSTAT_Pos)
#define BT_MAC_BLEINTSTAT0_HOPINTSTAT   BT_MAC_BLEINTSTAT0_HOPINTSTAT_Msk
#define BT_MAC_BLEINTSTAT0_ERRORINTSTAT_Pos  (16U)
#define BT_MAC_BLEINTSTAT0_ERRORINTSTAT_Msk  (0x1UL << BT_MAC_BLEINTSTAT0_ERRORINTSTAT_Pos)
#define BT_MAC_BLEINTSTAT0_ERRORINTSTAT  BT_MAC_BLEINTSTAT0_ERRORINTSTAT_Msk

/*************** Bit definition for BT_MAC_BLEINTACK0 register ****************/
#define BT_MAC_BLEINTACK0_HOPINTACK_Pos  (7U)
#define BT_MAC_BLEINTACK0_HOPINTACK_Msk  (0x1UL << BT_MAC_BLEINTACK0_HOPINTACK_Pos)
#define BT_MAC_BLEINTACK0_HOPINTACK     BT_MAC_BLEINTACK0_HOPINTACK_Msk
#define BT_MAC_BLEINTACK0_ERRORINTACK_Pos  (16U)
#define BT_MAC_BLEINTACK0_ERRORINTACK_Msk  (0x1UL << BT_MAC_BLEINTACK0_ERRORINTACK_Pos)
#define BT_MAC_BLEINTACK0_ERRORINTACK   BT_MAC_BLEINTACK0_ERRORINTACK_Msk

/*********** Bit definition for BT_MAC_BLECURRENTRXDESCPTR register ***********/
#define BT_MAC_BLECURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos  (0U)
#define BT_MAC_BLECURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk  (0x3FFFUL << BT_MAC_BLECURRENTRXDESCPTR_CURRENTRXDESCPTR_Pos)
#define BT_MAC_BLECURRENTRXDESCPTR_CURRENTRXDESCPTR  BT_MAC_BLECURRENTRXDESCPTR_CURRENTRXDESCPTR_Msk

/*************** Bit definition for BT_MAC_BLEDIAGCNTL register ***************/
#define BT_MAC_BLEDIAGCNTL_DIAG0_Pos    (0U)
#define BT_MAC_BLEDIAGCNTL_DIAG0_Msk    (0x7FUL << BT_MAC_BLEDIAGCNTL_DIAG0_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG0        BT_MAC_BLEDIAGCNTL_DIAG0_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG0_EN_Pos  (7U)
#define BT_MAC_BLEDIAGCNTL_DIAG0_EN_Msk  (0x1UL << BT_MAC_BLEDIAGCNTL_DIAG0_EN_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG0_EN     BT_MAC_BLEDIAGCNTL_DIAG0_EN_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG1_Pos    (8U)
#define BT_MAC_BLEDIAGCNTL_DIAG1_Msk    (0x7FUL << BT_MAC_BLEDIAGCNTL_DIAG1_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG1        BT_MAC_BLEDIAGCNTL_DIAG1_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG1_EN_Pos  (15U)
#define BT_MAC_BLEDIAGCNTL_DIAG1_EN_Msk  (0x1UL << BT_MAC_BLEDIAGCNTL_DIAG1_EN_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG1_EN     BT_MAC_BLEDIAGCNTL_DIAG1_EN_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG2_Pos    (16U)
#define BT_MAC_BLEDIAGCNTL_DIAG2_Msk    (0x7FUL << BT_MAC_BLEDIAGCNTL_DIAG2_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG2        BT_MAC_BLEDIAGCNTL_DIAG2_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG2_EN_Pos  (23U)
#define BT_MAC_BLEDIAGCNTL_DIAG2_EN_Msk  (0x1UL << BT_MAC_BLEDIAGCNTL_DIAG2_EN_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG2_EN     BT_MAC_BLEDIAGCNTL_DIAG2_EN_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG3_Pos    (24U)
#define BT_MAC_BLEDIAGCNTL_DIAG3_Msk    (0x7FUL << BT_MAC_BLEDIAGCNTL_DIAG3_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG3        BT_MAC_BLEDIAGCNTL_DIAG3_Msk
#define BT_MAC_BLEDIAGCNTL_DIAG3_EN_Pos  (31U)
#define BT_MAC_BLEDIAGCNTL_DIAG3_EN_Msk  (0x1UL << BT_MAC_BLEDIAGCNTL_DIAG3_EN_Pos)
#define BT_MAC_BLEDIAGCNTL_DIAG3_EN     BT_MAC_BLEDIAGCNTL_DIAG3_EN_Msk

/*************** Bit definition for BT_MAC_BLEDIAGSTAT register ***************/
#define BT_MAC_BLEDIAGSTAT_DIAG0STAT_Pos  (0U)
#define BT_MAC_BLEDIAGSTAT_DIAG0STAT_Msk  (0xFFUL << BT_MAC_BLEDIAGSTAT_DIAG0STAT_Pos)
#define BT_MAC_BLEDIAGSTAT_DIAG0STAT    BT_MAC_BLEDIAGSTAT_DIAG0STAT_Msk
#define BT_MAC_BLEDIAGSTAT_DIAG1STAT_Pos  (8U)
#define BT_MAC_BLEDIAGSTAT_DIAG1STAT_Msk  (0xFFUL << BT_MAC_BLEDIAGSTAT_DIAG1STAT_Pos)
#define BT_MAC_BLEDIAGSTAT_DIAG1STAT    BT_MAC_BLEDIAGSTAT_DIAG1STAT_Msk
#define BT_MAC_BLEDIAGSTAT_DIAG2STAT_Pos  (16U)
#define BT_MAC_BLEDIAGSTAT_DIAG2STAT_Msk  (0xFFUL << BT_MAC_BLEDIAGSTAT_DIAG2STAT_Pos)
#define BT_MAC_BLEDIAGSTAT_DIAG2STAT    BT_MAC_BLEDIAGSTAT_DIAG2STAT_Msk
#define BT_MAC_BLEDIAGSTAT_DIAG3STAT_Pos  (24U)
#define BT_MAC_BLEDIAGSTAT_DIAG3STAT_Msk  (0xFFUL << BT_MAC_BLEDIAGSTAT_DIAG3STAT_Pos)
#define BT_MAC_BLEDIAGSTAT_DIAG3STAT    BT_MAC_BLEDIAGSTAT_DIAG3STAT_Msk

/************* Bit definition for BT_MAC_BLEDEBUGADDMAX register **************/
#define BT_MAC_BLEDEBUGADDMAX_EM_ADDMAX_Pos  (0U)
#define BT_MAC_BLEDEBUGADDMAX_EM_ADDMAX_Msk  (0xFFFFUL << BT_MAC_BLEDEBUGADDMAX_EM_ADDMAX_Pos)
#define BT_MAC_BLEDEBUGADDMAX_EM_ADDMAX  BT_MAC_BLEDEBUGADDMAX_EM_ADDMAX_Msk
#define BT_MAC_BLEDEBUGADDMAX_REG_ADDMAX_Pos  (16U)
#define BT_MAC_BLEDEBUGADDMAX_REG_ADDMAX_Msk  (0xFFFFUL << BT_MAC_BLEDEBUGADDMAX_REG_ADDMAX_Pos)
#define BT_MAC_BLEDEBUGADDMAX_REG_ADDMAX  BT_MAC_BLEDEBUGADDMAX_REG_ADDMAX_Msk

/************* Bit definition for BT_MAC_BLEDEBUGADDMIN register **************/
#define BT_MAC_BLEDEBUGADDMIN_EM_ADDMIN_Pos  (0U)
#define BT_MAC_BLEDEBUGADDMIN_EM_ADDMIN_Msk  (0xFFFFUL << BT_MAC_BLEDEBUGADDMIN_EM_ADDMIN_Pos)
#define BT_MAC_BLEDEBUGADDMIN_EM_ADDMIN  BT_MAC_BLEDEBUGADDMIN_EM_ADDMIN_Msk
#define BT_MAC_BLEDEBUGADDMIN_REG_ADDMIN_Pos  (16U)
#define BT_MAC_BLEDEBUGADDMIN_REG_ADDMIN_Msk  (0xFFFFUL << BT_MAC_BLEDEBUGADDMIN_REG_ADDMIN_Pos)
#define BT_MAC_BLEDEBUGADDMIN_REG_ADDMIN  BT_MAC_BLEDEBUGADDMIN_REG_ADDMIN_Msk

/************ Bit definition for BT_MAC_BLEERRORTYPESTAT register *************/
#define BT_MAC_BLEERRORTYPESTAT_TXCRYPT_ERROR_Pos  (0U)
#define BT_MAC_BLEERRORTYPESTAT_TXCRYPT_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_TXCRYPT_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_TXCRYPT_ERROR  BT_MAC_BLEERRORTYPESTAT_TXCRYPT_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RXCRYPT_ERROR_Pos  (1U)
#define BT_MAC_BLEERRORTYPESTAT_RXCRYPT_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RXCRYPT_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RXCRYPT_ERROR  BT_MAC_BLEERRORTYPESTAT_RXCRYPT_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos  (2U)
#define BT_MAC_BLEERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_PKTCNTL_EMACC_ERROR  BT_MAC_BLEERRORTYPESTAT_PKTCNTL_EMACC_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RADIO_EMACC_ERROR_Pos  (3U)
#define BT_MAC_BLEERRORTYPESTAT_RADIO_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RADIO_EMACC_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RADIO_EMACC_ERROR  BT_MAC_BLEERRORTYPESTAT_RADIO_EMACC_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos  (4U)
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR  BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_ENTRY_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos  (5U)
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_APFM_ERROR  BT_MAC_BLEERRORTYPESTAT_ACT_SCHDL_APFM_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Pos  (6U)
#define BT_MAC_BLEERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_EVT_CNTL_APFM_ERROR  BT_MAC_BLEERRORTYPESTAT_EVT_CNTL_APFM_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_LIST_ERROR_Pos  (7U)
#define BT_MAC_BLEERRORTYPESTAT_LIST_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_LIST_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_LIST_ERROR  BT_MAC_BLEERRORTYPESTAT_LIST_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_IFS_UNDERRUN_Pos  (8U)
#define BT_MAC_BLEERRORTYPESTAT_IFS_UNDERRUN_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_IFS_UNDERRUN_Pos)
#define BT_MAC_BLEERRORTYPESTAT_IFS_UNDERRUN  BT_MAC_BLEERRORTYPESTAT_IFS_UNDERRUN_Msk
#define BT_MAC_BLEERRORTYPESTAT_ADV_UNDERRUN_Pos  (9U)
#define BT_MAC_BLEERRORTYPESTAT_ADV_UNDERRUN_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_ADV_UNDERRUN_Pos)
#define BT_MAC_BLEERRORTYPESTAT_ADV_UNDERRUN  BT_MAC_BLEERRORTYPESTAT_ADV_UNDERRUN_Msk
#define BT_MAC_BLEERRORTYPESTAT_LLCHMAP_ERROR_Pos  (10U)
#define BT_MAC_BLEERRORTYPESTAT_LLCHMAP_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_LLCHMAP_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_LLCHMAP_ERROR  BT_MAC_BLEERRORTYPESTAT_LLCHMAP_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_CSFORMAT_ERROR_Pos  (11U)
#define BT_MAC_BLEERRORTYPESTAT_CSFORMAT_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_CSFORMAT_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_CSFORMAT_ERROR  BT_MAC_BLEERRORTYPESTAT_CSFORMAT_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos  (12U)
#define BT_MAC_BLEERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_TXDESC_EMPTY_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_TXDESC_EMPTY_ERROR  BT_MAC_BLEERRORTYPESTAT_TXDESC_EMPTY_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos  (13U)
#define BT_MAC_BLEERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RXDESC_EMPTY_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RXDESC_EMPTY_ERROR  BT_MAC_BLEERRORTYPESTAT_RXDESC_EMPTY_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_TXDATA_PTR_ERROR_Pos  (14U)
#define BT_MAC_BLEERRORTYPESTAT_TXDATA_PTR_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_TXDATA_PTR_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_TXDATA_PTR_ERROR  BT_MAC_BLEERRORTYPESTAT_TXDATA_PTR_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RXDATA_PTR_ERROR_Pos  (15U)
#define BT_MAC_BLEERRORTYPESTAT_RXDATA_PTR_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RXDATA_PTR_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RXDATA_PTR_ERROR  BT_MAC_BLEERRORTYPESTAT_RXDATA_PTR_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RAL_ERROR_Pos  (16U)
#define BT_MAC_BLEERRORTYPESTAT_RAL_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RAL_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RAL_ERROR  BT_MAC_BLEERRORTYPESTAT_RAL_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_RAL_UNDERRUN_Pos  (17U)
#define BT_MAC_BLEERRORTYPESTAT_RAL_UNDERRUN_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_RAL_UNDERRUN_Pos)
#define BT_MAC_BLEERRORTYPESTAT_RAL_UNDERRUN  BT_MAC_BLEERRORTYPESTAT_RAL_UNDERRUN_Msk
#define BT_MAC_BLEERRORTYPESTAT_TMAFS_UNDERRUN_Pos  (18U)
#define BT_MAC_BLEERRORTYPESTAT_TMAFS_UNDERRUN_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_TMAFS_UNDERRUN_Pos)
#define BT_MAC_BLEERRORTYPESTAT_TMAFS_UNDERRUN  BT_MAC_BLEERRORTYPESTAT_TMAFS_UNDERRUN_Msk
#define BT_MAC_BLEERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Pos  (19U)
#define BT_MAC_BLEERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_TXAEHEADER_PTR_ERROR  BT_MAC_BLEERRORTYPESTAT_TXAEHEADER_PTR_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_PHY_ERROR_Pos  (20U)
#define BT_MAC_BLEERRORTYPESTAT_PHY_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_PHY_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_PHY_ERROR  BT_MAC_BLEERRORTYPESTAT_PHY_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_FIFOINTOVF_Pos  (21U)
#define BT_MAC_BLEERRORTYPESTAT_FIFOINTOVF_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_FIFOINTOVF_Pos)
#define BT_MAC_BLEERRORTYPESTAT_FIFOINTOVF  BT_MAC_BLEERRORTYPESTAT_FIFOINTOVF_Msk
#define BT_MAC_BLEERRORTYPESTAT_DFCNTL_EMACC_ERROR_Pos  (22U)
#define BT_MAC_BLEERRORTYPESTAT_DFCNTL_EMACC_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_DFCNTL_EMACC_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_DFCNTL_EMACC_ERROR  BT_MAC_BLEERRORTYPESTAT_DFCNTL_EMACC_ERROR_Msk
#define BT_MAC_BLEERRORTYPESTAT_FREQSEL_ERROR_Pos  (23U)
#define BT_MAC_BLEERRORTYPESTAT_FREQSEL_ERROR_Msk  (0x1UL << BT_MAC_BLEERRORTYPESTAT_FREQSEL_ERROR_Pos)
#define BT_MAC_BLEERRORTYPESTAT_FREQSEL_ERROR  BT_MAC_BLEERRORTYPESTAT_FREQSEL_ERROR_Msk

/************* Bit definition for BT_MAC_BLESWPROFILING register **************/
#define BT_MAC_BLESWPROFILING_SWPROF_Pos  (0U)
#define BT_MAC_BLESWPROFILING_SWPROF_Msk  (0xFFFFFFFFUL << BT_MAC_BLESWPROFILING_SWPROF_Pos)
#define BT_MAC_BLESWPROFILING_SWPROF    BT_MAC_BLESWPROFILING_SWPROF_Msk

/************** Bit definition for BT_MAC_BLERADIOCNTL2 register **************/
#define BT_MAC_BLERADIOCNTL2_FREQTABLE_PTR_Pos  (0U)
#define BT_MAC_BLERADIOCNTL2_FREQTABLE_PTR_Msk  (0x3FFFUL << BT_MAC_BLERADIOCNTL2_FREQTABLE_PTR_Pos)
#define BT_MAC_BLERADIOCNTL2_FREQTABLE_PTR  BT_MAC_BLERADIOCNTL2_FREQTABLE_PTR_Msk
#define BT_MAC_BLERADIOCNTL2_PHYMSK_Pos  (22U)
#define BT_MAC_BLERADIOCNTL2_PHYMSK_Msk  (0x3UL << BT_MAC_BLERADIOCNTL2_PHYMSK_Pos)
#define BT_MAC_BLERADIOCNTL2_PHYMSK     BT_MAC_BLERADIOCNTL2_PHYMSK_Msk

/************** Bit definition for BT_MAC_BLERADIOCNTL3 register **************/
#define BT_MAC_BLERADIOCNTL3_TXRATE0CFG_Pos  (8U)
#define BT_MAC_BLERADIOCNTL3_TXRATE0CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_TXRATE0CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_TXRATE0CFG  BT_MAC_BLERADIOCNTL3_TXRATE0CFG_Msk
#define BT_MAC_BLERADIOCNTL3_TXRATE1CFG_Pos  (10U)
#define BT_MAC_BLERADIOCNTL3_TXRATE1CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_TXRATE1CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_TXRATE1CFG  BT_MAC_BLERADIOCNTL3_TXRATE1CFG_Msk
#define BT_MAC_BLERADIOCNTL3_TXRATE2CFG_Pos  (12U)
#define BT_MAC_BLERADIOCNTL3_TXRATE2CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_TXRATE2CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_TXRATE2CFG  BT_MAC_BLERADIOCNTL3_TXRATE2CFG_Msk
#define BT_MAC_BLERADIOCNTL3_TXRATE3CFG_Pos  (14U)
#define BT_MAC_BLERADIOCNTL3_TXRATE3CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_TXRATE3CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_TXRATE3CFG  BT_MAC_BLERADIOCNTL3_TXRATE3CFG_Msk
#define BT_MAC_BLERADIOCNTL3_RXRATE0CFG_Pos  (24U)
#define BT_MAC_BLERADIOCNTL3_RXRATE0CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_RXRATE0CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_RXRATE0CFG  BT_MAC_BLERADIOCNTL3_RXRATE0CFG_Msk
#define BT_MAC_BLERADIOCNTL3_RXRATE1CFG_Pos  (26U)
#define BT_MAC_BLERADIOCNTL3_RXRATE1CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_RXRATE1CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_RXRATE1CFG  BT_MAC_BLERADIOCNTL3_RXRATE1CFG_Msk
#define BT_MAC_BLERADIOCNTL3_RXRATE2CFG_Pos  (28U)
#define BT_MAC_BLERADIOCNTL3_RXRATE2CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_RXRATE2CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_RXRATE2CFG  BT_MAC_BLERADIOCNTL3_RXRATE2CFG_Msk
#define BT_MAC_BLERADIOCNTL3_RXRATE3CFG_Pos  (30U)
#define BT_MAC_BLERADIOCNTL3_RXRATE3CFG_Msk  (0x3UL << BT_MAC_BLERADIOCNTL3_RXRATE3CFG_Pos)
#define BT_MAC_BLERADIOCNTL3_RXRATE3CFG  BT_MAC_BLERADIOCNTL3_RXRATE3CFG_Msk

/************ Bit definition for BT_MAC_BLERADIOPWRUPDN0 register *************/
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRUP0_Pos  (0U)
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRUP0_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN0_TXPWRUP0_Pos)
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRUP0  BT_MAC_BLERADIOPWRUPDN0_TXPWRUP0_Msk
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRDN0_Pos  (8U)
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRDN0_Msk  (0x7FUL << BT_MAC_BLERADIOPWRUPDN0_TXPWRDN0_Pos)
#define BT_MAC_BLERADIOPWRUPDN0_TXPWRDN0  BT_MAC_BLERADIOPWRUPDN0_TXPWRDN0_Msk
#define BT_MAC_BLERADIOPWRUPDN0_RXPWRUP0_Pos  (16U)
#define BT_MAC_BLERADIOPWRUPDN0_RXPWRUP0_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN0_RXPWRUP0_Pos)
#define BT_MAC_BLERADIOPWRUPDN0_RXPWRUP0  BT_MAC_BLERADIOPWRUPDN0_RXPWRUP0_Msk
#define BT_MAC_BLERADIOPWRUPDN0_SYNC_POSITION0_Pos  (24U)
#define BT_MAC_BLERADIOPWRUPDN0_SYNC_POSITION0_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN0_SYNC_POSITION0_Pos)
#define BT_MAC_BLERADIOPWRUPDN0_SYNC_POSITION0  BT_MAC_BLERADIOPWRUPDN0_SYNC_POSITION0_Msk

/************ Bit definition for BT_MAC_BLERADIOPWRUPDN1 register *************/
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRUP1_Pos  (0U)
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRUP1_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN1_TXPWRUP1_Pos)
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRUP1  BT_MAC_BLERADIOPWRUPDN1_TXPWRUP1_Msk
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRDN1_Pos  (8U)
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRDN1_Msk  (0x7FUL << BT_MAC_BLERADIOPWRUPDN1_TXPWRDN1_Pos)
#define BT_MAC_BLERADIOPWRUPDN1_TXPWRDN1  BT_MAC_BLERADIOPWRUPDN1_TXPWRDN1_Msk
#define BT_MAC_BLERADIOPWRUPDN1_RXPWRUP1_Pos  (16U)
#define BT_MAC_BLERADIOPWRUPDN1_RXPWRUP1_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN1_RXPWRUP1_Pos)
#define BT_MAC_BLERADIOPWRUPDN1_RXPWRUP1  BT_MAC_BLERADIOPWRUPDN1_RXPWRUP1_Msk
#define BT_MAC_BLERADIOPWRUPDN1_SYNC_POSITION1_Pos  (24U)
#define BT_MAC_BLERADIOPWRUPDN1_SYNC_POSITION1_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN1_SYNC_POSITION1_Pos)
#define BT_MAC_BLERADIOPWRUPDN1_SYNC_POSITION1  BT_MAC_BLERADIOPWRUPDN1_SYNC_POSITION1_Msk

/************ Bit definition for BT_MAC_BLERADIOPWRUPDN2 register *************/
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRUP2_Pos  (0U)
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRUP2_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN2_TXPWRUP2_Pos)
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRUP2  BT_MAC_BLERADIOPWRUPDN2_TXPWRUP2_Msk
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRDN2_Pos  (8U)
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRDN2_Msk  (0x7FUL << BT_MAC_BLERADIOPWRUPDN2_TXPWRDN2_Pos)
#define BT_MAC_BLERADIOPWRUPDN2_TXPWRDN2  BT_MAC_BLERADIOPWRUPDN2_TXPWRDN2_Msk
#define BT_MAC_BLERADIOPWRUPDN2_RXPWRUP2_Pos  (16U)
#define BT_MAC_BLERADIOPWRUPDN2_RXPWRUP2_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN2_RXPWRUP2_Pos)
#define BT_MAC_BLERADIOPWRUPDN2_RXPWRUP2  BT_MAC_BLERADIOPWRUPDN2_RXPWRUP2_Msk
#define BT_MAC_BLERADIOPWRUPDN2_SYNC_POSITION2_Pos  (24U)
#define BT_MAC_BLERADIOPWRUPDN2_SYNC_POSITION2_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN2_SYNC_POSITION2_Pos)
#define BT_MAC_BLERADIOPWRUPDN2_SYNC_POSITION2  BT_MAC_BLERADIOPWRUPDN2_SYNC_POSITION2_Msk

/************ Bit definition for BT_MAC_BLERADIOPWRUPDN3 register *************/
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRUP3_Pos  (0U)
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRUP3_Msk  (0xFFUL << BT_MAC_BLERADIOPWRUPDN3_TXPWRUP3_Pos)
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRUP3  BT_MAC_BLERADIOPWRUPDN3_TXPWRUP3_Msk
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRDN3_Pos  (8U)
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRDN3_Msk  (0x7FUL << BT_MAC_BLERADIOPWRUPDN3_TXPWRDN3_Pos)
#define BT_MAC_BLERADIOPWRUPDN3_TXPWRDN3  BT_MAC_BLERADIOPWRUPDN3_TXPWRDN3_Msk

/************ Bit definition for BT_MAC_BLERADIOTXRXTIM0 register *************/
#define BT_MAC_BLERADIOTXRXTIM0_TXPATHDLY0_Pos  (0U)
#define BT_MAC_BLERADIOTXRXTIM0_TXPATHDLY0_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM0_TXPATHDLY0_Pos)
#define BT_MAC_BLERADIOTXRXTIM0_TXPATHDLY0  BT_MAC_BLERADIOTXRXTIM0_TXPATHDLY0_Msk
#define BT_MAC_BLERADIOTXRXTIM0_RXPATHDLY0_Pos  (8U)
#define BT_MAC_BLERADIOTXRXTIM0_RXPATHDLY0_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM0_RXPATHDLY0_Pos)
#define BT_MAC_BLERADIOTXRXTIM0_RXPATHDLY0  BT_MAC_BLERADIOTXRXTIM0_RXPATHDLY0_Msk
#define BT_MAC_BLERADIOTXRXTIM0_RFRXTMDA0_Pos  (16U)
#define BT_MAC_BLERADIOTXRXTIM0_RFRXTMDA0_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM0_RFRXTMDA0_Pos)
#define BT_MAC_BLERADIOTXRXTIM0_RFRXTMDA0  BT_MAC_BLERADIOTXRXTIM0_RFRXTMDA0_Msk

/************ Bit definition for BT_MAC_BLERADIOTXRXTIM1 register *************/
#define BT_MAC_BLERADIOTXRXTIM1_TXPATHDLY1_Pos  (0U)
#define BT_MAC_BLERADIOTXRXTIM1_TXPATHDLY1_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM1_TXPATHDLY1_Pos)
#define BT_MAC_BLERADIOTXRXTIM1_TXPATHDLY1  BT_MAC_BLERADIOTXRXTIM1_TXPATHDLY1_Msk
#define BT_MAC_BLERADIOTXRXTIM1_RXPATHDLY1_Pos  (8U)
#define BT_MAC_BLERADIOTXRXTIM1_RXPATHDLY1_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM1_RXPATHDLY1_Pos)
#define BT_MAC_BLERADIOTXRXTIM1_RXPATHDLY1  BT_MAC_BLERADIOTXRXTIM1_RXPATHDLY1_Msk
#define BT_MAC_BLERADIOTXRXTIM1_RFRXTMDA1_Pos  (16U)
#define BT_MAC_BLERADIOTXRXTIM1_RFRXTMDA1_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM1_RFRXTMDA1_Pos)
#define BT_MAC_BLERADIOTXRXTIM1_RFRXTMDA1  BT_MAC_BLERADIOTXRXTIM1_RFRXTMDA1_Msk

/************ Bit definition for BT_MAC_BLERADIOTXRXTIM2 register *************/
#define BT_MAC_BLERADIOTXRXTIM2_TXPATHDLY2_Pos  (0U)
#define BT_MAC_BLERADIOTXRXTIM2_TXPATHDLY2_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM2_TXPATHDLY2_Pos)
#define BT_MAC_BLERADIOTXRXTIM2_TXPATHDLY2  BT_MAC_BLERADIOTXRXTIM2_TXPATHDLY2_Msk
#define BT_MAC_BLERADIOTXRXTIM2_RXPATHDLY2_Pos  (8U)
#define BT_MAC_BLERADIOTXRXTIM2_RXPATHDLY2_Msk  (0xFFUL << BT_MAC_BLERADIOTXRXTIM2_RXPATHDLY2_Pos)
#define BT_MAC_BLERADIOTXRXTIM2_RXPATHDLY2  BT_MAC_BLERADIOTXRXTIM2_RXPATHDLY2_Msk
#define BT_MAC_BLERADIOTXRXTIM2_RFRXTMDA2_Pos  (16U)
#define BT_MAC_BLERADIOTXRXTIM2_RFRXTMDA2_Msk  (0xFFUL << BT_MAC_BLERADIOTXRXTIM2_RFRXTMDA2_Pos)
#define BT_MAC_BLERADIOTXRXTIM2_RFRXTMDA2  BT_MAC_BLERADIOTXRXTIM2_RFRXTMDA2_Msk
#define BT_MAC_BLERADIOTXRXTIM2_RXFLUSHPATHDLY2_Pos  (24U)
#define BT_MAC_BLERADIOTXRXTIM2_RXFLUSHPATHDLY2_Msk  (0xFFUL << BT_MAC_BLERADIOTXRXTIM2_RXFLUSHPATHDLY2_Pos)
#define BT_MAC_BLERADIOTXRXTIM2_RXFLUSHPATHDLY2  BT_MAC_BLERADIOTXRXTIM2_RXFLUSHPATHDLY2_Msk

/************ Bit definition for BT_MAC_BLERADIOTXRXTIM3 register *************/
#define BT_MAC_BLERADIOTXRXTIM3_TXPATHDLY3_Pos  (0U)
#define BT_MAC_BLERADIOTXRXTIM3_TXPATHDLY3_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM3_TXPATHDLY3_Pos)
#define BT_MAC_BLERADIOTXRXTIM3_TXPATHDLY3  BT_MAC_BLERADIOTXRXTIM3_TXPATHDLY3_Msk
#define BT_MAC_BLERADIOTXRXTIM3_RFRXTMDA3_Pos  (16U)
#define BT_MAC_BLERADIOTXRXTIM3_RFRXTMDA3_Msk  (0x7FUL << BT_MAC_BLERADIOTXRXTIM3_RFRXTMDA3_Pos)
#define BT_MAC_BLERADIOTXRXTIM3_RFRXTMDA3  BT_MAC_BLERADIOTXRXTIM3_RFRXTMDA3_Msk
#define BT_MAC_BLERADIOTXRXTIM3_RXFLUSHPATHDLY3_Pos  (24U)
#define BT_MAC_BLERADIOTXRXTIM3_RXFLUSHPATHDLY3_Msk  (0xFFUL << BT_MAC_BLERADIOTXRXTIM3_RXFLUSHPATHDLY3_Pos)
#define BT_MAC_BLERADIOTXRXTIM3_RXFLUSHPATHDLY3  BT_MAC_BLERADIOTXRXTIM3_RXFLUSHPATHDLY3_Msk

/************** Bit definition for BT_MAC_BLERFTESTCNTL register **************/
#define BT_MAC_BLERFTESTCNTL_TXLENGTH_Pos  (0U)
#define BT_MAC_BLERFTESTCNTL_TXLENGTH_Msk  (0xFFUL << BT_MAC_BLERFTESTCNTL_TXLENGTH_Pos)
#define BT_MAC_BLERFTESTCNTL_TXLENGTH   BT_MAC_BLERFTESTCNTL_TXLENGTH_Msk
#define BT_MAC_BLERFTESTCNTL_TXPKTCNTEN_Pos  (11U)
#define BT_MAC_BLERFTESTCNTL_TXPKTCNTEN_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_TXPKTCNTEN_Pos)
#define BT_MAC_BLERFTESTCNTL_TXPKTCNTEN  BT_MAC_BLERFTESTCNTL_TXPKTCNTEN_Msk
#define BT_MAC_BLERFTESTCNTL_TXPLDSRC_Pos  (12U)
#define BT_MAC_BLERFTESTCNTL_TXPLDSRC_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_TXPLDSRC_Pos)
#define BT_MAC_BLERFTESTCNTL_TXPLDSRC   BT_MAC_BLERFTESTCNTL_TXPLDSRC_Msk
#define BT_MAC_BLERFTESTCNTL_PRBSTYPE_Pos  (13U)
#define BT_MAC_BLERFTESTCNTL_PRBSTYPE_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_PRBSTYPE_Pos)
#define BT_MAC_BLERFTESTCNTL_PRBSTYPE   BT_MAC_BLERFTESTCNTL_PRBSTYPE_Msk
#define BT_MAC_BLERFTESTCNTL_TXLENGTHSRC_Pos  (14U)
#define BT_MAC_BLERFTESTCNTL_TXLENGTHSRC_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_TXLENGTHSRC_Pos)
#define BT_MAC_BLERFTESTCNTL_TXLENGTHSRC  BT_MAC_BLERFTESTCNTL_TXLENGTHSRC_Msk
#define BT_MAC_BLERFTESTCNTL_INFINITETX_Pos  (15U)
#define BT_MAC_BLERFTESTCNTL_INFINITETX_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_INFINITETX_Pos)
#define BT_MAC_BLERFTESTCNTL_INFINITETX  BT_MAC_BLERFTESTCNTL_INFINITETX_Msk
#define BT_MAC_BLERFTESTCNTL_PERCOUNT_MODE_Pos  (24U)
#define BT_MAC_BLERFTESTCNTL_PERCOUNT_MODE_Msk  (0x3UL << BT_MAC_BLERFTESTCNTL_PERCOUNT_MODE_Pos)
#define BT_MAC_BLERFTESTCNTL_PERCOUNT_MODE  BT_MAC_BLERFTESTCNTL_PERCOUNT_MODE_Msk
#define BT_MAC_BLERFTESTCNTL_RXPKTCNTEN_Pos  (27U)
#define BT_MAC_BLERFTESTCNTL_RXPKTCNTEN_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_RXPKTCNTEN_Pos)
#define BT_MAC_BLERFTESTCNTL_RXPKTCNTEN  BT_MAC_BLERFTESTCNTL_RXPKTCNTEN_Msk
#define BT_MAC_BLERFTESTCNTL_INFINITERX_Pos  (31U)
#define BT_MAC_BLERFTESTCNTL_INFINITERX_Msk  (0x1UL << BT_MAC_BLERFTESTCNTL_INFINITERX_Pos)
#define BT_MAC_BLERFTESTCNTL_INFINITERX  BT_MAC_BLERFTESTCNTL_INFINITERX_Msk

/************* Bit definition for BT_MAC_BLERFTESTTXSTAT register *************/
#define BT_MAC_BLERFTESTTXSTAT_TXPKTCNT_Pos  (0U)
#define BT_MAC_BLERFTESTTXSTAT_TXPKTCNT_Msk  (0xFFFFFFFFUL << BT_MAC_BLERFTESTTXSTAT_TXPKTCNT_Pos)
#define BT_MAC_BLERFTESTTXSTAT_TXPKTCNT  BT_MAC_BLERFTESTTXSTAT_TXPKTCNT_Msk

/************* Bit definition for BT_MAC_BLERFTESTRXSTAT register *************/
#define BT_MAC_BLERFTESTRXSTAT_RXPKTCNT_Pos  (0U)
#define BT_MAC_BLERFTESTRXSTAT_RXPKTCNT_Msk  (0xFFFFFFFFUL << BT_MAC_BLERFTESTRXSTAT_RXPKTCNT_Pos)
#define BT_MAC_BLERFTESTRXSTAT_RXPKTCNT  BT_MAC_BLERFTESTRXSTAT_RXPKTCNT_Msk

/************** Bit definition for BT_MAC_STARTEVTCLKN register ***************/
#define BT_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Pos  (0U)
#define BT_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Pos)
#define BT_MAC_STARTEVTCLKN_STARTEVTCLKNTS  BT_MAC_STARTEVTCLKN_STARTEVTCLKNTS_Msk

/************* Bit definition for BT_MAC_STARTEVTFINECNT register *************/
#define BT_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Pos  (0U)
#define BT_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Msk  (0x3FFUL << BT_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Pos)
#define BT_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS  BT_MAC_STARTEVTFINECNT_STARTEVTFINECNTTS_Msk

/*************** Bit definition for BT_MAC_ENDEVTCLKN register ****************/
#define BT_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Pos  (0U)
#define BT_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Pos)
#define BT_MAC_ENDEVTCLKN_ENDEVTCLKNTS  BT_MAC_ENDEVTCLKN_ENDEVTCLKNTS_Msk

/************** Bit definition for BT_MAC_ENDEVTFINECNT register **************/
#define BT_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Pos  (0U)
#define BT_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Msk  (0x3FFUL << BT_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Pos)
#define BT_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS  BT_MAC_ENDEVTFINECNT_ENDEVTFINECNTTS_Msk

/*************** Bit definition for BT_MAC_SKIPEVTCLKN register ***************/
#define BT_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Pos  (0U)
#define BT_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Msk  (0xFFFFFFFUL << BT_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Pos)
#define BT_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS  BT_MAC_SKIPEVTCLKN_SKIPEVTCLKNTS_Msk

/************* Bit definition for BT_MAC_SKIPEVTFINECNT register **************/
#define BT_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Pos  (0U)
#define BT_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Msk  (0x3FFUL << BT_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Pos)
#define BT_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS  BT_MAC_SKIPEVTFINECNT_SKIPEVTFINECNTTS_Msk

/***************** Bit definition for BT_MAC_ADVTIM register ******************/
#define BT_MAC_ADVTIM_ADVINT_Pos        (0U)
#define BT_MAC_ADVTIM_ADVINT_Msk        (0x3FFFUL << BT_MAC_ADVTIM_ADVINT_Pos)
#define BT_MAC_ADVTIM_ADVINT            BT_MAC_ADVTIM_ADVINT_Msk
#define BT_MAC_ADVTIM_RX_AUXPTR_THR_Pos  (16U)
#define BT_MAC_ADVTIM_RX_AUXPTR_THR_Msk  (0xFFUL << BT_MAC_ADVTIM_RX_AUXPTR_THR_Pos)
#define BT_MAC_ADVTIM_RX_AUXPTR_THR     BT_MAC_ADVTIM_RX_AUXPTR_THR_Msk
#define BT_MAC_ADVTIM_TX_AUXPTR_THR_Pos  (24U)
#define BT_MAC_ADVTIM_TX_AUXPTR_THR_Msk  (0xFFUL << BT_MAC_ADVTIM_TX_AUXPTR_THR_Pos)
#define BT_MAC_ADVTIM_TX_AUXPTR_THR     BT_MAC_ADVTIM_TX_AUXPTR_THR_Msk

/*************** Bit definition for BT_MAC_ACTSCANCNTL register ***************/
#define BT_MAC_ACTSCANCNTL_UPPERLIMIT_Pos  (0U)
#define BT_MAC_ACTSCANCNTL_UPPERLIMIT_Msk  (0x1FFUL << BT_MAC_ACTSCANCNTL_UPPERLIMIT_Pos)
#define BT_MAC_ACTSCANCNTL_UPPERLIMIT   BT_MAC_ACTSCANCNTL_UPPERLIMIT_Msk
#define BT_MAC_ACTSCANCNTL_BACKOFF_Pos  (16U)
#define BT_MAC_ACTSCANCNTL_BACKOFF_Msk  (0x1FFUL << BT_MAC_ACTSCANCNTL_BACKOFF_Pos)
#define BT_MAC_ACTSCANCNTL_BACKOFF      BT_MAC_ACTSCANCNTL_BACKOFF_Msk

/**************** Bit definition for BT_MAC_WPALCNTL register *****************/
#define BT_MAC_WPALCNTL_WPALBASEPTR_Pos  (0U)
#define BT_MAC_WPALCNTL_WPALBASEPTR_Msk  (0x3FFFUL << BT_MAC_WPALCNTL_WPALBASEPTR_Pos)
#define BT_MAC_WPALCNTL_WPALBASEPTR     BT_MAC_WPALCNTL_WPALBASEPTR_Msk
#define BT_MAC_WPALCNTL_WPALNBDEV_Pos   (16U)
#define BT_MAC_WPALCNTL_WPALNBDEV_Msk   (0xFFUL << BT_MAC_WPALCNTL_WPALNBDEV_Pos)
#define BT_MAC_WPALCNTL_WPALNBDEV       BT_MAC_WPALCNTL_WPALNBDEV_Msk

/*************** Bit definition for BT_MAC_WPALCURRENT register ***************/
#define BT_MAC_WPALCURRENT_WPALCURRENTPTR_Pos  (0U)
#define BT_MAC_WPALCURRENT_WPALCURRENTPTR_Msk  (0xFFFFUL << BT_MAC_WPALCURRENT_WPALCURRENTPTR_Pos)
#define BT_MAC_WPALCURRENT_WPALCURRENTPTR  BT_MAC_WPALCURRENT_WPALCURRENTPTR_Msk

/************* Bit definition for BT_MAC_SEARCH_TIMEOUT register **************/
#define BT_MAC_SEARCH_TIMEOUT_SEARCH_TIMEOUT_Pos  (0U)
#define BT_MAC_SEARCH_TIMEOUT_SEARCH_TIMEOUT_Msk  (0x3FUL << BT_MAC_SEARCH_TIMEOUT_SEARCH_TIMEOUT_Pos)
#define BT_MAC_SEARCH_TIMEOUT_SEARCH_TIMEOUT  BT_MAC_SEARCH_TIMEOUT_SEARCH_TIMEOUT_Msk

/************* Bit definition for BT_MAC_BLECOEXIFCNTL0 register **************/
#define BT_MAC_BLECOEXIFCNTL0_WLANCOEX_EN_Pos  (0U)
#define BT_MAC_BLECOEXIFCNTL0_WLANCOEX_EN_Msk  (0x1UL << BT_MAC_BLECOEXIFCNTL0_WLANCOEX_EN_Pos)
#define BT_MAC_BLECOEXIFCNTL0_WLANCOEX_EN  BT_MAC_BLECOEXIFCNTL0_WLANCOEX_EN_Msk
#define BT_MAC_BLECOEXIFCNTL0_SYNCGEN_EN_Pos  (1U)
#define BT_MAC_BLECOEXIFCNTL0_SYNCGEN_EN_Msk  (0x1UL << BT_MAC_BLECOEXIFCNTL0_SYNCGEN_EN_Pos)
#define BT_MAC_BLECOEXIFCNTL0_SYNCGEN_EN  BT_MAC_BLECOEXIFCNTL0_SYNCGEN_EN_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSCOEX_EN_Pos  (2U)
#define BT_MAC_BLECOEXIFCNTL0_MWSCOEX_EN_Msk  (0x1UL << BT_MAC_BLECOEXIFCNTL0_MWSCOEX_EN_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSCOEX_EN  BT_MAC_BLECOEXIFCNTL0_MWSCOEX_EN_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSWCI_EN_Pos  (3U)
#define BT_MAC_BLECOEXIFCNTL0_MWSWCI_EN_Msk  (0x1UL << BT_MAC_BLECOEXIFCNTL0_MWSWCI_EN_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSWCI_EN  BT_MAC_BLECOEXIFCNTL0_MWSWCI_EN_Msk
#define BT_MAC_BLECOEXIFCNTL0_WLANRXMSK_Pos  (4U)
#define BT_MAC_BLECOEXIFCNTL0_WLANRXMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_WLANRXMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_WLANRXMSK  BT_MAC_BLECOEXIFCNTL0_WLANRXMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_WLANTXMSK_Pos  (6U)
#define BT_MAC_BLECOEXIFCNTL0_WLANTXMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_WLANTXMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_WLANTXMSK  BT_MAC_BLECOEXIFCNTL0_WLANTXMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSRXMSK_Pos  (8U)
#define BT_MAC_BLECOEXIFCNTL0_MWSRXMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_MWSRXMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSRXMSK  BT_MAC_BLECOEXIFCNTL0_MWSRXMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSTXMSK_Pos  (10U)
#define BT_MAC_BLECOEXIFCNTL0_MWSTXMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_MWSTXMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSTXMSK  BT_MAC_BLECOEXIFCNTL0_MWSTXMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSRXFREQMSK_Pos  (12U)
#define BT_MAC_BLECOEXIFCNTL0_MWSRXFREQMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_MWSRXFREQMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSRXFREQMSK  BT_MAC_BLECOEXIFCNTL0_MWSRXFREQMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSTXFREQMSK_Pos  (14U)
#define BT_MAC_BLECOEXIFCNTL0_MWSTXFREQMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_MWSTXFREQMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSTXFREQMSK  BT_MAC_BLECOEXIFCNTL0_MWSTXFREQMSK_Msk
#define BT_MAC_BLECOEXIFCNTL0_WLCTXPRIOMODE_Pos  (16U)
#define BT_MAC_BLECOEXIFCNTL0_WLCTXPRIOMODE_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_WLCTXPRIOMODE_Pos)
#define BT_MAC_BLECOEXIFCNTL0_WLCTXPRIOMODE  BT_MAC_BLECOEXIFCNTL0_WLCTXPRIOMODE_Msk
#define BT_MAC_BLECOEXIFCNTL0_WLCRXPRIOMODE_Pos  (18U)
#define BT_MAC_BLECOEXIFCNTL0_WLCRXPRIOMODE_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_WLCRXPRIOMODE_Pos)
#define BT_MAC_BLECOEXIFCNTL0_WLCRXPRIOMODE  BT_MAC_BLECOEXIFCNTL0_WLCRXPRIOMODE_Msk
#define BT_MAC_BLECOEXIFCNTL0_MWSSCANFREQMSK_Pos  (20U)
#define BT_MAC_BLECOEXIFCNTL0_MWSSCANFREQMSK_Msk  (0x3UL << BT_MAC_BLECOEXIFCNTL0_MWSSCANFREQMSK_Pos)
#define BT_MAC_BLECOEXIFCNTL0_MWSSCANFREQMSK  BT_MAC_BLECOEXIFCNTL0_MWSSCANFREQMSK_Msk

/************* Bit definition for BT_MAC_BLECOEXIFCNTL1 register **************/
#define BT_MAC_BLECOEXIFCNTL1_WLCPDELAY_Pos  (0U)
#define BT_MAC_BLECOEXIFCNTL1_WLCPDELAY_Msk  (0x7FUL << BT_MAC_BLECOEXIFCNTL1_WLCPDELAY_Pos)
#define BT_MAC_BLECOEXIFCNTL1_WLCPDELAY  BT_MAC_BLECOEXIFCNTL1_WLCPDELAY_Msk
#define BT_MAC_BLECOEXIFCNTL1_WLCPDURATION_Pos  (8U)
#define BT_MAC_BLECOEXIFCNTL1_WLCPDURATION_Msk  (0x7FUL << BT_MAC_BLECOEXIFCNTL1_WLCPDURATION_Pos)
#define BT_MAC_BLECOEXIFCNTL1_WLCPDURATION  BT_MAC_BLECOEXIFCNTL1_WLCPDURATION_Msk
#define BT_MAC_BLECOEXIFCNTL1_WLCPTXTHR_Pos  (16U)
#define BT_MAC_BLECOEXIFCNTL1_WLCPTXTHR_Msk  (0x1FUL << BT_MAC_BLECOEXIFCNTL1_WLCPTXTHR_Pos)
#define BT_MAC_BLECOEXIFCNTL1_WLCPTXTHR  BT_MAC_BLECOEXIFCNTL1_WLCPTXTHR_Msk
#define BT_MAC_BLECOEXIFCNTL1_WLCPRXTHR_Pos  (24U)
#define BT_MAC_BLECOEXIFCNTL1_WLCPRXTHR_Msk  (0x1FUL << BT_MAC_BLECOEXIFCNTL1_WLCPRXTHR_Pos)
#define BT_MAC_BLECOEXIFCNTL1_WLCPRXTHR  BT_MAC_BLECOEXIFCNTL1_WLCPRXTHR_Msk

/************* Bit definition for BT_MAC_BLECOEXIFCNTL2 register **************/
#define BT_MAC_BLECOEXIFCNTL2_TX_ANT_DELAY_Pos  (0U)
#define BT_MAC_BLECOEXIFCNTL2_TX_ANT_DELAY_Msk  (0xFUL << BT_MAC_BLECOEXIFCNTL2_TX_ANT_DELAY_Pos)
#define BT_MAC_BLECOEXIFCNTL2_TX_ANT_DELAY  BT_MAC_BLECOEXIFCNTL2_TX_ANT_DELAY_Msk
#define BT_MAC_BLECOEXIFCNTL2_RX_ANT_DELAY_Pos  (8U)
#define BT_MAC_BLECOEXIFCNTL2_RX_ANT_DELAY_Msk  (0xFUL << BT_MAC_BLECOEXIFCNTL2_RX_ANT_DELAY_Pos)
#define BT_MAC_BLECOEXIFCNTL2_RX_ANT_DELAY  BT_MAC_BLECOEXIFCNTL2_RX_ANT_DELAY_Msk

/**************** Bit definition for BT_MAC_BLEMPRIO0 register ****************/
#define BT_MAC_BLEMPRIO0_BLEM0_Pos      (0U)
#define BT_MAC_BLEMPRIO0_BLEM0_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM0_Pos)
#define BT_MAC_BLEMPRIO0_BLEM0          BT_MAC_BLEMPRIO0_BLEM0_Msk
#define BT_MAC_BLEMPRIO0_BLEM1_Pos      (4U)
#define BT_MAC_BLEMPRIO0_BLEM1_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM1_Pos)
#define BT_MAC_BLEMPRIO0_BLEM1          BT_MAC_BLEMPRIO0_BLEM1_Msk
#define BT_MAC_BLEMPRIO0_BLEM2_Pos      (8U)
#define BT_MAC_BLEMPRIO0_BLEM2_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM2_Pos)
#define BT_MAC_BLEMPRIO0_BLEM2          BT_MAC_BLEMPRIO0_BLEM2_Msk
#define BT_MAC_BLEMPRIO0_BLEM3_Pos      (12U)
#define BT_MAC_BLEMPRIO0_BLEM3_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM3_Pos)
#define BT_MAC_BLEMPRIO0_BLEM3          BT_MAC_BLEMPRIO0_BLEM3_Msk
#define BT_MAC_BLEMPRIO0_BLEM4_Pos      (16U)
#define BT_MAC_BLEMPRIO0_BLEM4_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM4_Pos)
#define BT_MAC_BLEMPRIO0_BLEM4          BT_MAC_BLEMPRIO0_BLEM4_Msk
#define BT_MAC_BLEMPRIO0_BLEM5_Pos      (20U)
#define BT_MAC_BLEMPRIO0_BLEM5_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM5_Pos)
#define BT_MAC_BLEMPRIO0_BLEM5          BT_MAC_BLEMPRIO0_BLEM5_Msk
#define BT_MAC_BLEMPRIO0_BLEM6_Pos      (24U)
#define BT_MAC_BLEMPRIO0_BLEM6_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM6_Pos)
#define BT_MAC_BLEMPRIO0_BLEM6          BT_MAC_BLEMPRIO0_BLEM6_Msk
#define BT_MAC_BLEMPRIO0_BLEM7_Pos      (28U)
#define BT_MAC_BLEMPRIO0_BLEM7_Msk      (0xFUL << BT_MAC_BLEMPRIO0_BLEM7_Pos)
#define BT_MAC_BLEMPRIO0_BLEM7          BT_MAC_BLEMPRIO0_BLEM7_Msk

/**************** Bit definition for BT_MAC_BLEMPRIO1 register ****************/
#define BT_MAC_BLEMPRIO1_BLEM8_Pos      (0U)
#define BT_MAC_BLEMPRIO1_BLEM8_Msk      (0xFUL << BT_MAC_BLEMPRIO1_BLEM8_Pos)
#define BT_MAC_BLEMPRIO1_BLEM8          BT_MAC_BLEMPRIO1_BLEM8_Msk
#define BT_MAC_BLEMPRIO1_BLEM9_Pos      (4U)
#define BT_MAC_BLEMPRIO1_BLEM9_Msk      (0xFUL << BT_MAC_BLEMPRIO1_BLEM9_Pos)
#define BT_MAC_BLEMPRIO1_BLEM9          BT_MAC_BLEMPRIO1_BLEM9_Msk
#define BT_MAC_BLEMPRIO1_BLEM10_Pos     (8U)
#define BT_MAC_BLEMPRIO1_BLEM10_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM10_Pos)
#define BT_MAC_BLEMPRIO1_BLEM10         BT_MAC_BLEMPRIO1_BLEM10_Msk
#define BT_MAC_BLEMPRIO1_BLEM11_Pos     (12U)
#define BT_MAC_BLEMPRIO1_BLEM11_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM11_Pos)
#define BT_MAC_BLEMPRIO1_BLEM11         BT_MAC_BLEMPRIO1_BLEM11_Msk
#define BT_MAC_BLEMPRIO1_BLEM12_Pos     (16U)
#define BT_MAC_BLEMPRIO1_BLEM12_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM12_Pos)
#define BT_MAC_BLEMPRIO1_BLEM12         BT_MAC_BLEMPRIO1_BLEM12_Msk
#define BT_MAC_BLEMPRIO1_BLEM13_Pos     (20U)
#define BT_MAC_BLEMPRIO1_BLEM13_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM13_Pos)
#define BT_MAC_BLEMPRIO1_BLEM13         BT_MAC_BLEMPRIO1_BLEM13_Msk
#define BT_MAC_BLEMPRIO1_BLEM14_Pos     (24U)
#define BT_MAC_BLEMPRIO1_BLEM14_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM14_Pos)
#define BT_MAC_BLEMPRIO1_BLEM14         BT_MAC_BLEMPRIO1_BLEM14_Msk
#define BT_MAC_BLEMPRIO1_BLEM15_Pos     (28U)
#define BT_MAC_BLEMPRIO1_BLEM15_Msk     (0xFUL << BT_MAC_BLEMPRIO1_BLEM15_Pos)
#define BT_MAC_BLEMPRIO1_BLEM15         BT_MAC_BLEMPRIO1_BLEM15_Msk

/**************** Bit definition for BT_MAC_BLEMPRIO2 register ****************/
#define BT_MAC_BLEMPRIO2_BLEM16_Pos     (0U)
#define BT_MAC_BLEMPRIO2_BLEM16_Msk     (0xFUL << BT_MAC_BLEMPRIO2_BLEM16_Pos)
#define BT_MAC_BLEMPRIO2_BLEM16         BT_MAC_BLEMPRIO2_BLEM16_Msk
#define BT_MAC_BLEMPRIO2_BLEM17_Pos     (4U)
#define BT_MAC_BLEMPRIO2_BLEM17_Msk     (0xFUL << BT_MAC_BLEMPRIO2_BLEM17_Pos)
#define BT_MAC_BLEMPRIO2_BLEM17         BT_MAC_BLEMPRIO2_BLEM17_Msk
#define BT_MAC_BLEMPRIO2_BLEM18_Pos     (8U)
#define BT_MAC_BLEMPRIO2_BLEM18_Msk     (0xFUL << BT_MAC_BLEMPRIO2_BLEM18_Pos)
#define BT_MAC_BLEMPRIO2_BLEM18         BT_MAC_BLEMPRIO2_BLEM18_Msk
#define BT_MAC_BLEMPRIO2_BLEMDEFAULT_Pos  (28U)
#define BT_MAC_BLEMPRIO2_BLEMDEFAULT_Msk  (0xFUL << BT_MAC_BLEMPRIO2_BLEMDEFAULT_Pos)
#define BT_MAC_BLEMPRIO2_BLEMDEFAULT    BT_MAC_BLEMPRIO2_BLEMDEFAULT_Msk

/***************** Bit definition for BT_MAC_RALCNTL register *****************/
#define BT_MAC_RALCNTL_RALBASEPTR_Pos   (0U)
#define BT_MAC_RALCNTL_RALBASEPTR_Msk   (0xFFFFUL << BT_MAC_RALCNTL_RALBASEPTR_Pos)
#define BT_MAC_RALCNTL_RALBASEPTR       BT_MAC_RALCNTL_RALBASEPTR_Msk
#define BT_MAC_RALCNTL_RALNBDEV_Pos     (16U)
#define BT_MAC_RALCNTL_RALNBDEV_Msk     (0xFFUL << BT_MAC_RALCNTL_RALNBDEV_Pos)
#define BT_MAC_RALCNTL_RALNBDEV         BT_MAC_RALCNTL_RALNBDEV_Msk

/*************** Bit definition for BT_MAC_RALCURRENT register ****************/
#define BT_MAC_RALCURRENT_RALCURRENTPTR_Pos  (0U)
#define BT_MAC_RALCURRENT_RALCURRENTPTR_Msk  (0xFFFFUL << BT_MAC_RALCURRENT_RALCURRENTPTR_Pos)
#define BT_MAC_RALCURRENT_RALCURRENTPTR  BT_MAC_RALCURRENT_RALCURRENTPTR_Msk

/************** Bit definition for BT_MAC_RAL_LOCAL_RND register **************/
#define BT_MAC_RAL_LOCAL_RND_LRND_VAL_Pos  (0U)
#define BT_MAC_RAL_LOCAL_RND_LRND_VAL_Msk  (0x3FFFFFUL << BT_MAC_RAL_LOCAL_RND_LRND_VAL_Pos)
#define BT_MAC_RAL_LOCAL_RND_LRND_VAL   BT_MAC_RAL_LOCAL_RND_LRND_VAL_Msk
#define BT_MAC_RAL_LOCAL_RND_LRND_INIT_Pos  (31U)
#define BT_MAC_RAL_LOCAL_RND_LRND_INIT_Msk  (0x1UL << BT_MAC_RAL_LOCAL_RND_LRND_INIT_Pos)
#define BT_MAC_RAL_LOCAL_RND_LRND_INIT  BT_MAC_RAL_LOCAL_RND_LRND_INIT_Msk

/************** Bit definition for BT_MAC_RAL_PEER_RND register ***************/
#define BT_MAC_RAL_PEER_RND_PRND_VAL_Pos  (0U)
#define BT_MAC_RAL_PEER_RND_PRND_VAL_Msk  (0x3FFFFFUL << BT_MAC_RAL_PEER_RND_PRND_VAL_Pos)
#define BT_MAC_RAL_PEER_RND_PRND_VAL    BT_MAC_RAL_PEER_RND_PRND_VAL_Msk
#define BT_MAC_RAL_PEER_RND_PRND_INIT_Pos  (31U)
#define BT_MAC_RAL_PEER_RND_PRND_INIT_Msk  (0x1UL << BT_MAC_RAL_PEER_RND_PRND_INIT_Pos)
#define BT_MAC_RAL_PEER_RND_PRND_INIT   BT_MAC_RAL_PEER_RND_PRND_INIT_Msk

/*************** Bit definition for BT_MAC_DFCNTL0_1US register ***************/
#define BT_MAC_DFCNTL0_1US_TXSWSTINST0_1US_Pos  (0U)
#define BT_MAC_DFCNTL0_1US_TXSWSTINST0_1US_Msk  (0xFFUL << BT_MAC_DFCNTL0_1US_TXSWSTINST0_1US_Pos)
#define BT_MAC_DFCNTL0_1US_TXSWSTINST0_1US  BT_MAC_DFCNTL0_1US_TXSWSTINST0_1US_Msk
#define BT_MAC_DFCNTL0_1US_RXSWSTINST0_1US_Pos  (16U)
#define BT_MAC_DFCNTL0_1US_RXSWSTINST0_1US_Msk  (0xFFUL << BT_MAC_DFCNTL0_1US_RXSWSTINST0_1US_Pos)
#define BT_MAC_DFCNTL0_1US_RXSWSTINST0_1US  BT_MAC_DFCNTL0_1US_RXSWSTINST0_1US_Msk
#define BT_MAC_DFCNTL0_1US_RXSAMPSTINST0_1US_Pos  (24U)
#define BT_MAC_DFCNTL0_1US_RXSAMPSTINST0_1US_Msk  (0xFFUL << BT_MAC_DFCNTL0_1US_RXSAMPSTINST0_1US_Pos)
#define BT_MAC_DFCNTL0_1US_RXSAMPSTINST0_1US  BT_MAC_DFCNTL0_1US_RXSAMPSTINST0_1US_Msk

/*************** Bit definition for BT_MAC_DFCNTL0_2US register ***************/
#define BT_MAC_DFCNTL0_2US_TXSWSTINST0_2US_Pos  (0U)
#define BT_MAC_DFCNTL0_2US_TXSWSTINST0_2US_Msk  (0xFFUL << BT_MAC_DFCNTL0_2US_TXSWSTINST0_2US_Pos)
#define BT_MAC_DFCNTL0_2US_TXSWSTINST0_2US  BT_MAC_DFCNTL0_2US_TXSWSTINST0_2US_Msk
#define BT_MAC_DFCNTL0_2US_RXSWSTINST0_2US_Pos  (16U)
#define BT_MAC_DFCNTL0_2US_RXSWSTINST0_2US_Msk  (0xFFUL << BT_MAC_DFCNTL0_2US_RXSWSTINST0_2US_Pos)
#define BT_MAC_DFCNTL0_2US_RXSWSTINST0_2US  BT_MAC_DFCNTL0_2US_RXSWSTINST0_2US_Msk
#define BT_MAC_DFCNTL0_2US_RXSAMPSTINST0_2US_Pos  (24U)
#define BT_MAC_DFCNTL0_2US_RXSAMPSTINST0_2US_Msk  (0xFFUL << BT_MAC_DFCNTL0_2US_RXSAMPSTINST0_2US_Pos)
#define BT_MAC_DFCNTL0_2US_RXSAMPSTINST0_2US  BT_MAC_DFCNTL0_2US_RXSAMPSTINST0_2US_Msk

/*************** Bit definition for BT_MAC_DFCNTL1_1US register ***************/
#define BT_MAC_DFCNTL1_1US_TXSWSTINST1_1US_Pos  (0U)
#define BT_MAC_DFCNTL1_1US_TXSWSTINST1_1US_Msk  (0xFFUL << BT_MAC_DFCNTL1_1US_TXSWSTINST1_1US_Pos)
#define BT_MAC_DFCNTL1_1US_TXSWSTINST1_1US  BT_MAC_DFCNTL1_1US_TXSWSTINST1_1US_Msk
#define BT_MAC_DFCNTL1_1US_RXSWSTINST1_1US_Pos  (16U)
#define BT_MAC_DFCNTL1_1US_RXSWSTINST1_1US_Msk  (0xFFUL << BT_MAC_DFCNTL1_1US_RXSWSTINST1_1US_Pos)
#define BT_MAC_DFCNTL1_1US_RXSWSTINST1_1US  BT_MAC_DFCNTL1_1US_RXSWSTINST1_1US_Msk
#define BT_MAC_DFCNTL1_1US_RXSAMPSTINST1_1US_Pos  (24U)
#define BT_MAC_DFCNTL1_1US_RXSAMPSTINST1_1US_Msk  (0xFFUL << BT_MAC_DFCNTL1_1US_RXSAMPSTINST1_1US_Pos)
#define BT_MAC_DFCNTL1_1US_RXSAMPSTINST1_1US  BT_MAC_DFCNTL1_1US_RXSAMPSTINST1_1US_Msk

/*************** Bit definition for BT_MAC_DFCNTL1_2US register ***************/
#define BT_MAC_DFCNTL1_2US_TXSWSTINST1_2US_Pos  (0U)
#define BT_MAC_DFCNTL1_2US_TXSWSTINST1_2US_Msk  (0xFFUL << BT_MAC_DFCNTL1_2US_TXSWSTINST1_2US_Pos)
#define BT_MAC_DFCNTL1_2US_TXSWSTINST1_2US  BT_MAC_DFCNTL1_2US_TXSWSTINST1_2US_Msk
#define BT_MAC_DFCNTL1_2US_RXSWSTINST1_2US_Pos  (16U)
#define BT_MAC_DFCNTL1_2US_RXSWSTINST1_2US_Msk  (0xFFUL << BT_MAC_DFCNTL1_2US_RXSWSTINST1_2US_Pos)
#define BT_MAC_DFCNTL1_2US_RXSWSTINST1_2US  BT_MAC_DFCNTL1_2US_RXSWSTINST1_2US_Msk
#define BT_MAC_DFCNTL1_2US_RXSAMPSTINST1_2US_Pos  (24U)
#define BT_MAC_DFCNTL1_2US_RXSAMPSTINST1_2US_Msk  (0xFFUL << BT_MAC_DFCNTL1_2US_RXSAMPSTINST1_2US_Pos)
#define BT_MAC_DFCNTL1_2US_RXSAMPSTINST1_2US  BT_MAC_DFCNTL1_2US_RXSAMPSTINST1_2US_Msk

/************** Bit definition for BT_MAC_DFCURRENTPTR register ***************/
#define BT_MAC_DFCURRENTPTR_DFCURRENTPTR_Pos  (0U)
#define BT_MAC_DFCURRENTPTR_DFCURRENTPTR_Msk  (0x3FFFUL << BT_MAC_DFCURRENTPTR_DFCURRENTPTR_Pos)
#define BT_MAC_DFCURRENTPTR_DFCURRENTPTR  BT_MAC_DFCURRENTPTR_DFCURRENTPTR_Msk

/**************** Bit definition for BT_MAC_DFANTCNTL register ****************/
#define BT_MAC_DFANTCNTL_TXPRIMANTID_Pos  (0U)
#define BT_MAC_DFANTCNTL_TXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANTCNTL_TXPRIMANTID_Pos)
#define BT_MAC_DFANTCNTL_TXPRIMANTID    BT_MAC_DFANTCNTL_TXPRIMANTID_Msk
#define BT_MAC_DFANTCNTL_TXPRIMIDCNTEN_Pos  (7U)
#define BT_MAC_DFANTCNTL_TXPRIMIDCNTEN_Msk  (0x1UL << BT_MAC_DFANTCNTL_TXPRIMIDCNTEN_Pos)
#define BT_MAC_DFANTCNTL_TXPRIMIDCNTEN  BT_MAC_DFANTCNTL_TXPRIMIDCNTEN_Msk
#define BT_MAC_DFANTCNTL_RXPRIMANTID_Pos  (8U)
#define BT_MAC_DFANTCNTL_RXPRIMANTID_Msk  (0x7FUL << BT_MAC_DFANTCNTL_RXPRIMANTID_Pos)
#define BT_MAC_DFANTCNTL_RXPRIMANTID    BT_MAC_DFANTCNTL_RXPRIMANTID_Msk
#define BT_MAC_DFANTCNTL_RXPRIMIDCNTEN_Pos  (15U)
#define BT_MAC_DFANTCNTL_RXPRIMIDCNTEN_Msk  (0x1UL << BT_MAC_DFANTCNTL_RXPRIMIDCNTEN_Pos)
#define BT_MAC_DFANTCNTL_RXPRIMIDCNTEN  BT_MAC_DFANTCNTL_RXPRIMIDCNTEN_Msk

/**************** Bit definition for BT_MAC_DFIFCNTL register *****************/
#define BT_MAC_DFIFCNTL_SYMBOL_ORDER_Pos  (0U)
#define BT_MAC_DFIFCNTL_SYMBOL_ORDER_Msk  (0x1UL << BT_MAC_DFIFCNTL_SYMBOL_ORDER_Pos)
#define BT_MAC_DFIFCNTL_SYMBOL_ORDER    BT_MAC_DFIFCNTL_SYMBOL_ORDER_Msk
#define BT_MAC_DFIFCNTL_MSB_LSB_ORDER_Pos  (1U)
#define BT_MAC_DFIFCNTL_MSB_LSB_ORDER_Msk  (0x1UL << BT_MAC_DFIFCNTL_MSB_LSB_ORDER_Pos)
#define BT_MAC_DFIFCNTL_MSB_LSB_ORDER   BT_MAC_DFIFCNTL_MSB_LSB_ORDER_Msk
#define BT_MAC_DFIFCNTL_IF_WIDTH_Pos    (2U)
#define BT_MAC_DFIFCNTL_IF_WIDTH_Msk    (0x3UL << BT_MAC_DFIFCNTL_IF_WIDTH_Pos)
#define BT_MAC_DFIFCNTL_IF_WIDTH        BT_MAC_DFIFCNTL_IF_WIDTH_Msk
#define BT_MAC_DFIFCNTL_SAMPVALID_BEH_Pos  (4U)
#define BT_MAC_DFIFCNTL_SAMPVALID_BEH_Msk  (0x3UL << BT_MAC_DFIFCNTL_SAMPVALID_BEH_Pos)
#define BT_MAC_DFIFCNTL_SAMPVALID_BEH   BT_MAC_DFIFCNTL_SAMPVALID_BEH_Msk
#define BT_MAC_DFIFCNTL_SAMPREQ_BEH_Pos  (6U)
#define BT_MAC_DFIFCNTL_SAMPREQ_BEH_Msk  (0x1UL << BT_MAC_DFIFCNTL_SAMPREQ_BEH_Pos)
#define BT_MAC_DFIFCNTL_SAMPREQ_BEH     BT_MAC_DFIFCNTL_SAMPREQ_BEH_Msk
#define BT_MAC_DFIFCNTL_ANTSWTCNT_BEH_Pos  (7U)
#define BT_MAC_DFIFCNTL_ANTSWTCNT_BEH_Msk  (0x1UL << BT_MAC_DFIFCNTL_ANTSWTCNT_BEH_Pos)
#define BT_MAC_DFIFCNTL_ANTSWTCNT_BEH   BT_MAC_DFIFCNTL_ANTSWTCNT_BEH_Msk

/*************** Bit definition for BT_MAC_FREQSELCNTL register ***************/
#define BT_MAC_FREQSELCNTL_FREQSEL_START_Pos  (0U)
#define BT_MAC_FREQSELCNTL_FREQSEL_START_Msk  (0x1UL << BT_MAC_FREQSELCNTL_FREQSEL_START_Pos)
#define BT_MAC_FREQSELCNTL_FREQSEL_START  BT_MAC_FREQSELCNTL_FREQSEL_START_Msk
#define BT_MAC_FREQSELCNTL_FREQSEL_MODE_Pos  (1U)
#define BT_MAC_FREQSELCNTL_FREQSEL_MODE_Msk  (0x1UL << BT_MAC_FREQSELCNTL_FREQSEL_MODE_Pos)
#define BT_MAC_FREQSELCNTL_FREQSEL_MODE  BT_MAC_FREQSELCNTL_FREQSEL_MODE_Msk
#define BT_MAC_FREQSELCNTL_NBLOOPS_Pos  (16U)
#define BT_MAC_FREQSELCNTL_NBLOOPS_Msk  (0xFFUL << BT_MAC_FREQSELCNTL_NBLOOPS_Pos)
#define BT_MAC_FREQSELCNTL_NBLOOPS      BT_MAC_FREQSELCNTL_NBLOOPS_Msk

/*************** Bit definition for BT_MAC_FREQSELPTR register ****************/
#define BT_MAC_FREQSELPTR_FREQSELPTR_Pos  (0U)
#define BT_MAC_FREQSELPTR_FREQSELPTR_Msk  (0x3FFFUL << BT_MAC_FREQSELPTR_FREQSELPTR_Pos)
#define BT_MAC_FREQSELPTR_FREQSELPTR    BT_MAC_FREQSELPTR_FREQSELPTR_Msk

/************ Bit definition for BT_MAC_FREQSEL_CS1_SEED register *************/
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_HOPINT_Pos  (0U)
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_HOPINT_Msk  (0x1FUL << BT_MAC_FREQSEL_CS1_SEED_FREQSEL_HOPINT_Pos)
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_HOPINT  BT_MAC_FREQSEL_CS1_SEED_FREQSEL_HOPINT_Msk
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_LAST_CHIDX_Pos  (16U)
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_LAST_CHIDX_Msk  (0x3FUL << BT_MAC_FREQSEL_CS1_SEED_FREQSEL_LAST_CHIDX_Pos)
#define BT_MAC_FREQSEL_CS1_SEED_FREQSEL_LAST_CHIDX  BT_MAC_FREQSEL_CS1_SEED_FREQSEL_LAST_CHIDX_Msk

/************** Bit definition for BT_MAC_FREQ_CS2_SEED register **************/
#define BT_MAC_FREQ_CS2_SEED_FREQSEL_EVTCNT_Pos  (0U)
#define BT_MAC_FREQ_CS2_SEED_FREQSEL_EVTCNT_Msk  (0xFFFFUL << BT_MAC_FREQ_CS2_SEED_FREQSEL_EVTCNT_Pos)
#define BT_MAC_FREQ_CS2_SEED_FREQSEL_EVTCNT  BT_MAC_FREQ_CS2_SEED_FREQSEL_EVTCNT_Msk
#define BT_MAC_FREQ_CS2_SEED_CHANNEL_IDENTIFIER_Pos  (16U)
#define BT_MAC_FREQ_CS2_SEED_CHANNEL_IDENTIFIER_Msk  (0xFFFFUL << BT_MAC_FREQ_CS2_SEED_CHANNEL_IDENTIFIER_Pos)
#define BT_MAC_FREQ_CS2_SEED_CHANNEL_IDENTIFIER  BT_MAC_FREQ_CS2_SEED_CHANNEL_IDENTIFIER_Msk

/************ Bit definition for BT_MAC_FREQSEL_LLCHMAP0 register *************/
#define BT_MAC_FREQSEL_LLCHMAP0_FREQSEL_LLCHAMP0_Pos  (0U)
#define BT_MAC_FREQSEL_LLCHMAP0_FREQSEL_LLCHAMP0_Msk  (0xFFFFFFFFUL << BT_MAC_FREQSEL_LLCHMAP0_FREQSEL_LLCHAMP0_Pos)
#define BT_MAC_FREQSEL_LLCHMAP0_FREQSEL_LLCHAMP0  BT_MAC_FREQSEL_LLCHMAP0_FREQSEL_LLCHAMP0_Msk

/************ Bit definition for BT_MAC_FREQSEL_LLCHMAP1 register *************/
#define BT_MAC_FREQSEL_LLCHMAP1_FREQSEL_LLCHMAP1_Pos  (0U)
#define BT_MAC_FREQSEL_LLCHMAP1_FREQSEL_LLCHMAP1_Msk  (0x1FUL << BT_MAC_FREQSEL_LLCHMAP1_FREQSEL_LLCHMAP1_Pos)
#define BT_MAC_FREQSEL_LLCHMAP1_FREQSEL_LLCHMAP1  BT_MAC_FREQSEL_LLCHMAP1_FREQSEL_LLCHMAP1_Msk

/*************** Bit definition for BT_MAC_ISOCNTCNTL register ****************/
#define BT_MAC_ISOCNTCNTL_ISOCORRMODE_Pos  (0U)
#define BT_MAC_ISOCNTCNTL_ISOCORRMODE_Msk  (0x1UL << BT_MAC_ISOCNTCNTL_ISOCORRMODE_Pos)
#define BT_MAC_ISOCNTCNTL_ISOCORRMODE   BT_MAC_ISOCNTCNTL_ISOCORRMODE_Msk
#define BT_MAC_ISOCNTCNTL_ISO_PHASE_SHIFT_MODE_Pos  (1U)
#define BT_MAC_ISOCNTCNTL_ISO_PHASE_SHIFT_MODE_Msk  (0x1UL << BT_MAC_ISOCNTCNTL_ISO_PHASE_SHIFT_MODE_Pos)
#define BT_MAC_ISOCNTCNTL_ISO_PHASE_SHIFT_MODE  BT_MAC_ISOCNTCNTL_ISO_PHASE_SHIFT_MODE_Msk
#define BT_MAC_ISOCNTCNTL_ISO_CLKSHIFT_MODE_Pos  (2U)
#define BT_MAC_ISOCNTCNTL_ISO_CLKSHIFT_MODE_Msk  (0x1UL << BT_MAC_ISOCNTCNTL_ISO_CLKSHIFT_MODE_Pos)
#define BT_MAC_ISOCNTCNTL_ISO_CLKSHIFT_MODE  BT_MAC_ISOCNTCNTL_ISO_CLKSHIFT_MODE_Msk
#define BT_MAC_ISOCNTCNTL_ISO_UPD_Pos   (30U)
#define BT_MAC_ISOCNTCNTL_ISO_UPD_Msk   (0x1UL << BT_MAC_ISOCNTCNTL_ISO_UPD_Pos)
#define BT_MAC_ISOCNTCNTL_ISO_UPD       BT_MAC_ISOCNTCNTL_ISO_UPD_Msk
#define BT_MAC_ISOCNTCNTL_ISOSAMP_Pos   (31U)
#define BT_MAC_ISOCNTCNTL_ISOSAMP_Msk   (0x1UL << BT_MAC_ISOCNTCNTL_ISOSAMP_Pos)
#define BT_MAC_ISOCNTCNTL_ISOSAMP       BT_MAC_ISOCNTCNTL_ISOSAMP_Msk

/*************** Bit definition for BT_MAC_ISOCNTSAMP register ****************/
#define BT_MAC_ISOCNTSAMP_ISOCNTSAMP_Pos  (0U)
#define BT_MAC_ISOCNTSAMP_ISOCNTSAMP_Msk  (0xFFFFFFFFUL << BT_MAC_ISOCNTSAMP_ISOCNTSAMP_Pos)
#define BT_MAC_ISOCNTSAMP_ISOCNTSAMP    BT_MAC_ISOCNTSAMP_ISOCNTSAMP_Msk

/*************** Bit definition for BT_MAC_ISOCNTCORR register ****************/
#define BT_MAC_ISOCNTCORR_ISOCNTCORR_Pos  (0U)
#define BT_MAC_ISOCNTCORR_ISOCNTCORR_Msk  (0xFFFFFFFFUL << BT_MAC_ISOCNTCORR_ISOCNTCORR_Pos)
#define BT_MAC_ISOCNTCORR_ISOCNTCORR    BT_MAC_ISOCNTCORR_ISOCNTCORR_Msk

/************* Bit definition for BT_MAC_ISOINTCORR_HUS register **************/
#define BT_MAC_ISOINTCORR_HUS_ISOCNTCORR_HUS_Pos  (0U)
#define BT_MAC_ISOINTCORR_HUS_ISOCNTCORR_HUS_Msk  (0x1UL << BT_MAC_ISOINTCORR_HUS_ISOCNTCORR_HUS_Pos)
#define BT_MAC_ISOINTCORR_HUS_ISOCNTCORR_HUS  BT_MAC_ISOINTCORR_HUS_ISOCNTCORR_HUS_Msk

/*************** Bit definition for BT_MAC_ISOINTCNTL register ****************/
#define BT_MAC_ISOINTCNTL_ISOINTMSK_Pos  (0U)
#define BT_MAC_ISOINTCNTL_ISOINTMSK_Msk  (0xFFUL << BT_MAC_ISOINTCNTL_ISOINTMSK_Pos)
#define BT_MAC_ISOINTCNTL_ISOINTMSK     BT_MAC_ISOINTCNTL_ISOINTMSK_Msk

/*************** Bit definition for BT_MAC_ISOINTSTAT register ****************/
#define BT_MAC_ISOINTSTAT_ISOINTSTAT_Pos  (0U)
#define BT_MAC_ISOINTSTAT_ISOINTSTAT_Msk  (0xFFUL << BT_MAC_ISOINTSTAT_ISOINTSTAT_Pos)
#define BT_MAC_ISOINTSTAT_ISOINTSTAT    BT_MAC_ISOINTSTAT_ISOINTSTAT_Msk

/**************** Bit definition for BT_MAC_ISOINTACK register ****************/
#define BT_MAC_ISOINTACK_ISOINTACK_Pos  (0U)
#define BT_MAC_ISOINTACK_ISOINTACK_Msk  (0xFFUL << BT_MAC_ISOINTACK_ISOINTACK_Pos)
#define BT_MAC_ISOINTACK_ISOINTACK      BT_MAC_ISOINTACK_ISOINTACK_Msk

/*************** Bit definition for BT_MAC_ISOGPIOCNTL register ***************/
#define BT_MAC_ISOGPIOCNTL_ISOGPIOMSK_Pos  (0U)
#define BT_MAC_ISOGPIOCNTL_ISOGPIOMSK_Msk  (0xFFUL << BT_MAC_ISOGPIOCNTL_ISOGPIOMSK_Pos)
#define BT_MAC_ISOGPIOCNTL_ISOGPIOMSK   BT_MAC_ISOGPIOCNTL_ISOGPIOMSK_Msk
#define BT_MAC_ISOGPIOCNTL_ISOCPIOBEH_Pos  (31U)
#define BT_MAC_ISOGPIOCNTL_ISOCPIOBEH_Msk  (0x1UL << BT_MAC_ISOGPIOCNTL_ISOCPIOBEH_Pos)
#define BT_MAC_ISOGPIOCNTL_ISOCPIOBEH   BT_MAC_ISOGPIOCNTL_ISOCPIOBEH_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT0 register ***************/
#define BT_MAC_ISOTIMERTGT0_ISOTIMERTGT0_Pos  (0U)
#define BT_MAC_ISOTIMERTGT0_ISOTIMERTGT0_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT0_ISOTIMERTGT0_Pos)
#define BT_MAC_ISOTIMERTGT0_ISOTIMERTGT0  BT_MAC_ISOTIMERTGT0_ISOTIMERTGT0_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT1 register ***************/
#define BT_MAC_ISOTIMERTGT1_ISOTIMERTGT1_Pos  (0U)
#define BT_MAC_ISOTIMERTGT1_ISOTIMERTGT1_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT1_ISOTIMERTGT1_Pos)
#define BT_MAC_ISOTIMERTGT1_ISOTIMERTGT1  BT_MAC_ISOTIMERTGT1_ISOTIMERTGT1_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT2 register ***************/
#define BT_MAC_ISOTIMERTGT2_ISOTIMERTGT2_Pos  (0U)
#define BT_MAC_ISOTIMERTGT2_ISOTIMERTGT2_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT2_ISOTIMERTGT2_Pos)
#define BT_MAC_ISOTIMERTGT2_ISOTIMERTGT2  BT_MAC_ISOTIMERTGT2_ISOTIMERTGT2_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT3 register ***************/
#define BT_MAC_ISOTIMERTGT3_ISOTIMERTGT3_Pos  (0U)
#define BT_MAC_ISOTIMERTGT3_ISOTIMERTGT3_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT3_ISOTIMERTGT3_Pos)
#define BT_MAC_ISOTIMERTGT3_ISOTIMERTGT3  BT_MAC_ISOTIMERTGT3_ISOTIMERTGT3_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT4 register ***************/
#define BT_MAC_ISOTIMERTGT4_ISOTIMERTGT4_Pos  (0U)
#define BT_MAC_ISOTIMERTGT4_ISOTIMERTGT4_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT4_ISOTIMERTGT4_Pos)
#define BT_MAC_ISOTIMERTGT4_ISOTIMERTGT4  BT_MAC_ISOTIMERTGT4_ISOTIMERTGT4_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT5 register ***************/
#define BT_MAC_ISOTIMERTGT5_ISOTIMERTGT5_Pos  (0U)
#define BT_MAC_ISOTIMERTGT5_ISOTIMERTGT5_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT5_ISOTIMERTGT5_Pos)
#define BT_MAC_ISOTIMERTGT5_ISOTIMERTGT5  BT_MAC_ISOTIMERTGT5_ISOTIMERTGT5_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT6 register ***************/
#define BT_MAC_ISOTIMERTGT6_ISOTIMERTGT6_Pos  (0U)
#define BT_MAC_ISOTIMERTGT6_ISOTIMERTGT6_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT6_ISOTIMERTGT6_Pos)
#define BT_MAC_ISOTIMERTGT6_ISOTIMERTGT6  BT_MAC_ISOTIMERTGT6_ISOTIMERTGT6_Msk

/************** Bit definition for BT_MAC_ISOTIMERTGT7 register ***************/
#define BT_MAC_ISOTIMERTGT7_ISOTIMERTGT7_Pos  (0U)
#define BT_MAC_ISOTIMERTGT7_ISOTIMERTGT7_Msk  (0xFFFFFFFFUL << BT_MAC_ISOTIMERTGT7_ISOTIMERTGT7_Pos)
#define BT_MAC_ISOTIMERTGT7_ISOTIMERTGT7  BT_MAC_ISOTIMERTGT7_ISOTIMERTGT7_Msk

#endif
