/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   hw_port.h
@Time    :   2025/03/25 15:02:30
@Brief   :
@Details :
*
************************************************************/

#ifndef __HW_PORT_H__
#define __HW_PORT_H__
#include <rtthread.h>
#include <rtdevice.h>
#include "board.h"
#include "qw_log.h"

#define HW_PORT_LVL               LOG_LVL_DBG
#define HW_PORT_TAG               "HW_PORT"

#if (HW_PORT_LVL >= LOG_LVL_DBG)
    #define HW_PORT_D(...)        QW_LOG_D(HW_PORT_TAG, __VA_ARGS__)
#else
    #define HW_PORT_D(...)
#endif

#if (HW_PORT_LVL >= LOG_LVL_INFO)
    #define HW_PORT_I(...)        QW_LOG_I(HW_PORT_TAG, __VA_ARGS__)
#else
    #define HW_PORT_I(...)
#endif

#if (HW_PORT_LVL >= LOG_LVL_WARNING)
    #define HW_PORT_W(...)        QW_LOG_W(HW_PORT_TAG, __VA_ARGS__)
#else
    #define HW_PORT_W(...)
#endif

#if (HW_PORT_LVL >= LOG_LVL_ERROR)
    #define HW_PORT_E(...)        QW_LOG_E(HW_PORT_TAG, __VA_ARGS__)
#else
    #define HW_PORT_E(...)
#endif

unsigned char * IIC_Master_Init(void);
void IIC_Master_Send(unsigned char byAddr, unsigned char *pData, unsigned short wLen);
void IIC_Master_Receive(unsigned char byAddr, unsigned char *pData, unsigned short wLen);
void Delay_Ms(unsigned char byMilliSec);
void Set_GPIO_State(unsigned char byState);
void IIC_Master_DeInit(void);
extern void BSP_GPIO_Set(int pin, int val, int is_porta);
int HS_Reset(void);
#endif