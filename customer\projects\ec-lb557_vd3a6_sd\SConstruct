import os
import rtconfig

# Check SDK 
SIFLI_SDK = os.getenv('SIFLI_SDK')
if not SIFLI_SDK:
    print("Please run set_env.bat in root folder of SIFLI SDK to set environment.")
    exit()
from building import *



################################## change rtconfig.xxx to customize build ########################################
# print (rtconfig.OUTPUT_DIR)

AddOption("-M", "--ram",
          action="store_true", dest="ram", default=False,
          help="Image in RAM")
 
if (GetOption('ram')==True):
    rtconfig.LINK_SCRIPT = './linker_scripts/link_ram'
    
# Set default compile options
SifliEnv()

if (GetOption('ram')==True):
    rtconfig.OUTPUT_DIR += "ram_"
    rtconfig.LFLAGS += ' --predefine="-DCUSTOM_MEM_MAP" '
    
TARGET = rtconfig.OUTPUT_DIR + rtconfig.TARGET_NAME + '.' + rtconfig.TARGET_EXT
    
env = Environment(tools = ['mingw'],
    AS = rtconfig.AS, ASFLAGS = rtconfig.AFLAGS,
    CC = rtconfig.CC, CCFLAGS = rtconfig.CFLAGS,
    AR = rtconfig.AR, ARFLAGS = '-rc',
    LINK = rtconfig.LINK, LINKFLAGS = rtconfig.LFLAGS)
env.PrependENVPath('PATH', rtconfig.EXEC_PATH)

# prepare building environment
objs = PrepareBuilding(env)

# make a building
DoBuilding(TARGET, objs)
