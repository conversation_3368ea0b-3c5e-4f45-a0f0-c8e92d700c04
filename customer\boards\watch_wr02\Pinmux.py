import os
from openpyxl import load_workbook

#pinmux代码配置文件地址
pinmux_temp_path = "bsp_pinmux.txt"
pinmux_code_path = "bsp_pinmux.c"
#wr02配置文件地址
wr02_config_temp_path = "wr02_board_config.txt"
wr02_config_code_path = "../../../../app/project/wr02_board_config.h"


#item[0],      item[1],   item[2],           item[3],      item[4],           item[5] item[6]
#('PAD_PA00', 'GPIO_A0', 'GPIO_MODE_INPUT', 'PIN_NOPULL', 'VIBRATOR_INT_PIN', 注释     , 0)

#PIN初始化模板, 5,0,1,2,
# pin_init_template1 = """    //{}
#     HAL_PIN_Set({}, {}, {}, {});
# """




# pin_init_template2 = """    GPIO_InitStruct.Pin = {};
#     GPIO_InitStruct.Mode = {};
#     GPIO_InitStruct.Pull = GPIO_NOPULL;
#     HAL_GPIO_Init((GPIO_TypeDef *)hwp_gpio{}, &GPIO_InitStruct);

# """

pin_init_template1 = """    //{}
    qw_gpio_set({}, {}, {});
"""




pin_init_template2 = """    //{}
    qw_special_pin_set({}, {}, {});
"""


# qw_gpio_set(PMIC_I2C_SCL, GPIO_MODE_OUTPUT, GPIO_NOPULL);
# qw_special_pin_set(I2C4_SCL_PIN, I2C4_SCL, PIN_PULLUP);


#PIN高阻模板
pin_init_template3 = """    HAL_PIN_Set_Analog({}, {});// {}
"""

##PIN抽象模板
pin_init_template4 = """
#define {} {}//{}
"""

#通信接口抽象模板
pin_init_template5 = """
#define {} "{}"
"""

#hcpu组初始化标志字符串
hcpu_init_flag = "hcpu_init_group_@_@"

#lcpu组初始化标志字符串
lcpu_init_flag = "lcpu_init_group_@_@"

#高阻组初始化标志字符串
hcpu_highz_init_flag = "hcpu_highz_init_group_@_@"
#高阻组初始化标志字符串
lcpu_highz_init_flag = "lcpu_highz_init_group_@_@"

#配置组初始化标志字符串
wr0_board_config_hcpu_flag = "wr0_board_config_hcpu_group_@_@"
wr0_board_config_lcpu_flag = "wr0_board_config_lcpu_group_@_@"

#通信接口配置组初始化标志字符串
communication_config_hcpu_flag = "communication_config_hcpu_group_@_@"
communication_config_lcpu_flag = "communication_config_lcpu_group_@_@"

#HCPU偏移量
HCPU_LIST_OFFSET = 21
#HCPU IO偏移量
HCPU_IO_OFFSET = 96

#HCPU初始化列表
hcpu_init_list = []

#LCPU初始化列表
lcpu_init_list = []

#高阻列表
highz_list = []

#hcpu初始化代码
hcpu_io_init_code = []


#lcpu初始化代码
lcpu_io_init_code = []


#高阻初始化代码
highz_init_code = []

#配置代码
wr0_board_config_code = []
wr0_board_config_hcpu_code = []
wr0_board_config_lcpu_code = []


def read_pinmux_excel(file_path):
    # 读取Excel文件
    workbook = load_workbook(filename=file_path)
    # 其他变量初始化...
    communication_config_hcpu_code = []
    communication_config_lcpu_code = []
    # 读取前两个子表
    sheet1 = workbook.worksheets[0]
    sheet2 = workbook.worksheets[1]

    #############获取hcpu列表#####################
    # 遍历第一个工作表的行
    for index, row in enumerate(sheet1.iter_rows(min_row=2, values_only=True), start=2):  # 从第2行开始
        func_sel = row[14]  # 第6列是功能选择
        disc_sel = row[18]  # 第18列是说明
        if func_sel :  # 如果功能选择列有值
            if index - 2 - HCPU_LIST_OFFSET < 0:
                #报错
                print("Error: HCPU number exceeds the maximum limit")
                exit(1)
            else:
                hcpu_init_list.append((row[0], row[14], row[15], row[16], row[17], row[18], index - 2 - HCPU_LIST_OFFSET))  # 添加当前行号
    
    # 遍历第二个工作表的行
    for index, row in enumerate(sheet2.iter_rows(min_row=2, values_only=True), start=2):  # 从第2行开始
        func_sel = row[14]  # 第14列是功能选择
        disc_sel = row[19]  # 第18列是说明
        hcpu_sel = row[17]  # 第17列是hcpu选择
        # 如果功能选择列有值,并且hcpu选择列有值
        if func_sel and (hcpu_sel == "是" or hcpu_sel == "同时") : 
            hcpu_init_list.append((row[0],row[14], row[15], row[16], row[18], row[19], index - 2 + HCPU_IO_OFFSET ))  # 添加当前行号
    
    #############获取lcpu列表#####################
    # 遍历第二个工作表的行
    for index, row in enumerate(sheet2.iter_rows(min_row=2, values_only=True), start=2):  # 从第2行开始
        func_sel = row[14]  # 第14列是功能选择
        hcpu_sel = row[17]  # 第17列是hcpu选择
        disc_sel = row[19]  # 第19列是说明
        # 如果功能选择列有值,并且hcpu选择列有值
        if func_sel and (hcpu_sel != "是" or hcpu_sel == "同时") :  
            lcpu_init_list.append((row[0],row[14], row[15], row[16], row[18], row[19], index - 2 + HCPU_IO_OFFSET ))  # 添加当前行号
    #############获取高阻列表#####################  
    # 遍历第一个工作表的行
    for index, row in enumerate(sheet1.iter_rows(min_row=2, values_only=True), start=2):  # 从第2行开始
        func_sel = row[14]  # 第14列是功能选择
        disc_sel = row[18]  # 第18列是说明
        if (func_sel == None) and (disc_sel != "默认"):  # 如果功能选择列有值
            highz_list.append((row[0], index - 2 - HCPU_LIST_OFFSET,row[18]))  # 添加行号
    # 遍历第二个工作表的行
    for index, row in enumerate(sheet2.iter_rows(min_row=2, values_only=True), start=2):  # 从第2行开始
        func_sel = row[14]  # 第14列是功能选择
        disc_sel = row[19]  # 第19列是说明
        if (func_sel == None) and (disc_sel != "默认"):  # 如果功能选择列有值
            highz_list.append((row[0], index - 2 + HCPU_IO_OFFSET,row[19]))  # 添加行号

    #############获取大核通信接口抽象列表#####################  
    start_index = 19
    end_index = 35
    # 获取列标题并打印
    headers = [cell.value for cell in sheet1[1][19:35]]  # 获取第1行的第20到35列的标题
    #有值的列表
    hcpu_com_list = []
    # 对有值的列进行获取
    for index, row in enumerate(sheet1.iter_rows(min_row=3, values_only=True), start=2):  # 从第2行开始
        for i, value in enumerate(row[19:35], start=19):  # 从第20列开始
            if value:# 仅打印有值的列
                 communication_config_hcpu_code.append(pin_init_template5.format(value, headers[i-19]))
                 # 打印有值的列
                 if i not in hcpu_com_list:  # 如果hcpu_com_list没有这个值就加入
                    # 获取要添加的值
                    value_to_add = pin_init_template4.format(headers[i-19].upper()+'_BAUDRATE   ', sheet1.cell(row=2, column=i+1).value, "")

                    # 检查是否已经添加
                    if value_to_add not in hcpu_com_list:
                        hcpu_com_list.append(value_to_add)  # 添加值和对应的列标题

    #print("hcpu_com_list:", hcpu_com_list) #接口列表暂时没用
    communication_config_hcpu_code = hcpu_com_list + communication_config_hcpu_code
    
    #############获取小核通信接口抽象列表#####################  
    headers = [cell.value for cell in sheet2[1][20:28]]  # 获取第1行的第20到35列的标题
    #有值的列表
    lcpu_com_list = []
    # 对有值的列进行获取
    for index, row in enumerate(sheet2.iter_rows(min_row=3, values_only=True), start=2):  # 从第2行开始
        for i, value in enumerate(row[20:28], start=20):  # 从第20列开始
            if value:  # 仅打印有值的列
                communication_config_lcpu_code.append(pin_init_template5.format(value, headers[i-20]))
                # 打印有值的列
                if i not in lcpu_com_list:  # 如果lcpu_com_list没有这个值就加入
                    # 获取要添加的值
                    value_to_add = pin_init_template4.format(headers[i-20].upper()+'_BAUDRATE   ', sheet2.cell(row=2, column=i+1).value, "")
                    # 检查是否已经添加
                    if value_to_add not in lcpu_com_list:
                        lcpu_com_list.append(value_to_add)  # 添加值和对应的列标题
    #print("lcpu_com_list:", lcpu_com_list) #接口列表暂时没用
    communication_config_lcpu_code = lcpu_com_list + communication_config_lcpu_code
    #打印三个列表
    print("hcpu_init_list:\n", hcpu_init_list)
    print("lcpu_init_list:\n", lcpu_init_list)
    #print("highz_list:\n", highz_list)
    #print("communication_config_hcpu_code:", communication_config_hcpu_code)
    #print("communication_config_lcpu_code:", communication_config_lcpu_code)

    return hcpu_init_list, lcpu_init_list, highz_list,communication_config_hcpu_code, communication_config_lcpu_code


#item[0],      item[1],   item[2],           item[3],      item[4],           item[5]
#('PAD_PA00', 'GPIO_A0', 'GPIO_MODE_INPUT', 'PIN_NOPULL', 'VIBRATOR_INT_PIN', None

def generate_code(hcpu_init_list, lcpu_init_list, highz_list,com_config_hcpu_code,com_config_lcpu_code):
    hcpu_io_init_code = []  # 初始化列表
    lcpu_io_init_code = []  # 初始化列表
    hcpu_highz_init_code = []  # 初始化列表
    lcpu_highz_init_code = []  # 初始化列表
    wr0_board_config_code = []  # 初始化列表
    ###############大核初始化代码#################
    for item in hcpu_init_list: #5,0,1,2,
#item[0],      item[1],   item[2],           item[3],      item[4],           item[5] item[6]
#('PAD_PA00', 'GPIO_A0', 'GPIO_MODE_INPUT', 'PIN_NOPULL', 'VIBRATOR_INT_PIN', 注释     , 0)
        #如果是通用io
        if  'GPIO_' in item[1] and item[5] != '默认':#
            hcpu_io_init_code.append(pin_init_template1.format(item[5],item[4], item[2], item[3]))
        elif 'GPIO_' not in item[1] and item[5] != '默认':
            hcpu_io_init_code.append(pin_init_template2.format(item[5],item[4],item[1], item[3]))
        else:
            print("Error: item[0]:", item[0])
    #print("hcpu_io_init_code:", hcpu_io_init_code)
    #换行
    #print("-------------------------------------------------------------------")
    ###############小核初始化代码#################
    for item in lcpu_init_list: #5,0,1,2,
        if  'GPIO_' in item[1] and item[5] != '默认':#
            lcpu_io_init_code.append(pin_init_template1.format(item[5],item[4], item[2], item[3]))
        elif 'GPIO_' not in item[1] and item[5] != '默认':
            lcpu_io_init_code.append(pin_init_template2.format(item[5],item[4],item[1], item[3]))
        else:
            print("Error: item[0]:", item[0])
    #print("lcpu_io_init_code:", lcpu_io_init_code)
    #print("-------------------------------------------------------------------")
    ###############高阻初始化代码#################
    for item in highz_list: #5,0,1,2,
        # 使用append代替+操作，避免类型错误
        if 'PAD_PA' in item[0]:
            hcpu_highz_init_code.append(pin_init_template3.format(item[0],'1',item[2]))
        else:
            lcpu_highz_init_code.append(pin_init_template3.format(item[0],'0',item[2]))
    #print("highz_init_code:", highz_init_code)
    #print("-------------------------------------------------------------------")



    ###############大核 wr0_board_config初始化代码#################
    for item in hcpu_init_list: #5,0,1,2,
        if item[4] != None:
            wr0_board_config_hcpu_code.append(pin_init_template4.format(item[4], item[6],item[0]))
    
    #print("wr0_board_config_code:", wr0_board_config_hcpu_code)
    #print("-------------------------------------------------------------------")
    ###############小核 wr0_board_config初始化代码#################
    for item in lcpu_init_list: #5,0,1,2,
        if item[4] != None:
            wr0_board_config_lcpu_code.append(pin_init_template4.format(item[4], item[6],item[0]))
    #print("wr0_board_config_code:", wr0_board_config_lcpu_code)
    #print("-------------------------------------------------------------------")

    #最终写入
    ###############pinmux初始化代码#################
    temp_code = ''
    # 读取pinmux_temp_path文件内容并存储到temp_code变量中，指定编码为utf-8
    with open(pinmux_temp_path, 'r', encoding='utf-8') as f:
        temp_code = f.read()
    #将temp_code中的hcpu_init_flag替换为hcpu_io_init_code中的内容
    temp_code = temp_code.replace(hcpu_init_flag,''.join(hcpu_io_init_code))
    #将temp_code中的lcpu_init_flag替换为lcpu_io_init_code中的内容
    temp_code = temp_code.replace(lcpu_init_flag,''.join(lcpu_io_init_code))
    #将temp_code中的highz_init_flag替换为highz_init_code中的内容
    temp_code = temp_code.replace(hcpu_highz_init_flag,''.join(hcpu_highz_init_code))
    #将temp_code中的highz_init_flag替换为highz_init_code中的内容
    temp_code = temp_code.replace(lcpu_highz_init_flag,''.join(lcpu_highz_init_code))
    #print("temp_code:", temp_code)
        #将代码中的None替换为空字符串
    temp_code = temp_code.replace('None','')
    #写入到pinmux_code_path文件中
    with open(pinmux_code_path, 'w', encoding='utf-8') as f:
        f.write(temp_code)
    ###############wr02confi抽象代码#################
    temp_code = ''
    # 读取pinmux_temp_path文件内容并存储到temp_code变量中，指定编码为utf-8
    with open(wr02_config_temp_path, 'r', encoding='utf-8') as f:
        temp_code = f.read()
    #HCPU引脚抽象
    temp_code = temp_code.replace(wr0_board_config_hcpu_flag,'//*************引脚接口抽象****************'+''.join(wr0_board_config_hcpu_code)+'//*************通信接口抽象****************'+''.join(com_config_hcpu_code))
    #HCPU通信接口抽象

    #LCPU引脚抽象
    temp_code = temp_code.replace(wr0_board_config_lcpu_flag,'//*************引脚接口抽象****************'+''.join(wr0_board_config_lcpu_code)+'//*************通信接口抽象****************'+''.join(com_config_lcpu_code))
    #LCPU通信接口抽象

    #将代码中的None替换为空字符串
    temp_code = temp_code.replace('None','')

    #print("temp_code:", temp_code)
    #写入到pinmux_code_path文件中
    with open(wr02_config_code_path, 'w', encoding='utf-8') as f:
        f.write(temp_code)

    return hcpu_io_init_code, lcpu_io_init_code, highz_init_code, wr0_board_config_code
# 主函数
if __name__ == "__main__":
    #跳转到当前脚本的目录
    os.chdir(os.path.dirname(__file__))
    # Call the function and pass the path of Pinmux.xlsx
    hcpu_init_list, lcpu_init_list, highz_list,com_config_hcpu_code,com_config_lcpu_code = read_pinmux_excel('Pinmux.xlsx')
    generate_code(hcpu_init_list, lcpu_init_list, highz_list,com_config_hcpu_code,com_config_lcpu_code)
