#ifndef _LTRX140A_H_
#define _LTRX140A_H_

#include <stdint.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>

#ifdef __cplusplus
extern "C" {
#endif

#define LTRX140A_ADDRESS	0x23
#define LTRX140A_PART_ID	0x1c

/****************register***********************/
#define LTRX_CONFIG			0x7f
#define	LTRX_CONTR			0x80
#define	LTRX_RESET			0x81
#define LTRX_INT_TIME		0x85
#define	LTRX_PART_ID		0x86
#define LTRX_MANUFAC_ID		0x87
#define LTRX_STATUS			0x88
#define LTRX_IR_DATA_LSB	0x89
#define LTRX_IR_DATA_MSB	0x8a
#define LTRX_DATA_LSB		0x8b
#define LTRX_DATA_MSB		0x8c
#define LTRX_SAR			0x95
#define LTRX_MAIN_CONTR		0xad
#define LTRX_DARK_CONFIG	0xb9


typedef enum {
    LTRX140A_NOMAL_MODE = 0,
    LTRX140A_SLEEP_MODE = 1
} LTRX140A_PowerMode_e;

//初始化
extern int32_t LTRX140A_Init(void);
//反初始化
extern uint8_t LTRX140A_CheckID(void);
//读取数据
extern int32_t LTRX140A_ReadResult(uint16_t *pdata);
//切换模式
extern int32_t LTRX140A_Switch_PowerMode(LTRX140A_PowerMode_e mode);

#ifdef __cplusplus
}
#endif

#endif /* DRIVERS_I2C_DEVICE_ALS_ALS_LTRX140A_H_ */
