/**
  ******************************************************************************
  * @file   rm69090.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the rm69090.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __SPD2012_H
#define __SPD2012_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"
#include "rtconfig.h"
/** @addtogroup BSP
  * @{
  */
#ifdef TOUCH_RESET_PIN
#define  SPD2012_TP_RST    TOUCH_RESET_PIN
#else
#define  SPD2012_TP_RST    86  //Obsolete
#endif /* TOUCH_RESET_PIN */


#define  SPD2012_I2C_ADDR           (0x53)  /* I2C slave address */

/** @addtogroup Components
  * @{
  */

/** @addtogroup SPD2012
  * @{
  */

/** @defgroup SPD2012_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup SPD2012_Exported_Constants
  * @{
  */

/**
  * @brief SPD2012 chip IDs
  */
#define SPD2012_ID                  0x1190a7

/**
  * @brief  SPD2012 Size
  */
#define  SPD2012_LCD_PIXEL_WIDTH    (454)
#define  SPD2012_LCD_PIXEL_HEIGHT   (454)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define SPD2012_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define SPD2012_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define SPD2012_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  SPD2012 Registers
  */
#define SPD2012_SW_RESET           0x01
#define SPD2012_LCD_ID             0x04
#define SPD2012_DSI_ERR            0x05
#define SPD2012_POWER_MODE         0x0A
#define SPD2012_SLEEP_IN           0x10
#define SPD2012_SLEEP_OUT          0x11
#define SPD2012_PARTIAL_DISPLAY    0x12
#define SPD2012_DISPLAY_INVERSION  0x21
#define SPD2012_DISPLAY_OFF        0x28
#define SPD2012_DISPLAY_ON         0x29
#define SPD2012_WRITE_RAM          0x2C
#define SPD2012_READ_RAM           0x2E
#define SPD2012_CASET              0x2A
#define SPD2012_RASET              0x2B
#define SPD2012_PART_CASET         0x30
#define SPD2012_PART_RASET         0x31
#define SPD2012_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define SPD2012_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define SPD2012_TEARING_EFFECT     0x35
#define SPD2012_NORMAL_DISPLAY     0x36
#define SPD2012_IDLE_MODE_OFF      0x38
#define SPD2012_IDLE_MODE_ON       0x39
#define SPD2012_COLOR_MODE         0x3A
#define SPD2012_CONTINUE_WRITE_RAM 0x3C
#define SPD2012_WBRIGHT            0x51 /* Write brightness*/
#define SPD2012_RBRIGHT            0x53 /* Read brightness*/
#define SPD2012_PORCH_CTRL         0xB2
#define SPD2012_FRAME_CTRL         0xB3
#define SPD2012_GATE_CTRL          0xB7
#define SPD2012_VCOM_SET           0xBB
#define SPD2012_LCM_CTRL           0xC0
#define SPD2012_SET_TIME_SRC       0xC2
#define SPD2012_SET_DISP_MODE      0xC4
#define SPD2012_VCOMH_OFFSET_SET   0xC5
#define SPD2012_FR_CTRL            0xC6
#define SPD2012_POWER_CTRL         0xD0
#define SPD2012_PV_GAMMA_CTRL      0xE0
#define SPD2012_NV_GAMMA_CTRL      0xE1
#define SPD2012_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup SPD2012_Exported_Functions
  * @{
  */
void     SPD2012_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t SPD2012_ReadID(LCDC_HandleTypeDef *hlcdc);

void     SPD2012_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     SPD2012_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void SPD2012_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void SPD2012_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void SPD2012_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t SPD2012_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void SPD2012_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void SPD2012_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

void SPD2012TP_INIT(void);
void spd2012tp_reset(void);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __SPD2012_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
