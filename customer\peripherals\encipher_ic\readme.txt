encipher_ic文件层级结构：
	|-----hal
		|-----bluetooth
		|-----device_info
		|-----os
		|-----se
			|-----hsc32i1
			|-----se_v1
				|-----vendor_se_v1.h
			|-----se_v2
				|-----vendor_se_v2.h
		|-----ble_csi_layer.c
	|-----include
	|-----lib
	|-----alipay_app.c
	|-----alipay_app.h
	|-----readme.txt

/*************************************************************************************************************************************************************************************/
注意：include，lib，hal\bluetooth，hal\device_info，hal\os，hal\se\se_v1\vendor_se_v1.h，hal\se\se_v2\vendor_se_v2.h这几个文件夹是由支付宝的SDK里面提供，除非更新
最新的SDK包，否则不需要更改。
/*************************************************************************************************************************************************************************************/
驱动层需要看：

hal\se\hsc32i1 下的文件是实现ZFB对应的csi接口，如果需要更换加密IC的话，只需要替换该目录下的东西。目前获取的方式有两种：
	1.SOC厂商和加密IC厂商调好后提供IC包给到我们，然后做对应的替换即可。（一般只需要修改Pin脚和“I2C name”）
	2.加密IC提供原生SDK，然后又我们和加密IC厂商联调后导入。

hal\se\se_v1\vendor_se_v1.h和hal\se\se_v1\vendor_se_v1.h需要根据加密IC实际的设定而选择（可以问加密IC厂商或者支付宝）
/*************************************************************************************************************************************************************************************/

/*************************************************************************************************************************************************************************************/
蓝牙层需要看：

hal\ble_csi_layer.c 文件是需要我们蓝牙的小伙伴去适配支付宝的csi蓝牙接口，目前应该需要csi_ble_write的实现，和按照支付宝的要求去连接上手机。
/*************************************************************************************************************************************************************************************/

/*************************************************************************************************************************************************************************************/
UI层和驱动层需要看：

alipay_app.c和alipay_app.h 是属于应用层的实现，将支付宝的所有功能封装成了对应的接口，这个是需要驱动的小伙伴做的，做完后通过接口文档给到UI的小伙伴。

详细的UI小伙伴请看飞书如下连接：
/*************************************************************************************************************************************************************************************/



