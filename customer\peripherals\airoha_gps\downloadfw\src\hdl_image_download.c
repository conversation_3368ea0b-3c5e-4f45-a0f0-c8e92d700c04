/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : hdl_image_download.c
*<AUTHOR> igpsport
*@Date : 2024/01/01
*@Description : GPS固件升级相关功能实现
************************************************************************
*/

/* 包含头文件 */
#include "hdl_api.h"                  /* HDL API接口 */
#include "hdl_image_download.h"       /* 固件下载相关定义 */
#include "airoha_ag333x.h"           /* AG333X GPS芯片相关定义 */
#include "rtthread.h"                 /* RT-Thread核心头文件 */
#include "rtdevice.h"                /* RT-Thread设备相关头文件 */
#include "ff.h"                      /* FatFS文件系统头文件 */
#include "subscribe_service.h"
#include "qw_log.h"
#include "cfg_gps.h"
#include "qw_fs.h"
#include "qw_ota.h"

#define HDL_IMAGE_LOG(...) QW_LOG_W("gns_update", __VA_ARGS__)

/* 全局变量定义 */
static hdl_da_info_t da_info;        /* DA信息结构体 */
static hdl_da_report_t da_report;    /* DA报告结构体 */

static GNSS_UPDATED_STATUS fw_updated = GNSS_UPDATE_IDLE;  /* 固件升级状态 */
static uint8_t fw_updated_pct = 0;   /* 固件升级进度百分比 */
static uint16_t gps_firmware_version = 0x0001; /* 固件版本 */
static char current_gps_path[128] = GPS_IMG_PATH; /* 当前GPS升级路径 */
#define RSC_BIN_FILENAME          "0:/WR02.rsc"
#define GNSS_FILE_NUM 5

const char* g_fileBinName[GNSS_FILE_NUM] = {
    "bootloader.bin",
    "gnss_config.bin",
    "gnss_demo.bin",
    "partition_table.bin",
    "slave_da_UART.bin"
    };

/* 升级完成信号量 */
static rt_sem_t fw_updated_sem = RT_NULL;

/**
 * @brief 初始化回调函数
 * @param usr_arg 用户参数
 */
void init_cb_demo(void *usr_arg)
{
    if (usr_arg != NULL && strlen((const char *)usr_arg) > 0) {
        HDL_IMAGE_LOG("%s", usr_arg);
    }
}

/**
 * @brief DA发送回调函数
 * @param usr_arg 用户参数
 * @param sent_bytes 已发送字节数
 * @param da_total_bytes 总字节数
 */
void da_send_cb_demo(void *usr_arg, uint32_t sent_bytes, uint32_t da_total_bytes)
{
    uint8_t percent = sent_bytes * 100 / da_total_bytes;
    fw_updated_pct = percent * 8 / 100;  // 更新固件升级进度
    //HDL_IMAGE_LOG("DA Progress %d%% (%d/%d Bytes)", percent, sent_bytes, da_total_bytes);
}

/**
 * @brief 下载回调函数
 * @param usr_arg 用户参数
 * @param cur_image_name 当前镜像名称
 * @param sent_bytes 已发送字节数
 * @param total_bytes 总字节数
 */
void download_cb_demo(void *usr_arg, char *cur_image_name, uint32_t sent_bytes, uint32_t total_bytes)
{
    uint8_t percent = sent_bytes * 100 / total_bytes;
    if (strcmp(cur_image_name, "gnss_demo") == 0) {
        fw_updated_pct = percent * 52 / 100 + 48;  // 更新固件升级进度
    }

    //HDL_IMAGE_LOG("Image (%s) Progress %d%% (%d/%d Bytes)",cur_image_name, percent, sent_bytes, total_bytes);
}

/**
 * @brief 连接设备并初始化DA下载
 */
void hdl_image_download_connect(void)
{
    da_info.da_flash_addr = HDL_DA_FLASH_POS;
    da_info.da_run_addr = HDL_DA_RUN_ADDR;
    da_info.da_len = HDL_DA_SIZE;
    hdl_connect_arg_t connect_arg;
    connect_arg.conn_da_init_cb = init_cb_demo;
    connect_arg.conn_da_init_cb_arg = "Download DA now...";
    connect_arg.conn_da_send_cb = da_send_cb_demo;
    connect_arg.conn_da_send_cb_arg = "";
    connect_arg.conn_enable_log = FALSE;
    memset((void *)&da_report, 0, sizeof(da_report));

    /* 连接设备 */
    bool success = hdl_connect(&da_info, &connect_arg, &da_report);
    HDL_IMAGE_LOG("hdl_connect %d",success);
}

/**
 * @brief 进度回调函数
 * @param usr_arg 用户参数
 * @param percent 进度百分比
 */
void progress_cb_demo(void *usr_arg, uint8_t percent)
{
    fw_updated_pct = percent * 40 / 100 + 8;  // 更新固件升级进度
    //HDL_IMAGE_LOG("%s %d%%", (usr_arg != NULL ? usr_arg : "percent "), percent);
}

/**
 * @brief 格式化Flash
 */
static bool hdl_image_download_format(void)
{
    /* 格式化所有区域 */
    hdl_format_arg_t format_arg;
    format_arg.format_init_cb = init_cb_demo;
    format_arg.format_init_cb_arg = "Format All now...";
    format_arg.format_progress_cb = progress_cb_demo;
    format_arg.format_progress_cb_arg = "Format Progress";
    format_arg.format_addr = da_report.flash_base_addr;
    format_arg.format_size = da_report.flash_size;
    bool ret = hdl_format(&format_arg);
    HDL_IMAGE_LOG("hdl_format %d",ret);
    return ret;
}

/**
 * @brief 加载并下载固件镜像
 */
static bool hdl_image_download_load_image(void)
{
    /* 下载4个镜像文件 */
    uint32_t demo_size = 0;
    uint32_t bootloader_size = 0;
    FIL* fp = NULL;
    if(fp == NULL)
    {
        fp = (FIL *)rt_malloc(sizeof(FIL));
    }
    RT_ASSERT(fp != NULL);

    /* 获取bootloader和demo镜像大小 */
    char tmp[64];
    sprintf(tmp, "%s%s.bin", current_gps_path, HDL_BL_IMAGE_NAME);
    if (f_open(fp, tmp, FA_OPEN_EXISTING|FA_READ) != FR_OK)
    {
        HDL_IMAGE_LOG("hdl_image_download_load_image open %s failed", tmp);
        rt_free(fp);
        return false;
    }
    bootloader_size = f_size(fp);
    HDL_IMAGE_LOG("bootloader_size %d",bootloader_size);
    f_close(fp);
    memset((void *)fp, 0, sizeof(FIL));
    sprintf(tmp, "%s%s.bin", current_gps_path, HDL_GNSS_DEMO_IMAGE_NAME);
    if (f_open(fp, tmp, FA_OPEN_EXISTING|FA_READ) != FR_OK)
    {
        HDL_IMAGE_LOG("hdl_image_download_load_image open %s failed", tmp);
        rt_free(fp);
        return false;
    }
    demo_size = f_size(fp);
    HDL_IMAGE_LOG("demo_size %d",demo_size);
    f_close(fp);
    rt_free(fp);

    /* 配置4个镜像信息 */
    hdl_image_t partition_image;
    hdl_image_t bl_image;
    hdl_image_t cm4_image;
    hdl_image_t config_image;

    /* 分区表镜像 */
    partition_image.image_host_flash_addr = HDL_PARTITION_IMAGE_HOST_FLASH_POS;
    partition_image.image_slave_flash_addr = HDL_PARTITION_IMAGE_SLAVE_FLASH_POS;
    partition_image.image_len = HDL_PARTITION_IMAGE_SIZE;
    partition_image.image_name = HDL_PARTITION_IMAGE_NAME;
    partition_image.image_is_bootloader = FALSE;
    partition_image.next = &bl_image;

    /* bootloader镜像 */
    bl_image.image_host_flash_addr = HDL_BL_IMAGE_HOST_FLASH_POS;
    bl_image.image_slave_flash_addr = HDL_BL_IMAGE_SLAVE_FLASH_POS;
    bl_image.image_len = bootloader_size;
    bl_image.image_name = HDL_BL_IMAGE_NAME;
    bl_image.image_is_bootloader = TRUE;
    bl_image.next = &cm4_image;

    /* CM4固件镜像 */
    cm4_image.image_host_flash_addr = HDL_GNSS_DEMO_IMAGE_HOST_FLASH_POS;
    cm4_image.image_slave_flash_addr = HDL_GNSS_DEMO_IMAGE_SLAVE_FLASH_POS;
    cm4_image.image_len = demo_size;
    cm4_image.image_name = HDL_GNSS_DEMO_IMAGE_NAME;
    cm4_image.image_is_bootloader = FALSE;
    cm4_image.next = &config_image;

    /* 配置镜像 */
    config_image.image_host_flash_addr = HDL_GNSS_CONFIG_IMAGE_HOST_FLASH_POS;
    config_image.image_slave_flash_addr = HDL_GNSS_CONFIG_IMAGE_SLAVE_FLASH_POS;
    config_image.image_len = HDL_GNSS_CONFIG_IMAGE_SIZE;
    config_image.image_name = HDL_GNSS_CONFIG_IMAGE_NAME;
    config_image.image_is_bootloader = FALSE;
    config_image.next = NULL;

    /* 开始下载镜像 */
    hdl_download_arg_t download_arg;
    download_arg.download_init_cb = init_cb_demo;
    download_arg.download_init_cb_arg = "Download Images now...";
    download_arg.download_cb = download_cb_demo;
    download_arg.download_cb_arg = "";
    download_arg.download_images = &partition_image;
    bool ret = hdl_download(&download_arg);
    HDL_IMAGE_LOG("hdl_download %d", ret);
    return ret;
}

/**
 * @brief 断开连接
 */
void hdl_image_download_disconnect(void)
{
    bool success = hdl_disconnect(DISCONNECT_RESET);
    HDL_IMAGE_LOG("hdl_disconnect %d",success);
}

/**
 * @brief GPS固件下载
 */
void gnss_fw_download()
{
#ifdef RT_USING_PM
    rt_pm_request(PM_SLEEP_MODE_IDLE);
#ifdef BSP_PM_FREQ_SCALING
    rt_pm_hw_device_start();
#endif
#endif
    fw_updated = GNSS_UPDATE_ING;
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    hdl_data_buf_init();
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    hdl_image_download_connect();
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    if (!hdl_image_download_format())
    {
        fw_updated = GNSS_UPDATE_FAIL;
        HDL_IMAGE_LOG("hdl_image_download_format failed");
        goto exit;
    }
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    if (!hdl_image_download_load_image())
    {
        fw_updated = GNSS_UPDATE_FAIL;
        HDL_IMAGE_LOG("hdl_image_download_load_image failed");
        goto exit;
    }
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    hdl_image_download_disconnect();
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);
    hdl_data_buf_clear();
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);

    ag333x_gps_poweroff();
    HDL_IMAGE_LOG("%s %d", __func__, __LINE__);

    set_gps_soc_version(gps_firmware_version);
    rt_thread_delay(100);
    fw_updated = GNSS_UPDATE_SUCCESS;
    HDL_IMAGE_LOG("download fw finished: %d, version now %d", rt_tick_get(), get_gps_soc_version());

exit:
#ifdef RT_USING_PM
    rt_pm_release(PM_SLEEP_MODE_IDLE);
#ifdef BSP_PM_FREQ_SCALING
    rt_pm_hw_device_stop();
#endif
#endif
}

/**
 * @brief 读取GNSS固件版本号
 * @return uint16_t GNSS固件版本号
 */
uint16_t gnss_version_read()
{
    QW_FIL *fp = NULL;
    uint8_t buffer[2];
    uint16_t result = 0;
    char tmp[64];
    sprintf(tmp, "%sgnss_config.bin", current_gps_path);

    // 打开文件
    if ((result = qw_f_open(&fp, tmp, QW_FA_READ)) != QW_OK) {
        HDL_IMAGE_LOG("gnss_version_check open %s fail ret = %d\n", tmp, result);
        return -1;
    }

    // 移动文件指针到偏移量 0x311 处
    if ((result = qw_f_lseek(fp, 0x311)) != QW_OK) {
        HDL_IMAGE_LOG("gnss_version_check lseek err ret = %d\n", result);
        qw_f_close(fp);
        return -1;
    }

    // 读取 2 个字节的数据
    UINT bytes_read;
    if ((result = qw_f_read(fp, buffer, sizeof(buffer), &bytes_read)) != QW_OK) {
        HDL_IMAGE_LOG("gnss_version_check read err ret = %d\n", result);
        qw_f_close(fp);
        return -1;
    }

    // 关闭文件
    qw_f_close(fp);

    // 将读取的数据拼接成一个十进制数字
    result = (buffer[0] << 8) | buffer[1];

    return result;
}

/**
 * @brief 检查升级文件是否全部存在
 * @return bool
 */
static bool all_files_exist() {
    char file_path[100];
    for (int i = 0; i < GNSS_FILE_NUM; i++) {
        memset(file_path, 0, sizeof(file_path));
        rt_snprintf(file_path, sizeof(file_path), "%s%s", current_gps_path, g_fileBinName[i]);
        QW_FIL *fp;
        if (QW_OK != qw_f_open(&fp, file_path, QW_FA_OPEN_EXISTING | QW_FA_READ)) {
            HDL_IMAGE_LOG("gnss_fw_updated %s open fail!!!\n", file_path);
            return false;
        }
        qw_f_close(fp);
    }
    return true;
}

/**
 * @brief 检查是否需要GPS固件升级
 * @return void
 */
bool gnss_fw_updated_check(void)
{
#ifdef GPS_USING_AG333X
    HDL_IMAGE_LOG("gnss_fw_updated version %d\n", get_gps_soc_version());
    gps_firmware_version = gnss_version_read();
    if (gps_firmware_version == 0xFFFF) {
        return false;
    }

    if (gps_firmware_version != get_gps_soc_version() && all_files_exist())
    {
        return true;
    }
#endif

    return false;
}

/**
 * @brief 获取GPS固件升级状态
 * @return GNSS_UPDATED_STATUS 升级状态
 */
GNSS_UPDATED_STATUS gnss_fw_get_update_status(void)
{
    return fw_updated;
}

/**
 * @brief 获取GPS固件升级进度
 * @return int 升级进度(0-100)
 */
int gnss_fw_updated_pct_get(void)
{
    return fw_updated_pct;
}

/**
 * @brief GPS固件升级接口（可指定升级文件路径）
 * @param folder_path 升级文件所在文件夹路径
 * @return void
 */
void gnss_fw_updated(const char *folder_path)
{
    HDL_IMAGE_LOG("gnss_fw_updated called with folder: %s", folder_path ? folder_path : "NULL");

    // 保存原始路径
    char original_path[128];
    strncpy(original_path, current_gps_path, sizeof(original_path) - 1);
    original_path[sizeof(original_path) - 1] = '\0';

    if (folder_path == NULL) {
        HDL_IMAGE_LOG("gnss_fw_updated: folder_path is NULL, using default path");
        folder_path = GPS_IMG_PATH;  // 使用默认路径
    }

    // 设置新的GPS升级路径（确保以/结尾）
    strncpy(current_gps_path, folder_path, sizeof(current_gps_path) - 1);
    current_gps_path[sizeof(current_gps_path) - 1] = '\0';

    // 确保路径以/结尾
    size_t len = strlen(current_gps_path);
    if (len > 0 && current_gps_path[len - 1] != '/') {
        if (len < sizeof(current_gps_path) - 1) {
            current_gps_path[len] = '/';
            current_gps_path[len + 1] = '\0';
        }
    }

    HDL_IMAGE_LOG("gnss_fw_updated: using GPS path: %s", current_gps_path);

    // 重置升级状态和进度
    fw_updated = GNSS_UPDATE_IDLE;
    fw_updated_pct = 0;

    // 检查指定路径下的升级文件是否存在
    if (!all_files_exist()) {
        HDL_IMAGE_LOG("gnss_fw_updated: not all required files found in %s", current_gps_path);
        fw_updated = GNSS_UPDATE_FAIL;
        // 恢复原始路径
        strncpy(current_gps_path, original_path, sizeof(current_gps_path));
        return;
    }

    // 检查是否需要升级
    if (!gnss_fw_updated_check()) {
        HDL_IMAGE_LOG("gnss_fw_updated: no update needed");
        fw_updated = GNSS_UPDATE_SUCCESS;
        fw_updated_pct = 100;
        // 恢复原始路径
        strncpy(current_gps_path, original_path, sizeof(current_gps_path));
        return;
    }

    HDL_IMAGE_LOG("gnss_fw_updated: starting GPS firmware update from %s", current_gps_path);

    // 执行GPS固件下载（现在会使用current_gps_path）
    gnss_fw_download();

    HDL_IMAGE_LOG("gnss_fw_updated: GPS firmware update completed, status: %d", fw_updated);

    // 恢复原始路径
    strncpy(current_gps_path, original_path, sizeof(current_gps_path));
    HDL_IMAGE_LOG("gnss_fw_updated: restored original GPS path: %s", current_gps_path);
}
