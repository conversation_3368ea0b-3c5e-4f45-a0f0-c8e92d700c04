/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   airoha_ag333x.h
@Time    :   2025/04/03 13:44:13
*
**************************************************************************/
#ifndef __AIROHA_AG333X_H
#define __AIROHA_AG333X_H

#include <stdint.h>
#include <stdbool.h>
#include "gps_api.h"

#ifdef __cplusplus
extern "C" {
#endif

    int ag333x_init(void);
    int ag333x_uninit(void);
    
    void ag333x_gps_cmd_ctrl(enum_airoha_cmd gps_cmd);
    void ag333x_gps_sleep(void);
    void ag333x_gps_wakeup(void);
    void ag333x_gps_poweron(void);
    void ag333x_gps_poweroff(void);
    int ag333x_gps_get(uint8_t* data, uint16_t length);
    int ag333x_gps_set(uint8_t* data, uint16_t length);
    void ag333x_gps_gnss_set(gps_mode_type_e mode);
    void ag333x_config_default(void);
    //void ag333x_gps_sleep_save(void);
    void ag333x_epoc_config(void);

    void airoha_ag333x_poweron_with_chip_reset(void);
    void ag333x_gps_poweroff_with_chipen(void);
    uint32_t ag333x_pair_command_encode(const uint8_t* buf, 
        int32_t buf_len, uint8_t* temp_buf, int32_t temp_buf_len);
    void ag333x_gps_ldo_power_reset(void);
    void ag333x_gps_navi_data_save(void);

    void ag333x_set_time_utc(char* utc_time);
    void ag333x_set_location(char* location);

    void ag333x_set_navigation_mode(char mode);
    void ag333x_gps_mcu_wakeup_gnss(void);
    
    void ag333x_gps_debug_log_start(void);
    void ag333x_gps_debug_log_stop(void);
    void ag333x_gps_pair_debug(uint8_t *pair);
#ifdef __cplusplus
}
#endif

#endif 
