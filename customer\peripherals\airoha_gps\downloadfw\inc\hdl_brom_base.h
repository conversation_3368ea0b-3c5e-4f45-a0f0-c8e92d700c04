/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_BROM_BASE_H__
#define __HDL_BROM_BASE_H__

#include "hdl_channel.h"
#include "hdl_ports/hdl_flash_api.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    BROM_CMD_READ16      = 0xD0,
    BROM_CMD_READ32      = 0xD1,
    BROM_CMD_WRITE16     = 0xD2,
    BROM_CMD_WRITE32     = 0xD4,
    BROM_CMD_JUMP_DA     = 0xD5,
    BROM_CMD_SEND_DA     = 0xD7
} HDL_BROM_CMD;

#if defined(CHIP_7686)
#define HDL_WDT_REG                 0xA2090000
#define HDL_WDT_VAL                 0x0011
#define HDL_DA_RUN_ADDR             0x04009000

#elif defined(CHIP_3335)
#define HDL_WDT_REG                 0xA2080000
#define HDL_WDT_VAL                 0x0010
#define HDL_DA_RUN_ADDR             0x04204000
#endif

#define BROM_OK      0x0000
#define BROM_ERROR   0x1000

bool hdl_brom_start();

bool hdl_brom_disable_wdt();

bool hdl_brom_read16(uint32_t addr, uint16_t *data);
bool hdl_brom_read32(uint32_t addr, uint32_t *data);
bool hdl_brom_write16(uint32_t addr, uint16_t data);
bool hdl_brom_write32(uint32_t addr, uint32_t data);

bool hdl_brom_send_da(const hdl_connect_arg_t *connect_arg,
                      uint32_t da_flash_pos, uint32_t da_start_addr, uint32_t da_len);
bool hdl_brom_jump_da(uint32_t addr);


#ifdef __cplusplus
}
#endif

#endif //__HDL_BROM_BASE_H__

