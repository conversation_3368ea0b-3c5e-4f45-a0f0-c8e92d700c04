#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I $SDK_ROOT/drivers/cmsis/sf32lb56x
#include "rtconfig.h"
#include "mem_map.h"


; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 CODE_START_ADDR CODE_SIZE  {    ; load region size_region
  ER_IROM1 CODE_START_ADDR CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   *(FSymTab)
   *.o (.rodata.*)
  }
  ER_IROM1_EX HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
   *.o (.l1_non_ret_text_*)
   *.o (.l1_non_ret_rodata_*)
  }  

#ifdef BSP_USING_PSRAM
  RW_PSRAM1 PSRAM_DATA_START_ADDR UNINIT{  ; ZI data, retained
    *.o (.l2_ret_data_*)
    *.o (.l2_ret_bss_*)
    *.o (.l2_cache_ret_data_*)
    *.o (.l2_cache_ret_bss_*)    
  }
  RW_PSRAM_NON_RET +0  UNINIT{  ; ZI data, not retained and reused by SRAM retention
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)  
  }
  ScatterAssert((ImageLength(RW_PSRAM1)+ImageLength(RW_PSRAM_NON_RET))<PSRAM_DATA_SIZE)
#endif

  RW_IRAM_RET HPSYS_RETM_BASE HPSYS_RETM_SIZE {  
;    .ANY2 (+RW +ZI)    

   *.o (.l1_ret_text_*)
   *.o (.l1_ret_rodata_*)
   *.o (.bss.retm_bss_*)
   *.o (.retm_data_*)

#ifdef BSP_USING_NOR_FLASH3
   drv_spi_flash.o (+RO)
#endif   
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   bf0_hal_hpaon.o (+RO)
   bf0_hal_mpi.o (+RO)
   bf0_hal_mpi_ex.o (+RO)
   bf0_hal_mpi_psram.o (+RO)
   drv_psram.o (+RO)
   context_rvds.o (+RO)
   drv_common.o (.text.HAL_GetTick)
 
   idle.o (.bss.idle_thread_stack)
   bf0_hal_rcc.o   (+RO)
#if 1//def BSP_USING_PM   
   bf0_pm.o        (.text.sifli_light_handler)
   bf0_pm.o        (.text.sifli_deep_handler)
   bf0_pm.o        (.text.sifli_standby_handler)
   bf0_pm.o        (.text.SystemInitFromStandby)
   bf0_pm.o        (.text.BSP_GPIO_Set)
#endif
   *.o             (.text.SystemPowerOnModeGet)
   bsp_init.o         (+RO)
   bsp_lcd_tp.o       (+RO)
   bsp_pinmux.o       (+RO)
   bsp_power.o        (+RO)
   bf0_hal_gpio.o     (+RO)
   bf0_hal.o          (.text.HAL_Init)
   *.o                (.text.HAL_Delay_us)
   bf0_hal.o          (.text.HAL_Delay_us_)
   *.o                (.text.HAL_MspInit)
   bf0_hal_pinmux.o   (+RO)
   bf0_pin_const.o    (+RO)
   *.o                (.text.rt_memset)
   drv_common.o       (.text.HAL_Delay_us)
   drv_common.o       (.text.rt_hw_us_delay)
 
    
   drv_psram.o(.bss.bf0_psram_handle)
  }

  RW_IRAM0 +0 UNINIT {  ; ZI data, not retained
#ifdef BSP_USING_PM  
    *.o (non_ret) ; non-retention section
    *.o (STACK)   ; ISR stack
#endif
    *.o (.l1_non_ret_data_*)
    *.o (.l1_non_ret_bss_*)
#ifndef BSP_USING_PSRAM
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)
    *.o (.nand_cache)
#endif
}
  RW_IRAM1 +0  {  ; RW data  ,  retained
    ;*.o (Jlink_RTT, +First)
    *.o (.l1_ret_data_*)
    *.o (.l1_ret_bss_*)
   .ANY (+RW +ZI)
  }
  ScatterAssert(ImageLength(RW_IRAM0)+ImageLength(RW_IRAM1)<HCPU_RAM_DATA_SIZE)
}

LR_IROM2 HCPU_FLASH2_IMG_START_ADDR HCPU_FLASH2_IMG_SIZE  {  ; load region size_region
  ER_IROM2 HCPU_FLASH2_IMG_START_ADDR HCPU_FLASH2_IMG_SIZE  {  ; RW data
   *.o (.ROM1_IMG)
   *.o (.ROM3_IMG)
   }
}

LR_IROM3 HCPU_FLASH2_FONT_START_ADDR HCPU_FLASH2_FONT_SIZE  {  ; load region size_region
  ER_IROM3 HCPU_FLASH2_FONT_START_ADDR HCPU_FLASH2_FONT_SIZE  {  ; RW data
   lvsf_font_*.o  (.rodata.*)
   }
}

;LR_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {    ; load region size_region
;  ER_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
;   *.o (.rodata.*)
;  }
;}

;LR_IROM3 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; load region size_region
;  RW_IRAM6 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; RW data
;   *.o (.ROM3_IMG)
;   }
;}


;LR_IROM3 RAM6_BASE RAM6_SIZE  {  ; load region size_region
;  RW_IRAM6 RAM6_BASE RAM6_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER1)
;   *.o (KWS_BIAS)
;  }
;  RW_IRAM7 RAM7_BASE RAM7_SIZE  {  ; RW data
;   *.o (KWS_WT)
;  }
;  RW_IRAM8 RAM8_BASE RAM8_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER2)
;   *.o (KWS_BIAS2)
;  }
;}