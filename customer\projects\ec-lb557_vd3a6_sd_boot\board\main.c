/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-06     zylx         first version
 */

#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include "shell.h"
#include "register.h"
#include "dfu.h"
#include "drv_flash.h"
#include "drv_io.h"
#include "drv_psram.h"

#define SD_PAGE_SIZE    (512)

#define BOOT_INVALID_ADDR 0xFFFFFFFF

#define SDIO_LOGIC_ADDR     (0x68000000)
#define SD_CODE_OFFSET      (0x100000)
#define SD_CODE_SIZE        (256 * 1024)

#define SD_PSRAM_CODE_BASE  (0X60000000)

struct sec_configuration *g_sec_config;

static void clear_interrupt_setting(void)
{
    uint32_t i;
    for (i = 0; i < 16; i++)
    {
        NVIC->ICER[i] = 0xFFFFFFFF;
        __DSB();
        __ISB();
    }
}

void boot_mpu_img(uint8_t *dest)
{
    //__asm("MOV      R0, #0");
    //__asm("MSR      PSPLIM, R0");
    __set_PSPLIM(0);

    __asm("LDR SP, [%0]" :: "r"(dest));
    __asm("LDR PC, [%0, #4]" :: "r"(dest));
}

void dfu_copy_img(uint8_t *dest, uint8_t *src, uint32_t len)
{
    // TODO, define a special base for SD, now use MPI4 base for test
    uint32_t addr = (uint32_t)src - SDIO_LOGIC_ADDR;
    uint32_t size = len + SD_PAGE_SIZE - 1 ;
    rt_device_t dev = rt_device_find("sd0");
    if (dev)
    {
        if (rt_device_open(dev, RT_DEVICE_FLAG_RDWR) == RT_EOK)
        {
            int m = 0;
            for (; m < size / SD_PAGE_SIZE; m++)
            {
                rt_device_read(dev, addr / SD_PAGE_SIZE + m, (uint8_t *)(dest + m * SD_PAGE_SIZE), 1);
            }
        }
        rt_device_close(dev);
    }
    SCB_CleanDCache();
    SCB_InvalidateDCache();
}

void dfu_bootjump(void)
{
    uint32_t i;

    //rt_kprintf("Before disable int\n");

    register rt_base_t ret;
    ret = rt_hw_interrupt_disable();
    clear_interrupt_setting();
    rt_hw_interrupt_enable(ret);
    //rt_kprintf("after disable int\n");

    for (i = 0; i < 8; i++)
        NVIC->ICER[0] = 0xFFFFFFFF;
    for (i = 0; i < 8; i++)
        NVIC->ICPR[0] = 0xFFFFFFFF;
    SysTick->CTRL = 0;
    SCB->ICSR |= SCB_ICSR_PENDNMICLR_Msk;
    SCB->SHCSR &= ~(SCB_SHCSR_USGFAULTACT_Msk | SCB_SHCSR_BUSFAULTACT_Msk | SCB_SHCSR_MEMFAULTACT_Msk);

    //rt_kprintf("CONTROL_SPSEL_Msk ??\n");
    if (CONTROL_SPSEL_Msk & __get_CONTROL())
    {
        __set_MSP(__get_PSP());
        __set_CONTROL(__get_CONTROL() & ~CONTROL_SPSEL_Msk);
    }

    SCB->VTOR = (uint32_t)(SDIO_LOGIC_ADDR + SD_CODE_OFFSET);

    rt_kprintf("Switch bootloader to user code ~~~~\n\n");
    boot_mpu_img((uint8_t *)SD_PSRAM_CODE_BASE);
}

//void dfu_ctrl_boot_to_user_fw(void)
//{
//    if (CONTROL_nPRIV_Msk & __get_CONTROL())
//    {
//        __asm("SVC #0");
//   }
//    else
//    {
//        dfu_bootjump();
//    }
//}


static int dl_mode = 0;

static int bm(int argc, char **argv)
{
    dl_mode = 1;
    return 0;
}
MSH_CMD_EXPORT(bm, boot mode);


int main(void)
{
    int count = 1;
    rt_kprintf("After start: dl_mode=%d", dl_mode);

    g_sec_config = (struct sec_configuration *)QSPI1_MEM_BASE;

    //debug_uart_init();
    rt_thread_mdelay(500);

    rt_device_t dev = rt_device_find("sd0");    // get block device
    while (dev == NULL)
    {
        rt_thread_mdelay(1000);
        dev = rt_device_find("sd0");
    }

#if 0
    rt_kprintf("dl_mode=%d\n", dl_mode);
    uint32_t test_psram = 0x60400000;
    uint32_t test_sd = 0x68100000;
    int i;
    uint32_t *tbuf;
    dfu_copy_img((uint8_t *)test_psram, (uint8_t *)test_sd, 0x1000);
    rt_kprintf("READ SD from 0x%08x to psram 0x%08x done\n", test_sd, test_psram);
    tbuf = (uint32_t *)test_psram;
    for (i = 0; i < 256; i++)
    {
        rt_kprintf("0x%08x ", tbuf[i]);
        if ((i & 7) == 7)
            rt_kprintf("\n");
    }
    while (1)
    {
        rt_thread_mdelay(1000000);
    }
#endif
    rt_kprintf("dl_mode=%d\n", dl_mode);

    if (g_sec_config->magic == SEC_CONFIG_MAGIC && dl_mode == 0)
    {
        uint32_t *codebuf;
        //begin copy code from sd to psram
        //rt_kprintf("Begin copy code\n");
        dfu_copy_img((uint8_t *)SD_PSRAM_CODE_BASE, (uint8_t *)(SDIO_LOGIC_ADDR + SD_CODE_OFFSET), SD_CODE_SIZE);
        codebuf = (uint32_t *)SD_PSRAM_CODE_BASE;
        rt_kprintf("Copy done, SP = 0x%08x, PC = 0x%08x\n", codebuf[0], codebuf[1]);
        //boot_mpu_img((uint8_t *)SD_PSRAM_CODE_BASE);
        dfu_bootjump();
    }

    while (count++)
    {
        rt_thread_mdelay(1000000);
    }

    return RT_EOK;
}

