#ifndef __AUDCODEC_HP_H
#define __AUDCODEC_HP_H

typedef struct
{
    __IO uint32_t ID;
    __IO uint32_t CFG;
    __IO uint32_t IRQ;
    __IO uint32_t RSVD3;
    __IO uint32_t DAC_CFG;
    __IO uint32_t APB_STAT;
    __IO uint32_t RSVD2[6];
    __IO uint32_t DAC_CH0_CFG;
    __IO uint32_t DAC_CH0_CFG_EXT;
    __IO uint32_t DAC_CH1_CFG;
    __IO uint32_t DAC_CH1_CFG_EXT;
    __IO uint32_t RSVD1[4];
    __IO uint32_t DAC_CH0_ENTRY;
    __IO uint32_t DAC_CH1_ENTRY;
    __IO uint32_t DAC_CH0_DEBUG;
    __IO uint32_t DAC_CH1_DEBUG;
    __IO uint32_t DAC_CH0_DC;
    __IO uint32_t DAC_CH1_DC;
    __IO uint32_t RESERVED_IN;
    __IO uint32_t RESERVED_OUT;
} AUDCODEC_HP_TypeDef;


/***************** Bit definition for AUDCODEC_HP_ID register *****************/
#define AUDCODEC_HP_ID_FUNC_Pos         (0U)
#define AUDCODEC_HP_ID_FUNC_Msk         (0xFFFFFFFFUL << AUDCODEC_HP_ID_FUNC_Pos)
#define AUDCODEC_HP_ID_FUNC             AUDCODEC_HP_ID_FUNC_Msk

/**************** Bit definition for AUDCODEC_HP_CFG register *****************/
#define AUDCODEC_HP_CFG_ENABLE_Pos      (0U)
#define AUDCODEC_HP_CFG_ENABLE_Msk      (0x1UL << AUDCODEC_HP_CFG_ENABLE_Pos)
#define AUDCODEC_HP_CFG_ENABLE          AUDCODEC_HP_CFG_ENABLE_Msk
#define AUDCODEC_HP_CFG_DAC_1K_MODE_Pos  (1U)
#define AUDCODEC_HP_CFG_DAC_1K_MODE_Msk  (0x1UL << AUDCODEC_HP_CFG_DAC_1K_MODE_Pos)
#define AUDCODEC_HP_CFG_DAC_1K_MODE     AUDCODEC_HP_CFG_DAC_1K_MODE_Msk
#define AUDCODEC_HP_CFG_EN_DLY_SEL_Pos  (2U)
#define AUDCODEC_HP_CFG_EN_DLY_SEL_Msk  (0x3UL << AUDCODEC_HP_CFG_EN_DLY_SEL_Pos)
#define AUDCODEC_HP_CFG_EN_DLY_SEL      AUDCODEC_HP_CFG_EN_DLY_SEL_Msk

/**************** Bit definition for AUDCODEC_HP_IRQ register *****************/
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_Pos  (0U)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF  AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_Pos  (1U)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF  AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_Pos  (2U)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF  AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_Pos  (3U)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF  AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_Pos  (4U)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF  AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_Pos  (5U)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF  AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_Pos  (6U)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF  AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_Pos  (7U)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF  AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_Pos  (8U)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF  AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_Pos  (9U)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF  AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_Pos  (10U)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF  AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_Pos  (11U)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF  AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_MSK_Pos  (16U)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_APB_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_MSK_Pos  (17U)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_APB_UF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_MSK_Pos  (18U)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_OUT_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_MSK_Pos  (19U)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_OUT_UF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_MSK_Pos  (20U)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_STB_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_MSK_Pos  (21U)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH0_STB_UF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_MSK_Pos  (22U)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_APB_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_MSK_Pos  (23U)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_APB_UF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_MSK_Pos  (24U)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_OUT_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_MSK_Pos  (25U)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_OUT_UF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_MSK_Pos  (26U)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_STB_OF_MSK_Msk
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_MSK_Pos  (27U)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_MSK_Msk  (0x1UL << AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_MSK_Pos)
#define AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_MSK  AUDCODEC_HP_IRQ_DAC_CH1_STB_UF_MSK_Msk

/************** Bit definition for AUDCODEC_HP_DAC_CFG register ***************/
#define AUDCODEC_HP_DAC_CFG_OSR_SEL_Pos  (0U)
#define AUDCODEC_HP_DAC_CFG_OSR_SEL_Msk  (0x7UL << AUDCODEC_HP_DAC_CFG_OSR_SEL_Pos)
#define AUDCODEC_HP_DAC_CFG_OSR_SEL     AUDCODEC_HP_DAC_CFG_OSR_SEL_Msk
#define AUDCODEC_HP_DAC_CFG_OP_MODE_Pos  (3U)
#define AUDCODEC_HP_DAC_CFG_OP_MODE_Msk  (0x3UL << AUDCODEC_HP_DAC_CFG_OP_MODE_Pos)
#define AUDCODEC_HP_DAC_CFG_OP_MODE     AUDCODEC_HP_DAC_CFG_OP_MODE_Msk
#define AUDCODEC_HP_DAC_CFG_PATH_RESET_Pos  (5U)
#define AUDCODEC_HP_DAC_CFG_PATH_RESET_Msk  (0x1UL << AUDCODEC_HP_DAC_CFG_PATH_RESET_Pos)
#define AUDCODEC_HP_DAC_CFG_PATH_RESET  AUDCODEC_HP_DAC_CFG_PATH_RESET_Msk
#define AUDCODEC_HP_DAC_CFG_CLK_SRC_SEL_Pos  (6U)
#define AUDCODEC_HP_DAC_CFG_CLK_SRC_SEL_Msk  (0x1UL << AUDCODEC_HP_DAC_CFG_CLK_SRC_SEL_Pos)
#define AUDCODEC_HP_DAC_CFG_CLK_SRC_SEL  AUDCODEC_HP_DAC_CFG_CLK_SRC_SEL_Msk
#define AUDCODEC_HP_DAC_CFG_CLK_DIV_Pos  (8U)
#define AUDCODEC_HP_DAC_CFG_CLK_DIV_Msk  (0xFFUL << AUDCODEC_HP_DAC_CFG_CLK_DIV_Pos)
#define AUDCODEC_HP_DAC_CFG_CLK_DIV     AUDCODEC_HP_DAC_CFG_CLK_DIV_Msk

/************** Bit definition for AUDCODEC_HP_APB_STAT register **************/
#define AUDCODEC_HP_APB_STAT_DAC_CH0_FIFO_CNT_Pos  (0U)
#define AUDCODEC_HP_APB_STAT_DAC_CH0_FIFO_CNT_Msk  (0xFUL << AUDCODEC_HP_APB_STAT_DAC_CH0_FIFO_CNT_Pos)
#define AUDCODEC_HP_APB_STAT_DAC_CH0_FIFO_CNT  AUDCODEC_HP_APB_STAT_DAC_CH0_FIFO_CNT_Msk
#define AUDCODEC_HP_APB_STAT_DAC_CH1_FIFO_CNT_Pos  (4U)
#define AUDCODEC_HP_APB_STAT_DAC_CH1_FIFO_CNT_Msk  (0xFUL << AUDCODEC_HP_APB_STAT_DAC_CH1_FIFO_CNT_Pos)
#define AUDCODEC_HP_APB_STAT_DAC_CH1_FIFO_CNT  AUDCODEC_HP_APB_STAT_DAC_CH1_FIFO_CNT_Msk

/************ Bit definition for AUDCODEC_HP_DAC_CH0_CFG register *************/
#define AUDCODEC_HP_DAC_CH0_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_HP_DAC_CH0_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_ENABLE_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_ENABLE  AUDCODEC_HP_DAC_CH0_CFG_ENABLE_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DOUT_MUTE_Pos  (1U)
#define AUDCODEC_HP_DAC_CH0_CFG_DOUT_MUTE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_DOUT_MUTE_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DOUT_MUTE  AUDCODEC_HP_DAC_CH0_CFG_DOUT_MUTE_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DEM_MODE_Pos  (2U)
#define AUDCODEC_HP_DAC_CH0_CFG_DEM_MODE_Msk  (0x3UL << AUDCODEC_HP_DAC_CH0_CFG_DEM_MODE_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DEM_MODE  AUDCODEC_HP_DAC_CH0_CFG_DEM_MODE_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_STB_FIFO_CNT_Pos  (4U)
#define AUDCODEC_HP_DAC_CH0_CFG_STB_FIFO_CNT_Msk  (0x7UL << AUDCODEC_HP_DAC_CH0_CFG_STB_FIFO_CNT_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_STB_FIFO_CNT  AUDCODEC_HP_DAC_CH0_CFG_STB_FIFO_CNT_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_HP_DAC_CH0_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_DMA_EN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DMA_EN  AUDCODEC_HP_DAC_CH0_CFG_DMA_EN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_HP_DAC_CH0_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH0_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_ROUGH_VOL  AUDCODEC_HP_DAC_CH0_CFG_ROUGH_VOL_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_HP_DAC_CH0_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH0_CFG_FINE_VOL_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_FINE_VOL  AUDCODEC_HP_DAC_CH0_CFG_FINE_VOL_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_HP_DAC_CH0_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DATA_FORMAT  AUDCODEC_HP_DAC_CH0_CFG_DATA_FORMAT_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_SINC_GAIN_Pos  (17U)
#define AUDCODEC_HP_DAC_CH0_CFG_SINC_GAIN_Msk  (0x1FFUL << AUDCODEC_HP_DAC_CH0_CFG_SINC_GAIN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_SINC_GAIN  AUDCODEC_HP_DAC_CH0_CFG_SINC_GAIN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_GAIN_Pos  (26U)
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_GAIN_Msk  (0x7UL << AUDCODEC_HP_DAC_CH0_CFG_DITHER_GAIN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_GAIN  AUDCODEC_HP_DAC_CH0_CFG_DITHER_GAIN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_EN_Pos  (29U)
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_DITHER_EN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_DITHER_EN  AUDCODEC_HP_DAC_CH0_CFG_DITHER_EN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_CLK_ANA_POL_Pos  (30U)
#define AUDCODEC_HP_DAC_CH0_CFG_CLK_ANA_POL_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_CLK_ANA_POL_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_CLK_ANA_POL  AUDCODEC_HP_DAC_CH0_CFG_CLK_ANA_POL_Msk

/********** Bit definition for AUDCODEC_HP_DAC_CH0_CFG_EXT register ***********/
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_EN_Pos  (0U)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_EN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_EN  AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_EN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_MODE_Pos  (1U)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_MODE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_MODE_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_MODE  AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_MODE_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Pos  (2U)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN  AUDCODEC_HP_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Pos  (3U)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_INTERVAL  AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Msk
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_STAT_Pos  (7U)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_STAT_Msk  (0x3UL << AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_STAT_Pos)
#define AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_STAT  AUDCODEC_HP_DAC_CH0_CFG_EXT_RAMP_STAT_Msk

/************ Bit definition for AUDCODEC_HP_DAC_CH1_CFG register *************/
#define AUDCODEC_HP_DAC_CH1_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_HP_DAC_CH1_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_ENABLE_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_ENABLE  AUDCODEC_HP_DAC_CH1_CFG_ENABLE_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DOUT_MUTE_Pos  (1U)
#define AUDCODEC_HP_DAC_CH1_CFG_DOUT_MUTE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_DOUT_MUTE_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DOUT_MUTE  AUDCODEC_HP_DAC_CH1_CFG_DOUT_MUTE_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DEM_MODE_Pos  (2U)
#define AUDCODEC_HP_DAC_CH1_CFG_DEM_MODE_Msk  (0x3UL << AUDCODEC_HP_DAC_CH1_CFG_DEM_MODE_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DEM_MODE  AUDCODEC_HP_DAC_CH1_CFG_DEM_MODE_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_STB_FIFO_CNT_Pos  (4U)
#define AUDCODEC_HP_DAC_CH1_CFG_STB_FIFO_CNT_Msk  (0x7UL << AUDCODEC_HP_DAC_CH1_CFG_STB_FIFO_CNT_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_STB_FIFO_CNT  AUDCODEC_HP_DAC_CH1_CFG_STB_FIFO_CNT_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_HP_DAC_CH1_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_DMA_EN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DMA_EN  AUDCODEC_HP_DAC_CH1_CFG_DMA_EN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_HP_DAC_CH1_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH1_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_ROUGH_VOL  AUDCODEC_HP_DAC_CH1_CFG_ROUGH_VOL_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_HP_DAC_CH1_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH1_CFG_FINE_VOL_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_FINE_VOL  AUDCODEC_HP_DAC_CH1_CFG_FINE_VOL_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_HP_DAC_CH1_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DATA_FORMAT  AUDCODEC_HP_DAC_CH1_CFG_DATA_FORMAT_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_SINC_GAIN_Pos  (17U)
#define AUDCODEC_HP_DAC_CH1_CFG_SINC_GAIN_Msk  (0x1FFUL << AUDCODEC_HP_DAC_CH1_CFG_SINC_GAIN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_SINC_GAIN  AUDCODEC_HP_DAC_CH1_CFG_SINC_GAIN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_GAIN_Pos  (26U)
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_GAIN_Msk  (0x7UL << AUDCODEC_HP_DAC_CH1_CFG_DITHER_GAIN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_GAIN  AUDCODEC_HP_DAC_CH1_CFG_DITHER_GAIN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_EN_Pos  (29U)
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_DITHER_EN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_DITHER_EN  AUDCODEC_HP_DAC_CH1_CFG_DITHER_EN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_CLK_ANA_POL_Pos  (30U)
#define AUDCODEC_HP_DAC_CH1_CFG_CLK_ANA_POL_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_CLK_ANA_POL_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_CLK_ANA_POL  AUDCODEC_HP_DAC_CH1_CFG_CLK_ANA_POL_Msk

/********** Bit definition for AUDCODEC_HP_DAC_CH1_CFG_EXT register ***********/
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_EN_Pos  (0U)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_EN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_EN  AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_EN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_MODE_Pos  (1U)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_MODE_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_MODE_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_MODE  AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_MODE_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Pos  (2U)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN  AUDCODEC_HP_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Pos  (3U)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Msk  (0xFUL << AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_INTERVAL  AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Msk
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_STAT_Pos  (7U)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_STAT_Msk  (0x3UL << AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_STAT_Pos)
#define AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_STAT  AUDCODEC_HP_DAC_CH1_CFG_EXT_RAMP_STAT_Msk

/*********** Bit definition for AUDCODEC_HP_DAC_CH0_ENTRY register ************/
#define AUDCODEC_HP_DAC_CH0_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_HP_DAC_CH0_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_HP_DAC_CH0_ENTRY_DATA_Pos)
#define AUDCODEC_HP_DAC_CH0_ENTRY_DATA  AUDCODEC_HP_DAC_CH0_ENTRY_DATA_Msk

/*********** Bit definition for AUDCODEC_HP_DAC_CH1_ENTRY register ************/
#define AUDCODEC_HP_DAC_CH1_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_HP_DAC_CH1_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_HP_DAC_CH1_ENTRY_DATA_Pos)
#define AUDCODEC_HP_DAC_CH1_ENTRY_DATA  AUDCODEC_HP_DAC_CH1_ENTRY_DATA_Msk

/*********** Bit definition for AUDCODEC_HP_DAC_CH0_DEBUG register ************/
#define AUDCODEC_HP_DAC_CH0_DEBUG_DATA_OUT_Pos  (0U)
#define AUDCODEC_HP_DAC_CH0_DEBUG_DATA_OUT_Msk  (0xFFFFUL << AUDCODEC_HP_DAC_CH0_DEBUG_DATA_OUT_Pos)
#define AUDCODEC_HP_DAC_CH0_DEBUG_DATA_OUT  AUDCODEC_HP_DAC_CH0_DEBUG_DATA_OUT_Msk
#define AUDCODEC_HP_DAC_CH0_DEBUG_BYPASS_Pos  (16U)
#define AUDCODEC_HP_DAC_CH0_DEBUG_BYPASS_Msk  (0x1UL << AUDCODEC_HP_DAC_CH0_DEBUG_BYPASS_Pos)
#define AUDCODEC_HP_DAC_CH0_DEBUG_BYPASS  AUDCODEC_HP_DAC_CH0_DEBUG_BYPASS_Msk

/*********** Bit definition for AUDCODEC_HP_DAC_CH1_DEBUG register ************/
#define AUDCODEC_HP_DAC_CH1_DEBUG_DATA_OUT_Pos  (0U)
#define AUDCODEC_HP_DAC_CH1_DEBUG_DATA_OUT_Msk  (0xFFFFUL << AUDCODEC_HP_DAC_CH1_DEBUG_DATA_OUT_Pos)
#define AUDCODEC_HP_DAC_CH1_DEBUG_DATA_OUT  AUDCODEC_HP_DAC_CH1_DEBUG_DATA_OUT_Msk
#define AUDCODEC_HP_DAC_CH1_DEBUG_BYPASS_Pos  (16U)
#define AUDCODEC_HP_DAC_CH1_DEBUG_BYPASS_Msk  (0x1UL << AUDCODEC_HP_DAC_CH1_DEBUG_BYPASS_Pos)
#define AUDCODEC_HP_DAC_CH1_DEBUG_BYPASS  AUDCODEC_HP_DAC_CH1_DEBUG_BYPASS_Msk

#define AUDCODEC_HP_DAC_CH0_DC_OFFSET_Pos  (0U)
#define AUDCODEC_HP_DAC_CH0_DC_OFFSET_Msk  (0xFFFFFFUL << AUDCODEC_HP_DAC_CH0_DC_OFFSET_Pos)
#define AUDCODEC_HP_DAC_CH0_DC_OFFSET   AUDCODEC_HP_DAC_CH0_DC_OFFSET_Msk
#define AUDCODEC_HP_DAC_CH1_DC_OFFSET_Pos  (0U)
#define AUDCODEC_HP_DAC_CH1_DC_OFFSET_Msk  (0xFFFFFFUL << AUDCODEC_HP_DAC_CH1_DC_OFFSET_Pos)
#define AUDCODEC_HP_DAC_CH1_DC_OFFSET   AUDCODEC_HP_DAC_CH1_DC_OFFSET_Msk
/************ Bit definition for AUDCODEC_HP_RESERVED_IN register *************/
#define AUDCODEC_HP_RESERVED_IN_CTRL_0_Pos  (0U)
#define AUDCODEC_HP_RESERVED_IN_CTRL_0_Msk  (0xFFUL << AUDCODEC_HP_RESERVED_IN_CTRL_0_Pos)
#define AUDCODEC_HP_RESERVED_IN_CTRL_0  AUDCODEC_HP_RESERVED_IN_CTRL_0_Msk
#define AUDCODEC_HP_RESERVED_IN_CTRL_1_Pos  (8U)
#define AUDCODEC_HP_RESERVED_IN_CTRL_1_Msk  (0xFFUL << AUDCODEC_HP_RESERVED_IN_CTRL_1_Pos)
#define AUDCODEC_HP_RESERVED_IN_CTRL_1  AUDCODEC_HP_RESERVED_IN_CTRL_1_Msk
#define AUDCODEC_HP_RESERVED_IN_CTRL_2_Pos  (16U)
#define AUDCODEC_HP_RESERVED_IN_CTRL_2_Msk  (0xFFUL << AUDCODEC_HP_RESERVED_IN_CTRL_2_Pos)
#define AUDCODEC_HP_RESERVED_IN_CTRL_2  AUDCODEC_HP_RESERVED_IN_CTRL_2_Msk

/************ Bit definition for AUDCODEC_HP_RESERVED_OUT register ************/
#define AUDCODEC_HP_RESERVED_OUT_STAT_Pos  (0U)
#define AUDCODEC_HP_RESERVED_OUT_STAT_Msk  (0xFFUL << AUDCODEC_HP_RESERVED_OUT_STAT_Pos)
#define AUDCODEC_HP_RESERVED_OUT_STAT   AUDCODEC_HP_RESERVED_OUT_STAT_Msk

#endif
