#include <stdint.h>
#include <string.h>
#include "bf0_hal.h"
#include "mem_map.h"
#include "register.h"
#include "bf0_hal_patch.h"
#ifdef HAL_LCPU_PATCH_MODULE
static const unsigned int g_lcpu_patch_list[]= {
	0x50544348, 0x000000C8, 0x00018990, 0xBE24F117, 
	0x0004B550, 0xBAA6F0E7, 0x00013F90, 0xBA5EF11C, 
	0x0003C380, 0xBF00BF00, 0x0002FC00, 0xBE6CF101, 
	0x000083EC, 0xBE80F127, 0x00026258, 0xB808F10B, 
	0x0001E470, 0xBA08F112, 0x00033528, 0xBB16F0FE, 
	0x00036F34, 0xBF58F0FA, 0x00037D3C, 0xB93EF0FA, 
	0x00034868, 0xBA4EF0FD, 0x0000E088, 0xE00C0C08, 
	0x0003B2AC, 0xB8BAF0F7, 0x00033C0C, 0xB83AF0FE, 
	0x0005495C, 0x00130529, 0x00023EC0, 0xB918F10D, 
	0x000189DC, 0xBE10F117, 0x00018A68, 0xBDDCF117, 
	0x0002D654, 0xBEB0F103, 0x00017C38, 0xBC8AF118, 
	0x00014FC4, 0xBA86F11B, 0x0002B578, 0xBEAAF105, 
	0x0002E248, 0xB98EF103, 0x0002FC14, 0xBE92F101, 
};
static const unsigned int g_lcpu_patch_bin[]= {
	0xF001B580, 0x4816FA95, 0x60014916, 0x49174816, 
	0x48176001, 0x60014917, 0x49184817, 0x48186001, 
	0x60014918, 0x49194818, 0x48196001, 0x60014919, 
	0x491A4819, 0x481A6001, 0x6001491A, 0x491B481A, 
	0x481B6001, 0x6001491B, 0x491C481B, 0x481C6001, 
	0x6001491C, 0xA123A01C, 0x4080E8BD, 0xBE60F713, 
	0x2012D6D8, 0x001314F5, 0x2012D410, 0x00131369, 
	0x2012D594, 0x00132591, 0x2012D488, 0x00131DC9, 
	0x2012D5BC, 0x00130321, 0x2012D5D0, 0x001303C5, 
	0x2012D5C4, 0x00130331, 0x2012D5A8, 0x001329D5, 
	0x2012D5B8, 0x001301DD, 0x2012D4FC, 0x00132265, 
	0x2012D420, 0x001307A5, 0x2012D418, 0x00130159, 
	0x2012D4E8, 0x001301AD, 0x63746150, 0x73252868, 
	0x6F682029, 0x69206B6F, 0x6174736E, 0x2E2E6C6C, 
	0x0000000A, 0x61726576, 0x31305F33, 0x00000036, 
	0xD0031C43, 0xBF0C2801, 0x31012100, 0x4B14B5F0, 
	0x36FFF04F, 0xF3C4681C, 0x1F252402, 0x0407F084, 
	0x2500BF38, 0xBF282C03, 0xFA062403, 0xFA06F705, 
	0x43BAF404, 0x3FFFF1B0, 0x0104EA21, 0x040FF000, 
	0xF105FA01, 0x0404F1A4, 0x0102EA41, 0x4A054423, 
	0x030CF103, 0x1141EA4F, 0x1883BFC8, 0xBDF07019, 
	0xE000ED0C, 0xE000E400, 0x4604B51C, 0x4910B928, 
	0xF6FD2002, 0xF6EAFAF7, 0x2004F981, 0x0007F88D, 
	0xFBECF6E8, 0xB1406900, 0xF10D480A, 0xF10D0107, 
	0x68030203, 0x4798202F, 0xBD1CB100, 0x1003F8DD, 
	0x42814805, 0x4620BF04, 0xFA0CF6EA, 0xBF00BD1C, 
	0x000545C8, 0x2012DB84, 0x00090020, 0x460DB570, 
	0xF6D04606, 0x4604FB13, 0xF8504807, 0xB1280026, 
	0x46294630, 0xFD30F6DF, 0xE0004605, 0x4620250C, 
	0xFB08F6D0, 0xBD704628, 0x2012EE18, 0x2801B5B0, 
	0xBB80D02B, 0x4C474D46, 0x28006828, 0x2000D151, 
	0xFBE6F6E8, 0x60282800, 0x8AC1D04B, 0xF44F050A, 
	0xBF586200, 0x6280F44F, 0xF2400589, 0xBF582103, 
	0x1103F240, 0xF7114411, 0xB128FAFF, 0xA03A4988, 
	0x72E7F44F, 0xFE2EF71A, 0x21006828, 0xFBCEF711, 
	0x493A6828, 0xFBE4F711, 0x4C17E02B, 0x002CF894, 
	0x2400B148, 0x4A7EE025, 0xF44F2100, 0x240073F7, 
	0xFE36F71A, 0xF6E8E01D, 0x6941FB6D, 0x490FB909, 
	0xF6FC6141, 0x6020FB6B, 0x4919B930, 0xF240A00C, 
	0xF71012E5, 0x6820FC4D, 0xFBC4F6FC, 0x4914B128, 
	0xF44FA014, 0xF71072F3, 0x2001FC43, 0x002CF884, 
	0x46204C1A, 0xBF00BDB0, 0x2012DC80, 0x0003E5E1, 
	0x5F435049, 0x55455551, 0x4E495F45, 0x494C4156, 
	0x41485F44, 0x454C444E, 0x203D2120, 0x786F626D, 
	0x766E655F, 0x6370692E, 0x726F705F, 0x00000074, 
	0x0013316D, 0x3D3D2030, 0x63706920, 0x6575715F, 
	0x6F5F6575, 0x286E6570, 0x786F626D, 0x766E655F, 
	0x6370692E, 0x726F705F, 0x00002974, 0x00057F8C, 
	0x2012DC50, 0x00133190, 0x455F5452, 0x3D204B4F, 
	0x6572203D, 0x746C7573, 0x00000000, 0x00052625, 
	0xF6D0B580, 0xF6D0FA5B, 0x4847FA5D, 0xBD806940, 
	0x4605B5F8, 0x460C481D, 0xD3044285, 0xA01C4940, 
	0xF71A22B7, 0xF710FD9F, 0xB150F97D, 0x00C5EB05, 
	0x44086821, 0x6021B2C1, 0x4369213D, 0x2010EB01, 
	0xF710BDF8, 0x4606F967, 0x68004838, 0xFBA54780, 
	0x27640100, 0xFBA02300, 0xFB010207, 0xB2F22107, 
	0xFA520052, 0x00D2F286, 0xFE6EF6D0, 0x23002264, 
	0xF6D04605, 0xFB00FE69, 0x68225117, 0x29644411, 
	0xD3DD6021, 0x30013964, 0xBDF86021, 0x001E8480, 
	0x7963706C, 0x73656C63, 0x32203C20, 0x30303030, 
	0x00003030, 0x4604B5B0, 0xF93CF710, 0x4814B160, 
	0xD3044284, 0xA013491A, 0xF71A22A4, 0x02A0FD53, 
	0xFB902164, 0xE019F0F1, 0xF924F710, 0x680A4915, 
	0x6809B112, 0x61514A14, 0xF643B2C0, 0xFB842298, 
	0xFBA00100, 0xFB014002, 0x48100502, 0x47806800, 
	0x46204602, 0x23004629, 0xFE26F6D0, 0xBDB03801, 
	0x003FFFFF, 0x635F7368, 0x3C20746E, 0x78302820, 
	0x46464646, 0x46464646, 0x203E3E20, 0x00293031, 
	0x00133160, 0x4007B040, 0x2012F45C, 0x2012D5BC, 
	0x4604B510, 0xF713A006, 0x2047FC63, 0xFBEEF70F, 
	0x3038490A, 0xE9C44A0A, 0x60A21000, 0xBF00BD10, 
	0x6269735F, 0x5F73656C, 0x6B736174, 0x696E695F, 
	0x61705F74, 0x20686374, 0x00000A0D, 0x00059C4C, 
	0x00190004, 0x460CB5F8, 0x4605490C, 0xF6E42048, 
	0x4606F8A5, 0x46296820, 0x46342210, 0x0738F106, 
	0x0F34F844, 0xF6D04638, 0x4630FADC, 0x46224639, 
	0xB0012304, 0x40F0E8BD, 0xBAEEF6E4, 0x001304D1, 
	0xBA54F6E4, 0x4604B510, 0x0C04F830, 0xFC9EF000, 
	0x0C04F824, 0x0C06F834, 0xFD50F6F0, 0x0C06F824, 
	0x28FFB2C0, 0x4806D106, 0x0C06F844, 0x0C08F834, 
	0x0C02F824, 0x680148EA, 0xE8BD4620, 0x47084010, 
	0x0DAF0007, 0x8081210F, 0x60014902, 0x210048D1, 
	0x47707001, 0x001330A8, 0x460DB5B0, 0x46042101, 
	0xFF3CF6F2, 0xD1092801, 0xFC78F6F0, 0x8869B280, 
	0xBF384288, 0x46204601, 0xFDD4F000, 0xBDB02000, 
	0x4604B5F8, 0xF8504817, 0xF8900024, 0xB130008B, 
	0x21024620, 0x23042200, 0xFF86F6E7, 0x460DE020, 
	0xFC5CF6F0, 0x20004606, 0x22032104, 0x02209000, 
	0x46201D83, 0xFCAAF6FE, 0xF6F04607, 0x8869FC4F, 
	0xBF384288, 0x46204601, 0xF6F3B289, 0xB2B0FC91, 
	0x42888869, 0xBF384638, 0x80794631, 0xF892F6FF, 
	0xBDF82000, 0x2012E9CC, 0xF6D9B580, 0x4804FA4B, 
	0x48046801, 0x48044788, 0xE8BD6800, 0x47004080, 
	0x2012D594, 0x2012DB90, 0x2012D668, 0x4806B580, 
	0x06406800, 0xF04FBF44, 0xBD8030FF, 0x68004803, 
	0x20004780, 0xBF00BD80, 0x4007003C, 0x2012D66C, 
	0xF6D0B5B0, 0x4605F8EB, 0x30284831, 0xF9EFF6E9, 
	0x46284604, 0xF8E6F6D0, 0xBF142C00, 0x200088A0, 
	0xBF00BDB0, 0x4601B570, 0xF64F4822, 0x880072FE, 
	0x102FF200, 0x0001F020, 0x20F1F200, 0x0001F020, 
	0xF0203041, 0x309D0001, 0x30794010, 0x0001F020, 
	0x1051F200, 0x0001F020, 0x1021F200, 0x0001F020, 
	0x100FF200, 0xF6004010, 0xF02030E9, 0x30670001, 
	0x0001F020, 0x1001F600, 0x0001F020, 0xF3C01A08, 
	0x2C082407, 0xA20CD304, 0x23874620, 0xFC18F71A, 
	0xEB064E0F, 0xF10000C4, 0xF6D004F8, 0x4605F89F, 
	0x0028F106, 0xF6E94621, 0x4628F9B3, 0x4070E8BD, 
	0xB898F6D0, 0x00054D08, 0x5F656C62, 0x6C697475, 
	0x6675625F, 0x7461705F, 0x632E6863, 0x00000000, 
	0x2012DFA8, 0x1A4088C0, 0xF080FAB0, 0x47700940, 
	0x88C388CA, 0x88808889, 0x4048405A, 0xFAB04310, 
	0x0940F080, 0x00004770, 0x460CB5B0, 0xB9214605, 
	0xA105A004, 0xF71A2280, 0xFBB5FBBD, 0xFB00F0F4, 
	0xBDB05014, 0x00766964, 0x6D5F6F63, 0x2E687461, 
	0x00000068, 0x491C4B1D, 0x0080EB00, 0x3200F501, 
	0xF203881B, 0xF023132F, 0xF2030301, 0x401A23F1, 
	0x23C0F501, 0x2160F501, 0x401A3241, 0x4011329D, 
	0x0080EB01, 0x5A404901, 0x47700BC0, 0x20130000, 
	0x490D4B0E, 0x0080EB00, 0x3200F501, 0xF203881B, 
	0xF023132F, 0xF2030301, 0x401A23F1, 0x23C0F501, 
	0x2160F501, 0x401A3241, 0x4011329D, 0x0080EB01, 
	0x5A404903, 0x47700AC0, 0x0001FFFE, 0x00054D08, 
	0x2013000C, 0xF6DDB580, 0x200CF947, 0xFFF6F6FC, 
	0x200C490A, 0xFFCEF6FC, 0x2100200C, 0xFF4AF6FC, 
	0xF6FC200D, 0x4906FFEB, 0xF6FC200D, 0x200DFFC3, 
	0xE8BD2100, 0xF6FC4080, 0xBF00BF3D, 0x0013309C, 
	0x00133090, 0x481FB570, 0xEBB07800, 0xBF1C2F13, 
	0xBD702000, 0x461C4888, 0x6806460D, 0xF9E3F6F0, 
	0x46284601, 0x47B04622, 0xBD702001, 0x4882B570, 
	0x460D461C, 0xF6F06806, 0x4601F9D6, 0x46224628, 
	0x200147B0, 0xBF00BD70, 0x460DB570, 0x0A19461C, 
	0x480B23FF, 0x2F14EBB3, 0xD0077081, 0x70194B09, 
	0x80018829, 0x21034610, 0xFF04F6FC, 0x68064872, 
	0xF9B9F6F0, 0x46284601, 0x47B04622, 0xBD702001, 
	0x2012DF6C, 0x201331BC, 0x486BB570, 0x460D461C, 
	0xF6F06806, 0x4601F9A8, 0x46224628, 0x200147B0, 
	0xBF00BD70, 0x4604B5B0, 0x00FFF020, 0x6F60F5B0, 
	0x4293D10C, 0x48C3D00A, 0x23024619, 0xF6406805, 
	0x47A86034, 0x800449C9, 0x47886809, 0xBDB02000, 
	0x2012D410, 0x460DB570, 0x21022003, 0xF6FC461C, 
	0x4855FEC9, 0xF6F06806, 0x4601F97E, 0x46224628, 
	0x200147B0, 0xBF00BD70, 0x484FB570, 0x460D461C, 
	0xF6F06806, 0x4601F970, 0x46224628, 0x200147B0, 
	0xBF00BD70, 0x4848B570, 0x460D461C, 0xF6F06806, 
	0x4601F962, 0x46224628, 0x200147B0, 0xBF00BD70, 
	0x41F0E92D, 0x460C7808, 0xD0232802, 0xD02F2801, 
	0xD13E2800, 0x46194820, 0x68052348, 0x600BF640, 
	0x460547A8, 0x462E203E, 0xF8261D2F, 0x481C0F02, 
	0x6803463A, 0x46312092, 0xB1404798, 0x4638A119, 
	0xF04F220E, 0xF6D0080E, 0xF8A6F88C, 0x78208000, 
	0xE01A7028, 0x46194810, 0x6805230A, 0x600BF640, 
	0x782147A8, 0xF8004605, 0xF6E41B02, 0xE00CFFA1, 
	0x46194809, 0x6805230A, 0x600BF640, 0x782147A8, 
	0xF8004605, 0xF6E41B02, 0x4804FF71, 0x46286801, 
	0x20004788, 0x81F0E8BD, 0x2012D40C, 0x2012D410, 
	0x2012D654, 0x4C464953, 0x4C422D49, 0x45442D45, 
	0x00000056, 0x4814B570, 0x460D461C, 0xF6F06806, 
	0x4601F8FA, 0x46224628, 0x200147B0, 0xBF00BD70, 
	0x480DB570, 0x460D461C, 0xF6F06806, 0x4601F8EC, 
	0x46224628, 0x200147B0, 0xBF00BD70, 0x4806B570, 
	0x460D461C, 0xF6F06806, 0x4601F8DE, 0x46224628, 
	0x200147B0, 0xBF00BD70, 0x2012D450, 0x48FAB5B0, 
	0x4619460C, 0x68052302, 0x600DF640, 0x460547A8, 
	0x28027820, 0x2801D00D, 0xB988D00F, 0x1D228860, 
	0xBF28283E, 0xB2C1203E, 0x680348EE, 0x47982092, 
	0x1CA0E006, 0xFFC8F6E4, 0x8860E002, 0xFF9EF6E4, 
	0x70682000, 0x70287820, 0x680148E8, 0x47884628, 
	0xBDB02000, 0x20A4B57C, 0x0206F10D, 0xF8AD460D, 
	0x20010006, 0xF88CF6F1, 0x28014604, 0x7868D117, 
	0xD9012805, 0xE00F2140, 0xEB014910, 0x68C60080, 
	0x7BF0B14E, 0xD00C2801, 0xD1042803, 0xB9107B70, 
	0x28027BB0, 0x2143D007, 0xF6F12001, 0x4620FB77, 
	0x2002BD7C, 0x683273F0, 0x46294630, 0x28004790, 
	0x4601D0F5, 0x73F02001, 0xBF00E7EE, 0x2012E920, 
	0xA008B5B0, 0x460D461C, 0xF91AF713, 0xB2E0492E, 
	0x4A0B696B, 0xF1058008, 0x46280110, 0xFCCAF7FF, 
	0xBDB02000, 0x6D706167, 0x7365615F, 0x5F36685F, 
	0x5F646D63, 0x646E6168, 0x0A72656C, 0x00000000, 
	0x00130B25, 0x4601B118, 0xF713A012, 0xB5F8B8F9, 
	0x4614A016, 0xF713460D, 0x481AF8F3, 0x23142207, 
	0x48196807, 0xF6408806, 0x463150B3, 0x460747B8, 
	0x4631A016, 0xF7132207, 0x4638F8E3, 0x22104629, 
	0xFF87F6CF, 0x613C48A9, 0x46386801, 0x40F8E8BD, 
	0xBF004708, 0x73656120, 0x616D635F, 0x36682063, 
	0x72726520, 0x2520726F, 0x00000A78, 0x6D706167, 
	0x7365615F, 0x5F36685F, 0x75736572, 0x635F746C, 
	0x00000A62, 0x2012D40C, 0x201331BE, 0x6D706167, 
	0x7365615F, 0x5F36685F, 0x75736572, 0x635F746C, 
	0x64252062, 0x6425202C, 0x0000000A, 0x2012D410, 
	0xB092B570, 0x21104608, 0x1047F88D, 0xF1A17801, 
	0x2A0A02A0, 0x2301D817, 0x64B1F240, 0x42234093, 
	0x4882D009, 0x42887940, 0x4A81D00A, 0x73A7F44F, 
	0xF95EF71A, 0x2A08E004, 0x487CD105, 0x71012100, 
	0xFD56F6E4, 0x2928E0EE, 0x2903D011, 0x291AD02C, 
	0x291BD03A, 0x2901D04D, 0x80E4F040, 0x28007840, 
	0x4973D07C, 0x22D9A077, 0xF924F71A, 0x4871E0DA, 
	0x21104C6E, 0xF1046803, 0x20AE0226, 0x20004798, 
	0x0038F884, 0xFD26F6E4, 0xF0402800, 0x200380CB, 
	0xF6FC2102, 0xF894FCF7, 0x28000037, 0x80C2F000, 
	0xF6E42001, 0xE0BDFEE1, 0xB1337843, 0x46084A60, 
	0xF44F4619, 0xF71A739D, 0x2003F91B, 0xF6FC2101, 
	0xF6E4FCE1, 0xE0ADFD07, 0xF8954D58, 0x28010038, 
	0x2002D131, 0x22032107, 0xF8852301, 0x48560038, 
	0xF6406804, 0x47A05019, 0x7001211A, 0x68094953, 
	0x4853E097, 0x0147F10D, 0x484C6803, 0x0426F100, 
	0x462220AE, 0x46064798, 0x2107484B, 0x23112203, 
	0xF6406805, 0x47A85023, 0x2E004605, 0x2000D06E, 
	0x000DF8C5, 0x0009F8C5, 0x0005F8C5, 0x0001F8C5, 
	0x70282028, 0x4840E06A, 0x22032107, 0x68042311, 
	0x5023F640, 0x460447A0, 0x21282000, 0x0038F885, 
	0xF8004620, 0xF1051B01, 0xE05C0126, 0x21074836, 
	0x23342203, 0x68042503, 0x5004F640, 0x460447A0, 
	0xF10D20F7, 0xF10D0105, 0x84E00206, 0x7003F640, 
	0x4D2F76E5, 0x20408020, 0xF88D682B, 0x20810005, 
	0xB1184798, 0xF0407EE0, 0x76E00080, 0x1D22482A, 
	0x0146F10D, 0x0022F8C4, 0x76A02004, 0x1001F240, 
	0xF44F85E0, 0x80607096, 0x682B2091, 0xB9304798, 
	0x28407A60, 0x7EA0D303, 0x0001F040, 0x8C2076A0, 
	0xF10D682B, 0xF0400147, 0x84200080, 0xF1004813, 
	0x20AE0526, 0x4798462A, 0xF1044601, 0xB189000A, 
	0x60C12100, 0x60416081, 0xE00F6001, 0x46282128, 
	0xF8002210, 0x46211B01, 0xFE4BF6CF, 0x6801480B, 
	0xE0064628, 0x22104629, 0xFE43F6CF, 0x68014807, 
	0x47884620, 0xB0122000, 0xBF00BD70, 0x2012DF6C, 
	0x00133130, 0x2012D660, 0x2012D40C, 0x2012D410, 
	0x2012D654, 0x00000030, 0x084800FB, 0x4605B5B0, 
	0x0009F06F, 0xF185FA50, 0x2908B2EC, 0x2007D813, 
	0xF001E8DF, 0x17090705, 0x110F0D0B, 0x20040016, 
	0x2005E00F, 0x2006E00D, 0x2008E00B, 0x2003E009, 
	0x200CE007, 0x4620E005, 0xFF18F70E, 0xE000B280, 
	0xF025200D, 0x2C1201FF, 0x0001EA40, 0xF6FCBF18, 
	0xBDB0FC63, 0x51A1F640, 0xBF044288, 0x47704804, 
	0x51B2F640, 0xBF0C4288, 0x20004802, 0xBF004770, 
	0x00130A75, 0x00130AE1, 0x47F0E92D, 0x461F2047, 
	0x460D4614, 0xFECAF70E, 0x78284606, 0x28103803, 
	0x810BF200, 0xF010E8DF, 0x0109002E, 0x01090063, 
	0x01090087, 0x01090109, 0x00110109, 0x01090011, 
	0x00C10109, 0x001C0109, 0xF027001C, 0x430400FF, 
	0x680648E0, 0xFE6FF6EF, 0xF2444601, 0xE0097020, 
	0x00FFF027, 0x48DB4304, 0xF6EF6806, 0x4601FE64, 
	0x7007F244, 0x23024622, 0x210047B0, 0x78697001, 
	0xE0A07041, 0x28007868, 0x80D7F000, 0x280A0A27, 
	0xEB06D105, 0x6A800087, 0xF0402800, 0x48CD80CE, 
	0xF6EF6806, 0x4601FE48, 0x7014F244, 0x23124622, 
	0xF8DF47B0, 0x4606818C, 0xEB087868, 0x70300587, 
	0xB1816869, 0x0007F818, 0x1CB07070, 0x2007F818, 
	0xFD87F6CF, 0x0900F04F, 0x9007F808, 0xF6EC6868, 
	0xF8C5FEF3, 0x46309004, 0xFA20F71C, 0x7868E0A1, 
	0x2814EA4F, 0xBF18280A, 0xD1672800, 0x462248B5, 
	0x6805230A, 0x0006F047, 0x01F9F020, 0x4003F640, 
	0x210747A8, 0xEB067001, 0x6A890188, 0x3201880A, 
	0x22028082, 0x70428849, 0x210080C1, 0x49AA8101, 
	0x47886809, 0x48A7E081, 0xF6EF6807, 0x4601FDFC, 
	0x7014F244, 0x23124622, 0x786947B8, 0x20004607, 
	0x290AB111, 0x4608BF18, 0xF1060A25, 0x70380828, 
	0x0025F858, 0x70797901, 0x79021D41, 0xF6CF1CB8, 
	0x4933FD38, 0x0685EB01, 0xB1386870, 0x0900F04F, 
	0x9005F801, 0xFEA0F6EC, 0x9004F8C6, 0xF71C4638, 
	0xF858F9CD, 0x28000025, 0x492AD04B, 0x6809380C, 
	0x20004788, 0x0025F848, 0x488AE043, 0xF6EF6806, 
	0x4601FDC2, 0x7016F244, 0x23044622, 0x786947B0, 
	0x88697001, 0xF71C8041, 0xE036F9B1, 0x68074881, 
	0xFDB1F6EF, 0xF2444601, 0x46227014, 0x47B82312, 
	0x905CF8DF, 0x78684607, 0x0588EB09, 0x68697038, 
	0xF819B181, 0x70780008, 0xF8191CB8, 0xF6CF2008, 
	0xF04FFCF0, 0xF8090A00, 0x6868A008, 0xFE5CF6EC, 
	0xA004F8C5, 0xF71C4638, 0xF106F989, 0xF8550528, 
	0xB1300028, 0x380C4907, 0x47886809, 0xF8452000, 
	0x46200028, 0xF6FC2100, 0x2000FAC5, 0x87F0E8BD, 
	0x201331A0, 0x2012D458, 0x4832B570, 0x460D461C, 
	0xF6EF6806, 0x4601FD70, 0x46224628, 0x200147B0, 
	0xBF00BD70, 0x4F22B5F8, 0x22014604, 0x6F80F5B1, 
	0x0020F857, 0x208BF880, 0xF44FBF28, 0x29176180, 
	0x2117BF98, 0x1088F8A0, 0x68054852, 0xF6ED4620, 
	0x0221FB3D, 0x1D8E2304, 0xF640B281, 0x46324002, 
	0x460547A8, 0x0024F857, 0xF8B02101, 0x80280088, 
	0xF6F24620, 0x2801F933, 0x480ED111, 0x68024631, 
	0x401DF640, 0x46204790, 0xF6F22101, 0x8840F947, 
	0x22002101, 0x46208068, 0xFDD4F6F2, 0x2000E001, 
	0x483D8068, 0x46286801, 0x40F8E8BD, 0xBF004708, 
	0x2012E9CC, 0x2012D468, 0x4806B570, 0x460D461C, 
	0xF6EF6806, 0x4601FD18, 0x46224628, 0x200147B0, 
	0xBF00BD70, 0x2012D450, 0x43F8E92D, 0x461E4610, 
	0x460C4615, 0xFA24F6FC, 0x4628B148, 0xFA20F6FC, 
	0xFAB03801, 0x0940F080, 0xE8BD0040, 0x204783F8, 
	0xFD34F70E, 0x9DB0F8DF, 0xF8D94680, 0xF6EF7000, 
	0x4601FCF2, 0x462A88A0, 0xB2833008, 0x7004F244, 
	0x460747B8, 0xF8880A30, 0xF6F1001E, 0xF040FB9F, 
	0x1DA10080, 0x28D2B2C0, 0x2601BF0C, 0x88602600, 
	0x88208078, 0x88A08038, 0x80B871BE, 0x88A21DF8, 
	0xFC27F6CF, 0xF71C4638, 0xB19EF8C9, 0x001EF898, 
	0x6000F8D9, 0x2304462A, 0x30060200, 0xF640B281, 
	0x47B04016, 0x70812100, 0x80018821, 0x68094906, 
	0xE0034788, 0x21014628, 0xF9FCF6FC, 0xE8BD2000, 
	0xBF0083F8, 0x2012D40C, 0x2012D410, 0x4610B570, 
	0x460C4615, 0xF9C4F6FC, 0xD024283F, 0x0A2D4E13, 
	0x0025F856, 0x2BFF8983, 0x2B08BF18, 0x4628D103, 
	0xFA84F6ED, 0x78A24603, 0x46287921, 0xF812F6EE, 
	0x0025F856, 0x2AFF8982, 0x7820D008, 0x28002101, 
	0x3870BF18, 0x4628B2C3, 0xF81AF6EE, 0x88617922, 
	0xF6EF4628, 0x2000FB35, 0xBF00BD70, 0x2012E90C, 
	0x4FF8E92D, 0xD12C2B13, 0x1D0E460C, 0xF6FE0A15, 
	0xF8DFFCA1, 0xF8DFA054, 0x4680B054, 0xF20F2700, 
	0x78200950, 0xD2164287, 0x0025F85A, 0xF836B188, 
	0xF8BB0017, 0x44081008, 0x0008F8AB, 0xF8364628, 
	0xF0001017, 0xF6FEFB0B, 0x4601FC85, 0xF7124648, 
	0x3701FCFF, 0xF1B8E7E5, 0xBF040F00, 0xF6FB200B, 
	0x2000FDBB, 0x8FF8E8BD, 0x2012EDC0, 0x2012EDD4, 
	0x5F696368, 0x635F626E, 0x705F706D, 0x5F73746B, 
	0x5F747665, 0x646E6168, 0x5F72656C, 0x63746170, 
	0x64252068, 0x0000000A, 0x4605B5B0, 0xFA36F6CF, 
	0x3D0C4604, 0x4629480B, 0xFB4AF6E8, 0xF6FB4628, 
	0xB130FF63, 0x4628A208, 0x21AFF64F, 0xF719231A, 
	0x4620FD97, 0xFA26F6CF, 0xE8BD2004, 0xF6FB40B0, 
	0xBF00BD83, 0x2012FD5C, 0x6D5F656B, 0x705F6773, 
	0x68637461, 0x0000632E, 0x4FFEE92D, 0xF980FA5F, 
	0x4682460C, 0xF1B90A05, 0xD2040F0E, 0xF8504841, 
	0xB9666039, 0x493EE004, 0x22A7A039, 0xFD52F719, 
	0x46484A3B, 0x23AE4629, 0xFD6AF719, 0x89312600, 
	0xD305428D, 0x46284A36, 0xF71923AF, 0x8931FD61, 
	0xD25B428D, 0xB9266876, 0xA0334931, 0xF71922B7, 
	0x5D70FD39, 0xD05142A0, 0x4E335574, 0x80CCF8DF, 
	0xB0CCF8DF, 0xE9CD2000, 0xF1060001, 0xF6CF0714, 
	0x4604F9D5, 0x46414638, 0xF6FC4652, 0xB3D0F82A, 
	0xF1B94605, 0xD11C0F06, 0xF64088A9, 0x79E8420A, 
	0xD0134291, 0x6F41F5B1, 0xA902D113, 0xB9815C09, 
	0xAA022101, 0x46305411, 0x462A4659, 0xF811F6FC, 
	0x4601B138, 0xF6E84638, 0xE7F4FACB, 0x5C09A901, 
	0x4630B151, 0xF6E84629, 0x4620FAC3, 0xF9AAF6CF, 
	0xF6FB2004, 0xE7CAFD09, 0x2201A901, 0x4630540A, 
	0x462A4659, 0xFFF5F6FB, 0xD0EA2800, 0x46384601, 
	0xFAAEF6E8, 0x4620E7F3, 0xF994F6CF, 0x8FFEE8BD, 
	0x65707974, 0x54203C20, 0x5F4B5341, 0x0058414D, 
	0x00133120, 0x2012DB14, 0x735F656B, 0x65746174, 
	0x705F6469, 0x00007274, 0x2012FD5C, 0x001306D5, 
	0x001306E1, 0x4604B5B0, 0xB2CD2047, 0xFBC6F70E, 
	0xD1044285, 0xE8BD4620, 0xF00140B0, 0x2D07BB61, 
	0x2D01D006, 0x2000BF12, 0xF0004620, 0xBDB0FBD5, 
	0xF7FF4620, 0xBDB0FCA7, 0xEB014907, 0x477000C0, 
	0x49044803, 0x48046001, 0xF7FE3018, 0xBF00BFEB, 
	0x2012DB10, 0x00131529, 0x201331C4, 0x200049CB, 
	0x28042200, 0xB2D0BF04, 0xF8514770, 0x30013020, 
	0xBF182B00, 0xE7F43201, 0x4FF0E92D, 0x4681B089, 
	0x460C48C2, 0x1029F850, 0xF0002900, 0x4620817C, 
	0xBF182C00, 0xF8512001, 0xF1BAAF04, 0xF10A0F00, 
	0x91050B10, 0xBF184651, 0x40082101, 0xB01CF8CD, 
	0x2109EA4F, 0x91043104, 0x010EF10A, 0xF10A9106, 
	0x9103010C, 0xF00007C0, 0x20008156, 0x0022F8AD, 
	0xF88D2001, 0xF6E70021, 0x4680FA09, 0x49B0B920, 
	0x2244A0AB, 0xFC56F719, 0xF64048AE, 0xEA48210D, 
	0xF8BA0600, 0x42880004, 0xF5B0D01E, 0xD02B6F21, 
	0x2112F640, 0xD1474288, 0xF10D9906, 0x46330222, 
	0x28008808, 0x2001BF18, 0x0021F88D, 0x0021F10D, 
	0x46589000, 0xFE12F6FD, 0xB3E04683, 0x0012F8BA, 
	0x2008F8BA, 0xE0202106, 0x2000F8BB, 0x21024648, 
	0xF6FD4627, 0x4604FA37, 0xF894B388, 0x07C00020, 
	0xF04FD138, 0xE0790B33, 0x0021F10D, 0xF10D9B06, 
	0x46320122, 0x46589000, 0xFF94F6FC, 0xB3184683, 
	0x0012F8BA, 0x2008F8BA, 0x90002105, 0x90012000, 
	0x465B4648, 0xF9FAF6FE, 0xF6E89805, 0x4886F9B8, 
	0x46506801, 0xE0734788, 0x46494A81, 0xF71923D4, 
	0x2501FC17, 0x9807E079, 0xB9488800, 0x4658E7BE, 
	0x0B33F04F, 0xE04C2500, 0x88009807, 0xD0D72800, 
	0xE0682501, 0x88009806, 0xF8BAB170, 0x8B621014, 
	0x30021A09, 0xF3F2FBB0, 0x0012FB03, 0x42A91A15, 
	0x460DBF38, 0xE006B9B0, 0x0014F8BA, 0x30028B65, 
	0xBF3842A8, 0x8BA04605, 0x4969B928, 0x228CA06B, 
	0xFBC8F719, 0x38018BA0, 0x83A09903, 0x300188C8, 
	0x200080C8, 0xBF182800, 0xB2A92001, 0xF88D9A06, 
	0x46330021, 0x2100E9CD, 0x46489002, 0xF10D4659, 
	0xF6FD0222, 0x4683FA63, 0xF8BAB940, 0xF8BA0014, 
	0x4281100E, 0x8087F040, 0x0B00F04F, 0x000EF104, 
	0x78002501, 0x1012F8BA, 0x2008F8BA, 0xE9CD465B, 
	0x46480100, 0xF6FE2104, 0x9805F989, 0xF947F6E8, 
	0x6801484D, 0x47884650, 0x2000B10D, 0x463C6060, 
	0x0F00F1BB, 0x4640D006, 0xF976F6E7, 0xB01CF8DD, 
	0xE05A2500, 0xF8DD2500, 0x484AB01C, 0x46486806, 
	0xFFD6F6EC, 0x46019A04, 0x1006F241, 0x47B02308, 
	0xF8BD4606, 0x80700022, 0xF8BAB930, 0x4A381004, 
	0x23E32000, 0xFB84F719, 0xF8C64648, 0xF6EC8004, 
	0xF5B0FFBF, 0xD3095F80, 0xF6EC4648, 0x4A30FFB9, 
	0xF6404601, 0x23ED70FF, 0xFB72F719, 0xF6408830, 
	0xEA2071FF, 0x46480701, 0xFFAAF6EC, 0x1021F89D, 
	0x80304338, 0x0FFCF011, 0x4A25D008, 0x5040F44F, 
	0xF71923EE, 0x8830FB5D, 0x1021F89D, 0x301FF36F, 
	0x300DF361, 0x48288030, 0x46306801, 0x49274788, 
	0x38018908, 0x46488108, 0xF878F000, 0xF9FAF6FE, 
	0xA0234601, 0xFA74F712, 0xB2E03C01, 0xBF182800, 
	0x40282001, 0x8BA0E6B6, 0x2501B108, 0xF894E009, 
	0xF0400020, 0xF8840002, 0x98050020, 0xF8C7F6E8, 
	0x463C2500, 0x9805E78F, 0x46486801, 0xBF182900, 
	0xF6FE2101, 0xB009FA1B, 0x8FF0E8BD, 0x2012EDC0, 
	0x5F667562, 0x20727470, 0x30203D21, 0x00000000, 
	0x00133181, 0x20130000, 0x2012D458, 0x6263656C, 
	0x65703E2D, 0x635F7265, 0x69646572, 0x3D212074, 
	0x00003020, 0x2012D40C, 0x2012D438, 0x2012EDD4, 
	0x6363326C, 0x7461645F, 0x65735F61, 0x705F646E, 
	0x68637461, 0x0A642520, 0x00000000, 0x2100B570, 
	0xF6FE4604, 0x4620F9E3, 0xF6FC2100, 0x4E18FBB7, 
	0x0444EB06, 0xF8348935, 0x44280F0A, 0xF7FF8130, 
	0xB108FE25, 0xE0018930, 0x813088F0, 0xB110B91D, 
	0xF6FB200B, 0x2000FAC9, 0xBD708020, 0xEB01490C, 
	0x89410040, 0x81413101, 0xBF004770, 0xEB024A08, 
	0x89420040, 0x81411A51, 0xBF004770, 0x4604B510, 
	0xFC3EF6FC, 0x21004802, 0x0044EB00, 0xBD108141, 
	0x2012EDD4, 0x4604B570, 0xF85048C5, 0xF8B00024, 
	0x07C0004E, 0x460DD10B, 0x21014620, 0xF7014616, 
	0xB920FEB1, 0x21004620, 0xFEACF701, 0x2524B110, 
	0xBD704628, 0xF64788E8, 0x1A3071FF, 0x4288B280, 
	0x2528D201, 0x4813E7F4, 0x68062310, 0x1C410220, 
	0x100DF240, 0x47B0460A, 0x20064606, 0x22034621, 
	0x46307130, 0xFEDEF701, 0x2101480B, 0x60304632, 
	0x0001F8D5, 0x796860B0, 0x88E87330, 0x462081F0, 
	0xFE96F701, 0x21034620, 0x25002200, 0xFFA0F701, 
	0xBF00E7CE, 0x2012D40C, 0x000313B9, 0x4604B5B0, 
	0x784B489F, 0xF850884A, 0x88885024, 0xF8A54619, 
	0x46200048, 0x3044F885, 0x2046F8A5, 0xFC29F000, 
	0x004EF8B5, 0xF0402100, 0xF8A50004, 0x4620004E, 
	0xFE58F701, 0xD1042802, 0x21004620, 0xFCF2F702, 
	0xF8B5E00C, 0x0700004E, 0x4620D408, 0xFCD8F700, 
	0x004EF8B5, 0x0008F020, 0x004EF8A5, 0xBDB02000, 
	0xD80C2805, 0xF8514986, 0xB1400020, 0x0050F890, 
	0x0003F000, 0xFAB03803, 0x0940F080, 0x20014770, 
	0xBF004770, 0x482FB570, 0x460D0A14, 0x6024F850, 
	0xF7FF4620, 0xB1D8FFE5, 0x002DF895, 0xF896B1F8, 
	0xB9811052, 0x21004620, 0xFE08F701, 0x7901B120, 
	0xD1012901, 0xB9317A41, 0xA0244923, 0x7286F44F, 
	0xF9E8F719, 0x46202100, 0xF6FF462A, 0xE00CFF79, 
	0x21004620, 0xFE06F701, 0x2002B148, 0xF896BD70, 
	0xF0000051, 0xF886007F, 0x20000051, 0xF8B6BD70, 
	0x06C0004E, 0x4628D504, 0xFE42F701, 0xD0EC2800, 
	0xF7014628, 0xB170FE3D, 0xF7014628, 0x2801FE39, 
	0x4628D009, 0xFE34F701, 0x46014A0B, 0xF2404620, 
	0xF7191329, 0x4620F9D5, 0x462A2100, 0xFDF0F701, 
	0xF7014628, 0x4601FE25, 0x22004620, 0xFD12F700, 
	0xBD702001, 0x2012EDE0, 0x00133141, 0x73616572, 
	0x21206E6F, 0x4F43203D, 0x5252455F, 0x4E5F524F, 
	0x52455F4F, 0x00524F52, 0x41F0E92D, 0x4605B08E, 
	0x460C4843, 0x8025F850, 0x68014842, 0x47884628, 
	0x8A278921, 0x5028F8AD, 0x00492602, 0x91062F02, 
	0xEA4F8961, 0x91070141, 0x4637BF98, 0x97088A62, 
	0xBF882A02, 0xF8944616, 0x96092027, 0xF88D0052, 
	0xF898202A, 0x07D2204E, 0x4408D107, 0xF7FE3801, 
	0x3703FDA3, 0x91091CF1, 0xF0009708, 0xF88D0101, 
	0xF894102B, 0xB911102D, 0x102EF894, 0x482BB3A1, 
	0xA8066801, 0xB1184788, 0x85602000, 0xE01A8960, 
	0x004EF8B8, 0x980C07C0, 0x990BD103, 0xF7FE3001, 
	0x4A23FD83, 0x8AA12300, 0xB2839300, 0x68164628, 
	0xF3C29A0B, 0x47B0024F, 0x41E2F240, 0xF0F1FB90, 
	0x980B8560, 0x85200840, 0x0027F884, 0x0046F8B8, 
	0xD106280F, 0x89618920, 0xBF181A08, 0xF8842001, 
	0xB00E0027, 0x81F0E8BD, 0x29008AE1, 0xF64FD0C7, 
	0x429172FF, 0xEB00D0C3, 0x89210041, 0x5014F8AD, 
	0x00499703, 0xF7FE9102, 0x9004FD4F, 0x68014806, 
	0x4788A802, 0xD1B22800, 0x85608AE0, 0xE7D19802, 
	0x2012EDE0, 0x2012D4C8, 0x2012D604, 0x2012D610, 
	0x2012D4A4, 0x4615B5B0, 0xB1694604, 0xD00D2903, 
	0xD1172902, 0x21014620, 0xFD08F701, 0xFD68F701, 
	0xD00F280A, 0xE0053501, 0xE004210B, 0x28107868, 
	0x3502D108, 0x782A2109, 0x4620B122, 0x40B0E8BD, 
	0xBE7CF701, 0x0000BDB0, 0x7F05F5B0, 0x480CBF04, 
	0xF2404770, 0x4288210B, 0x4808BF04, 0xF2404770, 
	0x4288210F, 0x4804BF04, 0xF2404770, 0x4288110B, 
	0x2000BF14, 0x47704803, 0x00132201, 0x001322A1, 
	0x0013210D, 0x00131A75, 0x43F8E92D, 0x90ACF8DF, 
	0x5020F859, 0xD04F2D00, 0x46904EF4, 0x4604460F, 
	0x21004628, 0x47906832, 0x009EF895, 0x6832B120, 
	0x0038F105, 0x47902100, 0x007CF8B5, 0xD5030700, 
	0x6801481F, 0x47882002, 0x0091F895, 0xD10E2802, 
	0xF89048E9, 0x463060D0, 0xFCE4F7FE, 0x4630B138, 
	0xFCFEF7FE, 0xD10242A0, 0x68004816, 0xB1E74780, 
	0x21004885, 0x233C22FF, 0x68072600, 0x2007F240, 
	0xF1B847B8, 0xF8800F00, 0xF8806035, 0x70048001, 
	0xF8B5D006, 0xB1111080, 0x608EF895, 0x2600E000, 
	0xF880497A, 0x68096038, 0xF8594788, 0xF6FB0024, 
	0x2000F8E3, 0x0024F849, 0x83F8E8BD, 0x2012EE00, 
	0x2012D63C, 0x2012D558, 0x460CB570, 0xF6CE4605, 
	0x4606FD05, 0x46214628, 0xFBD4F6DC, 0x46304604, 
	0xFD00F6CE, 0xBD704620, 0x4FF8E92D, 0xB194F8DF, 
	0x8020F85B, 0x0F00F1B8, 0x80C1F000, 0x29004607, 
	0x80B1F000, 0x46394860, 0x22FF4615, 0x465F2301, 
	0x91002401, 0x02086806, 0xF2401C41, 0x47B02012, 
	0x7005495A, 0x47886809, 0x48594A5A, 0x052CF108, 
	0x3100F500, 0xF2028812, 0xF022122F, 0xF2020201, 
	0x401122F1, 0x22C0F500, 0x40113141, 0x2260F500, 
	0x10F0F500, 0xEA01319D, 0xF1090902, 0xEA010179, 
	0x46280B00, 0xFDC3F6E7, 0x494B4606, 0x009EF898, 
	0x0600468A, 0xF898D031, 0xF898009A, 0xEB01109C, 
	0x46510080, 0xEBC0B2C0, 0xEB0B00C0, 0x44500040, 
	0xF0018841, 0x1E4A0103, 0xD3122A02, 0xD10A2903, 
	0xF6E68880, 0xF8D8FF3B, 0xB1500028, 0xA03B4996, 
	0x7236F44F, 0x4893E003, 0xF2404993, 0xF71822E1, 
	0xF898FFE1, 0xF898109C, 0x3801009E, 0x0101EA24, 
	0x009EF888, 0x109CF888, 0xF8D8E7CB, 0xB1100028, 
	0xF6E68880, 0x46BBFF1B, 0xB13E9F00, 0xF6E688B0, 
	0x4628FDBB, 0xFD7BF6E7, 0xE7F64606, 0xF04F487E, 
	0xF8900806, 0x1DB460D0, 0x4284B2F0, 0xFBB0D92B, 
	0xEB01F1F8, 0xEBA00141, 0x46280541, 0xFC02F7FE, 
	0x4628B1F8, 0xFC1CF7FE, 0xD11A42B8, 0x0185EB05, 
	0xEB0A4650, 0x008F0081, 0x0512F100, 0x0009F835, 
	0xF6E6B108, 0xF6E6FF3B, 0xF829FF25, 0x46500005, 
	0x000AEA47, 0x1000F839, 0x31DFF36F, 0x1000F829, 
	0x36019F00, 0x4814E7D0, 0x68024639, 0x47902004, 
	0x0027F85B, 0xF800F6FB, 0xF84B2000, 0xE8BD0027, 
	0xBF008FF8, 0x2012EE18, 0x2012D40C, 0x2012D410, 
	0x0001FFFE, 0x00054D08, 0x20130000, 0x5F6E6F63, 
	0x2D726170, 0x636C6C3E, 0x78745F70, 0x203D3D20, 
	0x4C4C554E, 0x00000000, 0x2012D648, 0x41F0E92D, 
	0x48494604, 0x5024F850, 0x4848B1AD, 0x21014690, 
	0x46286802, 0xF8954790, 0x2802009B, 0xF8B5D113, 
	0x223D0090, 0x06002101, 0x2216BF58, 0xE8BD4620, 
	0xF7FF41F0, 0x4843BEF9, 0xF2404943, 0xE8BD3269, 
	0xF71841F0, 0x4AA5BF3F, 0xF5004839, 0x88123100, 
	0x122FF202, 0x0701F022, 0x22F1F207, 0xF5004011, 
	0xF50022C0, 0x31412060, 0x319D4011, 0x0600EA01, 
	0x68004830, 0x46014780, 0x2206482F, 0x00D0F890, 
	0xFBB03005, 0xEB02F2F2, 0xEBA00242, 0xEB000042, 
	0x482A0280, 0x0282EA40, 0x44325AB3, 0x2B208852, 
	0xF012BF08, 0xD00B0203, 0x0090F8B5, 0x020EF000, 
	0xD1032A02, 0x2056F895, 0xD1132A01, 0xE0122204, 
	0xFB14225A, 0x44107202, 0x05D28C02, 0x8EC2D5EC, 
	0xF4028EC3, 0xF4235280, 0x441A5380, 0x5280F482, 
	0xE7E186C2, 0xF8B52203, 0x6F6E308A, 0x0F00F1B8, 
	0x0301F103, 0x308AF8A5, 0x44336DAB, 0x4370F023, 
	0x4B0F65AB, 0xEB03D005, 0x7DAB0242, 0x441A7852, 
	0xF813E001, 0x75AA2012, 0x2200F3C0, 0xE8BD4620, 
	0xF70541F0, 0xBF00BFBB, 0x2012EE18, 0x2012D5FC, 
	0x0001FFFE, 0x2012D520, 0x2012EE30, 0x20130002, 
	0x00059C28, 0x00133152, 0x00133154, 0x41F0E92D, 
	0x46880A16, 0xF70B4630, 0x4604FF1F, 0xF8504822, 
	0xB9280026, 0xA0214928, 0x72C8F44F, 0xFEAAF718, 
	0x4925B92C, 0xF240A025, 0xF7181291, 0x6867FEA3, 
	0x4921B937, 0xF44FA026, 0xF71872C9, 0x6867FE9B, 
	0x5000F8B8, 0xD80B42AF, 0xF7FF4630, 0xBB00FC79, 
	0x46284A19, 0xF44F4639, 0xF71873CE, 0x6867FEA9, 
	0x463968A0, 0xF7FE3001, 0xF8B8FABF, 0x42881000, 
	0x481DD00F, 0x46206801, 0x68614788, 0x0000F8B8, 
	0x38014408, 0xFAB0F7FE, 0x481860A0, 0x46206801, 
	0x20004788, 0x81F0E8BD, 0x2012EDE0, 0x5F636C6C, 
	0x5B766E65, 0x6B6E696C, 0x5D64695F, 0x203D2120, 
	0x4C4C554E, 0x00000000, 0x00133141, 0x6E616C70, 
	0x746C655F, 0x203D2120, 0x4C4C554E, 0x00000000, 
	0x6E616C70, 0x746C655F, 0x6E693E2D, 0x76726574, 
	0x21206C61, 0x0030203D, 0x2012D60C, 0x2012D614, 
	0x4882B5B0, 0xF8500A14, 0xB33D5024, 0x21004620, 
	0xFA60F701, 0xD0052805, 0x21014620, 0xFA5AF701, 
	0xD11B2805, 0x21004620, 0xFA54F701, 0xD1052805, 
	0x21044620, 0xF7002200, 0xE00AF98D, 0x21014620, 
	0xFA48F701, 0xD1042805, 0x210A4620, 0xF7012200, 
	0xF8B5FBB5, 0x4620104A, 0xFE98F6FF, 0xBDB02000, 
	0x47704770, 0x7CC1B510, 0x235A4A0B, 0x2403FB11, 
	0xFF9AF6DD, 0x880949B7, 0x112FF201, 0x0101F021, 
	0x07D25A62, 0x5B0AD006, 0xD4030792, 0xF0225B0A, 
	0x530A0201, 0xBF00BD10, 0x20130034, 0x00054D08, 
	0x41F0E92D, 0x4859B092, 0x460D0A14, 0x7024F850, 
	0xF8B7B1DF, 0x0640004E, 0x88A8D468, 0x46164954, 
	0x4301221B, 0xF6CEA80A, 0xF89DFBD4, 0x28240028, 
	0x0008F88D, 0xF897D311, 0xF0011050, 0x1E4A0103, 
	0xD34E2A02, 0xD1512900, 0x4A4BE02D, 0x21004620, 
	0x236EF240, 0xFDE4F718, 0xEB00E048, 0xF8DF0140, 
	0x78AA8114, 0x0181EB08, 0x4293890B, 0x2020D11B, 
	0xF8ADAA11, 0x68480044, 0x9000A90A, 0xF6E7A802, 
	0xF897FD7F, 0xF0011050, 0xE8DF0103, 0x1702F001, 
	0xF89D3921, 0xEB011008, 0xEB080241, 0x7A920282, 
	0xD02307D2, 0xF88DE035, 0x20070045, 0x2200A911, 
	0x0044F88D, 0xF6FF4620, 0xE017FEE5, 0x1008F89D, 
	0x0241EB01, 0x0282EB08, 0x07527A92, 0xE020D509, 
	0x1008F89D, 0x0241EB01, 0x0282EB08, 0x07927A92, 
	0x4620D417, 0x2201213D, 0xF96EF6FF, 0xF6E688A8, 
	0x4620FD15, 0xFDDEF6FF, 0xB0122000, 0x81F0E8BD, 
	0x1008F89D, 0x0241EB01, 0x0282EB08, 0x07127A92, 
	0xB108D5EC, 0xE019221E, 0xD3072924, 0x46304A16, 
	0x23FDF240, 0xFD7CF718, 0x1008F89D, 0xD007290C, 
	0xBF0E2901, 0xEB014B12, 0xF8580041, 0xE0003020, 
	0x882A4B0E, 0x4620A902, 0x46024798, 0xD0CD2A00, 
	0xBF182A3D, 0xD1022A28, 0x46114620, 0xF89DE7C3, 
	0x46201008, 0xF6FF2300, 0xE7BFFE3D, 0x2012EDE0, 
	0x20130000, 0x00057A9C, 0x00133141, 0x001319ED, 
	0x00131955, 0x47F0E92D, 0xA114F8DF, 0x46904604, 
	0x4E444689, 0x001DF89A, 0xD00628FF, 0x212000C0, 
	0xF000FA01, 0x43086831, 0x483F6030, 0xB1A96801, 
	0x7024F851, 0xF897B197, 0xF897003C, 0x21005028, 
	0x103CF887, 0xD1102802, 0x4F3C4C3B, 0x46286821, 
	0x28004788, 0x6838D04C, 0xE7F74780, 0x46204A38, 
	0x4305F240, 0xFD1CF718, 0x4830E044, 0x68022101, 
	0x47904638, 0x215A482E, 0x0001FB15, 0x880949F4, 
	0x112FF201, 0x0101F021, 0x05525A42, 0x5A0AD50D, 
	0x6280F422, 0xF897520A, 0x2803003D, 0x4620BF04, 
	0xF8B6F70A, 0xF8A72000, 0x4620003D, 0xFFACF708, 
	0x003EF897, 0xD0042802, 0xD1292801, 0x0059F897, 
	0x463DBB30, 0x0F3DF815, 0xBF042803, 0xF70A4620, 
	0x2000F89F, 0x003EF887, 0x70282000, 0x1001F04F, 
	0x46424649, 0x00C0F8C6, 0xF7094620, 0xE001FE13, 
	0xFC5EF708, 0x001DF89A, 0xD00728FF, 0x212000C0, 
	0xF000FA01, 0xEA216831, 0x60300000, 0x87F0E8BD, 
	0x003DF897, 0xD1E12802, 0x053DF107, 0xBF00E7DC, 
	0x2012DBC8, 0x50050064, 0x2012DCC0, 0x2012D5FC, 
	0x20130038, 0x00054D08, 0x2012D554, 0x2012D558, 
	0x00133154, 0x680A49C3, 0x4200F022, 0x72DFF360, 
	0x4770600A, 0xA1054804, 0xF3C06800, 0x28030041, 
	0x2002BF08, 0x47705C08, 0x50042024, 0x00020001, 
	0xB08AB5B0, 0x222F49A7, 0x13FFF240, 0x49A6600A, 
	0x209CF8D1, 0x0201F042, 0x209CF8C1, 0x2098F8D1, 
	0x1200F442, 0x2098F8C1, 0x2098F8D1, 0x42E0F022, 
	0x2098F8C1, 0x2098F8D1, 0x5280F042, 0x2098F8C1, 
	0x2090F8D1, 0xF240439A, 0xF8C133FF, 0xF8D12090, 
	0xF0422090, 0xF8C102FF, 0x69CA2090, 0x4B93439A, 
	0x69CA61CA, 0x2293F36F, 0x69CA61CA, 0x521EF36F, 
	0x69CA61CA, 0x72B4F442, 0x69CA61CA, 0x4208F442, 
	0x69CA61CA, 0x6230F042, 0xF8D161CA, 0xF4222080, 
	0xF8C102FC, 0xF8D12080, 0xF4422080, 0xF8C112F0, 
	0x694A2080, 0x0209F36F, 0x694A614A, 0x02A6F042, 
	0xF8D1614A, 0xF36F20A4, 0xF8C10210, 0xF8D120A4, 
	0xF36F20A8, 0xF8C10210, 0xF8D120A8, 0x431A20A4, 
	0xF8C14B77, 0xF8D120A4, 0x431A20A8, 0x20A8F8C1, 
	0xF022690A, 0x610A727F, 0xF442690A, 0x610A0200, 
	0xF442690A, 0x610A1280, 0xF442690A, 0x610A7280, 
	0xF0026C0A, 0x640A4240, 0xF0426C0A, 0x640A6200, 
	0xF4426C0A, 0x640A4200, 0xF0426C0A, 0x640A0230, 
	0xF0026C4A, 0x644A4240, 0xF0426C4A, 0x644A6210, 
	0xF4426C4A, 0x644A4200, 0x644A6C4A, 0x608A4A5D, 
	0xF42268CA, 0x60CA427F, 0xF44268CA, 0x60CA422A, 
	0xF36F680A, 0x600A329B, 0xF042680A, 0x600A6240, 
	0x2090F8D1, 0x2251F36F, 0x2090F8C1, 0x2090F8D1, 
	0x32FFF442, 0x2090F8C1, 0x20A0F8D1, 0x03FCF002, 
	0x2BEC4A4D, 0xF8D1D10B, 0xF02330A0, 0xF8C103FC, 
	0xF8D130A0, 0xF04330A0, 0xF8C103F8, 0xF8D130A0, 
	0xF42330A0, 0xF8C163E0, 0xF8D130A0, 0xF44330A0, 
	0xF8C163C0, 0x694B30A0, 0x614A401A, 0xF442694A, 
	0x614A322A, 0xF02268CA, 0x60CA02FF, 0xF04268CA, 
	0x60CA0208, 0x68134A39, 0x0303F023, 0x68136013, 
	0x0303F043, 0x4A366013, 0x4A366242, 0x22116202, 
	0x202CF880, 0x61424A34, 0xF8D14A37, 0xB2C910A0, 
	0xF8800889, 0x4931102D, 0x49316281, 0x493161C1, 
	0x2100E9C0, 0x61814931, 0x46692000, 0xD0022828, 
	0x30015408, 0x482EE7FA, 0x22284669, 0xF5008800, 
	0x46287580, 0x0013F2C2, 0xF953F6CE, 0x05C0F6C0, 
	0x48294C28, 0x6065213A, 0x0C04F844, 0x20E4F04F, 
	0x482660A0, 0x692060E0, 0x0007F361, 0x48246120, 
	0xF2406160, 0x61A07055, 0x61E04822, 0x62604822, 
	0x62A04822, 0xF0406820, 0x60204080, 0xF4206820, 
	0x60205000, 0x04806820, 0xA01DD505, 0xF240A126, 
	0xF71862D5, 0x6820FB2F, 0x4080F040, 0xB00A6020, 
	0xBF00BDB0, 0x201331C0, 0x50044008, 0x0001AAE1, 
	0x000155C3, 0x00800800, 0xFFF003FF, 0x50042030, 
	0x001328F1, 0x001328D1, 0x0013299D, 0x0013292D, 
	0x00132929, 0x00132565, 0x001328CD, 0x00132945, 
	0x00054D08, 0x50050074, 0x1A141000, 0x003C0537, 
	0x00500755, 0x00090907, 0x1CA80903, 0x17280003, 
	0x725F7069, 0x6F696461, 0x6C746E63, 0x70645F31, 
	0x72726F63, 0x5F6E655F, 0x66746567, 0x3D202928, 
	0x7830203D, 0x00000030, 0x625F6672, 0x70665F66, 
	0x705F6167, 0x68637461, 0x0000632E, 0x00004770, 
	0x02004912, 0x30014A12, 0x52888809, 0x68034811, 
	0x0301F043, 0x18886003, 0x0002F8D0, 0xBF004770, 
	0x4A0AB510, 0x24814B0A, 0x54D48812, 0x0E0B441A, 
	0x0C0B7153, 0x70917050, 0x71134806, 0x70D30A0B, 
	0xF0416801, 0x60010101, 0xBD106800, 0x00054D08, 
	0x20130128, 0x50050070, 0x4770B240, 0x68014804, 
	0x0101F041, 0x68016001, 0xD5FC06C9, 0xBF004770, 
	0x4007003C, 0x460CB5B0, 0xF7FF4605, 0xEBC0FE13, 
	0x491F1000, 0x0040EB01, 0x29102100, 0xF910D007, 
	0x2A7F2011, 0x42AAD005, 0x3101DA04, 0x2110E7F5, 
	0x3901E000, 0xB2EDB2CA, 0x0401F084, 0x3012F810, 
	0xBF181B5B, 0x2A002301, 0x2201BF18, 0x401A4023, 
	0xB2C91A89, 0x0041EB00, 0xBDB07840, 0x4604B510, 
	0xFDE8F7FF, 0x1000EBC0, 0xEB014909, 0x21000040, 
	0xD0082910, 0x0241EB00, 0x2AFF7852, 0x42A2D003, 
	0x3101D003, 0x2000E7F4, 0xF810BD10, 0xBD100011, 
	0x00133034, 0x8800B57C, 0x6189F641, 0xBF1E4208, 
	0x68684D2B, 0x70C0EA5F, 0x2400D102, 0xBD7C4620, 
	0x4E2F2401, 0x8B7060EC, 0xD0F707C0, 0xF7184668, 
	0x6828FDDD, 0x0001F020, 0x48226028, 0x47806800, 
	0xF4406828, 0x6028508C, 0x1C416830, 0x9900D011, 
	0xD2044281, 0xF4406828, 0x60286080, 0xF04FE009, 
	0x603030FF, 0xF4206828, 0x60286080, 0x68004816, 
	0x68704780, 0xD0111C41, 0x42819900, 0x6828D204, 
	0x6000F440, 0xE0096028, 0x30FFF04F, 0x68286070, 
	0x6000F420, 0x480D6028, 0x47806800, 0x1C4168B0, 
	0x9900D008, 0xBF3D4281, 0xF4406828, 0x60287000, 
	0xFDB0F718, 0x24012001, 0xFD4AF718, 0xBF00E7AE, 
	0x5005000C, 0x2012D5B0, 0x2012D5E8, 0x2012D5EC, 
	0x49034802, 0x61486800, 0xBF004770, 0x4007B040, 
	0x2012F45C, 0x798B480C, 0x2056F890, 0xF0222B40, 
	0xD30C0207, 0x0201F042, 0x2056F880, 0x2001F8D1, 
	0x204EF8C0, 0x1005F8B1, 0x1052F8A0, 0xF880E001, 
	0x20002056, 0xBF004770, 0x2012E920, 0x41F0E92D, 
	0xF04FB086, 0x20470800, 0x460C4615, 0x8016F8AD, 
	0xF89CF70D, 0x48F24606, 0xF6EE6807, 0x4601F85C, 
	0x700AF244, 0x2302462A, 0x460547B8, 0x46307C67, 
	0xF70D2100, 0x7C21F918, 0x0214F104, 0x8000E9CD, 
	0x0016F10D, 0xF001463B, 0xE9CD017F, 0x46212102, 
	0xF6E32200, 0x7028FF50, 0x0016F89D, 0x46287068, 
	0xFC34F71A, 0xB0062000, 0x81F0E8BD, 0x43F0E92D, 
	0xF04FB085, 0x20470900, 0x460C4615, 0x9012F8AD, 
	0xF864F70D, 0x48D64606, 0xF6EE6807, 0x4601F824, 
	0x700AF244, 0x2302462A, 0x460547B8, 0x8003F894, 
	0x46308827, 0xF70D2100, 0x78A1F8DE, 0xE9CD1D22, 
	0xF10D9000, 0x46430012, 0x011FF001, 0x2102E9CD, 
	0x22004639, 0xFE9DF6E3, 0xF89D7028, 0x70680012, 
	0xF71A4628, 0x2000FBFB, 0xE8BDB005, 0xBF0083F0, 
	0x6140F5A0, 0xD937290D, 0x4115F640, 0xBF044288, 
	0x4770482F, 0x7106F244, 0xBF044288, 0x47704820, 
	0x7108F244, 0xBF044288, 0x47704822, 0x7109F244, 
	0xBF044288, 0x47704824, 0x7110F244, 0xBF044288, 
	0x4770481D, 0x7115F244, 0xBF044288, 0x4770481B, 
	0x7124F244, 0xBF044288, 0x47704815, 0x711DF244, 
	0xBF044288, 0x47704816, 0x711CF244, 0xBF044288, 
	0x47704818, 0x47702000, 0xF001E8DF, 0x0707070A, 
	0x0E07070C, 0x07071007, 0xE7F30808, 0x47704807, 
	0x4770480E, 0x47704803, 0x4770480A, 0x47704802, 
	0x00132CA9, 0x00132D95, 0x00132FCD, 0x00132E1D, 
	0x00132F85, 0x00132B5D, 0x00132ED5, 0x00132E91, 
	0x00132AB5, 0x00132D51, 0x00132AED, 0x00130E99, 
	0x001311A9, 0x00132CFD, 0x2047B570, 0x460C4615, 
	0xFFC4F70C, 0x680E49C3, 0xF70D2100, 0x4602F84C, 
	0xF0458860, 0xF0210106, 0x300801F9, 0xF44FB283, 
	0x47B06041, 0x20134605, 0x70281D21, 0x80A87820, 
	0x80E88860, 0x0008F105, 0xF6CD8862, 0x48B6FEC2, 
	0x46286801, 0x20004788, 0xBF00BD70, 0x2047B570, 
	0x460C4615, 0xFF9AF70C, 0x680E49AE, 0xF70D2100, 
	0x4602F822, 0xF0458860, 0xF0210106, 0x300801F9, 
	0xF44FB283, 0x47B06041, 0x20124605, 0x70281D21, 
	0x80A87820, 0x80E88860, 0x0008F105, 0xF6CD8862, 
	0x48A1FE98, 0x46286801, 0x20004788, 0xBF00BD70, 
	0x7888B570, 0xD11B2802, 0x460C7908, 0xD1172829, 
	0x46154898, 0xF6ED6806, 0x4601FF2E, 0x7013F244, 
	0x2314462A, 0x460547B0, 0x1CE18820, 0x78A08028, 
	0x1CE870A8, 0xF6CD78A2, 0x4628FE74, 0xFB16F71A, 
	0xBD702000, 0x43F8E92D, 0x46982047, 0x460C4615, 
	0xFF4CF70C, 0x921CF8DF, 0xF8D94607, 0xF6ED6000, 
	0x4601FF0A, 0x7011F244, 0x2316462A, 0x460647B0, 
	0x1D618820, 0x88608030, 0x79208070, 0x1D707130, 
	0xF6CD7922, 0x0A28FE4E, 0x0080EB07, 0x46306284, 
	0xFAECF71A, 0x0006F048, 0x6000F8D9, 0x230A462A, 
	0x01F9F020, 0x4003F640, 0x210547B0, 0x70012202, 
	0x80818821, 0x70428861, 0x210080C1, 0x496E8101, 
	0x47886809, 0xE8BD2001, 0xBF0083F8, 0x2047B5F8, 
	0x4615461E, 0xF70C460C, 0x7821FF09, 0xD1122913, 
	0x680F4964, 0xF70C2100, 0x4602FF8E, 0x0006F046, 
	0xF0202302, 0xF64001F9, 0x47B8400E, 0x800188A1, 
	0x6809495D, 0x485B4788, 0xF6ED6806, 0x4601FEB4, 
	0x462A8860, 0xB2833006, 0x7018F244, 0x460547B0, 
	0x1DA17820, 0x88607028, 0x88A08068, 0x1DA880A8, 
	0xF6CD8862, 0x4628FDF6, 0xFA98F71A, 0xBDF82000, 
	0x2047B570, 0x460D461C, 0xFED0F70C, 0x680E4949, 
	0xF70C0A21, 0x4602FF58, 0x0006F044, 0xF0202308, 
	0xF64001F9, 0x47B0400F, 0x70012110, 0x804188A9, 
	0x60416829, 0x68094940, 0x20004788, 0xBF00BD70, 
	0x2012D40C, 0x43F8E92D, 0x461E4610, 0x460D4614, 
	0xFB8EF6FA, 0x4620B148, 0xFB8AF6FA, 0xFAB03801, 
	0x0940F080, 0xE8BD0040, 0x204783F8, 0xFE9EF70C, 
	0x782F4930, 0x8000F8D1, 0xF70C0A31, 0x4602FF24, 
	0x23180238, 0xB2813006, 0x4003F640, 0x460647C0, 
	0xF8DF2003, 0x0A27805C, 0x78687030, 0x0987EB08, 
	0x48127070, 0xF8D96070, 0xF6EA0004, 0x7868FF0D, 
	0x0007F808, 0x4638786F, 0xF9E3F70B, 0x463A3502, 
	0x0004F8C9, 0xF6CD4629, 0xF106FD8C, 0x46290008, 
	0xF6CD463A, 0x4818FD86, 0x46306801, 0x46204788, 
	0xF6FA2101, 0x2000FB6F, 0x83F8E8BD, 0xFFFF0001, 
	0x201331A0, 0x2047B570, 0x460D461C, 0xFE56F70C, 
	0x680E490C, 0xF70C0A21, 0x4602FEDE, 0x0006F044, 
	0xF0202308, 0xF64001F9, 0x47B0400F, 0x70012111, 
	0x804188A9, 0x60416829, 0x68094903, 0x20004788, 
	0xBF00BD70, 0x2012D40C, 0x2012D410, 0xF023B570, 
	0x460C00FF, 0x0502EA40, 0x68064805, 0xFDF3F6ED, 
	0x46204602, 0x711AF244, 0x47B0462B, 0xBD702001, 
	0x2012D454, 0x4614B51C, 0x4601460A, 0x6800480C, 
	0x480BB120, 0x46239300, 0xFBEAF71F, 0xBF00BD1C, 
	0x47702000, 0x4614B51C, 0x4601460A, 0x68004804, 
	0x4803B120, 0x46239300, 0xFC34F71F, 0xBF00BD1C, 
	0x2012DC50, 0x15EF13EC, 0x1BF617F2, 0x1EF81DF7, 
	0x22FA20F9, 0x27FC24FB, 0x2FFE2BFD, 0x3E0034FF, 
	0x11ECFF7F, 0x17F313F0, 0x1BFA18F7, 0x25001EFD, 
	0x2F022B01, 0x3D043403, 0x0000FF7F, 0x00000000, 
	0x11F010E6, 0x13FE12F9, 0x1A041501, 0x24062005, 
	0x2E082807, 0x3E0A3409, 0x0000FF7F, 0x00000000, 
	0x00054878, 0x201331B4, 0x00010004, 0x00054880, 
	0x201331B8, 0x00010004, 0x00000E01, 0x00130829, 
	0x00000E15, 0x001307E5, 0x00000D00, 0x00130BD1, 
	0x00000E13, 0x0013080D, 0x00000E17, 0x001308F5, 
	0x00000E19, 0x001308D9, 0x00000E0F, 0x001309C5, 
	0x00000E1B, 0x001309FD, 0x00000E2C, 0x00130869, 
	0x00000E03, 0x001308B5, 0x00000E11, 0x001309E1, 
	0x00000C12, 0x00131189, 0x00000E0A, 0x00130911, 
	0x00000E0C, 0x00130A1D, 0x00000C0D, 0x001310D9, 
	0x745F656B, 0x5F6B7361, 0x63746170, 0x00632E68, 
	0x5F707061, 0x6B736174, 0x7461705F, 0x632E6863, 
	0x636C6C00, 0x636C6C5F, 0x61705F70, 0x2E686374, 
	0x00300063, 0x5F646C6C, 0x63746170, 0x00632E68, 
	0x70697772, 0x7461705F, 0x632E6863, 0x77725F00, 
	0x655F7069, 0x675F6669, 0x705F7465, 0x68637461, 
	0x63326C00, 0x61705F63, 0x2E686374, 0x00000063, 
	0x00132FF5, 0x00133015, 0x00052585, 0x00052581, 
};

#if defined(BSP_CHIP_ID_COMPATIBLE) && defined(SF32LB55X)
void lcpu_patch_install_a3(void)
#else
void lcpu_patch_install(void)
#endif
{
#ifdef SOC_BF0_HCPU
	memset((void*)(LCPU_PATCH_START_ADDR_S),0,LCPU_PATCH_TOTAL_SIZE);
	memcpy((void*)(LCPU_PATCH_START_ADDR_S),g_lcpu_patch_bin,sizeof(g_lcpu_patch_bin));
#else
	memset((void*)(LCPU_PATCH_START_ADDR),0,LCPU_PATCH_TOTAL_SIZE);
	memcpy((void*)(LCPU_PATCH_START_ADDR),g_lcpu_patch_bin,sizeof(g_lcpu_patch_bin));
#endif
	memcpy((void*)(LCPU_PATCH_RECORD_ADDR),g_lcpu_patch_list,sizeof(g_lcpu_patch_list));
	HAL_PATCH_install();
}

#endif
