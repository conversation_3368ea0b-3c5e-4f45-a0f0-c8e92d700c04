/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_DATA_H__
#define __HDL_DATA_H__

#include "hdl_config.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*hdl_init_cb)(void *usr_arg);
typedef void (*hdl_da_send_cb)(void *usr_arg, uint32_t sent_bytes, uint32_t da_total_bytes);
typedef void (*hdl_download_cb)(void *usr_arg, char *cur_image_name,
                                uint32_t sent_bytes, uint32_t total_bytes);
typedef void (*hdl_progress_cb)(void *usr_arg, uint8_t percent);
typedef void (*hdl_readback_cb)(void *usr_arg, uint32_t cur_flash_addr,
                                uint8_t *readback_buf, uint32_t readback_buf_len,
                                uint32_t read_len, uint32_t readback_total_len);

typedef struct {
    uint32_t        da_flash_addr; // Or da_ram_pointer, Or da_file if your host have file system
    uint32_t        da_run_addr;
    uint32_t        da_len;
} hdl_da_info_t;

typedef struct {
    hdl_init_cb     conn_da_init_cb;
    void           *conn_da_init_cb_arg;
    hdl_da_send_cb  conn_da_send_cb;
    void           *conn_da_send_cb_arg;
    bool            conn_enable_log;
} hdl_connect_arg_t;

typedef struct {
    uint16_t        hw_code;
    uint16_t        hw_subcode;
    uint16_t        flash_manufacturer_id;
    uint16_t        flash_id1;
    uint16_t        flash_id2;
    uint32_t        flash_base_addr;
    uint32_t        flash_size;
} hdl_da_report_t;

typedef struct {
    hdl_init_cb     format_init_cb;
    void           *format_init_cb_arg;
    hdl_progress_cb format_progress_cb;
    void           *format_progress_cb_arg;
    uint32_t        format_addr;
    uint32_t        format_size;
} hdl_format_arg_t;

typedef struct _hdl_image_t {
    uint32_t        image_host_flash_addr; // Or image_ram_pointer, Or image_file if your host have file system
    uint32_t        image_slave_flash_addr;
    uint32_t        image_len;
    char           *image_name;
    bool            image_is_bootloader;
    struct _hdl_image_t *next;
} hdl_image_t;

typedef struct {
    hdl_init_cb     download_init_cb;
    void           *download_init_cb_arg;
    hdl_download_cb download_cb;
    void           *download_cb_arg;
    hdl_image_t    *download_images;
} hdl_download_arg_t;

typedef struct {
    hdl_init_cb     readback_init_cb;
    void           *readback_init_cb_arg;
    hdl_readback_cb readback_cb;
    void           *readback_cb_arg;
    uint32_t        readback_flash_addr;
    uint32_t        readback_total_len;
} hdl_readback_arg_t;

#ifdef __cplusplus
}
#endif

#endif //__HDL_DATA_H__

