import os
import rtconfig
import resource

# Check SDK 
SIFLI_SDK = os.getenv('SIFLI_SDK')
if not SIFLI_SDK:
    print("Please run set_env.bat in root folder of SIFLI SDK to set environment.")
    exit()
from building import *

# Set default compile options
SifliEnv()
os.system('prebuild.bat')
################################## change rtconfig.xxx to customize build ########################################
# print (rtconfig.OUTPUT_DIR)

AddOption("", "--resource",
          action="store_true", dest="resource", default=False,
          help="Compile resource")

CPPDEFINES = ['EPIC_DEBUG', 'EZIP_DEBUG']

if (GetOption('resource')==True):
    os.system('prebuild.bat')
    resource.BuildStringPackage(SIFLI_SDK+'example/watch_demo/resource/strings', SIFLI_SDK+'example/watch_demo/resource/lang', GetConfigValue('LV_EXT_RES_NON_STANDALONE'), GetConfigValue('LV_EXT_RES_DEFAULT_LANGUAGE'))
else:
    OUTPUT_DIR=rtconfig.OUTPUT_DIR
    TARGET = OUTPUT_DIR + rtconfig.TARGET_NAME + '.' + rtconfig.TARGET_EXT
    env = Environment(tools = ['mingw'],
        AS = rtconfig.AS, ASFLAGS = rtconfig.AFLAGS,
        CC = rtconfig.CC, CFLAGS = rtconfig.CFLAGS,
        CXX = rtconfig.CXX, CXXFLAGS = rtconfig.CXXFLAGS,
        AR = rtconfig.AR, ARFLAGS = '-rc',
        LINK = rtconfig.LINK, LINKFLAGS = rtconfig.LFLAGS, CPPDEFINES = CPPDEFINES)
    env.PrependENVPath('PATH', rtconfig.EXEC_PATH)

    # prepare building environment
    objs = PrepareBuilding(env)

    # make a building
    DoBuilding(TARGET, objs)
