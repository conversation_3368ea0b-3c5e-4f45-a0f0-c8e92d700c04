/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ag333x_pair.h
@Time    :   2025/03/20 13:44:13
*
**************************************************************************/
#ifndef __AG333X_PAIR_H
#define __AG333X_PAIR_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

void ag333x_parser_pair_init(void);

void ag333x_parser_pair_close(void);

void ag333x_parser_pair_cmd_process(const char* pair_ack);

bool ag333x_get_tow_wn_time(uint32_t* week, uint32_t* week_sec);

bool ag333x_gnss_is_need_update_epo(void);

bool gnss_device_is_power_on(void);

bool gnss_epo_aiding_is_requested(uint8_t type);

void gnss_epo_aiding_request_clear(uint8_t type);

bool gnss_time_aiding_is_requested(void);

void gnss_time_aiding_request_clear(void);

bool gnss_location_aiding_is_requested(void);

void gnss_location_aiding_request_clear(void);

bool gnss_get_dual_band(void);

void gnss_set_epo_valid(bool valid);

bool gnss_location_effective(void);

/** 
 * @*********************************************************************************************
 * @description: 获取gps固件配置状态
 * @param {*}   uint8_t* ，true配置有效，false无效
 * @return {*}  bool：true获取到的固件配置状态有效，false无效
 * @*********************************************************************************************
 */
bool gnss_fw_config_check(uint8_t *ret);

void gps_power_fault_check(void);

void gps_power_fault_recovery(void);

#ifdef __cplusplus
}
#endif


#endif 