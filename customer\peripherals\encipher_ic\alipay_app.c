/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File    :   alipay_app.c
*<AUTHOR>   qwkj
*@Date    :   2025/03/05
*@Description :   支付宝APP模块
************************************************************************/

#include "alipay_app.h"
#include "alipay_account_manage.h"
#include "alipay_pay.h"

int8_t _ctype_[] = {0};
/*******************************************bind ble start**************************************************************/

/*****************************************************************
*Name:alipay_bind_code_read
*Description:包含支付宝状态的初始化和调用绑定码获取的逻辑
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_bind_code_read(alipay_data_set_s *ali_data)
{
    int32_t len_bind_code = BIND_CODE_LEN;

    ali_data->ali_stu_up.fail_log = alipay_pre_init();

    if(ali_data->ali_stu_up.fail_log == 0)
    {
        if(alipay_get_binding_status() == false)//确认绑定状态
        {
            ali_data->ali_stu_up.fail_log = alipay_get_binding_code(ali_data->alipay_data_set_u.bind_code, &len_bind_code);
            if(ali_data->ali_stu_up.fail_log != ALIPAY_RV_OK)
            {
                ali_data->ali_stu_up.ali_status = STATUS_BIND_CODE_GET_FAIL;// 显示「绑定码-绑定码获取失败」
            }
            else
            {
                rt_kprintf("bind_code :%s\n",ali_data->alipay_data_set_u.bind_code);
                ali_data->ali_stu_up.ali_status = STATUS_SHOW_BIND_CODE;// 显示「绑定码-绑定码获取成功」
            } 
        }
        else
        {
            ali_data->ali_stu_up.ali_status = STATUS_SHOW_MENU_BAR;//显示「菜单栏界面」
        }
    }
    else
    {
        ali_data->ali_stu_up.ali_status = STATUS_PRE_INIT_FAIL;
    }

    rt_kprintf("alipay_bind_code_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}

/*****************************************************************
*Name:alipay_bind_read
*Description:获取支付宝的绑定状态
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_bind_read(alipay_data_set_s *ali_data)
{
    binding_status_e status = ALIPAY_STATUS_UNKNOWN;

    ali_data->ali_stu_up.fail_log = alipay_query_binding_result(&status);

    switch(status)
    {
        case ALIPAY_STATUS_BINDING_OK:
            ali_data->ali_stu_up.ali_status = STATUS_SHOW_BIND_PASS;//显示「绑定码-绑定成功」
            break;

        case ALIPAY_STATUS_START_BINDING:
            ali_data->ali_stu_up.ali_status = STATUS_BINDING;//显示「绑定码-正在绑定」
            break;

        case ALIPAY_STATUS_BINDING_FAIL:
            ali_data->ali_stu_up.ali_status = STATUS_BIND_FAIL;//显示「绑定码-绑定失败」
            break;

        //case ALIPAY_STATUS_UNKNOWN:
        default:
            ali_data->ali_stu_up.ali_status = STATUS_SHOW_BIND_CODE;//显示「绑定码-绑定码获取成功」
            break;
    }

    rt_kprintf("alipay_bind_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************bind ble end****************************************************************/

/*******************************************pay code start**************************************************************/

/*****************************************************************
*Name:alipay_pay_code_read
*Description:获取支付宝的付款二维码
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_pay_code_read(alipay_data_set_s *ali_data)
{
    uint32_t pay_code_len = PAY_CODE_LEN;

    ali_data->ali_stu_up.fail_log = alipay_get_paycode(ali_data->alipay_data_set_u.pay_code, &pay_code_len);

    if (ali_data->ali_stu_up.fail_log == ALIPAY_RV_OK) 
    {
        rt_kprintf("show:pay_code:%s\n",ali_data->alipay_data_set_u.pay_code);
        ali_data->ali_stu_up.ali_status = STATUS_SHOW_PAY_CODE;// 显示「付款码-⼆维码」
    } 
    else
    {
        ali_data->ali_stu_up.ali_status = STATUS_PAY_CODE_GET_FAIL;// 显示「付款码-⼆维码-获取失败」
    }

    rt_kprintf("alipay_pay_code_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************pay code end****************************************************************/

/*******************************************setting start***************************************************************/

/*****************************************************************
*Name:alipay_setting_id_read
*Description:获取支付宝的id信息
*Param:uint8_t数据类型的id,大小为128bit
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_setting_id_read(alipay_data_set_s *ali_data)
{
    uint32_t len_id = ID_LEN;

    ali_data->ali_stu_up.fail_log = alipay_get_logon_ID(ali_data->alipay_data_set_u.id, &len_id);

    if(ali_data->ali_stu_up.fail_log != ALIPAY_RV_OK)
    {
        ali_data->ali_stu_up.ali_status = STATUS_SETTING_ID_GET_FAIL;
    }
    else
    {
        rt_kprintf("ret_id:%s\n",ali_data->alipay_data_set_u.id);
        ali_data->ali_stu_up.ali_status = STATUS_SETTING_ID_GET_PASS;
    }

    rt_kprintf("alipay_setting_id_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}

/*****************************************************************
*Name:alipay_setting_nick_name_read
*Description:获取支付宝的nick_name信息
*Param:uint8_t数据类型的id,大小为128bit
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_setting_nick_name_read(alipay_data_set_s *ali_data)
{
    uint32_t len_nick_name = NICK_NAME_LEN;
    
    ali_data->ali_stu_up.fail_log = alipay_get_nick_name(ali_data->alipay_data_set_u.nick_name, &len_nick_name);

    if(ali_data->ali_stu_up.fail_log != ALIPAY_RV_OK)
    {
        ali_data->ali_stu_up.ali_status = STATUS_SETTING_NICK_NAME_GET_FAIL;
    }
    else
    {
        rt_kprintf("ret_nick_name:%s\n",ali_data->alipay_data_set_u.nick_name);
        ali_data->ali_stu_up.ali_status = STATUS_SETTING_NICK_NAME_GET_PASS;
    }

    rt_kprintf("alipay_setting_nick_name_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}

/*****************************************************************
*Name:alipay_setting_unbind_read
*Description:解除绑定
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_setting_unbind_read(alipay_data_set_s *ali_data)
{
    ali_data->ali_stu_up.fail_log = alipay_unbinding();

    if(ali_data->ali_stu_up.fail_log != ALIPAY_RV_OK)
    {
        ali_data->ali_stu_up.ali_status = STATUS_UNBIND_FAIL;
    }
    else
    {
        ali_data->ali_stu_up.ali_status = STATUS_UNBIND_PASS;
    }

    rt_kprintf("alipay_setting_unbind_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}

/*******************************************setting end*****************************************************************/

/*******************************************aid code start**************************************************************/

/*****************************************************************
*Name:alipay_aid_code_read
*Description:获取帮助码
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_aid_code_read(alipay_data_set_s *ali_data)
{
    uint32_t len_aid_code = AID_CODE_LEN;

    ali_data->ali_stu_up.fail_log = alipay_get_aid_code(ali_data->alipay_data_set_u.aid_code, &len_aid_code,true);

    if(ali_data->ali_stu_up.fail_log != ALIPAY_RV_OK)
    {
        ali_data->ali_stu_up.ali_status = STATUS_AID_CODE_GET_FAIL;// 显示「帮助码-获取绑定码失败」
    }
    else
    {
        rt_kprintf("ret_aid_code:%s\n",ali_data->alipay_data_set_u.aid_code);
        ali_data->ali_stu_up.ali_status = STATUS_SHOW_AID_CODE;// 显示「帮助码-获取帮助码成功」
    }

    rt_kprintf("alipay_aid_code_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************aid code end****************************************************************/

/*******************************************pay result start************************************************************/

/*****************************************************************
*Name:alipay_pay_result_read
*Description:获取支付结果
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
static ALIPAY_STATUS_E alipay_pay_result_read(alipay_data_set_s *ali_data)
{
    rt_kprintf("alipay_setting_unbind_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************pay result end**************************************************************/

/*******************************************alipay init/deinit start****************************************************/

/*****************************************************************
*Name:alipay_init
*Description:支付宝的一些初始化准备
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_init()
{
    se_i2c_init();
    
    return STATUS_ALIPAY_OK;
}

/*****************************************************************
*Name:alipay_deinit
*Description:解除支付宝的一些初始化准备
*Param:NA
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_deinit()
{
    se_i2c_deinit();

    return STATUS_ALIPAY_OK;
}
/*******************************************alipay init/deinit end******************************************************/

/*******************************************pay control start***********************************************************/

/*****************************************************************
*Name:alipay_control
*Description:获取支付宝的一些状态函数
*Param:
        ALIPAY_CMD_E ali_type :需要获取什么状态就传输什么命令
        alipay_data_set_s *ali_data:更新支付宝SDK反馈的状态和cmd对应的数据
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_control(ALIPAY_CMD_E ali_type,alipay_data_set_s *ali_data)
{
    if(ali_data == NULL)
    {
        return STATUS_SEND_DATAT_NULL;
    }

    ali_data->ali_stu_up.fail_log = ALIPAY_RV_OK;
    ali_data->ali_stu_up.ali_status = STATUS_START;

    //选择指令
    switch (ali_type) 
    {
        case NOT_DO_CMD:
            ali_data->ali_stu_up.ali_status = STATUS_ALIPAY_OK;
            break;

        case SE_IC_ENTER_LOW_POWER:
            se_enter_low_power();
            ali_data->ali_stu_up.ali_status = STATUS_ALIPAY_OK;
            break;

        case SE_IC_EXIT_LOW_POWER:
            se_exit_low_power();
            ali_data->ali_stu_up.ali_status = STATUS_ALIPAY_OK;
            break;

        default:
            ali_data->ali_stu_up.ali_status = STATUS_OTHER_PAGE;
            break;
    }

    rt_kprintf("alipay_control:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************pay control end*************************************************************/

/*******************************************pay write start*************************************************************/

/*****************************************************************
*Name:alipay_write
*Description:获取支付宝的一些状态函数
*Param:void
*Return:void
*Author:Ryan_Fan
*****************************************************************/
void alipay_write(void)
{
    rt_kprintf("alipay_write:NA\n");
}
/*******************************************pay write end***************************************************************/

/*******************************************center read start***********************************************************/

/*****************************************************************
*Name:alipay_read
*Description:获取支付宝的一些状态函数
*Param:
        ALIPAY_TYPE_E ali_type :需要获取什么状态就传输什么命令
        alipay_data_set_s *ali_data:更新支付宝SDK反馈的状态和cmd对应的数据
*Return:ALIPAY_STATUS_E
*Author:Ryan_Fan
*****************************************************************/
ALIPAY_STATUS_E alipay_read(ALIPAY_TYPE_E ali_type,alipay_data_set_s *ali_data)
{
    if(ali_data == NULL)
    {
        return STATUS_SEND_DATAT_NULL;
    }

    ali_data->ali_stu_up.fail_log = ALIPAY_RV_OK;
    ali_data->ali_stu_up.ali_status = STATUS_START;
    memset(ali_data->alipay_data_set_u.alipay_data_buff_max, 0, ALIPAY_DATA_BUFF_MAX_LEN);

    switch(ali_type)
    {
        case NOT_DO_TYPE:
            ali_data->ali_stu_up.ali_status = STATUS_ALIPAY_OK;
            break;

        case BIND_STATUS_GET:
            ali_data->ali_stu_up.ali_status = alipay_bind_read(ali_data);
            break;

        case UNBIND_STATUS_GET:
            ali_data->ali_stu_up.ali_status = alipay_setting_unbind_read(ali_data);
            break;

        case PAY_RESULT_GET:
            ali_data->ali_stu_up.ali_status = alipay_pay_result_read(ali_data);
            break;

        case BIND_CODE_GET:
            ali_data->ali_stu_up.ali_status = alipay_bind_code_read(ali_data);
            break;

        case PAY_CODE_GET:
            ali_data->ali_stu_up.ali_status = alipay_pay_code_read(ali_data);
            break;

        case SETTING_ID_GET:
            ali_data->ali_stu_up.ali_status = alipay_setting_id_read(ali_data);
            break;

        case SETTING_NICK_NAME_GET:
            ali_data->ali_stu_up.ali_status = alipay_setting_nick_name_read(ali_data);
            break;

        case AID_CODE_GET:
            ali_data->ali_stu_up.ali_status = alipay_aid_code_read(ali_data);
            break;

        default:
            ali_data->ali_stu_up.ali_status = STATUS_OTHER_PAGE;
            break;
    }

    rt_kprintf("alipay_read:%d\n",ali_data->ali_stu_up.ali_status);

    return ali_data->ali_stu_up.ali_status;
}
/*******************************************center read end*************************************************************/
