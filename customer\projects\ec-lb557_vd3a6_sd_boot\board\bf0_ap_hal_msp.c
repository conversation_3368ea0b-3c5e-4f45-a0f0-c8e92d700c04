/* Includes ------------------------------------------------------------------*/
#include <rtthread.h>
#include "bf0_hal.h"
#include "drv_io.h"
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */

/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN Define */

/* USER CODE END Define */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN Macro */

/* USER CODE END Macro */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* External functions --------------------------------------------------------*/
/* USER CODE BEGIN ExternalFunctions */

/* USER CODE END ExternalFunctions */

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

//void HAL_TIM_MspPostInit(TIM_HandleTypeDef *htim);
/**
* Initializes the Global MSP.
*/
void HAL_MspInit(void)
{
#ifdef SOC_BF0_HCPU
    {
        BSP_IO_Init();
    }
#endif
}

#ifdef HAL_SDHCI_MODULE_ENABLED

void HAL_SDHCI_MspInit(SDHCI_HandleTypeDef *handle)
{
    if (handle->Instance == (uint32_t)SDIO1)
    {
        GPIO_InitTypeDef GPIO_InitStruct;
        HAL_PIN_Set(PAD_PA01, GPIO_A1, PIN_PULLDOWN, 1);       // SD1_EN
        GPIO_InitStruct.Pin = 1;
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(hwp_gpio1, &GPIO_InitStruct);
        HAL_GPIO_WritePin(hwp_gpio1, 1, 1);             // SD1_EN

        HAL_PIN_Set(PAD_PA28, SD1_DIO0, PIN_PULLUP, 1);       // SDIO1
        HAL_PIN_Set(PAD_PA29, SD1_DIO1, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA30, SD1_DIO2, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA31, SD1_DIO3, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA47, SD1_DIO4, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA49, SD1_DIO5, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA51, SD1_DIO6, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA55, SD1_DIO7, PIN_PULLUP, 1);
        HAL_PIN_Set(PAD_PA34, SD1_CLK, PIN_NOPULL, 1);
        HAL_PIN_Set(PAD_PA36, SD1_CMD, PIN_PULLUP, 1);

        HAL_PIN_Set(PAD_PA37, GPIO_A37, PIN_PULLDOWN, 1);     // SDIO1 RESET
        HAL_Delay_us(100);
        HAL_PIN_Set(PAD_PA37, GPIO_A37, PIN_PULLUP, 1);
    }
}
#endif  //HAL_SDHCI_MODULE_ENABLED

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */


