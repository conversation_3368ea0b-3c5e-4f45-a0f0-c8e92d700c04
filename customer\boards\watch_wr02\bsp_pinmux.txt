/**
 ******************************************************************************
 * @file   bsp_pinmux.c
 * <AUTHOR> software development team
 ******************************************************************************
 */
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "bsp_board.h"
#include "bsp_pinmux.h"

/**
 * @brief 合封与固定引脚
 * 
 */
static void BSP_PIN_FixedInit(void)
{
    GPIO_InitTypeDef GPIO_InitStruct;
#ifdef BF0_HCPU
    //用于HCPU PSRAM合封的固定引脚
    uint32_t pid = (hwp_hpsys_cfg->IDR & HPSYS_CFG_IDR_PID) >> HPSYS_CFG_IDR_PID_Pos;

    if (pid == 0x1)
    {
        HAL_PIN_Set(PAD_SIP01, PSRAM_DQ0, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP02, PSRAM_DQ1, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP03, PSRAM_DQ2, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP04, PSRAM_DQ3, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP05, PSRAM_CS, PIN_NOPULL, 1);

        HAL_PIN_Set(PAD_SIP07, PSRAM_CLK, PIN_NOPULL, 1);
        HAL_PIN_Set(PAD_SIP08, PSRAM_DQ4, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP09, PSRAM_DQ5, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP10, PSRAM_DQ6, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP11, PSRAM_DQ7, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP12, PSRAM_DQS0, PIN_PULLDOWN, 1);
    }

#ifdef BSP_USING_EXT_PSRAM
    HAL_PIN_Set(PAD_PA02, PSRAM_DQ0, PIN_PULLDOWN, 1); // OPI PSRAM External, XCEELA interface, 16MB on board.
    HAL_PIN_Set(PAD_PA04, PSRAM_DQ1, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA05, PSRAM_DQ2, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA06, PSRAM_DQ3, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA09, PSRAM_DQ4, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA11, PSRAM_DQ5, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA12, PSRAM_DQ6, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA13, PSRAM_DQ7, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA15, PSRAM_DQS0, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA20, PSRAM_CLK, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA07, PSRAM_CS, PIN_NOPULL, 1);

#ifdef BSP_USING_DUAL_PSRAM
    HAL_PIN_Set(PAD_PA18, PSRAM_DQ8, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA22, PSRAM_DQ9, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA24, PSRAM_DQ10, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA32, PSRAM_DQ11, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA33, PSRAM_DQ12, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA59, PSRAM_DQ13, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA62, PSRAM_DQ14, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA64, PSRAM_DQ15, PIN_PULLDOWN, 1);
    HAL_PIN_Set(PAD_PA26, PSRAM_DQS1, PIN_PULLDOWN, 1);
#endif /* BSP_USING_DUAL_PSRAM */

    HAL_PIN_Set(PAD_PA67, GPIO_A67, PIN_PULLUP, 1); // PSRAM EN
#endif                                             

#ifdef SD_MMC_OLINE_SUPPORT
    HAL_PIN_Set(PAD_PA00, GPIO_A0, PIN_NOPULL, 1);   // Touch screen / i2c1 reset
    HAL_PIN_Set(PAD_PA01, GPIO_A1, PIN_PULLDOWN, 1); // SD1_EN/USB_DP/I2S1_BCK/PDM1_CLK
    HAL_PIN_Set(PAD_PA03, GPIO_A3, PIN_NOPULL, 1);   // Touch screen / i2c1  power en
#endif

#if BSP_USING_LCD
    HAL_PIN_Set(PAD_PB38, GPIO_B38, PIN_PULLUP, 0);
    HAL_PIN_Set(PAD_PA21, GPIO_A21, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PB22, GPIO_B22, PIN_PULLUP, 0);
#endif

#ifdef SD_MMC_OLINE_SUPPORT
    HAL_PIN_Set(PAD_PA28, SD1_DIO0, PIN_PULLUP, 1); // SDIO1
    HAL_PIN_Set(PAD_PA29, SD1_DIO1, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA30, SD1_DIO2, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA68, SD1_DIO3, PIN_PULLUP, 1);

    HAL_PIN_Set(PAD_PA62, SD1_DIO4, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA64, SD1_DIO5, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA22, SD1_DIO6, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA24, SD1_DIO7, PIN_PULLUP, 1);

    HAL_PIN_Set(PAD_PA60, SD1_CLK, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA61, SD1_CMD, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA27, GPIO_A27, PIN_PULLUP, 1); // SDIO1 RESET
#else 
    HAL_PIN_Set(PAD_PA28, SD1_DIO0, PIN_PULLUP, 1); // SDIO1
    HAL_PIN_Set(PAD_PA29, SD1_DIO1, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA30, SD1_DIO2, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA68, SD1_DIO3, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA60, SD1_CLK, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA61, SD1_CMD, PIN_PULLUP, 1);
    HAL_PIN_Set(PAD_PA27, GPIO_A27, PIN_PULLUP, 1); // SDIO1 RESET
#endif

#ifdef BSP_LCDC_USING_DPI
    HAL_PIN_Set(PAD_PA17, LCDC1_DPI_CLK, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA18, LCDC1_DPI_DE, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA19, LCDC1_DPI_R0, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA21, LCDC1_DPI_R1, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA22, LCDC1_DPI_R2, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA23, LCDC1_DPI_R3, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA24, LCDC1_DPI_R4, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA25, LCDC1_DPI_R5, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA26, LCDC1_DPI_R6, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA27, LCDC1_DPI_R7, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA28, LCDC1_DPI_G0, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA29, LCDC1_DPI_G1, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA30, LCDC1_DPI_G2, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA31, LCDC1_DPI_G3, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA32, LCDC1_DPI_G4, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA33, LCDC1_DPI_G5, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA34, LCDC1_DPI_G6, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA35, LCDC1_DPI_G7, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA36, LCDC1_DPI_B0, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA38, LCDC1_DPI_B1, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA41, LCDC1_DPI_B2, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA42, LCDC1_DPI_B3, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA43, LCDC1_DPI_B4, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA44, LCDC1_DPI_B5, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA45, LCDC1_DPI_B6, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA46, LCDC1_DPI_B7, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA59, LCDC1_DPI_HSYNC, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA62, LCDC1_DPI_VSYNC, PIN_NOPULL, 1);
    HAL_PIN_Set(PAD_PA64, LCDC1_DPI_SD, PIN_NOPULL, 1);
#endif /* BSP_LCDC_USING_DPI */

#else /*BF0_LCPU*/



#endif
    //LCPU QSPI4 PSRAM
    HAL_PIN_Set(PAD_PB24, GPIO_B24, PIN_PULLUP, 0); 
    HAL_PIN_Set(PAD_PB25, GPIO_B25, PIN_PULLUP, 0);

    HAL_PIN_Set(PAD_PB00, QSPI4_CLK, PIN_NOPULL, 0); 
    HAL_PIN_Set(PAD_PB01, QSPI4_CS, PIN_NOPULL, 0);
    HAL_PIN_Set(PAD_PB02, QSPI4_DIO0, PIN_PULLDOWN, 0);
    HAL_PIN_Set(PAD_PB03, QSPI4_DIO1, PIN_PULLDOWN, 0);
    HAL_PIN_Set(PAD_PB21, QSPI4_DIO2, PIN_PULLUP, 0);
    HAL_PIN_Set(PAD_PB07, QSPI4_DIO3, PIN_PULLUP, 0);
}

/**
 * @brief 用户自定义引脚 
 * 
 */
static void BSP_PIN_CustomInit(void)
{
    GPIO_InitTypeDef GPIO_InitStruct;
#ifdef BF0_HCPU/*大核初始化*/
hcpu_init_group_@_@
#else /* BF0_LCPU */
lcpu_init_group_@_@
#endif
}

/**
 * @brief 未使用引脚高阻初始化
 *
 */
static void BSP_PIN_UnusedInit(void)
{
    GPIO_InitTypeDef GPIO_InitStruct;
#ifdef BF0_HCPU
hcpu_highz_init_group_@_@
#else /* BF0_LCPU */
lcpu_highz_init_group_@_@
#endif
}

void BSP_PIN_Init(void)
{
    BSP_PIN_FixedInit();// 固定引脚初始化
    BSP_PIN_CustomInit();// 用户自定义引脚初始化
    BSP_PIN_UnusedInit();// 未使用引脚高阻初始化
}

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
