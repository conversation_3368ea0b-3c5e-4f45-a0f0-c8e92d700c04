/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */
#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include <stdint.h>
#include <stddef.h>

/*-----------------------------------------------------------------------------
 * GPIO-SIMULATED I2C
 *---------------------------------------------------------------------------*/
#define DEVICE_ID_TRANSMIT(x) (uint8_t)((x << 1) & 0xFE)
#define DEVICE_ID_RECEIVE(x)  (uint8_t)((x << 1) | 0x01)

/*-----------------------------------------------------------------------------
 * DATA TYPE
 *---------------------------------------------------------------------------*/

/** 
 * @brief Front-declaration for mixo i2c device struction data type
*/
typedef struct mixo_i2c_st mixo_i2c_t;

/*-----------------------------------------------------------------------------
 * FUNCTION DECLARATION
 *---------------------------------------------------------------------------*/

/**
 * @brief I2C (Peripheral or GPIO-simulated) write and read operation
 *
 * @param slave_id Slave id which will be communicated with.
 * @param write_buffer Pointer buffer to be write with.
 * @param write_size Size to be written.
 * @param read_buffer Pointer buffer to be read in.
 * @param read_size Size to be read.
 * @return 0 on success. otherwise error code.
 */
int mixo_i2c_write_read(uint8_t slave_id,
                        uint8_t* write_buffer,
                        size_t write_size,
                        uint8_t* read_buffer,
                        size_t read_size);

#ifdef __cplusplus
}
#endif
