config TSC_USING_ADS7846
    bool
    default n
    
config TSC_USING_IT7257
    bool
    default n

config TSC_USING_IT7259E
    bool
    default n
    
config TSC_USING_FT6336
    bool
    default n

config TSC_USING_FT3168
    bool
    default n
config TSC_USING_FT3169
    bool
    default n
config TSC_USING_TMA525B
    bool
    default n

config TSC_USING_ZTW622
    bool
    default n

config TSC_USING_SPD2012
    bool
    default n

config TSC_USING_CST918
    bool
    default n

config TSC_USING_CST918_0x15
    bool
    default n

config TSC_USING_BL7163      
    bool
    default n
    
config TSC_USING_CST816
    bool
    default n

config TSC_USING_BL6133
    bool
    default n


config TSC_USING_CHSC5816
    bool
    default n

config LCD_USING_ST7735
    bool 
    default n

config LCD_USING_ILI9341
    bool 
    default n

config LCD_USING_ILI9341
    bool
    default n
    
config LCD_USING_ST7789V
    bool
    default n
    
config LCD_USING_ST7789H2
    bool
    default n
    
config LCD_USING_RM69330
    bool
    default n
    
config LCD_USING_RM69090
    bool
    default n
    
config LCD_USING_RM690C0
    bool
    default n  
    
config LCD_USING_JDI387A
    bool
    default n
    
config LCD_USING_GC9C01
    bool
    default n

config LCD_USING_SPD2012
    bool
    default n

config LCD_USING_ST7797
    bool
    default n

config LCD_USING_ILI8688E
    bool
    default n

config LCD_USING_ST77903
    bool
    default n

config LCD_USING_SH8601
    bool
    default n

config LCD_USING_SH8601A
    bool
    default n

config LCD_USING_ATK7016
    bool
    default n
    
config LCD_USING_XM80240
    bool
    default n

config LCD_USING_GC9B71
    bool
    default n

config LCD_USING_RM6D010
    bool
    default n

config LCD_USING_ICN3311
    bool
    default n

config LCD_USING_ST7701S
    bool
    default n
    
config LCD_USING_FT2308
    bool
    default n
config LCD_USING_LS013B7DD02
    bool
    default n

config LCD_USING_ST7789_GTM024_08_SPI8P
    bool
    default n

config LCD_USING_CO5300
    bool
    default n
	
config BSP_LCDC_USING_SPI_NODCX_1DATA
    def_bool n 
config BSP_LCDC_USING_SPI_NODCX_2DATA
    def_bool n 
config BSP_LCDC_USING_SPI_DCX_1DATA
    def_bool n 
config BSP_LCDC_USING_SPI_DCX_2DATA
    def_bool n 
config BSP_LCDC_USING_QADSPI
    def_bool n 
config BSP_LCDC_USING_DDR_QADSPI
    def_bool n 
config BSP_LCDC_USING_DBI
    def_bool n 
config BSP_LCDC_USING_DSI
    def_bool n
config BSP_LCDC_USING_DPI
    def_bool n
config BSP_USING_RAMLESS_LCD
    def_bool n
config BSP_LCDC_USING_JDI_PARALLEL
    def_bool n

config LCD_USING_PWM_AS_BACKLIGHT
    def_bool n

config BSP_LCDC1_USE_LCDC2_TE
    def_bool n

config BSP_USE_LCDC2_ON_HPSYS
    def_bool n

config BSP_USING_ROUND_TYPE_LCD
    def_bool n

config BSP_USING_RECT_TYPE_LCD
    def_bool n

menu "Key config"
    menuconfig BSP_USING_KEY1
        bool "Use key1"
        default n
    if BSP_USING_KEY1  
        config BSP_KEY1_PIN
            int "KEY1 pin number"
            default 99
        config BSP_KEY1_ACTIVE_HIGH
            bool "Level is high if key is pressed"
            default n
    endif 
    menuconfig BSP_USING_KEY2
        bool "Use key2"
        default n
    if BSP_USING_KEY2 
        config BSP_KEY2_PIN
            int "KEY2 pin number"
            default 96
        config BSP_KEY2_ACTIVE_HIGH
            bool "Level is high if key is pressed"
            default n            
    endif    
endmenu

menu "Charger config"
    config BSP_USING_CHARGER
        bool "Use charger"
        default n
    if BSP_USING_CHARGER  
        config BSP_CHARGER_INT_PIN
	    int "CHARGER detect pin number"
	    default 143
	config BSP_CHARGER_EN_PIN  
	    int "CHARGER Enable pin number"
	    default -1
		config BSP_CHARGING_PIN  
	    int "CHARGER charging status pin number"
	    default -1
	config BSP_CHARGE_FULL_PIN  
	    int "CHARGER charge full status pin number"
	    default -1
	config BSP_BATTERY_DETECT_ADC
		string "battery detect used adc"
		default "bat1"
	config BSP_BATTERY_DETECT_ADC_CHANNEL
		int "battery detect used adc channel"
		default 1
    endif
endmenu

menuconfig SENSOR_USING_GPS
    bool "Enable GPS Sensor"
    default n
    if SENSOR_USING_GPS
        menuconfig GPS_USING_UC6226
            bool "Enable GPS module UC6226"
            select RT_USING_SENSOR
            default n
            if GPS_USING_UC6226
                config GPS_UART_NAME
                string "GPS Uart"
                default "uart3"

                config GPS_GPIO_BIT
                int "GPS RESET PIN"
                default 69

                config GPS_POWER_BIT
                int "GPS POWER BIT"
                default 72
            endif
    endif

menuconfig SENSOR_USING_HR
    bool "Enable Heart Rate Sensor"
    default n
    if SENSOR_USING_HR
        menuconfig HR_USING_GH3011
            bool "Enable HR Sensor GH3011"
            select RT_USING_SENSOR
            default n
            if HR_USING_GH3011
                config GH3011_I2C_BUS
                string "GH3011 I2C bus name"
                default "i2c4"
                config HR_MODEL_NAME
                string "GH3011 model name"
                default "gh3011"

                config GH3011_INT_BIT
                int "Heart rate ready int pin"
                default 79
                config GH3011_RST_PIN
                int "Heart rate reset pin"
                default 78
            endif   
        menuconfig HR_USING_HRS3300
            bool "Enable HR Sensor HRS3300"
            select RT_USING_SENSOR
            default n
            if HR_USING_HRS3300
                config HRS3300_I2C_BUS
                string "HRS3300 I2C bus name"
                default "i2c4"
                config HR_MODEL_NAME
                string "HRS3300 model name"
                default "hrs3300"

                config HRS3300_POW_PIN
                int "Heart rate power pin"
                default 78
                
                config HRS3300_INT_PIN
                int "Heart rate int pin"
                default 1
				
		config HR_USR_POLLING_MODE
                bool "HR use polling mode"
                default y
            endif             
    menuconfig HR_USING_SC7R30
            bool "Enable HR Sensor SC7R30"
            select RT_USING_SENSOR
            default n
            if HR_USING_SC7R30
                config SC7R30_I2C_BUS
                string "SC7R30 I2C bus name"
                default "i2c4"
            endif
            
    menuconfig HR_USING_PAH8007
            bool "Enable HR Sensor PAH8007"
            select RT_USING_SENSOR
            default n
            if HR_USING_PAH8007
                config PAH8007_I2C_BUS
                string "PAH8007 I2C bus name"
                default "i2c4"
            endif
	menuconfig HR_USING_VC32S
            bool "Enable HR Sensor VC32S"
            select RT_USING_SENSOR
            default n
            if HR_USING_VC32S
                config VC32S_I2C_BUS
                string "VC32S I2C bus name"
                default "i2c4"
                config HR_MODEL_NAME
                string "VC32S model name"
                default "vc32s"

                config VC32S_INT_BIT
                int "Heart rate ready int pin"
                default 140
                config VC32S_RST_PIN
                int "Heart rate reset pin"
                default 102
		config VC32S_POW_PIN
                int "Heart rate power pin"
                default -1
		config HR_USR_INT_MODE
                bool "HR use INT mode"
                default y
            endif 
    endif
    
menuconfig SENSOR_USING_PEDO
    bool "Enable Pedometer Sensor"
      default n
    if SENSOR_USING_PEDO
        menuconfig SENSOR_USING_SC7A20
            bool "Enable SC7A20 Sensor"
            select RT_USING_SENSOR
            default n
            if SENSOR_USING_SC7A20
                config SC7A20_I2C_BUS
                string "Sensor SC7A20 I2C"
                default "i2c4"    
            endif
    endif

menuconfig SENSOR_USING_6D
    bool "Enable 6D Sensor for Accelerator and Gyro"
    default n
    if SENSOR_USING_6D
        menuconfig ACC_USING_LSM6DSL
            bool "Enable Accelerator and Gyro LSM6DSL"
            select RT_USING_SENSOR
            default n
            if ACC_USING_LSM6DSL
                config LSM6DSL_USING_I2C
                int "LSM6DSL BUS type: 1 = I2C, 0 = SPI"
                default 0
                config LSM6DSL_BUS_NAME
                string "Sensor LSM6DSL BUS name"
                default "spi1"
                config GSENSOR_MODEL_NAME
                string "Gsensor Model name"
                default "lsm6dsl"
        
                config LSM6DSL_INT_GPIO_BIT
                int "LSM6DSL Interrupt 1 PIN"
                default 97
                config LSM6DSL_INT2_GPIO_BIT
                int "LSM6DSL Interrupt 2 PIN"
                default 94
                config LSM6DSL_POW_PIN
                int "lsm6dsl power pin"
                default 121

                config LSM_USING_AWT
                bool "Enable AWT fucntion"
                default y
                config LSM_USING_PEDO
                bool "Enable Pedometer fucntion"
                default y
        config GSENSOR_UES_FIFO
                bool "LSM6DSL use fifo"
                default y
            endif
    menuconfig ACC_USING_SC7A20
            bool "Enable Accelerator SC7A20"
            select RT_USING_SENSOR
            default n
            if ACC_USING_SC7A20
                config SC7A20_I2C_BUS
                string "SC7A20 I2C bus name"
                default "i2c5"
                config GSENSOR_MODEL_NAME
                string "Gsensor Model name"
                default "sc7a20"

                config SC7A20_INT_PIN
                int "SC7A20 int pin"
                default 144
            endif
    menuconfig ACC_USING_SC7A22
            bool "Enable Accelerator SC7A22"
            select RT_USING_SENSOR
            default n
            if ACC_USING_SC7A22
                config SC7A22_USING_I2C
                int "SC7A22 BUS type: 1 = I2C, 0 = SPI"
                default 0
                config SC7A22_BUS_NAME
                string "Sensor SC7A22 BUS name"
                default "spi3"
                config GSENSOR_MODEL_NAME
                string "Gsensor Model name"
                default "sc7a22"
        
                config SC7A22_INT_GPIO_BIT
                int "SC7A22 Interrupt 1 PIN"
                default 140
                
                config SC7A22_POW_PIN
                int "SC7A22 power pin"
                default 97
                
                config GSENSOR_UES_FIFO
                bool "SC7A22 use fifo"
                default y
            endif            
	menuconfig ACC_USING_STK8328C
            bool "Enable Accelerator STK8328C"
            select RT_USING_SENSOR
            default n
            if ACC_USING_STK8328C
                config STK8328C_BUS_NAME
                string "Sensor STK8328C BUS name"
                default "i2c6"
                config GSENSOR_MODEL_NAME
                string "Gsensor Model name"
                default "stk8328c"
        
                config STK8328C_INT1
                int "STK8328C Interrupt 1 PIN"
                default 139
                
				config STK8328C_INT2
                int "STK8328C Interrupt 2 PIN"
                default 105
				
				config STK8328C_POW_PIN
                int "STK8328C power pin"
                default -1

                config GSENSOR_UES_FIFO
                bool "STK8328C use fifo"
                default y
            endif
    config USING_GYRO_SENSOR
           bool "using gyro"
           default n
    endif

menuconfig SENSOR_USING_MAG
    bool "Enable Magnetic Sensor"
    default n
    if SENSOR_USING_MAG
        menuconfig MAG_USING_MMC36X0KJ
            bool "Enable Magnetic Sensor MMC36X0KJ"
            select RT_USING_SENSOR
            default n
            if MAG_USING_MMC36X0KJ
                config MMC36X0KJ_I2C_BUS
                string "Sensor MMC36X0KJ I2C"
                default "i2c4"    
            endif 
    endif

menuconfig SENSOR_USING_BAROMETER
    bool "Enable Barometer Sensor"
    default n
    if SENSOR_USING_BAROMETER
        menuconfig SENSOR_USING_BMP280
            bool "Enable BMP280 Sensor"
            select RT_USING_SENSOR
            default n
            if SENSOR_USING_BMP280
                config BMP280_I2C_BUS
                string "Sensor BMP280 I2C"
                default "i2c4"    
            endif
    endif    

menuconfig SENSOR_USING_ASL
    bool "Enable ASL Sensor"
    default n
    if SENSOR_USING_ASL
        menuconfig ASL_USING_TSL2572
            bool "Enable ASL Sensor TSL2572"
            select RT_USING_SENSOR
            default n
            if ASL_USING_TSL2572
                config TSL2572_I2C_NAME
                string "TSL2572 I2C BUS"
                default "i2c4"
            endif
    endif

menuconfig BL_USING_AW9364
    bool "Enable BackLight AW9364"
    default n
    if BL_USING_AW9364
        config AW9364_LIN_IO
        int "AW9364 PIN Number"
        default 70
    endif
    
menuconfig AUDIO_USING_DA7212
    bool "Set Audio Device DA7212"
    default n
    if AUDIO_USING_DA7212
        config DA7212_POWER_PIN
        int "DA7212 POWER PIN"
        default 37

        config DA7212_INTERFACE_NAME
        string "DA7212 interface name"
        default "i2c4"
    endif

menuconfig BUZZER_ENABLED
    bool "Enable buzzer"
    default n
    if BUZZER_ENABLED
        config BUZZER_INTERFACE_NAME
        string "BUZZER interface name"
        default "pwm2"

        config BUZZER_CHANEL_NUM
        int "BUZZER channel in PWM"
        default 1

        config BUZZER_FREQ
        int "BUZZER PWM freq"
        default 2730

    endif

menuconfig VIBRATOR_ENABLED
    bool "Enable vibrator"
    default n

menuconfig MOTOR_ENABLED
    bool "Enable motor"
    default n
    if MOTOR_ENABLED
       menuconfig MOTOR_USE_PWM
       bool "Enable MOTOR USE PWM"
       select RT_USING_MOTOR
       default n
       if MOTOR_USE_PWM
           config MOTOR_INTERFACE_NAME
           string "motor interface name"
           default "pwm2"

           config MOTOR_CHANEL_NUM
           int "motor channel in PWM"
           default 2

           config MOTOR_PERIOD
           int "motor period ms"
           default 200

           config MOTOR_POWER_IO
           int "motor power pin"
           default 44
       endif

       menuconfig MOTOR_SW_CONTRL
       bool "Enable MOTOR USE SW"
       select RT_USING_MOTOR
       default y
       if MOTOR_SW_CONTRL
           config MOTOR_POWER_IO
           int "motor power pin"
           default 44

           config MOTOR_CTRL_IO
           int "motor contrl pin"
           default 45
		   
		   config MOTOR_PERIOD
           int "motor period ms"
           default 200
       endif
    endif
	
menuconfig SENSOR_USING_MPU6050
    bool "Enable MPU6050"
    default n
    select RT_USING_SENSOR
    if SENSOR_USING_MPU6050
        config MPU6050
        bool
        default y

        config SENSOR_I2C
        string "Sensor I2C"
        default "i2c1"
    endif    
 
menuconfig SENSOR_USING_DOF10
    bool "Enable DOF10"
    select RT_USING_SENSOR
    default n
    if SENSOR_USING_DOF10
        config SENSOR_USING_ICM20948
        bool "Enable ICM20948 Sensor"
        default n

        config SENSOR_I2C_BUS
        string "Sensor I2C"
        default "i2c1"
    endif

menuconfig ENCODER_USING_DK05E01TF412
    bool "Wheel(DK05E01T)"
    default n

menuconfig PMIC_CTRL_ENABLE
    bool "Enable PMIC Controller"
    default n
    if PMIC_CTRL_ENABLE
        config PMICC_SCL_PIN
        int "PMIC Controller SCL PIN"
        default 55

        config PMICC_SDA_PIN
        int "PMIC Controller SDA PIN"
        default 44
    endif
menuconfig TOUCH_WAKEUP_SUPPORT
    bool "Enable wakeup by Pressing Touch Pannel"
    default n

menuconfig BSP_USING_SPI_CAMERA
    bool "Enable SPI Camera"
    default n

menuconfig RT_USING_GPS
    bool "Enable GPS device"
    select RT_USING_AT
    select AT_USING_CLIENT
    select GPS_USING_NMEALIB
    default n
    if RT_USING_GPS
        config GPS_USING_AG3335M
            bool "gps device switch"
            default y
			
		menuconfig GPS_PIN_CONFIG
			bool "gps pin number config -1:invalid >=0:valid"
			default y
			if GPS_PIN_CONFIG		
				config MCU_WAKEUP_GPS_PIN
					int "mcu wakeup gps pin number"
					default -1
					
				config MCU_RESET_GPS_PIN
					int "mcu reset gps pin number"
					default -1
					
				config GPS_LEVEL_SHIFT_PIN
					int "gps level shift pin number"
					default -1
					
				config GPS_POWER_PIN
					int "gps power pin number"
					default -1
					
				config GPS_WAKEUP_MCU_PIN
					int "gps wakeup mcu pin number"
					default -1
		endif
		
        config GPS_DEVICE_NAME
            string "gps device name"
            default "gps_device"
            
        config GPS_UART_NAME
            string "gps uart name"
            default "uart4"
            
        config GPS_UART_BAUD
            int "gps uart baud"
            default 115200
            
        config GPS_RECV_BUFF_LEN
            int "gps recv buff size"
            default 256
            
        config RT_US_GPS_DEBUG
        bool "open gps debug switch"
        default n
    endif
config PA_USING_AW882XX
    bool  "digital PA AW882XX enable"
    default n
config PA_USING_AW87390
    bool  "analog PA AW87390 enable"
    default n
config PA_USING_AW8155
    bool  "analog PA AW8155 enable"
    default n
