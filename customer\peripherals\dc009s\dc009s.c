/**
  ******************************************************************************
  * @file   dc009s.c
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "dc009s.h"
#include "app_timer.h"
#include "bsp_pinmux.h"
#include "power_ctl.h"
#include <rtthread.h>

#define BUZZER_INTERFACE_NAME "pwm6"
#define BUZZER_CHANEL_NUM 4

static struct rt_device_pwm *dc009s_dev = NULL;

int dc009s_init()
{
    //DC009S_I("dc009s_init\n");
	GPIO_InitTypeDef GPIO_InitStruct;
    dc009s_dev = (struct rt_device_pwm *)rt_device_find(BUZZER_INTERFACE_NAME);
    if (dc009s_dev == NULL)
    {
        DC009S_E("CM58530S can not find device %s\n", BUZZER_INTERFACE_NAME);
        return -1;
    }
    //开启蜂鸣器电源
    power_control_3v3_beep(true);

    // switch pinmux to gptimer
    qw_special_pin_set(BEEP_PWM_PIN, GPTIM5_CH4, PIN_NOPULL);
    //音量控制
    qw_gpio_set(Beep_volume_ENA,GPIO_MODE_OUTPUT,PIN_NOPULL);
    qw_gpio_set(Beep_volume_ENB,GPIO_MODE_OUTPUT,PIN_NOPULL);
    //全部拉高
    rt_pin_write(Beep_volume_ENA,1);
    rt_pin_write(Beep_volume_ENB,1);
    return 0;
}


/**
 * @brief  deinit buzzer
 * @param  None
 *
 * @return int
 */
int dc009s_deinit()
{
    //DC009S_I("dc009s_deinit\n");
    dc009s_dev = NULL;

    //关闭蜂鸣器电源
    power_control_3v3_beep(false);

    //将所有引脚高阻
    HAL_PIN_Set_Analog(PAD_PB26, 0);
    HAL_PIN_Set_Analog(PAD_PA06, 1);
    HAL_PIN_Set_Analog(PAD_PA11, 1);
    return 0;
}

int dc009s_open()
{
    if (dc009s_dev)
    {
        //写入开启状态
        rt_pwm_enable(dc009s_dev, BUZZER_CHANEL_NUM);

        return 0;
    }

    return -1;
}

//音量控制接口，位编码  0,1,2,3
void dc009s_set_volume(uint8_t volume)
{
    // switch pinmux to gptimer
    qw_special_pin_set(BEEP_PWM_PIN, GPTIM5_CH4, PIN_NOPULL);
    // 音量控制
    qw_gpio_set(Beep_volume_ENA, GPIO_MODE_OUTPUT, PIN_NOPULL);
    qw_gpio_set(Beep_volume_ENB, GPIO_MODE_OUTPUT, PIN_NOPULL);
    if (volume == 0x01)
    {
        rt_pin_write(Beep_volume_ENA, 0);
        rt_pin_write(Beep_volume_ENB, 1);
    }
    else if (volume == 0x02)
    {
        rt_pin_write(Beep_volume_ENA, 1);
        rt_pin_write(Beep_volume_ENB, 0);
    }
    else if (volume == 0x03)
    {
        rt_pin_write(Beep_volume_ENA, 1);
        rt_pin_write(Beep_volume_ENB, 1);
    }
    else
    {
        rt_pin_write(Beep_volume_ENA, 0);
        rt_pin_write(Beep_volume_ENB, 0);
    }
}

int dc009s_set_pwm(uint32_t freq, uint8_t duty_cycle)
{
    uint32_t period;
    uint32_t pulse;
    if (freq < 20 || freq > 20000 || duty_cycle == 0)
    {
        return -2;
    }
    if(duty_cycle > 100)
    {
        duty_cycle = 100;
    }

    period = 1000000000 / freq;
    pulse = (period * duty_cycle / 100);

    if (dc009s_dev)
    {
        rt_pwm_set(dc009s_dev, BUZZER_CHANEL_NUM, period, pulse);
        return 0;
    }

    return -1;
}


int dc009s_close()
{
    if (dc009s_dev)
    {
        rt_pwm_disable(dc009s_dev, BUZZER_CHANEL_NUM);

        return 0;
    }

    return -1;
}

#define DRV_DC009S_TEST
#ifdef DRV_DC009S_TEST
#include <string.h>
int cmd_dc009s(int argc, char *argv[])
{
    int ret;
    if (argc < 2)
    {
        DC009S_I("Invalid parameter\n");
        return 0;
    }
    if (strcmp(argv[1], "-init") == 0)
    {
        ret = dc009s_init();
        DC009S_I("dc009s_init %d\n", ret);
    }
    if (strcmp(argv[1], "-deinit") == 0)
    {
        ret = dc009s_deinit();
        DC009S_I("dc009s_deinit %d\n", ret);
    }
    else if (strcmp(argv[1], "-config") == 0)
    {
        int freq = atoi(argv[2]);
        uint8_t duty_cycle = atoi(argv[3]);
        ret = dc009s_set_pwm(freq, duty_cycle);
        DC009S_I("dc009s_set freq %d, duty_cycle %d\n", freq, duty_cycle);
    }
    else if (strcmp(argv[1], "-open") == 0)
    {
        ret = dc009s_open();
        DC009S_I("dc009s_open %d\n", ret);
    }
    else if (strcmp(argv[1], "-close") == 0)
    {
        ret = dc009s_close();
        DC009S_I("dc009s_close %d\n", ret);
    }
    else if (strcmp(argv[1], "-volume") == 0)
    {
        int volume = atoi(argv[2]);
        dc009s_set_volume(volume);
    }
    else if (strcmp(argv[1], "-loop") == 0)
    {
        int i;
        for (i = 1; i < 100; i++)
        {
            DC009S_I("freq %d Hz duty 50%%...\n",i*100);
            dc009s_set_pwm(i * 100, 50);
            rt_thread_delay(2000);
        }
        DC009S_I("loop test over...\n");
    }
    else
    {
        DC009S_I("Invalid parameter\n");
    }

    return 0;
}

FINSH_FUNCTION_EXPORT(cmd_dc009s, dc009s test);
MSH_CMD_EXPORT(cmd_dc009s, dc009s test);

#endif //DRV_DC009S_TEST
/**
 * 命令行进行测试应该发送这些
 * cmd_dc009s -init
 * cmd_dc009s -config 1000 50
 * cmd_dc009s -open
 * cmd_dc009s -close
 * cmd_dc009s -volume 1
 * cmd_dc009s -loop
 * cmd_dc009s -deinit
 */

