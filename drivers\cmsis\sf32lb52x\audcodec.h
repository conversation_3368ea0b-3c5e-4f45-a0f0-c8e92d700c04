#ifndef __AUDCODEC_H
#define __AUDCODEC_H

typedef struct
{
    __IO uint32_t ID;
    __IO uint32_t CFG;
    __IO uint32_t IRQ;
    __IO uint32_t IRQ_MSK;
    __IO uint32_t DAC_CFG;
    __IO uint32_t ADC_CFG;
    __IO uint32_t APB_STAT;
    __IO uint32_t RSVD5;
    __IO uint32_t ADC_CH0_CFG;
    __IO uint32_t ADC_CH1_CFG;
    __IO uint32_t RSVD4[2];
    __IO uint32_t DAC_CH0_CFG;
    __IO uint32_t DAC_CH0_CFG_EXT;
    __IO uint32_t DAC_CH1_CFG;
    __IO uint32_t DAC_CH1_CFG_EXT;
    __IO uint32_t ADC_CH0_ENTRY;
    __IO uint32_t ADC_CH1_ENTRY;
    __IO uint32_t RSVD3[2];
    __IO uint32_t DAC_CH0_ENTRY;
    __IO uint32_t DAC_CH1_ENTRY;
    __IO uint32_t DAC_CH0_DEBUG;
    __IO uint32_t DAC_CH1_DEBUG;
    __IO uint32_t DAC_CH0_DC;
    __IO uint32_t DAC_CH1_DC;
    __IO uint32_t RSVD2[2];
    __IO uint32_t COMMON_CFG;
    __IO uint32_t BG_CFG0;
    __IO uint32_t BG_CFG1;
    __IO uint32_t BG_CFG2;
    __IO uint32_t REFGEN_CFG;
    __IO uint32_t PLL_CFG0;
    __IO uint32_t PLL_CFG1;
    __IO uint32_t PLL_CFG2;
    __IO uint32_t PLL_CFG3;
    __IO uint32_t PLL_CFG4;
    __IO uint32_t PLL_CFG5;
    __IO uint32_t PLL_CFG6;
    __IO uint32_t PLL_STAT;
    __IO uint32_t PLL_CAL_CFG;
    __IO uint32_t PLL_CAL_RESULT;
    __IO uint32_t ADC_ANA_CFG;
    __IO uint32_t ADC1_CFG1;
    __IO uint32_t ADC1_CFG2;
    __IO uint32_t ADC2_CFG1;
    __IO uint32_t ADC2_CFG2;
    __IO uint32_t DAC1_CFG;
    __IO uint32_t DAC2_CFG;
    __IO uint32_t RSVD1[2];
    __IO uint32_t RESERVED_IN0;
    __IO uint32_t RESERVED_IN1;
    __IO uint32_t RESERVED_OUT;
} AUDCODEC_TypeDef;


/****************** Bit definition for AUDCODEC_ID register *******************/
#define AUDCODEC_ID_FUNC_Pos            (0U)
#define AUDCODEC_ID_FUNC_Msk            (0xFFFFFFFFUL << AUDCODEC_ID_FUNC_Pos)
#define AUDCODEC_ID_FUNC                AUDCODEC_ID_FUNC_Msk

/****************** Bit definition for AUDCODEC_CFG register ******************/
#define AUDCODEC_CFG_ADC_ENABLE_Pos     (0U)
#define AUDCODEC_CFG_ADC_ENABLE_Msk     (0x1UL << AUDCODEC_CFG_ADC_ENABLE_Pos)
#define AUDCODEC_CFG_ADC_ENABLE         AUDCODEC_CFG_ADC_ENABLE_Msk
#define AUDCODEC_CFG_DAC_ENABLE_Pos     (1U)
#define AUDCODEC_CFG_DAC_ENABLE_Msk     (0x1UL << AUDCODEC_CFG_DAC_ENABLE_Pos)
#define AUDCODEC_CFG_DAC_ENABLE         AUDCODEC_CFG_DAC_ENABLE_Msk
#define AUDCODEC_CFG_DAC_1K_MODE_Pos    (2U)
#define AUDCODEC_CFG_DAC_1K_MODE_Msk    (0x1UL << AUDCODEC_CFG_DAC_1K_MODE_Pos)
#define AUDCODEC_CFG_DAC_1K_MODE        AUDCODEC_CFG_DAC_1K_MODE_Msk
#define AUDCODEC_CFG_ADC_EN_DLY_SEL_Pos  (3U)
#define AUDCODEC_CFG_ADC_EN_DLY_SEL_Msk  (0x3UL << AUDCODEC_CFG_ADC_EN_DLY_SEL_Pos)
#define AUDCODEC_CFG_ADC_EN_DLY_SEL     AUDCODEC_CFG_ADC_EN_DLY_SEL_Msk

/****************** Bit definition for AUDCODEC_IRQ register ******************/
#define AUDCODEC_IRQ_DAC_CH0_APB_OF_Pos  (0U)
#define AUDCODEC_IRQ_DAC_CH0_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH0_APB_OF_Pos)
#define AUDCODEC_IRQ_DAC_CH0_APB_OF     AUDCODEC_IRQ_DAC_CH0_APB_OF_Msk
#define AUDCODEC_IRQ_DAC_CH0_OUT_UF_Pos  (1U)
#define AUDCODEC_IRQ_DAC_CH0_OUT_UF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH0_OUT_UF_Pos)
#define AUDCODEC_IRQ_DAC_CH0_OUT_UF     AUDCODEC_IRQ_DAC_CH0_OUT_UF_Msk
#define AUDCODEC_IRQ_DAC_CH0_STB_OF_Pos  (2U)
#define AUDCODEC_IRQ_DAC_CH0_STB_OF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH0_STB_OF_Pos)
#define AUDCODEC_IRQ_DAC_CH0_STB_OF     AUDCODEC_IRQ_DAC_CH0_STB_OF_Msk
#define AUDCODEC_IRQ_DAC_CH1_APB_OF_Pos  (3U)
#define AUDCODEC_IRQ_DAC_CH1_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH1_APB_OF_Pos)
#define AUDCODEC_IRQ_DAC_CH1_APB_OF     AUDCODEC_IRQ_DAC_CH1_APB_OF_Msk
#define AUDCODEC_IRQ_DAC_CH1_OUT_UF_Pos  (4U)
#define AUDCODEC_IRQ_DAC_CH1_OUT_UF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH1_OUT_UF_Pos)
#define AUDCODEC_IRQ_DAC_CH1_OUT_UF     AUDCODEC_IRQ_DAC_CH1_OUT_UF_Msk
#define AUDCODEC_IRQ_DAC_CH1_STB_OF_Pos  (5U)
#define AUDCODEC_IRQ_DAC_CH1_STB_OF_Msk  (0x1UL << AUDCODEC_IRQ_DAC_CH1_STB_OF_Pos)
#define AUDCODEC_IRQ_DAC_CH1_STB_OF     AUDCODEC_IRQ_DAC_CH1_STB_OF_Msk
#define AUDCODEC_IRQ_ADC_CH0_APB_OF_Pos  (16U)
#define AUDCODEC_IRQ_ADC_CH0_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_ADC_CH0_APB_OF_Pos)
#define AUDCODEC_IRQ_ADC_CH0_APB_OF     AUDCODEC_IRQ_ADC_CH0_APB_OF_Msk
#define AUDCODEC_IRQ_ADC_CH0_APB_UF_Pos  (17U)
#define AUDCODEC_IRQ_ADC_CH0_APB_UF_Msk  (0x1UL << AUDCODEC_IRQ_ADC_CH0_APB_UF_Pos)
#define AUDCODEC_IRQ_ADC_CH0_APB_UF     AUDCODEC_IRQ_ADC_CH0_APB_UF_Msk
#define AUDCODEC_IRQ_ADC_CH0_SAT_Pos    (18U)
#define AUDCODEC_IRQ_ADC_CH0_SAT_Msk    (0x1UL << AUDCODEC_IRQ_ADC_CH0_SAT_Pos)
#define AUDCODEC_IRQ_ADC_CH0_SAT        AUDCODEC_IRQ_ADC_CH0_SAT_Msk
#define AUDCODEC_IRQ_ADC_CH1_APB_OF_Pos  (19U)
#define AUDCODEC_IRQ_ADC_CH1_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_ADC_CH1_APB_OF_Pos)
#define AUDCODEC_IRQ_ADC_CH1_APB_OF     AUDCODEC_IRQ_ADC_CH1_APB_OF_Msk
#define AUDCODEC_IRQ_ADC_CH1_APB_UF_Pos  (20U)
#define AUDCODEC_IRQ_ADC_CH1_APB_UF_Msk  (0x1UL << AUDCODEC_IRQ_ADC_CH1_APB_UF_Pos)
#define AUDCODEC_IRQ_ADC_CH1_APB_UF     AUDCODEC_IRQ_ADC_CH1_APB_UF_Msk
#define AUDCODEC_IRQ_ADC_CH1_SAT_Pos    (21U)
#define AUDCODEC_IRQ_ADC_CH1_SAT_Msk    (0x1UL << AUDCODEC_IRQ_ADC_CH1_SAT_Pos)
#define AUDCODEC_IRQ_ADC_CH1_SAT        AUDCODEC_IRQ_ADC_CH1_SAT_Msk

/**************** Bit definition for AUDCODEC_IRQ_MSK register ****************/
#define AUDCODEC_IRQ_MSK_DAC_CH0_APB_OF_Pos  (0U)
#define AUDCODEC_IRQ_MSK_DAC_CH0_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH0_APB_OF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH0_APB_OF  AUDCODEC_IRQ_MSK_DAC_CH0_APB_OF_Msk
#define AUDCODEC_IRQ_MSK_DAC_CH0_OUT_UF_Pos  (1U)
#define AUDCODEC_IRQ_MSK_DAC_CH0_OUT_UF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH0_OUT_UF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH0_OUT_UF  AUDCODEC_IRQ_MSK_DAC_CH0_OUT_UF_Msk
#define AUDCODEC_IRQ_MSK_DAC_CH0_STB_OF_Pos  (2U)
#define AUDCODEC_IRQ_MSK_DAC_CH0_STB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH0_STB_OF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH0_STB_OF  AUDCODEC_IRQ_MSK_DAC_CH0_STB_OF_Msk
#define AUDCODEC_IRQ_MSK_DAC_CH1_APB_OF_Pos  (3U)
#define AUDCODEC_IRQ_MSK_DAC_CH1_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH1_APB_OF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH1_APB_OF  AUDCODEC_IRQ_MSK_DAC_CH1_APB_OF_Msk
#define AUDCODEC_IRQ_MSK_DAC_CH1_OUT_UF_Pos  (4U)
#define AUDCODEC_IRQ_MSK_DAC_CH1_OUT_UF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH1_OUT_UF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH1_OUT_UF  AUDCODEC_IRQ_MSK_DAC_CH1_OUT_UF_Msk
#define AUDCODEC_IRQ_MSK_DAC_CH1_STB_OF_Pos  (5U)
#define AUDCODEC_IRQ_MSK_DAC_CH1_STB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_DAC_CH1_STB_OF_Pos)
#define AUDCODEC_IRQ_MSK_DAC_CH1_STB_OF  AUDCODEC_IRQ_MSK_DAC_CH1_STB_OF_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_OF_Pos  (16U)
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH0_APB_OF_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_OF  AUDCODEC_IRQ_MSK_ADC_CH0_APB_OF_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_UF_Pos  (17U)
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_UF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH0_APB_UF_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH0_APB_UF  AUDCODEC_IRQ_MSK_ADC_CH0_APB_UF_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH0_SAT_Pos  (18U)
#define AUDCODEC_IRQ_MSK_ADC_CH0_SAT_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH0_SAT_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH0_SAT    AUDCODEC_IRQ_MSK_ADC_CH0_SAT_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_OF_Pos  (19U)
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_OF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH1_APB_OF_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_OF  AUDCODEC_IRQ_MSK_ADC_CH1_APB_OF_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_UF_Pos  (20U)
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_UF_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH1_APB_UF_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH1_APB_UF  AUDCODEC_IRQ_MSK_ADC_CH1_APB_UF_Msk
#define AUDCODEC_IRQ_MSK_ADC_CH1_SAT_Pos  (21U)
#define AUDCODEC_IRQ_MSK_ADC_CH1_SAT_Msk  (0x1UL << AUDCODEC_IRQ_MSK_ADC_CH1_SAT_Pos)
#define AUDCODEC_IRQ_MSK_ADC_CH1_SAT    AUDCODEC_IRQ_MSK_ADC_CH1_SAT_Msk

/**************** Bit definition for AUDCODEC_DAC_CFG register ****************/
#define AUDCODEC_DAC_CFG_OSR_SEL_Pos    (0U)
#define AUDCODEC_DAC_CFG_OSR_SEL_Msk    (0xFUL << AUDCODEC_DAC_CFG_OSR_SEL_Pos)
#define AUDCODEC_DAC_CFG_OSR_SEL        AUDCODEC_DAC_CFG_OSR_SEL_Msk
#define AUDCODEC_DAC_CFG_OP_MODE_Pos    (4U)
#define AUDCODEC_DAC_CFG_OP_MODE_Msk    (0x3UL << AUDCODEC_DAC_CFG_OP_MODE_Pos)
#define AUDCODEC_DAC_CFG_OP_MODE        AUDCODEC_DAC_CFG_OP_MODE_Msk
#define AUDCODEC_DAC_CFG_PATH_RESET_Pos  (6U)
#define AUDCODEC_DAC_CFG_PATH_RESET_Msk  (0x1UL << AUDCODEC_DAC_CFG_PATH_RESET_Pos)
#define AUDCODEC_DAC_CFG_PATH_RESET     AUDCODEC_DAC_CFG_PATH_RESET_Msk
#define AUDCODEC_DAC_CFG_CLK_SRC_SEL_Pos  (7U)
#define AUDCODEC_DAC_CFG_CLK_SRC_SEL_Msk  (0x1UL << AUDCODEC_DAC_CFG_CLK_SRC_SEL_Pos)
#define AUDCODEC_DAC_CFG_CLK_SRC_SEL    AUDCODEC_DAC_CFG_CLK_SRC_SEL_Msk
#define AUDCODEC_DAC_CFG_CLK_DIV_Pos    (8U)
#define AUDCODEC_DAC_CFG_CLK_DIV_Msk    (0xFFUL << AUDCODEC_DAC_CFG_CLK_DIV_Pos)
#define AUDCODEC_DAC_CFG_CLK_DIV        AUDCODEC_DAC_CFG_CLK_DIV_Msk
#define AUDCODEC_DAC_CFG_MANUAL_OSR_MODE_Pos  (16U)
#define AUDCODEC_DAC_CFG_MANUAL_OSR_MODE_Msk  (0x1UL << AUDCODEC_DAC_CFG_MANUAL_OSR_MODE_Pos)
#define AUDCODEC_DAC_CFG_MANUAL_OSR_MODE  AUDCODEC_DAC_CFG_MANUAL_OSR_MODE_Msk
#define AUDCODEC_DAC_CFG_HBF1_BYPASS_M_Pos  (17U)
#define AUDCODEC_DAC_CFG_HBF1_BYPASS_M_Msk  (0x1UL << AUDCODEC_DAC_CFG_HBF1_BYPASS_M_Pos)
#define AUDCODEC_DAC_CFG_HBF1_BYPASS_M  AUDCODEC_DAC_CFG_HBF1_BYPASS_M_Msk
#define AUDCODEC_DAC_CFG_HBF2_BYPASS_M_Pos  (18U)
#define AUDCODEC_DAC_CFG_HBF2_BYPASS_M_Msk  (0x1UL << AUDCODEC_DAC_CFG_HBF2_BYPASS_M_Pos)
#define AUDCODEC_DAC_CFG_HBF2_BYPASS_M  AUDCODEC_DAC_CFG_HBF2_BYPASS_M_Msk
#define AUDCODEC_DAC_CFG_HBF3_BYPASS_M_Pos  (19U)
#define AUDCODEC_DAC_CFG_HBF3_BYPASS_M_Msk  (0x1UL << AUDCODEC_DAC_CFG_HBF3_BYPASS_M_Pos)
#define AUDCODEC_DAC_CFG_HBF3_BYPASS_M  AUDCODEC_DAC_CFG_HBF3_BYPASS_M_Msk
#define AUDCODEC_DAC_CFG_HBF4_BYPASS_M_Pos  (20U)
#define AUDCODEC_DAC_CFG_HBF4_BYPASS_M_Msk  (0x1UL << AUDCODEC_DAC_CFG_HBF4_BYPASS_M_Pos)
#define AUDCODEC_DAC_CFG_HBF4_BYPASS_M  AUDCODEC_DAC_CFG_HBF4_BYPASS_M_Msk
#define AUDCODEC_DAC_CFG_INTERP3_BYPASS_M_Pos  (21U)
#define AUDCODEC_DAC_CFG_INTERP3_BYPASS_M_Msk  (0x1UL << AUDCODEC_DAC_CFG_INTERP3_BYPASS_M_Pos)
#define AUDCODEC_DAC_CFG_INTERP3_BYPASS_M  AUDCODEC_DAC_CFG_INTERP3_BYPASS_M_Msk
#define AUDCODEC_DAC_CFG_SINC_RATE_SEL_M_Pos  (22U)
#define AUDCODEC_DAC_CFG_SINC_RATE_SEL_M_Msk  (0x7UL << AUDCODEC_DAC_CFG_SINC_RATE_SEL_M_Pos)
#define AUDCODEC_DAC_CFG_SINC_RATE_SEL_M  AUDCODEC_DAC_CFG_SINC_RATE_SEL_M_Msk
#define AUDCODEC_DAC_CFG_SDM_OSR_SEL_M_Pos  (25U)
#define AUDCODEC_DAC_CFG_SDM_OSR_SEL_M_Msk  (0x3UL << AUDCODEC_DAC_CFG_SDM_OSR_SEL_M_Pos)
#define AUDCODEC_DAC_CFG_SDM_OSR_SEL_M  AUDCODEC_DAC_CFG_SDM_OSR_SEL_M_Msk

/**************** Bit definition for AUDCODEC_ADC_CFG register ****************/
#define AUDCODEC_ADC_CFG_OSR_SEL_Pos    (0U)
#define AUDCODEC_ADC_CFG_OSR_SEL_Msk    (0x7UL << AUDCODEC_ADC_CFG_OSR_SEL_Pos)
#define AUDCODEC_ADC_CFG_OSR_SEL        AUDCODEC_ADC_CFG_OSR_SEL_Msk
#define AUDCODEC_ADC_CFG_OP_MODE_Pos    (3U)
#define AUDCODEC_ADC_CFG_OP_MODE_Msk    (0x3UL << AUDCODEC_ADC_CFG_OP_MODE_Pos)
#define AUDCODEC_ADC_CFG_OP_MODE        AUDCODEC_ADC_CFG_OP_MODE_Msk
#define AUDCODEC_ADC_CFG_PATH_RESET_Pos  (5U)
#define AUDCODEC_ADC_CFG_PATH_RESET_Msk  (0x1UL << AUDCODEC_ADC_CFG_PATH_RESET_Pos)
#define AUDCODEC_ADC_CFG_PATH_RESET     AUDCODEC_ADC_CFG_PATH_RESET_Msk
#define AUDCODEC_ADC_CFG_CLK_SRC_SEL_Pos  (6U)
#define AUDCODEC_ADC_CFG_CLK_SRC_SEL_Msk  (0x1UL << AUDCODEC_ADC_CFG_CLK_SRC_SEL_Pos)
#define AUDCODEC_ADC_CFG_CLK_SRC_SEL    AUDCODEC_ADC_CFG_CLK_SRC_SEL_Msk
#define AUDCODEC_ADC_CFG_CLK_DIV_Pos    (8U)
#define AUDCODEC_ADC_CFG_CLK_DIV_Msk    (0xFFUL << AUDCODEC_ADC_CFG_CLK_DIV_Pos)
#define AUDCODEC_ADC_CFG_CLK_DIV        AUDCODEC_ADC_CFG_CLK_DIV_Msk

/*************** Bit definition for AUDCODEC_APB_STAT register ****************/
#define AUDCODEC_APB_STAT_DAC_CH0_FIFO_CNT_Pos  (0U)
#define AUDCODEC_APB_STAT_DAC_CH0_FIFO_CNT_Msk  (0xFUL << AUDCODEC_APB_STAT_DAC_CH0_FIFO_CNT_Pos)
#define AUDCODEC_APB_STAT_DAC_CH0_FIFO_CNT  AUDCODEC_APB_STAT_DAC_CH0_FIFO_CNT_Msk
#define AUDCODEC_APB_STAT_DAC_CH1_FIFO_CNT_Pos  (4U)
#define AUDCODEC_APB_STAT_DAC_CH1_FIFO_CNT_Msk  (0xFUL << AUDCODEC_APB_STAT_DAC_CH1_FIFO_CNT_Pos)
#define AUDCODEC_APB_STAT_DAC_CH1_FIFO_CNT  AUDCODEC_APB_STAT_DAC_CH1_FIFO_CNT_Msk
#define AUDCODEC_APB_STAT_ADC_CH0_FIFO_CNT_Pos  (16U)
#define AUDCODEC_APB_STAT_ADC_CH0_FIFO_CNT_Msk  (0xFUL << AUDCODEC_APB_STAT_ADC_CH0_FIFO_CNT_Pos)
#define AUDCODEC_APB_STAT_ADC_CH0_FIFO_CNT  AUDCODEC_APB_STAT_ADC_CH0_FIFO_CNT_Msk
#define AUDCODEC_APB_STAT_ADC_CH1_FIFO_CNT_Pos  (20U)
#define AUDCODEC_APB_STAT_ADC_CH1_FIFO_CNT_Msk  (0xFUL << AUDCODEC_APB_STAT_ADC_CH1_FIFO_CNT_Pos)
#define AUDCODEC_APB_STAT_ADC_CH1_FIFO_CNT  AUDCODEC_APB_STAT_ADC_CH1_FIFO_CNT_Msk

/************** Bit definition for AUDCODEC_ADC_CH0_CFG register **************/
#define AUDCODEC_ADC_CH0_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_ADC_CH0_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_ENABLE_Pos)
#define AUDCODEC_ADC_CH0_CFG_ENABLE     AUDCODEC_ADC_CH0_CFG_ENABLE_Msk
#define AUDCODEC_ADC_CH0_CFG_HPF_BYPASS_Pos  (1U)
#define AUDCODEC_ADC_CH0_CFG_HPF_BYPASS_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_HPF_BYPASS_Pos)
#define AUDCODEC_ADC_CH0_CFG_HPF_BYPASS  AUDCODEC_ADC_CH0_CFG_HPF_BYPASS_Msk
#define AUDCODEC_ADC_CH0_CFG_HPF_COEF_Pos  (2U)
#define AUDCODEC_ADC_CH0_CFG_HPF_COEF_Msk  (0xFUL << AUDCODEC_ADC_CH0_CFG_HPF_COEF_Pos)
#define AUDCODEC_ADC_CH0_CFG_HPF_COEF   AUDCODEC_ADC_CH0_CFG_HPF_COEF_Msk
#define AUDCODEC_ADC_CH0_CFG_STB_INV_Pos  (6U)
#define AUDCODEC_ADC_CH0_CFG_STB_INV_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_STB_INV_Pos)
#define AUDCODEC_ADC_CH0_CFG_STB_INV    AUDCODEC_ADC_CH0_CFG_STB_INV_Msk
#define AUDCODEC_ADC_CH0_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_ADC_CH0_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_DMA_EN_Pos)
#define AUDCODEC_ADC_CH0_CFG_DMA_EN     AUDCODEC_ADC_CH0_CFG_DMA_EN_Msk
#define AUDCODEC_ADC_CH0_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_ADC_CH0_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_ADC_CH0_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_ADC_CH0_CFG_ROUGH_VOL  AUDCODEC_ADC_CH0_CFG_ROUGH_VOL_Msk
#define AUDCODEC_ADC_CH0_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_ADC_CH0_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_ADC_CH0_CFG_FINE_VOL_Pos)
#define AUDCODEC_ADC_CH0_CFG_FINE_VOL   AUDCODEC_ADC_CH0_CFG_FINE_VOL_Msk
#define AUDCODEC_ADC_CH0_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_ADC_CH0_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_ADC_CH0_CFG_DATA_FORMAT  AUDCODEC_ADC_CH0_CFG_DATA_FORMAT_Msk
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_EN_Pos  (17U)
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_EN_Msk  (0x1UL << AUDCODEC_ADC_CH0_CFG_SAT_DET_EN_Pos)
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_EN  AUDCODEC_ADC_CH0_CFG_SAT_DET_EN_Msk
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_LEN_Pos  (18U)
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_LEN_Msk  (0x3UL << AUDCODEC_ADC_CH0_CFG_SAT_DET_LEN_Pos)
#define AUDCODEC_ADC_CH0_CFG_SAT_DET_LEN  AUDCODEC_ADC_CH0_CFG_SAT_DET_LEN_Msk

/************** Bit definition for AUDCODEC_ADC_CH1_CFG register **************/
#define AUDCODEC_ADC_CH1_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_ADC_CH1_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_ENABLE_Pos)
#define AUDCODEC_ADC_CH1_CFG_ENABLE     AUDCODEC_ADC_CH1_CFG_ENABLE_Msk
#define AUDCODEC_ADC_CH1_CFG_HPF_BYPASS_Pos  (1U)
#define AUDCODEC_ADC_CH1_CFG_HPF_BYPASS_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_HPF_BYPASS_Pos)
#define AUDCODEC_ADC_CH1_CFG_HPF_BYPASS  AUDCODEC_ADC_CH1_CFG_HPF_BYPASS_Msk
#define AUDCODEC_ADC_CH1_CFG_HPF_COEF_Pos  (2U)
#define AUDCODEC_ADC_CH1_CFG_HPF_COEF_Msk  (0xFUL << AUDCODEC_ADC_CH1_CFG_HPF_COEF_Pos)
#define AUDCODEC_ADC_CH1_CFG_HPF_COEF   AUDCODEC_ADC_CH1_CFG_HPF_COEF_Msk
#define AUDCODEC_ADC_CH1_CFG_STB_INV_Pos  (6U)
#define AUDCODEC_ADC_CH1_CFG_STB_INV_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_STB_INV_Pos)
#define AUDCODEC_ADC_CH1_CFG_STB_INV    AUDCODEC_ADC_CH1_CFG_STB_INV_Msk
#define AUDCODEC_ADC_CH1_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_ADC_CH1_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_DMA_EN_Pos)
#define AUDCODEC_ADC_CH1_CFG_DMA_EN     AUDCODEC_ADC_CH1_CFG_DMA_EN_Msk
#define AUDCODEC_ADC_CH1_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_ADC_CH1_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_ADC_CH1_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_ADC_CH1_CFG_ROUGH_VOL  AUDCODEC_ADC_CH1_CFG_ROUGH_VOL_Msk
#define AUDCODEC_ADC_CH1_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_ADC_CH1_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_ADC_CH1_CFG_FINE_VOL_Pos)
#define AUDCODEC_ADC_CH1_CFG_FINE_VOL   AUDCODEC_ADC_CH1_CFG_FINE_VOL_Msk
#define AUDCODEC_ADC_CH1_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_ADC_CH1_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_ADC_CH1_CFG_DATA_FORMAT  AUDCODEC_ADC_CH1_CFG_DATA_FORMAT_Msk
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_EN_Pos  (17U)
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_EN_Msk  (0x1UL << AUDCODEC_ADC_CH1_CFG_SAT_DET_EN_Pos)
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_EN  AUDCODEC_ADC_CH1_CFG_SAT_DET_EN_Msk
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_LEN_Pos  (18U)
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_LEN_Msk  (0x3UL << AUDCODEC_ADC_CH1_CFG_SAT_DET_LEN_Pos)
#define AUDCODEC_ADC_CH1_CFG_SAT_DET_LEN  AUDCODEC_ADC_CH1_CFG_SAT_DET_LEN_Msk

/************** Bit definition for AUDCODEC_DAC_CH0_CFG register **************/
#define AUDCODEC_DAC_CH0_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_DAC_CH0_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_ENABLE_Pos)
#define AUDCODEC_DAC_CH0_CFG_ENABLE     AUDCODEC_DAC_CH0_CFG_ENABLE_Msk
#define AUDCODEC_DAC_CH0_CFG_DOUT_MUTE_Pos  (1U)
#define AUDCODEC_DAC_CH0_CFG_DOUT_MUTE_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_DOUT_MUTE_Pos)
#define AUDCODEC_DAC_CH0_CFG_DOUT_MUTE  AUDCODEC_DAC_CH0_CFG_DOUT_MUTE_Msk
#define AUDCODEC_DAC_CH0_CFG_DEM_MODE_Pos  (2U)
#define AUDCODEC_DAC_CH0_CFG_DEM_MODE_Msk  (0x3UL << AUDCODEC_DAC_CH0_CFG_DEM_MODE_Pos)
#define AUDCODEC_DAC_CH0_CFG_DEM_MODE   AUDCODEC_DAC_CH0_CFG_DEM_MODE_Msk
#define AUDCODEC_DAC_CH0_CFG_STB_FIFO_CNT_Pos  (4U)
#define AUDCODEC_DAC_CH0_CFG_STB_FIFO_CNT_Msk  (0x7UL << AUDCODEC_DAC_CH0_CFG_STB_FIFO_CNT_Pos)
#define AUDCODEC_DAC_CH0_CFG_STB_FIFO_CNT  AUDCODEC_DAC_CH0_CFG_STB_FIFO_CNT_Msk
#define AUDCODEC_DAC_CH0_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_DAC_CH0_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_DMA_EN_Pos)
#define AUDCODEC_DAC_CH0_CFG_DMA_EN     AUDCODEC_DAC_CH0_CFG_DMA_EN_Msk
#define AUDCODEC_DAC_CH0_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_DAC_CH0_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_DAC_CH0_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_DAC_CH0_CFG_ROUGH_VOL  AUDCODEC_DAC_CH0_CFG_ROUGH_VOL_Msk
#define AUDCODEC_DAC_CH0_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_DAC_CH0_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_DAC_CH0_CFG_FINE_VOL_Pos)
#define AUDCODEC_DAC_CH0_CFG_FINE_VOL   AUDCODEC_DAC_CH0_CFG_FINE_VOL_Msk
#define AUDCODEC_DAC_CH0_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_DAC_CH0_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_DAC_CH0_CFG_DATA_FORMAT  AUDCODEC_DAC_CH0_CFG_DATA_FORMAT_Msk
#define AUDCODEC_DAC_CH0_CFG_SINC_GAIN_Pos  (17U)
#define AUDCODEC_DAC_CH0_CFG_SINC_GAIN_Msk  (0x1FFUL << AUDCODEC_DAC_CH0_CFG_SINC_GAIN_Pos)
#define AUDCODEC_DAC_CH0_CFG_SINC_GAIN  AUDCODEC_DAC_CH0_CFG_SINC_GAIN_Msk
#define AUDCODEC_DAC_CH0_CFG_DITHER_GAIN_Pos  (26U)
#define AUDCODEC_DAC_CH0_CFG_DITHER_GAIN_Msk  (0x7UL << AUDCODEC_DAC_CH0_CFG_DITHER_GAIN_Pos)
#define AUDCODEC_DAC_CH0_CFG_DITHER_GAIN  AUDCODEC_DAC_CH0_CFG_DITHER_GAIN_Msk
#define AUDCODEC_DAC_CH0_CFG_DITHER_EN_Pos  (29U)
#define AUDCODEC_DAC_CH0_CFG_DITHER_EN_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_DITHER_EN_Pos)
#define AUDCODEC_DAC_CH0_CFG_DITHER_EN  AUDCODEC_DAC_CH0_CFG_DITHER_EN_Msk
#define AUDCODEC_DAC_CH0_CFG_CLK_ANA_POL_Pos  (30U)
#define AUDCODEC_DAC_CH0_CFG_CLK_ANA_POL_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_CLK_ANA_POL_Pos)
#define AUDCODEC_DAC_CH0_CFG_CLK_ANA_POL  AUDCODEC_DAC_CH0_CFG_CLK_ANA_POL_Msk

/************ Bit definition for AUDCODEC_DAC_CH0_CFG_EXT register ************/
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_EN_Pos  (0U)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_EN_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_EXT_RAMP_EN_Pos)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_EN  AUDCODEC_DAC_CH0_CFG_EXT_RAMP_EN_Msk
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_MODE_Pos  (1U)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_MODE_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_EXT_RAMP_MODE_Pos)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_MODE  AUDCODEC_DAC_CH0_CFG_EXT_RAMP_MODE_Msk
#define AUDCODEC_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Pos  (2U)
#define AUDCODEC_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Msk  (0x1UL << AUDCODEC_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Pos)
#define AUDCODEC_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN  AUDCODEC_DAC_CH0_CFG_EXT_ZERO_ADJUST_EN_Msk
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Pos  (3U)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Msk  (0xFUL << AUDCODEC_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Pos)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_INTERVAL  AUDCODEC_DAC_CH0_CFG_EXT_RAMP_INTERVAL_Msk
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_STAT_Pos  (7U)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_STAT_Msk  (0x3UL << AUDCODEC_DAC_CH0_CFG_EXT_RAMP_STAT_Pos)
#define AUDCODEC_DAC_CH0_CFG_EXT_RAMP_STAT  AUDCODEC_DAC_CH0_CFG_EXT_RAMP_STAT_Msk

/************** Bit definition for AUDCODEC_DAC_CH1_CFG register **************/
#define AUDCODEC_DAC_CH1_CFG_ENABLE_Pos  (0U)
#define AUDCODEC_DAC_CH1_CFG_ENABLE_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_ENABLE_Pos)
#define AUDCODEC_DAC_CH1_CFG_ENABLE     AUDCODEC_DAC_CH1_CFG_ENABLE_Msk
#define AUDCODEC_DAC_CH1_CFG_DOUT_MUTE_Pos  (1U)
#define AUDCODEC_DAC_CH1_CFG_DOUT_MUTE_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_DOUT_MUTE_Pos)
#define AUDCODEC_DAC_CH1_CFG_DOUT_MUTE  AUDCODEC_DAC_CH1_CFG_DOUT_MUTE_Msk
#define AUDCODEC_DAC_CH1_CFG_DEM_MODE_Pos  (2U)
#define AUDCODEC_DAC_CH1_CFG_DEM_MODE_Msk  (0x3UL << AUDCODEC_DAC_CH1_CFG_DEM_MODE_Pos)
#define AUDCODEC_DAC_CH1_CFG_DEM_MODE   AUDCODEC_DAC_CH1_CFG_DEM_MODE_Msk
#define AUDCODEC_DAC_CH1_CFG_STB_FIFO_CNT_Pos  (4U)
#define AUDCODEC_DAC_CH1_CFG_STB_FIFO_CNT_Msk  (0x7UL << AUDCODEC_DAC_CH1_CFG_STB_FIFO_CNT_Pos)
#define AUDCODEC_DAC_CH1_CFG_STB_FIFO_CNT  AUDCODEC_DAC_CH1_CFG_STB_FIFO_CNT_Msk
#define AUDCODEC_DAC_CH1_CFG_DMA_EN_Pos  (7U)
#define AUDCODEC_DAC_CH1_CFG_DMA_EN_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_DMA_EN_Pos)
#define AUDCODEC_DAC_CH1_CFG_DMA_EN     AUDCODEC_DAC_CH1_CFG_DMA_EN_Msk
#define AUDCODEC_DAC_CH1_CFG_ROUGH_VOL_Pos  (8U)
#define AUDCODEC_DAC_CH1_CFG_ROUGH_VOL_Msk  (0xFUL << AUDCODEC_DAC_CH1_CFG_ROUGH_VOL_Pos)
#define AUDCODEC_DAC_CH1_CFG_ROUGH_VOL  AUDCODEC_DAC_CH1_CFG_ROUGH_VOL_Msk
#define AUDCODEC_DAC_CH1_CFG_FINE_VOL_Pos  (12U)
#define AUDCODEC_DAC_CH1_CFG_FINE_VOL_Msk  (0xFUL << AUDCODEC_DAC_CH1_CFG_FINE_VOL_Pos)
#define AUDCODEC_DAC_CH1_CFG_FINE_VOL   AUDCODEC_DAC_CH1_CFG_FINE_VOL_Msk
#define AUDCODEC_DAC_CH1_CFG_DATA_FORMAT_Pos  (16U)
#define AUDCODEC_DAC_CH1_CFG_DATA_FORMAT_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_DATA_FORMAT_Pos)
#define AUDCODEC_DAC_CH1_CFG_DATA_FORMAT  AUDCODEC_DAC_CH1_CFG_DATA_FORMAT_Msk
#define AUDCODEC_DAC_CH1_CFG_SINC_GAIN_Pos  (17U)
#define AUDCODEC_DAC_CH1_CFG_SINC_GAIN_Msk  (0x1FFUL << AUDCODEC_DAC_CH1_CFG_SINC_GAIN_Pos)
#define AUDCODEC_DAC_CH1_CFG_SINC_GAIN  AUDCODEC_DAC_CH1_CFG_SINC_GAIN_Msk
#define AUDCODEC_DAC_CH1_CFG_DITHER_GAIN_Pos  (26U)
#define AUDCODEC_DAC_CH1_CFG_DITHER_GAIN_Msk  (0x7UL << AUDCODEC_DAC_CH1_CFG_DITHER_GAIN_Pos)
#define AUDCODEC_DAC_CH1_CFG_DITHER_GAIN  AUDCODEC_DAC_CH1_CFG_DITHER_GAIN_Msk
#define AUDCODEC_DAC_CH1_CFG_DITHER_EN_Pos  (29U)
#define AUDCODEC_DAC_CH1_CFG_DITHER_EN_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_DITHER_EN_Pos)
#define AUDCODEC_DAC_CH1_CFG_DITHER_EN  AUDCODEC_DAC_CH1_CFG_DITHER_EN_Msk
#define AUDCODEC_DAC_CH1_CFG_CLK_ANA_POL_Pos  (30U)
#define AUDCODEC_DAC_CH1_CFG_CLK_ANA_POL_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_CLK_ANA_POL_Pos)
#define AUDCODEC_DAC_CH1_CFG_CLK_ANA_POL  AUDCODEC_DAC_CH1_CFG_CLK_ANA_POL_Msk

/************ Bit definition for AUDCODEC_DAC_CH1_CFG_EXT register ************/
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_EN_Pos  (0U)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_EN_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_EXT_RAMP_EN_Pos)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_EN  AUDCODEC_DAC_CH1_CFG_EXT_RAMP_EN_Msk
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_MODE_Pos  (1U)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_MODE_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_EXT_RAMP_MODE_Pos)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_MODE  AUDCODEC_DAC_CH1_CFG_EXT_RAMP_MODE_Msk
#define AUDCODEC_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Pos  (2U)
#define AUDCODEC_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Msk  (0x1UL << AUDCODEC_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Pos)
#define AUDCODEC_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN  AUDCODEC_DAC_CH1_CFG_EXT_ZERO_ADJUST_EN_Msk
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Pos  (3U)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Msk  (0xFUL << AUDCODEC_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Pos)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_INTERVAL  AUDCODEC_DAC_CH1_CFG_EXT_RAMP_INTERVAL_Msk
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_STAT_Pos  (7U)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_STAT_Msk  (0x3UL << AUDCODEC_DAC_CH1_CFG_EXT_RAMP_STAT_Pos)
#define AUDCODEC_DAC_CH1_CFG_EXT_RAMP_STAT  AUDCODEC_DAC_CH1_CFG_EXT_RAMP_STAT_Msk

/************* Bit definition for AUDCODEC_ADC_CH0_ENTRY register *************/
#define AUDCODEC_ADC_CH0_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_ADC_CH0_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_ADC_CH0_ENTRY_DATA_Pos)
#define AUDCODEC_ADC_CH0_ENTRY_DATA     AUDCODEC_ADC_CH0_ENTRY_DATA_Msk

/************* Bit definition for AUDCODEC_ADC_CH1_ENTRY register *************/
#define AUDCODEC_ADC_CH1_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_ADC_CH1_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_ADC_CH1_ENTRY_DATA_Pos)
#define AUDCODEC_ADC_CH1_ENTRY_DATA     AUDCODEC_ADC_CH1_ENTRY_DATA_Msk

/************* Bit definition for AUDCODEC_DAC_CH0_ENTRY register *************/
#define AUDCODEC_DAC_CH0_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_DAC_CH0_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_DAC_CH0_ENTRY_DATA_Pos)
#define AUDCODEC_DAC_CH0_ENTRY_DATA     AUDCODEC_DAC_CH0_ENTRY_DATA_Msk

/************* Bit definition for AUDCODEC_DAC_CH1_ENTRY register *************/
#define AUDCODEC_DAC_CH1_ENTRY_DATA_Pos  (0U)
#define AUDCODEC_DAC_CH1_ENTRY_DATA_Msk  (0xFFFFFFFFUL << AUDCODEC_DAC_CH1_ENTRY_DATA_Pos)
#define AUDCODEC_DAC_CH1_ENTRY_DATA     AUDCODEC_DAC_CH1_ENTRY_DATA_Msk

/************* Bit definition for AUDCODEC_DAC_CH0_DEBUG register *************/
#define AUDCODEC_DAC_CH0_DEBUG_DATA_OUT_Pos  (0U)
#define AUDCODEC_DAC_CH0_DEBUG_DATA_OUT_Msk  (0xFFFFUL << AUDCODEC_DAC_CH0_DEBUG_DATA_OUT_Pos)
#define AUDCODEC_DAC_CH0_DEBUG_DATA_OUT  AUDCODEC_DAC_CH0_DEBUG_DATA_OUT_Msk
#define AUDCODEC_DAC_CH0_DEBUG_BYPASS_Pos  (16U)
#define AUDCODEC_DAC_CH0_DEBUG_BYPASS_Msk  (0x1UL << AUDCODEC_DAC_CH0_DEBUG_BYPASS_Pos)
#define AUDCODEC_DAC_CH0_DEBUG_BYPASS   AUDCODEC_DAC_CH0_DEBUG_BYPASS_Msk

/************* Bit definition for AUDCODEC_DAC_CH1_DEBUG register *************/
#define AUDCODEC_DAC_CH1_DEBUG_DATA_OUT_Pos  (0U)
#define AUDCODEC_DAC_CH1_DEBUG_DATA_OUT_Msk  (0xFFFFUL << AUDCODEC_DAC_CH1_DEBUG_DATA_OUT_Pos)
#define AUDCODEC_DAC_CH1_DEBUG_DATA_OUT  AUDCODEC_DAC_CH1_DEBUG_DATA_OUT_Msk
#define AUDCODEC_DAC_CH1_DEBUG_BYPASS_Pos  (16U)
#define AUDCODEC_DAC_CH1_DEBUG_BYPASS_Msk  (0x1UL << AUDCODEC_DAC_CH1_DEBUG_BYPASS_Pos)
#define AUDCODEC_DAC_CH1_DEBUG_BYPASS   AUDCODEC_DAC_CH1_DEBUG_BYPASS_Msk

/************** Bit definition for AUDCODEC_DAC_CH0_DC register ***************/
#define AUDCODEC_DAC_CH0_DC_OFFSET_Pos  (0U)
#define AUDCODEC_DAC_CH0_DC_OFFSET_Msk  (0xFFFFFFUL << AUDCODEC_DAC_CH0_DC_OFFSET_Pos)
#define AUDCODEC_DAC_CH0_DC_OFFSET      AUDCODEC_DAC_CH0_DC_OFFSET_Msk

/************** Bit definition for AUDCODEC_DAC_CH1_DC register ***************/
#define AUDCODEC_DAC_CH1_DC_OFFSET_Pos  (0U)
#define AUDCODEC_DAC_CH1_DC_OFFSET_Msk  (0xFFFFFFUL << AUDCODEC_DAC_CH1_DC_OFFSET_Pos)
#define AUDCODEC_DAC_CH1_DC_OFFSET      AUDCODEC_DAC_CH1_DC_OFFSET_Msk

/************** Bit definition for AUDCODEC_COMMON_CFG register ***************/
#define AUDCODEC_COMMON_CFG_DC_TR_Pos   (0U)
#define AUDCODEC_COMMON_CFG_DC_TR_Msk   (0x7UL << AUDCODEC_COMMON_CFG_DC_TR_Pos)
#define AUDCODEC_COMMON_CFG_DC_TR       AUDCODEC_COMMON_CFG_DC_TR_Msk
#define AUDCODEC_COMMON_CFG_DC_BR_Pos   (3U)
#define AUDCODEC_COMMON_CFG_DC_BR_Msk   (0x7UL << AUDCODEC_COMMON_CFG_DC_BR_Pos)
#define AUDCODEC_COMMON_CFG_DC_BR       AUDCODEC_COMMON_CFG_DC_BR_Msk
#define AUDCODEC_COMMON_CFG_DC_MR_Pos   (6U)
#define AUDCODEC_COMMON_CFG_DC_MR_Msk   (0x7UL << AUDCODEC_COMMON_CFG_DC_MR_Pos)
#define AUDCODEC_COMMON_CFG_DC_MR       AUDCODEC_COMMON_CFG_DC_MR_Msk

/**************** Bit definition for AUDCODEC_BG_CFG0 register ****************/
#define AUDCODEC_BG_CFG0_EN_Pos         (0U)
#define AUDCODEC_BG_CFG0_EN_Msk         (0x1UL << AUDCODEC_BG_CFG0_EN_Pos)
#define AUDCODEC_BG_CFG0_EN             AUDCODEC_BG_CFG0_EN_Msk
#define AUDCODEC_BG_CFG0_LP_MODE_Pos    (1U)
#define AUDCODEC_BG_CFG0_LP_MODE_Msk    (0x1UL << AUDCODEC_BG_CFG0_LP_MODE_Pos)
#define AUDCODEC_BG_CFG0_LP_MODE        AUDCODEC_BG_CFG0_LP_MODE_Msk
#define AUDCODEC_BG_CFG0_VREF_SEL_Pos   (2U)
#define AUDCODEC_BG_CFG0_VREF_SEL_Msk   (0xFUL << AUDCODEC_BG_CFG0_VREF_SEL_Pos)
#define AUDCODEC_BG_CFG0_VREF_SEL       AUDCODEC_BG_CFG0_VREF_SEL_Msk
#define AUDCODEC_BG_CFG0_EN_CHOP_Pos    (6U)
#define AUDCODEC_BG_CFG0_EN_CHOP_Msk    (0x1UL << AUDCODEC_BG_CFG0_EN_CHOP_Pos)
#define AUDCODEC_BG_CFG0_EN_CHOP        AUDCODEC_BG_CFG0_EN_CHOP_Msk
#define AUDCODEC_BG_CFG0_EN_SMPL_Pos    (7U)
#define AUDCODEC_BG_CFG0_EN_SMPL_Msk    (0x1UL << AUDCODEC_BG_CFG0_EN_SMPL_Pos)
#define AUDCODEC_BG_CFG0_EN_SMPL        AUDCODEC_BG_CFG0_EN_SMPL_Msk
#define AUDCODEC_BG_CFG0_EN_RCFLT_Pos   (8U)
#define AUDCODEC_BG_CFG0_EN_RCFLT_Msk   (0x1UL << AUDCODEC_BG_CFG0_EN_RCFLT_Pos)
#define AUDCODEC_BG_CFG0_EN_RCFLT       AUDCODEC_BG_CFG0_EN_RCFLT_Msk
#define AUDCODEC_BG_CFG0_MIC_VREF_SEL_Pos  (9U)
#define AUDCODEC_BG_CFG0_MIC_VREF_SEL_Msk  (0x7UL << AUDCODEC_BG_CFG0_MIC_VREF_SEL_Pos)
#define AUDCODEC_BG_CFG0_MIC_VREF_SEL   AUDCODEC_BG_CFG0_MIC_VREF_SEL_Msk
#define AUDCODEC_BG_CFG0_EN_AMP_Pos     (12U)
#define AUDCODEC_BG_CFG0_EN_AMP_Msk     (0x1UL << AUDCODEC_BG_CFG0_EN_AMP_Pos)
#define AUDCODEC_BG_CFG0_EN_AMP         AUDCODEC_BG_CFG0_EN_AMP_Msk
#define AUDCODEC_BG_CFG0_SET_VC_Pos     (13U)
#define AUDCODEC_BG_CFG0_SET_VC_Msk     (0x1UL << AUDCODEC_BG_CFG0_SET_VC_Pos)
#define AUDCODEC_BG_CFG0_SET_VC         AUDCODEC_BG_CFG0_SET_VC_Msk

/**************** Bit definition for AUDCODEC_BG_CFG1 register ****************/
#define AUDCODEC_BG_CFG1_SAMPCLK_HI_Pos  (0U)
#define AUDCODEC_BG_CFG1_SAMPCLK_HI_Msk  (0xFFFFFFFFUL << AUDCODEC_BG_CFG1_SAMPCLK_HI_Pos)
#define AUDCODEC_BG_CFG1_SAMPCLK_HI     AUDCODEC_BG_CFG1_SAMPCLK_HI_Msk

/**************** Bit definition for AUDCODEC_BG_CFG2 register ****************/
#define AUDCODEC_BG_CFG2_SAMPCLK_LO_Pos  (0U)
#define AUDCODEC_BG_CFG2_SAMPCLK_LO_Msk  (0xFFFFFFFFUL << AUDCODEC_BG_CFG2_SAMPCLK_LO_Pos)
#define AUDCODEC_BG_CFG2_SAMPCLK_LO     AUDCODEC_BG_CFG2_SAMPCLK_LO_Msk

/************** Bit definition for AUDCODEC_REFGEN_CFG register ***************/
#define AUDCODEC_REFGEN_CFG_EN_Pos      (0U)
#define AUDCODEC_REFGEN_CFG_EN_Msk      (0x1UL << AUDCODEC_REFGEN_CFG_EN_Pos)
#define AUDCODEC_REFGEN_CFG_EN          AUDCODEC_REFGEN_CFG_EN_Msk
#define AUDCODEC_REFGEN_CFG_EN_CHOP_Pos  (1U)
#define AUDCODEC_REFGEN_CFG_EN_CHOP_Msk  (0x1UL << AUDCODEC_REFGEN_CFG_EN_CHOP_Pos)
#define AUDCODEC_REFGEN_CFG_EN_CHOP     AUDCODEC_REFGEN_CFG_EN_CHOP_Msk
#define AUDCODEC_REFGEN_CFG_BM_Pos      (2U)
#define AUDCODEC_REFGEN_CFG_BM_Msk      (0x3UL << AUDCODEC_REFGEN_CFG_BM_Pos)
#define AUDCODEC_REFGEN_CFG_BM          AUDCODEC_REFGEN_CFG_BM_Msk
#define AUDCODEC_REFGEN_CFG_LP_MODE_Pos  (4U)
#define AUDCODEC_REFGEN_CFG_LP_MODE_Msk  (0x1UL << AUDCODEC_REFGEN_CFG_LP_MODE_Pos)
#define AUDCODEC_REFGEN_CFG_LP_MODE     AUDCODEC_REFGEN_CFG_LP_MODE_Msk
#define AUDCODEC_REFGEN_CFG_LV_MODE_Pos  (5U)
#define AUDCODEC_REFGEN_CFG_LV_MODE_Msk  (0x1UL << AUDCODEC_REFGEN_CFG_LV_MODE_Pos)
#define AUDCODEC_REFGEN_CFG_LV_MODE     AUDCODEC_REFGEN_CFG_LV_MODE_Msk
#define AUDCODEC_REFGEN_CFG_RZSEL_Pos   (6U)
#define AUDCODEC_REFGEN_CFG_RZSEL_Msk   (0x3UL << AUDCODEC_REFGEN_CFG_RZSEL_Pos)
#define AUDCODEC_REFGEN_CFG_RZSEL       AUDCODEC_REFGEN_CFG_RZSEL_Msk
#define AUDCODEC_REFGEN_CFG_DISCHG_Pos  (8U)
#define AUDCODEC_REFGEN_CFG_DISCHG_Msk  (0x1UL << AUDCODEC_REFGEN_CFG_DISCHG_Pos)
#define AUDCODEC_REFGEN_CFG_DISCHG      AUDCODEC_REFGEN_CFG_DISCHG_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG0 register ****************/
#define AUDCODEC_PLL_CFG0_ICP_OS_SEL_Pos  (0U)
#define AUDCODEC_PLL_CFG0_ICP_OS_SEL_Msk  (0x1FUL << AUDCODEC_PLL_CFG0_ICP_OS_SEL_Pos)
#define AUDCODEC_PLL_CFG0_ICP_OS_SEL    AUDCODEC_PLL_CFG0_ICP_OS_SEL_Msk
#define AUDCODEC_PLL_CFG0_OPEN_Pos      (5U)
#define AUDCODEC_PLL_CFG0_OPEN_Msk      (0x1UL << AUDCODEC_PLL_CFG0_OPEN_Pos)
#define AUDCODEC_PLL_CFG0_OPEN          AUDCODEC_PLL_CFG0_OPEN_Msk
#define AUDCODEC_PLL_CFG0_ICP_SEL_Pos   (6U)
#define AUDCODEC_PLL_CFG0_ICP_SEL_Msk   (0x1FUL << AUDCODEC_PLL_CFG0_ICP_SEL_Pos)
#define AUDCODEC_PLL_CFG0_ICP_SEL       AUDCODEC_PLL_CFG0_ICP_SEL_Msk
#define AUDCODEC_PLL_CFG0_SEL_VREF_ANA_Pos  (11U)
#define AUDCODEC_PLL_CFG0_SEL_VREF_ANA_Msk  (0xFUL << AUDCODEC_PLL_CFG0_SEL_VREF_ANA_Pos)
#define AUDCODEC_PLL_CFG0_SEL_VREF_ANA  AUDCODEC_PLL_CFG0_SEL_VREF_ANA_Msk
#define AUDCODEC_PLL_CFG0_EN_ANA_Pos    (15U)
#define AUDCODEC_PLL_CFG0_EN_ANA_Msk    (0x1UL << AUDCODEC_PLL_CFG0_EN_ANA_Pos)
#define AUDCODEC_PLL_CFG0_EN_ANA        AUDCODEC_PLL_CFG0_EN_ANA_Msk
#define AUDCODEC_PLL_CFG0_VCO_LP_MODE_Pos  (16U)
#define AUDCODEC_PLL_CFG0_VCO_LP_MODE_Msk  (0x1UL << AUDCODEC_PLL_CFG0_VCO_LP_MODE_Pos)
#define AUDCODEC_PLL_CFG0_VCO_LP_MODE   AUDCODEC_PLL_CFG0_VCO_LP_MODE_Msk
#define AUDCODEC_PLL_CFG0_FC_VCO_Pos    (17U)
#define AUDCODEC_PLL_CFG0_FC_VCO_Msk    (0x1FUL << AUDCODEC_PLL_CFG0_FC_VCO_Pos)
#define AUDCODEC_PLL_CFG0_FC_VCO        AUDCODEC_PLL_CFG0_FC_VCO_Msk
#define AUDCODEC_PLL_CFG0_EN_VCO_FLT_Pos  (22U)
#define AUDCODEC_PLL_CFG0_EN_VCO_FLT_Msk  (0x1UL << AUDCODEC_PLL_CFG0_EN_VCO_FLT_Pos)
#define AUDCODEC_PLL_CFG0_EN_VCO_FLT    AUDCODEC_PLL_CFG0_EN_VCO_FLT_Msk
#define AUDCODEC_PLL_CFG0_SEL_VREF_VCO_Pos  (23U)
#define AUDCODEC_PLL_CFG0_SEL_VREF_VCO_Msk  (0xFUL << AUDCODEC_PLL_CFG0_SEL_VREF_VCO_Pos)
#define AUDCODEC_PLL_CFG0_SEL_VREF_VCO  AUDCODEC_PLL_CFG0_SEL_VREF_VCO_Msk
#define AUDCODEC_PLL_CFG0_EN_VCO_Pos    (27U)
#define AUDCODEC_PLL_CFG0_EN_VCO_Msk    (0x1UL << AUDCODEC_PLL_CFG0_EN_VCO_Pos)
#define AUDCODEC_PLL_CFG0_EN_VCO        AUDCODEC_PLL_CFG0_EN_VCO_Msk
#define AUDCODEC_PLL_CFG0_EN_IARY_Pos   (28U)
#define AUDCODEC_PLL_CFG0_EN_IARY_Msk   (0x1UL << AUDCODEC_PLL_CFG0_EN_IARY_Pos)
#define AUDCODEC_PLL_CFG0_EN_IARY       AUDCODEC_PLL_CFG0_EN_IARY_Msk
#define AUDCODEC_PLL_CFG0_SEL_CKREF_Pos  (29U)
#define AUDCODEC_PLL_CFG0_SEL_CKREF_Msk  (0x3UL << AUDCODEC_PLL_CFG0_SEL_CKREF_Pos)
#define AUDCODEC_PLL_CFG0_SEL_CKREF     AUDCODEC_PLL_CFG0_SEL_CKREF_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG1 register ****************/
#define AUDCODEC_PLL_CFG1_R3_SEL_Pos    (0U)
#define AUDCODEC_PLL_CFG1_R3_SEL_Msk    (0xFUL << AUDCODEC_PLL_CFG1_R3_SEL_Pos)
#define AUDCODEC_PLL_CFG1_R3_SEL        AUDCODEC_PLL_CFG1_R3_SEL_Msk
#define AUDCODEC_PLL_CFG1_RZ_SEL_Pos    (4U)
#define AUDCODEC_PLL_CFG1_RZ_SEL_Msk    (0xFUL << AUDCODEC_PLL_CFG1_RZ_SEL_Pos)
#define AUDCODEC_PLL_CFG1_RZ_SEL        AUDCODEC_PLL_CFG1_RZ_SEL_Msk
#define AUDCODEC_PLL_CFG1_C2_SEL_Pos    (8U)
#define AUDCODEC_PLL_CFG1_C2_SEL_Msk    (0x7UL << AUDCODEC_PLL_CFG1_C2_SEL_Pos)
#define AUDCODEC_PLL_CFG1_C2_SEL        AUDCODEC_PLL_CFG1_C2_SEL_Msk
#define AUDCODEC_PLL_CFG1_CZ_SEL_Pos    (11U)
#define AUDCODEC_PLL_CFG1_CZ_SEL_Msk    (0x7UL << AUDCODEC_PLL_CFG1_CZ_SEL_Pos)
#define AUDCODEC_PLL_CFG1_CZ_SEL        AUDCODEC_PLL_CFG1_CZ_SEL_Msk
#define AUDCODEC_PLL_CFG1_CSD_RST_Pos   (14U)
#define AUDCODEC_PLL_CFG1_CSD_RST_Msk   (0x1UL << AUDCODEC_PLL_CFG1_CSD_RST_Pos)
#define AUDCODEC_PLL_CFG1_CSD_RST       AUDCODEC_PLL_CFG1_CSD_RST_Msk
#define AUDCODEC_PLL_CFG1_CSD_EN_Pos    (15U)
#define AUDCODEC_PLL_CFG1_CSD_EN_Msk    (0x1UL << AUDCODEC_PLL_CFG1_CSD_EN_Pos)
#define AUDCODEC_PLL_CFG1_CSD_EN        AUDCODEC_PLL_CFG1_CSD_EN_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG2 register ****************/
#define AUDCODEC_PLL_CFG2_MMD_STG_Pos   (0U)
#define AUDCODEC_PLL_CFG2_MMD_STG_Msk   (0x3UL << AUDCODEC_PLL_CFG2_MMD_STG_Pos)
#define AUDCODEC_PLL_CFG2_MMD_STG       AUDCODEC_PLL_CFG2_MMD_STG_Msk
#define AUDCODEC_PLL_CFG2_TR_DTEST_Pos  (2U)
#define AUDCODEC_PLL_CFG2_TR_DTEST_Msk  (0xFUL << AUDCODEC_PLL_CFG2_TR_DTEST_Pos)
#define AUDCODEC_PLL_CFG2_TR_DTEST      AUDCODEC_PLL_CFG2_TR_DTEST_Msk
#define AUDCODEC_PLL_CFG2_TE_DTEST_Pos  (6U)
#define AUDCODEC_PLL_CFG2_TE_DTEST_Msk  (0x1UL << AUDCODEC_PLL_CFG2_TE_DTEST_Pos)
#define AUDCODEC_PLL_CFG2_TE_DTEST      AUDCODEC_PLL_CFG2_TE_DTEST_Msk
#define AUDCODEC_PLL_CFG2_RSTB_SYNC_EN_Pos  (7U)
#define AUDCODEC_PLL_CFG2_RSTB_SYNC_EN_Msk  (0x1UL << AUDCODEC_PLL_CFG2_RSTB_SYNC_EN_Pos)
#define AUDCODEC_PLL_CFG2_RSTB_SYNC_EN  AUDCODEC_PLL_CFG2_RSTB_SYNC_EN_Msk
#define AUDCODEC_PLL_CFG2_RSTB_Pos      (8U)
#define AUDCODEC_PLL_CFG2_RSTB_Msk      (0x1UL << AUDCODEC_PLL_CFG2_RSTB_Pos)
#define AUDCODEC_PLL_CFG2_RSTB          AUDCODEC_PLL_CFG2_RSTB_Msk
#define AUDCODEC_PLL_CFG2_SEL_VREF_DIG_Pos  (9U)
#define AUDCODEC_PLL_CFG2_SEL_VREF_DIG_Msk  (0xFUL << AUDCODEC_PLL_CFG2_SEL_VREF_DIG_Pos)
#define AUDCODEC_PLL_CFG2_SEL_VREF_DIG  AUDCODEC_PLL_CFG2_SEL_VREF_DIG_Msk
#define AUDCODEC_PLL_CFG2_EN_DIG_Pos    (13U)
#define AUDCODEC_PLL_CFG2_EN_DIG_Msk    (0x1UL << AUDCODEC_PLL_CFG2_EN_DIG_Pos)
#define AUDCODEC_PLL_CFG2_EN_DIG        AUDCODEC_PLL_CFG2_EN_DIG_Msk
#define AUDCODEC_PLL_CFG2_EN_LF_TSTBUF_Pos  (14U)
#define AUDCODEC_PLL_CFG2_EN_LF_TSTBUF_Msk  (0x1UL << AUDCODEC_PLL_CFG2_EN_LF_TSTBUF_Pos)
#define AUDCODEC_PLL_CFG2_EN_LF_TSTBUF  AUDCODEC_PLL_CFG2_EN_LF_TSTBUF_Msk
#define AUDCODEC_PLL_CFG2_SEL_LF_VCIN_Pos  (15U)
#define AUDCODEC_PLL_CFG2_SEL_LF_VCIN_Msk  (0x7UL << AUDCODEC_PLL_CFG2_SEL_LF_VCIN_Pos)
#define AUDCODEC_PLL_CFG2_SEL_LF_VCIN   AUDCODEC_PLL_CFG2_SEL_LF_VCIN_Msk
#define AUDCODEC_PLL_CFG2_EN_LF_VCIN_Pos  (18U)
#define AUDCODEC_PLL_CFG2_EN_LF_VCIN_Msk  (0x1UL << AUDCODEC_PLL_CFG2_EN_LF_VCIN_Pos)
#define AUDCODEC_PLL_CFG2_EN_LF_VCIN    AUDCODEC_PLL_CFG2_EN_LF_VCIN_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG3 register ****************/
#define AUDCODEC_PLL_CFG3_SDIN_Pos      (0U)
#define AUDCODEC_PLL_CFG3_SDIN_Msk      (0xFFFFFUL << AUDCODEC_PLL_CFG3_SDIN_Pos)
#define AUDCODEC_PLL_CFG3_SDIN          AUDCODEC_PLL_CFG3_SDIN_Msk
#define AUDCODEC_PLL_CFG3_FCW_Pos       (20U)
#define AUDCODEC_PLL_CFG3_FCW_Msk       (0x1FUL << AUDCODEC_PLL_CFG3_FCW_Pos)
#define AUDCODEC_PLL_CFG3_FCW           AUDCODEC_PLL_CFG3_FCW_Msk
#define AUDCODEC_PLL_CFG3_SDM_UPDATE_Pos  (25U)
#define AUDCODEC_PLL_CFG3_SDM_UPDATE_Msk  (0x1UL << AUDCODEC_PLL_CFG3_SDM_UPDATE_Pos)
#define AUDCODEC_PLL_CFG3_SDM_UPDATE    AUDCODEC_PLL_CFG3_SDM_UPDATE_Msk
#define AUDCODEC_PLL_CFG3_SDMIN_BYPASS_Pos  (26U)
#define AUDCODEC_PLL_CFG3_SDMIN_BYPASS_Msk  (0x1UL << AUDCODEC_PLL_CFG3_SDMIN_BYPASS_Pos)
#define AUDCODEC_PLL_CFG3_SDMIN_BYPASS  AUDCODEC_PLL_CFG3_SDMIN_BYPASS_Msk
#define AUDCODEC_PLL_CFG3_SDM_MODE_Pos  (27U)
#define AUDCODEC_PLL_CFG3_SDM_MODE_Msk  (0x1UL << AUDCODEC_PLL_CFG3_SDM_MODE_Pos)
#define AUDCODEC_PLL_CFG3_SDM_MODE      AUDCODEC_PLL_CFG3_SDM_MODE_Msk
#define AUDCODEC_PLL_CFG3_EN_SDM_DITHER_Pos  (28U)
#define AUDCODEC_PLL_CFG3_EN_SDM_DITHER_Msk  (0x1UL << AUDCODEC_PLL_CFG3_EN_SDM_DITHER_Pos)
#define AUDCODEC_PLL_CFG3_EN_SDM_DITHER  AUDCODEC_PLL_CFG3_EN_SDM_DITHER_Msk
#define AUDCODEC_PLL_CFG3_SDM_DITHER_Pos  (29U)
#define AUDCODEC_PLL_CFG3_SDM_DITHER_Msk  (0x1UL << AUDCODEC_PLL_CFG3_SDM_DITHER_Pos)
#define AUDCODEC_PLL_CFG3_SDM_DITHER    AUDCODEC_PLL_CFG3_SDM_DITHER_Msk
#define AUDCODEC_PLL_CFG3_EN_SDM_Pos    (30U)
#define AUDCODEC_PLL_CFG3_EN_SDM_Msk    (0x1UL << AUDCODEC_PLL_CFG3_EN_SDM_Pos)
#define AUDCODEC_PLL_CFG3_EN_SDM        AUDCODEC_PLL_CFG3_EN_SDM_Msk
#define AUDCODEC_PLL_CFG3_SDMCLK_POL_Pos  (31U)
#define AUDCODEC_PLL_CFG3_SDMCLK_POL_Msk  (0x1UL << AUDCODEC_PLL_CFG3_SDMCLK_POL_Pos)
#define AUDCODEC_PLL_CFG3_SDMCLK_POL    AUDCODEC_PLL_CFG3_SDMCLK_POL_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG4 register ****************/
#define AUDCODEC_PLL_CFG4_DIVB_CLK_CHOP_DAC_Pos  (0U)
#define AUDCODEC_PLL_CFG4_DIVB_CLK_CHOP_DAC_Msk  (0x3UL << AUDCODEC_PLL_CFG4_DIVB_CLK_CHOP_DAC_Pos)
#define AUDCODEC_PLL_CFG4_DIVB_CLK_CHOP_DAC  AUDCODEC_PLL_CFG4_DIVB_CLK_CHOP_DAC_Msk
#define AUDCODEC_PLL_CFG4_DIVA_CLK_CHOP_DAC_Pos  (2U)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_CHOP_DAC_Msk  (0x7UL << AUDCODEC_PLL_CFG4_DIVA_CLK_CHOP_DAC_Pos)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_CHOP_DAC  AUDCODEC_PLL_CFG4_DIVA_CLK_CHOP_DAC_Msk
#define AUDCODEC_PLL_CFG4_EN_CLK_CHOP_DAC_Pos  (5U)
#define AUDCODEC_PLL_CFG4_EN_CLK_CHOP_DAC_Msk  (0x1UL << AUDCODEC_PLL_CFG4_EN_CLK_CHOP_DAC_Pos)
#define AUDCODEC_PLL_CFG4_EN_CLK_CHOP_DAC  AUDCODEC_PLL_CFG4_EN_CLK_CHOP_DAC_Msk
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DAC_Pos  (6U)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DAC_Msk  (0x1FUL << AUDCODEC_PLL_CFG4_DIVA_CLK_DAC_Pos)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DAC  AUDCODEC_PLL_CFG4_DIVA_CLK_DAC_Msk
#define AUDCODEC_PLL_CFG4_EN_CLK_DAC_Pos  (11U)
#define AUDCODEC_PLL_CFG4_EN_CLK_DAC_Msk  (0x1UL << AUDCODEC_PLL_CFG4_EN_CLK_DAC_Pos)
#define AUDCODEC_PLL_CFG4_EN_CLK_DAC    AUDCODEC_PLL_CFG4_EN_CLK_DAC_Msk
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC_Pos  (12U)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC_Msk  (0x1UL << AUDCODEC_PLL_CFG4_SEL_CLK_DAC_Pos)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC   AUDCODEC_PLL_CFG4_SEL_CLK_DAC_Msk
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC_SOURCE_Pos  (13U)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC_SOURCE_Msk  (0x3UL << AUDCODEC_PLL_CFG4_SEL_CLK_DAC_SOURCE_Pos)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DAC_SOURCE  AUDCODEC_PLL_CFG4_SEL_CLK_DAC_SOURCE_Msk
#define AUDCODEC_PLL_CFG4_SEL_CLK_DIG_Pos  (15U)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DIG_Msk  (0x1UL << AUDCODEC_PLL_CFG4_SEL_CLK_DIG_Pos)
#define AUDCODEC_PLL_CFG4_SEL_CLK_DIG   AUDCODEC_PLL_CFG4_SEL_CLK_DIG_Msk
#define AUDCODEC_PLL_CFG4_CLK_DIG_STR_Pos  (16U)
#define AUDCODEC_PLL_CFG4_CLK_DIG_STR_Msk  (0x3UL << AUDCODEC_PLL_CFG4_CLK_DIG_STR_Pos)
#define AUDCODEC_PLL_CFG4_CLK_DIG_STR   AUDCODEC_PLL_CFG4_CLK_DIG_STR_Msk
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DIG_Pos  (18U)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DIG_Msk  (0x1FUL << AUDCODEC_PLL_CFG4_DIVA_CLK_DIG_Pos)
#define AUDCODEC_PLL_CFG4_DIVA_CLK_DIG  AUDCODEC_PLL_CFG4_DIVA_CLK_DIG_Msk
#define AUDCODEC_PLL_CFG4_EN_CLK_DIG_Pos  (23U)
#define AUDCODEC_PLL_CFG4_EN_CLK_DIG_Msk  (0x1UL << AUDCODEC_PLL_CFG4_EN_CLK_DIG_Pos)
#define AUDCODEC_PLL_CFG4_EN_CLK_DIG    AUDCODEC_PLL_CFG4_EN_CLK_DIG_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG5 register ****************/
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_BG_Pos  (0U)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_BG_Msk  (0x3UL << AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_BG_Pos)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_BG  AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_BG_Msk
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_BG_Pos  (2U)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_BG_Msk  (0x1FUL << AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_BG_Pos)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_BG  AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_BG_Msk
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_BG_Pos  (7U)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_BG_Msk  (0x1UL << AUDCODEC_PLL_CFG5_EN_CLK_CHOP_BG_Pos)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_BG  AUDCODEC_PLL_CFG5_EN_CLK_CHOP_BG_Msk
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_REFGEN_Pos  (8U)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_REFGEN_Msk  (0x3UL << AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_REFGEN_Pos)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_REFGEN  AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_REFGEN_Msk
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_REFGEN_Pos  (10U)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_REFGEN_Msk  (0x1FUL << AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_REFGEN_Pos)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_REFGEN  AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_REFGEN_Msk
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_REFGEN_Pos  (15U)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_REFGEN_Msk  (0x1UL << AUDCODEC_PLL_CFG5_EN_CLK_CHOP_REFGEN_Pos)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_REFGEN  AUDCODEC_PLL_CFG5_EN_CLK_CHOP_REFGEN_Msk
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_DAC2_Pos  (16U)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_DAC2_Msk  (0x3UL << AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_DAC2  AUDCODEC_PLL_CFG5_DIVB_CLK_CHOP_DAC2_Msk
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_DAC2_Pos  (18U)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_DAC2_Msk  (0x7UL << AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_DAC2  AUDCODEC_PLL_CFG5_DIVA_CLK_CHOP_DAC2_Msk
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_DAC2_Pos  (21U)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_DAC2_Msk  (0x1UL << AUDCODEC_PLL_CFG5_EN_CLK_CHOP_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_EN_CLK_CHOP_DAC2  AUDCODEC_PLL_CFG5_EN_CLK_CHOP_DAC2_Msk
#define AUDCODEC_PLL_CFG5_DIVA_CLK_DAC2_Pos  (22U)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_DAC2_Msk  (0x1FUL << AUDCODEC_PLL_CFG5_DIVA_CLK_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_DIVA_CLK_DAC2  AUDCODEC_PLL_CFG5_DIVA_CLK_DAC2_Msk
#define AUDCODEC_PLL_CFG5_EN_CLK_DAC2_Pos  (27U)
#define AUDCODEC_PLL_CFG5_EN_CLK_DAC2_Msk  (0x1UL << AUDCODEC_PLL_CFG5_EN_CLK_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_EN_CLK_DAC2   AUDCODEC_PLL_CFG5_EN_CLK_DAC2_Msk
#define AUDCODEC_PLL_CFG5_SEL_CLK_DAC2_Pos  (28U)
#define AUDCODEC_PLL_CFG5_SEL_CLK_DAC2_Msk  (0x1UL << AUDCODEC_PLL_CFG5_SEL_CLK_DAC2_Pos)
#define AUDCODEC_PLL_CFG5_SEL_CLK_DAC2  AUDCODEC_PLL_CFG5_SEL_CLK_DAC2_Msk

/*************** Bit definition for AUDCODEC_PLL_CFG6 register ****************/
#define AUDCODEC_PLL_CFG6_SEL_TST_CLK_Pos  (0U)
#define AUDCODEC_PLL_CFG6_SEL_TST_CLK_Msk  (0xFUL << AUDCODEC_PLL_CFG6_SEL_TST_CLK_Pos)
#define AUDCODEC_PLL_CFG6_SEL_TST_CLK   AUDCODEC_PLL_CFG6_SEL_TST_CLK_Msk
#define AUDCODEC_PLL_CFG6_EN_TST_CLK_Pos  (4U)
#define AUDCODEC_PLL_CFG6_EN_TST_CLK_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_TST_CLK_Pos)
#define AUDCODEC_PLL_CFG6_EN_TST_CLK    AUDCODEC_PLL_CFG6_EN_TST_CLK_Msk
#define AUDCODEC_PLL_CFG6_EN_CLK_RCCAL_Pos  (5U)
#define AUDCODEC_PLL_CFG6_EN_CLK_RCCAL_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_CLK_RCCAL_Pos)
#define AUDCODEC_PLL_CFG6_EN_CLK_RCCAL  AUDCODEC_PLL_CFG6_EN_CLK_RCCAL_Msk
#define AUDCODEC_PLL_CFG6_SEL_CLK_CHOP_MICBIAS_Pos  (6U)
#define AUDCODEC_PLL_CFG6_SEL_CLK_CHOP_MICBIAS_Msk  (0x3UL << AUDCODEC_PLL_CFG6_SEL_CLK_CHOP_MICBIAS_Pos)
#define AUDCODEC_PLL_CFG6_SEL_CLK_CHOP_MICBIAS  AUDCODEC_PLL_CFG6_SEL_CLK_CHOP_MICBIAS_Msk
#define AUDCODEC_PLL_CFG6_EN_CLK_CHOP_MICBIAS_Pos  (8U)
#define AUDCODEC_PLL_CFG6_EN_CLK_CHOP_MICBIAS_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_CLK_CHOP_MICBIAS_Pos)
#define AUDCODEC_PLL_CFG6_EN_CLK_CHOP_MICBIAS  AUDCODEC_PLL_CFG6_EN_CLK_CHOP_MICBIAS_Msk
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC2_Pos  (9U)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC2_Msk  (0x1UL << AUDCODEC_PLL_CFG6_SEL_CLK_ADC2_Pos)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC2  AUDCODEC_PLL_CFG6_SEL_CLK_ADC2_Msk
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC2_Pos  (10U)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC2_Msk  (0x7UL << AUDCODEC_PLL_CFG6_DIVA_CLK_ADC2_Pos)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC2  AUDCODEC_PLL_CFG6_DIVA_CLK_ADC2_Msk
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC2_Pos  (13U)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC2_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_CLK_ADC2_Pos)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC2   AUDCODEC_PLL_CFG6_EN_CLK_ADC2_Msk
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC1_Pos  (14U)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC1_Msk  (0x1UL << AUDCODEC_PLL_CFG6_SEL_CLK_ADC1_Pos)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC1  AUDCODEC_PLL_CFG6_SEL_CLK_ADC1_Msk
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC1_Pos  (15U)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC1_Msk  (0x7UL << AUDCODEC_PLL_CFG6_DIVA_CLK_ADC1_Pos)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC1  AUDCODEC_PLL_CFG6_DIVA_CLK_ADC1_Msk
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC1_Pos  (18U)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC1_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_CLK_ADC1_Pos)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC1   AUDCODEC_PLL_CFG6_EN_CLK_ADC1_Msk
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC0_Pos  (19U)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC0_Msk  (0x1UL << AUDCODEC_PLL_CFG6_SEL_CLK_ADC0_Pos)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC0  AUDCODEC_PLL_CFG6_SEL_CLK_ADC0_Msk
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC0_Pos  (20U)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC0_Msk  (0x7UL << AUDCODEC_PLL_CFG6_DIVA_CLK_ADC0_Pos)
#define AUDCODEC_PLL_CFG6_DIVA_CLK_ADC0  AUDCODEC_PLL_CFG6_DIVA_CLK_ADC0_Msk
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC0_Pos  (23U)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC0_Msk  (0x1UL << AUDCODEC_PLL_CFG6_EN_CLK_ADC0_Pos)
#define AUDCODEC_PLL_CFG6_EN_CLK_ADC0   AUDCODEC_PLL_CFG6_EN_CLK_ADC0_Msk
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC_SOURCE_Pos  (24U)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC_SOURCE_Msk  (0x3UL << AUDCODEC_PLL_CFG6_SEL_CLK_ADC_SOURCE_Pos)
#define AUDCODEC_PLL_CFG6_SEL_CLK_ADC_SOURCE  AUDCODEC_PLL_CFG6_SEL_CLK_ADC_SOURCE_Msk

/*************** Bit definition for AUDCODEC_PLL_STAT register ****************/
#define AUDCODEC_PLL_STAT_UNLOCK_Pos    (0U)
#define AUDCODEC_PLL_STAT_UNLOCK_Msk    (0x1UL << AUDCODEC_PLL_STAT_UNLOCK_Pos)
#define AUDCODEC_PLL_STAT_UNLOCK        AUDCODEC_PLL_STAT_UNLOCK_Msk
#define AUDCODEC_PLL_STAT_SLIPPED_UP_Pos  (1U)
#define AUDCODEC_PLL_STAT_SLIPPED_UP_Msk  (0x1UL << AUDCODEC_PLL_STAT_SLIPPED_UP_Pos)
#define AUDCODEC_PLL_STAT_SLIPPED_UP    AUDCODEC_PLL_STAT_SLIPPED_UP_Msk
#define AUDCODEC_PLL_STAT_SLIPPED_DN_Pos  (2U)
#define AUDCODEC_PLL_STAT_SLIPPED_DN_Msk  (0x1UL << AUDCODEC_PLL_STAT_SLIPPED_DN_Pos)
#define AUDCODEC_PLL_STAT_SLIPPED_DN    AUDCODEC_PLL_STAT_SLIPPED_DN_Msk

/************** Bit definition for AUDCODEC_PLL_CAL_CFG register **************/
#define AUDCODEC_PLL_CAL_CFG_EN_Pos     (0U)
#define AUDCODEC_PLL_CAL_CFG_EN_Msk     (0x1UL << AUDCODEC_PLL_CAL_CFG_EN_Pos)
#define AUDCODEC_PLL_CAL_CFG_EN         AUDCODEC_PLL_CAL_CFG_EN_Msk
#define AUDCODEC_PLL_CAL_CFG_DONE_Pos   (1U)
#define AUDCODEC_PLL_CAL_CFG_DONE_Msk   (0x1UL << AUDCODEC_PLL_CAL_CFG_DONE_Pos)
#define AUDCODEC_PLL_CAL_CFG_DONE       AUDCODEC_PLL_CAL_CFG_DONE_Msk
#define AUDCODEC_PLL_CAL_CFG_LEN_Pos    (16U)
#define AUDCODEC_PLL_CAL_CFG_LEN_Msk    (0xFFFFUL << AUDCODEC_PLL_CAL_CFG_LEN_Pos)
#define AUDCODEC_PLL_CAL_CFG_LEN        AUDCODEC_PLL_CAL_CFG_LEN_Msk

/************ Bit definition for AUDCODEC_PLL_CAL_RESULT register *************/
#define AUDCODEC_PLL_CAL_RESULT_XTAL_CNT_Pos  (0U)
#define AUDCODEC_PLL_CAL_RESULT_XTAL_CNT_Msk  (0xFFFFUL << AUDCODEC_PLL_CAL_RESULT_XTAL_CNT_Pos)
#define AUDCODEC_PLL_CAL_RESULT_XTAL_CNT  AUDCODEC_PLL_CAL_RESULT_XTAL_CNT_Msk
#define AUDCODEC_PLL_CAL_RESULT_PLL_CNT_Pos  (16U)
#define AUDCODEC_PLL_CAL_RESULT_PLL_CNT_Msk  (0xFFFFUL << AUDCODEC_PLL_CAL_RESULT_PLL_CNT_Pos)
#define AUDCODEC_PLL_CAL_RESULT_PLL_CNT  AUDCODEC_PLL_CAL_RESULT_PLL_CNT_Msk

/************** Bit definition for AUDCODEC_ADC_ANA_CFG register **************/
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_CHOP_EN_Pos  (0U)
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_CHOP_EN_Msk  (0x1UL << AUDCODEC_ADC_ANA_CFG_MICBIAS_CHOP_EN_Pos)
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_CHOP_EN  AUDCODEC_ADC_ANA_CFG_MICBIAS_CHOP_EN_Msk
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_EN_Pos  (1U)
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_EN_Msk  (0x1UL << AUDCODEC_ADC_ANA_CFG_MICBIAS_EN_Pos)
#define AUDCODEC_ADC_ANA_CFG_MICBIAS_EN  AUDCODEC_ADC_ANA_CFG_MICBIAS_EN_Msk
#define AUDCODEC_ADC_ANA_CFG_CAPCODE_Pos  (2U)
#define AUDCODEC_ADC_ANA_CFG_CAPCODE_Msk  (0x1FUL << AUDCODEC_ADC_ANA_CFG_CAPCODE_Pos)
#define AUDCODEC_ADC_ANA_CFG_CAPCODE    AUDCODEC_ADC_ANA_CFG_CAPCODE_Msk

/*************** Bit definition for AUDCODEC_ADC1_CFG1 register ***************/
#define AUDCODEC_ADC1_CFG1_PERI_BM_Pos  (0U)
#define AUDCODEC_ADC1_CFG1_PERI_BM_Msk  (0x3UL << AUDCODEC_ADC1_CFG1_PERI_BM_Pos)
#define AUDCODEC_ADC1_CFG1_PERI_BM      AUDCODEC_ADC1_CFG1_PERI_BM_Msk
#define AUDCODEC_ADC1_CFG1_CLKOUT_INV_Pos  (2U)
#define AUDCODEC_ADC1_CFG1_CLKOUT_INV_Msk  (0x1UL << AUDCODEC_ADC1_CFG1_CLKOUT_INV_Pos)
#define AUDCODEC_ADC1_CFG1_CLKOUT_INV   AUDCODEC_ADC1_CFG1_CLKOUT_INV_Msk
#define AUDCODEC_ADC1_CFG1_VCMST_Pos    (3U)
#define AUDCODEC_ADC1_CFG1_VCMST_Msk    (0x1UL << AUDCODEC_ADC1_CFG1_VCMST_Pos)
#define AUDCODEC_ADC1_CFG1_VCMST        AUDCODEC_ADC1_CFG1_VCMST_Msk
#define AUDCODEC_ADC1_CFG1_FCHOP_SEL_Pos  (4U)
#define AUDCODEC_ADC1_CFG1_FCHOP_SEL_Msk  (0x3UL << AUDCODEC_ADC1_CFG1_FCHOP_SEL_Pos)
#define AUDCODEC_ADC1_CFG1_FCHOP_SEL    AUDCODEC_ADC1_CFG1_FCHOP_SEL_Msk
#define AUDCODEC_ADC1_CFG1_VREF_SEL_Pos  (6U)
#define AUDCODEC_ADC1_CFG1_VREF_SEL_Msk  (0x7UL << AUDCODEC_ADC1_CFG1_VREF_SEL_Pos)
#define AUDCODEC_ADC1_CFG1_VREF_SEL     AUDCODEC_ADC1_CFG1_VREF_SEL_Msk
#define AUDCODEC_ADC1_CFG1_BM_INT2_Pos  (9U)
#define AUDCODEC_ADC1_CFG1_BM_INT2_Msk  (0x7UL << AUDCODEC_ADC1_CFG1_BM_INT2_Pos)
#define AUDCODEC_ADC1_CFG1_BM_INT2      AUDCODEC_ADC1_CFG1_BM_INT2_Msk
#define AUDCODEC_ADC1_CFG1_BM_INT1_Pos  (12U)
#define AUDCODEC_ADC1_CFG1_BM_INT1_Msk  (0x7UL << AUDCODEC_ADC1_CFG1_BM_INT1_Pos)
#define AUDCODEC_ADC1_CFG1_BM_INT1      AUDCODEC_ADC1_CFG1_BM_INT1_Msk
#define AUDCODEC_ADC1_CFG1_VST_SEL_Pos  (15U)
#define AUDCODEC_ADC1_CFG1_VST_SEL_Msk  (0x7UL << AUDCODEC_ADC1_CFG1_VST_SEL_Pos)
#define AUDCODEC_ADC1_CFG1_VST_SEL      AUDCODEC_ADC1_CFG1_VST_SEL_Msk
#define AUDCODEC_ADC1_CFG1_GC_Pos       (18U)
#define AUDCODEC_ADC1_CFG1_GC_Msk       (0x7UL << AUDCODEC_ADC1_CFG1_GC_Pos)
#define AUDCODEC_ADC1_CFG1_GC           AUDCODEC_ADC1_CFG1_GC_Msk
#define AUDCODEC_ADC1_CFG1_DACN_EN_Pos  (21U)
#define AUDCODEC_ADC1_CFG1_DACN_EN_Msk  (0x1UL << AUDCODEC_ADC1_CFG1_DACN_EN_Pos)
#define AUDCODEC_ADC1_CFG1_DACN_EN      AUDCODEC_ADC1_CFG1_DACN_EN_Msk
#define AUDCODEC_ADC1_CFG1_DIFF_EN_Pos  (22U)
#define AUDCODEC_ADC1_CFG1_DIFF_EN_Msk  (0x1UL << AUDCODEC_ADC1_CFG1_DIFF_EN_Pos)
#define AUDCODEC_ADC1_CFG1_DIFF_EN      AUDCODEC_ADC1_CFG1_DIFF_EN_Msk
#define AUDCODEC_ADC1_CFG1_FSP_Pos      (23U)
#define AUDCODEC_ADC1_CFG1_FSP_Msk      (0x3UL << AUDCODEC_ADC1_CFG1_FSP_Pos)
#define AUDCODEC_ADC1_CFG1_FSP          AUDCODEC_ADC1_CFG1_FSP_Msk

/*************** Bit definition for AUDCODEC_ADC1_CFG2 register ***************/
#define AUDCODEC_ADC1_CFG2_CLEAR_Pos    (0U)
#define AUDCODEC_ADC1_CFG2_CLEAR_Msk    (0x1UL << AUDCODEC_ADC1_CFG2_CLEAR_Pos)
#define AUDCODEC_ADC1_CFG2_CLEAR        AUDCODEC_ADC1_CFG2_CLEAR_Msk
#define AUDCODEC_ADC1_CFG2_CHOP_EN_Pos  (1U)
#define AUDCODEC_ADC1_CFG2_CHOP_EN_Msk  (0x1UL << AUDCODEC_ADC1_CFG2_CHOP_EN_Pos)
#define AUDCODEC_ADC1_CFG2_CHOP_EN      AUDCODEC_ADC1_CFG2_CHOP_EN_Msk
#define AUDCODEC_ADC1_CFG2_RSTB_Pos     (2U)
#define AUDCODEC_ADC1_CFG2_RSTB_Msk     (0x1UL << AUDCODEC_ADC1_CFG2_RSTB_Pos)
#define AUDCODEC_ADC1_CFG2_RSTB         AUDCODEC_ADC1_CFG2_RSTB_Msk
#define AUDCODEC_ADC1_CFG2_EN_Pos       (3U)
#define AUDCODEC_ADC1_CFG2_EN_Msk       (0x1UL << AUDCODEC_ADC1_CFG2_EN_Pos)
#define AUDCODEC_ADC1_CFG2_EN           AUDCODEC_ADC1_CFG2_EN_Msk

/*************** Bit definition for AUDCODEC_ADC2_CFG1 register ***************/
#define AUDCODEC_ADC2_CFG1_PERI_BM_Pos  (0U)
#define AUDCODEC_ADC2_CFG1_PERI_BM_Msk  (0x3UL << AUDCODEC_ADC2_CFG1_PERI_BM_Pos)
#define AUDCODEC_ADC2_CFG1_PERI_BM      AUDCODEC_ADC2_CFG1_PERI_BM_Msk
#define AUDCODEC_ADC2_CFG1_CLKOUT_INV_Pos  (2U)
#define AUDCODEC_ADC2_CFG1_CLKOUT_INV_Msk  (0x1UL << AUDCODEC_ADC2_CFG1_CLKOUT_INV_Pos)
#define AUDCODEC_ADC2_CFG1_CLKOUT_INV   AUDCODEC_ADC2_CFG1_CLKOUT_INV_Msk
#define AUDCODEC_ADC2_CFG1_VCMST_Pos    (3U)
#define AUDCODEC_ADC2_CFG1_VCMST_Msk    (0x1UL << AUDCODEC_ADC2_CFG1_VCMST_Pos)
#define AUDCODEC_ADC2_CFG1_VCMST        AUDCODEC_ADC2_CFG1_VCMST_Msk
#define AUDCODEC_ADC2_CFG1_FCHOP_SEL_Pos  (4U)
#define AUDCODEC_ADC2_CFG1_FCHOP_SEL_Msk  (0x3UL << AUDCODEC_ADC2_CFG1_FCHOP_SEL_Pos)
#define AUDCODEC_ADC2_CFG1_FCHOP_SEL    AUDCODEC_ADC2_CFG1_FCHOP_SEL_Msk
#define AUDCODEC_ADC2_CFG1_VREF_SEL_Pos  (6U)
#define AUDCODEC_ADC2_CFG1_VREF_SEL_Msk  (0x7UL << AUDCODEC_ADC2_CFG1_VREF_SEL_Pos)
#define AUDCODEC_ADC2_CFG1_VREF_SEL     AUDCODEC_ADC2_CFG1_VREF_SEL_Msk
#define AUDCODEC_ADC2_CFG1_BM_INT2_Pos  (9U)
#define AUDCODEC_ADC2_CFG1_BM_INT2_Msk  (0x7UL << AUDCODEC_ADC2_CFG1_BM_INT2_Pos)
#define AUDCODEC_ADC2_CFG1_BM_INT2      AUDCODEC_ADC2_CFG1_BM_INT2_Msk
#define AUDCODEC_ADC2_CFG1_BM_INT1_Pos  (12U)
#define AUDCODEC_ADC2_CFG1_BM_INT1_Msk  (0x7UL << AUDCODEC_ADC2_CFG1_BM_INT1_Pos)
#define AUDCODEC_ADC2_CFG1_BM_INT1      AUDCODEC_ADC2_CFG1_BM_INT1_Msk
#define AUDCODEC_ADC2_CFG1_VST_SEL_Pos  (15U)
#define AUDCODEC_ADC2_CFG1_VST_SEL_Msk  (0x7UL << AUDCODEC_ADC2_CFG1_VST_SEL_Pos)
#define AUDCODEC_ADC2_CFG1_VST_SEL      AUDCODEC_ADC2_CFG1_VST_SEL_Msk
#define AUDCODEC_ADC2_CFG1_GC_Pos       (18U)
#define AUDCODEC_ADC2_CFG1_GC_Msk       (0x1FUL << AUDCODEC_ADC2_CFG1_GC_Pos)
#define AUDCODEC_ADC2_CFG1_GC           AUDCODEC_ADC2_CFG1_GC_Msk
#define AUDCODEC_ADC2_CFG1_FSP_Pos      (23U)
#define AUDCODEC_ADC2_CFG1_FSP_Msk      (0x3UL << AUDCODEC_ADC2_CFG1_FSP_Pos)
#define AUDCODEC_ADC2_CFG1_FSP          AUDCODEC_ADC2_CFG1_FSP_Msk

/*************** Bit definition for AUDCODEC_ADC2_CFG2 register ***************/
#define AUDCODEC_ADC2_CFG2_CLEAR_Pos    (0U)
#define AUDCODEC_ADC2_CFG2_CLEAR_Msk    (0x1UL << AUDCODEC_ADC2_CFG2_CLEAR_Pos)
#define AUDCODEC_ADC2_CFG2_CLEAR        AUDCODEC_ADC2_CFG2_CLEAR_Msk
#define AUDCODEC_ADC2_CFG2_CHOP_EN_Pos  (1U)
#define AUDCODEC_ADC2_CFG2_CHOP_EN_Msk  (0x1UL << AUDCODEC_ADC2_CFG2_CHOP_EN_Pos)
#define AUDCODEC_ADC2_CFG2_CHOP_EN      AUDCODEC_ADC2_CFG2_CHOP_EN_Msk
#define AUDCODEC_ADC2_CFG2_RSTB_Pos     (2U)
#define AUDCODEC_ADC2_CFG2_RSTB_Msk     (0x1UL << AUDCODEC_ADC2_CFG2_RSTB_Pos)
#define AUDCODEC_ADC2_CFG2_RSTB         AUDCODEC_ADC2_CFG2_RSTB_Msk
#define AUDCODEC_ADC2_CFG2_EN_Pos       (3U)
#define AUDCODEC_ADC2_CFG2_EN_Msk       (0x1UL << AUDCODEC_ADC2_CFG2_EN_Pos)
#define AUDCODEC_ADC2_CFG2_EN           AUDCODEC_ADC2_CFG2_EN_Msk

/*************** Bit definition for AUDCODEC_DAC1_CFG register ****************/
#define AUDCODEC_DAC1_CFG_EN_OS_DAC_Pos  (0U)
#define AUDCODEC_DAC1_CFG_EN_OS_DAC_Msk  (0x1UL << AUDCODEC_DAC1_CFG_EN_OS_DAC_Pos)
#define AUDCODEC_DAC1_CFG_EN_OS_DAC     AUDCODEC_DAC1_CFG_EN_OS_DAC_Msk
#define AUDCODEC_DAC1_CFG_OS_DAC_Pos    (1U)
#define AUDCODEC_DAC1_CFG_OS_DAC_Msk    (0x7FUL << AUDCODEC_DAC1_CFG_OS_DAC_Pos)
#define AUDCODEC_DAC1_CFG_OS_DAC        AUDCODEC_DAC1_CFG_OS_DAC_Msk
#define AUDCODEC_DAC1_CFG_GAIN_Pos      (8U)
#define AUDCODEC_DAC1_CFG_GAIN_Msk      (0xFUL << AUDCODEC_DAC1_CFG_GAIN_Pos)
#define AUDCODEC_DAC1_CFG_GAIN          AUDCODEC_DAC1_CFG_GAIN_Msk
#define AUDCODEC_DAC1_CFG_SR_Pos        (12U)
#define AUDCODEC_DAC1_CFG_SR_Msk        (0x1UL << AUDCODEC_DAC1_CFG_SR_Pos)
#define AUDCODEC_DAC1_CFG_SR            AUDCODEC_DAC1_CFG_SR_Msk
#define AUDCODEC_DAC1_CFG_POL_CLK_Pos   (13U)
#define AUDCODEC_DAC1_CFG_POL_CLK_Msk   (0x1UL << AUDCODEC_DAC1_CFG_POL_CLK_Pos)
#define AUDCODEC_DAC1_CFG_POL_CLK       AUDCODEC_DAC1_CFG_POL_CLK_Msk
#define AUDCODEC_DAC1_CFG_LP_MODE_Pos   (14U)
#define AUDCODEC_DAC1_CFG_LP_MODE_Msk   (0x1UL << AUDCODEC_DAC1_CFG_LP_MODE_Pos)
#define AUDCODEC_DAC1_CFG_LP_MODE       AUDCODEC_DAC1_CFG_LP_MODE_Msk
#define AUDCODEC_DAC1_CFG_SEL_VCM_Pos   (15U)
#define AUDCODEC_DAC1_CFG_SEL_VCM_Msk   (0x7UL << AUDCODEC_DAC1_CFG_SEL_VCM_Pos)
#define AUDCODEC_DAC1_CFG_SEL_VCM       AUDCODEC_DAC1_CFG_SEL_VCM_Msk
#define AUDCODEC_DAC1_CFG_BM_Pos        (18U)
#define AUDCODEC_DAC1_CFG_BM_Msk        (0x3UL << AUDCODEC_DAC1_CFG_BM_Pos)
#define AUDCODEC_DAC1_CFG_BM            AUDCODEC_DAC1_CFG_BM_Msk
#define AUDCODEC_DAC1_CFG_EN_CHOP_Pos   (20U)
#define AUDCODEC_DAC1_CFG_EN_CHOP_Msk   (0x1UL << AUDCODEC_DAC1_CFG_EN_CHOP_Pos)
#define AUDCODEC_DAC1_CFG_EN_CHOP       AUDCODEC_DAC1_CFG_EN_CHOP_Msk
#define AUDCODEC_DAC1_CFG_EN_AMP_Pos    (21U)
#define AUDCODEC_DAC1_CFG_EN_AMP_Msk    (0x1UL << AUDCODEC_DAC1_CFG_EN_AMP_Pos)
#define AUDCODEC_DAC1_CFG_EN_AMP        AUDCODEC_DAC1_CFG_EN_AMP_Msk
#define AUDCODEC_DAC1_CFG_EN_VCM_Pos    (22U)
#define AUDCODEC_DAC1_CFG_EN_VCM_Msk    (0x1UL << AUDCODEC_DAC1_CFG_EN_VCM_Pos)
#define AUDCODEC_DAC1_CFG_EN_VCM        AUDCODEC_DAC1_CFG_EN_VCM_Msk
#define AUDCODEC_DAC1_CFG_EN_DAC_Pos    (23U)
#define AUDCODEC_DAC1_CFG_EN_DAC_Msk    (0x1UL << AUDCODEC_DAC1_CFG_EN_DAC_Pos)
#define AUDCODEC_DAC1_CFG_EN_DAC        AUDCODEC_DAC1_CFG_EN_DAC_Msk
#define AUDCODEC_DAC1_CFG_SEL_VSTART_Pos  (24U)
#define AUDCODEC_DAC1_CFG_SEL_VSTART_Msk  (0x3UL << AUDCODEC_DAC1_CFG_SEL_VSTART_Pos)
#define AUDCODEC_DAC1_CFG_SEL_VSTART    AUDCODEC_DAC1_CFG_SEL_VSTART_Msk

/*************** Bit definition for AUDCODEC_DAC2_CFG register ****************/
#define AUDCODEC_DAC2_CFG_EN_OS_DAC_Pos  (0U)
#define AUDCODEC_DAC2_CFG_EN_OS_DAC_Msk  (0x1UL << AUDCODEC_DAC2_CFG_EN_OS_DAC_Pos)
#define AUDCODEC_DAC2_CFG_EN_OS_DAC     AUDCODEC_DAC2_CFG_EN_OS_DAC_Msk
#define AUDCODEC_DAC2_CFG_OS_DAC_Pos    (1U)
#define AUDCODEC_DAC2_CFG_OS_DAC_Msk    (0x7FUL << AUDCODEC_DAC2_CFG_OS_DAC_Pos)
#define AUDCODEC_DAC2_CFG_OS_DAC        AUDCODEC_DAC2_CFG_OS_DAC_Msk
#define AUDCODEC_DAC2_CFG_GAIN_Pos      (8U)
#define AUDCODEC_DAC2_CFG_GAIN_Msk      (0xFUL << AUDCODEC_DAC2_CFG_GAIN_Pos)
#define AUDCODEC_DAC2_CFG_GAIN          AUDCODEC_DAC2_CFG_GAIN_Msk
#define AUDCODEC_DAC2_CFG_SR_Pos        (12U)
#define AUDCODEC_DAC2_CFG_SR_Msk        (0x1UL << AUDCODEC_DAC2_CFG_SR_Pos)
#define AUDCODEC_DAC2_CFG_SR            AUDCODEC_DAC2_CFG_SR_Msk
#define AUDCODEC_DAC2_CFG_POL_CLK_Pos   (13U)
#define AUDCODEC_DAC2_CFG_POL_CLK_Msk   (0x1UL << AUDCODEC_DAC2_CFG_POL_CLK_Pos)
#define AUDCODEC_DAC2_CFG_POL_CLK       AUDCODEC_DAC2_CFG_POL_CLK_Msk
#define AUDCODEC_DAC2_CFG_LP_MODE_Pos   (14U)
#define AUDCODEC_DAC2_CFG_LP_MODE_Msk   (0x1UL << AUDCODEC_DAC2_CFG_LP_MODE_Pos)
#define AUDCODEC_DAC2_CFG_LP_MODE       AUDCODEC_DAC2_CFG_LP_MODE_Msk
#define AUDCODEC_DAC2_CFG_SEL_VCM_Pos   (15U)
#define AUDCODEC_DAC2_CFG_SEL_VCM_Msk   (0x7UL << AUDCODEC_DAC2_CFG_SEL_VCM_Pos)
#define AUDCODEC_DAC2_CFG_SEL_VCM       AUDCODEC_DAC2_CFG_SEL_VCM_Msk
#define AUDCODEC_DAC2_CFG_BM_Pos        (18U)
#define AUDCODEC_DAC2_CFG_BM_Msk        (0x3UL << AUDCODEC_DAC2_CFG_BM_Pos)
#define AUDCODEC_DAC2_CFG_BM            AUDCODEC_DAC2_CFG_BM_Msk
#define AUDCODEC_DAC2_CFG_EN_CHOP_Pos   (20U)
#define AUDCODEC_DAC2_CFG_EN_CHOP_Msk   (0x1UL << AUDCODEC_DAC2_CFG_EN_CHOP_Pos)
#define AUDCODEC_DAC2_CFG_EN_CHOP       AUDCODEC_DAC2_CFG_EN_CHOP_Msk
#define AUDCODEC_DAC2_CFG_EN_AMP_Pos    (21U)
#define AUDCODEC_DAC2_CFG_EN_AMP_Msk    (0x1UL << AUDCODEC_DAC2_CFG_EN_AMP_Pos)
#define AUDCODEC_DAC2_CFG_EN_AMP        AUDCODEC_DAC2_CFG_EN_AMP_Msk
#define AUDCODEC_DAC2_CFG_EN_VCM_Pos    (22U)
#define AUDCODEC_DAC2_CFG_EN_VCM_Msk    (0x1UL << AUDCODEC_DAC2_CFG_EN_VCM_Pos)
#define AUDCODEC_DAC2_CFG_EN_VCM        AUDCODEC_DAC2_CFG_EN_VCM_Msk
#define AUDCODEC_DAC2_CFG_EN_DAC_Pos    (23U)
#define AUDCODEC_DAC2_CFG_EN_DAC_Msk    (0x1UL << AUDCODEC_DAC2_CFG_EN_DAC_Pos)
#define AUDCODEC_DAC2_CFG_EN_DAC        AUDCODEC_DAC2_CFG_EN_DAC_Msk
#define AUDCODEC_DAC2_CFG_SEL_VSTART_Pos  (24U)
#define AUDCODEC_DAC2_CFG_SEL_VSTART_Msk  (0x3UL << AUDCODEC_DAC2_CFG_SEL_VSTART_Pos)
#define AUDCODEC_DAC2_CFG_SEL_VSTART    AUDCODEC_DAC2_CFG_SEL_VSTART_Msk

/************* Bit definition for AUDCODEC_RESERVED_IN0 register **************/
#define AUDCODEC_RESERVED_IN0_CTRL0_Pos  (0U)
#define AUDCODEC_RESERVED_IN0_CTRL0_Msk  (0xFFUL << AUDCODEC_RESERVED_IN0_CTRL0_Pos)
#define AUDCODEC_RESERVED_IN0_CTRL0     AUDCODEC_RESERVED_IN0_CTRL0_Msk
#define AUDCODEC_RESERVED_IN0_CTRL1_Pos  (8U)
#define AUDCODEC_RESERVED_IN0_CTRL1_Msk  (0xFFUL << AUDCODEC_RESERVED_IN0_CTRL1_Pos)
#define AUDCODEC_RESERVED_IN0_CTRL1     AUDCODEC_RESERVED_IN0_CTRL1_Msk
#define AUDCODEC_RESERVED_IN0_CTRL2_Pos  (16U)
#define AUDCODEC_RESERVED_IN0_CTRL2_Msk  (0xFFUL << AUDCODEC_RESERVED_IN0_CTRL2_Pos)
#define AUDCODEC_RESERVED_IN0_CTRL2     AUDCODEC_RESERVED_IN0_CTRL2_Msk
#define AUDCODEC_RESERVED_IN0_CTRL3_Pos  (24U)
#define AUDCODEC_RESERVED_IN0_CTRL3_Msk  (0xFFUL << AUDCODEC_RESERVED_IN0_CTRL3_Pos)
#define AUDCODEC_RESERVED_IN0_CTRL3     AUDCODEC_RESERVED_IN0_CTRL3_Msk

/************* Bit definition for AUDCODEC_RESERVED_IN1 register **************/
#define AUDCODEC_RESERVED_IN1_CTRL4_Pos  (0U)
#define AUDCODEC_RESERVED_IN1_CTRL4_Msk  (0xFFUL << AUDCODEC_RESERVED_IN1_CTRL4_Pos)
#define AUDCODEC_RESERVED_IN1_CTRL4     AUDCODEC_RESERVED_IN1_CTRL4_Msk
#define AUDCODEC_RESERVED_IN1_CTRL5_Pos  (8U)
#define AUDCODEC_RESERVED_IN1_CTRL5_Msk  (0xFFUL << AUDCODEC_RESERVED_IN1_CTRL5_Pos)
#define AUDCODEC_RESERVED_IN1_CTRL5     AUDCODEC_RESERVED_IN1_CTRL5_Msk

/************* Bit definition for AUDCODEC_RESERVED_OUT register **************/
#define AUDCODEC_RESERVED_OUT_STAT0_Pos  (0U)
#define AUDCODEC_RESERVED_OUT_STAT0_Msk  (0xFFUL << AUDCODEC_RESERVED_OUT_STAT0_Pos)
#define AUDCODEC_RESERVED_OUT_STAT0     AUDCODEC_RESERVED_OUT_STAT0_Msk
#define AUDCODEC_RESERVED_OUT_STAT1_Pos  (8U)
#define AUDCODEC_RESERVED_OUT_STAT1_Msk  (0xFFUL << AUDCODEC_RESERVED_OUT_STAT1_Pos)
#define AUDCODEC_RESERVED_OUT_STAT1     AUDCODEC_RESERVED_OUT_STAT1_Msk

#endif
