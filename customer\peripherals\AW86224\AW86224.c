/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AW86224.c
@Time    :   2025/03/12 09:14:55
@Brief   :
@Details :
*
************************************************************/

#include "AW86224.h"
#include "power_ctl.h"
#include "bsp_pinmux.h"
#include <stdlib.h>
#include "drv_io.h"
#ifndef IGS_BOOT
#include "qw_system_params.h"
#include "bsp_board_bus.h"
#endif

#define AW86224_I2C_NAME "i2c1"
#define TIMER_SELECTION "gptim1"

extern struct aw_haptic_func *g_func_haptic_nv;
//extern void haptic_nv_gpio_exti_callback(uint16_t GPIO_Pin);
extern void haptic_nv_gpio_exti_callback(void* arg);
extern void haptic_nv_tim_periodelapsedcallback(GPT_HandleTypeDef *htim);

rt_device_t timer_dev = RT_NULL;
struct rt_i2c_bus_device *aw86224_bus= RT_NULL;
static uint8_t g_aw86224_gain = AW86224_MAX_GAIN;  //0x80 MAX

static bool g_aw86224_init = false;
static bool g_aw86224_is_first_init = true;

static uint8_t aw86224_write_reg(uint8_t address, uint8_t tx_cmd)
{
    struct rt_i2c_msg msgs[1];
    uint8_t value[2];
    uint32_t res;

    if (aw86224_bus)
    {
        value[0] = address;
        value[1] = tx_cmd;

        msgs[0].addr = AW86224_ADDRESS;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = value;
        msgs[0].len = 2;

        if (rt_i2c_transfer(aw86224_bus, msgs, 1) == 1)
        {
            return RT_EOK;
        }
        else
        {
            AW86224_E("aw86224_bus write err!");
        }
    }
    else
    {
        AW86224_E("aw86224_bus NULL!");
    }
    return RT_ERROR;
}

static uint8_t aw86224_read_reg(uint8_t address, uint8_t *rx_buffer, const uint8_t len)
{
    struct rt_i2c_msg msgs[2];
    uint8_t value[1];
    uint32_t res;

    if (aw86224_bus)
    {
        value[0] = address;

        msgs[0].addr = AW86224_ADDRESS;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = value;
        msgs[0].len = 1;

        msgs[1].addr = AW86224_ADDRESS;
        msgs[1].flags = RT_I2C_RD;
        msgs[1].buf = rx_buffer;
        msgs[1].len = len;

        if (rt_i2c_transfer(aw86224_bus, msgs, 2) == 2)
        {
            return RT_EOK;
        }
        else
        {
            AW86224_E("aw86224_bus read err!");
        }
    }
    else
    {
        AW86224_E("aw86224_bus NULL!");
    }
    return RT_ERROR;
}



void haptic_nv_gpio_exti_callback(void* arg)
{
    g_haptic_nv->irq_handle = AW_IRQ_ON;
    uint8_t per_press = (g_haptic_nv->rtp_cnt*100) / g_haptic_nv->rtp_len;
    AW86224_I("###moter rtp play press = %d%%...\n",per_press);
    g_func_haptic_nv->irq_handle();
}
/**
 * @brief  aw86224的初始化函数
 *
 * @param void
 * @return int32_t
 */
int32_t AW86224_Init(void)
{
    //AW86224_I("aw86224 init tick %d\n",rt_tick_get());

    int ret = RT_ERROR;
    if(g_aw86224_init) return ret;

    //if(!g_aw86224_is_first_init)  // 第一次不需要开启，默认在BOOT里面开启
    {
        //pmic_device_control(PMIC_OUT_VBAT_HVSW150_2, 1, 1); //开启电源
        power_control_3v3_motor(true);//开启马达电源
    }

    qw_gpio_set(AW86224_ENABLE_IO, GPIO_MODE_OUTPUT, PIN_PULLUP);
    //rt_pin_write(AW86224_ENABLE_IO, 1);

    #ifdef AW_IRQ_CONFIG
        qw_gpio_set(AW86224_EXT_INTERRUPT_PIN, GPIO_MODE_INPUT, PIN_PULLUP);
        //Set pin input mode
        rt_pin_mode(AW86224_EXT_INTERRUPT_PIN, PIN_MODE_INPUT_PULLUP);
        //Enable rasing edge interrupt mode
        rt_pin_attach_irq(AW86224_EXT_INTERRUPT_PIN, PIN_IRQ_MODE_FALLING, haptic_nv_gpio_exti_callback, RT_NULL);

        haptic_nv_enable_irq();
    #else
        HAL_PIN_Set_Analog(PAD_PA00, 1); // 管脚低功耗时的配置
    #endif

        /* get i2c bus device */
        aw86224_bus = rt_i2c_bus_device_find(AW86224_I2C_NAME);
        if (aw86224_bus)
        {
            //AW86224_D("Find i2c bus device %s\n", AW86224_I2C_NAME);
#ifndef USE_BSP_BORAD_BUS_INIT
            qw_special_pin_set(I2C1_SCL_PIN, I2C1_SCL, PIN_NOPULL);  // 已有外部上拉
            qw_special_pin_set(I2C1_SDA_PIN, I2C1_SDA, PIN_NOPULL);
            rt_device_open(&(aw86224_bus->parent), RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_RX | RT_DEVICE_FLAG_INT_TX);
            {
                struct rt_i2c_configuration configuration =
                {
                    .mode = 0,
                    .addr = 0,
                    .timeout = 500,
                    .max_hz  = 100000,
                };

                rt_i2c_configure(aw86224_bus, &configuration);
            }
#else
            bsp_i2c1_bus_init();
#endif
        }
        else
        {
            AW86224_E("Can not found i2c bus %s, init fail\n", AW86224_I2C_NAME);
            return -1;
        }
    #if 0
        // Find and open gptimer device
        timer_dev = rt_device_find(TIMER_SELECTION);

        rt_err_t err = rt_device_open(timer_dev, RT_DEVICE_FLAG_RDWR);
        if (err != RT_EOK)
        {
            AW86224_E("Failed to open device GPTim2\n");
            return -1;
        }

        // Configure Timer
        int freq=1000000;
        rt_device_control(timer_dev, HWTIMER_CTRL_FREQ_SET, (void *)&freq);
        int mode=HWTIMER_MODE_ONESHOT;
        rt_device_control(timer_dev, HWTIMER_CTRL_MODE_SET, (void *)&mode);

        // Prepare for timeout
        rt_device_set_rx_indicate(timer_dev, haptic_nv_tim_periodelapsedcallback);

        // Start timer
        rt_hwtimerval_t t={3,500}; // 3 seconds and 500 microseconds
        rt_size_t ret = rt_device_write(timer_dev, 0, &t, sizeof(t));
        if (ret != sizeof(t))
        {
            AW86224_E("Failed to start timer\n");
            return -1;
        }
    #endif

    uint8_t try_time = 3; // 重试次数
    while(try_time--)
    {
        rt_thread_delay(10);
        rt_pin_write(AW86224_ENABLE_IO, 1);
        if(g_aw86224_is_first_init)  // 首次初始化，需要校正
            ret = haptic_nv_boot_init();
        else                         // 进入低功耗后恢复初始化，直接写入之前存的校正值
        {
            ret = haptic_nv_reinit();
        }

        if (ret != AW_SUCCESS)
        {
            ret = RT_ERROR;
        }
        else
        {
            ret = RT_EOK;
            break;
        }
    }

    if(ret == RT_EOK)
    {
        g_func_haptic_nv->set_gain(10);
        g_aw86224_is_first_init = false;
        g_aw86224_init = true;
        //AW86224_I("aw86224 init out tick %d\n",rt_tick_get());
#ifndef IGS_BOOT
        //给工模传递init信息
        fat_dev_info_t fat_dev_info={0};
        fat_dev_info.dev_type = FAT_DEV_MOTOR;
        fat_dev_info.dev_id = 0;
        fat_dev_info.dev_state = ret==RT_EOK?1:0;
        fat_set_dev_init_info(fat_dev_info);
#endif
    }else{
        AW86224_E("aw86224 init err!!!\r\n");
    }
    return ret;
}


int32_t AW86224_Remind(void)
{
    if(g_aw86224_init) haptic_nv_remind();
    else AW86224_E("aw86224 init err!!!\r\n");
    return 0;
}

/**
 * @brief  aw86224的反初始化函数
 *  关闭设备
 * @param void
 * @return int32_t
 */
int32_t AW86224_DeInit(void)
{
    //AW86224_I("aw86224 deinit\n");
    if(!g_aw86224_init) return 0;
    g_aw86224_init = false;
    if(aw86224_bus)
    {
#ifndef USE_BSP_BORAD_BUS_INIT
        rt_err_t ret = 0;
        ret = rt_device_close((rt_device_t)&(aw86224_bus->parent));
        // rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_OUTPUT_OD);//设置输入
        // rt_pin_mode(I2C1_SCL_PIN, PIN_MODE_OUTPUT_OD);//设置输入
        // qw_gpio_set(I2C1_SCL_PIN, GPIO_MODE_OUTPUT_OD, PIN_NOPULL);
        // qw_gpio_set(I2C1_SDA_PIN, GPIO_MODE_OUTPUT_OD, PIN_NOPULL);
#else
        bsp_i2c1_bus_uninit();
#endif
        aw86224_bus = RT_NULL;
    }

    rt_pin_write(AW86224_ENABLE_IO,0); // 关闭马达
    //HAL_PIN_Set_Analog(PAD_PA08, 1); // 管脚低功耗时的配置

#ifdef AW_IRQ_CONFIG
    haptic_nv_disable_irq();
#endif
    HAL_PIN_Set_Analog(PAD_PA00, 1); // 管脚低功耗时的配置

    //pmic_device_control(PMIC_OUT_VBAT_HVSW150_2, 0, 1);//关闭电源
    power_control_3v3_motor(false);//关闭马达电源

    return 0;
}



/**
 * @brief  aw86224的id检查函数
 *
 * @param void
 * @return uint8_t
 */
uint8_t AW86224_CheckID(void)
{
    if(!g_aw86224_init) return false;
    uint8_t whoami = 0;
    aw86224_read_reg(AW862XX_REG_SYSST,&whoami,1);
    AW86224_I("whoami = %d\n",whoami);
    if(AW86224_ID == whoami)return true;
    return false;
}

#ifndef IGS_BOOT
/**
 * @brief  aw86224的控制函数
 *  aw862xx_ram_init: 初始化RAM。
    aw862xx_trig_init: 初始化触发器。
    aw862xx_play_mode: 设置播放模式。
    aw862xx_play_stop: 停止播放。
    aw862xx_irq_clear: 清除中断。
    aw862xx_cont_config: 配置连续播放模式。
    aw862xx_offset_cali: 偏移校准。
    aw862xx_haptic_start: 启动haptic。
    aw862xx_check_qualify: 检查设备是否符合要求。
    aw862xx_judge_rtp_going: 判断RTP是否正在进行中。
    aw862xx_protect_config: 配置保护模式。
    aw862xx_misc_para_init: 初始化其他参数。
    aw862xx_interrupt_setup: 设置中断。
    aw862xx_rtp_get_fifo_afs: 获取RTP FIFO ALMOST FULL状态。
    aw862xx_vbat_mode_config: 配置VBAT模式。
    aw862xx_calculate_cali_data: 计算校准数据。
    aw862xx_set_gain: 设置增益。
    aw862xx_set_wav_seq: 设置波形序列。
    aw862xx_set_wav_loop: 设置波形循环。
    aw862xx_set_ram_data: 设置RAM数据。
    aw862xx_get_ram_data: 获取RAM数据。
    aw862xx_set_fifo_addr: 设置FIFO地址。
    aw862xx_get_fifo_addr: 获取FIFO地址。
    aw862xx_set_rtp_aei: 设置RTP AEI。
    aw862xx_set_rtp_data: 设置RTP数据。
    aw862xx_set_ram_addr: 设置RAM地址。
    aw862xx_set_trim_lra: 设置TRIM LRA。
    aw862xx_set_base_addr: 设置基地址。
    aw862xx_set_repeat_seq: 设置重复序列。
    aw862xx_get_f0: 获取f0频率。
    aw862xx_get_reg: 获取寄存器。
    aw862xx_get_vbat: 获取VBAT电压。
    aw862xx_get_irq_state: 获取中断状态。
    aw862xx_get_glb_state: 获取全局状态。
    aw862xx_get_lra_resistance: 获取LRA电阻。
 * @param pdata
 * @return int32_t
 */

int32_t vibration_init(void)
{
    // 开机校准参数设置
    uint16_t vib_f0_calib_pre = 0;
    uint8_t vib_f0_calib_percet = 0;
    if (get_f0_calib_base(&vib_f0_calib_pre) && get_f0_calib_percent(&vib_f0_calib_percet))
    {
        if(vibration_f0_calib_set(vib_f0_calib_pre, vib_f0_calib_percet) != RT_EOK)
        {
            // 若参数不合法，设置成厂商数据
            set_f0_calib_base(aw8622x_dts.f0_pre);              // F0基准值
            set_f0_calib_percent(aw8622x_dts.f0_cali_percent);  // F0基准值最大偏差
        }
    }
    return AW86224_Init();
}

int32_t vibration_deinit(void)
{
    if(!g_aw86224_init) return RT_ERROR;
    return AW86224_DeInit();
}

int32_t set_vibration_gain(uint8_t gain)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_aw86224_gain = gain;
    g_func_haptic_nv->set_gain(gain); //GAIN=gain/128 默认0X80
    return 0;
}

uint8_t get_vibration_gain(void)
{
    return g_aw86224_gain;
}

int32_t set_vibration_start(uint8_t* rtp_data,uint32_t rtp_len,uint8_t gain)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_func_haptic_nv->rtp_vib_work(gain,rtp_data,rtp_len);
    return 0;
}

int32_t set_vibration_short(uint8_t index, uint8_t gain,uint8_t loop)
{
    if(!g_aw86224_init) return RT_ERROR;
    return g_func_haptic_nv->short_vib_work(index, gain,loop);
}

int32_t set_vibration_stop(void)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_func_haptic_nv->play_stop();
    return 0;
}
//连续播放
int32_t set_vibration_cont(uint8_t index, uint8_t gain)
{
    if(!g_aw86224_init) return RT_ERROR;
    return g_func_haptic_nv->cont_vib_work(index,gain);
}
//F0检测
int32_t vibration_f0_check(uint32_t *freq)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_func_haptic_nv->f0_show();
    *freq = g_haptic_nv->f0;
    return 0;
}

int32_t vibration_f0_calib_set(uint16_t f0_pre,uint8_t f0_cali_percent)
{
    // 入参合规检测,否则使用厂商数据
    //rt_kprintf("vibration_f0_calib_set f0_pre = %d,f0_cali_percent = %d\n",f0_pre,f0_cali_percent);
    int ret = 0;
    if((f0_pre > 1000)&&(f0_pre < 4000)&&(f0_cali_percent < 20))
    {
        aw8622x_dts.f0_pre = f0_pre;
        aw8622x_dts.f0_cali_percent = f0_cali_percent;
        ret = 0;
    }else{

        aw8622x_dts.f0_pre = 1700;
        aw8622x_dts.f0_cali_percent = 7;
        ret = -1;
    }
    return ret;
}

//F0校正
int32_t vibration_f0_calib(uint32_t *freq)
{
    if(!g_aw86224_init) return RT_ERROR;
    int ret = g_func_haptic_nv->f0_cali();
    *freq = g_haptic_nv->f0;
    return ret;
}

int32_t vibration_osc_check(uint32_t *ocs)
{
    if(!g_aw86224_init) return RT_ERROR;
    int ret = g_func_haptic_nv->offset_cali();
    *ocs = g_haptic_nv->ocs;
    return ret;
}

int32_t vibration_res_check(uint32_t *res)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_func_haptic_nv->get_lra_resistance();
    *res = g_haptic_nv->lra;
    return 0;
}

int32_t vibration_vbat_check(uint32_t *vbat)
{
    if(!g_aw86224_init) return RT_ERROR;
    g_func_haptic_nv->get_vbat();
    *vbat = g_haptic_nv->vbat;
    return 0;
}




#define AW86224_FUNC_TEST
#ifdef AW86224_FUNC_TEST
int cmd_aw86224(int argc, char *argv[])
{

    if (strcmp(argv[1], "init") == 0)
    {
        if (vibration_init() == 0)
        {
            AW86224_I("AW86224 Init success\n");
        }
        else
        {
            AW86224_I("AW86224 Init failed\n");
        }
    }
    else if (strcmp(argv[1], "deinit") == 0)
    {
        if (vibration_deinit() == 0)
        {
            AW86224_I("AW86224 DeInit success\n");
        }
        else
        {
            AW86224_I("AW86224 DeInit failed\n");
        }
    }
    else if (strcmp(argv[1], "checkid") == 0)
    {
        if (AW86224_CheckID() == 0)
        {
            AW86224_I("AW86224 CheckID failed\n");
        }
        else
        {
            AW86224_I("AW86224 CheckID success\n");
        }
    }
    else if (strcmp(argv[1], "gain") == 0)
    {
        //读取第三个参数设置gain
        g_func_haptic_nv->set_gain(atoi(argv[2]));
    }
    else if (strcmp(argv[1], "short") == 0)
    {
        int index = atoi(argv[2]);
        if(index<21){
            g_func_haptic_nv->short_vib_work(index, g_aw86224_gain,1);
        }
    }
    // else if (strcmp(argv[1], "long") == 0)
    // {
    //     g_func_haptic_nv->long_vib_work(1, g_aw86224_gain,500);
    // }
    //连续模式测试
    else if (strcmp(argv[1], "cont") == 0)
    {
        g_func_haptic_nv->cont_vib_work(1,g_aw86224_gain);
    }
    else if (strcmp(argv[1], "rtp") == 0)
    {
        g_func_haptic_nv->rtp_vib_work(g_aw86224_gain,(uint8_t*)haptic_nv_rtp_data,haptic_nv_rtp_len);
    }
    else if (strcmp(argv[1], "ram") == 0)
    {
        g_func_haptic_nv->get_ram_num();
        g_func_haptic_nv->ram_show();
    }
    //停止模式测试
    else if ((strcmp(argv[1], "stop") == 0)||(strcmp(argv[1], "sleep") == 0))
    {
        g_func_haptic_nv->play_stop();
    }
    else if (strcmp(argv[1], "f0_real") == 0)
    {
        g_func_haptic_nv->f0_show();
    }
    else if (strcmp(argv[1], "f0_now") == 0)
    {
        g_func_haptic_nv->cali_show();
    }
    else if (strcmp(argv[1], "f0_calib") == 0)
    {
        g_func_haptic_nv->f0_cali();
    }
    else if (strcmp(argv[1], "osc_check") == 0)
    {
        g_func_haptic_nv->offset_cali();
    }
    else if (strcmp(argv[1], "impedance") == 0)
    {
        g_func_haptic_nv->get_lra_resistance();
    }
    else if (strcmp(argv[1], "vbat_check") == 0)
    {
        g_func_haptic_nv->get_vbat();
    }
    else
    {
        AW86224_I("Usage: aw86224 [init|checkid] argc = %d\n",argc);
    }

    return 0;
}

FINSH_FUNCTION_EXPORT(cmd_aw86224, aw86224 function test);
MSH_CMD_EXPORT(cmd_aw86224, aw86224 function test);
#endif

/**
 * 命令行进行测试应该发送这些
 * cmd_aw86224 init
 * cmd_aw86224 deinit
 * cmd_aw86224 checkid
 * cmd_aw86224 Stest
 * cmd_aw86224 sleep
 */
#endif
