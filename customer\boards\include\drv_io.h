/**
  ******************************************************************************
  * @file   lcd_io.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifndef __DRV_IO_H__
#define __DRV_IO_H__

#include "stdint.h"
#include "stdbool.h"

/**
 * @brief Init IO(pinmux) setting
 */
void BSP_IO_Init(void);

/**
 * @brief Get Flash divider
 */
uint16_t BSP_GetFlash1DIV(void);
uint16_t BSP_GetFlash2DIV(void);
uint16_t BSP_GetFlash3DIV(void);
uint16_t BSP_GetFlash4DIV(void);
uint16_t BSP_GetFlash5DIV(void);

/**
 * @brief Set Flash divider
 */
void BSP_SetFlash1DIV(uint16_t div);
void BSP_SetFlash2DIV(uint16_t div);
void BSP_SetFlash3DIV(uint16_t div);
void BSP_SetFlash4DIV(uint16_t div);
void BSP_SetFlash5DIV(uint16_t div);

/**
 * @brief Power up/down board
 */
void BSP_IO_Power_Down(int coreid, bool is_deep_sleep);
void BSP_Power_Up(bool is_deep_sleep);


void BSP_PowerDownCustom(int coreid, bool is_deep_sleep);

void BSP_PowerUpCustom(bool is_deep_sleep);


/**
 * @brief LCD power up/down/reset
 */
void BSP_LCD_Reset(uint8_t high1_low0);
void BSP_LCD_PowerUp(void);
void BSP_LCD_PowerDown(void);

/**
 * @brief Touch power up/down/reset
 */
void BSP_TP_Reset(uint8_t high1_low0);
void BSP_TP_PowerUp(void);
void BSP_TP_PowerDown(bool is_tp_only);

/**
 * @brief Flash
 */

void *BSP_Flash_get_handle(uint32_t addr);
int BSP_Flash_read_id(uint32_t addr);

int BSP_Nor_erase(void *hflash, uint32_t addr, uint32_t size);
int BSP_Nor_write(void *handle, uint32_t addr, const uint8_t *buf, uint32_t size);


void BSP_Flash_var_init(void);
int BSP_Flash_hw1_init(void);
int BSP_Flash_hw2_init(void);
int BSP_Flash_hw3_init(void);
int BSP_Flash_hw4_init(void);
int BSP_Flash_hw5_init(void);


int BSP_Flash_Init(void);

void BSP_GPIO_Set(int pin, int val, int is_porta);

/**
  * @}
  */

#endif





/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
