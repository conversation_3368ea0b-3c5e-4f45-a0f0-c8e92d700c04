/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hdl_brom_base.h"
#include "airoha_ag333x.h"
#include "rtthread.h"

extern uint8_t *g_hdl_data_buf;

bool g_brom_task_stop = FALSE;
const unsigned char HDL_START_CMD[4]    = {0xA0, 0x0A, 0x50, 0x05};
const unsigned char HDL_BROM_RSP_CMD[4] = {0x5F, 0xF5, 0xAF, 0xFA};

static void brom_write8_echo(uint8_t data)
{
    // HDL_LOGI("brom_write8_echo 0x%02X\n", data);
    HDL_COM_PutByte(data);
    uint8_t rx_data = HDL_COM_GetByte();
    // HDL_LOGI("brom_write8_echo 0x%02X - 0x%02X\n", data, rx_data);
}

static void brom_write16_echo(uint16_t data)
{
    // HDL_LOGI("brom_write16_echo 0x%04X\n", data);
    HDL_COM_PutData16(data);
    uint16_t rx_data = HDL_COM_GetData16();
    // HDL_LOGI("brom_write16_echo 0x%04X - 0x%04X\n", data, rx_data);
}

static void brom_write32_echo(uint32_t data)
{
    // HDL_LOGI("brom_write32_echo 0x%08X\n", data);
    HDL_COM_PutData32(data);
    uint32_t rx_data = HDL_COM_GetData32();
    // HDL_LOGI("brom_write32_echo 0x%08X - 0x%08X\n", data, rx_data);
}

static rt_timer_t tx_timer_id_s = NULL;

static void tx_timer_handler(void *p)
{
    if(g_brom_task_stop){
        rt_timer_stop(tx_timer_id_s);
    }else{
        HDL_COM_PutByte(HDL_START_CMD[0]);
    }
}

static void tx_timer_init(void)
{
    if(tx_timer_id_s == NULL)
    {
        tx_timer_id_s = rt_timer_create("gpsfw", tx_timer_handler,
        RT_NULL, 30, RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
        rt_kprintf("tx_timer_init\n");
    }

    if(tx_timer_id_s)
    {
	    rt_timer_start(tx_timer_id_s);
        rt_kprintf("tx_timer_start\n");
    }
    
    HDL_COM_PutByte(HDL_START_CMD[0]);
}

bool hdl_brom_start()
{
#ifdef  GPS_USING_AG333X
    char rx_data = 0;
    uint32_t handshake_timeout = 0;
    // rt_kprintf("hdl_brom_start\n");
    ag333x_gps_poweroff_with_chipen();
brom_start_retry:
    // hdl_delay(4000);
    g_brom_task_stop = FALSE;
    airoha_ag333x_poweron_with_chip_reset();
    tx_timer_init();
#if defined (HDL_VIA_UART)
    while (1) {
        rx_data = HDL_COM_GetByte();
        // rt_kprintf("hdl_brom_start 0xA0<->0x%02X\n", rx_data);
        if (rx_data == HDL_BROM_RSP_CMD[0]) {
            g_brom_task_stop = TRUE;
            handshake_timeout = 0;
            rt_timer_stop(tx_timer_id_s);
            // rt_kprintf("hdl_brom_start suc 0xA0<->0x5F\n");
            break;
        }

        if(handshake_timeout >= 1000){
            rt_kprintf("reset\n");
            handshake_timeout = 0;
            ag333x_gps_poweroff_with_chipen();
            hdl_delay(1000);
            goto brom_start_retry;
        }
        hdl_delay(3);
        handshake_timeout ++;
    }
#endif

    #if defined (HDL_VIA_I2C)
        hdl_i2c_enable_check_data_valid();
    #endif
    HDL_COM_PutByte(HDL_START_CMD[1]);
    rx_data = HDL_COM_GetByte();
    if (rx_data == HDL_BROM_RSP_CMD[1]) {
        HDL_LOGI("hdl_brom_start suc 0x0A<->0xF5\n");
    } else {
        HDL_LOGE("hdl_brom_start fail 0x0A ->(error) 0x%02X, retry\n", rx_data);
        rt_timer_stop(tx_timer_id_s);
        ag333x_gps_poweroff_with_chipen();
        hdl_delay(1000);
        goto brom_start_retry;
    }
    HDL_COM_PutByte(HDL_START_CMD[2]);
    rx_data = HDL_COM_GetByte();
    if (rx_data == HDL_BROM_RSP_CMD[2]) {
        HDL_LOGI("hdl_brom_start suc 0x50<->0xAF\n");
    } else {
        HDL_LOGE("hdl_brom_start fail 0x50 ->(error) 0x%02X, retry\n", rx_data);
        rt_timer_stop(tx_timer_id_s);
        ag333x_gps_poweroff_with_chipen();
        hdl_delay(1000);
        goto brom_start_retry;
    }
    HDL_COM_PutByte(HDL_START_CMD[3]);
    rx_data = HDL_COM_GetByte();
    if (rx_data == HDL_BROM_RSP_CMD[3]) {
        HDL_LOGI("hdl_brom_start suc 0x05<->0xFA\n");
    } else {
        HDL_LOGE("hdl_brom_start fail 0x05 ->(error) 0x%02X, retry\n", rx_data);
        rt_timer_stop(tx_timer_id_s);
        ag333x_gps_poweroff_with_chipen();
        hdl_delay(1000);
        goto brom_start_retry;
    }
    
    HDL_LOGI("hdl_brom_start pass\n");

    rt_timer_delete(tx_timer_id_s);
    tx_timer_id_s = NULL;
    return TRUE;
#else
    return FALSE;
#endif
}

bool hdl_brom_disable_wdt()
{
    return hdl_brom_write16(HDL_WDT_REG, HDL_WDT_VAL);
}

bool hdl_brom_read16(uint32_t addr, uint16_t *data)
{
    bool success = FALSE;
    brom_write8_echo(BROM_CMD_READ16);
    brom_write32_echo(addr);
    brom_write32_echo(1);

    uint16_t status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_read16\n");
    *data = HDL_COM_GetData16();
    status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_read16\n");
    success = TRUE;

exit:
    return success;
}

bool hdl_brom_read32(uint32_t addr, uint32_t *data)
{
    bool success = FALSE;
    brom_write8_echo(BROM_CMD_READ32);
    brom_write32_echo(addr);
    brom_write32_echo(1);

    uint16_t status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_read32");
    *data = HDL_COM_GetData16();
    status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_read32");
    success = TRUE;

exit:
    return success;
}

bool hdl_brom_write16(uint32_t addr, uint16_t data)
{
    bool success = FALSE;
    brom_write8_echo(BROM_CMD_WRITE16);
    brom_write32_echo(addr);
    brom_write32_echo(1);

    uint16_t status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_write16");
    brom_write16_echo(data);
    status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_write16");
    success = TRUE;

exit:
    return success;
}

bool hdl_brom_write32(uint32_t addr, uint32_t data)
{
    bool success = FALSE;
    brom_write8_echo(BROM_CMD_WRITE32);
    brom_write32_echo(addr);
    brom_write32_echo(1);

    uint16_t status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_write32");
    brom_write32_echo(data);
    status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_write32");
    success = TRUE;

exit:
    return success;
}

bool hdl_brom_send_da(const hdl_connect_arg_t *connect_arg,
                      uint32_t da_flash_pos, uint32_t da_start_addr, uint32_t da_len)
{
    bool success = FALSE;
    bool ret = hdl_flash_init();
    HDL_Require_Noerr_Action(ret, exit, "hdl_flash_init");
    HDL_LOGI("hdl_brom_send_da 0x%08X 0x%08X %d", da_flash_pos, da_start_addr, da_len);

    // DA Init Callback
    if (connect_arg != NULL && connect_arg->conn_da_init_cb != NULL) {
        connect_arg->conn_da_init_cb(connect_arg->conn_da_init_cb_arg);
    }

    brom_write8_echo(BROM_CMD_SEND_DA);
    brom_write32_echo(da_start_addr);
    brom_write32_echo(da_len);
    brom_write32_echo(0);   // data_sig_len = 0
    uint16_t status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_send_da");

    const uint32_t packet_len = 1024;
    uint32_t total_sent_len = 0;
    uint32_t local_check_sum = 0;
    while (total_sent_len < da_len) {
        unsigned int to_send_len = min(packet_len, da_len - total_sent_len);
        ret = hdl_flash_read(HDL_DA_IMAGE_NAME,da_flash_pos + total_sent_len, g_hdl_data_buf, to_send_len);
        if (ret) {
            uint32_t sent_len = HDL_COM_PutByte_Buffer(g_hdl_data_buf, to_send_len);
            total_sent_len += sent_len;
            HDL_LOGI("hdl_brom_send_da, %d sent %d", to_send_len, sent_len);

            // DA Send Callback
            if (connect_arg != NULL && connect_arg->conn_da_send_cb != NULL) {
                connect_arg->conn_da_send_cb(connect_arg->conn_da_send_cb_arg, total_sent_len, da_len);
            }

            if (sent_len > 0) {
                local_check_sum ^= hdl_compute_checksum(g_hdl_data_buf, sent_len);
            }
        } else {
            HDL_LOGI("hdl_brom_send_da, flash read fail %d", ret);
        }
    }
    uint16_t brom_check_sum = HDL_COM_GetData16();
    HDL_LOGI("hdl_brom_send_da, local_check_sum=%d brom_check_sum=%d", local_check_sum, brom_check_sum);
    HDL_Require_Noerr_Action(local_check_sum == brom_check_sum, exit, "hdl_brom_send_da, checksum");
    status = HDL_COM_GetData16();
    HDL_Require_Noerr_Action(status < BROM_ERROR, exit, "hdl_brom_send_da");
    success = TRUE;

exit:
    hdl_flash_deinit();
    return success;
}

bool hdl_brom_jump_da(uint32_t addr)
{
    HDL_LOGI("hdl_brom_jump_da 0x%08X", addr);
    brom_write8_echo(BROM_CMD_JUMP_DA);
    brom_write32_echo(addr);
    uint16_t status = HDL_COM_GetData16();
    return (status < BROM_ERROR);
}
