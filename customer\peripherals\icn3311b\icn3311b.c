/**
  ******************************************************************************
  * @file   icn3311.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for ICN3311 LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "icn3311.h"
#include "log.h"
#include "bf0_hal.h"
/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ICN3311
  * @brief This file provides a set of functions needed to drive the
  *        ICN3311 LCD.
  * @{
  */

/** @defgroup ICN3311_Private_TypesDefinitions
  * @{
  */

#define ROW_OFFSET  (0x00)
//#define COL_OFFSET  (0x00)
#define COL_OFFSET  (0x0E)  //xulin modify


typedef struct
{
    rt_uint16_t width;
    rt_uint16_t height;
    rt_uint16_t id;
    rt_uint8_t  dir;            //Horizontal or vertical screen control: 0, vertical; 1, horizontal
    rt_uint16_t wramcmd;
    rt_uint16_t setxcmd;
    rt_uint16_t setycmd;
} lcd_info_t;


/**
  * @}
  */

/** @defgroup ICN3311_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ICN3311_Private_Macros
  * @{
  */
#define RGB_ARRAY_LEN (320)

//Definition of scan direction
#define L2R_U2D  0
#define L2R_D2U  1
#define R2L_U2D  2
#define R2L_D2U  3
#define U2D_L2R  4
#define U2D_R2L  5
#define D2U_L2R  6
#define D2U_R2L  7
#define DFT_SCAN_DIR  L2R_U2D




#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   HAL_DBG_printf(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

/*icn3311 start colume & row must can be divided by 2, and roi width&height too.*/
#define ICN3311_ALIGN2(x) //((x) = (x) & (~1))
#define ICN3311_ALIGN1(x) //((x) = (0 == ((x) & 1)) ? (x - 1) : x)

static lcd_info_t lcddev;

/**
  * @}
  */

/** @defgroup ICN3311_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ICN3311_drv =
{
    ICN3311_Init,
    ICN3311_ReadID,
    ICN3311_DisplayOn,
    ICN3311_DisplayOff,

    ICN3311_SetRegion,
    ICN3311_WritePixel,
    ICN3311_WriteMultiplePixels,

    ICN3311_ReadPixel,

    ICN3311_SetColorMode,
    ICN3311_SetBrightness
};


static uint16_t ArrayRGB[RGB_ARRAY_LEN] = {0};


#ifdef BSP_LCDC_USING_DSI

static const LCDC_InitTypeDef lcdc_int_cfg_dsi =
{
    .lcd_itf = LCDC_INTF_DSI,
    .freq = DSI_FREQ_480MHZ, //ICN3311 RGB565 only support 320Mbps,  RGB888 support 500Mbps
    .color_mode = LCDC_PIXEL_FORMAT_RGB888,  //DBI output color format,   should match with .cfg.dsi.CmdCfg.ColorCoding

    .cfg = {

        .dsi = {

            .Init = {
                .AutomaticClockLaneControl = DSI_AUTO_CLK_LANE_CTRL_ENABLE,
                .NumberOfLanes = DSI_ONE_DATA_LANE,
                .TXEscapeCkdiv = 0x4,
            },

            .CmdCfg = {
                .VirtualChannelID      = 0,
                .CommandSize           = 0xFFFF,
#if 0//def LCD_ICN3311_VSYNC_ENABLE
                .TEAcknowledgeRequest  = DSI_TE_ACKNOWLEDGE_ENABLE,     //Open TE
#else
                .TEAcknowledgeRequest  = DSI_TE_ACKNOWLEDGE_DISABLE,     //Close TE
#endif /* LCD_ICN3311_VSYNC_ENABLE */
                .ColorCoding           = DSI_RGB888,                    //DSI input & output color format
            },

            .PhyTimings = {
                .ClockLaneHS2LPTime = 35,
                .ClockLaneLP2HSTime = 35,
                .DataLaneHS2LPTime = 35,
                .DataLaneLP2HSTime = 35,
                .DataLaneMaxReadTime = 0,
                .StopWaitTime = 0,
            },

            .HostTimeouts = {
                .TimeoutCkdiv = 1,
                .HighSpeedTransmissionTimeout = 0,
                .LowPowerReceptionTimeout = 0,
                .HighSpeedReadTimeout = 0,
                .LowPowerReadTimeout = 0,
                .HighSpeedWriteTimeout = 0,
                //.HighSpeedWritePrespMode = DSI_HS_PM_DISABLE,
                .LowPowerWriteTimeout = 0,
                .BTATimeout = 0,
            },


            .LPCmd = {
                .LPGenShortWriteNoP    = DSI_LP_GSW0P_ENABLE,
                .LPGenShortWriteOneP   = DSI_LP_GSW1P_ENABLE,
                .LPGenShortWriteTwoP   = DSI_LP_GSW2P_ENABLE,
                .LPGenShortReadNoP     = DSI_LP_GSR0P_ENABLE,
                .LPGenShortReadOneP    = DSI_LP_GSR1P_ENABLE,
                .LPGenShortReadTwoP    = DSI_LP_GSR2P_ENABLE,
                .LPGenLongWrite        = DSI_LP_GLW_ENABLE,
                .LPDcsShortWriteNoP    = DSI_LP_DSW0P_ENABLE,
                .LPDcsShortWriteOneP   = DSI_LP_DSW1P_ENABLE,
                .LPDcsShortReadNoP     = DSI_LP_DSR0P_ENABLE,
                .LPDcsLongWrite        = DSI_LP_DLW_DISABLE,
                .LPMaxReadPacket       = DSI_LP_MRDP_ENABLE,
                .AcknowledgeRequest    = DSI_ACKNOWLEDGE_DISABLE, //disable LCD error reports
            },


            .vsyn_delay_us = 1000,
        },
    },
};
#endif /* BSP_LCDC_USING_DSI */

#define QAD_SPI_ITF LCDC_INTF_SPI_DCX_4DATA

static LCDC_InitTypeDef lcdc_int_cfg_spi =
{
    .lcd_itf = QAD_SPI_ITF, //LCDC_INTF_SPI_NODCX_1DATA,
    .freq = 24000000,
    //.color_mode = LCDC_PIXEL_FORMAT_RGB888,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,

    .cfg = {
        .spi = {
            .dummy_clock = 0, //0: QAD-SPI/SPI3   1:SPI4
            .syn_mode = HAL_LCDC_SYNC_DISABLE, //HAL_LCDC_SYNC_VER,
            .vsyn_polarity = 0,
            //default_vbp=2, frame rate=82, delay=115us,
            //TODO: use us to define delay instead of cycle, delay_cycle=115*48
            .vsyn_delay_us = 1000,
            .hsyn_num = 0,
        },
    },

};


static LCDC_InitTypeDef lcdc_int_cfg;


static void     ICN3311_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
static uint32_t ICN3311_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);
static void ICN3311_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable);



void HAL_DBG_printf(const char *fmt, ...)
{
    va_list args;
    static char rt_log_buf[128];
    extern void rt_kputs(const char *str);

    va_start(args, fmt);
    rt_vsnprintf(rt_log_buf, sizeof(rt_log_buf) - 1, fmt, args);
    rt_kputs(rt_log_buf);
    rt_kputs("\r\n");
    va_end(args);
}

LCD_DRIVER_EXPORT(icn3311, ICN3311_ID, &lcdc_int_cfg,
                  &ICN3311_drv,
                  ICN3311_LCD_PIXEL_WIDTH,
                  ICN3311_LCD_PIXEL_HEIGHT, 1);

/**
  * @}
  */


/** @defgroup ICN3311_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ICN3311_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ICN3311_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf) || HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 2000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }
    }
}


/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ICN3311_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];

#ifdef BSP_LCDC_USING_DSI
    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_dsi, sizeof(lcdc_int_cfg));
#else
    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_spi, sizeof(lcdc_int_cfg));
    rt_kprintf("\n ICN3311_Init lcdc_int_cfg_spi\n");
#endif

    /* Initialize ICN3311 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    LCD_DRIVER_DELAY_MS(10); //delay 10 ms

    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(20); //delay 20 ms
    //LCD_DRIVER_DELAY_MS(100); //delay 100 ms
    BSP_LCD_Reset(1);

    LCD_DRIVER_DELAY_MS(10); //LCD must at sleep in mode after power on, 10ms is enough
    rt_kprintf("\n ICN3311_Init \n");
#if 0

    ICN3311_WriteReg(hlcdc, 0x11, (uint8_t *)NULL, 0);
    LCD_DRIVER_DELAY_MS(80);


    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x01;
    parameter[3] = 0xc5;
    ICN3311_WriteReg(hlcdc, 0x2a, parameter, 4);
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x01;
    parameter[3] = 0xc5;
    ICN3311_WriteReg(hlcdc, 0x2b, parameter, 4);

    parameter[0] = 0x01;
    parameter[1] = 0xc2;
    ICN3311_WriteReg(hlcdc, 0x44, parameter, 2);

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0x35, parameter, 1); //enable TE

    //parameter[0] = 0x40;
    //ICN3311_WriteReg(hlcdc, 0x36, parameter, 1); //enable revert




    parameter[0] = 0x28;
    ICN3311_WriteReg(hlcdc, 0x53, parameter, 1);

    parameter[0] = 0xFF;
    ICN3311_WriteReg(hlcdc, 0x51, parameter, 1); //ser brightness

    parameter[0] = 0x06;
    ICN3311_WriteReg(hlcdc, 0xb1, parameter, 1); //set back proch


    LCD_DRIVER_DELAY_MS(50);
    ICN3311_WriteReg(hlcdc, 0x29, (uint8_t *)NULL, 0);
    LCD_DRIVER_DELAY_MS(80);



#if 0//Bist mode
    parameter[0] = 0x5a;
    parameter[1] = 0x5a;
    ICN3311_WriteReg(hlcdc, 0xc0, parameter, 2);

    parameter[0] = 0x81;
    ICN3311_WriteReg(hlcdc, 0xba, parameter, 1);

    while (1);
#endif

    //ICN3311_WriteReg(hlcdc,0x23, (uint8_t *)NULL, 0);

    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    //parameter[0] = 0x02;
    // ICN3311_WriteReg(hlcdc,ICN3311_TEARING_EFFECT, parameter, 1);
#else
    /*
    FE,00
    C4,80

    FE,00
    3A,55
    35,00
    53,20
    51,FF
    63,FF
    2A,00,00,10,01,7F
    2B,00,00,00,01,BF

    11,00
    Delay_ms(120);
    29,00
    */

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0xFE, parameter, 1);
    parameter[0] = 0x80;
    ICN3311_WriteReg(hlcdc, 0xC4, parameter, 1);

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0xFE, parameter, 1);

    parameter[0] = 0x55;
    ICN3311_WriteReg(hlcdc, 0x3A, parameter, 1);
    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0x35, parameter, 1);
    parameter[0] = 0x20;
    ICN3311_WriteReg(hlcdc, 0x53, parameter, 1);

    parameter[0] = 0xff;
    ICN3311_WriteReg(hlcdc, 0x51, parameter, 1);
    parameter[0] = 0xff;
    ICN3311_WriteReg(hlcdc, 0x63, parameter, 1);


    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x10;
    parameter[3] = 0x01;
    parameter[4] = 0x7f;
    ICN3311_WriteReg(hlcdc, 0x2A, parameter, 5);
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x00;
    parameter[3] = 0x01;
    parameter[4] = 0xbf;
    ICN3311_WriteReg(hlcdc, 0x2B, parameter, 5);

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0x11, parameter, 1);

#if 0//BIST mode        
    parameter[0] = 0xD0;
    ICN3311_WriteReg(hlcdc, 0xFE, parameter, 1);

    //bist en
    parameter[0] = 0x80;
    ICN3311_WriteReg(hlcdc, 0x4E, parameter, 1);

    parameter[0] = 0x40;
    ICN3311_WriteReg(hlcdc, 0xFE, parameter, 1);
    parameter[0] = 0x54;
    ICN3311_WriteReg(hlcdc, 0xAF, parameter, 1);

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0xFE, parameter, 1);
    parameter[0] = 0x0B;
    ICN3311_WriteReg(hlcdc, 0xC2, parameter, 1);
#endif
//sleep out+display on
    LCD_DRIVER_DELAY_MS(120);

    parameter[0] = 0x00;
    ICN3311_WriteReg(hlcdc, 0x29, parameter, 1);

//  ICN3311_WriteReg(hlcdc, 0x29, (uint8_t *)NULL, 0);
#endif

}



/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ICN3311_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;
    /*
        data = ICN3311_ReadData(hlcdc,ICN3311_CASET, 4);
        DEBUG_PRINTF("\nICN3311_CASET 0x%x \n", data);


        data = ICN3311_ReadData(hlcdc,ICN3311_RASET, 4);
        DEBUG_PRINTF("\nICN3311_RASET 0x%x \n", data);
    */
    data = ICN3311_ReadData(hlcdc, ICN3311_LCD_ID, 3);
    rt_kprintf("\nICN3311_ReadID 0x%x \n", data);
    data = ICN3311_ReadData(hlcdc, ICN3311_RASET, 4);
    rt_kprintf("\nICN3311_Read ICN3311_RASET 0x%x \n", data);

#if 0
    data = ICN3311_ReadData(hlcdc, 0x0a, 4);
    DEBUG_PRINTF("\nICN3311_Read  0a 0x%x \n", data);
#endif
    if (data)
    {
        DEBUG_PRINTF("LCD module use ICN3311 IC \n");
        data = ICN3311_ID;
    }
    data = ICN3311_ID;

    return data;

}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ICN3311_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    rt_kprintf("\n ICN3311_DisplayOn\n");
    ICN3311_WriteReg(hlcdc, ICN3311_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ICN3311_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    ICN3311_WriteReg(hlcdc, ICN3311_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void ICN3311_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];
    ICN3311_ALIGN2(Xpos0);
    ICN3311_ALIGN2(Ypos0);
    ICN3311_ALIGN1(Xpos1);
    ICN3311_ALIGN1(Ypos1);

    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Xpos0 += COL_OFFSET;
    Xpos1 += COL_OFFSET;

    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    ICN3311_WriteReg(hlcdc, ICN3311_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    ICN3311_WriteReg(hlcdc, ICN3311_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ICN3311_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;
    uint8_t   parameter[4];

    ICN3311_ALIGN2(Xpos);
    ICN3311_ALIGN2(Ypos);
    rt_kprintf("\n ICN3311_WritePixel xpos=%d, ypos=%d\n", Xpos, Ypos);

    if ((Xpos >= ICN3311_LCD_PIXEL_WIDTH) || (Ypos >= ICN3311_LCD_PIXEL_HEIGHT))
    {
        return;
    }

    /* Set Cursor */
    ICN3311_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ICN3311_WriteReg(hlcdc, ICN3311_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void ICN3311_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;
    ICN3311_ALIGN2(Xpos0);
    ICN3311_ALIGN2(Ypos0);
    ICN3311_ALIGN1(Xpos1);
    ICN3311_ALIGN1(Ypos1);


    uint32_t data;
    //data = ICN3311_ReadData(hlcdc, 0x05, 3);
    //DEBUG_PRINTF("DSI ERROR= 0x%x \n", data);

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);

    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ((0x32 << 24) | (ICN3311_WRITE_RAM << 8)), 4);
    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, (ICN3311_WRITE_RAM << 8), 2);
    }
    else
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ICN3311_WRITE_RAM, 1);
    }
}


/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ICN3311_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        uint32_t cmd;

        cmd = (0x02 << 24) | (LCD_Reg << 8);

        if (0 != NbParameters)
        {
            /* Send command's parameters if any */
            HAL_LCDC_WriteU32Reg(hlcdc, cmd, Parameters, NbParameters);
        }
        else
        {
            uint32_t v = 0;
            HAL_LCDC_WriteU32Reg(hlcdc, cmd, (uint8_t *)&v, 1);
        }

    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        uint8_t i;

        LCD_Reg = LCD_Reg << 8;

        if (0 == NbParameters)
        {
            HAL_LCDC_WriteU16Reg(hlcdc, LCD_Reg, NULL, 0);
        }
        else
        {
            for (i = 0; i < NbParameters; i++)
            {
                uint8_t v[2];
                v[0] = 0;
                v[1] = Parameters[i];

                HAL_LCDC_WriteU16Reg(hlcdc, LCD_Reg + i, v, 2);
            }
        }
    }
    else
    {
        HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
    }
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ICN3311_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ICN3311_ReadMode(hlcdc, true);
    if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        HAL_LCDC_ReadU32Reg(hlcdc, ((0x03 << 24) | (RegValue << 8)), (uint8_t *)&rd_data, ReadSize);
    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        uint8_t i;

        RegValue = RegValue << 8;
        for (i = 0; i < ReadSize; i++)
        {
            uint16_t v;
            HAL_LCDC_ReadU16Reg(hlcdc, RegValue + i, (uint8_t *)&v, 1);

            rd_data = (rd_data << 8) | (v & 0xFF);
        }
    }
    else
    {
        HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    }
    ICN3311_ReadMode(hlcdc, false);
    return rd_data;
}



uint32_t ICN3311_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t  r, g, b;
    uint32_t ret_v, read_value;
    DEBUG_PRINTF("ICN3311_ReadPixel[%d,%d]\n", Xpos, Ypos);

    ICN3311_ALIGN2(Xpos);
    ICN3311_ALIGN2(Ypos);

    ICN3311_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    read_value = ICN3311_ReadData(hlcdc, ICN3311_READ_RAM, 4);
    DEBUG_PRINTF("result: [%x]\n", read_value);

    b = (read_value >> 0) & 0xFF;
    g = (read_value >> 8) & 0xFF;
    r = (read_value >> 16) & 0xFF;

    DEBUG_PRINTF("r=%d, g=%d, b=%d \n", r, g, b);

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        ret_v = (uint32_t)(((r << 11) & 0xF800) | ((g << 5) & 0x7E0) | ((b >> 3) & 0X1F));
        break;

    /*
       (8bit R + 3bit dummy + 8bit G + 3bit dummy + 8bit B)

    */
    case LCDC_PIXEL_FORMAT_RGB888:
        ret_v = (uint32_t)(((r << 16) & 0xFF0000) | ((g << 8) & 0xFF00) | ((b) & 0XFF));
        break;

    default:
        RT_ASSERT(0);
        break;
    }


    //ICN3311_WriteReg(hlcdc,ICN3311_COLOR_MODE, parameter, 1);

    return ret_v;
}


void ICN3311_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x75;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;

    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        parameter[0] = 0x77;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
        break;

    default:
        return; //unsupport
        break;
    }

    ICN3311_WriteReg(hlcdc, ICN3311_COLOR_MODE, parameter, 1);


    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

#define ICN3311_BRIGHTNESS_MAX 0xFF

void     ICN3311_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((int)ICN3311_BRIGHTNESS_MAX * br / 100);
    ICN3311_WriteReg(hlcdc, ICN3311_WBRIGHT, &bright, 1);
}



/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
