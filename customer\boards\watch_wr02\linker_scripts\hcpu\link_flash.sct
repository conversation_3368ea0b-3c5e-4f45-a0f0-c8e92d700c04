#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I.
#include "rtconfig.h"
#include "../../../../../drivers/cmsis/sf32lb55x/mem_map.h"


; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 HCPU_FLASH_CODE_START_ADDR HCPU_FLASH_CODE_SIZE  {    ; load region size_region
  ER_IROM1 HCPU_FLASH_CODE_START_ADDR HCPU_FLASH_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   *.o (.rodata.*)
  }
  ER_IROM1_EX HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
   drv_spi_flash.o (.text.*)
   drv_spi_flash.o (.rodata.*)
   bf0_hal_spi_flash.o (.text.*)
   bf0_hal_dma.o   (.text.HAL_DMA_PollForTransfer)
   drv_common.o    (.text.HAL_GetTick)
   clock.o         (.text.rt_tick_get)
   bf0_hal_rcc.o   (.text.*)
  }  
  RW_IRAM1 HCPU_RAM_DATA_START_ADDR HCPU_RAM_DATA_SIZE-0x100  {  ; RW data  
#ifdef BSP_USING_JLINK_RTT   
   *.o (Jlink_RTT, +First)
#endif   
   .ANY (+RW +ZI)
  }
  RW_IRAM2 HCPU_RAM_DATA_START_ADDR+HCPU_RAM_DATA_SIZE-0x100 UNINIT 0x100  {  ; RW data  
  test_ctrl.o(UNINITZI)
  }
}

LR_IROM2 HCPU_FLASH_IMG_START_ADDR HCPU_FLASH_IMG_SIZE  {  ; load region size_region
  ER_IROM2 HCPU_FLASH_IMG_START_ADDR HCPU_FLASH_IMG_SIZE  {  ; RW data
   *.o (.ROM1_IMG)
   *.o (.ROM3_IMG)
   }
}

LR_IROM3 HCPU_FLASH_FONT_START_ADDR HCPU_FLASH_FONT_SIZE  {  ; load region size_region
  ER_IROM3 HCPU_FLASH_FONT_START_ADDR HCPU_FLASH_FONT_SIZE  {  ; RW data
   lvsf_font_*.o  (.rodata.*)
   }
}

;LR_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {    ; load region size_region
;  ER_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
;   *.o (.rodata.*)
;  }
;}

;LR_IROM3 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; load region size_region
;  RW_IRAM6 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; RW data
;   *.o (.ROM3_IMG)
;   }
;}


;LR_IROM3 RAM6_BASE RAM6_SIZE  {  ; load region size_region
;  RW_IRAM6 RAM6_BASE RAM6_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER1)
;   *.o (KWS_BIAS)
;  }
;  RW_IRAM7 RAM7_BASE RAM7_SIZE  {  ; RW data
;   *.o (KWS_WT)
;  }
;  RW_IRAM8 RAM8_BASE RAM8_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER2)
;   *.o (KWS_BIAS2)
;  }
;}


