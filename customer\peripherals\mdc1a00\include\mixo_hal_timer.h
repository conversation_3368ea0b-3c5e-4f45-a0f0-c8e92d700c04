/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include <stdint.h>

/*-----------------------------------------------------------------------------
 * FUNCTIONS DECLARATION
 *---------------------------------------------------------------------------*/

/**
 * @brief delay in microsecond level
 *
 * @param ms
 */
void mixo_hal_delay_ms(uint32_t ms);

/**
 * @brief delay in millisecond level
 *
 * @param us
 */
void mixo_hal_delay_us(uint32_t us);

/**
 * @brief get microsecond 
 *
 * @return uint32_t
 */
uint32_t mixo_hal_get_tick_ms(void);


/**
 * @brief Get millisecond by timer service
 * 
 * @return uint32_t 
 */
uint32_t mixo_hal_get_tick_us(void);

#ifdef __cplusplus
}
#endif
