#ifndef __HPSYS_RCC_H
#define __HPSYS_RCC_H

typedef struct
{
    __IO uint32_t RSTR1;
    __IO uint32_t RSTR2;
    __IO uint32_t ENR1;
    __IO uint32_t ENR2;
    __IO uint32_t CSR;
    __IO uint32_t CFGR;
    __IO uint32_t I2SR;
    __IO uint32_t USBCR;
    __IO uint32_t DLL1CR;
    __IO uint32_t DLL2CR;
    __IO uint32_t DLL3CR;
    __IO uint32_t HRCCAL1;
    __IO uint32_t HRCCAL2;
    __IO uint32_t DBGCLKR;
} HPSYS_RCC_TypeDef;


/**************** Bit definition for HPSYS_RCC_RSTR1 register *****************/
#define HPSYS_RCC_RSTR1_DMAC1_Pos       (0U)
#define HPSYS_RCC_RSTR1_DMAC1_Msk       (0x1UL << HPSYS_RCC_RSTR1_DMAC1_Pos)
#define HPSYS_RCC_RSTR1_DMAC1           HPSYS_RCC_RSTR1_DMAC1_Msk
#define HPSYS_RCC_RSTR1_MAILBOX1_Pos    (1U)
#define HPSYS_RCC_RSTR1_MAILBOX1_Msk    (0x1UL << HPSYS_RCC_RSTR1_MAILBOX1_Pos)
#define HPSYS_RCC_RSTR1_MAILBOX1        HPSYS_RCC_RSTR1_MAILBOX1_Msk
#define HPSYS_RCC_RSTR1_PINMUX1_Pos     (2U)
#define HPSYS_RCC_RSTR1_PINMUX1_Msk     (0x1UL << HPSYS_RCC_RSTR1_PINMUX1_Pos)
#define HPSYS_RCC_RSTR1_PINMUX1         HPSYS_RCC_RSTR1_PINMUX1_Msk
#define HPSYS_RCC_RSTR1_USART1_Pos      (3U)
#define HPSYS_RCC_RSTR1_USART1_Msk      (0x1UL << HPSYS_RCC_RSTR1_USART1_Pos)
#define HPSYS_RCC_RSTR1_USART1          HPSYS_RCC_RSTR1_USART1_Msk
#define HPSYS_RCC_RSTR1_USART2_Pos      (4U)
#define HPSYS_RCC_RSTR1_USART2_Msk      (0x1UL << HPSYS_RCC_RSTR1_USART2_Pos)
#define HPSYS_RCC_RSTR1_USART2          HPSYS_RCC_RSTR1_USART2_Msk
#define HPSYS_RCC_RSTR1_EZIP_Pos        (5U)
#define HPSYS_RCC_RSTR1_EZIP_Msk        (0x1UL << HPSYS_RCC_RSTR1_EZIP_Pos)
#define HPSYS_RCC_RSTR1_EZIP            HPSYS_RCC_RSTR1_EZIP_Msk
#define HPSYS_RCC_RSTR1_EPIC_Pos        (6U)
#define HPSYS_RCC_RSTR1_EPIC_Msk        (0x1UL << HPSYS_RCC_RSTR1_EPIC_Pos)
#define HPSYS_RCC_RSTR1_EPIC            HPSYS_RCC_RSTR1_EPIC_Msk
#define HPSYS_RCC_RSTR1_LCDC1_Pos       (7U)
#define HPSYS_RCC_RSTR1_LCDC1_Msk       (0x1UL << HPSYS_RCC_RSTR1_LCDC1_Pos)
#define HPSYS_RCC_RSTR1_LCDC1           HPSYS_RCC_RSTR1_LCDC1_Msk
#define HPSYS_RCC_RSTR1_I2S1_Pos        (8U)
#define HPSYS_RCC_RSTR1_I2S1_Msk        (0x1UL << HPSYS_RCC_RSTR1_I2S1_Pos)
#define HPSYS_RCC_RSTR1_I2S1            HPSYS_RCC_RSTR1_I2S1_Msk
#define HPSYS_RCC_RSTR1_I2S2_Pos        (9U)
#define HPSYS_RCC_RSTR1_I2S2_Msk        (0x1UL << HPSYS_RCC_RSTR1_I2S2_Pos)
#define HPSYS_RCC_RSTR1_I2S2            HPSYS_RCC_RSTR1_I2S2_Msk
#define HPSYS_RCC_RSTR1_SYSCFG1_Pos     (10U)
#define HPSYS_RCC_RSTR1_SYSCFG1_Msk     (0x1UL << HPSYS_RCC_RSTR1_SYSCFG1_Pos)
#define HPSYS_RCC_RSTR1_SYSCFG1         HPSYS_RCC_RSTR1_SYSCFG1_Msk
#define HPSYS_RCC_RSTR1_EFUSEC_Pos      (11U)
#define HPSYS_RCC_RSTR1_EFUSEC_Msk      (0x1UL << HPSYS_RCC_RSTR1_EFUSEC_Pos)
#define HPSYS_RCC_RSTR1_EFUSEC          HPSYS_RCC_RSTR1_EFUSEC_Msk
#define HPSYS_RCC_RSTR1_AES_Pos         (12U)
#define HPSYS_RCC_RSTR1_AES_Msk         (0x1UL << HPSYS_RCC_RSTR1_AES_Pos)
#define HPSYS_RCC_RSTR1_AES             HPSYS_RCC_RSTR1_AES_Msk
#define HPSYS_RCC_RSTR1_CRC_Pos         (13U)
#define HPSYS_RCC_RSTR1_CRC_Msk         (0x1UL << HPSYS_RCC_RSTR1_CRC_Pos)
#define HPSYS_RCC_RSTR1_CRC             HPSYS_RCC_RSTR1_CRC_Msk
#define HPSYS_RCC_RSTR1_TRNG_Pos        (14U)
#define HPSYS_RCC_RSTR1_TRNG_Msk        (0x1UL << HPSYS_RCC_RSTR1_TRNG_Pos)
#define HPSYS_RCC_RSTR1_TRNG            HPSYS_RCC_RSTR1_TRNG_Msk
#define HPSYS_RCC_RSTR1_GPTIM1_Pos      (15U)
#define HPSYS_RCC_RSTR1_GPTIM1_Msk      (0x1UL << HPSYS_RCC_RSTR1_GPTIM1_Pos)
#define HPSYS_RCC_RSTR1_GPTIM1          HPSYS_RCC_RSTR1_GPTIM1_Msk
#define HPSYS_RCC_RSTR1_GPTIM2_Pos      (16U)
#define HPSYS_RCC_RSTR1_GPTIM2_Msk      (0x1UL << HPSYS_RCC_RSTR1_GPTIM2_Pos)
#define HPSYS_RCC_RSTR1_GPTIM2          HPSYS_RCC_RSTR1_GPTIM2_Msk
#define HPSYS_RCC_RSTR1_BTIM1_Pos       (17U)
#define HPSYS_RCC_RSTR1_BTIM1_Msk       (0x1UL << HPSYS_RCC_RSTR1_BTIM1_Pos)
#define HPSYS_RCC_RSTR1_BTIM1           HPSYS_RCC_RSTR1_BTIM1_Msk
#define HPSYS_RCC_RSTR1_BTIM2_Pos       (18U)
#define HPSYS_RCC_RSTR1_BTIM2_Msk       (0x1UL << HPSYS_RCC_RSTR1_BTIM2_Pos)
#define HPSYS_RCC_RSTR1_BTIM2           HPSYS_RCC_RSTR1_BTIM2_Msk
#define HPSYS_RCC_RSTR1_WDT1_Pos        (19U)
#define HPSYS_RCC_RSTR1_WDT1_Msk        (0x1UL << HPSYS_RCC_RSTR1_WDT1_Pos)
#define HPSYS_RCC_RSTR1_WDT1            HPSYS_RCC_RSTR1_WDT1_Msk
#define HPSYS_RCC_RSTR1_SPI1_Pos        (20U)
#define HPSYS_RCC_RSTR1_SPI1_Msk        (0x1UL << HPSYS_RCC_RSTR1_SPI1_Pos)
#define HPSYS_RCC_RSTR1_SPI1            HPSYS_RCC_RSTR1_SPI1_Msk
#define HPSYS_RCC_RSTR1_SPI2_Pos        (21U)
#define HPSYS_RCC_RSTR1_SPI2_Msk        (0x1UL << HPSYS_RCC_RSTR1_SPI2_Pos)
#define HPSYS_RCC_RSTR1_SPI2            HPSYS_RCC_RSTR1_SPI2_Msk
#define HPSYS_RCC_RSTR1_EXTDMA_Pos      (22U)
#define HPSYS_RCC_RSTR1_EXTDMA_Msk      (0x1UL << HPSYS_RCC_RSTR1_EXTDMA_Pos)
#define HPSYS_RCC_RSTR1_EXTDMA          HPSYS_RCC_RSTR1_EXTDMA_Msk
#define HPSYS_RCC_RSTR1_PSRAMC_Pos      (23U)
#define HPSYS_RCC_RSTR1_PSRAMC_Msk      (0x1UL << HPSYS_RCC_RSTR1_PSRAMC_Pos)
#define HPSYS_RCC_RSTR1_PSRAMC          HPSYS_RCC_RSTR1_PSRAMC_Msk
#define HPSYS_RCC_RSTR1_NNACC_Pos       (24U)
#define HPSYS_RCC_RSTR1_NNACC_Msk       (0x1UL << HPSYS_RCC_RSTR1_NNACC_Pos)
#define HPSYS_RCC_RSTR1_NNACC           HPSYS_RCC_RSTR1_NNACC_Msk
#define HPSYS_RCC_RSTR1_PDM1_Pos        (25U)
#define HPSYS_RCC_RSTR1_PDM1_Msk        (0x1UL << HPSYS_RCC_RSTR1_PDM1_Pos)
#define HPSYS_RCC_RSTR1_PDM1            HPSYS_RCC_RSTR1_PDM1_Msk
#define HPSYS_RCC_RSTR1_PDM2_Pos        (26U)
#define HPSYS_RCC_RSTR1_PDM2_Msk        (0x1UL << HPSYS_RCC_RSTR1_PDM2_Pos)
#define HPSYS_RCC_RSTR1_PDM2            HPSYS_RCC_RSTR1_PDM2_Msk
#define HPSYS_RCC_RSTR1_I2C1_Pos        (27U)
#define HPSYS_RCC_RSTR1_I2C1_Msk        (0x1UL << HPSYS_RCC_RSTR1_I2C1_Pos)
#define HPSYS_RCC_RSTR1_I2C1            HPSYS_RCC_RSTR1_I2C1_Msk
#define HPSYS_RCC_RSTR1_I2C2_Pos        (28U)
#define HPSYS_RCC_RSTR1_I2C2_Msk        (0x1UL << HPSYS_RCC_RSTR1_I2C2_Pos)
#define HPSYS_RCC_RSTR1_I2C2            HPSYS_RCC_RSTR1_I2C2_Msk
#define HPSYS_RCC_RSTR1_DSIHOST_Pos     (29U)
#define HPSYS_RCC_RSTR1_DSIHOST_Msk     (0x1UL << HPSYS_RCC_RSTR1_DSIHOST_Pos)
#define HPSYS_RCC_RSTR1_DSIHOST         HPSYS_RCC_RSTR1_DSIHOST_Msk
#define HPSYS_RCC_RSTR1_DSIPHY_Pos      (30U)
#define HPSYS_RCC_RSTR1_DSIPHY_Msk      (0x1UL << HPSYS_RCC_RSTR1_DSIPHY_Pos)
#define HPSYS_RCC_RSTR1_DSIPHY          HPSYS_RCC_RSTR1_DSIPHY_Msk
#define HPSYS_RCC_RSTR1_PTC1_Pos        (31U)
#define HPSYS_RCC_RSTR1_PTC1_Msk        (0x1UL << HPSYS_RCC_RSTR1_PTC1_Pos)
#define HPSYS_RCC_RSTR1_PTC1            HPSYS_RCC_RSTR1_PTC1_Msk

/**************** Bit definition for HPSYS_RCC_RSTR2 register *****************/
#define HPSYS_RCC_RSTR2_GPIO1_Pos       (0U)
#define HPSYS_RCC_RSTR2_GPIO1_Msk       (0x1UL << HPSYS_RCC_RSTR2_GPIO1_Pos)
#define HPSYS_RCC_RSTR2_GPIO1           HPSYS_RCC_RSTR2_GPIO1_Msk
#define HPSYS_RCC_RSTR2_QSPI1_Pos       (1U)
#define HPSYS_RCC_RSTR2_QSPI1_Msk       (0x1UL << HPSYS_RCC_RSTR2_QSPI1_Pos)
#define HPSYS_RCC_RSTR2_QSPI1           HPSYS_RCC_RSTR2_QSPI1_Msk
#define HPSYS_RCC_RSTR2_QSPI2_Pos       (2U)
#define HPSYS_RCC_RSTR2_QSPI2_Msk       (0x1UL << HPSYS_RCC_RSTR2_QSPI2_Pos)
#define HPSYS_RCC_RSTR2_QSPI2           HPSYS_RCC_RSTR2_QSPI2_Msk
#define HPSYS_RCC_RSTR2_QSPI3_Pos       (3U)
#define HPSYS_RCC_RSTR2_QSPI3_Msk       (0x1UL << HPSYS_RCC_RSTR2_QSPI3_Pos)
#define HPSYS_RCC_RSTR2_QSPI3           HPSYS_RCC_RSTR2_QSPI3_Msk
#define HPSYS_RCC_RSTR2_SDMMC1_Pos      (4U)
#define HPSYS_RCC_RSTR2_SDMMC1_Msk      (0x1UL << HPSYS_RCC_RSTR2_SDMMC1_Pos)
#define HPSYS_RCC_RSTR2_SDMMC1          HPSYS_RCC_RSTR2_SDMMC1_Msk
#define HPSYS_RCC_RSTR2_SDMMC2_Pos      (5U)
#define HPSYS_RCC_RSTR2_SDMMC2_Msk      (0x1UL << HPSYS_RCC_RSTR2_SDMMC2_Pos)
#define HPSYS_RCC_RSTR2_SDMMC2          HPSYS_RCC_RSTR2_SDMMC2_Msk
#define HPSYS_RCC_RSTR2_USBC_Pos        (6U)
#define HPSYS_RCC_RSTR2_USBC_Msk        (0x1UL << HPSYS_RCC_RSTR2_USBC_Pos)
#define HPSYS_RCC_RSTR2_USBC            HPSYS_RCC_RSTR2_USBC_Msk
#define HPSYS_RCC_RSTR2_BUSMON1_Pos     (7U)
#define HPSYS_RCC_RSTR2_BUSMON1_Msk     (0x1UL << HPSYS_RCC_RSTR2_BUSMON1_Pos)
#define HPSYS_RCC_RSTR2_BUSMON1         HPSYS_RCC_RSTR2_BUSMON1_Msk
#define HPSYS_RCC_RSTR2_I2C3_Pos        (8U)
#define HPSYS_RCC_RSTR2_I2C3_Msk        (0x1UL << HPSYS_RCC_RSTR2_I2C3_Pos)
#define HPSYS_RCC_RSTR2_I2C3            HPSYS_RCC_RSTR2_I2C3_Msk

/***************** Bit definition for HPSYS_RCC_ENR1 register *****************/
#define HPSYS_RCC_ENR1_DMAC1_Pos        (0U)
#define HPSYS_RCC_ENR1_DMAC1_Msk        (0x1UL << HPSYS_RCC_ENR1_DMAC1_Pos)
#define HPSYS_RCC_ENR1_DMAC1            HPSYS_RCC_ENR1_DMAC1_Msk
#define HPSYS_RCC_ENR1_MAILBOX1_Pos     (1U)
#define HPSYS_RCC_ENR1_MAILBOX1_Msk     (0x1UL << HPSYS_RCC_ENR1_MAILBOX1_Pos)
#define HPSYS_RCC_ENR1_MAILBOX1         HPSYS_RCC_ENR1_MAILBOX1_Msk
#define HPSYS_RCC_ENR1_PINMUX1_Pos      (2U)
#define HPSYS_RCC_ENR1_PINMUX1_Msk      (0x1UL << HPSYS_RCC_ENR1_PINMUX1_Pos)
#define HPSYS_RCC_ENR1_PINMUX1          HPSYS_RCC_ENR1_PINMUX1_Msk
#define HPSYS_RCC_ENR1_USART1_Pos       (3U)
#define HPSYS_RCC_ENR1_USART1_Msk       (0x1UL << HPSYS_RCC_ENR1_USART1_Pos)
#define HPSYS_RCC_ENR1_USART1           HPSYS_RCC_ENR1_USART1_Msk
#define HPSYS_RCC_ENR1_USART2_Pos       (4U)
#define HPSYS_RCC_ENR1_USART2_Msk       (0x1UL << HPSYS_RCC_ENR1_USART2_Pos)
#define HPSYS_RCC_ENR1_USART2           HPSYS_RCC_ENR1_USART2_Msk
#define HPSYS_RCC_ENR1_EZIP_Pos         (5U)
#define HPSYS_RCC_ENR1_EZIP_Msk         (0x1UL << HPSYS_RCC_ENR1_EZIP_Pos)
#define HPSYS_RCC_ENR1_EZIP             HPSYS_RCC_ENR1_EZIP_Msk
#define HPSYS_RCC_ENR1_EPIC_Pos         (6U)
#define HPSYS_RCC_ENR1_EPIC_Msk         (0x1UL << HPSYS_RCC_ENR1_EPIC_Pos)
#define HPSYS_RCC_ENR1_EPIC             HPSYS_RCC_ENR1_EPIC_Msk
#define HPSYS_RCC_ENR1_LCDC1_Pos        (7U)
#define HPSYS_RCC_ENR1_LCDC1_Msk        (0x1UL << HPSYS_RCC_ENR1_LCDC1_Pos)
#define HPSYS_RCC_ENR1_LCDC1            HPSYS_RCC_ENR1_LCDC1_Msk
#define HPSYS_RCC_ENR1_I2S1_Pos         (8U)
#define HPSYS_RCC_ENR1_I2S1_Msk         (0x1UL << HPSYS_RCC_ENR1_I2S1_Pos)
#define HPSYS_RCC_ENR1_I2S1             HPSYS_RCC_ENR1_I2S1_Msk
#define HPSYS_RCC_ENR1_I2S2_Pos         (9U)
#define HPSYS_RCC_ENR1_I2S2_Msk         (0x1UL << HPSYS_RCC_ENR1_I2S2_Pos)
#define HPSYS_RCC_ENR1_I2S2             HPSYS_RCC_ENR1_I2S2_Msk
#define HPSYS_RCC_ENR1_SYSCFG1_Pos      (10U)
#define HPSYS_RCC_ENR1_SYSCFG1_Msk      (0x1UL << HPSYS_RCC_ENR1_SYSCFG1_Pos)
#define HPSYS_RCC_ENR1_SYSCFG1          HPSYS_RCC_ENR1_SYSCFG1_Msk
#define HPSYS_RCC_ENR1_EFUSEC_Pos       (11U)
#define HPSYS_RCC_ENR1_EFUSEC_Msk       (0x1UL << HPSYS_RCC_ENR1_EFUSEC_Pos)
#define HPSYS_RCC_ENR1_EFUSEC           HPSYS_RCC_ENR1_EFUSEC_Msk
#define HPSYS_RCC_ENR1_AES_Pos          (12U)
#define HPSYS_RCC_ENR1_AES_Msk          (0x1UL << HPSYS_RCC_ENR1_AES_Pos)
#define HPSYS_RCC_ENR1_AES              HPSYS_RCC_ENR1_AES_Msk
#define HPSYS_RCC_ENR1_CRC_Pos          (13U)
#define HPSYS_RCC_ENR1_CRC_Msk          (0x1UL << HPSYS_RCC_ENR1_CRC_Pos)
#define HPSYS_RCC_ENR1_CRC              HPSYS_RCC_ENR1_CRC_Msk
#define HPSYS_RCC_ENR1_TRNG_Pos         (14U)
#define HPSYS_RCC_ENR1_TRNG_Msk         (0x1UL << HPSYS_RCC_ENR1_TRNG_Pos)
#define HPSYS_RCC_ENR1_TRNG             HPSYS_RCC_ENR1_TRNG_Msk
#define HPSYS_RCC_ENR1_GPTIM1_Pos       (15U)
#define HPSYS_RCC_ENR1_GPTIM1_Msk       (0x1UL << HPSYS_RCC_ENR1_GPTIM1_Pos)
#define HPSYS_RCC_ENR1_GPTIM1           HPSYS_RCC_ENR1_GPTIM1_Msk
#define HPSYS_RCC_ENR1_GPTIM2_Pos       (16U)
#define HPSYS_RCC_ENR1_GPTIM2_Msk       (0x1UL << HPSYS_RCC_ENR1_GPTIM2_Pos)
#define HPSYS_RCC_ENR1_GPTIM2           HPSYS_RCC_ENR1_GPTIM2_Msk
#define HPSYS_RCC_ENR1_BTIM1_Pos        (17U)
#define HPSYS_RCC_ENR1_BTIM1_Msk        (0x1UL << HPSYS_RCC_ENR1_BTIM1_Pos)
#define HPSYS_RCC_ENR1_BTIM1            HPSYS_RCC_ENR1_BTIM1_Msk
#define HPSYS_RCC_ENR1_BTIM2_Pos        (18U)
#define HPSYS_RCC_ENR1_BTIM2_Msk        (0x1UL << HPSYS_RCC_ENR1_BTIM2_Pos)
#define HPSYS_RCC_ENR1_BTIM2            HPSYS_RCC_ENR1_BTIM2_Msk
#define HPSYS_RCC_ENR1_WDT1_Pos         (19U)
#define HPSYS_RCC_ENR1_WDT1_Msk         (0x1UL << HPSYS_RCC_ENR1_WDT1_Pos)
#define HPSYS_RCC_ENR1_WDT1             HPSYS_RCC_ENR1_WDT1_Msk
#define HPSYS_RCC_ENR1_SPI1_Pos         (20U)
#define HPSYS_RCC_ENR1_SPI1_Msk         (0x1UL << HPSYS_RCC_ENR1_SPI1_Pos)
#define HPSYS_RCC_ENR1_SPI1             HPSYS_RCC_ENR1_SPI1_Msk
#define HPSYS_RCC_ENR1_SPI2_Pos         (21U)
#define HPSYS_RCC_ENR1_SPI2_Msk         (0x1UL << HPSYS_RCC_ENR1_SPI2_Pos)
#define HPSYS_RCC_ENR1_SPI2             HPSYS_RCC_ENR1_SPI2_Msk
#define HPSYS_RCC_ENR1_EXTDMA_Pos       (22U)
#define HPSYS_RCC_ENR1_EXTDMA_Msk       (0x1UL << HPSYS_RCC_ENR1_EXTDMA_Pos)
#define HPSYS_RCC_ENR1_EXTDMA           HPSYS_RCC_ENR1_EXTDMA_Msk
#define HPSYS_RCC_ENR1_PSRAMC_Pos       (23U)
#define HPSYS_RCC_ENR1_PSRAMC_Msk       (0x1UL << HPSYS_RCC_ENR1_PSRAMC_Pos)
#define HPSYS_RCC_ENR1_PSRAMC           HPSYS_RCC_ENR1_PSRAMC_Msk
#define HPSYS_RCC_ENR1_NNACC_Pos        (24U)
#define HPSYS_RCC_ENR1_NNACC_Msk        (0x1UL << HPSYS_RCC_ENR1_NNACC_Pos)
#define HPSYS_RCC_ENR1_NNACC            HPSYS_RCC_ENR1_NNACC_Msk
#define HPSYS_RCC_ENR1_PDM1_Pos         (25U)
#define HPSYS_RCC_ENR1_PDM1_Msk         (0x1UL << HPSYS_RCC_ENR1_PDM1_Pos)
#define HPSYS_RCC_ENR1_PDM1             HPSYS_RCC_ENR1_PDM1_Msk
#define HPSYS_RCC_ENR1_PDM2_Pos         (26U)
#define HPSYS_RCC_ENR1_PDM2_Msk         (0x1UL << HPSYS_RCC_ENR1_PDM2_Pos)
#define HPSYS_RCC_ENR1_PDM2             HPSYS_RCC_ENR1_PDM2_Msk
#define HPSYS_RCC_ENR1_I2C1_Pos         (27U)
#define HPSYS_RCC_ENR1_I2C1_Msk         (0x1UL << HPSYS_RCC_ENR1_I2C1_Pos)
#define HPSYS_RCC_ENR1_I2C1             HPSYS_RCC_ENR1_I2C1_Msk
#define HPSYS_RCC_ENR1_I2C2_Pos         (28U)
#define HPSYS_RCC_ENR1_I2C2_Msk         (0x1UL << HPSYS_RCC_ENR1_I2C2_Pos)
#define HPSYS_RCC_ENR1_I2C2             HPSYS_RCC_ENR1_I2C2_Msk
#define HPSYS_RCC_ENR1_DSIHOST_Pos      (29U)
#define HPSYS_RCC_ENR1_DSIHOST_Msk      (0x1UL << HPSYS_RCC_ENR1_DSIHOST_Pos)
#define HPSYS_RCC_ENR1_DSIHOST          HPSYS_RCC_ENR1_DSIHOST_Msk
#define HPSYS_RCC_ENR1_DSIPHY_Pos       (30U)
#define HPSYS_RCC_ENR1_DSIPHY_Msk       (0x1UL << HPSYS_RCC_ENR1_DSIPHY_Pos)
#define HPSYS_RCC_ENR1_DSIPHY           HPSYS_RCC_ENR1_DSIPHY_Msk
#define HPSYS_RCC_ENR1_PTC1_Pos         (31U)
#define HPSYS_RCC_ENR1_PTC1_Msk         (0x1UL << HPSYS_RCC_ENR1_PTC1_Pos)
#define HPSYS_RCC_ENR1_PTC1             HPSYS_RCC_ENR1_PTC1_Msk

/***************** Bit definition for HPSYS_RCC_ENR2 register *****************/
#define HPSYS_RCC_ENR2_GPIO1_Pos        (0U)
#define HPSYS_RCC_ENR2_GPIO1_Msk        (0x1UL << HPSYS_RCC_ENR2_GPIO1_Pos)
#define HPSYS_RCC_ENR2_GPIO1            HPSYS_RCC_ENR2_GPIO1_Msk
#define HPSYS_RCC_ENR2_QSPI1_Pos        (1U)
#define HPSYS_RCC_ENR2_QSPI1_Msk        (0x1UL << HPSYS_RCC_ENR2_QSPI1_Pos)
#define HPSYS_RCC_ENR2_QSPI1            HPSYS_RCC_ENR2_QSPI1_Msk
#define HPSYS_RCC_ENR2_QSPI2_Pos        (2U)
#define HPSYS_RCC_ENR2_QSPI2_Msk        (0x1UL << HPSYS_RCC_ENR2_QSPI2_Pos)
#define HPSYS_RCC_ENR2_QSPI2            HPSYS_RCC_ENR2_QSPI2_Msk
#define HPSYS_RCC_ENR2_QSPI3_Pos        (3U)
#define HPSYS_RCC_ENR2_QSPI3_Msk        (0x1UL << HPSYS_RCC_ENR2_QSPI3_Pos)
#define HPSYS_RCC_ENR2_QSPI3            HPSYS_RCC_ENR2_QSPI3_Msk
#define HPSYS_RCC_ENR2_SDMMC1_Pos       (4U)
#define HPSYS_RCC_ENR2_SDMMC1_Msk       (0x1UL << HPSYS_RCC_ENR2_SDMMC1_Pos)
#define HPSYS_RCC_ENR2_SDMMC1           HPSYS_RCC_ENR2_SDMMC1_Msk
#define HPSYS_RCC_ENR2_SDMMC2_Pos       (5U)
#define HPSYS_RCC_ENR2_SDMMC2_Msk       (0x1UL << HPSYS_RCC_ENR2_SDMMC2_Pos)
#define HPSYS_RCC_ENR2_SDMMC2           HPSYS_RCC_ENR2_SDMMC2_Msk
#define HPSYS_RCC_ENR2_USBC_Pos         (6U)
#define HPSYS_RCC_ENR2_USBC_Msk         (0x1UL << HPSYS_RCC_ENR2_USBC_Pos)
#define HPSYS_RCC_ENR2_USBC             HPSYS_RCC_ENR2_USBC_Msk
#define HPSYS_RCC_ENR2_BUSMON1_Pos      (7U)
#define HPSYS_RCC_ENR2_BUSMON1_Msk      (0x1UL << HPSYS_RCC_ENR2_BUSMON1_Pos)
#define HPSYS_RCC_ENR2_BUSMON1          HPSYS_RCC_ENR2_BUSMON1_Msk
#define HPSYS_RCC_ENR2_I2C3_Pos         (8U)
#define HPSYS_RCC_ENR2_I2C3_Msk         (0x1UL << HPSYS_RCC_ENR2_I2C3_Pos)
#define HPSYS_RCC_ENR2_I2C3             HPSYS_RCC_ENR2_I2C3_Msk

/***************** Bit definition for HPSYS_RCC_CSR register ******************/
#define HPSYS_RCC_CSR_SEL_SYS_Pos       (0U)
#define HPSYS_RCC_CSR_SEL_SYS_Msk       (0x3UL << HPSYS_RCC_CSR_SEL_SYS_Pos)
#define HPSYS_RCC_CSR_SEL_SYS           HPSYS_RCC_CSR_SEL_SYS_Msk
#define HPSYS_RCC_CSR_SEL_PSRAMC_Pos    (2U)
#define HPSYS_RCC_CSR_SEL_PSRAMC_Msk    (0x3UL << HPSYS_RCC_CSR_SEL_PSRAMC_Pos)
#define HPSYS_RCC_CSR_SEL_PSRAMC        HPSYS_RCC_CSR_SEL_PSRAMC_Msk
#define HPSYS_RCC_CSR_SEL_QSPI1_Pos     (4U)
#define HPSYS_RCC_CSR_SEL_QSPI1_Msk     (0x3UL << HPSYS_RCC_CSR_SEL_QSPI1_Pos)
#define HPSYS_RCC_CSR_SEL_QSPI1         HPSYS_RCC_CSR_SEL_QSPI1_Msk
#define HPSYS_RCC_CSR_SEL_QSPI2_Pos     (6U)
#define HPSYS_RCC_CSR_SEL_QSPI2_Msk     (0x3UL << HPSYS_RCC_CSR_SEL_QSPI2_Pos)
#define HPSYS_RCC_CSR_SEL_QSPI2         HPSYS_RCC_CSR_SEL_QSPI2_Msk
#define HPSYS_RCC_CSR_SEL_QSPI3_Pos     (8U)
#define HPSYS_RCC_CSR_SEL_QSPI3_Msk     (0x3UL << HPSYS_RCC_CSR_SEL_QSPI3_Pos)
#define HPSYS_RCC_CSR_SEL_QSPI3         HPSYS_RCC_CSR_SEL_QSPI3_Msk
#define HPSYS_RCC_CSR_SEL_USART1_Pos    (10U)
#define HPSYS_RCC_CSR_SEL_USART1_Msk    (0x1UL << HPSYS_RCC_CSR_SEL_USART1_Pos)
#define HPSYS_RCC_CSR_SEL_USART1        HPSYS_RCC_CSR_SEL_USART1_Msk
#define HPSYS_RCC_CSR_SEL_USART2_Pos    (11U)
#define HPSYS_RCC_CSR_SEL_USART2_Msk    (0x1UL << HPSYS_RCC_CSR_SEL_USART2_Pos)
#define HPSYS_RCC_CSR_SEL_USART2        HPSYS_RCC_CSR_SEL_USART2_Msk
#define HPSYS_RCC_CSR_SEL_PDM1_Pos      (12U)
#define HPSYS_RCC_CSR_SEL_PDM1_Msk      (0x1UL << HPSYS_RCC_CSR_SEL_PDM1_Pos)
#define HPSYS_RCC_CSR_SEL_PDM1          HPSYS_RCC_CSR_SEL_PDM1_Msk
#define HPSYS_RCC_CSR_SEL_PDM2_Pos      (13U)
#define HPSYS_RCC_CSR_SEL_PDM2_Msk      (0x1UL << HPSYS_RCC_CSR_SEL_PDM2_Pos)
#define HPSYS_RCC_CSR_SEL_PDM2          HPSYS_RCC_CSR_SEL_PDM2_Msk
#define HPSYS_RCC_CSR_SEL_USBC_Pos      (14U)
#define HPSYS_RCC_CSR_SEL_USBC_Msk      (0x1UL << HPSYS_RCC_CSR_SEL_USBC_Pos)
#define HPSYS_RCC_CSR_SEL_USBC          HPSYS_RCC_CSR_SEL_USBC_Msk

/***************** Bit definition for HPSYS_RCC_CFGR register *****************/
#define HPSYS_RCC_CFGR_HDIV_Pos         (0U)
#define HPSYS_RCC_CFGR_HDIV_Msk         (0xFFUL << HPSYS_RCC_CFGR_HDIV_Pos)
#define HPSYS_RCC_CFGR_HDIV             HPSYS_RCC_CFGR_HDIV_Msk
#define HPSYS_RCC_CFGR_PDIV1_Pos        (8U)
#define HPSYS_RCC_CFGR_PDIV1_Msk        (0x7UL << HPSYS_RCC_CFGR_PDIV1_Pos)
#define HPSYS_RCC_CFGR_PDIV1            HPSYS_RCC_CFGR_PDIV1_Msk
#define HPSYS_RCC_CFGR_PDIV2_Pos        (12U)
#define HPSYS_RCC_CFGR_PDIV2_Msk        (0x7UL << HPSYS_RCC_CFGR_PDIV2_Pos)
#define HPSYS_RCC_CFGR_PDIV2            HPSYS_RCC_CFGR_PDIV2_Msk

/***************** Bit definition for HPSYS_RCC_I2SR register *****************/
#define HPSYS_RCC_I2SR_DIV_Pos          (0U)
#define HPSYS_RCC_I2SR_DIV_Msk          (0x1FUL << HPSYS_RCC_I2SR_DIV_Pos)
#define HPSYS_RCC_I2SR_DIV              HPSYS_RCC_I2SR_DIV_Msk

/**************** Bit definition for HPSYS_RCC_USBCR register *****************/
#define HPSYS_RCC_USBCR_DIV_Pos         (0U)
#define HPSYS_RCC_USBCR_DIV_Msk         (0x7UL << HPSYS_RCC_USBCR_DIV_Pos)
#define HPSYS_RCC_USBCR_DIV             HPSYS_RCC_USBCR_DIV_Msk

/**************** Bit definition for HPSYS_RCC_DLL1CR register ****************/
#define HPSYS_RCC_DLL1CR_EN_Pos         (0U)
#define HPSYS_RCC_DLL1CR_EN_Msk         (0x1UL << HPSYS_RCC_DLL1CR_EN_Pos)
#define HPSYS_RCC_DLL1CR_EN             HPSYS_RCC_DLL1CR_EN_Msk
#define HPSYS_RCC_DLL1CR_SW_Pos         (1U)
#define HPSYS_RCC_DLL1CR_SW_Msk         (0x1UL << HPSYS_RCC_DLL1CR_SW_Pos)
#define HPSYS_RCC_DLL1CR_SW             HPSYS_RCC_DLL1CR_SW_Msk
#define HPSYS_RCC_DLL1CR_STG_Pos        (2U)
#define HPSYS_RCC_DLL1CR_STG_Msk        (0xFUL << HPSYS_RCC_DLL1CR_STG_Pos)
#define HPSYS_RCC_DLL1CR_STG            HPSYS_RCC_DLL1CR_STG_Msk
#define HPSYS_RCC_DLL1CR_XTALIN_EN_Pos  (6U)
#define HPSYS_RCC_DLL1CR_XTALIN_EN_Msk  (0x1UL << HPSYS_RCC_DLL1CR_XTALIN_EN_Pos)
#define HPSYS_RCC_DLL1CR_XTALIN_EN      HPSYS_RCC_DLL1CR_XTALIN_EN_Msk
#define HPSYS_RCC_DLL1CR_MODE48M_EN_Pos  (7U)
#define HPSYS_RCC_DLL1CR_MODE48M_EN_Msk  (0x1UL << HPSYS_RCC_DLL1CR_MODE48M_EN_Pos)
#define HPSYS_RCC_DLL1CR_MODE48M_EN     HPSYS_RCC_DLL1CR_MODE48M_EN_Msk
#define HPSYS_RCC_DLL1CR_LDO_VREF_Pos   (8U)
#define HPSYS_RCC_DLL1CR_LDO_VREF_Msk   (0xFUL << HPSYS_RCC_DLL1CR_LDO_VREF_Pos)
#define HPSYS_RCC_DLL1CR_LDO_VREF       HPSYS_RCC_DLL1CR_LDO_VREF_Msk
#define HPSYS_RCC_DLL1CR_IN_DIV2_EN_Pos  (12U)
#define HPSYS_RCC_DLL1CR_IN_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL1CR_IN_DIV2_EN_Pos)
#define HPSYS_RCC_DLL1CR_IN_DIV2_EN     HPSYS_RCC_DLL1CR_IN_DIV2_EN_Msk
#define HPSYS_RCC_DLL1CR_OUT_DIV2_EN_Pos  (13U)
#define HPSYS_RCC_DLL1CR_OUT_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL1CR_OUT_DIV2_EN_Pos)
#define HPSYS_RCC_DLL1CR_OUT_DIV2_EN    HPSYS_RCC_DLL1CR_OUT_DIV2_EN_Msk
#define HPSYS_RCC_DLL1CR_MCU_PRCHG_EN_Pos  (14U)
#define HPSYS_RCC_DLL1CR_MCU_PRCHG_EN_Msk  (0x1UL << HPSYS_RCC_DLL1CR_MCU_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL1CR_MCU_PRCHG_EN   HPSYS_RCC_DLL1CR_MCU_PRCHG_EN_Msk
#define HPSYS_RCC_DLL1CR_MCU_PRCHG_Pos  (15U)
#define HPSYS_RCC_DLL1CR_MCU_PRCHG_Msk  (0x1UL << HPSYS_RCC_DLL1CR_MCU_PRCHG_Pos)
#define HPSYS_RCC_DLL1CR_MCU_PRCHG      HPSYS_RCC_DLL1CR_MCU_PRCHG_Msk
#define HPSYS_RCC_DLL1CR_PRCHG_EN_Pos   (16U)
#define HPSYS_RCC_DLL1CR_PRCHG_EN_Msk   (0x1UL << HPSYS_RCC_DLL1CR_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL1CR_PRCHG_EN       HPSYS_RCC_DLL1CR_PRCHG_EN_Msk
#define HPSYS_RCC_DLL1CR_PRCHG_EXT_Pos  (17U)
#define HPSYS_RCC_DLL1CR_PRCHG_EXT_Msk  (0x1UL << HPSYS_RCC_DLL1CR_PRCHG_EXT_Pos)
#define HPSYS_RCC_DLL1CR_PRCHG_EXT      HPSYS_RCC_DLL1CR_PRCHG_EXT_Msk
#define HPSYS_RCC_DLL1CR_VST_SEL_Pos    (18U)
#define HPSYS_RCC_DLL1CR_VST_SEL_Msk    (0x1UL << HPSYS_RCC_DLL1CR_VST_SEL_Pos)
#define HPSYS_RCC_DLL1CR_VST_SEL        HPSYS_RCC_DLL1CR_VST_SEL_Msk
#define HPSYS_RCC_DLL1CR_BYPASS_Pos     (19U)
#define HPSYS_RCC_DLL1CR_BYPASS_Msk     (0x1UL << HPSYS_RCC_DLL1CR_BYPASS_Pos)
#define HPSYS_RCC_DLL1CR_BYPASS         HPSYS_RCC_DLL1CR_BYPASS_Msk
#define HPSYS_RCC_DLL1CR_DTEST_EN_Pos   (20U)
#define HPSYS_RCC_DLL1CR_DTEST_EN_Msk   (0x1UL << HPSYS_RCC_DLL1CR_DTEST_EN_Pos)
#define HPSYS_RCC_DLL1CR_DTEST_EN       HPSYS_RCC_DLL1CR_DTEST_EN_Msk
#define HPSYS_RCC_DLL1CR_DTEST_TR_Pos   (21U)
#define HPSYS_RCC_DLL1CR_DTEST_TR_Msk   (0xFUL << HPSYS_RCC_DLL1CR_DTEST_TR_Pos)
#define HPSYS_RCC_DLL1CR_DTEST_TR       HPSYS_RCC_DLL1CR_DTEST_TR_Msk
#define HPSYS_RCC_DLL1CR_PU_DLY_Pos     (25U)
#define HPSYS_RCC_DLL1CR_PU_DLY_Msk     (0x7UL << HPSYS_RCC_DLL1CR_PU_DLY_Pos)
#define HPSYS_RCC_DLL1CR_PU_DLY         HPSYS_RCC_DLL1CR_PU_DLY_Msk
#define HPSYS_RCC_DLL1CR_LOCK_DLY_Pos   (28U)
#define HPSYS_RCC_DLL1CR_LOCK_DLY_Msk   (0x7UL << HPSYS_RCC_DLL1CR_LOCK_DLY_Pos)
#define HPSYS_RCC_DLL1CR_LOCK_DLY       HPSYS_RCC_DLL1CR_LOCK_DLY_Msk
#define HPSYS_RCC_DLL1CR_READY_Pos      (31U)
#define HPSYS_RCC_DLL1CR_READY_Msk      (0x1UL << HPSYS_RCC_DLL1CR_READY_Pos)
#define HPSYS_RCC_DLL1CR_READY          HPSYS_RCC_DLL1CR_READY_Msk

/**************** Bit definition for HPSYS_RCC_DLL2CR register ****************/
#define HPSYS_RCC_DLL2CR_EN_Pos         (0U)
#define HPSYS_RCC_DLL2CR_EN_Msk         (0x1UL << HPSYS_RCC_DLL2CR_EN_Pos)
#define HPSYS_RCC_DLL2CR_EN             HPSYS_RCC_DLL2CR_EN_Msk
#define HPSYS_RCC_DLL2CR_SW_Pos         (1U)
#define HPSYS_RCC_DLL2CR_SW_Msk         (0x1UL << HPSYS_RCC_DLL2CR_SW_Pos)
#define HPSYS_RCC_DLL2CR_SW             HPSYS_RCC_DLL2CR_SW_Msk
#define HPSYS_RCC_DLL2CR_STG_Pos        (2U)
#define HPSYS_RCC_DLL2CR_STG_Msk        (0xFUL << HPSYS_RCC_DLL2CR_STG_Pos)
#define HPSYS_RCC_DLL2CR_STG            HPSYS_RCC_DLL2CR_STG_Msk
#define HPSYS_RCC_DLL2CR_XTALIN_EN_Pos  (6U)
#define HPSYS_RCC_DLL2CR_XTALIN_EN_Msk  (0x1UL << HPSYS_RCC_DLL2CR_XTALIN_EN_Pos)
#define HPSYS_RCC_DLL2CR_XTALIN_EN      HPSYS_RCC_DLL2CR_XTALIN_EN_Msk
#define HPSYS_RCC_DLL2CR_MODE48M_EN_Pos  (7U)
#define HPSYS_RCC_DLL2CR_MODE48M_EN_Msk  (0x1UL << HPSYS_RCC_DLL2CR_MODE48M_EN_Pos)
#define HPSYS_RCC_DLL2CR_MODE48M_EN     HPSYS_RCC_DLL2CR_MODE48M_EN_Msk
#define HPSYS_RCC_DLL2CR_LDO_VREF_Pos   (8U)
#define HPSYS_RCC_DLL2CR_LDO_VREF_Msk   (0xFUL << HPSYS_RCC_DLL2CR_LDO_VREF_Pos)
#define HPSYS_RCC_DLL2CR_LDO_VREF       HPSYS_RCC_DLL2CR_LDO_VREF_Msk
#define HPSYS_RCC_DLL2CR_IN_DIV2_EN_Pos  (12U)
#define HPSYS_RCC_DLL2CR_IN_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL2CR_IN_DIV2_EN_Pos)
#define HPSYS_RCC_DLL2CR_IN_DIV2_EN     HPSYS_RCC_DLL2CR_IN_DIV2_EN_Msk
#define HPSYS_RCC_DLL2CR_OUT_DIV2_EN_Pos  (13U)
#define HPSYS_RCC_DLL2CR_OUT_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL2CR_OUT_DIV2_EN_Pos)
#define HPSYS_RCC_DLL2CR_OUT_DIV2_EN    HPSYS_RCC_DLL2CR_OUT_DIV2_EN_Msk
#define HPSYS_RCC_DLL2CR_MCU_PRCHG_EN_Pos  (14U)
#define HPSYS_RCC_DLL2CR_MCU_PRCHG_EN_Msk  (0x1UL << HPSYS_RCC_DLL2CR_MCU_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL2CR_MCU_PRCHG_EN   HPSYS_RCC_DLL2CR_MCU_PRCHG_EN_Msk
#define HPSYS_RCC_DLL2CR_MCU_PRCHG_Pos  (15U)
#define HPSYS_RCC_DLL2CR_MCU_PRCHG_Msk  (0x1UL << HPSYS_RCC_DLL2CR_MCU_PRCHG_Pos)
#define HPSYS_RCC_DLL2CR_MCU_PRCHG      HPSYS_RCC_DLL2CR_MCU_PRCHG_Msk
#define HPSYS_RCC_DLL2CR_PRCHG_EN_Pos   (16U)
#define HPSYS_RCC_DLL2CR_PRCHG_EN_Msk   (0x1UL << HPSYS_RCC_DLL2CR_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL2CR_PRCHG_EN       HPSYS_RCC_DLL2CR_PRCHG_EN_Msk
#define HPSYS_RCC_DLL2CR_PRCHG_EXT_Pos  (17U)
#define HPSYS_RCC_DLL2CR_PRCHG_EXT_Msk  (0x1UL << HPSYS_RCC_DLL2CR_PRCHG_EXT_Pos)
#define HPSYS_RCC_DLL2CR_PRCHG_EXT      HPSYS_RCC_DLL2CR_PRCHG_EXT_Msk
#define HPSYS_RCC_DLL2CR_VST_SEL_Pos    (18U)
#define HPSYS_RCC_DLL2CR_VST_SEL_Msk    (0x1UL << HPSYS_RCC_DLL2CR_VST_SEL_Pos)
#define HPSYS_RCC_DLL2CR_VST_SEL        HPSYS_RCC_DLL2CR_VST_SEL_Msk
#define HPSYS_RCC_DLL2CR_BYPASS_Pos     (19U)
#define HPSYS_RCC_DLL2CR_BYPASS_Msk     (0x1UL << HPSYS_RCC_DLL2CR_BYPASS_Pos)
#define HPSYS_RCC_DLL2CR_BYPASS         HPSYS_RCC_DLL2CR_BYPASS_Msk
#define HPSYS_RCC_DLL2CR_DTEST_EN_Pos   (20U)
#define HPSYS_RCC_DLL2CR_DTEST_EN_Msk   (0x1UL << HPSYS_RCC_DLL2CR_DTEST_EN_Pos)
#define HPSYS_RCC_DLL2CR_DTEST_EN       HPSYS_RCC_DLL2CR_DTEST_EN_Msk
#define HPSYS_RCC_DLL2CR_DTEST_TR_Pos   (21U)
#define HPSYS_RCC_DLL2CR_DTEST_TR_Msk   (0xFUL << HPSYS_RCC_DLL2CR_DTEST_TR_Pos)
#define HPSYS_RCC_DLL2CR_DTEST_TR       HPSYS_RCC_DLL2CR_DTEST_TR_Msk
#define HPSYS_RCC_DLL2CR_PU_DLY_Pos     (25U)
#define HPSYS_RCC_DLL2CR_PU_DLY_Msk     (0x7UL << HPSYS_RCC_DLL2CR_PU_DLY_Pos)
#define HPSYS_RCC_DLL2CR_PU_DLY         HPSYS_RCC_DLL2CR_PU_DLY_Msk
#define HPSYS_RCC_DLL2CR_LOCK_DLY_Pos   (28U)
#define HPSYS_RCC_DLL2CR_LOCK_DLY_Msk   (0x7UL << HPSYS_RCC_DLL2CR_LOCK_DLY_Pos)
#define HPSYS_RCC_DLL2CR_LOCK_DLY       HPSYS_RCC_DLL2CR_LOCK_DLY_Msk
#define HPSYS_RCC_DLL2CR_READY_Pos      (31U)
#define HPSYS_RCC_DLL2CR_READY_Msk      (0x1UL << HPSYS_RCC_DLL2CR_READY_Pos)
#define HPSYS_RCC_DLL2CR_READY          HPSYS_RCC_DLL2CR_READY_Msk

/**************** Bit definition for HPSYS_RCC_DLL3CR register ****************/
#define HPSYS_RCC_DLL3CR_EN_Pos         (0U)
#define HPSYS_RCC_DLL3CR_EN_Msk         (0x1UL << HPSYS_RCC_DLL3CR_EN_Pos)
#define HPSYS_RCC_DLL3CR_EN             HPSYS_RCC_DLL3CR_EN_Msk
#define HPSYS_RCC_DLL3CR_SW_Pos         (1U)
#define HPSYS_RCC_DLL3CR_SW_Msk         (0x1UL << HPSYS_RCC_DLL3CR_SW_Pos)
#define HPSYS_RCC_DLL3CR_SW             HPSYS_RCC_DLL3CR_SW_Msk
#define HPSYS_RCC_DLL3CR_STG_Pos        (2U)
#define HPSYS_RCC_DLL3CR_STG_Msk        (0xFUL << HPSYS_RCC_DLL3CR_STG_Pos)
#define HPSYS_RCC_DLL3CR_STG            HPSYS_RCC_DLL3CR_STG_Msk
#define HPSYS_RCC_DLL3CR_XTALIN_EN_Pos  (6U)
#define HPSYS_RCC_DLL3CR_XTALIN_EN_Msk  (0x1UL << HPSYS_RCC_DLL3CR_XTALIN_EN_Pos)
#define HPSYS_RCC_DLL3CR_XTALIN_EN      HPSYS_RCC_DLL3CR_XTALIN_EN_Msk
#define HPSYS_RCC_DLL3CR_MODE48M_EN_Pos  (7U)
#define HPSYS_RCC_DLL3CR_MODE48M_EN_Msk  (0x1UL << HPSYS_RCC_DLL3CR_MODE48M_EN_Pos)
#define HPSYS_RCC_DLL3CR_MODE48M_EN     HPSYS_RCC_DLL3CR_MODE48M_EN_Msk
#define HPSYS_RCC_DLL3CR_LDO_VREF_Pos   (8U)
#define HPSYS_RCC_DLL3CR_LDO_VREF_Msk   (0xFUL << HPSYS_RCC_DLL3CR_LDO_VREF_Pos)
#define HPSYS_RCC_DLL3CR_LDO_VREF       HPSYS_RCC_DLL3CR_LDO_VREF_Msk
#define HPSYS_RCC_DLL3CR_IN_DIV2_EN_Pos  (12U)
#define HPSYS_RCC_DLL3CR_IN_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL3CR_IN_DIV2_EN_Pos)
#define HPSYS_RCC_DLL3CR_IN_DIV2_EN     HPSYS_RCC_DLL3CR_IN_DIV2_EN_Msk
#define HPSYS_RCC_DLL3CR_OUT_DIV2_EN_Pos  (13U)
#define HPSYS_RCC_DLL3CR_OUT_DIV2_EN_Msk  (0x1UL << HPSYS_RCC_DLL3CR_OUT_DIV2_EN_Pos)
#define HPSYS_RCC_DLL3CR_OUT_DIV2_EN    HPSYS_RCC_DLL3CR_OUT_DIV2_EN_Msk
#define HPSYS_RCC_DLL3CR_MCU_PRCHG_EN_Pos  (14U)
#define HPSYS_RCC_DLL3CR_MCU_PRCHG_EN_Msk  (0x1UL << HPSYS_RCC_DLL3CR_MCU_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL3CR_MCU_PRCHG_EN   HPSYS_RCC_DLL3CR_MCU_PRCHG_EN_Msk
#define HPSYS_RCC_DLL3CR_MCU_PRCHG_Pos  (15U)
#define HPSYS_RCC_DLL3CR_MCU_PRCHG_Msk  (0x1UL << HPSYS_RCC_DLL3CR_MCU_PRCHG_Pos)
#define HPSYS_RCC_DLL3CR_MCU_PRCHG      HPSYS_RCC_DLL3CR_MCU_PRCHG_Msk
#define HPSYS_RCC_DLL3CR_PRCHG_EN_Pos   (16U)
#define HPSYS_RCC_DLL3CR_PRCHG_EN_Msk   (0x1UL << HPSYS_RCC_DLL3CR_PRCHG_EN_Pos)
#define HPSYS_RCC_DLL3CR_PRCHG_EN       HPSYS_RCC_DLL3CR_PRCHG_EN_Msk
#define HPSYS_RCC_DLL3CR_PRCHG_EXT_Pos  (17U)
#define HPSYS_RCC_DLL3CR_PRCHG_EXT_Msk  (0x1UL << HPSYS_RCC_DLL3CR_PRCHG_EXT_Pos)
#define HPSYS_RCC_DLL3CR_PRCHG_EXT      HPSYS_RCC_DLL3CR_PRCHG_EXT_Msk
#define HPSYS_RCC_DLL3CR_VST_SEL_Pos    (18U)
#define HPSYS_RCC_DLL3CR_VST_SEL_Msk    (0x1UL << HPSYS_RCC_DLL3CR_VST_SEL_Pos)
#define HPSYS_RCC_DLL3CR_VST_SEL        HPSYS_RCC_DLL3CR_VST_SEL_Msk
#define HPSYS_RCC_DLL3CR_BYPASS_Pos     (19U)
#define HPSYS_RCC_DLL3CR_BYPASS_Msk     (0x1UL << HPSYS_RCC_DLL3CR_BYPASS_Pos)
#define HPSYS_RCC_DLL3CR_BYPASS         HPSYS_RCC_DLL3CR_BYPASS_Msk
#define HPSYS_RCC_DLL3CR_DTEST_EN_Pos   (20U)
#define HPSYS_RCC_DLL3CR_DTEST_EN_Msk   (0x1UL << HPSYS_RCC_DLL3CR_DTEST_EN_Pos)
#define HPSYS_RCC_DLL3CR_DTEST_EN       HPSYS_RCC_DLL3CR_DTEST_EN_Msk
#define HPSYS_RCC_DLL3CR_DTEST_TR_Pos   (21U)
#define HPSYS_RCC_DLL3CR_DTEST_TR_Msk   (0xFUL << HPSYS_RCC_DLL3CR_DTEST_TR_Pos)
#define HPSYS_RCC_DLL3CR_DTEST_TR       HPSYS_RCC_DLL3CR_DTEST_TR_Msk
#define HPSYS_RCC_DLL3CR_PU_DLY_Pos     (25U)
#define HPSYS_RCC_DLL3CR_PU_DLY_Msk     (0x7UL << HPSYS_RCC_DLL3CR_PU_DLY_Pos)
#define HPSYS_RCC_DLL3CR_PU_DLY         HPSYS_RCC_DLL3CR_PU_DLY_Msk
#define HPSYS_RCC_DLL3CR_LOCK_DLY_Pos   (28U)
#define HPSYS_RCC_DLL3CR_LOCK_DLY_Msk   (0x7UL << HPSYS_RCC_DLL3CR_LOCK_DLY_Pos)
#define HPSYS_RCC_DLL3CR_LOCK_DLY       HPSYS_RCC_DLL3CR_LOCK_DLY_Msk
#define HPSYS_RCC_DLL3CR_READY_Pos      (31U)
#define HPSYS_RCC_DLL3CR_READY_Msk      (0x1UL << HPSYS_RCC_DLL3CR_READY_Pos)
#define HPSYS_RCC_DLL3CR_READY          HPSYS_RCC_DLL3CR_READY_Msk

/*************** Bit definition for HPSYS_RCC_HRCCAL1 register ****************/
#define HPSYS_RCC_HRCCAL1_CAL_LENGTH_Pos  (0U)
#define HPSYS_RCC_HRCCAL1_CAL_LENGTH_Msk  (0xFFFFUL << HPSYS_RCC_HRCCAL1_CAL_LENGTH_Pos)
#define HPSYS_RCC_HRCCAL1_CAL_LENGTH    HPSYS_RCC_HRCCAL1_CAL_LENGTH_Msk
#define HPSYS_RCC_HRCCAL1_CAL_EN_Pos    (30U)
#define HPSYS_RCC_HRCCAL1_CAL_EN_Msk    (0x1UL << HPSYS_RCC_HRCCAL1_CAL_EN_Pos)
#define HPSYS_RCC_HRCCAL1_CAL_EN        HPSYS_RCC_HRCCAL1_CAL_EN_Msk
#define HPSYS_RCC_HRCCAL1_CAL_DONE_Pos  (31U)
#define HPSYS_RCC_HRCCAL1_CAL_DONE_Msk  (0x1UL << HPSYS_RCC_HRCCAL1_CAL_DONE_Pos)
#define HPSYS_RCC_HRCCAL1_CAL_DONE      HPSYS_RCC_HRCCAL1_CAL_DONE_Msk

/*************** Bit definition for HPSYS_RCC_HRCCAL2 register ****************/
#define HPSYS_RCC_HRCCAL2_HRC_CNT_Pos   (0U)
#define HPSYS_RCC_HRCCAL2_HRC_CNT_Msk   (0xFFFFUL << HPSYS_RCC_HRCCAL2_HRC_CNT_Pos)
#define HPSYS_RCC_HRCCAL2_HRC_CNT       HPSYS_RCC_HRCCAL2_HRC_CNT_Msk
#define HPSYS_RCC_HRCCAL2_HXT_CNT_Pos   (16U)
#define HPSYS_RCC_HRCCAL2_HXT_CNT_Msk   (0xFFFFUL << HPSYS_RCC_HRCCAL2_HXT_CNT_Pos)
#define HPSYS_RCC_HRCCAL2_HXT_CNT       HPSYS_RCC_HRCCAL2_HXT_CNT_Msk

/*************** Bit definition for HPSYS_RCC_DBGCLKR register ****************/
#define HPSYS_RCC_DBGCLKR_CLK_SEL_Pos   (0U)
#define HPSYS_RCC_DBGCLKR_CLK_SEL_Msk   (0x3UL << HPSYS_RCC_DBGCLKR_CLK_SEL_Pos)
#define HPSYS_RCC_DBGCLKR_CLK_SEL       HPSYS_RCC_DBGCLKR_CLK_SEL_Msk
#define HPSYS_RCC_DBGCLKR_CLK_EN_Pos    (2U)
#define HPSYS_RCC_DBGCLKR_CLK_EN_Msk    (0x1UL << HPSYS_RCC_DBGCLKR_CLK_EN_Pos)
#define HPSYS_RCC_DBGCLKR_CLK_EN        HPSYS_RCC_DBGCLKR_CLK_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_DBG_Pos  (4U)
#define HPSYS_RCC_DBGCLKR_DLL1_DBG_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_DBG_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_DBG      HPSYS_RCC_DBGCLKR_DLL1_DBG_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_LDO_EN_Pos  (5U)
#define HPSYS_RCC_DBGCLKR_DLL1_LDO_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_LDO_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_LDO_EN   HPSYS_RCC_DBGCLKR_DLL1_LDO_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_EN_Pos  (6U)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_OUT_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_EN   HPSYS_RCC_DBGCLKR_DLL1_OUT_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_LOOP_EN_Pos  (7U)
#define HPSYS_RCC_DBGCLKR_DLL1_LOOP_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_LOOP_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_LOOP_EN  HPSYS_RCC_DBGCLKR_DLL1_LOOP_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_RSTB_Pos  (8U)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_RSTB_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_OUT_RSTB_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_RSTB  HPSYS_RCC_DBGCLKR_DLL1_OUT_RSTB_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_CG_EN_Pos  (9U)
#define HPSYS_RCC_DBGCLKR_DLL1_CG_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL1_CG_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_CG_EN    HPSYS_RCC_DBGCLKR_DLL1_CG_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_STR_Pos  (10U)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_STR_Msk  (0x3UL << HPSYS_RCC_DBGCLKR_DLL1_OUT_STR_Pos)
#define HPSYS_RCC_DBGCLKR_DLL1_OUT_STR  HPSYS_RCC_DBGCLKR_DLL1_OUT_STR_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_DBG_Pos  (12U)
#define HPSYS_RCC_DBGCLKR_DLL2_DBG_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_DBG_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_DBG      HPSYS_RCC_DBGCLKR_DLL2_DBG_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_LDO_EN_Pos  (13U)
#define HPSYS_RCC_DBGCLKR_DLL2_LDO_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_LDO_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_LDO_EN   HPSYS_RCC_DBGCLKR_DLL2_LDO_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_EN_Pos  (14U)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_OUT_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_EN   HPSYS_RCC_DBGCLKR_DLL2_OUT_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_LOOP_EN_Pos  (15U)
#define HPSYS_RCC_DBGCLKR_DLL2_LOOP_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_LOOP_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_LOOP_EN  HPSYS_RCC_DBGCLKR_DLL2_LOOP_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_RSTB_Pos  (16U)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_RSTB_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_OUT_RSTB_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_RSTB  HPSYS_RCC_DBGCLKR_DLL2_OUT_RSTB_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_CG_EN_Pos  (17U)
#define HPSYS_RCC_DBGCLKR_DLL2_CG_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL2_CG_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_CG_EN    HPSYS_RCC_DBGCLKR_DLL2_CG_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_STR_Pos  (18U)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_STR_Msk  (0x3UL << HPSYS_RCC_DBGCLKR_DLL2_OUT_STR_Pos)
#define HPSYS_RCC_DBGCLKR_DLL2_OUT_STR  HPSYS_RCC_DBGCLKR_DLL2_OUT_STR_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_DBG_Pos  (20U)
#define HPSYS_RCC_DBGCLKR_DLL3_DBG_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_DBG_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_DBG      HPSYS_RCC_DBGCLKR_DLL3_DBG_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_LDO_EN_Pos  (21U)
#define HPSYS_RCC_DBGCLKR_DLL3_LDO_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_LDO_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_LDO_EN   HPSYS_RCC_DBGCLKR_DLL3_LDO_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_EN_Pos  (22U)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_OUT_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_EN   HPSYS_RCC_DBGCLKR_DLL3_OUT_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_LOOP_EN_Pos  (23U)
#define HPSYS_RCC_DBGCLKR_DLL3_LOOP_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_LOOP_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_LOOP_EN  HPSYS_RCC_DBGCLKR_DLL3_LOOP_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_RSTB_Pos  (24U)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_RSTB_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_OUT_RSTB_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_RSTB  HPSYS_RCC_DBGCLKR_DLL3_OUT_RSTB_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_CG_EN_Pos  (25U)
#define HPSYS_RCC_DBGCLKR_DLL3_CG_EN_Msk  (0x1UL << HPSYS_RCC_DBGCLKR_DLL3_CG_EN_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_CG_EN    HPSYS_RCC_DBGCLKR_DLL3_CG_EN_Msk
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_STR_Pos  (26U)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_STR_Msk  (0x3UL << HPSYS_RCC_DBGCLKR_DLL3_OUT_STR_Pos)
#define HPSYS_RCC_DBGCLKR_DLL3_OUT_STR  HPSYS_RCC_DBGCLKR_DLL3_OUT_STR_Msk

#endif
