/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hdl_channel.h"

g_hdl_channel_t g_hdl_channel;

HDL_CHANNEL_TYPE g_hdl_channel_type = CHANNEL_TYPE_UART;

bool hdl_channel_init(void)
{
    bool success = TRUE;
#ifdef HDL_VIA_UART
    g_hdl_channel_type = CHANNEL_TYPE_UART;
    g_hdl_channel.m_com_init              = hdl_uart_init;
    g_hdl_channel.m_com_get_byte          = hdl_uart_get_byte;
    g_hdl_channel.m_com_get_byte_status   = hdl_uart_get_byte_status;
    g_hdl_channel.m_com_get_byte_buffer   = hdl_uart_get_byte_buffer;
    g_hdl_channel.m_com_put_byte          = hdl_uart_put_byte;
    g_hdl_channel.m_com_put_byte_buffer   = hdl_uart_put_byte_buffer;
    g_hdl_channel.m_com_put_byte_complete = hdl_uart_put_byte_complete;
    g_hdl_channel.m_com_get_data16        = hdl_uart_get_data16;
    g_hdl_channel.m_com_put_data16        = hdl_uart_put_data16;
    g_hdl_channel.m_com_get_data32        = hdl_uart_get_data32;
    g_hdl_channel.m_com_put_data32        = hdl_uart_put_data32;
    g_hdl_channel.m_com_purge_fifo        = hdl_uart_purge_fifo;
    g_hdl_channel.m_com_set_baudrate      = hdl_uart_set_baudrate;
    g_hdl_channel.m_com_deinit            = hdl_uart_deinit;

#elif defined(HDL_VIA_SPI)
    g_hdl_channel_type = CHANNEL_TYPE_SPI;
    g_hdl_channel.m_com_init              = hdl_spi_master_init;
    g_hdl_channel.m_com_get_byte          = hdl_spi_master_get_byte;
    g_hdl_channel.m_com_get_byte_status   = hdl_spi_master_get_byte_status;
    g_hdl_channel.m_com_get_byte_buffer   = hdl_spi_master_get_byte_buffer;
    g_hdl_channel.m_com_put_byte          = hdl_spi_master_put_byte;
    g_hdl_channel.m_com_put_byte_buffer   = hdl_spi_master_put_byte_buffer;
    g_hdl_channel.m_com_put_byte_complete = hdl_spi_master_put_byte_complete;
    g_hdl_channel.m_com_get_data16        = hdl_spi_master_get_data16;
    g_hdl_channel.m_com_put_data16        = hdl_spi_master_put_data16;
    g_hdl_channel.m_com_get_data32        = hdl_spi_master_get_data32;
    g_hdl_channel.m_com_put_data32        = hdl_spi_master_put_data32;
    g_hdl_channel.m_com_purge_fifo        = hdl_spi_master_purge_fifo;
    g_hdl_channel.m_com_set_baudrate      = hdl_spi_master_set_baudrate;
    g_hdl_channel.m_com_deinit            = hdl_spi_master_deinit;

#elif defined(HDL_VIA_I2C)
    g_hdl_channel_type = CHANNEL_TYPE_I2C;
    g_hdl_channel.m_com_init              = hdl_i2c_master_init;
    g_hdl_channel.m_com_get_byte          = hdl_i2c_master_get_byte;
    g_hdl_channel.m_com_get_byte_status   = hdl_i2c_master_get_byte_status;
    g_hdl_channel.m_com_get_byte_buffer   = hdl_i2c_master_get_byte_buffer;
    g_hdl_channel.m_com_put_byte          = hdl_i2c_master_put_byte;
    g_hdl_channel.m_com_put_byte_buffer   = hdl_i2c_master_put_byte_buffer;
    g_hdl_channel.m_com_put_byte_complete = hdl_i2c_master_put_byte_complete;
    g_hdl_channel.m_com_get_data16        = hdl_i2c_master_get_data16;
    g_hdl_channel.m_com_put_data16        = hdl_i2c_master_put_data16;
    g_hdl_channel.m_com_get_data32        = hdl_i2c_master_get_data32;
    g_hdl_channel.m_com_put_data32        = hdl_i2c_master_put_data32;
    g_hdl_channel.m_com_purge_fifo        = hdl_i2c_master_purge_fifo;
    g_hdl_channel.m_com_set_baudrate      = hdl_i2c_master_set_baudrate;
    g_hdl_channel.m_com_deinit            = hdl_i2c_master_deinit;

#else
#error "No Define HDL_VIA_UART/SPI/I2C"
#endif

    HDL_LOGI("hdl_channel_type=%d", g_hdl_channel_type);
    success = HDL_COM_Init();
    return success;
}

uint8_t hdl_get_channel_type()
{
    return g_hdl_channel_type;
}


/*
void data_channel_put_byte(uint8 data)
{
    if (g_slave_channel == SLAVE_CHANNEL_UART) {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(DATA_CHANNEL);
        UART1_PutByte(data);
    } else if (g_slave_channel == SLAVE_CHANNEL_SPI) {

    } else if (g_slave_channel == SLAVE_CHANNEL_I2C) {

    } else if (g_slave_channel == SLAVE_CHANNEL_USB) {

    } else {

    }
}

void data_channel_put_byte_buffer(uint32* buf, uint32 length)
{
    if (g_slave_channel == SLAVE_CHANNEL_UART) {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(DATA_CHANNEL);
        UART1_PutByte_Buffer(buf, length);
    } else if (g_slave_channel == SLAVE_CHANNEL_SPI) {

    } else if (g_slave_channel == SLAVE_CHANNEL_I2C) {

    } else if (g_slave_channel == SLAVE_CHANNEL_USB) {

    } else {

    }
}

void data_channel_put_byte_complete(uint8 data)
{
    if (g_slave_channel == SLAVE_CHANNEL_UART) {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(DATA_CHANNEL);
        UART1_PutByte_Complete(data);
    } else if (g_slave_channel == SLAVE_CHANNEL_SPI) {

    } else if (g_slave_channel == SLAVE_CHANNEL_I2C) {

    } else if (g_slave_channel == SLAVE_CHANNEL_USB) {

    } else {

    }
}

void data_channel_put_data16(uint16 data)
{
    if (g_slave_channel == SLAVE_CHANNEL_UART) {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(DATA_CHANNEL);
        UART1_PutData16(data);
    } else if (g_slave_channel == SLAVE_CHANNEL_SPI) {

    } else if (g_slave_channel == SLAVE_CHANNEL_I2C) {

    } else if (g_slave_channel == SLAVE_CHANNEL_USB) {

    } else {

    }
}

void data_channel_put_data32(uint32 data)
{
    if (g_slave_channel == SLAVE_CHANNEL_UART) {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(DATA_CHANNEL);
        UART1_PutData32(data);
    } else if (g_slave_channel == SLAVE_CHANNEL_SPI) {

    } else if (g_slave_channel == SLAVE_CHANNEL_I2C) {

    } else if (g_slave_channel == SLAVE_CHANNEL_USB) {

    } else {

    }
}

void logging_channel_init(uint8 enable)
{
    if (TRUE == enable)
    {
        g_com_driver.m_com_put_byte          = data_channel_put_byte;
        g_com_driver.m_com_put_byte_buffer   = data_channel_put_byte_buffer;
        g_com_driver.m_com_put_byte_complete = data_channel_put_byte_complete;
        g_com_driver.m_com_put_data16        = data_channel_put_data16;
        g_com_driver.m_com_put_data32        = data_channel_put_data32;
    }
}

void logging_channel_put_byte_buffer(uint32* buf, uint32 length)
{_
    if (channel_is_usb_download_mode())
    {
        USB2COM_PutData32(CHANNEL_MARKER);
        USB2COM_PutData32(LOGGING_CHANNEL);
        USB2COM_PutData32(length);
        USB2COM_PutByte_Buffer(buf, length);
    }
    else
    {
        UART1_PutData32(CHANNEL_MARKER);
        UART1_PutData32(LOGGING_CHANNEL);
        UART1_PutData32(length);
        UART1_PutByte_Buffer(buf, length);
    }
}
*/
