/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_CONFIG_H__
#define __HDL_CONFIG_H__

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

#include "rtthread.h"

#ifdef __cplusplus
extern "C" {
#endif

#define GPS_IMG_PATH "0:/iGPSPORT/Vendor/gnss/"
// Turn on HDL Debug Log
// #define HDL_DEBUG

#ifndef TRUE
#define TRUE    1
#endif

#ifndef FALSE
#define FALSE   0
#endif

//#define CHIP_7686 // 7686=7682=5932
#define CHIP_3335
#define HDL_VIA_UART

// ToDo Porting: Please use your platform API to implement them.
#ifdef HDL_VIA_UART
#define HDL_UART_NUM    1

#elif defined(HDL_VIA_SPI)
#define SPIM_TEST_FREQUENCY     500000
#define SPIM_TEST_PORT          HAL_SPI_MASTER_0

#define SPIM_PIN_NUMBER_CS      HAL_GPIO_9
#define SPIM_PIN_FUNC_CS        HAL_GPIO_9_SPI_MST0_CS0
#define SPIM_PIN_NUMBER_CLK     HAL_GPIO_10
#define SPIM_PIN_FUNC_CLK       HAL_GPIO_10_SPI_MST0_SCK
#define SPIM_PIN_NUMBER_MOSI    HAL_GPIO_11
#define SPIM_PIN_FUNC_MOSI      HAL_GPIO_11_SPI_MST0_MOSI
#define SPIM_PIN_NUMBER_MISO    HAL_GPIO_8
#define SPIM_PIN_FUNC_MISO      HAL_GPIO_8_SPI_MST0_MISO

#elif defined(HDL_VIA_I2C)
#define I2C_MASTER_NUM          HAL_I2C_MASTER_0
#define I2C_MODE                HAL_I2C_IO_OPEN_DRAIN // first:HAL_I2C_IO_PUSH_PULL; second:HAL_I2C_IO_OPEN_DRAIN
#define I2C_SCL_PIN_USED        HAL_GPIO_6
#define I2C_SCL_MODE_USED       HAL_GPIO_6_I2C_MST0_SCL
#define I2C_SDA_PIN_USED        HAL_GPIO_5
#define I2C_SDA_MODE_USED       HAL_GPIO_5_I2C_MST0_SDA
#endif

// DA Flash Position
#define HDL_DA_FLASH_POS                0x00
#define HDL_DA_SIZE                     28672

#define HDL_DA_IMAGE_NAME               "slave_da_UART"

// Test Images Flash Position in Host & Target Device
#define HDL_PARTITION_IMAGE_HOST_FLASH_POS      0x00
#define HDL_PARTITION_IMAGE_SLAVE_FLASH_POS     0x08000000
#define HDL_PARTITION_IMAGE_SIZE                4096
#define HDL_PARTITION_IMAGE_NAME                "partition_table"

#define HDL_BL_IMAGE_HOST_FLASH_POS             0x00
#define HDL_BL_IMAGE_SLAVE_FLASH_POS            0x08003000
// #define HDL_BL_IMAGE_SIZE                       20480
#define HDL_BL_IMAGE_NAME                       "bootloader"

#define HDL_GNSS_DEMO_IMAGE_HOST_FLASH_POS      0x00
#define HDL_GNSS_DEMO_IMAGE_SLAVE_FLASH_POS     0x08013000
// #define HDL_GNSS_DEMO_IMAGE_SIZE                1514703
#define HDL_GNSS_DEMO_IMAGE_NAME                "gnss_demo"

#define HDL_GNSS_CONFIG_IMAGE_HOST_FLASH_POS    0x00
#define HDL_GNSS_CONFIG_IMAGE_SLAVE_FLASH_POS   0x083FF000
#define HDL_GNSS_CONFIG_IMAGE_SIZE              4096
#define HDL_GNSS_CONFIG_IMAGE_NAME              "gnss_config"

#ifdef __cplusplus
}
#endif

#endif //__HDL_CONFIG_H__

