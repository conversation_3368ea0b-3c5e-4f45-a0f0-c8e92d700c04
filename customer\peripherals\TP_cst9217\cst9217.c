/****************************************Copyright (c)****************************************
 * <PERSON><PERSON> Technology Co., Ltd
 *
 * --------------------------------------File Info--------------------------------------------
 * File name 	: cst9217.c
 * Created by 	: nullptr 2024-09-11
 * Descriptions	: 屏幕触摸驱动模块
 * -------------------------------------------------------------------------------------------
 * History 		:
 * v0.01	: 原始版本 
*********************************************************************************************/
#include <rtthread.h>
#include "board.h"
#include "cst9217.h"
#include "drv_touch.h"

/* Define -------------------------------------------------------------------*/
#define FT_MAX_WIDTH                   (466)
#define FT_MAX_HEIGHT                  (466)

static void cst9217_correct_pos(touch_msg_t ppos);
static struct touch_drivers cst9217_driver;

static void cst9217_correct_pos(touch_msg_t ppos)
{
    ppos->x = FT_MAX_WIDTH - ppos->x;
    if (ppos->x < 0)
    {
        ppos->x = 0;
    }

    ppos->y = FT_MAX_HEIGHT - ppos->y;
    if (ppos->y < 0)
    {
        ppos->y = 0;
    }


    return;
}

static rt_err_t read_point(touch_msg_t p_msg)
{
    int res;
    unsigned char out_val[3];
    uint8_t tp_num;
    rt_err_t err;

    LOG_D("cst9217 read_point");
    rt_touch_irq_pin_enable(1);

    res = 0;
    if (p_msg)
    {
        hyn_worker_process();

        // get x positon
        p_msg->x = p_g_chip_obj->point_info[0].x;
        LOG_D("outx %d,\n",p_msg->x);

        // get y position
        p_msg->y = p_g_chip_obj->point_info[0].y;
        LOG_D("outy %d,\n",p_msg->y);

        switch (p_g_chip_obj->point_info[0].evt)
        {
        case 0:
            p_msg->event = TOUCH_EVENT_UP;
            break;
        case 6:
            p_msg->event = TOUCH_EVENT_DOWN;
            break;
        case 7:
            p_msg->event = TOUCH_EVENT_MOVE;
            break;
        default:
            p_msg->event = TOUCH_EVENT_NONE;
            break;
        }

        cst9217_correct_pos(p_msg);


        LOG_I("TP event = %d, x = %d, y = %d\n",p_msg->event, p_msg->x, p_msg->y);

        return RT_EOK;
    }
    else
    {
        return RT_ERROR;
    }
}


//中断发生时：关中断 释放信号量，
//上层的drv_touch.c：tp_read_thread_entry  拿到信号量后，调用read_point，
// read_point：开中断 向上发送触摸事件和坐标
void cst9217_irq_handler(void *arg)
{
    rt_err_t ret = RT_ERROR;

    int value = (int)arg;
    LOG_D("cst9217 touch_irq_handler\n");

    //引脚拉低
    

    rt_touch_irq_pin_enable(0);

    p_g_chip_obj->status.int_trig = 1;

    ret = rt_sem_release(cst9217_driver.isr_sem);
    RT_ASSERT(RT_EOK == ret);
}


static rt_err_t init(void)
{
    LOG_D("cst9217 init");

    //中断
    rt_touch_irq_pin_attach(PIN_IRQ_MODE_FALLING, cst9217_irq_handler, NULL);
    rt_touch_irq_pin_enable(1); //Must enable before read I2C
    
    return RT_EOK;
}

static rt_err_t deinit(void)
{
    LOG_D("cst9217 deinit");
    //关中断 IC掉电
    rt_touch_irq_pin_enable(0);
    p_g_chip_obj->poweron_ic(false);
    return RT_EOK;
}

static rt_bool_t probe(void)
{
    int16_t ret;
    //初始化tp内部的结构体
    ret = hyn_tp_init();
    if(ret == -1)
    {
        return RT_FALSE;
    }
    p_g_chip_obj->reset_ic();
    // BSP_TP_Reset(0);
    // rt_thread_mdelay(100);
    // BSP_TP_Reset(1);

    LOG_I("cst9217 probe OK");

    return RT_TRUE;
}

static struct touch_ops ops =
{
    read_point,
    init,
    deinit
};

static int rt_cst9217_init(void)
{

    cst9217_driver.probe = probe;
    cst9217_driver.ops = &ops;
    cst9217_driver.user_data = RT_NULL;
    cst9217_driver.isr_sem = rt_sem_create("cst9217", 0, RT_IPC_FLAG_FIFO);

    rt_touch_drivers_register(&cst9217_driver);

    return 0;

}

// INIT_COMPONENT_EXPORT(rt_cst9217_init);
