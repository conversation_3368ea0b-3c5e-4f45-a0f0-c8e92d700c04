#include "ble_csi_layer.h"
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>

#include "bf0_ble_common.h"
#include "ble_lb55x.h"
#include "alipay_bind.h"

void csi_get_ble_addr(uint8_t *ble_addr)
{
    bd_addr_t addr;
    ble_get_public_address(&addr);
    // memcpy(ble_addr, addr.addr, BD_ADDR_LEN);
    for(uint8_t i=0; i<BD_ADDR_LEN; i++)
    {
        ble_addr[i] = addr.addr[BD_ADDR_LEN - i - 1];
    }
}

void csi_send_nus_data(uint8_t *data, uint16_t len)
{
    rt_hexdump("csi nus send:", 16, data, len);
    // lb55x_ble_nus_data_send(5, data, &len);
    lb55x_ble_nus_csi_data_send(data, &len);
}

extern ble_alipay_data_t g_alipay_data;
extern rt_mq_t g_alipay_message;

void csi_app_entry(void *param)
{
    uint8_t *data = 0;
    uint32_t len;
    ble_alipay_data_t alipay_data;
    while(1)
    {

        rt_mq_recv(g_alipay_message, &alipay_data, sizeof(alipay_data), RT_WAITING_FOREVER);
        // data = g_alipay_data.p_ble_alipay_data;
        // memcpy(data, g_alipay_data.a_ble_send_data, g_alipay_data.ble_send_data_len);
        // len = g_alipay_data.ble_alipay_data_len;

        data = alipay_data.p_ble_alipay_data;
        len = alipay_data.ble_alipay_data_len;
        rt_kprintf("rcv len %d\r\n", len);
        rt_hexdump("csi get:", 16, data, len);
        alipay_ble_recv_data_handle(data, len);
        rt_thread_mdelay(10);
    }
}

int ble_csi_init(void)
{
    rt_thread_t tid;
    tid = rt_thread_create("csi_app", csi_app_entry, NULL, 8192, RT_MAIN_THREAD_PRIORITY + 2, 10);
    rt_thread_startup(tid);
    return RT_EOK;
}
