/* 
Copyright (c) 2017 Silan MEMS. All Rights Reserved.
*/


#ifndef SL_Watch_ALGO_DRV__H__
#define SL_Watch_ALGO_DRV__H__


/*****************Silan_MEMS******************/
/*****************DATE:2016-11-29*************/

/*****************是否打开调试模式*************/
//#define SL_Watch_Algorithm_Debug_Mode_Open      1
/*********非手环工程师请勿操作该宏定义*********/


/********非手环工程师请勿操作该宏定义**********/
/*************计步算法使能宏定义***************/
#define SL_Pedo_Enable                         1
/**********抬手亮屏算法使能宏定义**************/
#define	SL_Turn_Wrist_Enable                   1
/************sc7a20驱动使能宏定义**************/
#define SL_SC7A20_Driver_Enable                1
/**********摇晃接听电话使能宏定义**************/
#define SL_Phone_Answer_Enable                 1

/**********抬手亮屏算法**********/
#ifdef SL_Turn_Wrist_Enable

/************根据客户需求进行设置*****************/
#define	SL_Light_Turn_On_Time_Enable 1
/*******0: 屏幕点亮时间不由加速度计决定,return one time****/
/*******1: 屏幕点亮时间由加速度计决定， return mul time****/
/*********非手环工程师请勿操作该宏定义************/

/******抬手亮屏后的延时监测时间设置**********/
#define	SL_Light_Turn_Down_Time_TH   (unsigned char)10
/******设置范围:10~100 单位:0.1s 相当于是1s~10s********/

#define  SL_Turn_Wrist_Last_Status  1
/*******抬手亮屏实现方式选择*********/
/*******0: 按照手势方式实现**********/
/*******1: 按照状态方式实现**********/

#define  SL_Turn_Wrist_Close_Status  1
/*******0: 按键按灭*********/
/*******1: 定时熄灭*********/

/*****************抬手亮屏算法初始化函数****************/
void SL_Turn_Wrist_Init(unsigned char *SL_Turn_Wrist_Para);
/************输入参数：*************/
/******SL_Turn_Wrist_Para[0]:加速度计贴片位置设置  0--7********/
/******SL_Turn_Wrist_Para[1]:抬手亮屏灵敏度设置    1--5********/
/******SL_Turn_Wrist_Para[2]:佩戴位置              0--1********/
/******SL_Turn_Wrist_Para[3]:屏幕点亮时间          10 ~ 100****/

/*************三轴加速度计贴片位置设置********************/
/***SL_Turn_Wrist_Para[0]：设定值的范围为: 0 ~ 7 *********/
/***请参考文档:Silan_MEMS_手环算法说明书_V0.3.pdf*********/

/***SL_Turn_Wrist_Para[1]:设定值的范围为: 1 ~ 5 **/
/*********默认值为：3   中等灵敏度*********/
/*********设定值为：1   最低灵敏度*********/
/*********默认值为：5   最高灵敏度*********/
/*********设定值为：1(最迟钝)~5(最灵敏)****/

/*****SL_Turn_Wrist_Para[2]: 手表佩戴位置  0:左手  1:右手***/
/*****SL_Turn_Wrist_Para[3]: 手表显示点亮时间   单位:0.1s***/
/*********设置范围: 10 ~ 100  相当于是  1.0s 到  10.0s******/

/***********返回参数情况说明*******/
/**Return: -1     备注说明:初始化失败，贴片位置参数错误配置超出范围，配置参数不符合要求**************/
/**Return: -2     备注说明:初始化失败，手环抬手亮屏灵敏度设置超出范围，配置参数不符合要求************/
/**Return: -3     备注说明:初始化失败，佩戴位置参数错误，配置参数不符合要求**************************/
/**Return: -4     备注说明:初始化失败，液晶屏点亮时间参数错误，配置参数不符合要求********************/
/**Return:  1     备注说明:初始化成功，配置参数符合要求**********************************************/
/**SL_Turn_Wrist_Para[3]设置的时间只有在SL_Light_Turn_On_Time_Enable定义为1的情况下才起作用*/
/**SL_Light_Turn_On_Time_Enable==0的时候，抬手了的状态只上报一次*****************/


/*****************抬手亮屏状态获取函数****************/
signed char SL_Turn_Wrist_Get_Status(void);
/***********输入参数：无***********/
/***********返回参数情况说明*******/
/**Return:  1     备注说明:屏幕需要点亮***************/
/**Return:  0     备注说明:屏幕不需要点亮*************/
/**Return: -1     备注说明:未初始化或初始化失败*******/

#endif

/**********摇晃接听电话使能宏定义**************/
#ifdef SL_Phone_Answer_Enable
unsigned char SL_Get_Phone_Answer_Status(unsigned char Sway_Degree,unsigned char Sway_Enable);
/***********输入参数   Sway_Degree：摇晃等级1--10***********/
/***********输入参数   Sway_Enable：0:disable 1:enalbe******/

/***摇晃等级：设置值越小，越容易触发接听动作**/
/***********返回参数情况说明*******/
/**Return:  1     备注说明:摇晃动作成立，接听电话**********/
/**Return:  0     备注说明:摇晃动作不成立，不接听电话******/
#endif

/**********走路跑步计步算法********************/
#ifdef SL_Pedo_Enable

/*******运动状态反馈的四种状态*******/
#define SL_STEP_STATIONARY           0/**静止或静坐**/
#define SL_STEP_SLOW_WALK            1/**慢走或散步**/
#define SL_STEP_WALK                 2/**正常走路**/
#define SL_STEP_RUN                  3/**跑步或剧烈运动**/

/******************复位计步值,初始化算法***************/
void SL_PEDO_InitAlgo(void);
/**********输入数据为：无******输出数据为：无**********/
/*使用方法: 系统时间到第二天时，请调用该函数清除计步值*/
/*说明:清除计步值后，对应的热量消耗及行走距离也会清零**/

/******************初始计步难度设置***************/
void SL_PEDO_Degree_Init(unsigned char SL_PEDO_Degree_Para);
/*********输入数据为：难度等级*****输出数据为：无*********/
/*********SL_PEDO_Degree_Para：2(易) ~ 8(难)**************/
/*********SL_PEDO_Degree_Para：默认值：3******************/
/*********不设置该值或设置值超出范围，其值默认为:3********/


/*************初始化个人参数*************/
/**参数初始化，用于热量，距离计算********/
void SL_Pedo_Person_Inf_Init(unsigned char *Person_Inf_Init);
/*********输入指针参数分别是:身高 体重 年龄 性别***举例:178,60,26 1*********/
/**身高范围:  30cm ~ 250cm  ***********/
/**体重范围:  10Kg ~ 200Kg  ***********/
/**年龄范围:  3岁  ~ 150岁  ***********/
/**性别范围:  0 ~ 1    0:女 1:男   ****/

/***************获取当天的当前计步值***************/
unsigned int SL_Pedo_GetStepCount(void);
/**************输出数据为：计步值(步)**************/

/*********************获取运动状态值**********************/
unsigned char SL_Pedo_GetMotion_Status(void);
/**********输入数据为：无*********************************/
/**********输出数据为：0 ~ 3 *****************************/
/**输出数据为：0(SL_STEP_STATIONARY) *静止或静坐**********/
/**输出数据为：1(SL_STEP_SLOW_WALK) **慢走或散步**********/
/**输出数据为：2(SL_STEP_WALK) *******正常走路************/
/**输出数据为：3(SL_STEP_RUN) ********跑步或剧烈运动******/

/**************获取截止当前当天的行走距离*****************/
unsigned int SL_Pedo_Step_Get_Distance(void);
/*******************输入数据为：无************************/
/*******************输出数据为：当天行走距离 *************/
/*******************单位:       分米(dm)******************/

/**************获取截止当前当天的热量消耗*****************/
unsigned int SL_Pedo_Step_Get_KCal(void);
/*******************输入数据为：无************************/
/*******************输出数据为：当天热量消耗值 ***********/
/*********单位: 大卡     1个单位=0.1大卡******************/
#endif

/****************SC7A20  手环算法执行函数***************/
/**说明:自行修改使用该函数，会发生错误，请联系厂家进行**/
/******说明:该函数需要定时执行,从而保证算法执行到位*****/
/***********sc7a20 驱动使能************/
/***************驱动内置***************/
#if SL_SC7A20_Driver_Enable

/***内置驱动客户，使用该函数***/
void SL_SC7A20_Watch_Algo_Exe(unsigned char SL_Int_Flag);
/*****传递参数：0 定时执行算法**默认执行算法***/
/*****传递参数：1 打开SC7A20中断功能，在判断计步值不变情况下，打开SC7A20中断*****/
/*****传递参数：2 关闭SC7A20中断功能，在GPIO中断服务函数中，关闭SC7A20的中断*****/

/****************计步算法判断时间:10S*******************/
/****************相当于多久没有计步就切换ODR************/
#define  SL_SC7A20_PEDO_IDLE_TIME         (unsigned char)7

/***************零点补偿设置*****************/
/**********16位带符号格式补偿数据************/
//#define  SL_ACCEL_ZERO_COMPENSATE         1
#ifdef   SL_ACCEL_ZERO_COMPENSATE
#define  SL_ACCEL_ZERO_COMPENSATE_X_VALUE 150
#define  SL_ACCEL_ZERO_COMPENSATE_Y_VALUE 250
#define  SL_ACCEL_ZERO_COMPENSATE_Z_VALUE 200
#endif

/*SDO 内部上拉是否使能*/
#define SL_SC7A20_SDO_PULL_DISABLE     0

/********客户需要进行的IIC接口封包函数****************/
extern unsigned char SL_MEMS_i2cWrite(unsigned char reg, unsigned char data);
extern unsigned char SL_MEMS_i2cRead(unsigned char reg, unsigned char len, unsigned char *buf);
/**MEMS_i2cWrite 函数中， Reg：寄存器地址   data：寄存器的配置值************/
/**MEMS_i2cWrite 函数 是一个单次写的函数*************************/
/***MEMS_i2cRead 函数中， Reg 同上，len:读取数据长度，buf:存储数据首地址（指针）**********/
/***MEMS_i2cRead 函数 是可以进行单次读或多次连续读取的函数*******/


/*************驱动初始化函数**************/
unsigned char SL_SC7A20_Driver_Init(void);
/*************输入参数:无*****************/
/*************返回数据情况如下************/
/**return :0x11  表示初始化是成功 OK********/
/**return :-1;   IIC 通信问题***************/



/*************中断配置参数初始化**********/
signed char SL_Sc7a20_Int_Config_Init(unsigned char *para);
/***输入数组参数分别是: 中断引脚选择  中断时的电平高低 中断时间阈值 中断幅度阈值***/
/********中断引脚选择: 1(中断1 PIN5)  2(中断2 PIN6) **************************/
/********进入中断时的电平: 0(中断触发时为低电平) 1(中断触发时为高电平)********/
/********中断判断时间阈值: N=(1~127)  N*1/ODR 单位s***************************/
/********中断判断幅度阈值: N=(1~127)  不同量程其含义不同**********************/
/*******返回参数说明*******************/
/**返回值:  1 中断参数初始化正确*******/
/**返回值: -1 中断引脚选择超出范围*****/
/**返回值: -2 中断高低电平选择错误*****/
/**返回值: -3 中断时间阈值值超出范围***/
/**返回值: -4 中断阈值超出范围*********/



/**********************************数据获取函数***********************************/
/**功能:驱动初始化后，需要对驱动情况进行测试，测试完成后，该函数可不调用**********/
unsigned char SL_SC7A20_GET_DATA(signed short *SL_SC7A20_Data_X_Buf,signed short *SL_SC7A20_Data_Y_Buf,signed short *SL_SC7A20_Data_Z_Buf);
/*************输入参数:XYZ 轴数据指针*******/
/*************返回数据情况如下**************/
/**return :FIFO 数据个数   各个指针中包含XYZ轴的数据****/
/******XYZ三轴数据格式：   16位带符号数据 signed short  量程为±4G  水平放置数据为 +8096****/
/******如需转换为重力单位(g)   请进行以下操作：********************/
/******返回数据值  *4.9/4096    **********/


#endif/****SL_SC7A20_Driver_Enable****/


#endif/****SL_Watch_ALGO_DRV__H__****/

