/*
 * LTRX140A.c
 *
 *  Created on: 2021年8月10日
 *      Author: qwkj
 */
#include "LTRX140A.h"
#include <stdlib.h>

#define DRV_DEBUG
#define LOG_TAG              "drv.als"
#include <drv_log.h>

static struct rt_i2c_bus_device *ltrx140a_bus= RT_NULL;

/**
 * @brief  ltrx140a的写寄存器函数
 * 
 * @param address 
 * @param tx_cmd 
 * @return uint8_t 
 */
static uint8_t ltrx140a_write_reg(uint8_t address, uint8_t tx_cmd)
{
    struct rt_i2c_msg msgs[1];
    uint8_t value[2];
    uint32_t res;

    if (ltrx140a_bus) {

        value[0] = address;
        value[1] = tx_cmd;

        msgs[0].addr = LTRX140A_ADDRESS;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = value;
        msgs[0].len = 2;

        if (rt_i2c_transfer(ltrx140a_bus, msgs, 1) == 1)
        {
            return RT_EOK;
        }
    }
    return RT_ERROR;
}

/**
 * @brief  ltrx140a的读取寄存器函数
 * 
 * @param address 
 * @param rx_buffer 
 * @param len 
 * @return uint8_t 
 */
static uint8_t ltrx140a_read_reg(uint8_t address, uint8_t *rx_buffer, const uint8_t len)
{
    struct rt_i2c_msg msgs[2];
    uint32_t res;

    if (ltrx140a_bus) {

        msgs[0].addr = LTRX140A_ADDRESS;
        msgs[0].flags = RT_I2C_WR;
        msgs[0].buf = &address;
        msgs[0].len = 1;

        msgs[1].addr = LTRX140A_ADDRESS;
        msgs[1].flags = RT_I2C_RD;
        msgs[1].buf = rx_buffer;
        msgs[1].len = len;

        if (rt_i2c_transfer(ltrx140a_bus, msgs, 2) == 2) {
            return RT_EOK;
        }
    }

    return RT_ERROR;
}

/**
 * @brief  ltrx140a的初始化函数
 * 
 * @param void
 * @return int32_t 
 */
int32_t LTRX140A_Init(void)
{
    /* get i2c bus device */
    ltrx140a_bus = rt_i2c_bus_device_find(LTRX140A_I2C_NAME);
    if (ltrx140a_bus)
    {
        LOG_D("Find i2c bus device %s\n", LTRX140A_I2C_NAME);
    }
    else
    {
        LOG_E("Can not found i2c bus %s, init fail\n", LTRX140A_I2C_NAME);
        return -1;
    }

    
    ltrx140a_write_reg(LTRX_RESET,0x01);
	ltrx140a_write_reg(LTRX_MAIN_CONTR,0x18);
	ltrx140a_write_reg(LTRX_CONFIG,0x00);
	ltrx140a_write_reg(LTRX_INT_TIME,0xa5);
	ltrx140a_write_reg(LTRX_CONTR,0x23);

    return 0;
}

/**
 * @brief  ltrx140a的id检查函数
 * 
 * @param void
 * @return uint8_t 
 */
uint8_t LTRX140A_CheckID(void)
{
	uint8_t whoami = 0;
	ltrx140a_read_reg(LTRX_PART_ID,&whoami,1);
    rt_kprintf("whoami = %d\n",whoami);
	if(LTRX140A_PART_ID == whoami)return true;
	return false;
}

/**
 * @brief  ltrx140a的读取结果函数
 * 
 * @param pdata
 * @return int 
 */
int32_t LTRX140A_ReadResult(uint16_t *pdata)
{
	static uint16_t als_last_value = 0;
	uint8_t ret = 0,tmp = 0;
	ltrx140a_read_reg(LTRX_STATUS,&tmp,1);
	if(tmp & 0x40){
		*pdata = als_last_value;
	}else{
		ret = ltrx140a_read_reg(LTRX_DATA_LSB,(uint8_t *)pdata,2);
		als_last_value = *pdata;
	}
	return ret;
}


/**
 * @brief  ltrx140a的模式切换函数
 * 
 * @param mode
 * @return int 
 */
int32_t LTRX140A_Switch_PowerMode(LTRX140A_PowerMode_e mode)
{
    if (mode == LTRX140A_NOMAL_MODE) // Resume mode
    {
        ltrx140a_write_reg(LTRX_RESET,0x01);
        ltrx140a_write_reg(LTRX_MAIN_CONTR,0x18);
        ltrx140a_write_reg(LTRX_CONTR,0x23);
    }
    else if (mode == LTRX140A_SLEEP_MODE) // Suspend mode
    {
        ltrx140a_write_reg(LTRX_RESET,0x00);
        ltrx140a_write_reg(LTRX_MAIN_CONTR,0x00);
        ltrx140a_write_reg(LTRX_CONTR,0x22);
    }
    else
    {
        return RT_ERROR; // Invalid mode
    }
    return RT_EOK;
}


#define LTRX140A_FUNC_TEST
#ifdef LTRX140A_FUNC_TEST
int cmd_als(int argc, char *argv[])
{
    uint16_t als_data = 0;
	int mode = 0;
    int ret = 0;
    if (argc >= 2)
    {
        if (strcmp(argv[1], "read") == 0)
        {

            LTRX140A_ReadResult(&als_data);
            LOG_D("als read data: %d\n", als_data);
        }
        else if (strcmp(argv[1], "check") == 0)
        {
            if (LTRX140A_CheckID())
            {
                LOG_D("als check id success\n");
            }
            else
            {
                LOG_E("als check id fail\n");
            }
        }
        else if (strcmp(argv[1], "init") == 0)
        {
            LTRX140A_Init();
        }
        else if (strcmp(argv[1], "power") == 0)
        {
            if (argc < 3)
            {
                LOG_E("Invalid mode\n");
                return 0;
            }

            mode = atoi(argv[2]);
            ret = LTRX140A_Switch_PowerMode(mode);
            if(ret){
                LOG_E("LTRX140A switch mode fail\n");
            }else{
                LOG_D("LTRX140A switch mode success\n");
            }
        }
        else
        {
            LOG_E("Invalid command\n");
        }
    }
    else
    {
        LOG_E("Invalid command\n");
    }
    return 0;
}
FINSH_FUNCTION_EXPORT(cmd_als, ltrx140a function test);
MSH_CMD_EXPORT(cmd_als, ltrx140a function test);
#endif

/**
 * 命令行进行测试应该发送这些
 * cmd_als init   // 初始化
 * cmd_als read  // 读取光感强度
 * cmd_als power 0  // 0:正常模式 1:休眠模式
 */

