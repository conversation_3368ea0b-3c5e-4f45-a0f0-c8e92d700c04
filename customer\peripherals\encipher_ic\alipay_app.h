#ifndef __ALIPAY_APP__
#define __ALIPAY_APP__

#include <stdio.h>
#include <rtthread.h>

#include "alipay_common.h"
#include "alipay_bind.h"
#include "aid_code.h"
#include "hs_private.h"
#include "./hal/se/se_v1/vendor_se_v1.h"

#define BIND_CODE_LEN 256
#define PAY_CODE_LEN 24
#define ID_LEN 128
#define NICK_NAME_LEN 128
#define AID_CODE_LEN 256
#define ALIPAY_DATA_BUFF_MAX_LEN 256

#define ALIPAY_DAYA_SET_S_LEN sizeof(alipay_data_set_s)

#define se_i2c_init() HS_Init()//根据实际加密ic的i2c初始化函数调用
#define se_i2c_deinit() HS_IIC_DeInit()//根据实际加密ic的i2c反初始化函数调用
#define se_enter_low_power() csi_enter_lpm(NULL);//根据实际加密ic的进低功耗函数调用
#define se_exit_low_power() csi_exit_lpm(NULL);//根据实际加密ic的退低功耗函数调用

typedef enum ALIPAY_STATUS
{
    STATUS_ALIPAY_OK = 0,
    STATUS_START,//状态开始时设置
    STATUS_PRE_INIT_FAIL,//支付宝初始化状态失败
    STATUS_BIND_CODE_GET_FAIL,//绑定码获取失败
    STATUS_BINDING,//正在绑定中
    STATUS_BIND_FAIL,//绑定失败
    STATUS_SHOW_BIND_PASS,//显示绑定成功
    STATUS_SHOW_BIND_CODE,//显示绑定码
    STATUS_SHOW_MENU_BAR,//显示菜单栏界面
    STATUS_SHOW_PAY_CODE,//显示付款码二维码
    STATUS_PAY_CODE_GET_FAIL,//付款码二维码获取失败
    STATUS_SHOW_AID_CODE,//显示帮助码二维码
    STATUS_AID_CODE_GET_FAIL,//帮助码获取失败
    STATUS_SETTING_ID_GET_PASS,//支付宝用户id获取成功
    STATUS_SETTING_ID_GET_FAIL,//支付宝用户id获取失败
    STATUS_SETTING_NICK_NAME_GET_PASS,//支付宝用户名称获取成功
    STATUS_SETTING_NICK_NAME_GET_FAIL,//支付宝用户名称获取失败
    STATUS_UNBIND_PASS,//解绑成功
    STATUS_UNBIND_FAIL,//解绑失败
    STATUS_OTHER_PAGE,//非正常的页面调取
    STATUS_SEND_DATAT_NULL,//传入的DATA数据为空值
    STATUS_NOT_USE,//未使用
    STATUS_ALIPAY_STATUS_OLD,//旧状态,用于判定是否需要发布数据
}ALIPAY_STATUS_E;

typedef enum ALIPAY_TYPE
{
    NOT_DO_TYPE = 0,//什么也不做
    BIND_STATUS_GET,//获取绑定状态
    UNBIND_STATUS_GET,//获取解除绑定状态
    PAY_RESULT_GET,//获取支付结果
    BIND_CODE_GET,//获取绑定码
    PAY_CODE_GET,//获取支付码
    AID_CODE_GET,//获取帮助码
    SETTING_ID_GET,//获取用户id
    SETTING_NICK_NAME_GET,//获取用户名称
}ALIPAY_TYPE_E;

typedef enum ALIPAY_CMD
{
    NOT_DO_CMD = 0,//什么也不做
    SE_IC_ENTER_LOW_POWER,//se ic进入低功耗模式(有些ic可能会把关掉打开电源类比于进出低功耗，这个注意区分)
    SE_IC_EXIT_LOW_POWER,//se ic退出低功耗模式
    SE_IC_POWER_OFF,//se ic关掉电源
    SE_IC_POWER_ON,//se ic打开电源
}ALIPAY_CMD_E;

typedef struct alipay_status_update
{
    int32_t fail_log;//用于存储支付宝函数的fail情况，例如像入参错误
    ALIPAY_STATUS_E ali_status;//支付宝的返回状态
}alipay_status_update_s;

typedef struct alipay_data_set
{
    alipay_status_update_s ali_stu_up;//用于状态的更新

    union
    {
        char bind_code[BIND_CODE_LEN];
        uint8_t pay_code[PAY_CODE_LEN];
        uint8_t id[ID_LEN];
        uint8_t nick_name[NICK_NAME_LEN];
        char aid_code[AID_CODE_LEN];
        char alipay_data_buff_max[ALIPAY_DATA_BUFF_MAX_LEN];//用于每次的清除
    }alipay_data_set_u;
}alipay_data_set_s;

extern ALIPAY_STATUS_E alipay_init();
extern ALIPAY_STATUS_E alipay_deinit();
extern ALIPAY_STATUS_E alipay_control(ALIPAY_CMD_E ali_cmd,alipay_data_set_s *ali_data);
extern void alipay_write(void);
extern ALIPAY_STATUS_E alipay_read(ALIPAY_TYPE_E ali_cmd,alipay_data_set_s *ali_data);

#endif