#include "mixo_hal.h"



int mixo_hal_init(void)
{
#ifdef CONFIG_MXS_USE_SPI
    mixo_hal_spi_init();
#endif
    return MIXO_HAL_OK;
}

void mixo_hal_deinit(void)
{
#ifdef CONFIG_MXS_USE_SPI
    mixo_hal_spi_deinit();
#endif
}

int mixo_hal_gpio_debug_set(uint8_t v)
{
    // TODO: Implement debug pin set
    return MIXO_HAL_OK;
}

int mixo_ui_display(int dx, int dy)
{
    // TODO: Implement UI display
    return MIXO_HAL_OK;
}

bool mixo_hal_is_btn_triggered()
{
    // TODO: Implement button pin inquiry
    return false;
}

bool mixo_hal_is_mot_triggered()
{
    // TODO: Implement motion pin inquiry
    return false;
}

void mixo_hal_led_control(bool on_off)
{
    // TODO: Implement LED control
}

/**
 * @brief get microsecond 
 *
 * @return uint32_t
 */
uint32_t mixo_hal_get_tick_ms(void)
{
    return rt_tick_get();
}

