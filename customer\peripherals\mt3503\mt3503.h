/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503.h
@Time    :   2025/02/13 20:35:06
@Brief   :   光旋钮驱动API模块
@Details :
*
************************************************************/

#ifndef __MT3503_H__
#define __MT3503_H__

#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <stdlib.h>
#include "sensor.h"
#include <stdint.h>
#include "bsp_board.h"
#include "mt3503_reg.h"
#include "mt3503_algo.h"
#include "qw_log.h"


#define MT3503_LOG_LVL               LOG_LVL_DBG
#define MT3503_LOG_TAG               "MT3503_API"

#if (MT3503_LOG_LVL >= LOG_LVL_DBG)
    #define MT3503_LOG_D(...)        QW_LOG_D(MT3503_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_LOG_D(...)
#endif

#if (MT3503_LOG_LVL >= LOG_LVL_INFO)
    #define MT3503_LOG_I(...)        QW_LOG_I(MT3503_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_LOG_I(...)
#endif

#if (MT3503_LOG_LVL >= LOG_LVL_WARNING)
    #define MT3503_LOG_W(...)        QW_LOG_W(MT3503_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_LOG_W(...)
#endif

#if (MT3503_LOG_LVL >= LOG_LVL_ERROR)
    #define MT3503_LOG_E(...)        QW_LOG_E(MT3503_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_LOG_E(...)
#endif

//达到阈值就触发一次用户回调（一步一触发）

#define MT3503_IIC_ADDR     MT3503_I2C_ADD_F
#define MT3503_ID           MT3503_PID1

typedef enum
{
    MT3503_CMD_START = 1,
    MT3503_CMD_STOP,
    MT3503_CMD_POWER_DOWN,
    MT3503_CMD_SLEEP_MOD_SET,
    MT3503_CMD_MOD_ENTER,
    MT3503_CMD_SET_RES_X,
    MT3503_CMD_READ_INFO,
    MT3503_CMD_GET_DATA,
    MT3503_CMD_GET_STATE,
    MT3503_CMD_CALIB_START,
    MT3503_CMD_CALIB_STOP,
}mt3503_cmd_type;

#ifdef __cplusplus
extern "C" {
#endif


int mt3503_init(void);
int mt3503_deinit(void);

/**
 *  MT3503_CMD_START 开始
    MT3503_CMD_STOP 停止
    MT3503_CMD_POWER_DOWN 掉电
    MT3503_CMD_SLEEP_MOD_SET 设置睡眠相关参数 参数类型@ mt3503_sleep_cfg_t
    MT3503_CMD_SET_RES_X 设置X轴分辨率 参数类型@ (uint16_t)
    MT3503_CMD_READ_INFO 获取X,Y值
    MT3503_CMD_GET_DATA 读取算法输出数据，旋转角度，速度信息
    MT3503_CMD_GET_STATE 读取工作状态
    MT3503_CMD_CALIB_START 开始校正
    MT3503_CMD_CALIB_STOP 结束校正
*/
int mt3503_control(mt3503_cmd_type cmd, void* argv);

#ifdef __cplusplus
}
#endif

#endif