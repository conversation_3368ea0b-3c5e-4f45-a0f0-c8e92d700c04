/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hdl_api.h"

#define HDL_HW_CODE_REG                 0x80000008
#define HDL_HW_SUBCODE_REG              0x8000000C

bool hdl_connect(const hdl_da_info_t *da_info, const hdl_connect_arg_t *connect_arg, hdl_da_report_t *da_report)
{
    if (da_info == NULL || connect_arg == NULL || da_report == NULL) {
        HDL_LOGE("hdl_connect fail, invalid parameter");
        return FALSE;
    }

    // Init Channel & HandShake with Chip BROM via Brom_StartCMD
    bool success = hdl_channel_init();
    HDL_Require_Noerr_Action(success, exit, "hdl_channel_init");
    hdl_brom_start();

    // Read Chip HW_Code/SubCode
    uint16_t hw_code = 0;
    uint16_t hw_subcode = 0;
    hdl_brom_read16(HDL_HW_CODE_REG, &hw_code);
    hdl_brom_read16(HDL_HW_SUBCODE_REG, &hw_subcode);
    HDL_LOGI("hdl_connect, hw_code 0x%04X 0x%04X\n", hw_code, hw_subcode);
    da_report->hw_code = hw_code;
    da_report->hw_subcode = hw_subcode;

    // Brom Disable WDT
    success = hdl_brom_disable_wdt();
    HDL_Require_Noerr_Action(success, exit, "hdl_brom_disable_wdt\n");

    // Brom Send DA
    success = hdl_brom_send_da(connect_arg, da_info->da_flash_addr, da_info->da_run_addr, da_info->da_len);
    HDL_Require_Noerr_Action(success, exit, "hdl_brom_send_da\n");

    // Brom Jump to DA
    success = hdl_brom_jump_da(da_info->da_run_addr);
    HDL_Require_Noerr_Action(success, exit, "hdl_brom_jump_da\n");

    // DA Sync
    success = hdl_sync_with_da(connect_arg->conn_enable_log, da_report);
    HDL_MAIN_LOG("DA_Report HW_Code=0x%04X-0x%04X, Flash ID=0x%04X 0x%04X 0x%04X, Flash Base Addr=0x%08X, Flash Size=0x%08X\n",
                 da_report->hw_code, da_report->hw_subcode, da_report->flash_manufacturer_id,
                 da_report->flash_id1, da_report->flash_id2, da_report->flash_base_addr,
                 da_report->flash_size);

exit:
    return success;
}

bool hdl_format(const hdl_format_arg_t *format_arg)
{
    if (format_arg == NULL || format_arg->format_size == 0 || format_arg->format_addr < 0x08000000) {
        HDL_LOGE("hdl_format fail, invalid parameter");
        return FALSE;
    }
    HDL_LOGI("hdl_format Addr=0x%08X Size=0x%08X", format_arg->format_addr, format_arg->format_size);
    return hdl_da_format(format_arg);
}

bool hdl_download(const hdl_download_arg_t *download_arg)
{
    bool success = FALSE;
    // Check Empty Parameter
    if (download_arg == NULL || download_arg->download_images == NULL) {
        HDL_LOGE("hdl_download fail, invalid parameter");
        return FALSE;
    }

    // Check Image Flash_Addr & Size
    hdl_image_t *image = download_arg->download_images;
    while (image != NULL) {
        HDL_LOGI("Download Image (%s) BL=%d 0x%08X->0x%08X %d",
                 image->image_name, image->image_is_bootloader, image->image_host_flash_addr,
                 image->image_slave_flash_addr, image->image_len);
        // image slave_flash_addr and image_len
        if (image->image_slave_flash_addr == 0 || image->image_len == 0) {
            HDL_LOGE("hdl_download fail, Image (%s) invalid flash_addr/len", image->image_name);
            return FALSE;
        }
        // image slave_flash_addr must be 4K align
        if (image->image_slave_flash_addr % 0x1000 != 0) {
            HDL_LOGE("hdl_download fail, Image (%s) Slave_Flash_Addr not 4K Align", image->image_name);
            return FALSE;
        }
        // image slave_flash_addr + size must < next_image slave_flash_addr
        hdl_image_t *next = image->next;
        if (next != NULL && image->image_slave_flash_addr + image->image_len >= next->image_slave_flash_addr) {
            HDL_LOGE("hdl_download fail, Image (%s) Size is overlap next Image (%s)",
                     image->image_name, next->image_name);
            return FALSE;
        }
        image = image->next;
    }

    // Download init callback
    if (download_arg->download_init_cb != NULL) {
        download_arg->download_init_cb(download_arg->download_init_cb_arg);
    }

    // Download Every Image
    image = download_arg->download_images;
    while (image != NULL) {
        HDL_LOGI("Start Download Image (%s)", image->image_name);
        success = hdl_da_download(image, download_arg->download_cb, download_arg->download_cb_arg);
        HDL_Require_Noerr_Action(success, exit, "hdl_da_download");
        image = image->next;
    }

exit:
    return success;
}

bool hdl_readback(const hdl_readback_arg_t *readback_arg)
{
    // Check Empty Parameter
    if (readback_arg == NULL || readback_arg->readback_flash_addr < 0x08000000
            || readback_arg->readback_total_len == 0) {
        HDL_LOGE("hdl_readback fail, invalid parameter");
        return FALSE;
    }
    return hdl_da_readback(readback_arg);
}

bool hdl_disconnect(HDL_DISCONNECT_TYPE disconnect_type)
{
    bool success = FALSE;
    HDL_LOGI("hdl_disconnect disconnect_type=%d", disconnect_type);
    if (disconnect_type == DISCONNECT_RESET) {
        success = hdl_da_enable_wdt(5);
    } else {
        bool power_off = (disconnect_type == DISCONNECT_POWER_OFF);
        success = hdl_da_finish(power_off);
    }
    HDL_COM_Deinit();
    return success;
}

