#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I ../../../../drivers/cmsis/sf32lb55x
#include "../../rtconfig.h"
#include "mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 FLASH_TABLE_START_ADDR FLASH_TABLE_SIZE  {    ; load region size_region, 
  ER_IROM1 FLASH_TABLE_START_ADDR FLASH_TABLE_SIZE  {  ; load address = execution address
   	.ANY (+RO)
  }
}
