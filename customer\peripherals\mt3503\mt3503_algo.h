/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503_algo.h
@Time    :   2025/02/11 17:43:15
@Brief   :   mt3503滤波防抖算法
@Details :
*
************************************************************/

#ifndef __MT3503_ALGO_H__
#define __MT3503_ALGO_H__
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "qw_log.h"
#define MT3503_ALGO_LOG_LVL               LOG_LVL_DBG
#define MT3503_ALGO_LOG_TAG               "MT3503_ALGO_API"
#if (MT3503_ALGO_LOG_LVL >= LOG_LVL_DBG)
    #define MT3503_ALGO_LOG_D(...)        QW_LOG_D(MT3503_ALGO_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_ALGO_LOG_D(...)
#endif
#if (MT3503_ALGO_LOG_LVL >= LOG_LVL_INFO)
    #define MT3503_ALGO_LOG_I(...)        QW_LOG_I(MT3503_ALGO_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_ALGO_LOG_I(...)
#endif
#if (MT3503_ALGO_LOG_LVL >= LOG_LVL_WARNING)
    #define MT3503_ALGO_LOG_W(...)        QW_LOG_W(MT3503_ALGO_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_ALGO_LOG_W(...)
#endif
#if (MT3503_ALGO_LOG_LVL >= LOG_LVL_ERROR)
    #define MT3503_ALGO_LOG_E(...)        QW_LOG_E(MT3503_ALGO_LOG_TAG, __VA_ARGS__)
#else
    #define MT3503_ALGO_LOG_E(...)
#endif

// 参考高驰的表优化按键模式参数：
//  1.高驰表旋转60度左右才发一个按键值。我们当前是15度，对应调整到30度
//  2.我们当前旋转6度就会发送第一个按键，太灵敏，容易误触，现在调整到15+15度

//#define USE_KNOB_TOUCH_MODE                   // 使用 TOUCH 模式
#define USE_KNOB_KEY_MODE                       // 使用 KEY 模式
// 旋转动作检测间隔时间，单位ms
#define MXS_MOTION_SAMPLE_INTERVAL         30   // 数值越大，灵敏度越低
#define MXS_MOTION_CALIB_TARGET          3600   // 校正转一圈360度对应的数值是3600，这样X增量10就对应角度的1度
// 旋转动作判定有效最小间隔时间，单位ms
#define MXS_MOTION_DETECTED_MIN_INTERVAL   90   // 数值越大，触发反应延时越大 释放-》转动          /MXS_MOTION_SAMPLE_INTERVAL * MXS_MOTION_X_DELTA_NOISE = 连续3次检测到转动大于1度，认为开始旋转
#define MXS_MOTION_DETECTED_MAX_INTERVAL  240   // 数值越大，解除反应延时越大 转动-》释放或者反向   /MXS_MOTION_SAMPLE_INTERVAL * MXS_MOTION_X_DELTA_NOISE = 连续8次检测到转动小于1度，认为停止旋转

#ifdef USE_NEW_ADJUST
// 旋钮x轴脉冲抑制阈值
// 旋钮x轴误触噪声阈值，单位十分之1度
#define MXS_MOTION_X_DELTA_NOISE           5   // 数值越大，灵敏度越低
// 旋钮x轴累计值，开始上报阈值，单位十分之1度
#define MXS_MOTION_X_START_COUNTER         20   // 数值越大，灵敏度越低，反应延时越大 > (MXS_MOTION_DETECTED_MIN_INTERVAL/MXS_MOTION_SAMPLE_INTERVAL)*MXS_MOTION_X_DELTA_NOISE
// 旋钮y轴，Y轴先起手大动作，当Y轴的移动增量绝对值大于X轴持续一定时间，可以认为Y轴先在动，忽略X轴。
// y轴按键误触限制参数，数值越大越防止按键动作误触发旋钮，但是降低灵敏度，单位十分之1度。
#define MXS_MOTION_Y_DELTA_MAX_LASTTIME    40   // 推荐略小于 MXS_MOTION_DETECTED_MIN_INTERVAL
#define MXS_MOTION_Y_DELTA_NOISE            3   // 数值越大，灵敏度越低,推荐略小于MXS_MOTION_X_DELTA_NOISE
/* 不同模式上报间隔差别大:
    TOUCH 要屏幕跟随，无极变化，上报频率需要大于等于24Hz小于等于屏的刷新频率(60HZ/30HZ)这里取25Hz,主要靠累计增量触发上报取5Hz,增量累计值取小一点（400）。
    KEY   配合马达，要有顿挫感，上报频率需要小于等于菜单内可视项数(一般5/4/3个)Hz，这里取4Hz,主要靠超时触发上报，这里取2Hz,增量累计值取大一点(800)。
*/
#ifdef USE_KNOB_TOUCH_MODE
// 用户回调触发上报最小/大时间间隔，单位ms
#define MXS_MOTION_DETECTED_MIN_GAP        40   // 上报限制最小间隔 MXS_MOTION_SAMPLE_INTERVAL*N >= MXS_MOTION_SAMPLE_INTERVAL
#define MXS_MOTION_DETECTED_MAX_GAP       200   // 上报限制最大间隔 MXS_MOTION_SAMPLE_INTERVAL*M >= MXS_MOTION_DETECTED_MIN_GAP and MXS_MOTION_DETECTED_MAX_INTERVAL
// 旋钮x轴累计值，再次上报间隔，单位十分之1度
#define MXS_MOTION_X_REPORT_COUNTER       60    // 数值越大，灵敏度越低，反应延时越大, 校正到3600后大致对应6度的旋转角度
#endif // USE_KNOB_TOUCH_MODE
#ifdef USE_KNOB_KEY_MODE
// 用户回调触发上报最小/大时间间隔，单位ms
#define MXS_MOTION_DETECTED_MIN_GAP       120   // 上报限制最小间隔 MXS_MOTION_SAMPLE_INTERVAL*N >= MXS_MOTION_SAMPLE_INTERVAL
#define MXS_MOTION_DETECTED_MAX_GAP       200   // 上报限制最大间隔 MXS_MOTION_SAMPLE_INTERVAL*M >= MXS_MOTION_DETECTED_MIN_GAP and MXS_MOTION_DETECTED_MAX_INTERVAL
// 旋钮x轴累计值，再次上报间隔，单位十分之1度
#define MXS_MOTION_X_REPORT_COUNTER       200   // 数值越大，灵敏度越低，反应延时越大
#endif // USE_KNOB_KEY_MODE
#else
// 旋钮x轴脉冲抑制阈值
// 旋钮x轴误触噪声阈值
#define MXS_MOTION_X_DELTA_NOISE           10   // 数值越大，灵敏度越低 旋转1度

// 旋钮x轴累计值，开始上报阈值
#define MXS_MOTION_X_START_COUNTER        150   // 数值越大，灵敏度越低，反应延时越大，大约旋转15度 > (MXS_MOTION_DETECTED_MIN_INTERVAL/MXS_MOTION_SAMPLE_INTERVAL)*MXS_MOTION_X_DELTA_NOISE

// 旋钮y轴，Y轴先起手大动作，当Y轴的移动增量绝对值大于X轴持续一定时间，可以认为Y轴先在动，忽略X轴。
// y轴按键误触限制参数，数值越大越防止按键动作误触发旋钮，但是降低灵敏度。
#define MXS_MOTION_Y_DELTA_MAX_LASTTIME    60   // 推荐略小于 MXS_MOTION_DETECTED_MIN_INTERVAL
#define MXS_MOTION_Y_DELTA_NOISE           10   // 数值越大，灵敏度越低,推荐略小于MXS_MOTION_X_DELTA_NOISE


/* 不同模式上报间隔差别大:
    TOUCH 要屏幕跟随，无极变化，上报频率需要大于等于24Hz小于等于屏的刷新频率(60HZ/30HZ)这里取25Hz,主要靠累计增量触发上报取5Hz,增量累计值取小一点（400）。
    KEY   配合马达，要有顿挫感，上报频率需要小于等于菜单内可视项数(一般5/4/3个)Hz，这里取4Hz,主要靠超时触发上报，这里取2Hz,增量累计值取大一点(800)。
*/

#ifdef USE_KNOB_TOUCH_MODE

// 用户回调触发上报最小/大时间间隔，单位ms
#define MXS_MOTION_DETECTED_MIN_GAP        40   // 上报限制最小间隔 MXS_MOTION_SAMPLE_INTERVAL*N >= MXS_MOTION_SAMPLE_INTERVAL
#define MXS_MOTION_DETECTED_MAX_GAP       200   // 上报限制最大间隔 MXS_MOTION_SAMPLE_INTERVAL*M >= MXS_MOTION_DETECTED_MIN_GAP and MXS_MOTION_DETECTED_MAX_INTERVAL

// 旋钮x轴累计值，再次上报间隔
#define MXS_MOTION_X_REPORT_COUNTER       300   // 数值越大，灵敏度越低，反应延时越大(约旋转30度)

#endif // USE_KNOB_TOUCH_MODE

#ifdef USE_KNOB_KEY_MODE

// 用户回调触发上报最小/大时间间隔，单位ms
#define MXS_MOTION_DETECTED_MIN_GAP       100   // 上报限制最小间隔 MXS_MOTION_SAMPLE_INTERVAL*N >= MXS_MOTION_SAMPLE_INTERVAL
#define MXS_MOTION_DETECTED_MAX_GAP       500   // 上报限制最大间隔 MXS_MOTION_SAMPLE_INTERVAL*M >= MXS_MOTION_DETECTED_MIN_GAP and MXS_MOTION_DETECTED_MAX_INTERVAL

// 旋钮x轴累计值，再次上报间隔
#define MXS_MOTION_X_REPORT_COUNTER       300   // 数值越大，灵敏度越低，反应延时越大(约旋转30度)

#endif // USE_KNOB_KEY_MODE

#endif

// motion状态信息 和 button_api.h 中 KNOB_MOTION 相同
typedef enum mxs_algo_motion_event {
    MXS_ALGO_MOTION_RELEASE = 0, // 按下释放或者旋转停止
    MXS_ALGO_MOTION_PRESS,       // 按下或者旋转
    MXS_ALGO_MOTION_ROTATE_CW,   // 顺时针旋转
    MXS_ALGO_MOTION_ROTATE_CCW,  // 顺时针逆时针旋转
} mxs_algo_motion_event_t;

//算法输出数据结构
struct mxs_algo_data
{
    rt_uint8_t event;        // 旋钮事件
    rt_uint8_t running;      // 当前是否旋转中
    uint16_t  scale_factor;  // 转一周量化的累计X增量值
    uint16_t  resx_clib;     // RES_X校正值
    uint16_t  last_time;     // 单次旋转持续时间
    rt_int16_t x;            // 当前X增量dx
    rt_int16_t y;            // 当前Y增量dy
    int32_t  speed;          // 速度量化
	int32_t  angle;          // 旋转角度（当前累计X增量对应的角度）
};
typedef struct mxs_algo_data *mxs_algo_data_t;

/* 光旋钮防抖防误触算法初始化 */
int mxs_algo_init(int (*callback)(int32_t keyval, int32_t key_event));

/* 光旋钮防抖防误触算法数据复位 */
int mxs_algo_reset(void);

/* 光旋钮防抖防误触算法*/
int mxs_algo_deal(int16_t dx, int16_t dy, uint32_t timestamp,
                  mxs_algo_motion_event_t *event, int32_t *value);
#endif // __MT3503_ALGO_H__
