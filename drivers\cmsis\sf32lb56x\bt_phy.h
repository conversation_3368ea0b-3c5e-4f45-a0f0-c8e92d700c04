#ifndef __BT_PHY_H
#define __BT_PHY_H

typedef struct
{
    __IO uint32_t RX_CTRL1;
    __IO uint32_t RX_CTRL2;
    __IO uint32_t NOTCH_CFG1;
    __IO uint32_t NOTCH_CFG2;
    __IO uint32_t NOTCH_CFG3;
    __IO uint32_t NOTCH_CFG4;
    __IO uint32_t NOTCH_CFG5;
    __IO uint32_t NOTCH_CFG6;
    __IO uint32_t NOTCH_CFG7;
    __IO uint32_t NOTCH_CFG8;
    __IO uint32_t NOTCH_CFG9;
    __IO uint32_t NOTCH_CFG10;
    __IO uint32_t NOTCH_CFG11;
    __IO uint32_t NOTCH_CFG12;
    __IO uint32_t NOTCH_CFG13;
    __IO uint32_t NOTCH_CFG14;
    __IO uint32_t INTERP_CFG1;
    __IO uint32_t TED_CFG1;
    __IO uint32_t MIXER_CFG1;
    __IO uint32_t PKTDET_CFG1;
    __IO uint32_t PKTDET_CFG2;
    __IO uint32_t DEMOD_CFG1;
    __IO uint32_t DEMOD_CFG2;
    __IO uint32_t DEMOD_CFG3;
    __IO uint32_t DEMOD_CFG4;
    __IO uint32_t DEMOD_CFG5;
    __IO uint32_t DEMOD_CFG6;
    __IO uint32_t DEMOD_CFG7;
    __IO uint32_t DEMOD_CFG8;
    __IO uint32_t DEMOD_CFG9;
    __IO uint32_t DEMOD_CFG10;
    __IO uint32_t DEMOD_CFG11;
    __IO uint32_t DEMOD_CFG12;
    __IO uint32_t DEMOD_CFG13;
    __IO uint32_t DEMOD_CFG14;
    __IO uint32_t RX_STATUS1;
    __IO uint32_t AGC_CTRL;
    __IO uint32_t AGC_CFG1;
    __IO uint32_t AGC_CFG2;
    __IO uint32_t AGC_CFG3;
    __IO uint32_t AGC_CFG4;
    __IO uint32_t AGC_CFG5;
    __IO uint32_t AGC_CFG6;
    __IO uint32_t AGC_CFG7;
    __IO uint32_t AGC_CFG8;
    __IO uint32_t AGC_CFG9;
    __IO uint32_t AGC_CFG10;
    __IO uint32_t AGC_CFG11;
    __IO uint32_t AGC_CFG12;
    __IO uint32_t RSSI_CFG1;
    __IO uint32_t AGC_STATUS;
    __IO uint32_t EDRSYNC_CFG1;
    __IO uint32_t EDRSYNC_CFG2;
    __IO uint32_t EDRDEMOD_CFG1;
    __IO uint32_t EDRDEMOD_CFG2;
    __IO uint32_t EDRTED_CFG1;
    __IO uint32_t TX_CTRL;
    __IO uint32_t TX_RCC_CTRL;
    __IO uint32_t TX_GAUSSFLT_CFG;
    __IO uint32_t TX_IF_MOD_CFG;
    __IO uint32_t TX_IF_MOD_CFG2;
    __IO uint32_t TX_IF_MOD_CFG3;
    __IO uint32_t TX_IF_MOD_CFG4;
    __IO uint32_t TX_IF_MOD_CFG5;
    __IO uint32_t TX_IF_MOD_CFG6;
    __IO uint32_t TX_IF_MOD_CFG7;
    __IO uint32_t TX_IF_MOD_CFG8;
    __IO uint32_t TX_HFP_CFG;
    __IO uint32_t TX_LFP_CFG;
    __IO uint32_t TX_PA_CFG;
    __IO uint32_t EDR_TMXBUF_GC_CFG1;
    __IO uint32_t EDR_TMXBUF_GC_CFG2;
    __IO uint32_t TX_DPSK_CFG1;
    __IO uint32_t TX_DPSK_CFG2;
    __IO uint32_t TX_DPSK_CFG3;
    __IO uint32_t TX_EDR_LPF_CFG;
    __IO uint32_t TX_DC_CAL_CFG0;
    __IO uint32_t TX_DC_CAL_CFG1;
    __IO uint32_t TX_DC_CAL_CFG2;
    __IO uint32_t TX_DC_CAL_CFG3;
    __IO uint32_t TX_DC_CAL_CFG4;
    __IO uint32_t LFP_MMDIV_CFG0;
    __IO uint32_t LFP_MMDIV_CFG1;
    __IO uint32_t LFP_MMDIV_CFG2;
    __IO uint32_t LFP_MMDIV_CFG3;
    __IO uint32_t LFP_MMDIV_CFG4;
    __IO uint32_t RX_HFP_CFG;
    __IO uint32_t LNA_GAIN_TBL0;
    __IO uint32_t LNA_GAIN_TBL1;
    __IO uint32_t LNA_GAIN_TBL2;
    __IO uint32_t LNA_GAIN_TBL3;
    __IO uint32_t LNA_GAIN_TBL4;
    __IO uint32_t LNA_GAIN_TBL5;
    __IO uint32_t DCCAL_MPT_CFG;
    __IO uint32_t DCCAL_RSLT1;
    __IO uint32_t DCCAL_RSLT2;
    __IO uint32_t RCOS_CFG0;
    __IO uint32_t RCOS_CFG1;
    __IO uint32_t RCOS_CFG2;
    __IO uint32_t RCOS_CFG3;
    __IO uint32_t RCOS_CFG4;
    __IO uint32_t RCOS_CFG5;
    __IO uint32_t RCOS_CFG6;
    __IO uint32_t RCOS_CFG7;
    __IO uint32_t RCOS_CFG8;
    __IO uint32_t RCOS_CFG9;
    __IO uint32_t RCOS_CFG10;
    __IO uint32_t RCOS_CFG11;
    __IO uint32_t RCOS_CFG12;
    __IO uint32_t RCOS_CFG13;
    __IO uint32_t RCOS_CFG14;
    __IO uint32_t RCOS_CFG15;
    __IO uint32_t RCOS_CFG16;
    __IO uint32_t RCOS_CFG17;
    __IO uint32_t RCOS_CFG18;
} BT_PHY_TypeDef;


/**************** Bit definition for BT_PHY_RX_CTRL1 register *****************/
#define BT_PHY_RX_CTRL1_ADC_SIGN_Pos    (0U)
#define BT_PHY_RX_CTRL1_ADC_SIGN_Msk    (0x1UL << BT_PHY_RX_CTRL1_ADC_SIGN_Pos)
#define BT_PHY_RX_CTRL1_ADC_SIGN        BT_PHY_RX_CTRL1_ADC_SIGN_Msk
#define BT_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Pos  (1U)
#define BT_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Msk  (0x1UL << BT_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Pos)
#define BT_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN  BT_PHY_RX_CTRL1_MIXER_IQ_SWAP_EN_Msk
#define BT_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Pos  (2U)
#define BT_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Msk  (0x1UL << BT_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Pos)
#define BT_PHY_RX_CTRL1_ADC_IQ_SWAP_EN  BT_PHY_RX_CTRL1_ADC_IQ_SWAP_EN_Msk
#define BT_PHY_RX_CTRL1_FORCE_RX_ON_Pos  (3U)
#define BT_PHY_RX_CTRL1_FORCE_RX_ON_Msk  (0x1UL << BT_PHY_RX_CTRL1_FORCE_RX_ON_Pos)
#define BT_PHY_RX_CTRL1_FORCE_RX_ON     BT_PHY_RX_CTRL1_FORCE_RX_ON_Msk
#define BT_PHY_RX_CTRL1_ADC_Q_EN_1_Pos  (4U)
#define BT_PHY_RX_CTRL1_ADC_Q_EN_1_Msk  (0x1UL << BT_PHY_RX_CTRL1_ADC_Q_EN_1_Pos)
#define BT_PHY_RX_CTRL1_ADC_Q_EN_1      BT_PHY_RX_CTRL1_ADC_Q_EN_1_Msk
#define BT_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Pos  (5U)
#define BT_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Msk  (0x1UL << BT_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Pos)
#define BT_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL  BT_PHY_RX_CTRL1_LPF1_SAMPLE_PHASE_SEL_Msk
#define BT_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Pos  (6U)
#define BT_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Msk  (0x1UL << BT_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Pos)
#define BT_PHY_RX_CTRL1_RSSI_SAMPLE_SEL  BT_PHY_RX_CTRL1_RSSI_SAMPLE_SEL_Msk
#define BT_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Pos  (7U)
#define BT_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Msk  (0x1UL << BT_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Pos)
#define BT_PHY_RX_CTRL1_PHY_RX_DUMP_EN  BT_PHY_RX_CTRL1_PHY_RX_DUMP_EN_Msk
#define BT_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Pos  (8U)
#define BT_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Msk  (0x3UL << BT_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Pos)
#define BT_PHY_RX_CTRL1_RX_DUMP_CLK_SEL  BT_PHY_RX_CTRL1_RX_DUMP_CLK_SEL_Msk
#define BT_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Pos  (10U)
#define BT_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Msk  (0x1FUL << BT_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Pos)
#define BT_PHY_RX_CTRL1_RX_DUMP_DATA_SEL  BT_PHY_RX_CTRL1_RX_DUMP_DATA_SEL_Msk
#define BT_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Pos  (15U)
#define BT_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Msk  (0x3UL << BT_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Pos)
#define BT_PHY_RX_CTRL1_RX_DBG_TRIG_SEL  BT_PHY_RX_CTRL1_RX_DBG_TRIG_SEL_Msk
#define BT_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Pos  (17U)
#define BT_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Msk  (0x1FUL << BT_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Pos)
#define BT_PHY_RX_CTRL1_RX_DBG_DATA_SEL  BT_PHY_RX_CTRL1_RX_DBG_DATA_SEL_Msk
#define BT_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Pos  (22U)
#define BT_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Msk  (0x1UL << BT_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Pos)
#define BT_PHY_RX_CTRL1_RX_LOOPBACK_MODE  BT_PHY_RX_CTRL1_RX_LOOPBACK_MODE_Msk
#define BT_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Pos  (23U)
#define BT_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Msk  (0x1UL << BT_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Pos)
#define BT_PHY_RX_CTRL1_RX_DUMP_Q_SEL   BT_PHY_RX_CTRL1_RX_DUMP_Q_SEL_Msk
#define BT_PHY_RX_CTRL1_FRC_ADC_24M_Pos  (24U)
#define BT_PHY_RX_CTRL1_FRC_ADC_24M_Msk  (0x1UL << BT_PHY_RX_CTRL1_FRC_ADC_24M_Pos)
#define BT_PHY_RX_CTRL1_FRC_ADC_24M     BT_PHY_RX_CTRL1_FRC_ADC_24M_Msk

/**************** Bit definition for BT_PHY_RX_CTRL2 register *****************/
#define BT_PHY_RX_CTRL2_FORCE_CLK_ON_AGC_Pos  (0U)
#define BT_PHY_RX_CTRL2_FORCE_CLK_ON_AGC_Msk  (0x1UL << BT_PHY_RX_CTRL2_FORCE_CLK_ON_AGC_Pos)
#define BT_PHY_RX_CTRL2_FORCE_CLK_ON_AGC  BT_PHY_RX_CTRL2_FORCE_CLK_ON_AGC_Msk
#define BT_PHY_RX_CTRL2_FORCE_RX_RESET_Pos  (1U)
#define BT_PHY_RX_CTRL2_FORCE_RX_RESET_Msk  (0x1UL << BT_PHY_RX_CTRL2_FORCE_RX_RESET_Pos)
#define BT_PHY_RX_CTRL2_FORCE_RX_RESET  BT_PHY_RX_CTRL2_FORCE_RX_RESET_Msk
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T0_Pos  (2U)
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T0_Msk  (0x1FUL << BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T0_Pos)
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T0  BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T0_Msk
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T1_Pos  (7U)
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T1_Msk  (0x1FUL << BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T1_Pos)
#define BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T1  BT_PHY_RX_CTRL2_CBPF_EDR_SWITCH_T1_Msk
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T0_Pos  (12U)
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T0_Msk  (0x1FUL << BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T0_Pos)
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T0  BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T0_Msk
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T1_Pos  (17U)
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T1_Msk  (0x1FUL << BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T1_Pos)
#define BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T1  BT_PHY_RX_CTRL2_ADC_EDR_SWITCH_T1_Msk
#define BT_PHY_RX_CTRL2_ADC_Q_EN_2_Pos  (22U)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_2_Msk  (0x1UL << BT_PHY_RX_CTRL2_ADC_Q_EN_2_Pos)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_2      BT_PHY_RX_CTRL2_ADC_Q_EN_2_Msk
#define BT_PHY_RX_CTRL2_ADC_Q_EN_C_Pos  (23U)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_C_Msk  (0x1UL << BT_PHY_RX_CTRL2_ADC_Q_EN_C_Pos)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_C      BT_PHY_RX_CTRL2_ADC_Q_EN_C_Msk
#define BT_PHY_RX_CTRL2_ADC_Q_EN_BR_Pos  (24U)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_BR_Msk  (0x1UL << BT_PHY_RX_CTRL2_ADC_Q_EN_BR_Pos)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_BR     BT_PHY_RX_CTRL2_ADC_Q_EN_BR_Msk
#define BT_PHY_RX_CTRL2_ADC_Q_EN_EDR_Pos  (25U)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_EDR_Msk  (0x1UL << BT_PHY_RX_CTRL2_ADC_Q_EN_EDR_Pos)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_EDR    BT_PHY_RX_CTRL2_ADC_Q_EN_EDR_Msk
#define BT_PHY_RX_CTRL2_ADC_Q_EN_FRC_EN_Pos  (26U)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_FRC_EN_Msk  (0x1UL << BT_PHY_RX_CTRL2_ADC_Q_EN_FRC_EN_Pos)
#define BT_PHY_RX_CTRL2_ADC_Q_EN_FRC_EN  BT_PHY_RX_CTRL2_ADC_Q_EN_FRC_EN_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG1 register ****************/
#define BT_PHY_NOTCH_CFG1_NOTCH_B0_1_Pos  (0U)
#define BT_PHY_NOTCH_CFG1_NOTCH_B0_1_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG1_NOTCH_B0_1_Pos)
#define BT_PHY_NOTCH_CFG1_NOTCH_B0_1    BT_PHY_NOTCH_CFG1_NOTCH_B0_1_Msk
#define BT_PHY_NOTCH_CFG1_NOTCH_B1_1_Pos  (14U)
#define BT_PHY_NOTCH_CFG1_NOTCH_B1_1_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG1_NOTCH_B1_1_Pos)
#define BT_PHY_NOTCH_CFG1_NOTCH_B1_1    BT_PHY_NOTCH_CFG1_NOTCH_B1_1_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG2 register ****************/
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_1_Pos  (0U)
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_1_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG2_NOTCH_A2_1_Pos)
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_1    BT_PHY_NOTCH_CFG2_NOTCH_A2_1_Msk
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_2_Pos  (14U)
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_2_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG2_NOTCH_A2_2_Pos)
#define BT_PHY_NOTCH_CFG2_NOTCH_A2_2    BT_PHY_NOTCH_CFG2_NOTCH_A2_2_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG3 register ****************/
#define BT_PHY_NOTCH_CFG3_NOTCH_B0_2_Pos  (0U)
#define BT_PHY_NOTCH_CFG3_NOTCH_B0_2_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG3_NOTCH_B0_2_Pos)
#define BT_PHY_NOTCH_CFG3_NOTCH_B0_2    BT_PHY_NOTCH_CFG3_NOTCH_B0_2_Msk
#define BT_PHY_NOTCH_CFG3_NOTCH_B1_2_Pos  (14U)
#define BT_PHY_NOTCH_CFG3_NOTCH_B1_2_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG3_NOTCH_B1_2_Pos)
#define BT_PHY_NOTCH_CFG3_NOTCH_B1_2    BT_PHY_NOTCH_CFG3_NOTCH_B1_2_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG4 register ****************/
#define BT_PHY_NOTCH_CFG4_NOTCH_B0_B_Pos  (0U)
#define BT_PHY_NOTCH_CFG4_NOTCH_B0_B_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG4_NOTCH_B0_B_Pos)
#define BT_PHY_NOTCH_CFG4_NOTCH_B0_B    BT_PHY_NOTCH_CFG4_NOTCH_B0_B_Msk
#define BT_PHY_NOTCH_CFG4_NOTCH_B1_B_Pos  (14U)
#define BT_PHY_NOTCH_CFG4_NOTCH_B1_B_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG4_NOTCH_B1_B_Pos)
#define BT_PHY_NOTCH_CFG4_NOTCH_B1_B    BT_PHY_NOTCH_CFG4_NOTCH_B1_B_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG5 register ****************/
#define BT_PHY_NOTCH_CFG5_NOTCH_A2_B_Pos  (0U)
#define BT_PHY_NOTCH_CFG5_NOTCH_A2_B_Msk  (0x3FFFUL << BT_PHY_NOTCH_CFG5_NOTCH_A2_B_Pos)
#define BT_PHY_NOTCH_CFG5_NOTCH_A2_B    BT_PHY_NOTCH_CFG5_NOTCH_A2_B_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG6 register ****************/
#define BT_PHY_NOTCH_CFG6_CHNL_NOTCH_EN0_1_Pos  (0U)
#define BT_PHY_NOTCH_CFG6_CHNL_NOTCH_EN0_1_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG6_CHNL_NOTCH_EN0_1_Pos)
#define BT_PHY_NOTCH_CFG6_CHNL_NOTCH_EN0_1  BT_PHY_NOTCH_CFG6_CHNL_NOTCH_EN0_1_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG7 register ****************/
#define BT_PHY_NOTCH_CFG7_CHNL_NOTCH_EN1_1_Pos  (0U)
#define BT_PHY_NOTCH_CFG7_CHNL_NOTCH_EN1_1_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG7_CHNL_NOTCH_EN1_1_Pos)
#define BT_PHY_NOTCH_CFG7_CHNL_NOTCH_EN1_1  BT_PHY_NOTCH_CFG7_CHNL_NOTCH_EN1_1_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG8 register ****************/
#define BT_PHY_NOTCH_CFG8_CHNL_NOTCH_EN2_1_Pos  (0U)
#define BT_PHY_NOTCH_CFG8_CHNL_NOTCH_EN2_1_Msk  (0x7FFFUL << BT_PHY_NOTCH_CFG8_CHNL_NOTCH_EN2_1_Pos)
#define BT_PHY_NOTCH_CFG8_CHNL_NOTCH_EN2_1  BT_PHY_NOTCH_CFG8_CHNL_NOTCH_EN2_1_Msk
#define BT_PHY_NOTCH_CFG8_NOTCH_RSSI_THD_1_Pos  (15U)
#define BT_PHY_NOTCH_CFG8_NOTCH_RSSI_THD_1_Msk  (0xFFUL << BT_PHY_NOTCH_CFG8_NOTCH_RSSI_THD_1_Pos)
#define BT_PHY_NOTCH_CFG8_NOTCH_RSSI_THD_1  BT_PHY_NOTCH_CFG8_NOTCH_RSSI_THD_1_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG9 register ****************/
#define BT_PHY_NOTCH_CFG9_CHNL_NOTCH_EN0_2_Pos  (0U)
#define BT_PHY_NOTCH_CFG9_CHNL_NOTCH_EN0_2_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG9_CHNL_NOTCH_EN0_2_Pos)
#define BT_PHY_NOTCH_CFG9_CHNL_NOTCH_EN0_2  BT_PHY_NOTCH_CFG9_CHNL_NOTCH_EN0_2_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG10 register ***************/
#define BT_PHY_NOTCH_CFG10_CHNL_NOTCH_EN1_2_Pos  (0U)
#define BT_PHY_NOTCH_CFG10_CHNL_NOTCH_EN1_2_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG10_CHNL_NOTCH_EN1_2_Pos)
#define BT_PHY_NOTCH_CFG10_CHNL_NOTCH_EN1_2  BT_PHY_NOTCH_CFG10_CHNL_NOTCH_EN1_2_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG11 register ***************/
#define BT_PHY_NOTCH_CFG11_CHNL_NOTCH_EN2_2_Pos  (0U)
#define BT_PHY_NOTCH_CFG11_CHNL_NOTCH_EN2_2_Msk  (0x7FFFUL << BT_PHY_NOTCH_CFG11_CHNL_NOTCH_EN2_2_Pos)
#define BT_PHY_NOTCH_CFG11_CHNL_NOTCH_EN2_2  BT_PHY_NOTCH_CFG11_CHNL_NOTCH_EN2_2_Msk
#define BT_PHY_NOTCH_CFG11_NOTCH_RSSI_THD_2_Pos  (15U)
#define BT_PHY_NOTCH_CFG11_NOTCH_RSSI_THD_2_Msk  (0xFFUL << BT_PHY_NOTCH_CFG11_NOTCH_RSSI_THD_2_Pos)
#define BT_PHY_NOTCH_CFG11_NOTCH_RSSI_THD_2  BT_PHY_NOTCH_CFG11_NOTCH_RSSI_THD_2_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG12 register ***************/
#define BT_PHY_NOTCH_CFG12_CHNL_NOTCH_EN0_B_Pos  (0U)
#define BT_PHY_NOTCH_CFG12_CHNL_NOTCH_EN0_B_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG12_CHNL_NOTCH_EN0_B_Pos)
#define BT_PHY_NOTCH_CFG12_CHNL_NOTCH_EN0_B  BT_PHY_NOTCH_CFG12_CHNL_NOTCH_EN0_B_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG13 register ***************/
#define BT_PHY_NOTCH_CFG13_CHNL_NOTCH_EN1_B_Pos  (0U)
#define BT_PHY_NOTCH_CFG13_CHNL_NOTCH_EN1_B_Msk  (0xFFFFFFFFUL << BT_PHY_NOTCH_CFG13_CHNL_NOTCH_EN1_B_Pos)
#define BT_PHY_NOTCH_CFG13_CHNL_NOTCH_EN1_B  BT_PHY_NOTCH_CFG13_CHNL_NOTCH_EN1_B_Msk

/*************** Bit definition for BT_PHY_NOTCH_CFG14 register ***************/
#define BT_PHY_NOTCH_CFG14_CHNL_NOTCH_EN2_B_Pos  (0U)
#define BT_PHY_NOTCH_CFG14_CHNL_NOTCH_EN2_B_Msk  (0x7FFFUL << BT_PHY_NOTCH_CFG14_CHNL_NOTCH_EN2_B_Pos)
#define BT_PHY_NOTCH_CFG14_CHNL_NOTCH_EN2_B  BT_PHY_NOTCH_CFG14_CHNL_NOTCH_EN2_B_Msk
#define BT_PHY_NOTCH_CFG14_NOTCH_RSSI_THD_B_Pos  (15U)
#define BT_PHY_NOTCH_CFG14_NOTCH_RSSI_THD_B_Msk  (0xFFUL << BT_PHY_NOTCH_CFG14_NOTCH_RSSI_THD_B_Pos)
#define BT_PHY_NOTCH_CFG14_NOTCH_RSSI_THD_B  BT_PHY_NOTCH_CFG14_NOTCH_RSSI_THD_B_Msk

/*************** Bit definition for BT_PHY_INTERP_CFG1 register ***************/
#define BT_PHY_INTERP_CFG1_TIMING_FACTOR_Pos  (0U)
#define BT_PHY_INTERP_CFG1_TIMING_FACTOR_Msk  (0x7FUL << BT_PHY_INTERP_CFG1_TIMING_FACTOR_Pos)
#define BT_PHY_INTERP_CFG1_TIMING_FACTOR  BT_PHY_INTERP_CFG1_TIMING_FACTOR_Msk
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_U_Pos  (7U)
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_U_Msk  (0x1UL << BT_PHY_INTERP_CFG1_INTERP_METHOD_U_Pos)
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_U  BT_PHY_INTERP_CFG1_INTERP_METHOD_U_Msk
#define BT_PHY_INTERP_CFG1_INTERP_EN_U_Pos  (8U)
#define BT_PHY_INTERP_CFG1_INTERP_EN_U_Msk  (0x1UL << BT_PHY_INTERP_CFG1_INTERP_EN_U_Pos)
#define BT_PHY_INTERP_CFG1_INTERP_EN_U  BT_PHY_INTERP_CFG1_INTERP_EN_U_Msk
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_B_Pos  (9U)
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_B_Msk  (0x1UL << BT_PHY_INTERP_CFG1_INTERP_METHOD_B_Pos)
#define BT_PHY_INTERP_CFG1_INTERP_METHOD_B  BT_PHY_INTERP_CFG1_INTERP_METHOD_B_Msk
#define BT_PHY_INTERP_CFG1_INTERP_EN_B_Pos  (10U)
#define BT_PHY_INTERP_CFG1_INTERP_EN_B_Msk  (0x1UL << BT_PHY_INTERP_CFG1_INTERP_EN_B_Pos)
#define BT_PHY_INTERP_CFG1_INTERP_EN_B  BT_PHY_INTERP_CFG1_INTERP_EN_B_Msk

/**************** Bit definition for BT_PHY_TED_CFG1 register *****************/
#define BT_PHY_TED_CFG1_TED_MU_P_U_Pos  (0U)
#define BT_PHY_TED_CFG1_TED_MU_P_U_Msk  (0xFUL << BT_PHY_TED_CFG1_TED_MU_P_U_Pos)
#define BT_PHY_TED_CFG1_TED_MU_P_U      BT_PHY_TED_CFG1_TED_MU_P_U_Msk
#define BT_PHY_TED_CFG1_TED_MU_F_U_Pos  (4U)
#define BT_PHY_TED_CFG1_TED_MU_F_U_Msk  (0xFUL << BT_PHY_TED_CFG1_TED_MU_F_U_Pos)
#define BT_PHY_TED_CFG1_TED_MU_F_U      BT_PHY_TED_CFG1_TED_MU_F_U_Msk
#define BT_PHY_TED_CFG1_TED_MU_P_BR_Pos  (8U)
#define BT_PHY_TED_CFG1_TED_MU_P_BR_Msk  (0xFUL << BT_PHY_TED_CFG1_TED_MU_P_BR_Pos)
#define BT_PHY_TED_CFG1_TED_MU_P_BR     BT_PHY_TED_CFG1_TED_MU_P_BR_Msk
#define BT_PHY_TED_CFG1_TED_MU_F_BR_Pos  (12U)
#define BT_PHY_TED_CFG1_TED_MU_F_BR_Msk  (0xFUL << BT_PHY_TED_CFG1_TED_MU_F_BR_Pos)
#define BT_PHY_TED_CFG1_TED_MU_F_BR     BT_PHY_TED_CFG1_TED_MU_F_BR_Msk

/*************** Bit definition for BT_PHY_MIXER_CFG1 register ****************/
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Pos  (0U)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Msk  (0x3FFUL << BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Pos)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_1  BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_1_Msk
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Pos  (10U)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Msk  (0x3FFUL << BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Pos)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_2  BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_2_Msk
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_BT_Pos  (20U)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_BT_Msk  (0x3FFUL << BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_BT_Pos)
#define BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_BT  BT_PHY_MIXER_CFG1_RX_MIXER_PHASE_BT_Msk

/*************** Bit definition for BT_PHY_PKTDET_CFG1 register ***************/
#define BT_PHY_PKTDET_CFG1_BLE_PKTDET_THD_Pos  (0U)
#define BT_PHY_PKTDET_CFG1_BLE_PKTDET_THD_Msk  (0x3FFFUL << BT_PHY_PKTDET_CFG1_BLE_PKTDET_THD_Pos)
#define BT_PHY_PKTDET_CFG1_BLE_PKTDET_THD  BT_PHY_PKTDET_CFG1_BLE_PKTDET_THD_Msk
#define BT_PHY_PKTDET_CFG1_BLE_PKT_CNT_THD_Pos  (14U)
#define BT_PHY_PKTDET_CFG1_BLE_PKT_CNT_THD_Msk  (0x1FFUL << BT_PHY_PKTDET_CFG1_BLE_PKT_CNT_THD_Pos)
#define BT_PHY_PKTDET_CFG1_BLE_PKT_CNT_THD  BT_PHY_PKTDET_CFG1_BLE_PKT_CNT_THD_Msk
#define BT_PHY_PKTDET_CFG1_BLE_HARD_CORR_THD_Pos  (23U)
#define BT_PHY_PKTDET_CFG1_BLE_HARD_CORR_THD_Msk  (0x1FUL << BT_PHY_PKTDET_CFG1_BLE_HARD_CORR_THD_Pos)
#define BT_PHY_PKTDET_CFG1_BLE_HARD_CORR_THD  BT_PHY_PKTDET_CFG1_BLE_HARD_CORR_THD_Msk

/*************** Bit definition for BT_PHY_PKTDET_CFG2 register ***************/
#define BT_PHY_PKTDET_CFG2_BR_PKTDET_THD_Pos  (0U)
#define BT_PHY_PKTDET_CFG2_BR_PKTDET_THD_Msk  (0x3FFFUL << BT_PHY_PKTDET_CFG2_BR_PKTDET_THD_Pos)
#define BT_PHY_PKTDET_CFG2_BR_PKTDET_THD  BT_PHY_PKTDET_CFG2_BR_PKTDET_THD_Msk
#define BT_PHY_PKTDET_CFG2_BR_PKT_CNT_THD_Pos  (14U)
#define BT_PHY_PKTDET_CFG2_BR_PKT_CNT_THD_Msk  (0x1FFUL << BT_PHY_PKTDET_CFG2_BR_PKT_CNT_THD_Pos)
#define BT_PHY_PKTDET_CFG2_BR_PKT_CNT_THD  BT_PHY_PKTDET_CFG2_BR_PKT_CNT_THD_Msk
#define BT_PHY_PKTDET_CFG2_BR_HARD_CORR_THD_Pos  (23U)
#define BT_PHY_PKTDET_CFG2_BR_HARD_CORR_THD_Msk  (0x1FUL << BT_PHY_PKTDET_CFG2_BR_HARD_CORR_THD_Pos)
#define BT_PHY_PKTDET_CFG2_BR_HARD_CORR_THD  BT_PHY_PKTDET_CFG2_BR_HARD_CORR_THD_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG1 register ****************/
#define BT_PHY_DEMOD_CFG1_BLE_MU_ERR_Pos  (0U)
#define BT_PHY_DEMOD_CFG1_BLE_MU_ERR_Msk  (0x3FFUL << BT_PHY_DEMOD_CFG1_BLE_MU_ERR_Pos)
#define BT_PHY_DEMOD_CFG1_BLE_MU_ERR    BT_PHY_DEMOD_CFG1_BLE_MU_ERR_Msk
#define BT_PHY_DEMOD_CFG1_BLE_MU_DC_Pos  (10U)
#define BT_PHY_DEMOD_CFG1_BLE_MU_DC_Msk  (0x3FFUL << BT_PHY_DEMOD_CFG1_BLE_MU_DC_Pos)
#define BT_PHY_DEMOD_CFG1_BLE_MU_DC     BT_PHY_DEMOD_CFG1_BLE_MU_DC_Msk
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_G_Pos  (20U)
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_G_Msk  (0x7FFUL << BT_PHY_DEMOD_CFG1_BLE_DEMOD_G_Pos)
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_G   BT_PHY_DEMOD_CFG1_BLE_DEMOD_G_Msk
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_METHOD_Pos  (31U)
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_METHOD_Msk  (0x1UL << BT_PHY_DEMOD_CFG1_BLE_DEMOD_METHOD_Pos)
#define BT_PHY_DEMOD_CFG1_BLE_DEMOD_METHOD  BT_PHY_DEMOD_CFG1_BLE_DEMOD_METHOD_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG2 register ****************/
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_0_Pos  (0U)
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_0_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_0_Pos)
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_0  BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_0_Msk
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_1_Pos  (12U)
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_1_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_1_Pos)
#define BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_1  BT_PHY_DEMOD_CFG2_BLE_DEMOD_PHASE_1_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG3 register ****************/
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_2_Pos  (0U)
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_2_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_2_Pos)
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_2  BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_2_Msk
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_3_Pos  (12U)
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_3_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_3_Pos)
#define BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_3  BT_PHY_DEMOD_CFG3_BLE_DEMOD_PHASE_3_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG4 register ****************/
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_4_Pos  (0U)
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_4_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_4_Pos)
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_4  BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_4_Msk
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_5_Pos  (12U)
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_5_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_5_Pos)
#define BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_5  BT_PHY_DEMOD_CFG4_BLE_DEMOD_PHASE_5_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG5 register ****************/
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_6_Pos  (0U)
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_6_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_6_Pos)
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_6  BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_6_Msk
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_7_Pos  (12U)
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_7_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_7_Pos)
#define BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_7  BT_PHY_DEMOD_CFG5_BLE_DEMOD_PHASE_7_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG6 register ****************/
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_0_Pos  (0U)
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_0_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_0_Pos)
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_0  BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_0_Msk
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_1_Pos  (12U)
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_1_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_1_Pos)
#define BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_1  BT_PHY_DEMOD_CFG6_BLE_DEMOD_PHASE_IDEAL_1_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG7 register ****************/
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_2_Pos  (0U)
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_2_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_2_Pos)
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_2  BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_2_Msk
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_3_Pos  (12U)
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_3_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_3_Pos)
#define BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_3  BT_PHY_DEMOD_CFG7_BLE_DEMOD_PHASE_IDEAL_3_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG8 register ****************/
#define BT_PHY_DEMOD_CFG8_BR_MU_ERR_Pos  (0U)
#define BT_PHY_DEMOD_CFG8_BR_MU_ERR_Msk  (0x3FFUL << BT_PHY_DEMOD_CFG8_BR_MU_ERR_Pos)
#define BT_PHY_DEMOD_CFG8_BR_MU_ERR     BT_PHY_DEMOD_CFG8_BR_MU_ERR_Msk
#define BT_PHY_DEMOD_CFG8_BR_MU_DC_Pos  (10U)
#define BT_PHY_DEMOD_CFG8_BR_MU_DC_Msk  (0x3FFUL << BT_PHY_DEMOD_CFG8_BR_MU_DC_Pos)
#define BT_PHY_DEMOD_CFG8_BR_MU_DC      BT_PHY_DEMOD_CFG8_BR_MU_DC_Msk
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_G_Pos  (20U)
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_G_Msk  (0x7FFUL << BT_PHY_DEMOD_CFG8_BR_DEMOD_G_Pos)
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_G    BT_PHY_DEMOD_CFG8_BR_DEMOD_G_Msk
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_METHOD_Pos  (31U)
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_METHOD_Msk  (0x1UL << BT_PHY_DEMOD_CFG8_BR_DEMOD_METHOD_Pos)
#define BT_PHY_DEMOD_CFG8_BR_DEMOD_METHOD  BT_PHY_DEMOD_CFG8_BR_DEMOD_METHOD_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG9 register ****************/
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_0_Pos  (0U)
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_0_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_0_Pos)
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_0  BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_0_Msk
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_1_Pos  (12U)
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_1_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_1_Pos)
#define BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_1  BT_PHY_DEMOD_CFG9_BR_DEMOD_PHASE_1_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG10 register ***************/
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_2_Pos  (0U)
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_2_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_2_Pos)
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_2  BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_2_Msk
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_3_Pos  (12U)
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_3_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_3_Pos)
#define BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_3  BT_PHY_DEMOD_CFG10_BR_DEMOD_PHASE_3_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG11 register ***************/
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_4_Pos  (0U)
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_4_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_4_Pos)
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_4  BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_4_Msk
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_5_Pos  (12U)
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_5_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_5_Pos)
#define BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_5  BT_PHY_DEMOD_CFG11_BR_DEMOD_PHASE_5_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG12 register ***************/
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_6_Pos  (0U)
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_6_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_6_Pos)
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_6  BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_6_Msk
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_7_Pos  (12U)
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_7_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_7_Pos)
#define BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_7  BT_PHY_DEMOD_CFG12_BR_DEMOD_PHASE_7_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG13 register ***************/
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_0_Pos  (0U)
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_0_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_0_Pos)
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_0  BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_0_Msk
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_1_Pos  (12U)
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_1_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_1_Pos)
#define BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_1  BT_PHY_DEMOD_CFG13_BR_DEMOD_PHASE_IDEAL_1_Msk

/*************** Bit definition for BT_PHY_DEMOD_CFG14 register ***************/
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_2_Pos  (0U)
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_2_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_2_Pos)
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_2  BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_2_Msk
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_3_Pos  (12U)
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_3_Msk  (0xFFFUL << BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_3_Pos)
#define BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_3  BT_PHY_DEMOD_CFG14_BR_DEMOD_PHASE_IDEAL_3_Msk

/*************** Bit definition for BT_PHY_RX_STATUS1 register ****************/
#define BT_PHY_RX_STATUS1_CFO_PHASE_Pos  (0U)
#define BT_PHY_RX_STATUS1_CFO_PHASE_Msk  (0xFFFUL << BT_PHY_RX_STATUS1_CFO_PHASE_Pos)
#define BT_PHY_RX_STATUS1_CFO_PHASE     BT_PHY_RX_STATUS1_CFO_PHASE_Msk
#define BT_PHY_RX_STATUS1_EDR_CARRIER_PHASE_Pos  (12U)
#define BT_PHY_RX_STATUS1_EDR_CARRIER_PHASE_Msk  (0xFFFUL << BT_PHY_RX_STATUS1_EDR_CARRIER_PHASE_Pos)
#define BT_PHY_RX_STATUS1_EDR_CARRIER_PHASE  BT_PHY_RX_STATUS1_EDR_CARRIER_PHASE_Msk

/**************** Bit definition for BT_PHY_AGC_CTRL register *****************/
#define BT_PHY_AGC_CTRL_AGC_ENABLE_Pos  (0U)
#define BT_PHY_AGC_CTRL_AGC_ENABLE_Msk  (0x1UL << BT_PHY_AGC_CTRL_AGC_ENABLE_Pos)
#define BT_PHY_AGC_CTRL_AGC_ENABLE      BT_PHY_AGC_CTRL_AGC_ENABLE_Msk
#define BT_PHY_AGC_CTRL_AGC_MODE_Pos    (1U)
#define BT_PHY_AGC_CTRL_AGC_MODE_Msk    (0x1UL << BT_PHY_AGC_CTRL_AGC_MODE_Pos)
#define BT_PHY_AGC_CTRL_AGC_MODE        BT_PHY_AGC_CTRL_AGC_MODE_Msk
#define BT_PHY_AGC_CTRL_DIG_GAIN_EN_Pos  (2U)
#define BT_PHY_AGC_CTRL_DIG_GAIN_EN_Msk  (0x1UL << BT_PHY_AGC_CTRL_DIG_GAIN_EN_Pos)
#define BT_PHY_AGC_CTRL_DIG_GAIN_EN     BT_PHY_AGC_CTRL_DIG_GAIN_EN_Msk
#define BT_PHY_AGC_CTRL_AGC_VGAADJ_EN_Pos  (3U)
#define BT_PHY_AGC_CTRL_AGC_VGAADJ_EN_Msk  (0x1UL << BT_PHY_AGC_CTRL_AGC_VGAADJ_EN_Pos)
#define BT_PHY_AGC_CTRL_AGC_VGAADJ_EN   BT_PHY_AGC_CTRL_AGC_VGAADJ_EN_Msk

/**************** Bit definition for BT_PHY_AGC_CFG1 register *****************/
#define BT_PHY_AGC_CFG1_ADC_MAG_THD0_Pos  (0U)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD0_Msk  (0x3FFUL << BT_PHY_AGC_CFG1_ADC_MAG_THD0_Pos)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD0    BT_PHY_AGC_CFG1_ADC_MAG_THD0_Msk
#define BT_PHY_AGC_CFG1_ADC_MAG_THD1_Pos  (10U)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD1_Msk  (0x3FFUL << BT_PHY_AGC_CFG1_ADC_MAG_THD1_Pos)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD1    BT_PHY_AGC_CFG1_ADC_MAG_THD1_Msk
#define BT_PHY_AGC_CFG1_ADC_MAG_THD2_Pos  (20U)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD2_Msk  (0x3FFUL << BT_PHY_AGC_CFG1_ADC_MAG_THD2_Pos)
#define BT_PHY_AGC_CFG1_ADC_MAG_THD2    BT_PHY_AGC_CFG1_ADC_MAG_THD2_Msk

/**************** Bit definition for BT_PHY_AGC_CFG2 register *****************/
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Pos  (0U)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Msk  (0xFUL << BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Pos)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD0  BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD0_Msk
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Pos  (4U)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Msk  (0xFUL << BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Pos)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD1  BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD1_Msk
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Pos  (8U)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Msk  (0xFUL << BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Pos)
#define BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD2  BT_PHY_AGC_CFG2_ADC_MAG_CNT_THD2_Msk
#define BT_PHY_AGC_CFG2_ADC_MAG_SET_Pos  (12U)
#define BT_PHY_AGC_CFG2_ADC_MAG_SET_Msk  (0x3FFUL << BT_PHY_AGC_CFG2_ADC_MAG_SET_Pos)
#define BT_PHY_AGC_CFG2_ADC_MAG_SET     BT_PHY_AGC_CFG2_ADC_MAG_SET_Msk

/**************** Bit definition for BT_PHY_AGC_CFG3 register *****************/
#define BT_PHY_AGC_CFG3_ADC_SAT_THD_Pos  (0U)
#define BT_PHY_AGC_CFG3_ADC_SAT_THD_Msk  (0x3FFUL << BT_PHY_AGC_CFG3_ADC_SAT_THD_Pos)
#define BT_PHY_AGC_CFG3_ADC_SAT_THD     BT_PHY_AGC_CFG3_ADC_SAT_THD_Msk
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM_Pos  (10U)
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM_Msk  (0xFUL << BT_PHY_AGC_CFG3_ADC_SAT_NUM_Pos)
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM     BT_PHY_AGC_CFG3_ADC_SAT_NUM_Msk
#define BT_PHY_AGC_CFG3_ADC_SAT_THD_BT_Pos  (14U)
#define BT_PHY_AGC_CFG3_ADC_SAT_THD_BT_Msk  (0x3FFUL << BT_PHY_AGC_CFG3_ADC_SAT_THD_BT_Pos)
#define BT_PHY_AGC_CFG3_ADC_SAT_THD_BT  BT_PHY_AGC_CFG3_ADC_SAT_THD_BT_Msk
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM_BT_Pos  (24U)
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM_BT_Msk  (0xFUL << BT_PHY_AGC_CFG3_ADC_SAT_NUM_BT_Pos)
#define BT_PHY_AGC_CFG3_ADC_SAT_NUM_BT  BT_PHY_AGC_CFG3_ADC_SAT_NUM_BT_Msk

/**************** Bit definition for BT_PHY_AGC_CFG4 register *****************/
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Pos  (0U)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Msk  (0xFUL << BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Pos)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD  BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_THD_Msk
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Pos  (4U)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Msk  (0x3UL << BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Pos)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD  BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_THD_Msk
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Pos  (6U)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Msk  (0xFUL << BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Pos)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD  BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_THD_Msk
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Pos  (10U)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Msk  (0xFUL << BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Pos)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT  BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_INIT_Msk
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Pos  (14U)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Msk  (0x3UL << BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Pos)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT  BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_INIT_Msk
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Pos  (16U)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Msk  (0xFUL << BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Pos)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT  BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_INIT_Msk
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Pos  (20U)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Msk  (0xFUL << BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Pos)
#define BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP  BT_PHY_AGC_CFG4_LNA_MIXER_GAIN_INDEX_STEP_Msk
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Pos  (24U)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Msk  (0x3UL << BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Pos)
#define BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP  BT_PHY_AGC_CFG4_CBPF_GAIN_INDEX_STEP_Msk
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Pos  (26U)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Msk  (0xFUL << BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Pos)
#define BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP  BT_PHY_AGC_CFG4_VGA_GAIN_INDEX_STEP_Msk

/**************** Bit definition for BT_PHY_AGC_CFG5 register *****************/
#define BT_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Pos  (0U)
#define BT_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Msk  (0x3UL << BT_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Pos)
#define BT_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0  BT_PHY_AGC_CFG5_AGC_CBPF_GAIN_INDEX_SETTING0_Msk
#define BT_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Pos  (2U)
#define BT_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Msk  (0xFUL << BT_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Pos)
#define BT_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0  BT_PHY_AGC_CFG5_AGC_VGA_GAIN_INDEX_SETTING0_Msk
#define BT_PHY_AGC_CFG5_DIG_GAIN_LOW_Pos  (6U)
#define BT_PHY_AGC_CFG5_DIG_GAIN_LOW_Msk  (0x3FUL << BT_PHY_AGC_CFG5_DIG_GAIN_LOW_Pos)
#define BT_PHY_AGC_CFG5_DIG_GAIN_LOW    BT_PHY_AGC_CFG5_DIG_GAIN_LOW_Msk
#define BT_PHY_AGC_CFG5_DIG_GAIN_HIGH_Pos  (12U)
#define BT_PHY_AGC_CFG5_DIG_GAIN_HIGH_Msk  (0x3FUL << BT_PHY_AGC_CFG5_DIG_GAIN_HIGH_Pos)
#define BT_PHY_AGC_CFG5_DIG_GAIN_HIGH   BT_PHY_AGC_CFG5_DIG_GAIN_HIGH_Msk
#define BT_PHY_AGC_CFG5_ADC_POWER_TARGET_BT_Pos  (18U)
#define BT_PHY_AGC_CFG5_ADC_POWER_TARGET_BT_Msk  (0x7FUL << BT_PHY_AGC_CFG5_ADC_POWER_TARGET_BT_Pos)
#define BT_PHY_AGC_CFG5_ADC_POWER_TARGET_BT  BT_PHY_AGC_CFG5_ADC_POWER_TARGET_BT_Msk

/**************** Bit definition for BT_PHY_AGC_CFG6 register *****************/
#define BT_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Pos  (0U)
#define BT_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Msk  (0x7FUL << BT_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Pos)
#define BT_PHY_AGC_CFG6_AGC_DELAY_RESET_1  BT_PHY_AGC_CFG6_AGC_DELAY_RESET_1_Msk
#define BT_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Pos  (7U)
#define BT_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Msk  (0x7FUL << BT_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Pos)
#define BT_PHY_AGC_CFG6_AGC_DELAY_PKDET_1  BT_PHY_AGC_CFG6_AGC_DELAY_PKDET_1_Msk
#define BT_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Pos  (14U)
#define BT_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Msk  (0x7FUL << BT_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Pos)
#define BT_PHY_AGC_CFG6_AGC_DELAY_LNA_1  BT_PHY_AGC_CFG6_AGC_DELAY_LNA_1_Msk
#define BT_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Pos  (21U)
#define BT_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Msk  (0x7FUL << BT_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Pos)
#define BT_PHY_AGC_CFG6_AGC_DELAY_DIG_1  BT_PHY_AGC_CFG6_AGC_DELAY_DIG_1_Msk

/**************** Bit definition for BT_PHY_AGC_CFG7 register *****************/
#define BT_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Pos  (0U)
#define BT_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Msk  (0x7FUL << BT_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Pos)
#define BT_PHY_AGC_CFG7_AGC_DELAY_RESET_2  BT_PHY_AGC_CFG7_AGC_DELAY_RESET_2_Msk
#define BT_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Pos  (7U)
#define BT_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Msk  (0x7FUL << BT_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Pos)
#define BT_PHY_AGC_CFG7_AGC_DELAY_PKDET_2  BT_PHY_AGC_CFG7_AGC_DELAY_PKDET_2_Msk
#define BT_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Pos  (14U)
#define BT_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Msk  (0x7FUL << BT_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Pos)
#define BT_PHY_AGC_CFG7_AGC_DELAY_LNA_2  BT_PHY_AGC_CFG7_AGC_DELAY_LNA_2_Msk
#define BT_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Pos  (21U)
#define BT_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Msk  (0x7FUL << BT_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Pos)
#define BT_PHY_AGC_CFG7_AGC_DELAY_DIG_2  BT_PHY_AGC_CFG7_AGC_DELAY_DIG_2_Msk

/**************** Bit definition for BT_PHY_AGC_CFG8 register *****************/
#define BT_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Pos  (0U)
#define BT_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Msk  (0x7FUL << BT_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Pos)
#define BT_PHY_AGC_CFG8_AGC_DELAY_CBPF_1  BT_PHY_AGC_CFG8_AGC_DELAY_CBPF_1_Msk
#define BT_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Pos  (7U)
#define BT_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Msk  (0x7FUL << BT_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Pos)
#define BT_PHY_AGC_CFG8_AGC_DELAY_ADC_1  BT_PHY_AGC_CFG8_AGC_DELAY_ADC_1_Msk
#define BT_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Pos  (14U)
#define BT_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Msk  (0x7FUL << BT_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Pos)
#define BT_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1  BT_PHY_AGC_CFG8_DIG_GAIN_WINDOW_1_Msk
#define BT_PHY_AGC_CFG8_ADC_POWER_TARGET_Pos  (21U)
#define BT_PHY_AGC_CFG8_ADC_POWER_TARGET_Msk  (0x7FUL << BT_PHY_AGC_CFG8_ADC_POWER_TARGET_Pos)
#define BT_PHY_AGC_CFG8_ADC_POWER_TARGET  BT_PHY_AGC_CFG8_ADC_POWER_TARGET_Msk

/**************** Bit definition for BT_PHY_AGC_CFG9 register *****************/
#define BT_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Pos  (0U)
#define BT_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Msk  (0x7FUL << BT_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Pos)
#define BT_PHY_AGC_CFG9_AGC_DELAY_CBPF_2  BT_PHY_AGC_CFG9_AGC_DELAY_CBPF_2_Msk
#define BT_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Pos  (7U)
#define BT_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Msk  (0x7FUL << BT_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Pos)
#define BT_PHY_AGC_CFG9_AGC_DELAY_ADC_2  BT_PHY_AGC_CFG9_AGC_DELAY_ADC_2_Msk
#define BT_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Pos  (14U)
#define BT_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Msk  (0x7FUL << BT_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Pos)
#define BT_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2  BT_PHY_AGC_CFG9_DIG_GAIN_WINDOW_2_Msk

/**************** Bit definition for BT_PHY_AGC_CFG10 register ****************/
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Pos  (0U)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Msk  (0x7FUL << BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Pos)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1  BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET1_1_Msk
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Pos  (7U)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Msk  (0x7FUL << BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Pos)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1  BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD1_1_Msk
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Pos  (14U)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Msk  (0x7FUL << BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Pos)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1  BT_PHY_AGC_CFG10_AGC_PEAKDET_TIMER_SET2_1_Msk
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Pos  (21U)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Msk  (0x7FUL << BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Pos)
#define BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1  BT_PHY_AGC_CFG10_AGC_PEAKDET_CNT_THD2_1_Msk

/**************** Bit definition for BT_PHY_AGC_CFG11 register ****************/
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Pos  (0U)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Msk  (0x7FUL << BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Pos)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2  BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET1_2_Msk
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Pos  (7U)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Msk  (0x7FUL << BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Pos)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2  BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD1_2_Msk
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Pos  (14U)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Msk  (0x7FUL << BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Pos)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2  BT_PHY_AGC_CFG11_AGC_PEAKDET_TIMER_SET2_2_Msk
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Pos  (21U)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Msk  (0x7FUL << BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Pos)
#define BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2  BT_PHY_AGC_CFG11_AGC_PEAKDET_CNT_THD2_2_Msk

/**************** Bit definition for BT_PHY_AGC_CFG12 register ****************/
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Pos  (0U)
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Msk  (0x7FUL << BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Pos)
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_1  BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_1_Msk
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Pos  (7U)
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Msk  (0x7FUL << BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Pos)
#define BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_2  BT_PHY_AGC_CFG12_AGC_URUN_WINDOW_2_Msk
#define BT_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Pos  (14U)
#define BT_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Msk  (0x7FUL << BT_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Pos)
#define BT_PHY_AGC_CFG12_ADC_POWER_URUN_THD  BT_PHY_AGC_CFG12_ADC_POWER_URUN_THD_Msk

/**************** Bit definition for BT_PHY_RSSI_CFG1 register ****************/
#define BT_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Pos  (0U)
#define BT_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Msk  (0x7FUL << BT_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Pos)
#define BT_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB  BT_PHY_RSSI_CFG1_DIG_GAIN_LOW_DB_Msk
#define BT_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Pos  (7U)
#define BT_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Msk  (0x7FUL << BT_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Pos)
#define BT_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB  BT_PHY_RSSI_CFG1_DIG_GAIN_HIGH_DB_Msk
#define BT_PHY_RSSI_CFG1_RSSI_MU_Pos    (14U)
#define BT_PHY_RSSI_CFG1_RSSI_MU_Msk    (0x7UL << BT_PHY_RSSI_CFG1_RSSI_MU_Pos)
#define BT_PHY_RSSI_CFG1_RSSI_MU        BT_PHY_RSSI_CFG1_RSSI_MU_Msk
#define BT_PHY_RSSI_CFG1_RSSI_OFFSET_Pos  (17U)
#define BT_PHY_RSSI_CFG1_RSSI_OFFSET_Msk  (0x3FUL << BT_PHY_RSSI_CFG1_RSSI_OFFSET_Pos)
#define BT_PHY_RSSI_CFG1_RSSI_OFFSET    BT_PHY_RSSI_CFG1_RSSI_OFFSET_Msk

/*************** Bit definition for BT_PHY_AGC_STATUS register ****************/
#define BT_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Pos  (0U)
#define BT_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Msk  (0xFUL << BT_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Pos)
#define BT_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX  BT_PHY_AGC_STATUS_LNA_MIXER_GAIN_INDEX_Msk
#define BT_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Pos  (4U)
#define BT_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Msk  (0x3UL << BT_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Pos)
#define BT_PHY_AGC_STATUS_CBPF_GAIN_INDEX  BT_PHY_AGC_STATUS_CBPF_GAIN_INDEX_Msk
#define BT_PHY_AGC_STATUS_VGA_GAIN_INDEX_Pos  (6U)
#define BT_PHY_AGC_STATUS_VGA_GAIN_INDEX_Msk  (0xFUL << BT_PHY_AGC_STATUS_VGA_GAIN_INDEX_Pos)
#define BT_PHY_AGC_STATUS_VGA_GAIN_INDEX  BT_PHY_AGC_STATUS_VGA_GAIN_INDEX_Msk
#define BT_PHY_AGC_STATUS_ADC_DIG_GAIN_Pos  (10U)
#define BT_PHY_AGC_STATUS_ADC_DIG_GAIN_Msk  (0x3FUL << BT_PHY_AGC_STATUS_ADC_DIG_GAIN_Pos)
#define BT_PHY_AGC_STATUS_ADC_DIG_GAIN  BT_PHY_AGC_STATUS_ADC_DIG_GAIN_Msk
#define BT_PHY_AGC_STATUS_RSSI_Pos      (16U)
#define BT_PHY_AGC_STATUS_RSSI_Msk      (0xFFUL << BT_PHY_AGC_STATUS_RSSI_Pos)
#define BT_PHY_AGC_STATUS_RSSI          BT_PHY_AGC_STATUS_RSSI_Msk

/************** Bit definition for BT_PHY_EDRSYNC_CFG1 register ***************/
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_CNT_THD_Pos  (0U)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_CNT_THD_Msk  (0xFFUL << BT_PHY_EDRSYNC_CFG1_EDRSYNC_CNT_THD_Pos)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_CNT_THD  BT_PHY_EDRSYNC_CFG1_EDRSYNC_CNT_THD_Msk
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_PHASECORR_THD_Pos  (8U)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_PHASECORR_THD_Msk  (0x3FFFFUL << BT_PHY_EDRSYNC_CFG1_EDRSYNC_PHASECORR_THD_Pos)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_PHASECORR_THD  BT_PHY_EDRSYNC_CFG1_EDRSYNC_PHASECORR_THD_Msk
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_METHOD_Pos  (26U)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_METHOD_Msk  (0x1UL << BT_PHY_EDRSYNC_CFG1_EDRSYNC_METHOD_Pos)
#define BT_PHY_EDRSYNC_CFG1_EDRSYNC_METHOD  BT_PHY_EDRSYNC_CFG1_EDRSYNC_METHOD_Msk

/************** Bit definition for BT_PHY_EDRSYNC_CFG2 register ***************/
#define BT_PHY_EDRSYNC_CFG2_EDRSYNC_PHASEUNWRAP_THD_Pos  (0U)
#define BT_PHY_EDRSYNC_CFG2_EDRSYNC_PHASEUNWRAP_THD_Msk  (0xFFUL << BT_PHY_EDRSYNC_CFG2_EDRSYNC_PHASEUNWRAP_THD_Pos)
#define BT_PHY_EDRSYNC_CFG2_EDRSYNC_PHASEUNWRAP_THD  BT_PHY_EDRSYNC_CFG2_EDRSYNC_PHASEUNWRAP_THD_Msk

/************** Bit definition for BT_PHY_EDRDEMOD_CFG1 register **************/
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_ERR_Pos  (0U)
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_ERR_Msk  (0x3FFUL << BT_PHY_EDRDEMOD_CFG1_EDR2_MU_ERR_Pos)
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_ERR  BT_PHY_EDRDEMOD_CFG1_EDR2_MU_ERR_Msk
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_DC_Pos  (10U)
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_DC_Msk  (0x3FFUL << BT_PHY_EDRDEMOD_CFG1_EDR2_MU_DC_Pos)
#define BT_PHY_EDRDEMOD_CFG1_EDR2_MU_DC  BT_PHY_EDRDEMOD_CFG1_EDR2_MU_DC_Msk

/************** Bit definition for BT_PHY_EDRDEMOD_CFG2 register **************/
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_ERR_Pos  (0U)
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_ERR_Msk  (0x3FFUL << BT_PHY_EDRDEMOD_CFG2_EDR3_MU_ERR_Pos)
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_ERR  BT_PHY_EDRDEMOD_CFG2_EDR3_MU_ERR_Msk
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_DC_Pos  (10U)
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_DC_Msk  (0x3FFUL << BT_PHY_EDRDEMOD_CFG2_EDR3_MU_DC_Pos)
#define BT_PHY_EDRDEMOD_CFG2_EDR3_MU_DC  BT_PHY_EDRDEMOD_CFG2_EDR3_MU_DC_Msk

/*************** Bit definition for BT_PHY_EDRTED_CFG1 register ***************/
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_P_Pos  (0U)
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_P_Msk  (0xFUL << BT_PHY_EDRTED_CFG1_TED_EDR2_MU_P_Pos)
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_P  BT_PHY_EDRTED_CFG1_TED_EDR2_MU_P_Msk
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_F_Pos  (4U)
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_F_Msk  (0xFUL << BT_PHY_EDRTED_CFG1_TED_EDR2_MU_F_Pos)
#define BT_PHY_EDRTED_CFG1_TED_EDR2_MU_F  BT_PHY_EDRTED_CFG1_TED_EDR2_MU_F_Msk
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_P_Pos  (8U)
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_P_Msk  (0xFUL << BT_PHY_EDRTED_CFG1_TED_EDR3_MU_P_Pos)
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_P  BT_PHY_EDRTED_CFG1_TED_EDR3_MU_P_Msk
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_F_Pos  (12U)
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_F_Msk  (0xFUL << BT_PHY_EDRTED_CFG1_TED_EDR3_MU_F_Pos)
#define BT_PHY_EDRTED_CFG1_TED_EDR3_MU_F  BT_PHY_EDRTED_CFG1_TED_EDR3_MU_F_Msk

/***************** Bit definition for BT_PHY_TX_CTRL register *****************/
#define BT_PHY_TX_CTRL_FORCE_TX_ON_Pos  (0U)
#define BT_PHY_TX_CTRL_FORCE_TX_ON_Msk  (0x1UL << BT_PHY_TX_CTRL_FORCE_TX_ON_Pos)
#define BT_PHY_TX_CTRL_FORCE_TX_ON      BT_PHY_TX_CTRL_FORCE_TX_ON_Msk
#define BT_PHY_TX_CTRL_TX_LOOPBACK_MODE_Pos  (1U)
#define BT_PHY_TX_CTRL_TX_LOOPBACK_MODE_Msk  (0x1UL << BT_PHY_TX_CTRL_TX_LOOPBACK_MODE_Pos)
#define BT_PHY_TX_CTRL_TX_LOOPBACK_MODE  BT_PHY_TX_CTRL_TX_LOOPBACK_MODE_Msk
#define BT_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Pos  (2U)
#define BT_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Msk  (0x1UL << BT_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Pos)
#define BT_PHY_TX_CTRL_MAC_PWR_CTRL_EN  BT_PHY_TX_CTRL_MAC_PWR_CTRL_EN_Msk
#define BT_PHY_TX_CTRL_MOD_METHOD_BLE_Pos  (3U)
#define BT_PHY_TX_CTRL_MOD_METHOD_BLE_Msk  (0x1UL << BT_PHY_TX_CTRL_MOD_METHOD_BLE_Pos)
#define BT_PHY_TX_CTRL_MOD_METHOD_BLE   BT_PHY_TX_CTRL_MOD_METHOD_BLE_Msk
#define BT_PHY_TX_CTRL_MOD_METHOD_BR_Pos  (4U)
#define BT_PHY_TX_CTRL_MOD_METHOD_BR_Msk  (0x1UL << BT_PHY_TX_CTRL_MOD_METHOD_BR_Pos)
#define BT_PHY_TX_CTRL_MOD_METHOD_BR    BT_PHY_TX_CTRL_MOD_METHOD_BR_Msk
#define BT_PHY_TX_CTRL_MOD_METHOD_EDR_Pos  (5U)
#define BT_PHY_TX_CTRL_MOD_METHOD_EDR_Msk  (0x1UL << BT_PHY_TX_CTRL_MOD_METHOD_EDR_Pos)
#define BT_PHY_TX_CTRL_MOD_METHOD_EDR   BT_PHY_TX_CTRL_MOD_METHOD_EDR_Msk
#define BT_PHY_TX_CTRL_MMDIV_SEL_Pos    (6U)
#define BT_PHY_TX_CTRL_MMDIV_SEL_Msk    (0x3UL << BT_PHY_TX_CTRL_MMDIV_SEL_Pos)
#define BT_PHY_TX_CTRL_MMDIV_SEL        BT_PHY_TX_CTRL_MMDIV_SEL_Msk
#define BT_PHY_TX_CTRL_DAC_SIGN_Pos     (8U)
#define BT_PHY_TX_CTRL_DAC_SIGN_Msk     (0x1UL << BT_PHY_TX_CTRL_DAC_SIGN_Pos)
#define BT_PHY_TX_CTRL_DAC_SIGN         BT_PHY_TX_CTRL_DAC_SIGN_Msk
#define BT_PHY_TX_CTRL_GUARD_TIME_SEL_Pos  (9U)
#define BT_PHY_TX_CTRL_GUARD_TIME_SEL_Msk  (0x3UL << BT_PHY_TX_CTRL_GUARD_TIME_SEL_Pos)
#define BT_PHY_TX_CTRL_GUARD_TIME_SEL   BT_PHY_TX_CTRL_GUARD_TIME_SEL_Msk
#define BT_PHY_TX_CTRL_GFSK_RD_EN_Pos   (11U)
#define BT_PHY_TX_CTRL_GFSK_RD_EN_Msk   (0x1UL << BT_PHY_TX_CTRL_GFSK_RD_EN_Pos)
#define BT_PHY_TX_CTRL_GFSK_RD_EN       BT_PHY_TX_CTRL_GFSK_RD_EN_Msk
#define BT_PHY_TX_CTRL_DPSK_RD_EN_Pos   (12U)
#define BT_PHY_TX_CTRL_DPSK_RD_EN_Msk   (0x1UL << BT_PHY_TX_CTRL_DPSK_RD_EN_Pos)
#define BT_PHY_TX_CTRL_DPSK_RD_EN       BT_PHY_TX_CTRL_DPSK_RD_EN_Msk

/*************** Bit definition for BT_PHY_TX_RCC_CTRL register ***************/
#define BT_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Pos  (0U)
#define BT_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON  BT_PHY_TX_RCC_CTRL_FORCE_PA_CTRL_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Pos  (1U)
#define BT_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_LFP_ON  BT_PHY_TX_RCC_CTRL_FORCE_LFP_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Pos  (2U)
#define BT_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_HFP_ON  BT_PHY_TX_RCC_CTRL_FORCE_HFP_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Pos  (3U)
#define BT_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON  BT_PHY_TX_RCC_CTRL_FORCE_IF_MOD_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_RC_ON_Pos  (4U)
#define BT_PHY_TX_RCC_CTRL_FORCE_RC_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_RC_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_RC_ON  BT_PHY_TX_RCC_CTRL_FORCE_RC_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Pos  (5U)
#define BT_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON  BT_PHY_TX_RCC_CTRL_FORCE_GAUSSFLT_ON_Msk
#define BT_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Pos  (6U)
#define BT_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Msk  (0x1UL << BT_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Pos)
#define BT_PHY_TX_RCC_CTRL_FORCE_TX_RESET  BT_PHY_TX_RCC_CTRL_FORCE_TX_RESET_Msk

/************* Bit definition for BT_PHY_TX_GAUSSFLT_CFG register *************/
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Pos  (0U)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Msk  (0x1FFUL << BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Pos)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1  BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_1_Msk
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Pos  (9U)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Msk  (0x1FFUL << BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Pos)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2  BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_2_Msk
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_BR_Pos  (18U)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_BR_Msk  (0x1FFUL << BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_BR_Pos)
#define BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_BR  BT_PHY_TX_GAUSSFLT_CFG_GAUSS_GAIN_BR_Msk

/************** Bit definition for BT_PHY_TX_IF_MOD_CFG register **************/
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BLE_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BLE_Msk  (0x3FFUL << BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BLE_Pos)
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BLE  BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BLE_Msk
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BR_Pos  (10U)
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BR_Msk  (0x3FFUL << BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BR_Pos)
#define BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BR  BT_PHY_TX_IF_MOD_CFG_TX_IF_PHASE_BR_Msk
#define BT_PHY_TX_IF_MOD_CFG_TX_RAMP_BYPASS_Pos  (20U)
#define BT_PHY_TX_IF_MOD_CFG_TX_RAMP_BYPASS_Msk  (0x1UL << BT_PHY_TX_IF_MOD_CFG_TX_RAMP_BYPASS_Pos)
#define BT_PHY_TX_IF_MOD_CFG_TX_RAMP_BYPASS  BT_PHY_TX_IF_MOD_CFG_TX_RAMP_BYPASS_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG2 register **************/
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_Msk
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_Msk
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_Msk
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_EN_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_EN_Msk  (0x1UL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_EN_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_EN  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BLE_FRC_EN_Msk
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_EN_Pos  (25U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_EN_Msk  (0x1UL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_EN_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_EN  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_BR_FRC_EN_Msk
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_EN_Pos  (26U)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_EN_Msk  (0x1UL << BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_EN_Pos)
#define BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_EN  BT_PHY_TX_IF_MOD_CFG2_TX_MOD_GAIN_EDR_FRC_EN_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG3 register **************/
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_0_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_0_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_0_Pos)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_0  BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_0_Msk
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_1_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_1_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_1_Pos)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_1  BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_1_Msk
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_2_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_2_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_2_Pos)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_2  BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_2_Msk
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_3_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_3_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_3_Pos)
#define BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_3  BT_PHY_TX_IF_MOD_CFG3_TX_MOD_GAIN_BLE_3_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG4 register **************/
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_4_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_4_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_4_Pos)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_4  BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_4_Msk
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_5_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_5_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_5_Pos)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_5  BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_5_Msk
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_6_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_6_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_6_Pos)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_6  BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_6_Msk
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_7_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_7_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_7_Pos)
#define BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_7  BT_PHY_TX_IF_MOD_CFG4_TX_MOD_GAIN_BLE_7_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG5 register **************/
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_0_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_0_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_0_Pos)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_0  BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_0_Msk
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_1_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_1_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_1_Pos)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_1  BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_1_Msk
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_2_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_2_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_2_Pos)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_2  BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_2_Msk
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_3_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_3_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_3_Pos)
#define BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_3  BT_PHY_TX_IF_MOD_CFG5_TX_MOD_GAIN_BR_3_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG6 register **************/
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_4_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_4_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_4_Pos)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_4  BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_4_Msk
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_5_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_5_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_5_Pos)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_5  BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_5_Msk
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_6_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_6_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_6_Pos)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_6  BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_6_Msk
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_7_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_7_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_7_Pos)
#define BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_7  BT_PHY_TX_IF_MOD_CFG6_TX_MOD_GAIN_BR_7_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG7 register **************/
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_0_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_0_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_0_Pos)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_0  BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_0_Msk
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_1_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_1_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_1_Pos)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_1  BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_1_Msk
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_2_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_2_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_2_Pos)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_2  BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_2_Msk
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_3_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_3_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_3_Pos)
#define BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_3  BT_PHY_TX_IF_MOD_CFG7_TX_MOD_GAIN_EDR_3_Msk

/************* Bit definition for BT_PHY_TX_IF_MOD_CFG8 register **************/
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_4_Pos  (0U)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_4_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_4_Pos)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_4  BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_4_Msk
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_5_Pos  (8U)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_5_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_5_Pos)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_5  BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_5_Msk
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_6_Pos  (16U)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_6_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_6_Pos)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_6  BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_6_Msk
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_7_Pos  (24U)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_7_Msk  (0xFFUL << BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_7_Pos)
#define BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_7  BT_PHY_TX_IF_MOD_CFG8_TX_MOD_GAIN_EDR_7_Msk

/*************** Bit definition for BT_PHY_TX_HFP_CFG register ****************/
#define BT_PHY_TX_HFP_CFG_TX_KCAL_COEF_Pos  (0U)
#define BT_PHY_TX_HFP_CFG_TX_KCAL_COEF_Msk  (0x1FFUL << BT_PHY_TX_HFP_CFG_TX_KCAL_COEF_Pos)
#define BT_PHY_TX_HFP_CFG_TX_KCAL_COEF  BT_PHY_TX_HFP_CFG_TX_KCAL_COEF_Msk
#define BT_PHY_TX_HFP_CFG_TX_KCAL_Pos   (9U)
#define BT_PHY_TX_HFP_CFG_TX_KCAL_Msk   (0xFFFUL << BT_PHY_TX_HFP_CFG_TX_KCAL_Pos)
#define BT_PHY_TX_HFP_CFG_TX_KCAL       BT_PHY_TX_HFP_CFG_TX_KCAL_Msk
#define BT_PHY_TX_HFP_CFG_HFP_FCW_SEL_Pos  (21U)
#define BT_PHY_TX_HFP_CFG_HFP_FCW_SEL_Msk  (0x1UL << BT_PHY_TX_HFP_CFG_HFP_FCW_SEL_Pos)
#define BT_PHY_TX_HFP_CFG_HFP_FCW_SEL   BT_PHY_TX_HFP_CFG_HFP_FCW_SEL_Msk
#define BT_PHY_TX_HFP_CFG_HFP_FCW_Pos   (22U)
#define BT_PHY_TX_HFP_CFG_HFP_FCW_Msk   (0x3FUL << BT_PHY_TX_HFP_CFG_HFP_FCW_Pos)
#define BT_PHY_TX_HFP_CFG_HFP_FCW       BT_PHY_TX_HFP_CFG_HFP_FCW_Msk
#define BT_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Pos  (28U)
#define BT_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Msk  (0x7UL << BT_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Pos)
#define BT_PHY_TX_HFP_CFG_HFP_DELAY_SEL  BT_PHY_TX_HFP_CFG_HFP_DELAY_SEL_Msk

/*************** Bit definition for BT_PHY_TX_LFP_CFG register ****************/
#define BT_PHY_TX_LFP_CFG_LFP_FCW_SEL_Pos  (0U)
#define BT_PHY_TX_LFP_CFG_LFP_FCW_SEL_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_LFP_FCW_SEL_Pos)
#define BT_PHY_TX_LFP_CFG_LFP_FCW_SEL   BT_PHY_TX_LFP_CFG_LFP_FCW_SEL_Msk
#define BT_PHY_TX_LFP_CFG_LFP_FCW_Pos   (1U)
#define BT_PHY_TX_LFP_CFG_LFP_FCW_Msk   (0x3FFUL << BT_PHY_TX_LFP_CFG_LFP_FCW_Pos)
#define BT_PHY_TX_LFP_CFG_LFP_FCW       BT_PHY_TX_LFP_CFG_LFP_FCW_Msk
#define BT_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Pos  (11U)
#define BT_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Pos)
#define BT_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN  BT_PHY_TX_LFP_CFG_TX_SDM_DITHER_EN_Msk
#define BT_PHY_TX_LFP_CFG_TX_SDM_SEL_Pos  (12U)
#define BT_PHY_TX_LFP_CFG_TX_SDM_SEL_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_TX_SDM_SEL_Pos)
#define BT_PHY_TX_LFP_CFG_TX_SDM_SEL    BT_PHY_TX_LFP_CFG_TX_SDM_SEL_Msk
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW_SEL_Pos  (13U)
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW_SEL_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_BT_LFP_FCW_SEL_Pos)
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW_SEL  BT_PHY_TX_LFP_CFG_BT_LFP_FCW_SEL_Msk
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW_Pos  (14U)
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW_Msk  (0x3FFUL << BT_PHY_TX_LFP_CFG_BT_LFP_FCW_Pos)
#define BT_PHY_TX_LFP_CFG_BT_LFP_FCW    BT_PHY_TX_LFP_CFG_BT_LFP_FCW_Msk
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_DITHER_EN_Pos  (24U)
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_DITHER_EN_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_BT_TX_SDM_DITHER_EN_Pos)
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_DITHER_EN  BT_PHY_TX_LFP_CFG_BT_TX_SDM_DITHER_EN_Msk
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_SEL_Pos  (25U)
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_SEL_Msk  (0x1UL << BT_PHY_TX_LFP_CFG_BT_TX_SDM_SEL_Pos)
#define BT_PHY_TX_LFP_CFG_BT_TX_SDM_SEL  BT_PHY_TX_LFP_CFG_BT_TX_SDM_SEL_Msk

/**************** Bit definition for BT_PHY_TX_PA_CFG register ****************/
#define BT_PHY_TX_PA_CFG_PA_RAMP_FORCE_Pos  (0U)
#define BT_PHY_TX_PA_CFG_PA_RAMP_FORCE_Msk  (0x3UL << BT_PHY_TX_PA_CFG_PA_RAMP_FORCE_Pos)
#define BT_PHY_TX_PA_CFG_PA_RAMP_FORCE  BT_PHY_TX_PA_CFG_PA_RAMP_FORCE_Msk
#define BT_PHY_TX_PA_CFG_PA_CTRL_TARGET_Pos  (2U)
#define BT_PHY_TX_PA_CFG_PA_CTRL_TARGET_Msk  (0x3FUL << BT_PHY_TX_PA_CFG_PA_CTRL_TARGET_Pos)
#define BT_PHY_TX_PA_CFG_PA_CTRL_TARGET  BT_PHY_TX_PA_CFG_PA_CTRL_TARGET_Msk
#define BT_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Pos  (8U)
#define BT_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Msk  (0x7UL << BT_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Pos)
#define BT_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX  BT_PHY_TX_PA_CFG_PA_RAMP_FACTOR_IDX_Msk

/*********** Bit definition for BT_PHY_EDR_TMXBUF_GC_CFG1 register ************/
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_Pos  (0U)
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC  BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_EN_Pos  (4U)
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_EN_Msk  (0x1UL << BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_EN_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_EN  BT_PHY_EDR_TMXBUF_GC_CFG1_EDR_TMXBUF_GC_FRC_EN_Msk

/*********** Bit definition for BT_PHY_EDR_TMXBUF_GC_CFG2 register ************/
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_0_Pos  (0U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_0_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_0_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_0  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_0_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_1_Pos  (4U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_1_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_1_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_1  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_1_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_2_Pos  (8U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_2_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_2_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_2  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_2_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_3_Pos  (12U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_3_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_3_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_3  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_3_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_4_Pos  (16U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_4_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_4_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_4  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_4_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_5_Pos  (20U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_5_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_5_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_5  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_5_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_6_Pos  (24U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_6_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_6_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_6  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_6_Msk
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_7_Pos  (28U)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_7_Msk  (0xFUL << BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_7_Pos)
#define BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_7  BT_PHY_EDR_TMXBUF_GC_CFG2_EDR_TMXBUF_GC_7_Msk

/************** Bit definition for BT_PHY_TX_DPSK_CFG1 register ***************/
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_Pos  (0U)
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_Pos)
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC  BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_Msk
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_EN_Pos  (8U)
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_EN_Msk  (0x1UL << BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_EN_Pos)
#define BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_EN  BT_PHY_TX_DPSK_CFG1_TX_DPSK_GAIN_FRC_EN_Msk

/************** Bit definition for BT_PHY_TX_DPSK_CFG2 register ***************/
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_0_Pos  (0U)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_0_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_0_Pos)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_0  BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_0_Msk
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_1_Pos  (8U)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_1_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_1_Pos)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_1  BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_1_Msk
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_2_Pos  (16U)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_2_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_2_Pos)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_2  BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_2_Msk
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_3_Pos  (24U)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_3_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_3_Pos)
#define BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_3  BT_PHY_TX_DPSK_CFG2_TX_DPSK_GAIN_3_Msk

/************** Bit definition for BT_PHY_TX_DPSK_CFG3 register ***************/
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_4_Pos  (0U)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_4_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_4_Pos)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_4  BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_4_Msk
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_5_Pos  (8U)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_5_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_5_Pos)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_5  BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_5_Msk
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_6_Pos  (16U)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_6_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_6_Pos)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_6  BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_6_Msk
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_7_Pos  (24U)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_7_Msk  (0xFFUL << BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_7_Pos)
#define BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_7  BT_PHY_TX_DPSK_CFG3_TX_DPSK_GAIN_7_Msk

/************* Bit definition for BT_PHY_TX_EDR_LPF_CFG register **************/
#define BT_PHY_TX_EDR_LPF_CFG_TX_EDR_LPF_BYPASS_Pos  (0U)
#define BT_PHY_TX_EDR_LPF_CFG_TX_EDR_LPF_BYPASS_Msk  (0x1UL << BT_PHY_TX_EDR_LPF_CFG_TX_EDR_LPF_BYPASS_Pos)
#define BT_PHY_TX_EDR_LPF_CFG_TX_EDR_LPF_BYPASS  BT_PHY_TX_EDR_LPF_CFG_TX_EDR_LPF_BYPASS_Msk

/************* Bit definition for BT_PHY_TX_DC_CAL_CFG0 register **************/
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE0_Pos  (0U)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE0_Msk  (0x3FFUL << BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE0_Pos)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE0  BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE0_Msk
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE1_Pos  (10U)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE1_Msk  (0x3FFUL << BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE1_Pos)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE1  BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_PHASE1_Msk
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_EN_Pos  (20U)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_EN_Msk  (0x1UL << BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_EN_Pos)
#define BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_EN  BT_PHY_TX_DC_CAL_CFG0_TX_DC_CAL_EN_Msk

/************* Bit definition for BT_PHY_TX_DC_CAL_CFG1 register **************/
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT0_Pos  (0U)
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT0_Msk  (0x3FFUL << BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT0_Pos)
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT0  BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT0_Msk
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT1_Pos  (10U)
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT1_Msk  (0x3FFUL << BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT1_Pos)
#define BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT1  BT_PHY_TX_DC_CAL_CFG1_TX_DC_CAL_PHASE_INIT1_Msk

/************* Bit definition for BT_PHY_TX_DC_CAL_CFG2 register **************/
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN0_Pos  (0U)
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN0_Msk  (0xFFUL << BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN0_Pos)
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN0  BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN0_Msk
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN1_Pos  (8U)
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN1_Msk  (0xFFUL << BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN1_Pos)
#define BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN1  BT_PHY_TX_DC_CAL_CFG2_TX_DC_CAL_GAIN1_Msk

/************* Bit definition for BT_PHY_TX_DC_CAL_CFG3 register **************/
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF0_Pos  (0U)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF0_Msk  (0x3FFFUL << BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF0_Pos)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF0  BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF0_Msk
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF1_Pos  (14U)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF1_Msk  (0x3FFFUL << BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF1_Pos)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF1  BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF1_Msk
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF_FRC_EN_Pos  (28U)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF_FRC_EN_Msk  (0x1UL << BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF_FRC_EN_Pos)
#define BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF_FRC_EN  BT_PHY_TX_DC_CAL_CFG3_TX_DC_CAL_COEF_FRC_EN_Msk

/************* Bit definition for BT_PHY_TX_DC_CAL_CFG4 register **************/
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_Q_Pos  (0U)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_Q_Msk  (0x7FFUL << BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_Q_Pos)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_Q  BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_Q_Msk
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_I_Pos  (11U)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_I_Msk  (0x7FFUL << BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_I_Pos)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_I  BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_I_Msk
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_FRC_EN_Pos  (22U)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_FRC_EN_Msk  (0x1UL << BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_FRC_EN_Pos)
#define BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_FRC_EN  BT_PHY_TX_DC_CAL_CFG4_TX_DC_CAL_OFFSET_FRC_EN_Msk
#define BT_PHY_TX_DC_CAL_CFG4_TX_IQ_SWAP_Pos  (23U)
#define BT_PHY_TX_DC_CAL_CFG4_TX_IQ_SWAP_Msk  (0x1UL << BT_PHY_TX_DC_CAL_CFG4_TX_IQ_SWAP_Pos)
#define BT_PHY_TX_DC_CAL_CFG4_TX_IQ_SWAP  BT_PHY_TX_DC_CAL_CFG4_TX_IQ_SWAP_Msk

/************* Bit definition for BT_PHY_LFP_MMDIV_CFG0 register **************/
#define BT_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Pos  (0U)
#define BT_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Msk  (0x1FFFFUL << BT_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Pos)
#define BT_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M  BT_PHY_LFP_MMDIV_CFG0_RX_MMDIV_OFFSET_1M_Msk

/************* Bit definition for BT_PHY_LFP_MMDIV_CFG1 register **************/
#define BT_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Pos  (0U)
#define BT_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Msk  (0x1FFFFUL << BT_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Pos)
#define BT_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M  BT_PHY_LFP_MMDIV_CFG1_RX_MMDIV_OFFSET_2M_Msk

/************* Bit definition for BT_PHY_LFP_MMDIV_CFG2 register **************/
#define BT_PHY_LFP_MMDIV_CFG2_RX_MMDIV_OFFSET_BT_Pos  (0U)
#define BT_PHY_LFP_MMDIV_CFG2_RX_MMDIV_OFFSET_BT_Msk  (0x1FFFFUL << BT_PHY_LFP_MMDIV_CFG2_RX_MMDIV_OFFSET_BT_Pos)
#define BT_PHY_LFP_MMDIV_CFG2_RX_MMDIV_OFFSET_BT  BT_PHY_LFP_MMDIV_CFG2_RX_MMDIV_OFFSET_BT_Msk

/************* Bit definition for BT_PHY_LFP_MMDIV_CFG3 register **************/
#define BT_PHY_LFP_MMDIV_CFG3_TX_MMDIV_OFFSET_Pos  (0U)
#define BT_PHY_LFP_MMDIV_CFG3_TX_MMDIV_OFFSET_Msk  (0x1FFFFUL << BT_PHY_LFP_MMDIV_CFG3_TX_MMDIV_OFFSET_Pos)
#define BT_PHY_LFP_MMDIV_CFG3_TX_MMDIV_OFFSET  BT_PHY_LFP_MMDIV_CFG3_TX_MMDIV_OFFSET_Msk

/************* Bit definition for BT_PHY_LFP_MMDIV_CFG4 register **************/
#define BT_PHY_LFP_MMDIV_CFG4_RF_MMDIV_TEST_Pos  (0U)
#define BT_PHY_LFP_MMDIV_CFG4_RF_MMDIV_TEST_Msk  (0x1FFFFFFUL << BT_PHY_LFP_MMDIV_CFG4_RF_MMDIV_TEST_Pos)
#define BT_PHY_LFP_MMDIV_CFG4_RF_MMDIV_TEST  BT_PHY_LFP_MMDIV_CFG4_RF_MMDIV_TEST_Msk
#define BT_PHY_LFP_MMDIV_CFG4_RF_TEST_MODE_Pos  (25U)
#define BT_PHY_LFP_MMDIV_CFG4_RF_TEST_MODE_Msk  (0x1UL << BT_PHY_LFP_MMDIV_CFG4_RF_TEST_MODE_Pos)
#define BT_PHY_LFP_MMDIV_CFG4_RF_TEST_MODE  BT_PHY_LFP_MMDIV_CFG4_RF_TEST_MODE_Msk

/*************** Bit definition for BT_PHY_RX_HFP_CFG register ****************/
#define BT_PHY_RX_HFP_CFG_RX_HFP_FCW_Pos  (0U)
#define BT_PHY_RX_HFP_CFG_RX_HFP_FCW_Msk  (0x3FUL << BT_PHY_RX_HFP_CFG_RX_HFP_FCW_Pos)
#define BT_PHY_RX_HFP_CFG_RX_HFP_FCW    BT_PHY_RX_HFP_CFG_RX_HFP_FCW_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL0 register **************/
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Pos)
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_0  BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_0_Msk
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Pos)
#define BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_1  BT_PHY_LNA_GAIN_TBL0_LNA_GAIN_1_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL1 register **************/
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Pos)
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_2  BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_2_Msk
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Pos)
#define BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_3  BT_PHY_LNA_GAIN_TBL1_LNA_GAIN_3_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL2 register **************/
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Pos)
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_4  BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_4_Msk
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Pos)
#define BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_5  BT_PHY_LNA_GAIN_TBL2_LNA_GAIN_5_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL3 register **************/
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Pos)
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_6  BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_6_Msk
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Pos)
#define BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_7  BT_PHY_LNA_GAIN_TBL3_LNA_GAIN_7_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL4 register **************/
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Pos)
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_8  BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_8_Msk
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Pos)
#define BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_9  BT_PHY_LNA_GAIN_TBL4_LNA_GAIN_9_Msk

/************** Bit definition for BT_PHY_LNA_GAIN_TBL5 register **************/
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Pos  (0U)
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Pos)
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_10  BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_10_Msk
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Pos  (11U)
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Msk  (0x7FFUL << BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Pos)
#define BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_11  BT_PHY_LNA_GAIN_TBL5_LNA_GAIN_11_Msk

/************** Bit definition for BT_PHY_DCCAL_MPT_CFG register **************/
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_EN_Pos  (0U)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_EN_Msk  (0x1UL << BT_PHY_DCCAL_MPT_CFG_DC_EST_EN_Pos)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_EN  BT_PHY_DCCAL_MPT_CFG_DC_EST_EN_Msk
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_MU_Pos  (1U)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_MU_Msk  (0x7UL << BT_PHY_DCCAL_MPT_CFG_DC_EST_MU_Pos)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_MU  BT_PHY_DCCAL_MPT_CFG_DC_EST_MU_Msk
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_LOW_Pos  (4U)
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_LOW_Msk  (0xFUL << BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_LOW_Pos)
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_LOW  BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_LOW_Msk
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_HIGH_Pos  (8U)
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_HIGH_Msk  (0xFUL << BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_HIGH_Pos)
#define BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_HIGH  BT_PHY_DCCAL_MPT_CFG_MP_LNA_GAIN_INDEX_HIGH_Msk
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_BYPASS_Pos  (12U)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_BYPASS_Msk  (0x1UL << BT_PHY_DCCAL_MPT_CFG_DC_EST_BYPASS_Pos)
#define BT_PHY_DCCAL_MPT_CFG_DC_EST_BYPASS  BT_PHY_DCCAL_MPT_CFG_DC_EST_BYPASS_Msk
#define BT_PHY_DCCAL_MPT_CFG_TX_DC_CAL_Pos  (13U)
#define BT_PHY_DCCAL_MPT_CFG_TX_DC_CAL_Msk  (0x1UL << BT_PHY_DCCAL_MPT_CFG_TX_DC_CAL_Pos)
#define BT_PHY_DCCAL_MPT_CFG_TX_DC_CAL  BT_PHY_DCCAL_MPT_CFG_TX_DC_CAL_Msk

/*************** Bit definition for BT_PHY_DCCAL_RSLT1 register ***************/
#define BT_PHY_DCCAL_RSLT1_DC_OUT_I_Pos  (0U)
#define BT_PHY_DCCAL_RSLT1_DC_OUT_I_Msk  (0xFFFUL << BT_PHY_DCCAL_RSLT1_DC_OUT_I_Pos)
#define BT_PHY_DCCAL_RSLT1_DC_OUT_I     BT_PHY_DCCAL_RSLT1_DC_OUT_I_Msk
#define BT_PHY_DCCAL_RSLT1_DC_OUT_Q_Pos  (16U)
#define BT_PHY_DCCAL_RSLT1_DC_OUT_Q_Msk  (0xFFFUL << BT_PHY_DCCAL_RSLT1_DC_OUT_Q_Pos)
#define BT_PHY_DCCAL_RSLT1_DC_OUT_Q     BT_PHY_DCCAL_RSLT1_DC_OUT_Q_Msk

/*************** Bit definition for BT_PHY_DCCAL_RSLT2 register ***************/
#define BT_PHY_DCCAL_RSLT2_DC_OUT_Pos   (0U)
#define BT_PHY_DCCAL_RSLT2_DC_OUT_Msk   (0xFFFFFFUL << BT_PHY_DCCAL_RSLT2_DC_OUT_Pos)
#define BT_PHY_DCCAL_RSLT2_DC_OUT       BT_PHY_DCCAL_RSLT2_DC_OUT_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG0 register ****************/
#define BT_PHY_RCOS_CFG0_RCOS_COEF_0_Pos  (0U)
#define BT_PHY_RCOS_CFG0_RCOS_COEF_0_Msk  (0xFFFUL << BT_PHY_RCOS_CFG0_RCOS_COEF_0_Pos)
#define BT_PHY_RCOS_CFG0_RCOS_COEF_0    BT_PHY_RCOS_CFG0_RCOS_COEF_0_Msk
#define BT_PHY_RCOS_CFG0_RCOS_COEF_1_Pos  (12U)
#define BT_PHY_RCOS_CFG0_RCOS_COEF_1_Msk  (0xFFFUL << BT_PHY_RCOS_CFG0_RCOS_COEF_1_Pos)
#define BT_PHY_RCOS_CFG0_RCOS_COEF_1    BT_PHY_RCOS_CFG0_RCOS_COEF_1_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG1 register ****************/
#define BT_PHY_RCOS_CFG1_RCOS_COEF_2_Pos  (0U)
#define BT_PHY_RCOS_CFG1_RCOS_COEF_2_Msk  (0xFFFUL << BT_PHY_RCOS_CFG1_RCOS_COEF_2_Pos)
#define BT_PHY_RCOS_CFG1_RCOS_COEF_2    BT_PHY_RCOS_CFG1_RCOS_COEF_2_Msk
#define BT_PHY_RCOS_CFG1_RCOS_COEF_3_Pos  (12U)
#define BT_PHY_RCOS_CFG1_RCOS_COEF_3_Msk  (0xFFFUL << BT_PHY_RCOS_CFG1_RCOS_COEF_3_Pos)
#define BT_PHY_RCOS_CFG1_RCOS_COEF_3    BT_PHY_RCOS_CFG1_RCOS_COEF_3_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG2 register ****************/
#define BT_PHY_RCOS_CFG2_RCOS_COEF_4_Pos  (0U)
#define BT_PHY_RCOS_CFG2_RCOS_COEF_4_Msk  (0xFFFUL << BT_PHY_RCOS_CFG2_RCOS_COEF_4_Pos)
#define BT_PHY_RCOS_CFG2_RCOS_COEF_4    BT_PHY_RCOS_CFG2_RCOS_COEF_4_Msk
#define BT_PHY_RCOS_CFG2_RCOS_COEF_5_Pos  (12U)
#define BT_PHY_RCOS_CFG2_RCOS_COEF_5_Msk  (0xFFFUL << BT_PHY_RCOS_CFG2_RCOS_COEF_5_Pos)
#define BT_PHY_RCOS_CFG2_RCOS_COEF_5    BT_PHY_RCOS_CFG2_RCOS_COEF_5_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG3 register ****************/
#define BT_PHY_RCOS_CFG3_RCOS_COEF_6_Pos  (0U)
#define BT_PHY_RCOS_CFG3_RCOS_COEF_6_Msk  (0xFFFUL << BT_PHY_RCOS_CFG3_RCOS_COEF_6_Pos)
#define BT_PHY_RCOS_CFG3_RCOS_COEF_6    BT_PHY_RCOS_CFG3_RCOS_COEF_6_Msk
#define BT_PHY_RCOS_CFG3_RCOS_COEF_7_Pos  (12U)
#define BT_PHY_RCOS_CFG3_RCOS_COEF_7_Msk  (0xFFFUL << BT_PHY_RCOS_CFG3_RCOS_COEF_7_Pos)
#define BT_PHY_RCOS_CFG3_RCOS_COEF_7    BT_PHY_RCOS_CFG3_RCOS_COEF_7_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG4 register ****************/
#define BT_PHY_RCOS_CFG4_RCOS_COEF_8_Pos  (0U)
#define BT_PHY_RCOS_CFG4_RCOS_COEF_8_Msk  (0xFFFUL << BT_PHY_RCOS_CFG4_RCOS_COEF_8_Pos)
#define BT_PHY_RCOS_CFG4_RCOS_COEF_8    BT_PHY_RCOS_CFG4_RCOS_COEF_8_Msk
#define BT_PHY_RCOS_CFG4_RCOS_COEF_9_Pos  (12U)
#define BT_PHY_RCOS_CFG4_RCOS_COEF_9_Msk  (0xFFFUL << BT_PHY_RCOS_CFG4_RCOS_COEF_9_Pos)
#define BT_PHY_RCOS_CFG4_RCOS_COEF_9    BT_PHY_RCOS_CFG4_RCOS_COEF_9_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG5 register ****************/
#define BT_PHY_RCOS_CFG5_RCOS_COEF_10_Pos  (0U)
#define BT_PHY_RCOS_CFG5_RCOS_COEF_10_Msk  (0xFFFUL << BT_PHY_RCOS_CFG5_RCOS_COEF_10_Pos)
#define BT_PHY_RCOS_CFG5_RCOS_COEF_10   BT_PHY_RCOS_CFG5_RCOS_COEF_10_Msk
#define BT_PHY_RCOS_CFG5_RCOS_COEF_11_Pos  (12U)
#define BT_PHY_RCOS_CFG5_RCOS_COEF_11_Msk  (0xFFFUL << BT_PHY_RCOS_CFG5_RCOS_COEF_11_Pos)
#define BT_PHY_RCOS_CFG5_RCOS_COEF_11   BT_PHY_RCOS_CFG5_RCOS_COEF_11_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG6 register ****************/
#define BT_PHY_RCOS_CFG6_RCOS_COEF_12_Pos  (0U)
#define BT_PHY_RCOS_CFG6_RCOS_COEF_12_Msk  (0xFFFUL << BT_PHY_RCOS_CFG6_RCOS_COEF_12_Pos)
#define BT_PHY_RCOS_CFG6_RCOS_COEF_12   BT_PHY_RCOS_CFG6_RCOS_COEF_12_Msk
#define BT_PHY_RCOS_CFG6_RCOS_COEF_13_Pos  (12U)
#define BT_PHY_RCOS_CFG6_RCOS_COEF_13_Msk  (0xFFFUL << BT_PHY_RCOS_CFG6_RCOS_COEF_13_Pos)
#define BT_PHY_RCOS_CFG6_RCOS_COEF_13   BT_PHY_RCOS_CFG6_RCOS_COEF_13_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG7 register ****************/
#define BT_PHY_RCOS_CFG7_RCOS_COEF_14_Pos  (0U)
#define BT_PHY_RCOS_CFG7_RCOS_COEF_14_Msk  (0xFFFUL << BT_PHY_RCOS_CFG7_RCOS_COEF_14_Pos)
#define BT_PHY_RCOS_CFG7_RCOS_COEF_14   BT_PHY_RCOS_CFG7_RCOS_COEF_14_Msk
#define BT_PHY_RCOS_CFG7_RCOS_COEF_15_Pos  (12U)
#define BT_PHY_RCOS_CFG7_RCOS_COEF_15_Msk  (0xFFFUL << BT_PHY_RCOS_CFG7_RCOS_COEF_15_Pos)
#define BT_PHY_RCOS_CFG7_RCOS_COEF_15   BT_PHY_RCOS_CFG7_RCOS_COEF_15_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG8 register ****************/
#define BT_PHY_RCOS_CFG8_RCOS_COEF_16_Pos  (0U)
#define BT_PHY_RCOS_CFG8_RCOS_COEF_16_Msk  (0xFFFUL << BT_PHY_RCOS_CFG8_RCOS_COEF_16_Pos)
#define BT_PHY_RCOS_CFG8_RCOS_COEF_16   BT_PHY_RCOS_CFG8_RCOS_COEF_16_Msk
#define BT_PHY_RCOS_CFG8_RCOS_COEF_17_Pos  (12U)
#define BT_PHY_RCOS_CFG8_RCOS_COEF_17_Msk  (0xFFFUL << BT_PHY_RCOS_CFG8_RCOS_COEF_17_Pos)
#define BT_PHY_RCOS_CFG8_RCOS_COEF_17   BT_PHY_RCOS_CFG8_RCOS_COEF_17_Msk

/**************** Bit definition for BT_PHY_RCOS_CFG9 register ****************/
#define BT_PHY_RCOS_CFG9_RCOS_COEF_18_Pos  (0U)
#define BT_PHY_RCOS_CFG9_RCOS_COEF_18_Msk  (0xFFFUL << BT_PHY_RCOS_CFG9_RCOS_COEF_18_Pos)
#define BT_PHY_RCOS_CFG9_RCOS_COEF_18   BT_PHY_RCOS_CFG9_RCOS_COEF_18_Msk
#define BT_PHY_RCOS_CFG9_RCOS_COEF_19_Pos  (12U)
#define BT_PHY_RCOS_CFG9_RCOS_COEF_19_Msk  (0xFFFUL << BT_PHY_RCOS_CFG9_RCOS_COEF_19_Pos)
#define BT_PHY_RCOS_CFG9_RCOS_COEF_19   BT_PHY_RCOS_CFG9_RCOS_COEF_19_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG10 register ****************/
#define BT_PHY_RCOS_CFG10_RCOS_COEF_20_Pos  (0U)
#define BT_PHY_RCOS_CFG10_RCOS_COEF_20_Msk  (0xFFFUL << BT_PHY_RCOS_CFG10_RCOS_COEF_20_Pos)
#define BT_PHY_RCOS_CFG10_RCOS_COEF_20  BT_PHY_RCOS_CFG10_RCOS_COEF_20_Msk
#define BT_PHY_RCOS_CFG10_RCOS_COEF_21_Pos  (12U)
#define BT_PHY_RCOS_CFG10_RCOS_COEF_21_Msk  (0xFFFUL << BT_PHY_RCOS_CFG10_RCOS_COEF_21_Pos)
#define BT_PHY_RCOS_CFG10_RCOS_COEF_21  BT_PHY_RCOS_CFG10_RCOS_COEF_21_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG11 register ****************/
#define BT_PHY_RCOS_CFG11_RCOS_COEF_22_Pos  (0U)
#define BT_PHY_RCOS_CFG11_RCOS_COEF_22_Msk  (0xFFFUL << BT_PHY_RCOS_CFG11_RCOS_COEF_22_Pos)
#define BT_PHY_RCOS_CFG11_RCOS_COEF_22  BT_PHY_RCOS_CFG11_RCOS_COEF_22_Msk
#define BT_PHY_RCOS_CFG11_RCOS_COEF_23_Pos  (12U)
#define BT_PHY_RCOS_CFG11_RCOS_COEF_23_Msk  (0xFFFUL << BT_PHY_RCOS_CFG11_RCOS_COEF_23_Pos)
#define BT_PHY_RCOS_CFG11_RCOS_COEF_23  BT_PHY_RCOS_CFG11_RCOS_COEF_23_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG12 register ****************/
#define BT_PHY_RCOS_CFG12_RCOS_COEF_24_Pos  (0U)
#define BT_PHY_RCOS_CFG12_RCOS_COEF_24_Msk  (0xFFFUL << BT_PHY_RCOS_CFG12_RCOS_COEF_24_Pos)
#define BT_PHY_RCOS_CFG12_RCOS_COEF_24  BT_PHY_RCOS_CFG12_RCOS_COEF_24_Msk
#define BT_PHY_RCOS_CFG12_RCOS_COEF_25_Pos  (12U)
#define BT_PHY_RCOS_CFG12_RCOS_COEF_25_Msk  (0xFFFUL << BT_PHY_RCOS_CFG12_RCOS_COEF_25_Pos)
#define BT_PHY_RCOS_CFG12_RCOS_COEF_25  BT_PHY_RCOS_CFG12_RCOS_COEF_25_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG13 register ****************/
#define BT_PHY_RCOS_CFG13_RCOS_COEF_26_Pos  (0U)
#define BT_PHY_RCOS_CFG13_RCOS_COEF_26_Msk  (0xFFFUL << BT_PHY_RCOS_CFG13_RCOS_COEF_26_Pos)
#define BT_PHY_RCOS_CFG13_RCOS_COEF_26  BT_PHY_RCOS_CFG13_RCOS_COEF_26_Msk
#define BT_PHY_RCOS_CFG13_RCOS_COEF_27_Pos  (12U)
#define BT_PHY_RCOS_CFG13_RCOS_COEF_27_Msk  (0xFFFUL << BT_PHY_RCOS_CFG13_RCOS_COEF_27_Pos)
#define BT_PHY_RCOS_CFG13_RCOS_COEF_27  BT_PHY_RCOS_CFG13_RCOS_COEF_27_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG14 register ****************/
#define BT_PHY_RCOS_CFG14_RCOS_COEF_28_Pos  (0U)
#define BT_PHY_RCOS_CFG14_RCOS_COEF_28_Msk  (0xFFFUL << BT_PHY_RCOS_CFG14_RCOS_COEF_28_Pos)
#define BT_PHY_RCOS_CFG14_RCOS_COEF_28  BT_PHY_RCOS_CFG14_RCOS_COEF_28_Msk
#define BT_PHY_RCOS_CFG14_RCOS_COEF_29_Pos  (12U)
#define BT_PHY_RCOS_CFG14_RCOS_COEF_29_Msk  (0xFFFUL << BT_PHY_RCOS_CFG14_RCOS_COEF_29_Pos)
#define BT_PHY_RCOS_CFG14_RCOS_COEF_29  BT_PHY_RCOS_CFG14_RCOS_COEF_29_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG15 register ****************/
#define BT_PHY_RCOS_CFG15_RCOS_COEF_30_Pos  (0U)
#define BT_PHY_RCOS_CFG15_RCOS_COEF_30_Msk  (0xFFFUL << BT_PHY_RCOS_CFG15_RCOS_COEF_30_Pos)
#define BT_PHY_RCOS_CFG15_RCOS_COEF_30  BT_PHY_RCOS_CFG15_RCOS_COEF_30_Msk
#define BT_PHY_RCOS_CFG15_RCOS_COEF_31_Pos  (12U)
#define BT_PHY_RCOS_CFG15_RCOS_COEF_31_Msk  (0xFFFUL << BT_PHY_RCOS_CFG15_RCOS_COEF_31_Pos)
#define BT_PHY_RCOS_CFG15_RCOS_COEF_31  BT_PHY_RCOS_CFG15_RCOS_COEF_31_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG16 register ****************/
#define BT_PHY_RCOS_CFG16_RCOS_COEF_32_Pos  (0U)
#define BT_PHY_RCOS_CFG16_RCOS_COEF_32_Msk  (0xFFFUL << BT_PHY_RCOS_CFG16_RCOS_COEF_32_Pos)
#define BT_PHY_RCOS_CFG16_RCOS_COEF_32  BT_PHY_RCOS_CFG16_RCOS_COEF_32_Msk
#define BT_PHY_RCOS_CFG16_RCOS_COEF_33_Pos  (12U)
#define BT_PHY_RCOS_CFG16_RCOS_COEF_33_Msk  (0xFFFUL << BT_PHY_RCOS_CFG16_RCOS_COEF_33_Pos)
#define BT_PHY_RCOS_CFG16_RCOS_COEF_33  BT_PHY_RCOS_CFG16_RCOS_COEF_33_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG17 register ****************/
#define BT_PHY_RCOS_CFG17_RCOS_COEF_34_Pos  (0U)
#define BT_PHY_RCOS_CFG17_RCOS_COEF_34_Msk  (0xFFFUL << BT_PHY_RCOS_CFG17_RCOS_COEF_34_Pos)
#define BT_PHY_RCOS_CFG17_RCOS_COEF_34  BT_PHY_RCOS_CFG17_RCOS_COEF_34_Msk
#define BT_PHY_RCOS_CFG17_RCOS_COEF_35_Pos  (12U)
#define BT_PHY_RCOS_CFG17_RCOS_COEF_35_Msk  (0xFFFUL << BT_PHY_RCOS_CFG17_RCOS_COEF_35_Pos)
#define BT_PHY_RCOS_CFG17_RCOS_COEF_35  BT_PHY_RCOS_CFG17_RCOS_COEF_35_Msk

/*************** Bit definition for BT_PHY_RCOS_CFG18 register ****************/
#define BT_PHY_RCOS_CFG18_RCOS_COEF_36_Pos  (0U)
#define BT_PHY_RCOS_CFG18_RCOS_COEF_36_Msk  (0xFFFUL << BT_PHY_RCOS_CFG18_RCOS_COEF_36_Pos)
#define BT_PHY_RCOS_CFG18_RCOS_COEF_36  BT_PHY_RCOS_CFG18_RCOS_COEF_36_Msk

#endif
