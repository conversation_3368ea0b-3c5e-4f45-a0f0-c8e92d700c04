#ifndef __BT_RFC_H
#define __BT_RFC_H

typedef struct
{
    __IO uint32_t DCO_REG1;
    __IO uint32_t DCO_REG2;
    __IO uint32_t DCO_REG3;
    __IO uint32_t MISC_CTRL_REG;
    __IO uint32_t RF_LODIST_REG;
    __IO uint32_t FBDV_REG1;
    __IO uint32_t FBDV_REG2;
    __IO uint32_t PFDCP_REG;
    __IO uint32_t LPF_REG;
    __IO uint32_t EDR_PLL_REG1;
    __IO uint32_t EDR_PLL_REG2;
    __IO uint32_t EDR_PLL_REG3;
    __IO uint32_t EDR_PLL_REG4;
    __IO uint32_t EDR_PLL_REG5;
    __IO uint32_t EDR_PLL_REG6;
    __IO uint32_t EDR_PLL_REG7;
    __IO uint32_t EDR_CAL_REG1;
    __IO uint32_t EDR_OSLO_REG;
    __IO uint32_t ATEST_REG;
    __IO uint32_t DTEST_REG;
    __IO uint32_t TRF_REG1;
    __IO uint32_t TRF_REG2;
    __IO uint32_t TRF_EDR_REG1;
    __IO uint32_t TRF_EDR_REG2;
    __IO uint32_t RRF_REG;
    __IO uint32_t RBB_REG1;
    __IO uint32_t RBB_REG2;
    __IO uint32_t RBB_REG3;
    __IO uint32_t RBB_REG4;
    __IO uint32_t RBB_REG5;
    __IO uint32_t RBB_REG6;
    __IO uint32_t ADC_REG;
    __IO uint32_t TBB_REG;
    __IO uint32_t ATSTBUF_REG;
    __IO uint32_t RSVD_REG1;
    __IO uint32_t RSVD_REG2;
    __IO uint32_t INCCAL_REG1;
    __IO uint32_t INCCAL_REG2;
    __IO uint32_t ROSCAL_REG1;
    __IO uint32_t ROSCAL_REG2;
    __IO uint32_t RCROSCAL_REG;
    __IO uint32_t PACAL_REG;
    __IO uint32_t CU_ADDR_REG1;
    __IO uint32_t CU_ADDR_REG2;
    __IO uint32_t CU_ADDR_REG3;
    __IO uint32_t CAL_ADDR_REG1;
    __IO uint32_t CAL_ADDR_REG2;
    __IO uint32_t CAL_ADDR_REG3;
    __IO uint32_t AGC_REG;
    __IO uint32_t TXDC_CAL_REG1;
    __IO uint32_t TXDC_CAL_REG2;
} BT_RFC_TypeDef;


/**************** Bit definition for BT_RFC_DCO_REG1 register *****************/
#define BT_RFC_DCO_REG1_BRF_EN_2M_MOD_LV_Pos  (0U)
#define BT_RFC_DCO_REG1_BRF_EN_2M_MOD_LV_Msk  (0x1UL << BT_RFC_DCO_REG1_BRF_EN_2M_MOD_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_EN_2M_MOD_LV  BT_RFC_DCO_REG1_BRF_EN_2M_MOD_LV_Msk
#define BT_RFC_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Pos  (1U)
#define BT_RFC_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Msk  (0x7UL << BT_RFC_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV  BT_RFC_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Msk
#define BT_RFC_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Pos  (4U)
#define BT_RFC_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Msk  (0x7UL << BT_RFC_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV  BT_RFC_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Msk
#define BT_RFC_DCO_REG1_BRF_VCO_FLR_EN_LV_Pos  (7U)
#define BT_RFC_DCO_REG1_BRF_VCO_FLR_EN_LV_Msk  (0x1UL << BT_RFC_DCO_REG1_BRF_VCO_FLR_EN_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_VCO_FLR_EN_LV  BT_RFC_DCO_REG1_BRF_VCO_FLR_EN_LV_Msk
#define BT_RFC_DCO_REG1_BRF_VCO_LDO_VREF_LV_Pos  (8U)
#define BT_RFC_DCO_REG1_BRF_VCO_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_DCO_REG1_BRF_VCO_LDO_VREF_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_VCO_LDO_VREF_LV  BT_RFC_DCO_REG1_BRF_VCO_LDO_VREF_LV_Msk
#define BT_RFC_DCO_REG1_BRF_VCO_EN_LV_Pos  (12U)
#define BT_RFC_DCO_REG1_BRF_VCO_EN_LV_Msk  (0x1UL << BT_RFC_DCO_REG1_BRF_VCO_EN_LV_Pos)
#define BT_RFC_DCO_REG1_BRF_VCO_EN_LV   BT_RFC_DCO_REG1_BRF_VCO_EN_LV_Msk

/**************** Bit definition for BT_RFC_DCO_REG2 register *****************/
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_UP_LV_Pos  (0U)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_UP_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_ACAL_UP_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_UP_LV  BT_RFC_DCO_REG2_BRF_VCO_ACAL_UP_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Pos  (1U)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_INCAL_LV  BT_RFC_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Pos  (2U)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_UP_LV  BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Pos  (3U)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV  BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Pos  (4U)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Msk  (0xFUL << BT_RFC_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV  BT_RFC_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Pos  (8U)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Msk  (0xFUL << BT_RFC_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV  BT_RFC_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_EN_LV_Pos  (12U)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_EN_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_ACAL_EN_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_ACAL_EN_LV  BT_RFC_DCO_REG2_BRF_VCO_ACAL_EN_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Pos  (13U)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Msk  (0x7UL << BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV  BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Pos  (16U)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Msk  (0x7UL << BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV  BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Pos  (19U)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_EN_LV  BT_RFC_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Pos  (20U)
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Msk  (0x7UL << BT_RFC_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV  BT_RFC_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Msk
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Pos  (23U)
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_VCO_FKCAL_EN_LV  BT_RFC_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Msk
#define BT_RFC_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Pos  (24U)
#define BT_RFC_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Msk  (0x1UL << BT_RFC_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Pos)
#define BT_RFC_DCO_REG2_BRF_EN_MOD_INPHASE_LV  BT_RFC_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Msk

/**************** Bit definition for BT_RFC_DCO_REG3 register *****************/
#define BT_RFC_DCO_REG3_BRF_VCO_PDX_LV_Pos  (0U)
#define BT_RFC_DCO_REG3_BRF_VCO_PDX_LV_Msk  (0xFFUL << BT_RFC_DCO_REG3_BRF_VCO_PDX_LV_Pos)
#define BT_RFC_DCO_REG3_BRF_VCO_PDX_LV  BT_RFC_DCO_REG3_BRF_VCO_PDX_LV_Msk
#define BT_RFC_DCO_REG3_BRF_VCO_IDAC_LV_Pos  (8U)
#define BT_RFC_DCO_REG3_BRF_VCO_IDAC_LV_Msk  (0x7FUL << BT_RFC_DCO_REG3_BRF_VCO_IDAC_LV_Pos)
#define BT_RFC_DCO_REG3_BRF_VCO_IDAC_LV  BT_RFC_DCO_REG3_BRF_VCO_IDAC_LV_Msk
#define BT_RFC_DCO_REG3_TX_KCAL_Pos     (16U)
#define BT_RFC_DCO_REG3_TX_KCAL_Msk     (0xFFFUL << BT_RFC_DCO_REG3_TX_KCAL_Pos)
#define BT_RFC_DCO_REG3_TX_KCAL         BT_RFC_DCO_REG3_TX_KCAL_Msk

/************** Bit definition for BT_RFC_MISC_CTRL_REG register **************/
#define BT_RFC_MISC_CTRL_REG_PDX_FORCE_EN_Pos  (0U)
#define BT_RFC_MISC_CTRL_REG_PDX_FORCE_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_PDX_FORCE_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_PDX_FORCE_EN  BT_RFC_MISC_CTRL_REG_PDX_FORCE_EN_Msk
#define BT_RFC_MISC_CTRL_REG_IDAC_FORCE_EN_Pos  (1U)
#define BT_RFC_MISC_CTRL_REG_IDAC_FORCE_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_IDAC_FORCE_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_IDAC_FORCE_EN  BT_RFC_MISC_CTRL_REG_IDAC_FORCE_EN_Msk
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_Pos  (2U)
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN  BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_Pos  (3U)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN  BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Pos  (4U)
#define BT_RFC_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL  BT_RFC_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Msk
#define BT_RFC_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Pos  (5U)
#define BT_RFC_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Pos)
#define BT_RFC_MISC_CTRL_REG_UNLOCK_FLAG_CLR  BT_RFC_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Msk
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Pos  (6U)
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN  BT_RFC_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Pos  (7U)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN  BT_RFC_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_Pos  (8U)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL  BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Pos  (9U)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN  BT_RFC_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_CBPF_BW_FRC_EN_Pos  (10U)
#define BT_RFC_MISC_CTRL_REG_CBPF_BW_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_CBPF_BW_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_CBPF_BW_FRC_EN  BT_RFC_MISC_CTRL_REG_CBPF_BW_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Pos  (11U)
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN  BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Pos  (12U)
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN  BT_RFC_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Pos  (13U)
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN  BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Pos  (14U)
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN  BT_RFC_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Pos  (15U)
#define BT_RFC_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN  BT_RFC_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Msk
#define BT_RFC_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Pos  (16U)
#define BT_RFC_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_XTAL_RFCH_SEL_EN  BT_RFC_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Msk
#define BT_RFC_MISC_CTRL_REG_IQ_SWAP_EN_Pos  (17U)
#define BT_RFC_MISC_CTRL_REG_IQ_SWAP_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_IQ_SWAP_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_IQ_SWAP_EN  BT_RFC_MISC_CTRL_REG_IQ_SWAP_EN_Msk
#define BT_RFC_MISC_CTRL_REG_BT_XTAL_REF_EN_Pos  (18U)
#define BT_RFC_MISC_CTRL_REG_BT_XTAL_REF_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_BT_XTAL_REF_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_BT_XTAL_REF_EN  BT_RFC_MISC_CTRL_REG_BT_XTAL_REF_EN_Msk
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_Pos  (19U)
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN  BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_Msk
#define BT_RFC_MISC_CTRL_REG_EDR_UNLOCK_FLAG_CLR_Pos  (20U)
#define BT_RFC_MISC_CTRL_REG_EDR_UNLOCK_FLAG_CLR_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_EDR_UNLOCK_FLAG_CLR_Pos)
#define BT_RFC_MISC_CTRL_REG_EDR_UNLOCK_FLAG_CLR  BT_RFC_MISC_CTRL_REG_EDR_UNLOCK_FLAG_CLR_Msk
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_Pos  (21U)
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN  BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_Msk
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_FRC_EN_Pos  (22U)
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_FRC_EN  BT_RFC_MISC_CTRL_REG_EDR_XTAL_REF_EN_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_FRC_EN_Pos  (23U)
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_FRC_EN  BT_RFC_MISC_CTRL_REG_DAC_CLK_EN_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_BYPASS_DAC_FIFO_Pos  (24U)
#define BT_RFC_MISC_CTRL_REG_BYPASS_DAC_FIFO_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_BYPASS_DAC_FIFO_Pos)
#define BT_RFC_MISC_CTRL_REG_BYPASS_DAC_FIFO  BT_RFC_MISC_CTRL_REG_BYPASS_DAC_FIFO_Msk
#define BT_RFC_MISC_CTRL_REG_DAC_WCLK_EDGE_SEL_Pos  (25U)
#define BT_RFC_MISC_CTRL_REG_DAC_WCLK_EDGE_SEL_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_DAC_WCLK_EDGE_SEL_Pos)
#define BT_RFC_MISC_CTRL_REG_DAC_WCLK_EDGE_SEL  BT_RFC_MISC_CTRL_REG_DAC_WCLK_EDGE_SEL_Msk
#define BT_RFC_MISC_CTRL_REG_ADC_Q_EN_FRC_EN_Pos  (26U)
#define BT_RFC_MISC_CTRL_REG_ADC_Q_EN_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_ADC_Q_EN_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_ADC_Q_EN_FRC_EN  BT_RFC_MISC_CTRL_REG_ADC_Q_EN_FRC_EN_Msk
#define BT_RFC_MISC_CTRL_REG_EN_2M_MOD_FRC_EN_Pos  (27U)
#define BT_RFC_MISC_CTRL_REG_EN_2M_MOD_FRC_EN_Msk  (0x1UL << BT_RFC_MISC_CTRL_REG_EN_2M_MOD_FRC_EN_Pos)
#define BT_RFC_MISC_CTRL_REG_EN_2M_MOD_FRC_EN  BT_RFC_MISC_CTRL_REG_EN_2M_MOD_FRC_EN_Msk

/************** Bit definition for BT_RFC_RF_LODIST_REG register **************/
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_FBDV_STR_LV_Pos  (0U)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_FBDV_STR_LV_Msk  (0x3UL << BT_RFC_RF_LODIST_REG_BRF_LODIST_FBDV_STR_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_FBDV_STR_LV  BT_RFC_RF_LODIST_REG_BRF_LODIST_FBDV_STR_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_STR_LV_Pos  (2U)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_STR_LV_Msk  (0x3UL << BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_STR_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_STR_LV  BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_STR_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_EN_LV_Pos  (4U)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_EN_LV_Msk  (0x1UL << BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_EN_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_EN_LV  BT_RFC_RF_LODIST_REG_BRF_LODIST_TX_EN_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_STR_LV_Pos  (5U)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_STR_LV_Msk  (0x3UL << BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_STR_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_STR_LV  BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_STR_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_EN_LV_Pos  (7U)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_EN_LV_Msk  (0x1UL << BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_EN_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_EN_LV  BT_RFC_RF_LODIST_REG_BRF_LODIST_RX_EN_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_EN_RFBG_LV_Pos  (8U)
#define BT_RFC_RF_LODIST_REG_BRF_EN_RFBG_LV_Msk  (0x1UL << BT_RFC_RF_LODIST_REG_BRF_EN_RFBG_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_EN_RFBG_LV  BT_RFC_RF_LODIST_REG_BRF_EN_RFBG_LV_Msk
#define BT_RFC_RF_LODIST_REG_BRF_EN_VDDPSW_LV_Pos  (9U)
#define BT_RFC_RF_LODIST_REG_BRF_EN_VDDPSW_LV_Msk  (0x1UL << BT_RFC_RF_LODIST_REG_BRF_EN_VDDPSW_LV_Pos)
#define BT_RFC_RF_LODIST_REG_BRF_EN_VDDPSW_LV  BT_RFC_RF_LODIST_REG_BRF_EN_VDDPSW_LV_Msk

/**************** Bit definition for BT_RFC_FBDV_REG1 register ****************/
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Pos  (0U)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV  BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Pos  (1U)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV  BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Pos  (2U)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_EN_LV  BT_RFC_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Pos  (3U)
#define BT_RFC_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Msk  (0x3UL << BT_RFC_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FBDV_MOD_STG_LV  BT_RFC_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Pos  (5U)
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV  BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_LV_Pos  (6U)
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_LV  BT_RFC_FBDV_REG1_BRF_FBDV_RSTB_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Pos  (7U)
#define BT_RFC_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FBDV_LDO_VREF_LV  BT_RFC_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Msk
#define BT_RFC_FBDV_REG1_BRF_FBDV_EN_LV_Pos  (11U)
#define BT_RFC_FBDV_REG1_BRF_FBDV_EN_LV_Msk  (0x1UL << BT_RFC_FBDV_REG1_BRF_FBDV_EN_LV_Pos)
#define BT_RFC_FBDV_REG1_BRF_FBDV_EN_LV  BT_RFC_FBDV_REG1_BRF_FBDV_EN_LV_Msk

/**************** Bit definition for BT_RFC_FBDV_REG2 register ****************/
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Pos  (0U)
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Msk  (0xFFFFUL << BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Pos)
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_OP_LV  BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Msk
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Pos  (16U)
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Msk  (0xFFFFUL << BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Pos)
#define BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV  BT_RFC_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Msk

/**************** Bit definition for BT_RFC_PFDCP_REG register ****************/
#define BT_RFC_PFDCP_REG_BRF_CSD_DN_LV_Pos  (0U)
#define BT_RFC_PFDCP_REG_BRF_CSD_DN_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_CSD_DN_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_CSD_DN_LV  BT_RFC_PFDCP_REG_BRF_CSD_DN_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_CSD_UP_LV_Pos  (1U)
#define BT_RFC_PFDCP_REG_BRF_CSD_UP_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_CSD_UP_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_CSD_UP_LV  BT_RFC_PFDCP_REG_BRF_CSD_UP_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_LO_UNLOCK_LV_Pos  (2U)
#define BT_RFC_PFDCP_REG_BRF_LO_UNLOCK_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_LO_UNLOCK_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_LO_UNLOCK_LV  BT_RFC_PFDCP_REG_BRF_LO_UNLOCK_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Pos  (3U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Pos  (4U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_EN_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Pos  (5U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Msk  (0x3FUL << BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_OS_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Pos  (11U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Msk  (0xFUL << BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_SET_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Pos  (15U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Msk
#define BT_RFC_PFDCP_REG_BRF_PFDCP_EN_LV_Pos  (19U)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_EN_LV_Msk  (0x1UL << BT_RFC_PFDCP_REG_BRF_PFDCP_EN_LV_Pos)
#define BT_RFC_PFDCP_REG_BRF_PFDCP_EN_LV  BT_RFC_PFDCP_REG_BRF_PFDCP_EN_LV_Msk

/***************** Bit definition for BT_RFC_LPF_REG register *****************/
#define BT_RFC_LPF_REG_BRF_LPF_RZ_SEL_LV_Pos  (0U)
#define BT_RFC_LPF_REG_BRF_LPF_RZ_SEL_LV_Msk  (0x7UL << BT_RFC_LPF_REG_BRF_LPF_RZ_SEL_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LPF_RZ_SEL_LV  BT_RFC_LPF_REG_BRF_LPF_RZ_SEL_LV_Msk
#define BT_RFC_LPF_REG_BRF_LPF_RP4_SEL_LV_Pos  (3U)
#define BT_RFC_LPF_REG_BRF_LPF_RP4_SEL_LV_Msk  (0x7UL << BT_RFC_LPF_REG_BRF_LPF_RP4_SEL_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LPF_RP4_SEL_LV  BT_RFC_LPF_REG_BRF_LPF_RP4_SEL_LV_Msk
#define BT_RFC_LPF_REG_BRF_LPF_CZ_SEL_LV_Pos  (6U)
#define BT_RFC_LPF_REG_BRF_LPF_CZ_SEL_LV_Msk  (0x7UL << BT_RFC_LPF_REG_BRF_LPF_CZ_SEL_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LPF_CZ_SEL_LV  BT_RFC_LPF_REG_BRF_LPF_CZ_SEL_LV_Msk
#define BT_RFC_LPF_REG_BRF_LPF_CP4_SEL_LV_Pos  (9U)
#define BT_RFC_LPF_REG_BRF_LPF_CP4_SEL_LV_Msk  (0x3UL << BT_RFC_LPF_REG_BRF_LPF_CP4_SEL_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LPF_CP4_SEL_LV  BT_RFC_LPF_REG_BRF_LPF_CP4_SEL_LV_Msk
#define BT_RFC_LPF_REG_BRF_LPF_CP3_SEL_LV_Pos  (11U)
#define BT_RFC_LPF_REG_BRF_LPF_CP3_SEL_LV_Msk  (0x7UL << BT_RFC_LPF_REG_BRF_LPF_CP3_SEL_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LPF_CP3_SEL_LV  BT_RFC_LPF_REG_BRF_LPF_CP3_SEL_LV_Msk
#define BT_RFC_LPF_REG_BRF_LO_OPEN_LV_Pos  (14U)
#define BT_RFC_LPF_REG_BRF_LO_OPEN_LV_Msk  (0x1UL << BT_RFC_LPF_REG_BRF_LO_OPEN_LV_Pos)
#define BT_RFC_LPF_REG_BRF_LO_OPEN_LV   BT_RFC_LPF_REG_BRF_LO_OPEN_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG1 register ***************/
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_VAR_VVN_BM_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_VAR_VVN_BM_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_VAR_VVN_BM_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_VAR_VVN_BM_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_VAR_VVN_BM_LV_Msk
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_CBANK_VVN_BM_LV_Pos  (3U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_CBANK_VVN_BM_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_CBANK_VVN_BM_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_CBANK_VVN_BM_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_CBANK_VVN_BM_LV_Msk
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_FLT_EN_LV_Pos  (6U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_FLT_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_FLT_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_FLT_EN_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_FLT_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_LDO_VREF_LV_Pos  (7U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_LDO_VREF_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_LDO_VREF_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_VCO_LDO_VREF_LV_Msk
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_EN_VCO3G_LV_Pos  (11U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_EN_VCO3G_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG1_BRF_EDR_EN_VCO3G_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_EN_VCO3G_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_EN_VCO3G_LV_Msk
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_LO_EN_IARY_LV_Pos  (12U)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_LO_EN_IARY_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG1_BRF_EDR_LO_EN_IARY_LV_Pos)
#define BT_RFC_EDR_PLL_REG1_BRF_EDR_LO_EN_IARY_LV  BT_RFC_EDR_PLL_REG1_BRF_EDR_LO_EN_IARY_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG2 register ***************/
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_UP_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_UP_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_UP_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_UP_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_UP_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_INCAL_LV_Pos  (1U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_INCAL_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_INCAL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_INCAL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_INCAL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_UP_LV_Pos  (2U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_UP_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_UP_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_UP_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_UP_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_INCAL_LV_Pos  (3U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_INCAL_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_INCAL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_INCAL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO3G_ACAL_INCAL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VL_SEL_LV_Pos  (4U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VL_SEL_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VL_SEL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VL_SEL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VL_SEL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VH_SEL_LV_Pos  (7U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VH_SEL_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VH_SEL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VH_SEL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_VH_SEL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_EN_LV_Pos  (10U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_EN_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_INCFCAL_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_VC_SEL_LV_Pos  (11U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_VC_SEL_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_VC_SEL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_VC_SEL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_VC_SEL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_EN_LV_Pos  (14U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_EN_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_FKCAL_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VL_SEL_LV_Pos  (15U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VL_SEL_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VL_SEL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VL_SEL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VL_SEL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VH_SEL_LV_Pos  (19U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VH_SEL_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VH_SEL_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VH_SEL_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_VH_SEL_LV_Msk
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_EN_LV_Pos  (23U)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_EN_LV  BT_RFC_EDR_PLL_REG2_BRF_EDR_VCO_ACAL_EN_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG3 register ***************/
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_DN_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_DN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_DN_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_DN_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_DN_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_UP_LV_Pos  (1U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_UP_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_UP_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_UP_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_CSD_UP_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LO_UNLOCK_LV_Pos  (2U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LO_UNLOCK_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_LO_UNLOCK_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LO_UNLOCK_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_LO_UNLOCK_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_RST_LV_Pos  (3U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_RST_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_RST_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_RST_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_RST_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_EN_LV_Pos  (4U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_EN_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_CSD_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_OS_LV_Pos  (5U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_OS_LV_Msk  (0x3FUL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_OS_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_OS_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_OS_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_SET_LV_Pos  (11U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_SET_LV_Msk  (0x3FUL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_SET_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_ICP_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_LDO_VREF_LV_Pos  (17U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_LDO_VREF_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_LDO_VREF_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_LDO_VREF_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_EN_LV_Pos  (21U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_EN_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_PFDCP_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_RVCO_SEL_VMID_LV_Pos  (22U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_RVCO_SEL_VMID_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_RVCO_SEL_VMID_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_RVCO_SEL_VMID_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_RVCO_SEL_VMID_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LDOVREF_RVCO_LV_Pos  (25U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LDOVREF_RVCO_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG3_BRF_EDR_LDOVREF_RVCO_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_LDOVREF_RVCO_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_LDOVREF_RVCO_LV_Msk
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_EN_RVCO_LV_Pos  (29U)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_EN_RVCO_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG3_BRF_EDR_EN_RVCO_LV_Pos)
#define BT_RFC_EDR_PLL_REG3_BRF_EDR_EN_RVCO_LV  BT_RFC_EDR_PLL_REG3_BRF_EDR_EN_RVCO_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG4 register ***************/
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_VC_PATH_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_VC_PATH_LV_Msk  (0x3UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_VC_PATH_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_VC_PATH_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_VC_PATH_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RZ_SET_LV_Pos  (2U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RZ_SET_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RZ_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RZ_SET_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RZ_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RP4_SET_LV_Pos  (6U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RP4_SET_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RP4_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RP4_SET_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_RP4_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CZ_SET_LV_Pos  (10U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CZ_SET_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CZ_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CZ_SET_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CZ_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP4_SET_LV_Pos  (14U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP4_SET_LV_Msk  (0x7UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP4_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP4_SET_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP4_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP3_SET_LV_Pos  (17U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP3_SET_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP3_SET_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP3_SET_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LPF_CP3_SET_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LO_OPEN_LV_Pos  (21U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LO_OPEN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LO_OPEN_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LO_OPEN_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LO_OPEN_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LDOVREF_LF_LV_Pos  (22U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LDOVREF_LF_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG4_BRF_EDR_LDOVREF_LF_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_LDOVREF_LF_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_LDOVREF_LF_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_EN_LF_LV_Pos  (26U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_EN_LF_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_EN_LF_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_EN_LF_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_EN_LF_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_PFDCP_SEL_SIGN_LV_Pos  (27U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_PFDCP_SEL_SIGN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_PFDCP_SEL_SIGN_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_PFDCP_SEL_SIGN_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_PFDCP_SEL_SIGN_LV_Msk
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_CKREF_LV_Pos  (28U)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_CKREF_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_CKREF_LV_Pos)
#define BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_CKREF_LV  BT_RFC_EDR_PLL_REG4_BRF_EDR_SEL_CKREF_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG5 register ***************/
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_TR_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_TR_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_TR_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_TR_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_TR_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_EN_LV_Pos  (4U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_EN_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_DTEST_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RDY_LV_Pos  (5U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RDY_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RDY_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RDY_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RDY_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RSTB_LV_Pos  (6U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RSTB_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RSTB_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RSTB_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_RSTB_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_EN_LV_Pos  (7U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_EN_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FKCAL_CNT_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_MOD_STG_LV_Pos  (8U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_MOD_STG_LV_Msk  (0x3UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_MOD_STG_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_MOD_STG_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_MOD_STG_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_SYNC_EN_LV_Pos  (10U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_SYNC_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_SYNC_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_SYNC_EN_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_SYNC_EN_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_LV_Pos  (11U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_RSTB_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_LFP_FCW_LV_Pos  (12U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_LFP_FCW_LV_Msk  (0x3FFUL << BT_RFC_EDR_PLL_REG5_BRF_EDR_LFP_FCW_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_LFP_FCW_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_LFP_FCW_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_SEL_CKIN_LV_Pos  (22U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_SEL_CKIN_LV_Msk  (0x3UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_SEL_CKIN_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_SEL_CKIN_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_SEL_CKIN_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_LDO_VREF_LV_Pos  (24U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_LDO_VREF_LV_Msk  (0xFUL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_LDO_VREF_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_LDO_VREF_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_LDO_VREF_LV_Msk
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_EN_LV_Pos  (28U)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_EN_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_EN_LV_Pos)
#define BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_EN_LV  BT_RFC_EDR_PLL_REG5_BRF_EDR_FBDV_EN_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG6 register ***************/
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_OP_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_OP_LV_Msk  (0xFFFFUL << BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_OP_LV_Pos)
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_OP_LV  BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_OP_LV_Msk
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_DIVN_LV_Pos  (16U)
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_DIVN_LV_Msk  (0xFFFFUL << BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_DIVN_LV_Pos)
#define BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_DIVN_LV  BT_RFC_EDR_PLL_REG6_BRF_EDR_FKCAL_CNT_DIVN_LV_Msk

/************** Bit definition for BT_RFC_EDR_PLL_REG7 register ***************/
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_SDM_CLK_LV_Pos  (0U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_SDM_CLK_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_SDM_CLK_LV_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_SDM_CLK_LV  BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_SDM_CLK_LV_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_EN_CLK_PLLDIG_LV_Pos  (1U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_EN_CLK_PLLDIG_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_EN_CLK_PLLDIG_LV_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_EN_CLK_PLLDIG_LV  BT_RFC_EDR_PLL_REG7_BRF_EDR_EN_CLK_PLLDIG_LV_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_CKFB_WIDE_LV_Pos  (2U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_CKFB_WIDE_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_CKFB_WIDE_LV_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_CKFB_WIDE_LV  BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_CKFB_WIDE_LV_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_FCW_LV_Pos  (3U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_FCW_LV_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_FCW_LV_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_FCW_LV  BT_RFC_EDR_PLL_REG7_BRF_EDR_SEL_FCW_LV_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_LV_Pos  (4U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_LV_Msk  (0x3FUL << BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_LV_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_LV  BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_LV_Msk
#define BT_RFC_EDR_PLL_REG7_BT_PDX_FORCE_EN_Pos  (10U)
#define BT_RFC_EDR_PLL_REG7_BT_PDX_FORCE_EN_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BT_PDX_FORCE_EN_Pos)
#define BT_RFC_EDR_PLL_REG7_BT_PDX_FORCE_EN  BT_RFC_EDR_PLL_REG7_BT_PDX_FORCE_EN_Msk
#define BT_RFC_EDR_PLL_REG7_BT_IDAC_FORCE_EN_Pos  (11U)
#define BT_RFC_EDR_PLL_REG7_BT_IDAC_FORCE_EN_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BT_IDAC_FORCE_EN_Pos)
#define BT_RFC_EDR_PLL_REG7_BT_IDAC_FORCE_EN  BT_RFC_EDR_PLL_REG7_BT_IDAC_FORCE_EN_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_FCW_FORCE_EN_Pos  (12U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_FCW_FORCE_EN_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_FCW_FORCE_EN_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_FCW_FORCE_EN  BT_RFC_EDR_PLL_REG7_BRF_EDR_FCW_FORCE_EN_Msk
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_FORCE_EN_Pos  (13U)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_FORCE_EN_Msk  (0x1UL << BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_FORCE_EN_Pos)
#define BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_FORCE_EN  BT_RFC_EDR_PLL_REG7_BRF_EDR_RVCO_FC_FORCE_EN_Msk

/************** Bit definition for BT_RFC_EDR_CAL_REG1 register ***************/
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_PDX_LV_Pos  (0U)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_PDX_LV_Msk  (0xFFUL << BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_PDX_LV_Pos)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_PDX_LV  BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_PDX_LV_Msk
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_IDAC_LV_Pos  (8U)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_IDAC_LV_Msk  (0x7FUL << BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_IDAC_LV_Pos)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_IDAC_LV  BT_RFC_EDR_CAL_REG1_BRF_EDR_VCO_IDAC_LV_Msk
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_FC_LV_Pos  (16U)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_FC_LV_Msk  (0x7UL << BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_FC_LV_Pos)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_FC_LV  BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_FC_LV_Msk
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_BM_LV_Pos  (20U)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_BM_LV_Msk  (0x1FUL << BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_BM_LV_Pos)
#define BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_BM_LV  BT_RFC_EDR_CAL_REG1_BRF_EDR_OSLO_BM_LV_Msk
#define BT_RFC_EDR_CAL_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Pos  (28U)
#define BT_RFC_EDR_CAL_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Msk  (0xFUL << BT_RFC_EDR_CAL_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Pos)
#define BT_RFC_EDR_CAL_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV  BT_RFC_EDR_CAL_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Msk

/************** Bit definition for BT_RFC_EDR_OSLO_REG register ***************/
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_SEL_LODIST_TX_LV_Pos  (0U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_SEL_LODIST_TX_LV_Msk  (0x3UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_SEL_LODIST_TX_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_SEL_LODIST_TX_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_SEL_LODIST_TX_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_LODIST_LV_Pos  (2U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_LODIST_LV_Msk  (0xFUL << BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_LODIST_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_LODIST_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_LODIST_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_LODIST_LV_Pos  (6U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_LODIST_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_LODIST_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_LODIST_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_LODIST_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_ACAL_CMP_LV_Pos  (7U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_ACAL_CMP_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_ACAL_CMP_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_ACAL_CMP_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_ACAL_CMP_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_PKDET_LV_Pos  (8U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_PKDET_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_PKDET_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_PKDET_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_PKDET_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_NGM_LV_Pos  (9U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_NGM_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_NGM_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_NGM_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_NGM_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_FCAL_LV_Pos  (10U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_FCAL_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_FCAL_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_FCAL_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_FCAL_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_PKDET_VREF_LV_Pos  (11U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_PKDET_VREF_LV_Msk  (0x7UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_PKDET_VREF_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_PKDET_VREF_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_OSLO_PKDET_VREF_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_OSLO_LV_Pos  (14U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_OSLO_LV_Msk  (0xFUL << BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_OSLO_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_OSLO_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_LDOVREF_OSLO_LV_Msk
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_LV_Pos  (18U)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_LV_Msk  (0x1UL << BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_LV_Pos)
#define BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_LV  BT_RFC_EDR_OSLO_REG_BRF_EDR_EN_OSLO_LV_Msk

/**************** Bit definition for BT_RFC_ATEST_REG register ****************/
#define BT_RFC_ATEST_REG_BRF_DC_TR_LV_Pos  (0U)
#define BT_RFC_ATEST_REG_BRF_DC_TR_LV_Msk  (0x7UL << BT_RFC_ATEST_REG_BRF_DC_TR_LV_Pos)
#define BT_RFC_ATEST_REG_BRF_DC_TR_LV   BT_RFC_ATEST_REG_BRF_DC_TR_LV_Msk
#define BT_RFC_ATEST_REG_BRF_DC_BR_LV_Pos  (3U)
#define BT_RFC_ATEST_REG_BRF_DC_BR_LV_Msk  (0x7UL << BT_RFC_ATEST_REG_BRF_DC_BR_LV_Pos)
#define BT_RFC_ATEST_REG_BRF_DC_BR_LV   BT_RFC_ATEST_REG_BRF_DC_BR_LV_Msk
#define BT_RFC_ATEST_REG_BRF_DC_MR_LV_Pos  (6U)
#define BT_RFC_ATEST_REG_BRF_DC_MR_LV_Msk  (0x7UL << BT_RFC_ATEST_REG_BRF_DC_MR_LV_Pos)
#define BT_RFC_ATEST_REG_BRF_DC_MR_LV   BT_RFC_ATEST_REG_BRF_DC_MR_LV_Msk

/**************** Bit definition for BT_RFC_DTEST_REG register ****************/
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Pos  (0U)
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Msk  (0xFUL << BT_RFC_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_TR_LV  BT_RFC_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Msk
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Pos  (4U)
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Msk  (0x1UL << BT_RFC_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_FBDV_DTEST_EN_LV  BT_RFC_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Msk
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_TR_LV_Pos  (5U)
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_TR_LV_Msk  (0xFUL << BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_TR_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_TR_LV  BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_TR_LV_Msk
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_EN_LV_Pos  (9U)
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_EN_LV_Msk  (0x1UL << BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_EN_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_EN_LV  BT_RFC_DTEST_REG_BRF_EDR_FBDV_DTEST_EN_LV_Msk
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TR_LV_Pos  (10U)
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TR_LV_Msk  (0xFUL << BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TR_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TR_LV  BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TR_LV_Msk
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TE_LV_Pos  (14U)
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TE_LV_Msk  (0x1UL << BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TE_LV_Pos)
#define BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TE_LV  BT_RFC_DTEST_REG_BRF_EDR_DIG_DTEST_TE_LV_Msk

/**************** Bit definition for BT_RFC_TRF_REG1 register *****************/
#define BT_RFC_TRF_REG1_BRF_PA_CAS_BP_LV_Pos  (0U)
#define BT_RFC_TRF_REG1_BRF_PA_CAS_BP_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_CAS_BP_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_CAS_BP_LV  BT_RFC_TRF_REG1_BRF_PA_CAS_BP_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_PM_LV_Pos  (1U)
#define BT_RFC_TRF_REG1_BRF_PA_PM_LV_Msk  (0x3UL << BT_RFC_TRF_REG1_BRF_PA_PM_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_PM_LV    BT_RFC_TRF_REG1_BRF_PA_PM_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_VC_LV_Pos  (3U)
#define BT_RFC_TRF_REG1_BRF_PA_VC_LV_Msk  (0x3FUL << BT_RFC_TRF_REG1_BRF_PA_VC_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_VC_LV    BT_RFC_TRF_REG1_BRF_PA_VC_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_RSTN_LV_Pos  (9U)
#define BT_RFC_TRF_REG1_BRF_PA_RSTN_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_RSTN_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_RSTN_LV  BT_RFC_TRF_REG1_BRF_PA_RSTN_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_SETBC_LV_Pos  (10U)
#define BT_RFC_TRF_REG1_BRF_PA_SETBC_LV_Msk  (0xFUL << BT_RFC_TRF_REG1_BRF_PA_SETBC_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_SETBC_LV  BT_RFC_TRF_REG1_BRF_PA_SETBC_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_SETSGN_LV_Pos  (14U)
#define BT_RFC_TRF_REG1_BRF_PA_SETSGN_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_SETSGN_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_SETSGN_LV  BT_RFC_TRF_REG1_BRF_PA_SETSGN_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_BCSEL_LV_Pos  (15U)
#define BT_RFC_TRF_REG1_BRF_PA_BCSEL_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_BCSEL_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_BCSEL_LV  BT_RFC_TRF_REG1_BRF_PA_BCSEL_LV_Msk
#define BT_RFC_TRF_REG1_BRF_TRF_SIG_EN_LV_Pos  (16U)
#define BT_RFC_TRF_REG1_BRF_TRF_SIG_EN_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_TRF_SIG_EN_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_TRF_SIG_EN_LV  BT_RFC_TRF_REG1_BRF_TRF_SIG_EN_LV_Msk
#define BT_RFC_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Pos  (17U)
#define BT_RFC_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Msk  (0xFUL << BT_RFC_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV  BT_RFC_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_OUT_PU_LV_Pos  (21U)
#define BT_RFC_TRF_REG1_BRF_PA_OUT_PU_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_OUT_PU_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_OUT_PU_LV  BT_RFC_TRF_REG1_BRF_PA_OUT_PU_LV_Msk
#define BT_RFC_TRF_REG1_BRF_PA_BUF_PU_LV_Pos  (22U)
#define BT_RFC_TRF_REG1_BRF_PA_BUF_PU_LV_Msk  (0x1UL << BT_RFC_TRF_REG1_BRF_PA_BUF_PU_LV_Pos)
#define BT_RFC_TRF_REG1_BRF_PA_BUF_PU_LV  BT_RFC_TRF_REG1_BRF_PA_BUF_PU_LV_Msk

/**************** Bit definition for BT_RFC_TRF_REG2 register *****************/
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Pos  (0U)
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Msk  (0xFUL << BT_RFC_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_GAIN_LV  BT_RFC_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_EN_LV_Pos  (4U)
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_EN_LV_Msk  (0x1UL << BT_RFC_TRF_REG2_BRF_PA_ATTEN_EN_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_ATTEN_EN_LV  BT_RFC_TRF_REG2_BRF_PA_ATTEN_EN_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_MATCH2_LV_Pos  (5U)
#define BT_RFC_TRF_REG2_BRF_PA_MATCH2_LV_Msk  (0x3UL << BT_RFC_TRF_REG2_BRF_PA_MATCH2_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_MATCH2_LV  BT_RFC_TRF_REG2_BRF_PA_MATCH2_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_MATCH1_LV_Pos  (7U)
#define BT_RFC_TRF_REG2_BRF_PA_MATCH1_LV_Msk  (0x3UL << BT_RFC_TRF_REG2_BRF_PA_MATCH1_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_MATCH1_LV  BT_RFC_TRF_REG2_BRF_PA_MATCH1_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_TX_RX_LV_Pos  (9U)
#define BT_RFC_TRF_REG2_BRF_PA_TX_RX_LV_Msk  (0x1UL << BT_RFC_TRF_REG2_BRF_PA_TX_RX_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_TX_RX_LV  BT_RFC_TRF_REG2_BRF_PA_TX_RX_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_MCAP_LV_Pos  (10U)
#define BT_RFC_TRF_REG2_BRF_PA_MCAP_LV_Msk  (0x1UL << BT_RFC_TRF_REG2_BRF_PA_MCAP_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_MCAP_LV  BT_RFC_TRF_REG2_BRF_PA_MCAP_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_UNIT_SEL_LV_Pos  (11U)
#define BT_RFC_TRF_REG2_BRF_PA_UNIT_SEL_LV_Msk  (0x1FUL << BT_RFC_TRF_REG2_BRF_PA_UNIT_SEL_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_UNIT_SEL_LV  BT_RFC_TRF_REG2_BRF_PA_UNIT_SEL_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Pos  (16U)
#define BT_RFC_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Msk  (0x3UL << BT_RFC_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV  BT_RFC_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Msk
#define BT_RFC_TRF_REG2_BRF_PA_BM_LV_Pos  (18U)
#define BT_RFC_TRF_REG2_BRF_PA_BM_LV_Msk  (0x3UL << BT_RFC_TRF_REG2_BRF_PA_BM_LV_Pos)
#define BT_RFC_TRF_REG2_BRF_PA_BM_LV    BT_RFC_TRF_REG2_BRF_PA_BM_LV_Msk

/************** Bit definition for BT_RFC_TRF_EDR_REG1 register ***************/
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PACAS_BM_LV_Pos  (0U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PACAS_BM_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PACAS_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PACAS_BM_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PACAS_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_BM_LV_Pos  (2U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_BM_LV_Msk  (0x1FUL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_BM_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_PU_LV_Pos  (7U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_PU_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_PU_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_PU_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_PA_PU_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_BM_LV_Pos  (8U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_BM_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_BM_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Pos  (10U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Msk  (0xFUL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAP_SEL_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_BM_LV_Pos  (14U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_BM_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_BM_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_SEL_LV_Pos  (16U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_SEL_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_SEL_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_SEL_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXCAS_SEL_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMX_PU_LV_Pos  (17U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMX_PU_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMX_PU_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMX_PU_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMX_PU_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_LOBIAS_BM_LV_Pos  (18U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_LOBIAS_BM_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_LOBIAS_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_LOBIAS_BM_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_LOBIAS_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_GC_LV_Pos  (20U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_GC_LV_Msk  (0xFUL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_GC_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_GC_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_GC_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_IBLD_LV_Pos  (24U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_IBLD_LV_Msk  (0xFUL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_IBLD_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_IBLD_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_IBLD_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_PU_LV_Pos  (28U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_PU_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_PU_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_PU_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_TMXBUF_PU_LV_Msk
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_IARRAY_EN_LV_Pos  (29U)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_IARRAY_EN_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_IARRAY_EN_LV_Pos)
#define BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_IARRAY_EN_LV  BT_RFC_TRF_EDR_REG1_BRF_TRF_EDR_IARRAY_EN_LV_Msk

/************** Bit definition for BT_RFC_TRF_EDR_REG2 register ***************/
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_PN_LV_Pos  (0U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_PN_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_PN_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_PN_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_PN_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_LV_Pos  (1U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_LV_Msk  (0xFUL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_OS_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_GC_LV_Pos  (5U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_GC_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_GC_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_GC_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_GC_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_BM_LV_Pos  (7U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_BM_LV_Msk  (0x7UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_BM_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_EN_LV_Pos  (10U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_EN_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_EN_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_EN_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PWRMTR_EN_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PA_XFMR_SG_LV_Pos  (11U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PA_XFMR_SG_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PA_XFMR_SG_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PA_XFMR_SG_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PA_XFMR_SG_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PAPMOS_BM_LV_Pos  (12U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PAPMOS_BM_LV_Msk  (0x7UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PAPMOS_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PAPMOS_BM_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PAPMOS_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_BM_LV_Pos  (15U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_BM_LV_Msk  (0x3UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_BM_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_BM_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_BM_LV_Msk
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_EN_LV_Pos  (17U)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_EN_LV_Msk  (0x1UL << BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_EN_LV_Pos)
#define BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_EN_LV  BT_RFC_TRF_EDR_REG2_BRF_TRF_EDR_PACAP_EN_LV_Msk

/***************** Bit definition for BT_RFC_RRF_REG register *****************/
#define BT_RFC_RRF_REG_BRF_MX_BM_LV_Pos  (0U)
#define BT_RFC_RRF_REG_BRF_MX_BM_LV_Msk  (0x7UL << BT_RFC_RRF_REG_BRF_MX_BM_LV_Pos)
#define BT_RFC_RRF_REG_BRF_MX_BM_LV     BT_RFC_RRF_REG_BRF_MX_BM_LV_Msk
#define BT_RFC_RRF_REG_BRF_MX_PU_LV_Pos  (3U)
#define BT_RFC_RRF_REG_BRF_MX_PU_LV_Msk  (0x1UL << BT_RFC_RRF_REG_BRF_MX_PU_LV_Pos)
#define BT_RFC_RRF_REG_BRF_MX_PU_LV     BT_RFC_RRF_REG_BRF_MX_PU_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_MATCH_LV_Pos  (4U)
#define BT_RFC_RRF_REG_BRF_LNA_MATCH_LV_Msk  (0x3UL << BT_RFC_RRF_REG_BRF_LNA_MATCH_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_MATCH_LV  BT_RFC_RRF_REG_BRF_LNA_MATCH_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_SHUNTSW_LV_Pos  (6U)
#define BT_RFC_RRF_REG_BRF_LNA_SHUNTSW_LV_Msk  (0x1UL << BT_RFC_RRF_REG_BRF_LNA_SHUNTSW_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_SHUNTSW_LV  BT_RFC_RRF_REG_BRF_LNA_SHUNTSW_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_FBRTRIM_LV_Pos  (7U)
#define BT_RFC_RRF_REG_BRF_LNA_FBRTRIM_LV_Msk  (0x7UL << BT_RFC_RRF_REG_BRF_LNA_FBRTRIM_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_FBRTRIM_LV  BT_RFC_RRF_REG_BRF_LNA_FBRTRIM_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_GC_LV_Pos  (10U)
#define BT_RFC_RRF_REG_BRF_LNA_GC_LV_Msk  (0xFUL << BT_RFC_RRF_REG_BRF_LNA_GC_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_GC_LV    BT_RFC_RRF_REG_BRF_LNA_GC_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_BM_LV_Pos  (14U)
#define BT_RFC_RRF_REG_BRF_LNA_BM_LV_Msk  (0x7UL << BT_RFC_RRF_REG_BRF_LNA_BM_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_BM_LV    BT_RFC_RRF_REG_BRF_LNA_BM_LV_Msk
#define BT_RFC_RRF_REG_BRF_LNA_PU_LV_Pos  (17U)
#define BT_RFC_RRF_REG_BRF_LNA_PU_LV_Msk  (0x1UL << BT_RFC_RRF_REG_BRF_LNA_PU_LV_Pos)
#define BT_RFC_RRF_REG_BRF_LNA_PU_LV    BT_RFC_RRF_REG_BRF_LNA_PU_LV_Msk
#define BT_RFC_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Pos  (18U)
#define BT_RFC_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Msk  (0xFUL << BT_RFC_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Pos)
#define BT_RFC_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV  BT_RFC_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Msk
#define BT_RFC_RRF_REG_BRF_RRF_LDO11_EN_LV_Pos  (22U)
#define BT_RFC_RRF_REG_BRF_RRF_LDO11_EN_LV_Msk  (0x1UL << BT_RFC_RRF_REG_BRF_RRF_LDO11_EN_LV_Pos)
#define BT_RFC_RRF_REG_BRF_RRF_LDO11_EN_LV  BT_RFC_RRF_REG_BRF_RRF_LDO11_EN_LV_Msk

/**************** Bit definition for BT_RFC_RBB_REG1 register *****************/
#define BT_RFC_RBB_REG1_BRF_CBPF_FC_LV_2M_Pos  (0U)
#define BT_RFC_RBB_REG1_BRF_CBPF_FC_LV_2M_Msk  (0x3UL << BT_RFC_RBB_REG1_BRF_CBPF_FC_LV_2M_Pos)
#define BT_RFC_RBB_REG1_BRF_CBPF_FC_LV_2M  BT_RFC_RBB_REG1_BRF_CBPF_FC_LV_2M_Msk
#define BT_RFC_RBB_REG1_BRF_CBPF_BM_LV_2M_Pos  (2U)
#define BT_RFC_RBB_REG1_BRF_CBPF_BM_LV_2M_Msk  (0x7UL << BT_RFC_RBB_REG1_BRF_CBPF_BM_LV_2M_Pos)
#define BT_RFC_RBB_REG1_BRF_CBPF_BM_LV_2M  BT_RFC_RBB_REG1_BRF_CBPF_BM_LV_2M_Msk
#define BT_RFC_RBB_REG1_BRF_CBPF_CC_LV_2M_Pos  (5U)
#define BT_RFC_RBB_REG1_BRF_CBPF_CC_LV_2M_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_CBPF_CC_LV_2M_Pos)
#define BT_RFC_RBB_REG1_BRF_CBPF_CC_LV_2M  BT_RFC_RBB_REG1_BRF_CBPF_CC_LV_2M_Msk
#define BT_RFC_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Pos  (9U)
#define BT_RFC_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Pos)
#define BT_RFC_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV  BT_RFC_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Msk
#define BT_RFC_RBB_REG1_BRF_EN_LDO_RBB_LV_Pos  (13U)
#define BT_RFC_RBB_REG1_BRF_EN_LDO_RBB_LV_Msk  (0x1UL << BT_RFC_RBB_REG1_BRF_EN_LDO_RBB_LV_Pos)
#define BT_RFC_RBB_REG1_BRF_EN_LDO_RBB_LV  BT_RFC_RBB_REG1_BRF_EN_LDO_RBB_LV_Msk
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2Q_BT_Pos  (14U)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2Q_BT_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_PKDET_VTH2Q_BT_Pos)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2Q_BT  BT_RFC_RBB_REG1_BRF_PKDET_VTH2Q_BT_Msk
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2I_BT_Pos  (18U)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2I_BT_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_PKDET_VTH2I_BT_Pos)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH2I_BT  BT_RFC_RBB_REG1_BRF_PKDET_VTH2I_BT_Msk
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1Q_BT_Pos  (22U)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1Q_BT_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_PKDET_VTH1Q_BT_Pos)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1Q_BT  BT_RFC_RBB_REG1_BRF_PKDET_VTH1Q_BT_Msk
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1I_BT_Pos  (26U)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1I_BT_Msk  (0xFUL << BT_RFC_RBB_REG1_BRF_PKDET_VTH1I_BT_Pos)
#define BT_RFC_RBB_REG1_BRF_PKDET_VTH1I_BT  BT_RFC_RBB_REG1_BRF_PKDET_VTH1I_BT_Msk

/**************** Bit definition for BT_RFC_RBB_REG2 register *****************/
#define BT_RFC_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Pos  (0U)
#define BT_RFC_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV  BT_RFC_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Msk
#define BT_RFC_RBB_REG2_BRF_RVGA_GC_LV_Pos  (1U)
#define BT_RFC_RBB_REG2_BRF_RVGA_GC_LV_Msk  (0x1FUL << BT_RFC_RBB_REG2_BRF_RVGA_GC_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_RVGA_GC_LV  BT_RFC_RBB_REG2_BRF_RVGA_GC_LV_Msk
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_Q_LV_Pos  (6U)
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_Q_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_EN_RVGA_Q_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_Q_LV  BT_RFC_RBB_REG2_BRF_EN_RVGA_Q_LV_Msk
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_I_LV_Pos  (7U)
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_I_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_EN_RVGA_I_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_EN_RVGA_I_LV  BT_RFC_RBB_REG2_BRF_EN_RVGA_I_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Pos  (8U)
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG2_LV  BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Pos  (9U)
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG1_LV  BT_RFC_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_GC_LV_Pos  (10U)
#define BT_RFC_RBB_REG2_BRF_CBPF_GC_LV_Msk  (0x3UL << BT_RFC_RBB_REG2_BRF_CBPF_GC_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_GC_LV  BT_RFC_RBB_REG2_BRF_CBPF_GC_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_EN_RC_Pos  (12U)
#define BT_RFC_RBB_REG2_BRF_CBPF_EN_RC_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_CBPF_EN_RC_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_EN_RC  BT_RFC_RBB_REG2_BRF_CBPF_EN_RC_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_FC_LV_Pos  (13U)
#define BT_RFC_RBB_REG2_BRF_CBPF_FC_LV_Msk  (0x3UL << BT_RFC_RBB_REG2_BRF_CBPF_FC_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_FC_LV  BT_RFC_RBB_REG2_BRF_CBPF_FC_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_BW_LV_Pos  (15U)
#define BT_RFC_RBB_REG2_BRF_CBPF_BW_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_CBPF_BW_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_BW_LV  BT_RFC_RBB_REG2_BRF_CBPF_BW_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_VSTART_LV_Pos  (16U)
#define BT_RFC_RBB_REG2_BRF_CBPF_VSTART_LV_Msk  (0x3UL << BT_RFC_RBB_REG2_BRF_CBPF_VSTART_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_VSTART_LV  BT_RFC_RBB_REG2_BRF_CBPF_VSTART_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_VCMREF_LV_Pos  (18U)
#define BT_RFC_RBB_REG2_BRF_CBPF_VCMREF_LV_Msk  (0x3UL << BT_RFC_RBB_REG2_BRF_CBPF_VCMREF_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_VCMREF_LV  BT_RFC_RBB_REG2_BRF_CBPF_VCMREF_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_BM_LV_Pos  (20U)
#define BT_RFC_RBB_REG2_BRF_CBPF_BM_LV_Msk  (0x7UL << BT_RFC_RBB_REG2_BRF_CBPF_BM_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_BM_LV  BT_RFC_RBB_REG2_BRF_CBPF_BM_LV_Msk
#define BT_RFC_RBB_REG2_BRF_CBPF_CC_LV_Pos  (23U)
#define BT_RFC_RBB_REG2_BRF_CBPF_CC_LV_Msk  (0xFUL << BT_RFC_RBB_REG2_BRF_CBPF_CC_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_CBPF_CC_LV  BT_RFC_RBB_REG2_BRF_CBPF_CC_LV_Msk
#define BT_RFC_RBB_REG2_BRF_EN_CBPF_LV_Pos  (27U)
#define BT_RFC_RBB_REG2_BRF_EN_CBPF_LV_Msk  (0x1UL << BT_RFC_RBB_REG2_BRF_EN_CBPF_LV_Pos)
#define BT_RFC_RBB_REG2_BRF_EN_CBPF_LV  BT_RFC_RBB_REG2_BRF_EN_CBPF_LV_Msk

/**************** Bit definition for BT_RFC_RBB_REG3 register *****************/
#define BT_RFC_RBB_REG3_BRF_EN_PKDET_LV_Pos  (0U)
#define BT_RFC_RBB_REG3_BRF_EN_PKDET_LV_Msk  (0xFUL << BT_RFC_RBB_REG3_BRF_EN_PKDET_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_EN_PKDET_LV  BT_RFC_RBB_REG3_BRF_EN_PKDET_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Pos  (4U)
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Msk  (0x1UL << BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG2_LV  BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Pos  (5U)
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Msk  (0x1UL << BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG1_LV  BT_RFC_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_VSTART_LV_Pos  (6U)
#define BT_RFC_RBB_REG3_BRF_RVGA_VSTART_LV_Msk  (0x3UL << BT_RFC_RBB_REG3_BRF_RVGA_VSTART_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_VSTART_LV  BT_RFC_RBB_REG3_BRF_RVGA_VSTART_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_VCMREF_LV_Pos  (8U)
#define BT_RFC_RBB_REG3_BRF_RVGA_VCMREF_LV_Msk  (0x3UL << BT_RFC_RBB_REG3_BRF_RVGA_VCMREF_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_VCMREF_LV  BT_RFC_RBB_REG3_BRF_RVGA_VCMREF_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_BM_LV_Pos  (10U)
#define BT_RFC_RBB_REG3_BRF_RVGA_BM_LV_Msk  (0x7UL << BT_RFC_RBB_REG3_BRF_RVGA_BM_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_BM_LV  BT_RFC_RBB_REG3_BRF_RVGA_BM_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_RZ_LV_Pos  (13U)
#define BT_RFC_RBB_REG3_BRF_RVGA_RZ_LV_Msk  (0x7UL << BT_RFC_RBB_REG3_BRF_RVGA_RZ_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_RZ_LV  BT_RFC_RBB_REG3_BRF_RVGA_RZ_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_CC_LV_Pos  (16U)
#define BT_RFC_RBB_REG3_BRF_RVGA_CC_LV_Msk  (0xFUL << BT_RFC_RBB_REG3_BRF_RVGA_CC_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_CC_LV  BT_RFC_RBB_REG3_BRF_RVGA_CC_LV_Msk
#define BT_RFC_RBB_REG3_BRF_RVGA_CFMAN_LV_Pos  (20U)
#define BT_RFC_RBB_REG3_BRF_RVGA_CFMAN_LV_Msk  (0x7UL << BT_RFC_RBB_REG3_BRF_RVGA_CFMAN_LV_Pos)
#define BT_RFC_RBB_REG3_BRF_RVGA_CFMAN_LV  BT_RFC_RBB_REG3_BRF_RVGA_CFMAN_LV_Msk

/**************** Bit definition for BT_RFC_RBB_REG4 register *****************/
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2Q_LV_Pos  (0U)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2Q_LV_Msk  (0xFUL << BT_RFC_RBB_REG4_BRF_PKDET_VTH2Q_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2Q_LV  BT_RFC_RBB_REG4_BRF_PKDET_VTH2Q_LV_Msk
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2I_LV_Pos  (4U)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2I_LV_Msk  (0xFUL << BT_RFC_RBB_REG4_BRF_PKDET_VTH2I_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH2I_LV  BT_RFC_RBB_REG4_BRF_PKDET_VTH2I_LV_Msk
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1Q_LV_Pos  (8U)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1Q_LV_Msk  (0xFUL << BT_RFC_RBB_REG4_BRF_PKDET_VTH1Q_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1Q_LV  BT_RFC_RBB_REG4_BRF_PKDET_VTH1Q_LV_Msk
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1I_LV_Pos  (12U)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1I_LV_Msk  (0xFUL << BT_RFC_RBB_REG4_BRF_PKDET_VTH1I_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_PKDET_VTH1I_LV  BT_RFC_RBB_REG4_BRF_PKDET_VTH1I_LV_Msk
#define BT_RFC_RBB_REG4_BRF_DOS_Q_LV_Pos  (16U)
#define BT_RFC_RBB_REG4_BRF_DOS_Q_LV_Msk  (0x7FUL << BT_RFC_RBB_REG4_BRF_DOS_Q_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_DOS_Q_LV    BT_RFC_RBB_REG4_BRF_DOS_Q_LV_Msk
#define BT_RFC_RBB_REG4_BRF_DOS_I_LV_Pos  (23U)
#define BT_RFC_RBB_REG4_BRF_DOS_I_LV_Msk  (0x7FUL << BT_RFC_RBB_REG4_BRF_DOS_I_LV_Pos)
#define BT_RFC_RBB_REG4_BRF_DOS_I_LV    BT_RFC_RBB_REG4_BRF_DOS_I_LV_Msk

/**************** Bit definition for BT_RFC_RBB_REG5 register *****************/
#define BT_RFC_RBB_REG5_BRF_RVGA_TX_LPBK_EN_LV_Pos  (0U)
#define BT_RFC_RBB_REG5_BRF_RVGA_TX_LPBK_EN_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_RVGA_TX_LPBK_EN_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_RVGA_TX_LPBK_EN_LV  BT_RFC_RBB_REG5_BRF_RVGA_TX_LPBK_EN_LV_Msk
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_LV_Pos  (1U)
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_LV  BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_LV_Msk
#define BT_RFC_RBB_REG5_BRF_IARY_BM_LV_Pos  (2U)
#define BT_RFC_RBB_REG5_BRF_IARY_BM_LV_Msk  (0x7UL << BT_RFC_RBB_REG5_BRF_IARY_BM_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_IARY_BM_LV  BT_RFC_RBB_REG5_BRF_IARY_BM_LV_Msk
#define BT_RFC_RBB_REG5_BRF_EN_IARRAY_LV_Pos  (5U)
#define BT_RFC_RBB_REG5_BRF_EN_IARRAY_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_EN_IARRAY_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_EN_IARRAY_LV  BT_RFC_RBB_REG5_BRF_EN_IARRAY_LV_Msk
#define BT_RFC_RBB_REG5_BRF_EN_OSDACQ_LV_Pos  (6U)
#define BT_RFC_RBB_REG5_BRF_EN_OSDACQ_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_EN_OSDACQ_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_EN_OSDACQ_LV  BT_RFC_RBB_REG5_BRF_EN_OSDACQ_LV_Msk
#define BT_RFC_RBB_REG5_BRF_EN_OSDACI_LV_Pos  (7U)
#define BT_RFC_RBB_REG5_BRF_EN_OSDACI_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_EN_OSDACI_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_EN_OSDACI_LV  BT_RFC_RBB_REG5_BRF_EN_OSDACI_LV_Msk
#define BT_RFC_RBB_REG5_BRF_RSTB_RCCAL_LV_Pos  (8U)
#define BT_RFC_RBB_REG5_BRF_RSTB_RCCAL_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_RSTB_RCCAL_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_RSTB_RCCAL_LV  BT_RFC_RBB_REG5_BRF_RSTB_RCCAL_LV_Msk
#define BT_RFC_RBB_REG5_BRF_CBPF_CAPMAN_LV_Pos  (9U)
#define BT_RFC_RBB_REG5_BRF_CBPF_CAPMAN_LV_Msk  (0x1FUL << BT_RFC_RBB_REG5_BRF_CBPF_CAPMAN_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_CBPF_CAPMAN_LV  BT_RFC_RBB_REG5_BRF_CBPF_CAPMAN_LV_Msk
#define BT_RFC_RBB_REG5_BRF_RCCAL_MANCAP_LV_Pos  (14U)
#define BT_RFC_RBB_REG5_BRF_RCCAL_MANCAP_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_RCCAL_MANCAP_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_RCCAL_MANCAP_LV  BT_RFC_RBB_REG5_BRF_RCCAL_MANCAP_LV_Msk
#define BT_RFC_RBB_REG5_BRF_RCCAL_SELXO_LV_Pos  (15U)
#define BT_RFC_RBB_REG5_BRF_RCCAL_SELXO_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_RCCAL_SELXO_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_RCCAL_SELXO_LV  BT_RFC_RBB_REG5_BRF_RCCAL_SELXO_LV_Msk
#define BT_RFC_RBB_REG5_BRF_EN_RCCAL_LV_Pos  (16U)
#define BT_RFC_RBB_REG5_BRF_EN_RCCAL_LV_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_EN_RCCAL_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_EN_RCCAL_LV  BT_RFC_RBB_REG5_BRF_EN_RCCAL_LV_Msk
#define BT_RFC_RBB_REG5_BRF_PKDET_BM_LV_Pos  (17U)
#define BT_RFC_RBB_REG5_BRF_PKDET_BM_LV_Msk  (0x7UL << BT_RFC_RBB_REG5_BRF_PKDET_BM_LV_Pos)
#define BT_RFC_RBB_REG5_BRF_PKDET_BM_LV  BT_RFC_RBB_REG5_BRF_PKDET_BM_LV_Msk
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_FRC_EN_Pos  (20U)
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_FRC_EN_Msk  (0x1UL << BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_FRC_EN_Pos)
#define BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_FRC_EN  BT_RFC_RBB_REG5_BRF_CBPF_BT_EN_FRC_EN_Msk

/**************** Bit definition for BT_RFC_RBB_REG6 register *****************/
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_BR_Pos  (0U)
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_BR_Msk  (0x3UL << BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_BR_Pos  (2U)
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_BR_Msk  (0x7UL << BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_BR_Pos  (5U)
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_BR_Msk  (0xFUL << BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_EDR_Pos  (9U)
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_EDR_Msk  (0x3UL << BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_FC_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_EDR_Pos  (11U)
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_EDR_Msk  (0x7UL << BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_BM_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_EDR_Pos  (14U)
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_EDR_Msk  (0xFUL << BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_CC_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_BR_Pos  (18U)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_BR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_BR  BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_BR_Pos  (19U)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_BR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_BR  BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_EDR_Pos  (20U)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_EDR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_EDR  BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG2_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_EDR_Pos  (21U)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_EDR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_EDR  BT_RFC_RBB_REG6_BRF_RVGA_W2X_STG1_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_BR_Pos  (22U)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_BR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_BR_Pos  (23U)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_BR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_EDR_Pos  (24U)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_EDR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG2_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_EDR_Pos  (25U)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_EDR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_W2X_STG1_LV_EDR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_BR_Pos  (26U)
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_BR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_BR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_BR  BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_BR_Msk
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_EDR_Pos  (27U)
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_EDR_Msk  (0x1UL << BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_EDR_Pos)
#define BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_EDR  BT_RFC_RBB_REG6_BRF_CBPF_BW_LV_EDR_Msk

/***************** Bit definition for BT_RFC_ADC_REG register *****************/
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Pos  (0U)
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Msk  (0xFUL << BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Pos)
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV  BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Msk
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADCREF_LV_Pos  (4U)
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADCREF_LV_Msk  (0x1UL << BT_RFC_ADC_REG_BRF_EN_LDO_ADCREF_LV_Pos)
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADCREF_LV  BT_RFC_ADC_REG_BRF_EN_LDO_ADCREF_LV_Msk
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Pos  (5U)
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Msk  (0xFUL << BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Pos)
#define BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADC_LV  BT_RFC_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Msk
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADC_LV_Pos  (9U)
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADC_LV_Msk  (0x1UL << BT_RFC_ADC_REG_BRF_EN_LDO_ADC_LV_Pos)
#define BT_RFC_ADC_REG_BRF_EN_LDO_ADC_LV  BT_RFC_ADC_REG_BRF_EN_LDO_ADC_LV_Msk
#define BT_RFC_ADC_REG_BRF_RSTB_ADC_LV_Pos  (10U)
#define BT_RFC_ADC_REG_BRF_RSTB_ADC_LV_Msk  (0x1UL << BT_RFC_ADC_REG_BRF_RSTB_ADC_LV_Pos)
#define BT_RFC_ADC_REG_BRF_RSTB_ADC_LV  BT_RFC_ADC_REG_BRF_RSTB_ADC_LV_Msk
#define BT_RFC_ADC_REG_BRF_ADC_VSP_LV_Pos  (11U)
#define BT_RFC_ADC_REG_BRF_ADC_VSP_LV_Msk  (0x3UL << BT_RFC_ADC_REG_BRF_ADC_VSP_LV_Pos)
#define BT_RFC_ADC_REG_BRF_ADC_VSP_LV   BT_RFC_ADC_REG_BRF_ADC_VSP_LV_Msk
#define BT_RFC_ADC_REG_BRF_ADC_CMPCL_LV_Pos  (13U)
#define BT_RFC_ADC_REG_BRF_ADC_CMPCL_LV_Msk  (0x7UL << BT_RFC_ADC_REG_BRF_ADC_CMPCL_LV_Pos)
#define BT_RFC_ADC_REG_BRF_ADC_CMPCL_LV  BT_RFC_ADC_REG_BRF_ADC_CMPCL_LV_Msk
#define BT_RFC_ADC_REG_BRF_ADC_CMM_LV_Pos  (16U)
#define BT_RFC_ADC_REG_BRF_ADC_CMM_LV_Msk  (0xFUL << BT_RFC_ADC_REG_BRF_ADC_CMM_LV_Pos)
#define BT_RFC_ADC_REG_BRF_ADC_CMM_LV   BT_RFC_ADC_REG_BRF_ADC_CMM_LV_Msk
#define BT_RFC_ADC_REG_BRF_EN_ADC_Q_LV_Pos  (20U)
#define BT_RFC_ADC_REG_BRF_EN_ADC_Q_LV_Msk  (0x1UL << BT_RFC_ADC_REG_BRF_EN_ADC_Q_LV_Pos)
#define BT_RFC_ADC_REG_BRF_EN_ADC_Q_LV  BT_RFC_ADC_REG_BRF_EN_ADC_Q_LV_Msk
#define BT_RFC_ADC_REG_BRF_EN_ADC_I_LV_Pos  (21U)
#define BT_RFC_ADC_REG_BRF_EN_ADC_I_LV_Msk  (0x1UL << BT_RFC_ADC_REG_BRF_EN_ADC_I_LV_Pos)
#define BT_RFC_ADC_REG_BRF_EN_ADC_I_LV  BT_RFC_ADC_REG_BRF_EN_ADC_I_LV_Msk

/***************** Bit definition for BT_RFC_TBB_REG register *****************/
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_DVDD_LV_Pos  (0U)
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_DVDD_LV_Msk  (0xFUL << BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_DVDD_LV_Pos)
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_DVDD_LV  BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_DVDD_LV_Msk
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_AVDD_LV_Pos  (4U)
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_AVDD_LV_Msk  (0xFUL << BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_AVDD_LV_Pos)
#define BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_AVDD_LV  BT_RFC_TBB_REG_BRF_SEL_LDOVREF_DAC_AVDD_LV_Msk
#define BT_RFC_TBB_REG_BRF_EN_TBB_IARRAY_LV_Pos  (8U)
#define BT_RFC_TBB_REG_BRF_EN_TBB_IARRAY_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_EN_TBB_IARRAY_LV_Pos)
#define BT_RFC_TBB_REG_BRF_EN_TBB_IARRAY_LV  BT_RFC_TBB_REG_BRF_EN_TBB_IARRAY_LV_Msk
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_DVDD_LV_Pos  (9U)
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_DVDD_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_EN_LDO_DAC_DVDD_LV_Pos)
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_DVDD_LV  BT_RFC_TBB_REG_BRF_EN_LDO_DAC_DVDD_LV_Msk
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_AVDD_LV_Pos  (10U)
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_AVDD_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_EN_LDO_DAC_AVDD_LV_Pos)
#define BT_RFC_TBB_REG_BRF_EN_LDO_DAC_AVDD_LV  BT_RFC_TBB_REG_BRF_EN_LDO_DAC_AVDD_LV_Msk
#define BT_RFC_TBB_REG_BRF_EN_DAC_LV_Pos  (11U)
#define BT_RFC_TBB_REG_BRF_EN_DAC_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_EN_DAC_LV_Pos)
#define BT_RFC_TBB_REG_BRF_EN_DAC_LV    BT_RFC_TBB_REG_BRF_EN_DAC_LV_Msk
#define BT_RFC_TBB_REG_BRF_DAC_START_LV_Pos  (12U)
#define BT_RFC_TBB_REG_BRF_DAC_START_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_DAC_START_LV_Pos)
#define BT_RFC_TBB_REG_BRF_DAC_START_LV  BT_RFC_TBB_REG_BRF_DAC_START_LV_Msk
#define BT_RFC_TBB_REG_BRF_DAC_SEL_CLK_BAR_LV_Pos  (13U)
#define BT_RFC_TBB_REG_BRF_DAC_SEL_CLK_BAR_LV_Msk  (0x1UL << BT_RFC_TBB_REG_BRF_DAC_SEL_CLK_BAR_LV_Pos)
#define BT_RFC_TBB_REG_BRF_DAC_SEL_CLK_BAR_LV  BT_RFC_TBB_REG_BRF_DAC_SEL_CLK_BAR_LV_Msk
#define BT_RFC_TBB_REG_BRF_DAC_LSB_CNT_LV_Pos  (14U)
#define BT_RFC_TBB_REG_BRF_DAC_LSB_CNT_LV_Msk  (0x3UL << BT_RFC_TBB_REG_BRF_DAC_LSB_CNT_LV_Pos)
#define BT_RFC_TBB_REG_BRF_DAC_LSB_CNT_LV  BT_RFC_TBB_REG_BRF_DAC_LSB_CNT_LV_Msk

/*************** Bit definition for BT_RFC_ATSTBUF_REG register ***************/
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Pos  (0U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Msk  (0x1UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Pos  (1U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Msk  (0x1UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Pos  (2U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Msk  (0x3UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Pos  (4U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Msk  (0x3UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Pos  (6U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Msk  (0x7UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_BM_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Pos  (9U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Msk  (0x7UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Pos  (12U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Msk  (0xFUL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CC_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Pos  (16U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Msk  (0x7UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Pos  (19U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Msk  (0x1UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Pos  (20U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Msk  (0x1FUL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_GC_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Pos  (25U)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Msk  (0x1UL << BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV  BT_RFC_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Msk
#define BT_RFC_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Pos  (26U)
#define BT_RFC_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Msk  (0x1UL << BT_RFC_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Pos)
#define BT_RFC_ATSTBUF_REG_BRF_EN_ATSTBUF_LV  BT_RFC_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Msk

/**************** Bit definition for BT_RFC_RSVD_REG1 register ****************/
#define BT_RFC_RSVD_REG1_BRF_RESERVE3_LV_Pos  (0U)
#define BT_RFC_RSVD_REG1_BRF_RESERVE3_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG1_BRF_RESERVE3_LV_Pos)
#define BT_RFC_RSVD_REG1_BRF_RESERVE3_LV  BT_RFC_RSVD_REG1_BRF_RESERVE3_LV_Msk
#define BT_RFC_RSVD_REG1_BRF_RESERVE2_LV_Pos  (8U)
#define BT_RFC_RSVD_REG1_BRF_RESERVE2_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG1_BRF_RESERVE2_LV_Pos)
#define BT_RFC_RSVD_REG1_BRF_RESERVE2_LV  BT_RFC_RSVD_REG1_BRF_RESERVE2_LV_Msk
#define BT_RFC_RSVD_REG1_BRF_RESERVE1_LV_Pos  (16U)
#define BT_RFC_RSVD_REG1_BRF_RESERVE1_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG1_BRF_RESERVE1_LV_Pos)
#define BT_RFC_RSVD_REG1_BRF_RESERVE1_LV  BT_RFC_RSVD_REG1_BRF_RESERVE1_LV_Msk
#define BT_RFC_RSVD_REG1_BRF_RESERVE0_LV_Pos  (24U)
#define BT_RFC_RSVD_REG1_BRF_RESERVE0_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG1_BRF_RESERVE0_LV_Pos)
#define BT_RFC_RSVD_REG1_BRF_RESERVE0_LV  BT_RFC_RSVD_REG1_BRF_RESERVE0_LV_Msk

/**************** Bit definition for BT_RFC_RSVD_REG2 register ****************/
#define BT_RFC_RSVD_REG2_BRF_RESERVE7_LV_Pos  (0U)
#define BT_RFC_RSVD_REG2_BRF_RESERVE7_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG2_BRF_RESERVE7_LV_Pos)
#define BT_RFC_RSVD_REG2_BRF_RESERVE7_LV  BT_RFC_RSVD_REG2_BRF_RESERVE7_LV_Msk
#define BT_RFC_RSVD_REG2_BRF_RESERVE6_LV_Pos  (8U)
#define BT_RFC_RSVD_REG2_BRF_RESERVE6_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG2_BRF_RESERVE6_LV_Pos)
#define BT_RFC_RSVD_REG2_BRF_RESERVE6_LV  BT_RFC_RSVD_REG2_BRF_RESERVE6_LV_Msk
#define BT_RFC_RSVD_REG2_BRF_RESERVE5_LV_Pos  (16U)
#define BT_RFC_RSVD_REG2_BRF_RESERVE5_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG2_BRF_RESERVE5_LV_Pos)
#define BT_RFC_RSVD_REG2_BRF_RESERVE5_LV  BT_RFC_RSVD_REG2_BRF_RESERVE5_LV_Msk
#define BT_RFC_RSVD_REG2_BRF_RESERVE4_LV_Pos  (24U)
#define BT_RFC_RSVD_REG2_BRF_RESERVE4_LV_Msk  (0xFFUL << BT_RFC_RSVD_REG2_BRF_RESERVE4_LV_Pos)
#define BT_RFC_RSVD_REG2_BRF_RESERVE4_LV  BT_RFC_RSVD_REG2_BRF_RESERVE4_LV_Msk

/*************** Bit definition for BT_RFC_INCCAL_REG1 register ***************/
#define BT_RFC_INCCAL_REG1_INCACAL_EN_Pos  (0U)
#define BT_RFC_INCCAL_REG1_INCACAL_EN_Msk  (0x1UL << BT_RFC_INCCAL_REG1_INCACAL_EN_Pos)
#define BT_RFC_INCCAL_REG1_INCACAL_EN   BT_RFC_INCCAL_REG1_INCACAL_EN_Msk
#define BT_RFC_INCCAL_REG1_INCFCAL_EN_Pos  (1U)
#define BT_RFC_INCCAL_REG1_INCFCAL_EN_Msk  (0x1UL << BT_RFC_INCCAL_REG1_INCFCAL_EN_Pos)
#define BT_RFC_INCCAL_REG1_INCFCAL_EN   BT_RFC_INCCAL_REG1_INCFCAL_EN_Msk
#define BT_RFC_INCCAL_REG1_INCACAL_WAIT_TIME_Pos  (2U)
#define BT_RFC_INCCAL_REG1_INCACAL_WAIT_TIME_Msk  (0x3FUL << BT_RFC_INCCAL_REG1_INCACAL_WAIT_TIME_Pos)
#define BT_RFC_INCCAL_REG1_INCACAL_WAIT_TIME  BT_RFC_INCCAL_REG1_INCACAL_WAIT_TIME_Msk
#define BT_RFC_INCCAL_REG1_INCFCAL_WAIT_TIME_Pos  (8U)
#define BT_RFC_INCCAL_REG1_INCFCAL_WAIT_TIME_Msk  (0x3FUL << BT_RFC_INCCAL_REG1_INCFCAL_WAIT_TIME_Pos)
#define BT_RFC_INCCAL_REG1_INCFCAL_WAIT_TIME  BT_RFC_INCCAL_REG1_INCFCAL_WAIT_TIME_Msk
#define BT_RFC_INCCAL_REG1_IDAC_OFFSET_Pos  (14U)
#define BT_RFC_INCCAL_REG1_IDAC_OFFSET_Msk  (0x7FUL << BT_RFC_INCCAL_REG1_IDAC_OFFSET_Pos)
#define BT_RFC_INCCAL_REG1_IDAC_OFFSET  BT_RFC_INCCAL_REG1_IDAC_OFFSET_Msk
#define BT_RFC_INCCAL_REG1_PDX_OFFSET_Pos  (21U)
#define BT_RFC_INCCAL_REG1_PDX_OFFSET_Msk  (0xFFUL << BT_RFC_INCCAL_REG1_PDX_OFFSET_Pos)
#define BT_RFC_INCCAL_REG1_PDX_OFFSET   BT_RFC_INCCAL_REG1_PDX_OFFSET_Msk
#define BT_RFC_INCCAL_REG1_INCCAL_START_Pos  (29U)
#define BT_RFC_INCCAL_REG1_INCCAL_START_Msk  (0x1UL << BT_RFC_INCCAL_REG1_INCCAL_START_Pos)
#define BT_RFC_INCCAL_REG1_INCCAL_START  BT_RFC_INCCAL_REG1_INCCAL_START_Msk
#define BT_RFC_INCCAL_REG1_FRC_INCCAL_CLK_ON_Pos  (30U)
#define BT_RFC_INCCAL_REG1_FRC_INCCAL_CLK_ON_Msk  (0x1UL << BT_RFC_INCCAL_REG1_FRC_INCCAL_CLK_ON_Pos)
#define BT_RFC_INCCAL_REG1_FRC_INCCAL_CLK_ON  BT_RFC_INCCAL_REG1_FRC_INCCAL_CLK_ON_Msk

/*************** Bit definition for BT_RFC_INCCAL_REG2 register ***************/
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_EN_Pos  (0U)
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_EN_Msk  (0x1UL << BT_RFC_INCCAL_REG2_EDR_INCACAL_EN_Pos)
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_EN  BT_RFC_INCCAL_REG2_EDR_INCACAL_EN_Msk
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_EN_Pos  (1U)
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_EN_Msk  (0x1UL << BT_RFC_INCCAL_REG2_EDR_INCFCAL_EN_Pos)
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_EN  BT_RFC_INCCAL_REG2_EDR_INCFCAL_EN_Msk
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_WAIT_TIME_Pos  (2U)
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_WAIT_TIME_Msk  (0x3FUL << BT_RFC_INCCAL_REG2_EDR_INCACAL_WAIT_TIME_Pos)
#define BT_RFC_INCCAL_REG2_EDR_INCACAL_WAIT_TIME  BT_RFC_INCCAL_REG2_EDR_INCACAL_WAIT_TIME_Msk
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_WAIT_TIME_Pos  (8U)
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_WAIT_TIME_Msk  (0x3FUL << BT_RFC_INCCAL_REG2_EDR_INCFCAL_WAIT_TIME_Pos)
#define BT_RFC_INCCAL_REG2_EDR_INCFCAL_WAIT_TIME  BT_RFC_INCCAL_REG2_EDR_INCFCAL_WAIT_TIME_Msk
#define BT_RFC_INCCAL_REG2_EDR_IDAC_OFFSET_Pos  (14U)
#define BT_RFC_INCCAL_REG2_EDR_IDAC_OFFSET_Msk  (0x7FUL << BT_RFC_INCCAL_REG2_EDR_IDAC_OFFSET_Pos)
#define BT_RFC_INCCAL_REG2_EDR_IDAC_OFFSET  BT_RFC_INCCAL_REG2_EDR_IDAC_OFFSET_Msk
#define BT_RFC_INCCAL_REG2_EDR_PDX_OFFSET_Pos  (21U)
#define BT_RFC_INCCAL_REG2_EDR_PDX_OFFSET_Msk  (0xFFUL << BT_RFC_INCCAL_REG2_EDR_PDX_OFFSET_Pos)
#define BT_RFC_INCCAL_REG2_EDR_PDX_OFFSET  BT_RFC_INCCAL_REG2_EDR_PDX_OFFSET_Msk

/*************** Bit definition for BT_RFC_ROSCAL_REG1 register ***************/
#define BT_RFC_ROSCAL_REG1_ROSCAL_START_Pos  (0U)
#define BT_RFC_ROSCAL_REG1_ROSCAL_START_Msk  (0x1UL << BT_RFC_ROSCAL_REG1_ROSCAL_START_Pos)
#define BT_RFC_ROSCAL_REG1_ROSCAL_START  BT_RFC_ROSCAL_REG1_ROSCAL_START_Msk
#define BT_RFC_ROSCAL_REG1_ROSCAL_BYPASS_Pos  (1U)
#define BT_RFC_ROSCAL_REG1_ROSCAL_BYPASS_Msk  (0x1UL << BT_RFC_ROSCAL_REG1_ROSCAL_BYPASS_Pos)
#define BT_RFC_ROSCAL_REG1_ROSCAL_BYPASS  BT_RFC_ROSCAL_REG1_ROSCAL_BYPASS_Msk
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_I_Pos  (2U)
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_I_Msk  (0x1UL << BT_RFC_ROSCAL_REG1_EN_ROSDAC_I_Pos)
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_I  BT_RFC_ROSCAL_REG1_EN_ROSDAC_I_Msk
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_Q_Pos  (3U)
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_Q_Msk  (0x1UL << BT_RFC_ROSCAL_REG1_EN_ROSDAC_Q_Pos)
#define BT_RFC_ROSCAL_REG1_EN_ROSDAC_Q  BT_RFC_ROSCAL_REG1_EN_ROSDAC_Q_Msk
#define BT_RFC_ROSCAL_REG1_ROSCAL_TA_Pos  (4U)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TA_Msk  (0x1FFUL << BT_RFC_ROSCAL_REG1_ROSCAL_TA_Pos)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TA    BT_RFC_ROSCAL_REG1_ROSCAL_TA_Msk
#define BT_RFC_ROSCAL_REG1_ROSCAL_TB_Pos  (13U)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TB_Msk  (0xFUL << BT_RFC_ROSCAL_REG1_ROSCAL_TB_Pos)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TB    BT_RFC_ROSCAL_REG1_ROSCAL_TB_Msk
#define BT_RFC_ROSCAL_REG1_ROSCAL_TC_Pos  (17U)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TC_Msk  (0x7FUL << BT_RFC_ROSCAL_REG1_ROSCAL_TC_Pos)
#define BT_RFC_ROSCAL_REG1_ROSCAL_TC    BT_RFC_ROSCAL_REG1_ROSCAL_TC_Msk

/*************** Bit definition for BT_RFC_ROSCAL_REG2 register ***************/
#define BT_RFC_ROSCAL_REG2_ROSCAL_DONE_Pos  (0U)
#define BT_RFC_ROSCAL_REG2_ROSCAL_DONE_Msk  (0x1UL << BT_RFC_ROSCAL_REG2_ROSCAL_DONE_Pos)
#define BT_RFC_ROSCAL_REG2_ROSCAL_DONE  BT_RFC_ROSCAL_REG2_ROSCAL_DONE_Msk
#define BT_RFC_ROSCAL_REG2_DOS_I_SW_Pos  (1U)
#define BT_RFC_ROSCAL_REG2_DOS_I_SW_Msk  (0x7FUL << BT_RFC_ROSCAL_REG2_DOS_I_SW_Pos)
#define BT_RFC_ROSCAL_REG2_DOS_I_SW     BT_RFC_ROSCAL_REG2_DOS_I_SW_Msk
#define BT_RFC_ROSCAL_REG2_DOS_Q_SW_Pos  (8U)
#define BT_RFC_ROSCAL_REG2_DOS_Q_SW_Msk  (0x7FUL << BT_RFC_ROSCAL_REG2_DOS_Q_SW_Pos)
#define BT_RFC_ROSCAL_REG2_DOS_Q_SW     BT_RFC_ROSCAL_REG2_DOS_Q_SW_Msk

/************** Bit definition for BT_RFC_RCROSCAL_REG register ***************/
#define BT_RFC_RCROSCAL_REG_ROS_ADC_Q_Pos  (0U)
#define BT_RFC_RCROSCAL_REG_ROS_ADC_Q_Msk  (0x3FFUL << BT_RFC_RCROSCAL_REG_ROS_ADC_Q_Pos)
#define BT_RFC_RCROSCAL_REG_ROS_ADC_Q   BT_RFC_RCROSCAL_REG_ROS_ADC_Q_Msk
#define BT_RFC_RCROSCAL_REG_ROS_ADC_I_Pos  (10U)
#define BT_RFC_RCROSCAL_REG_ROS_ADC_I_Msk  (0x3FFUL << BT_RFC_RCROSCAL_REG_ROS_ADC_I_Pos)
#define BT_RFC_RCROSCAL_REG_ROS_ADC_I   BT_RFC_RCROSCAL_REG_ROS_ADC_I_Msk
#define BT_RFC_RCROSCAL_REG_RCCAL_DONE_Pos  (20U)
#define BT_RFC_RCROSCAL_REG_RCCAL_DONE_Msk  (0x1UL << BT_RFC_RCROSCAL_REG_RCCAL_DONE_Pos)
#define BT_RFC_RCROSCAL_REG_RCCAL_DONE  BT_RFC_RCROSCAL_REG_RCCAL_DONE_Msk
#define BT_RFC_RCROSCAL_REG_RCCAL_START_Pos  (21U)
#define BT_RFC_RCROSCAL_REG_RCCAL_START_Msk  (0x1UL << BT_RFC_RCROSCAL_REG_RCCAL_START_Pos)
#define BT_RFC_RCROSCAL_REG_RCCAL_START  BT_RFC_RCROSCAL_REG_RCCAL_START_Msk
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE_OFFSET_Pos  (22U)
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE_OFFSET_Msk  (0xFUL << BT_RFC_RCROSCAL_REG_RC_CAPCODE_OFFSET_Pos)
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE_OFFSET  BT_RFC_RCROSCAL_REG_RC_CAPCODE_OFFSET_Msk
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE_Pos  (26U)
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE_Msk  (0x1FUL << BT_RFC_RCROSCAL_REG_RC_CAPCODE_Pos)
#define BT_RFC_RCROSCAL_REG_RC_CAPCODE  BT_RFC_RCROSCAL_REG_RC_CAPCODE_Msk

/**************** Bit definition for BT_RFC_PACAL_REG register ****************/
#define BT_RFC_PACAL_REG_PACAL_START_Pos  (0U)
#define BT_RFC_PACAL_REG_PACAL_START_Msk  (0x1UL << BT_RFC_PACAL_REG_PACAL_START_Pos)
#define BT_RFC_PACAL_REG_PACAL_START    BT_RFC_PACAL_REG_PACAL_START_Msk
#define BT_RFC_PACAL_REG_PACAL_DONE_Pos  (1U)
#define BT_RFC_PACAL_REG_PACAL_DONE_Msk  (0x1UL << BT_RFC_PACAL_REG_PACAL_DONE_Pos)
#define BT_RFC_PACAL_REG_PACAL_DONE     BT_RFC_PACAL_REG_PACAL_DONE_Msk
#define BT_RFC_PACAL_REG_SGN_CAL_RSLT_Pos  (2U)
#define BT_RFC_PACAL_REG_SGN_CAL_RSLT_Msk  (0x1UL << BT_RFC_PACAL_REG_SGN_CAL_RSLT_Pos)
#define BT_RFC_PACAL_REG_SGN_CAL_RSLT   BT_RFC_PACAL_REG_SGN_CAL_RSLT_Msk
#define BT_RFC_PACAL_REG_BC_CAL_RSLT_Pos  (3U)
#define BT_RFC_PACAL_REG_BC_CAL_RSLT_Msk  (0xFUL << BT_RFC_PACAL_REG_BC_CAL_RSLT_Pos)
#define BT_RFC_PACAL_REG_BC_CAL_RSLT    BT_RFC_PACAL_REG_BC_CAL_RSLT_Msk
#define BT_RFC_PACAL_REG_PACAL_RDY_Pos  (7U)
#define BT_RFC_PACAL_REG_PACAL_RDY_Msk  (0x1UL << BT_RFC_PACAL_REG_PACAL_RDY_Pos)
#define BT_RFC_PACAL_REG_PACAL_RDY      BT_RFC_PACAL_REG_PACAL_RDY_Msk
#define BT_RFC_PACAL_REG_PA_RSTB_FRC_EN_Pos  (8U)
#define BT_RFC_PACAL_REG_PA_RSTB_FRC_EN_Msk  (0x1UL << BT_RFC_PACAL_REG_PA_RSTB_FRC_EN_Pos)
#define BT_RFC_PACAL_REG_PA_RSTB_FRC_EN  BT_RFC_PACAL_REG_PA_RSTB_FRC_EN_Msk
#define BT_RFC_PACAL_REG_PACAL_CLK_EN_Pos  (9U)
#define BT_RFC_PACAL_REG_PACAL_CLK_EN_Msk  (0x1UL << BT_RFC_PACAL_REG_PACAL_CLK_EN_Pos)
#define BT_RFC_PACAL_REG_PACAL_CLK_EN   BT_RFC_PACAL_REG_PACAL_CLK_EN_Msk

/************** Bit definition for BT_RFC_CU_ADDR_REG1 register ***************/
#define BT_RFC_CU_ADDR_REG1_RXON_CFG_ADDR_Pos  (0U)
#define BT_RFC_CU_ADDR_REG1_RXON_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG1_RXON_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG1_RXON_CFG_ADDR  BT_RFC_CU_ADDR_REG1_RXON_CFG_ADDR_Msk
#define BT_RFC_CU_ADDR_REG1_RXOFF_CFG_ADDR_Pos  (16U)
#define BT_RFC_CU_ADDR_REG1_RXOFF_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG1_RXOFF_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG1_RXOFF_CFG_ADDR  BT_RFC_CU_ADDR_REG1_RXOFF_CFG_ADDR_Msk

/************** Bit definition for BT_RFC_CU_ADDR_REG2 register ***************/
#define BT_RFC_CU_ADDR_REG2_TXON_CFG_ADDR_Pos  (0U)
#define BT_RFC_CU_ADDR_REG2_TXON_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG2_TXON_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG2_TXON_CFG_ADDR  BT_RFC_CU_ADDR_REG2_TXON_CFG_ADDR_Msk
#define BT_RFC_CU_ADDR_REG2_TXOFF_CFG_ADDR_Pos  (16U)
#define BT_RFC_CU_ADDR_REG2_TXOFF_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG2_TXOFF_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG2_TXOFF_CFG_ADDR  BT_RFC_CU_ADDR_REG2_TXOFF_CFG_ADDR_Msk

/************** Bit definition for BT_RFC_CU_ADDR_REG3 register ***************/
#define BT_RFC_CU_ADDR_REG3_BT_TXON_CFG_ADDR_Pos  (0U)
#define BT_RFC_CU_ADDR_REG3_BT_TXON_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG3_BT_TXON_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG3_BT_TXON_CFG_ADDR  BT_RFC_CU_ADDR_REG3_BT_TXON_CFG_ADDR_Msk
#define BT_RFC_CU_ADDR_REG3_BT_TXOFF_CFG_ADDR_Pos  (16U)
#define BT_RFC_CU_ADDR_REG3_BT_TXOFF_CFG_ADDR_Msk  (0xFFFUL << BT_RFC_CU_ADDR_REG3_BT_TXOFF_CFG_ADDR_Pos)
#define BT_RFC_CU_ADDR_REG3_BT_TXOFF_CFG_ADDR  BT_RFC_CU_ADDR_REG3_BT_TXOFF_CFG_ADDR_Msk

/************** Bit definition for BT_RFC_CAL_ADDR_REG1 register **************/
#define BT_RFC_CAL_ADDR_REG1_BLE_RX_CAL_ADDR_Pos  (0U)
#define BT_RFC_CAL_ADDR_REG1_BLE_RX_CAL_ADDR_Msk  (0xFFFUL << BT_RFC_CAL_ADDR_REG1_BLE_RX_CAL_ADDR_Pos)
#define BT_RFC_CAL_ADDR_REG1_BLE_RX_CAL_ADDR  BT_RFC_CAL_ADDR_REG1_BLE_RX_CAL_ADDR_Msk
#define BT_RFC_CAL_ADDR_REG1_BT_RX_CAL_ADDR_Pos  (16U)
#define BT_RFC_CAL_ADDR_REG1_BT_RX_CAL_ADDR_Msk  (0xFFFUL << BT_RFC_CAL_ADDR_REG1_BT_RX_CAL_ADDR_Pos)
#define BT_RFC_CAL_ADDR_REG1_BT_RX_CAL_ADDR  BT_RFC_CAL_ADDR_REG1_BT_RX_CAL_ADDR_Msk

/************** Bit definition for BT_RFC_CAL_ADDR_REG2 register **************/
#define BT_RFC_CAL_ADDR_REG2_BLE_TX_CAL_ADDR_Pos  (0U)
#define BT_RFC_CAL_ADDR_REG2_BLE_TX_CAL_ADDR_Msk  (0xFFFUL << BT_RFC_CAL_ADDR_REG2_BLE_TX_CAL_ADDR_Pos)
#define BT_RFC_CAL_ADDR_REG2_BLE_TX_CAL_ADDR  BT_RFC_CAL_ADDR_REG2_BLE_TX_CAL_ADDR_Msk
#define BT_RFC_CAL_ADDR_REG2_BT_TX_CAL_ADDR_Pos  (16U)
#define BT_RFC_CAL_ADDR_REG2_BT_TX_CAL_ADDR_Msk  (0xFFFUL << BT_RFC_CAL_ADDR_REG2_BT_TX_CAL_ADDR_Pos)
#define BT_RFC_CAL_ADDR_REG2_BT_TX_CAL_ADDR  BT_RFC_CAL_ADDR_REG2_BT_TX_CAL_ADDR_Msk

/************** Bit definition for BT_RFC_CAL_ADDR_REG3 register **************/
#define BT_RFC_CAL_ADDR_REG3_TXDC_CAL_ADDR_Pos  (0U)
#define BT_RFC_CAL_ADDR_REG3_TXDC_CAL_ADDR_Msk  (0xFFFUL << BT_RFC_CAL_ADDR_REG3_TXDC_CAL_ADDR_Pos)
#define BT_RFC_CAL_ADDR_REG3_TXDC_CAL_ADDR  BT_RFC_CAL_ADDR_REG3_TXDC_CAL_ADDR_Msk

/***************** Bit definition for BT_RFC_AGC_REG register *****************/
#define BT_RFC_AGC_REG_LNA_GAIN_FRC_EN_Pos  (0U)
#define BT_RFC_AGC_REG_LNA_GAIN_FRC_EN_Msk  (0x1UL << BT_RFC_AGC_REG_LNA_GAIN_FRC_EN_Pos)
#define BT_RFC_AGC_REG_LNA_GAIN_FRC_EN  BT_RFC_AGC_REG_LNA_GAIN_FRC_EN_Msk
#define BT_RFC_AGC_REG_CBPF_GAIN_FRC_EN_Pos  (1U)
#define BT_RFC_AGC_REG_CBPF_GAIN_FRC_EN_Msk  (0x1UL << BT_RFC_AGC_REG_CBPF_GAIN_FRC_EN_Pos)
#define BT_RFC_AGC_REG_CBPF_GAIN_FRC_EN  BT_RFC_AGC_REG_CBPF_GAIN_FRC_EN_Msk
#define BT_RFC_AGC_REG_VGA_GAIN_FRC_EN_Pos  (2U)
#define BT_RFC_AGC_REG_VGA_GAIN_FRC_EN_Msk  (0x1UL << BT_RFC_AGC_REG_VGA_GAIN_FRC_EN_Pos)
#define BT_RFC_AGC_REG_VGA_GAIN_FRC_EN  BT_RFC_AGC_REG_VGA_GAIN_FRC_EN_Msk
#define BT_RFC_AGC_REG_LNA_GC_Pos       (3U)
#define BT_RFC_AGC_REG_LNA_GC_Msk       (0xFUL << BT_RFC_AGC_REG_LNA_GC_Pos)
#define BT_RFC_AGC_REG_LNA_GC           BT_RFC_AGC_REG_LNA_GC_Msk
#define BT_RFC_AGC_REG_CBPF_GC_Pos      (7U)
#define BT_RFC_AGC_REG_CBPF_GC_Msk      (0x3UL << BT_RFC_AGC_REG_CBPF_GC_Pos)
#define BT_RFC_AGC_REG_CBPF_GC          BT_RFC_AGC_REG_CBPF_GC_Msk
#define BT_RFC_AGC_REG_VGA_GC_Pos       (9U)
#define BT_RFC_AGC_REG_VGA_GC_Msk       (0x1FUL << BT_RFC_AGC_REG_VGA_GC_Pos)
#define BT_RFC_AGC_REG_VGA_GC           BT_RFC_AGC_REG_VGA_GC_Msk

/************** Bit definition for BT_RFC_TXDC_CAL_REG1 register **************/
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF0_Pos  (0U)
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF0_Msk  (0x3FFFUL << BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF0_Pos)
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF0  BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF0_Msk
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF1_Pos  (16U)
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF1_Msk  (0x3FFFUL << BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF1_Pos)
#define BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF1  BT_RFC_TXDC_CAL_REG1_TX_DC_CAL_COEF1_Msk

/************** Bit definition for BT_RFC_TXDC_CAL_REG2 register **************/
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_Q_Pos  (0U)
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_Q_Msk  (0x7FFUL << BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_Q_Pos)
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_Q  BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_Q_Msk
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_I_Pos  (16U)
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_I_Msk  (0x7FFUL << BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_I_Pos)
#define BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_I  BT_RFC_TXDC_CAL_REG2_TX_DC_CAL_OFFSET_I_Msk

#endif
