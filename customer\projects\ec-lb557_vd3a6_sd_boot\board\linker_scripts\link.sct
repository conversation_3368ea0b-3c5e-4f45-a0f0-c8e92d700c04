#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I $SDK_ROOT/drivers/cmsis/sf32lb55x -I $BSP_ROOT
#include "rtconfig.h"
#include "mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x10020000 BOOTLOADER_CODE_SIZE *2 {    ; load region size_region, 
  ER_IROM1 0x10020000 BOOTLOADER_CODE_SIZE *2 {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  ; 0x10000 to 0x80000 reserved to simulate flash.
  RW_IRAM1 BOOTLOADER_RAM_DATA_START_ADDR BOOTLOADER_RAM_DATA_SIZE*4  {  ; RW data
drv_spi_flash.o (.text.*)
   drv_spi_flash.o (.rodata.*)
   bf0_hal_qspi.o (.text.*)
   bf0_hal_qspi_ex.o (.text.*)
   bf0_hal_psram.o (.text.*)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   bf0_hal_dma.o   (.text.HAL_DMA_PollForTransfer)
   drv_common.o    (.text.HAL_GetTick)
   clock.o         (.text.rt_tick_get)
 
   bf0_hal_hpaon.o (.text.*)
   drv_psram.o (.text.*)
   *.o (.l1_non_ret_text_*)
   *.o (.l1_non_ret_rodata_*)
#ifdef BSP_USING_JLINK_RTT    
   *.o (Jlink_RTT, +First)
#endif   
   .ANY (+RW +ZI)
  }
}
