{"configurations": [{"intelliSenseMode": "clang-x64", "name": "Win32", "cppStandard": "c++11", "cStandard": "c99", "compilerPath": "C:/Keil_v5/ARM/ARMCLANG/bin/armclang", "includePath": ["C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/boards/common", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/boards/ec-lb557xxx", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/boards/include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/boards/include/config/sf32lb55x", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/drivers/Include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/drivers/cmsis/Include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/drivers/cmsis/sf32lb55x", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/external/CMSIS/Include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/ipc_queue/common", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/ipc_queue/porting/os", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/ipc_queue/porting/os/rtthread", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/ipc_queue/porting/sf32lb55x", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/ipc_queue/porting/sf32lb55x/hcpu", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/middleware/sifli_lib/lib", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/os_adaptor/inc", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/os_adaptor/src", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/projects/ec-lb557_vd3a6_sd_boot", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/customer/projects/ec-lb557_vd3a6_sd_boot/board", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/bsp/sifli/drivers", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/bsp/sifli/drivers/config/sf32lb55x", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/dfs/filesystems/devfs", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/dfs/filesystems/dhara/dhara", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/dfs/include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/drivers/include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/finsh", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/libc/compilers/armlibc", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/libc/compilers/common", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/utilities/llt_mem", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/utilities/ulog", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/components/utilities/ulog/backend", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/include", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/libcpu/arm/Cortex-M33", "C:/Users/<USER>/Desktop/C202402_WR02/B--Code/WR02/rtos/rtthread/libcpu/arm/common"], "defines": ["ARM_MATH_LOOPUNROLL", "LB55X_CHIP_ID=2", "RT_USING_ARM_LIBC", "SF32LB55X", "SIFLI_BUILD=\"000000\"", "SIFLI_VERSION=33619975", "SOC_BF0_HCPU", "USE_FULL_ASSERT", "USE_HAL_DRIVER", "__FILE__=__FILE_NAME__"]}]}