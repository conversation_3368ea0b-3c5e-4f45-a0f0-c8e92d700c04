/**
  ******************************************************************************
  * @file   rm690C0.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the rm690C0.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __rm690C0_H
#define __rm690C0_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup rm690C0
  * @{
  */

/** @defgroup rm690C0_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup rm690C0_Exported_Constants
  * @{
  */

/**
  * @brief rm690C0 chip IDs
  */
#define rm690C0_ID                  0x1000

/**
  * @brief  rm690C0 Size
  */
#define  rm690C0_LCD_PIXEL_WIDTH    LCD_HOR_RES_MAX
#define  rm690C0_LCD_PIXEL_HEIGHT   LCD_VER_RES_MAX

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define rm690C0_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define rm690C0_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define rm690C0_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  rm690C0 Registers
  */
#define rm690C0_SW_RESET           0x01
#define rm690C0_LCD_ID             0x04
#define rm690C0_DSI_ERR            0x05
#define rm690C0_POWER_MODE         0x0A
#define rm690C0_SLEEP_IN           0x10
#define rm690C0_SLEEP_OUT          0x11
#define rm690C0_PARTIAL_DISPLAY    0x12
#define rm690C0_DISPLAY_INVERSION  0x21
#define rm690C0_DISPLAY_OFF        0x28
#define rm690C0_DISPLAY_ON         0x29
#define rm690C0_WRITE_RAM          0x2C
#define rm690C0_READ_RAM           0x2E
#define rm690C0_CASET              0x2A
#define rm690C0_RASET              0x2B
#define rm690C0_PART_CASET              0x30
#define rm690C0_PART_RASET              0x31
#define rm690C0_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define rm690C0_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define rm690C0_TEARING_EFFECT     0x35
#define rm690C0_SCAN_DIRECTION_CTRL   0x36
#define rm690C0_IDLE_MODE_OFF      0x38
#define rm690C0_IDLE_MODE_ON       0x39
#define rm690C0_COLOR_MODE         0x3A
#define rm690C0_CONTINUE_WRITE_RAM 0x3C
#define rm690C0_WBRIGHT            0x51 /* Write brightness*/
#define rm690C0_DISPLAY_CTRL       0x53
#define rm690C0_HBM_WBRIGHT        0x63
#define rm690C0_HBM_ENABLE         0x66
#define rm690C0_PORCH_CTRL         0xB2
#define rm690C0_FRAME_CTRL         0xB3
#define rm690C0_GATE_CTRL          0xB7
#define rm690C0_VCOM_SET           0xBB
#define rm690C0_LCM_CTRL           0xC0
#define rm690C0_SET_TIME_SRC       0xC2
#define rm690C0_SET_DISP_MODE      0xC4
#define rm690C0_VCOMH_OFFSET_SET   0xC5
#define rm690C0_FR_CTRL            0xC6
#define rm690C0_POWER_CTRL         0xD0
#define rm690C0_LCD_ID_EX          0xDA
#define rm690C0_PV_GAMMA_CTRL      0xE0
#define rm690C0_NV_GAMMA_CTRL      0xE1
#define rm690C0_SPI2EN             0xE7
#define rm690C0_CMD_PAGE_SWITCH    0xFE

/**
  * @}
  */

/** @defgroup rm690C0_Exported_Functions
  * @{
  */
void     rm690C0_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t rm690C0_ReadID(LCDC_HandleTypeDef *hlcdc);

void     rm690C0_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     rm690C0_DisplayOff(LCDC_HandleTypeDef *hlcdc);

void rm690C0_SetHBM(LCDC_HandleTypeDef *hlcdc, bool enable, uint8_t br);
void rm690C0_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void rm690C0_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void rm690C0_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t rm690C0_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void rm690C0_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void rm690C0_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);
void rm690C0_IdleModeOn(LCDC_HandleTypeDef *hlcdc);
void rm690C0_IdleModeOff(LCDC_HandleTypeDef *hlcdc);
void rm690C0_Rotate(LCDC_HandleTypeDef *hlcdc, LCD_DrvRotateTypeDef degrees);


/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __rm690C0_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
