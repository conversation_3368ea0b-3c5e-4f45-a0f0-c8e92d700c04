/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ag333x_pair.c
@Time    :   2025/3/4 11:15:11
*
**************************************************************************/
#include "ag333x_pair.h"
#include <rtthread.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "gps_api.h"
#include "cfg_header_def.h"
#include "airoha_ag333x.h"
#include "igs_global.h"
#include "mytime.h"
#include "gnss_epo_download.h"
#include "gps_dev.h"
#include "service_datetime.h"
#ifndef IGS_BOOT
#include "qw_sensor_user.h"
#include "qw_system_params.h"
#endif

#if GPS_USING_AG333X

typedef struct{
    const char *cmd;
    void (*parer)(const char* pair_cmd, const char* pair_ack);
}ag333x_pair_parser;

static struct gnss_module_sysinfo{
    bool power_on;
    bool epo_valid;
    bool epoc_ack_reved;
    bool epoc_enable;
    bool fw_config_error;
    bool request_time;
    bool request_location;
    bool dual_band;
    uint32_t week_start;
    uint32_t week_sec_start;
    uint32_t week;
    uint32_t week_sec;
    uint32_t timer_tick;
    uint8_t gnss_epo[GNSS_EPO_TYPE_MAX]; // GNSS是否请求该类型文件 true：请求 false：没有
} gps_sys_info;

static uint32_t gnss_week_wn_to_s(uint32_t week, uint32_t week_sec)
{
    //604800 = 7*24*60*60 (一周的秒数)
    uint32_t difFromBegin = week * 604800 + week_sec;
    //1980-01-06 00:00:00 315936000
    return difFromBegin + 315936000;
}

static void gnss_pair_is_power_on(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR001,002,0*39\r\n ==> Power on was successful.
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        gps_sys_info.power_on = true;
        DRV_GPS_LOG_D("gps power on\n");
        set_gps_prefer_mode();
        // gps_config_default();

        //GPS 上电成功：标志自检通过
#ifndef IGS_BOOT
        //给工模传递init信息
        fat_dev_info_t fat_dev_info={0};
        fat_dev_info.dev_type = FAT_DEV_GPS;
        fat_dev_info.dev_id = 0;
        fat_dev_info.dev_state = 1;
        fat_set_dev_init_info(fat_dev_info);
#endif
    }
}

static void gnss_pair_request_aiding_epo(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR010,0,0,2276,547620*38
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        char* endchar = NULL;
        uint32_t week = 0, week_s = 0, request_type = 0;
        week = strtol(&pair_ack[13], &endchar, 10);
        if(week){
            week_s = strtol((endchar + 1), NULL, 10);
            gps_sys_info.week = week;
            gps_sys_info.week_sec = week_s;
            if(gps_sys_info.week_start == 0){
                gps_sys_info.week_start = week;
                gps_sys_info.week_sec_start = week_s;
            }
            request_type = strtol(&pair_ack[11], NULL, 10);
            if(request_type < GNSS_EPO_TYPE_MAX){
                gps_sys_info.gnss_epo[request_type] = true;
                DRV_GPS_LOG_D("request epo type = %d\n",request_type);
            }
        }
    }
}

static void gnss_pair_request_aiding_time(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR010,1,0,2276,547620*38
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        gps_sys_info.request_time = true;
    }
}

static void gnss_pair_request_aiding_location(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR010,2,0,2276,547620*38
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        gps_sys_info.request_location = true;
    }
}

static void gnss_pair_get_dual_band(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR105,0*22
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        // rt_kprintf("dual band\n");
        if(pair_ack[9] == '0')
        {
            gps_sys_info.dual_band = false;
        }
        else if (pair_ack[9] == '1')
        {
            gps_sys_info.dual_band = true;
        }
        // rt_kprintf("dual band end\n");
    }
}

static void gnss_pair_get_epoc_status(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR508,1,1,1*2B
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))){
        if(pair_ack[9] == '1' && pair_ack[11] == '1' /*&& pair_ack[13] == '1'*/){
            gps_sys_info.epoc_enable = true;
        }else{
            if(gps_sys_info.fw_config_error == false){
                if(pair_ack[11] == '0' || pair_ack[13] == '0'){
                    gps_sys_info.fw_config_error = true;
                }
            }
            ag333x_epoc_config();
            gps_sys_info.epoc_enable = true;
        }
        gps_sys_info.epoc_ack_reved = true;
    }
}

static void gnss_pair_set_ref_utc(void)
{
    char tmp[64];
    char tmpencode[72];
    sprintf(tmp,"PAIR590,%d,%d,%d,%d,%d,%d",g_sysTime.sysTime_t.tm_year,
    		g_sysTime.sysTime_t.tm_mon,g_sysTime.sysTime_t.tm_mday,
    		g_sysTime.sysTime_t.tm_hour,g_sysTime.sysTime_t.tm_min,
    		g_sysTime.sysTime_t.tm_sec);
    uint16_t size = ag333x_pair_command_encode((uint8_t *)tmp, strlen(tmp), (uint8_t *)tmpencode, 128);
    gps_gnss_send((uint8_t *)tmpencode, size);
}

static void gnss_pair_get_ref_utc(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR591,2000,01,01,09,00,20*3E
//    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))){
//        if(g_sysTime.sysTime_t.tm_year < 2024){return;}
//        char* endchar = NULL;
//        uint32_t year = 0;
//        year = strtol(&pair_ack[9], &endchar, 10);
//        if(year != g_sysTime.sysTime_t.tm_year){
//            gnss_pair_set_ref_utc();
//        }else{
//            qw_tm_t gps_ref_time;
//            gps_ref_time.tm_year = year;
//            gps_ref_time.tm_mon = strtol((endchar + 1), &endchar, 10);
//            gps_ref_time.tm_mday = strtol((endchar + 1), &endchar, 10);
//            gps_ref_time.tm_hour = strtol((endchar + 1), &endchar, 10);
//            gps_ref_time.tm_min = strtol((endchar + 1), &endchar, 10);
//            gps_ref_time.tm_sec = strtol((endchar + 1), &endchar, 10);
//            uint32_t gps_ref_utc_s = util_calendar_2_fittime(&gps_ref_time, 0, 0, 0, 0, 0, 0);
//            if(g_sysTime.sysTime_s > gps_ref_utc_s){
//                if(g_sysTime.sysTime_s - gps_ref_utc_s > 5){
//                    gnss_pair_set_ref_utc();
//                }
//            }else if(g_sysTime.sysTime_s < gps_ref_utc_s){
//                if(gps_ref_utc_s - g_sysTime.sysTime_s > 5){
//                    gnss_pair_set_ref_utc();
//                }
//            }
//        }
//    }
}

static void gnss_pair_nagivation_save(const char* pair_cmd, const char* pair_ack)
{
    //$PAIR511,0*3E
    if (!strncmp(pair_cmd, pair_ack, strlen(pair_cmd))) {
        rt_sem_t sem = gps_navigation_sem_get();
        if(sem){
            rt_sem_release(sem);
        }
    }
}

static const ag333x_pair_parser gps_pair_parser_table[] = {
    {"$PAIR001,002,0",gnss_pair_is_power_on},
    {"$PAIR010,0",gnss_pair_request_aiding_epo},
    {"$PAIR010,1",gnss_pair_request_aiding_time},
    {"$PAIR010,2",gnss_pair_request_aiding_location},
    {"$PAIR508",gnss_pair_get_epoc_status},
    {"$PAIR591",gnss_pair_get_ref_utc},
    {"$PAIR105",gnss_pair_get_dual_band},
    {"$PAIR001,511,0",gnss_pair_nagivation_save},
};

void ag333x_parser_pair_init(void)
{
    gps_sys_info.power_on = false;
    gps_sys_info.epo_valid = true;
    gps_sys_info.week_start = 0;
    gps_sys_info.week_sec_start = 0;
    gps_sys_info.week = 0;
    gps_sys_info.week_sec = 0;
    gps_sys_info.timer_tick = 0;
    gps_sys_info.epoc_ack_reved = false;
    gps_sys_info.epoc_enable = false;
    gps_sys_info.fw_config_error = false;
    memset(gps_sys_info.gnss_epo, 0, sizeof(char)*GNSS_EPO_TYPE_MAX);
    gps_sys_info.request_time = false;
    gps_sys_info.request_location = false;
    gps_sys_info.dual_band = true;
}

void ag333x_parser_pair_close(void)
{
    // 关机时无定位信息，不记录时间
    if (g_dev_gps_data.gpsSignal == enumGPS_SIG0)
    {
        return;
    }

    qw_tm_t rtc_time = {0};
    rtc_time.tm_year = g_dev_gps_data.date.year + 2000;
    rtc_time.tm_mon = g_dev_gps_data.date.month;
    rtc_time.tm_mday = g_dev_gps_data.date.day;
    rtc_time.tm_hour = g_dev_gps_data.time.hours;
    rtc_time.tm_min = g_dev_gps_data.time.minutes;
    rtc_time.tm_sec = g_dev_gps_data.time.seconds;

    int32_t timezone = service_datetime_get_timezone();
    uint32_t cur_s = service_datetime_datetime2gmt(&rtc_time, timezone);

    set_cfg_gps_get_time(cur_s);
}

void ag333x_parser_pair_cmd_process(const char* pair_ack)
{
    // rt_kprintf("ag333x_parser_pair_cmd_process pair_ack:%s\n", pair_ack);
    for(uint16_t i = 0; i < sizeof(gps_pair_parser_table) / sizeof(gps_pair_parser_table[0]); i ++)
    {
        if(gps_pair_parser_table[i].parer)
        {
            gps_pair_parser_table[i].parer(gps_pair_parser_table[i].cmd, pair_ack);
        }
    }
    gps_sys_info.timer_tick ++;
}

bool ag333x_get_tow_wn_time(uint32_t* week, uint32_t* week_sec)
{
    // rt_kprintf("gps_sys_info.power_on:%u, gps_sys_info.week_start:%u\n", gps_sys_info.power_on, gps_sys_info.week_start);
    if(gps_sys_info.power_on && gps_sys_info.week_start)
    {
        *week = gps_sys_info.week;
        *week_sec = gps_sys_info.week_sec;
        return true;
    }
    return false;
}

//刚上电时，判断上一次GPS运行时间，若超过俩小时则重新注入
bool ag333x_gnss_is_need_update_epo(void)
{
    if(gps_sys_info.epo_valid == false ||
       gnss_epo_download_info.download_mode == GNSS_EPO_DOWNLOAD_3D_RTCRAM ||
       gnss_epo_download_info.download_mode == GNSS_EPO_DOWNLOAD_6H_RTCRAM){
        return true;
    }
    if(gps_sys_info.power_on && gps_sys_info.week_start)
    {

        if(get_cfg_gps_get_time() && get_cfg_gps_get_time() != 0xffffffff)
        {
            uint32_t cur_s = gnss_week_wn_to_s(gps_sys_info.week_start, gps_sys_info.week_sec_start);
            uint32_t interval_time = 7200;
            // if(gnss_epo_download_info.download_mode == GNSS_EPO_DOWNLOAD_6H_FLASH)
            // {
            //     interval_time = 7200; // 2 hour
            // }
            // else
            // {
            //     interval_time = 86400; // 24 hour
            // }

            if(cur_s > get_cfg_gps_get_time() && (cur_s - get_cfg_gps_get_time() > interval_time)) // > 2 hour
            {
                gps_sys_info.epo_valid = false;
                return true;
            }
        }
        else
        {
            gps_sys_info.epo_valid = false;
            return true;
        }
    }
    return false;
}

bool gnss_device_is_power_on(void)
{
    return gps_sys_info.power_on;
}

bool gnss_epo_aiding_is_requested(uint8_t type)
{
    return gps_sys_info.gnss_epo[type];
}

void gnss_epo_aiding_request_clear(uint8_t type)
{
    if(type < GNSS_EPO_TYPE_MAX)
    {
        gps_sys_info.gnss_epo[type] = false;
    }
}

bool gnss_time_aiding_is_requested(void)
{
    return gps_sys_info.request_time;
}

void gnss_time_aiding_request_clear(void)
{
    gps_sys_info.request_time = false;
}

bool gnss_location_aiding_is_requested(void)
{
    return gps_sys_info.request_location;
}

bool gnss_location_effective(void)
{
    if(cfg_gps_backup_position_invalid() || get_cfg_gps_get_time() == 0)
    {
        DRV_GPS_LOG_I("gnss_location_effective false\n");
        return false;
    }

    uint32_t cur_s = service_datetime_get_gmt_time() - service_datetime_get_timezone();
    uint32_t interval_time = 1200;

    // 距离上次获取时间超过20分钟认为上一次参考位置无效
    if(cur_s >= get_cfg_gps_get_time() && (cur_s - get_cfg_gps_get_time() <= interval_time))
    {
        return true;
    }
    else
    {
        DRV_GPS_LOG_I("gnss_location_effective timeout\n");
        return false;
    }
}

void gnss_location_aiding_request_clear(void)
{
    gps_sys_info.request_location = false;
}

bool gnss_get_dual_band(void)
{
    return gps_sys_info.dual_band;
}

void gnss_set_epo_valid(bool valid)
{
    gps_sys_info.epo_valid = valid;
}

/*****************************************************/
#include "airoha_ag333x.h"
static void gnss_read_process(void *parameter)
{
    uint8_t read_buf[512];
    ag333x_init();
    ag333x_gps_poweron();
    // airoha_ag333x_poweron_with_chip_reset();
    while(1)
    {
        rt_memset(read_buf, 0, 512);
        if(RT_EOK == ag333x_gps_get(read_buf, 512))
        {
            rt_kprintf("%s\n", read_buf);
        }
        rt_thread_delay(1000);
    }
}

int gnss_init(void)
{
    if (rt_thread_find("gnss")!= RT_NULL)
    {
        return -1;
    }
    rt_thread_t init_task = rt_thread_create("gnss",
                        gnss_read_process,
                        NULL,
                        4096,
                        RT_THREAD_PRIORITY_MIDDLE,
                        RT_THREAD_TICK_DEFAULT);
    RT_ASSERT(init_task != NULL);
    rt_thread_startup(init_task);
    return 0;
}
// MSH_CMD_EXPORT(gnss_init, gnss_init);

int gnss_on(void)
{
    ag333x_init();
    ag333x_gps_poweron();
    return 0;
}
// MSH_CMD_EXPORT(gnss_on, gnss_on);

int gnss_off(void)
{
    ag333x_gps_poweroff();
    return 0;
}
// MSH_CMD_EXPORT(gnss_off, gnss_off);

#endif