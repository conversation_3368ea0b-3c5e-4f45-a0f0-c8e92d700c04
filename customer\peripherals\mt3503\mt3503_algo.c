/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503_algo.c
@Time    :   2025/02/11 17:47:26
@Brief   :   mt3503滤波防抖算法
@Details :
*
************************************************************/

#include "mt3503_algo.h"
#include <rtthread.h>

#ifndef ABS
#define ABS(x) ((x) > 0 ? (x) : -(x))
#endif

// 状态检测缓存
typedef struct
{
    mxs_algo_motion_event_t state; // 当前的状态
    bool     algo_sta;             // 正常状态，异常状态
    uint32_t time_gap;             // 同一状态累计时间、持续时间，单位ms
    int32_t counter;	            // 当前状态 旋钮x轴累计值
    uint32_t y_time_gap;           // 同一状态累计Y轴偏移大于X轴持续的时间
} mxs_algo_temp_t;


// 平均检测缓存
typedef struct
{
    uint32_t data_index;           // 正常状态，异常状态
    int32_t  counter;	            // 当前状态 旋钮x轴累计值
    int16_t  data[MXS_MOTION_DETECTED_MAX_INTERVAL/MXS_MOTION_SAMPLE_INTERVAL]; // 缓存历史值
} mxs_algo_value_t;

// 数据上报缓存
typedef struct
{
    int32_t  counter;             // 正常状态统记录上一次发送的时间
    uint32_t times;               // 正常状态统计发送次数
    uint32_t time_gap; 	        // 正常状态旋钮x轴旋转上报后持续时间，单位ms
} mxs_algo_report_t;

static mxs_algo_temp_t mxs_algo_stash={0};                    // 正常状态
static mxs_algo_temp_t mxs_algo_unkown={0};                   // 未知状态
static mxs_algo_temp_t *p_mxs_algo_now = &mxs_algo_unkown;
static uint32_t motion_x_key_event = MXS_ALGO_MOTION_RELEASE; // 正常状态上次的事件
static mxs_algo_report_t mxs_algo_report = {0};
static int (*user_knob_callback)(int32_t keyval,int32_t key_event) = RT_NULL; // 事件处理回调
static mxs_algo_value_t mxs_algo_val = {0};                   // 200MS内平均值


// 异常状态下更新旋转状态
static void mxs_algo_unkonw_state(mxs_algo_motion_event_t state){
    // 触发异常,异常结束正常,后台正常(切换)，异常结束异常：忽略上次异常(清除)
    memset((void*)&mxs_algo_unkown,0,sizeof(mxs_algo_temp_t)); // 清除
    mxs_algo_unkown.state = state;
    p_mxs_algo_now = &mxs_algo_unkown;
}

// 异常状态转正常
static void mxs_algo_stash_state(void){
    // 异常状态转正常
    memcpy((void*)&mxs_algo_stash,(void*)&mxs_algo_unkown,sizeof(mxs_algo_temp_t));
    mxs_algo_stash.algo_sta = true;

    memset(&mxs_algo_unkown,0,sizeof(mxs_algo_temp_t));        // 清除

    p_mxs_algo_now = &mxs_algo_stash;
}

/**
@brief 光旋钮防抖防误触算法复位.
@param[in] callback 按键事件回调函数
@retval ret of the successsful or not.
*/
int mxs_algo_reset(void){

    memset((void*)&mxs_algo_stash,0,sizeof(mxs_algo_temp_t));
    memset((void*)&mxs_algo_unkown,0,sizeof(mxs_algo_temp_t));
    p_mxs_algo_now = &mxs_algo_unkown;
    motion_x_key_event = MXS_ALGO_MOTION_RELEASE;
    memset((void*)&mxs_algo_report,0,sizeof(mxs_algo_report_t));

    memset((void*)&mxs_algo_val,0,sizeof(mxs_algo_value_t));

    return 0;
}


/**
@brief 光旋钮防抖防误触算法初始化.
@param[in] callback 按键事件回调函数
@retval ret of the successsful or not.
*/
int mxs_algo_init(int (*callback)(int32_t keyval,int32_t key_event)){

    if(callback == RT_NULL)
    {
        return -1;
    }
    user_knob_callback = callback;

    mxs_algo_reset();

    return 0;
}

/**
@brief 光旋钮防抖防误触算法.
@param[in] dx 旋钮x轴单次增量
@param[in] dy 旋钮y轴单次增量
@param[in] timestamp 时间撮，暂未使用
@param[out] event 返回当前的事件
@param[out] value 返回当前事件的累计增量。
@retval ret of the successsful or not.
*/
#ifdef USE_NEW_ADJUST
int mxs_algo_deal(int16_t dx,
                  int16_t dy,
                  uint32_t timestamp,
                  mxs_algo_motion_event_t* event,
                  int32_t* value)
{
    // 状态检测：X轴增量绝对值大于 MXS_MOTION_X_DELTA_NOISE 才认为是在转动X轴，防止误触
    if(ABS(dx) >= MXS_MOTION_X_DELTA_NOISE)                                 // 旋转有效
    {
        // 顺时针逆时针旋转方向判断
        if(dx<0){
            if(p_mxs_algo_now->state != MXS_ALGO_MOTION_ROTATE_CW){
                //MT3503_ALGO_LOG_I("mxs_x: state = %d gap = %d counter = %d cw\n", p_mxs_algo_now->state,p_mxs_algo_now->time_gap,p_mxs_algo_now->counter);
                mxs_algo_unkonw_state(MXS_ALGO_MOTION_ROTATE_CW);          // 顺时针转
            }
        }else{
            if(p_mxs_algo_now->state != MXS_ALGO_MOTION_ROTATE_CCW){
                //MT3503_ALGO_LOG_I("mxs_x: state = %d gap = %d counter = %d ccw\n", p_mxs_algo_now->state,p_mxs_algo_now->time_gap,p_mxs_algo_now->counter);
                mxs_algo_unkonw_state(MXS_ALGO_MOTION_ROTATE_CCW);         // 逆时针转
            }
        }

        /* 异常规则：Y轴先起手大动作，持续一段时间，忽略X轴小动作 */
        if((p_mxs_algo_now->algo_sta==false)&&(ABS(dx) < ABS(dy))){    // Y轴起手大动作
            p_mxs_algo_now->y_time_gap += MXS_MOTION_SAMPLE_INTERVAL;  // Y轴时间累积
            if(p_mxs_algo_now->y_time_gap>=MXS_MOTION_Y_DELTA_MAX_LASTTIME){
                //MT3503_ALGO_LOG_I("mxs_x: state = %d gap = %d counter = %d realse\n", p_mxs_algo_now->state,p_mxs_algo_now->time_gap,p_mxs_algo_now->counter);
                mxs_algo_unkonw_state(MXS_ALGO_MOTION_RELEASE);            // 未旋转状态
            }
        }else{
            p_mxs_algo_now->y_time_gap = 0;
        }
    }
    else{
        if(p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE){
            //MT3503_ALGO_LOG_I("mxs_x: state = %d gap = %d counter = %d realse\n", p_mxs_algo_now->state,p_mxs_algo_now->time_gap,p_mxs_algo_now->counter);
            mxs_algo_unkonw_state(MXS_ALGO_MOTION_RELEASE);                // 未旋转状态
        }
    }

    p_mxs_algo_now->time_gap += MXS_MOTION_SAMPLE_INTERVAL; // X轴时间累积
    p_mxs_algo_now->counter  += dx;                         // X轴增量累积

    //MT3503_ALGO_LOG_I("mxs_n: state = %d gap = %d counter = %d\n", p_mxs_algo_now->state,p_mxs_algo_now->time_gap,p_mxs_algo_now->counter);

    // 首次上报：
    // 状态切换延时：防抖，进入转动状态,触发延时 MXS_MOTION_DETECTED_MIN_INTERVAL ，退出转动状态，解除延时 MXS_MOTION_DETECTED_MAX_INTERVAL
    uint32_t motion_x_detected_time = MXS_MOTION_DETECTED_MIN_INTERVAL; //触发延时
    if(motion_x_key_event == MXS_ALGO_MOTION_RELEASE)
         motion_x_detected_time = MXS_MOTION_DETECTED_MIN_INTERVAL;      //触发延时
    else motion_x_detected_time = MXS_MOTION_DETECTED_MAX_INTERVAL;      //解除延时
    if((!p_mxs_algo_now->algo_sta)&&(p_mxs_algo_now->time_gap>motion_x_detected_time)){   // 动作持续时间有效
        // 首次上报规则一：报告间隔超过设定的 motion_x_detected_time，旋转时增量累计超过MXS_MOTION_X_START_COUNTER，上报。
        if((p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE)&&(ABS(p_mxs_algo_now->counter)>=MXS_MOTION_X_START_COUNTER)){ // 开始上报
            if(p_mxs_algo_now->state == MXS_ALGO_MOTION_ROTATE_CW){
                motion_x_key_event = MXS_ALGO_MOTION_ROTATE_CW;
                mxs_algo_stash_state(); /* 异常状态转正常，保存状态 */

                mxs_algo_report.time_gap = 0;
                mxs_algo_report.counter = 0;
                mxs_algo_report.times = 1;

                if(user_knob_callback != RT_NULL) {
                    int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
                    MT3503_ALGO_LOG_I("mxs_x: 1 start report cw ret =%d...\n",ret);
                }
            }
            if(p_mxs_algo_now->state == MXS_ALGO_MOTION_ROTATE_CCW){
                motion_x_key_event = MXS_ALGO_MOTION_ROTATE_CCW;
                mxs_algo_stash_state();  /* 异常状态转正常 */

                mxs_algo_report.time_gap = 0;
                mxs_algo_report.counter = 0;
                mxs_algo_report.times = 1;

                if(user_knob_callback != RT_NULL) {
                    int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
                    MT3503_ALGO_LOG_I("mxs_x: 1 start report ccw ret =%d...\n",ret);
                }
            }
        }else{
            if(motion_x_key_event!=MXS_ALGO_MOTION_RELEASE){
                motion_x_key_event = MXS_ALGO_MOTION_RELEASE;

                p_mxs_algo_now->state = MXS_ALGO_MOTION_RELEASE;
                p_mxs_algo_now->time_gap = 0;	// 清除X轴时间累积
                p_mxs_algo_now->counter = 0;	// 清除X轴增量累积
                p_mxs_algo_now->y_time_gap = 0; // 清除Y轴增量累积

                mxs_algo_report.time_gap = -1;
                mxs_algo_report.counter = 0;
                mxs_algo_report.times = 0;

                if(user_knob_callback != RT_NULL) {
                    int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
                    MT3503_ALGO_LOG_I("mxs_x: 1 report realse ret =%d...\n",ret);
                }
            }
        }
    }

    // 首次上报规则二：X轴增量绝对值大于 0 持续 MXS_MOTION_DETECTED_MAX_GAP 之内，累计值大于最小上报角度 MXS_MOTION_X_START_COUNTER
    if (motion_x_key_event == MXS_ALGO_MOTION_RELEASE)
    {
        mxs_algo_val.counter +=  dx;                                         // 载入最新值
        mxs_algo_val.counter -=  mxs_algo_val.data[mxs_algo_val.data_index]; // 剔除最旧值
        mxs_algo_val.data[mxs_algo_val.data_index] = dx;
        mxs_algo_val.data_index++;
        mxs_algo_val.data_index = (mxs_algo_val.data_index)%(MXS_MOTION_DETECTED_MAX_INTERVAL/MXS_MOTION_SAMPLE_INTERVAL);

        if (ABS(mxs_algo_val.counter) >= MXS_MOTION_X_START_COUNTER)
        {
            if (mxs_algo_val.counter < 0)
            {
                motion_x_key_event = MXS_ALGO_MOTION_ROTATE_CW;

                mxs_algo_report.time_gap = -1;
                mxs_algo_report.counter = 0;
                mxs_algo_report.times = 0;

                if(user_knob_callback != RT_NULL) {
                    int32_t ret = user_knob_callback(motion_x_key_event,mxs_algo_val.counter);
                    MT3503_ALGO_LOG_I("mxs_x: 2 start report cw ret =%d...\n",ret);
                }
            }
            else
            {
                motion_x_key_event = MXS_ALGO_MOTION_ROTATE_CCW;

                mxs_algo_report.time_gap = -1;
                mxs_algo_report.counter = 0;
                mxs_algo_report.times = 0;

                if(user_knob_callback != RT_NULL) {
                    int32_t ret = user_knob_callback(motion_x_key_event,mxs_algo_val.counter);
                    MT3503_ALGO_LOG_I("mxs_x: 2 start report ccw ret =%d...\n",ret);
                }
            }
        }
        else
        {
            // 无用逻辑：在常态，释放状态，防止X轴时间累积值，X轴增量累积值，因长时间亮屏越界。
            if(p_mxs_algo_now->state == MXS_ALGO_MOTION_RELEASE){
                p_mxs_algo_now->time_gap = 0;	// 清除X轴时间累积
                p_mxs_algo_now->counter = 0;	// 清除X轴增量累积
                p_mxs_algo_now->y_time_gap = 0; // 清除Y轴增量累积
            }
        }
    }

    // 再次上报：
    if((p_mxs_algo_now->algo_sta)&&(motion_x_key_event!=MXS_ALGO_MOTION_RELEASE)){ /* 正常状态，旋转中，间隔上报 */
        // 再次上报规则一：报告间隔超过设定的 MXS_MOTION_DETECTED_MAX_GAP，再次上报。
        if(mxs_algo_report.time_gap>= MXS_MOTION_DETECTED_MAX_GAP){
            if(user_knob_callback != RT_NULL) {
                int32_t ret = user_knob_callback(motion_x_key_event,mxs_algo_report.counter);
                MT3503_ALGO_LOG_I("mxs_x: max report %d times = %d ret =%d...\n",motion_x_key_event,mxs_algo_report.times,ret);
            }

            mxs_algo_report.time_gap = 0;
            mxs_algo_report.counter = 0;
            mxs_algo_report.times ++;
        }
        // 再次上报规则二：报告间隔超过设定的 MXS_MOTION_DETECTED_MIN_GAP并且 增量累计超过MXS_MOTION_X_REPORT_COUNTER，再次上报。
        if((mxs_algo_report.time_gap>= MXS_MOTION_DETECTED_MIN_GAP)&&(ABS(mxs_algo_report.counter)>=MXS_MOTION_X_REPORT_COUNTER)){
            if(user_knob_callback != RT_NULL) {
                int32_t ret = user_knob_callback(motion_x_key_event,mxs_algo_report.counter);
                MT3503_ALGO_LOG_I("mxs_x: min report %d times = %d ret =%d...\n",motion_x_key_event,mxs_algo_report.times,ret);
            }

            mxs_algo_report.time_gap = 0;
            mxs_algo_report.counter = 0;
            mxs_algo_report.times ++;
        }

        if(mxs_algo_report.time_gap>=0) {
            mxs_algo_report.time_gap += MXS_MOTION_SAMPLE_INTERVAL;
            mxs_algo_report.counter +=  dx;
        }
    }

    // 暂不扩展：异常状态，正常后台
    if((p_mxs_algo_now->algo_sta==false)&&(mxs_algo_stash.algo_sta)){
        mxs_algo_stash.time_gap += MXS_MOTION_SAMPLE_INTERVAL;
        //mxs_algo_stash.counter  += dx;
    }

    *event = motion_x_key_event;
    if(p_mxs_algo_now->algo_sta||((p_mxs_algo_now->algo_sta==false)&&(mxs_algo_stash.algo_sta))){
        *value = mxs_algo_stash.counter;
    }else{
        *value = mxs_algo_unkown.counter;
    }

    return 0;
}
#else
/**
@brief 光旋钮防抖防误触算法.
@param[in] dx 旋钮x轴单次增量
@param[in] dy 旋钮y轴单次增量
@param[in] timestamp 时间撮，暂未使用
@param[out] event 返回当前的事件
@param[out] value 返回当前事件的累计增量。
@retval ret of the successsful or not.
*/
int mxs_algo_deal(int16_t dx, int16_t dy, uint32_t timestamp,mxs_algo_motion_event_t *event, int32_t *value)
{
    static uint32_t time_ccw = 0;
    static uint32_t time_cw = 0;
    static uint32_t time_realse = 0;

    /* 异常规则：Y轴先起手大动作，持续一段时间，忽略X轴小动作 */
    if(ABS(dy) >= MXS_MOTION_Y_DELTA_NOISE){
        p_mxs_algo_now->counter = 0;
        p_mxs_algo_now->time_gap = 0;
        dx = 0;
        MT3503_ALGO_LOG_D("mxs_y: report act ...\n");
    }

    //if((ABS(dx) <= ABS(dy))&&(ABS(dx) > 0)) // Y轴有动作
    //{
       //p_mxs_algo_now->y_time_gap += MXS_MOTION_SAMPLE_INTERVAL;  // Y轴时间累积
       //if(p_mxs_algo_now->y_time_gap>=MXS_MOTION_Y_DELTA_MAX_LASTTIME)
       //{
       //     MT3503_ALGO_LOG_D("mxs_y: report act ...\n");
       //}
       //dx = 0;
   //}
   //else
   //{
   //    p_mxs_algo_now->y_time_gap = 0;
   //}

    if (p_mxs_algo_now->state != MXS_ALGO_MOTION_ROTATE_CCW) // 释放状态旋转条件
    {
        if (dx > MXS_MOTION_X_DELTA_NOISE)
        {
            time_ccw += MXS_MOTION_SAMPLE_INTERVAL;
            if (time_ccw >= MXS_MOTION_DETECTED_MIN_INTERVAL)
            {
                p_mxs_algo_now->state = MXS_ALGO_MOTION_ROTATE_CCW;
                if(p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE)
                {
                    p_mxs_algo_now->counter = 0;
                    p_mxs_algo_now->time_gap = 0;
                }

                //MT3503_ALGO_LOG_D("mxs_x: report ccw ...\n");
                mxs_algo_report.time_gap = 0;
                mxs_algo_report.times = 0;
                mxs_algo_report.counter = 0;

                time_ccw = 0;
                time_cw = 0;
                time_realse = 0;
            }
        }
        else
        {
            time_ccw = 0;
        }

        if (p_mxs_algo_now->counter > MXS_MOTION_X_START_COUNTER)
        {
            p_mxs_algo_now->state = MXS_ALGO_MOTION_ROTATE_CCW;
            p_mxs_algo_now->counter = 0;
            //MT3503_ALGO_LOG_D("mxs_x: report ccw ...\n");
            time_ccw = 0;
            time_cw = 0;
            time_realse = 0;

            mxs_algo_report.time_gap = 0;
            mxs_algo_report.times = 0;
            mxs_algo_report.counter = 0;
        }
    }

    if (p_mxs_algo_now->state != MXS_ALGO_MOTION_ROTATE_CW) // 释放状态旋转条件
    {
        if (dx < (0 - MXS_MOTION_X_DELTA_NOISE))
        {
            time_cw += MXS_MOTION_SAMPLE_INTERVAL;
            if (time_cw >= MXS_MOTION_DETECTED_MIN_INTERVAL)
            {
                p_mxs_algo_now->state = MXS_ALGO_MOTION_ROTATE_CW;
                if(p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE)
                {
                    p_mxs_algo_now->counter = 0;
                    p_mxs_algo_now->time_gap = 0;
                }
                //MT3503_ALGO_LOG_D("mxs_x: report cw ...\n");
                mxs_algo_report.time_gap = 0;
                mxs_algo_report.times = 0;
                mxs_algo_report.counter = 0;

                time_ccw = 0;
                time_cw = 0;
                time_realse = 0;
            }
        }
        else
        {
            time_cw = 0;
        }
        //
        if (p_mxs_algo_now->counter < (0 - MXS_MOTION_X_START_COUNTER))
        {
            p_mxs_algo_now->state = MXS_ALGO_MOTION_ROTATE_CW;
            p_mxs_algo_now->counter = 0;
            //MT3503_ALGO_LOG_D("mxs_x: report cw ...\n");
            time_ccw = 0;
            time_cw = 0;
            time_realse = 0;

            mxs_algo_report.time_gap = 0;
            mxs_algo_report.times = 0;
            mxs_algo_report.counter = 0;
        }
    }

    if (p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE)  // 旋转状态释放条件
    {
        if (ABS(dx) < 1/*MXS_MOTION_X_DELTA_NOISE*/)       // 限定0才能判定释放
        {
            time_realse += MXS_MOTION_SAMPLE_INTERVAL;
            if (time_realse >= MXS_MOTION_DETECTED_MAX_INTERVAL)
            {
                p_mxs_algo_now->state = MXS_ALGO_MOTION_RELEASE;
                p_mxs_algo_now->counter = 0;
                p_mxs_algo_now->time_gap = 0;

                mxs_algo_report.time_gap = 0;
                mxs_algo_report.times = 0;
                mxs_algo_report.counter = 0;
                motion_x_key_event = p_mxs_algo_now->state;

                if (user_knob_callback != RT_NULL)
                {
                   int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
                   //MT3503_ALGO_LOG_D("mxs_x: report realse ret =%d...\n", ret);
                }

                time_ccw = 0;
                time_cw = 0;
                time_realse = 0;
            }
        }
        else
        {
            time_realse = 0;
        }
    }

    p_mxs_algo_now->counter += dx;
    p_mxs_algo_now->time_gap += MXS_MOTION_SAMPLE_INTERVAL;

    if (p_mxs_algo_now->state != MXS_ALGO_MOTION_RELEASE)
    {
        // 超时未累计到上报值，认为是转动太慢30度0.5S, 发出释放
        if (mxs_algo_report.time_gap >= MXS_MOTION_DETECTED_MAX_GAP)
        {
            p_mxs_algo_now->state = MXS_ALGO_MOTION_RELEASE;
            p_mxs_algo_now->counter = 0;
            p_mxs_algo_now->time_gap = 0;

            mxs_algo_report.time_gap = 0;
            mxs_algo_report.times = 0;
            mxs_algo_report.counter = 0;
            motion_x_key_event = p_mxs_algo_now->state;

            if (user_knob_callback != RT_NULL)
            {
               int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
               //MT3503_ALGO_LOG_D("mxs_x: report realse ret =%d...\n", ret);
            }

            time_ccw = 0;
            time_cw = 0;
            time_realse = 0;
        }

        if(mxs_algo_report.times == 0)
        {
            // 首次上报规则一：旋转时增量累计超过MXS_MOTION_X_START_COUNTER，上报。
            if (ABS(p_mxs_algo_now->counter) > MXS_MOTION_X_START_COUNTER)
            {

                mxs_algo_report.time_gap = 0; // 清空计时器
                mxs_algo_report.times++;      // 置位计数次数
                mxs_algo_report.counter = p_mxs_algo_now->counter;
                motion_x_key_event = p_mxs_algo_now->state;
                if (user_knob_callback != RT_NULL)
                {
                    int32_t ret = user_knob_callback(p_mxs_algo_now->state,p_mxs_algo_now->counter);
                    //MT3503_ALGO_LOG_D("mxs_x: report %d times = %d ...\n",motion_x_key_event, mxs_algo_report.times);
                }
            }
        }
        else
        {
            //// 再次上报规则一：报告间隔超过设定的
            //// MXS_MOTION_DETECTED_MAX_GAP，再次上报。
            // if (mxs_algo_report.time_gap >= MXS_MOTION_DETECTED_MAX_GAP)
            // {

            //     mxs_algo_report.time_gap = 0;
            //     mxs_algo_report.times++;
            //     mxs_algo_report.counter = p_mxs_algo_now->counter;
            //     motion_x_key_event = p_mxs_algo_now->state;
            //     if (user_knob_callback != RT_NULL)
            //     {
            //         int32_t ret = user_knob_callback(p_mxs_algo_now->state, p_mxs_algo_now->counter);
            //         //MT3503_ALGO_LOG_D("mxs_x: report %d times = %d ...\n",motion_x_key_event, mxs_algo_report.times);
            //     }
            // }

            // 再次上报规则二：报告间隔超过设定的 MXS_MOTION_DETECTED_MIN_GAP并且
            // 增量累计超过MXS_MOTION_X_REPORT_COUNTER，再次上报。
            if ((mxs_algo_report.time_gap >= MXS_MOTION_DETECTED_MIN_GAP) &&
                (ABS(p_mxs_algo_now->counter - mxs_algo_report.counter) >= MXS_MOTION_X_REPORT_COUNTER))
            {

                mxs_algo_report.time_gap = 0;
                mxs_algo_report.times++;
                mxs_algo_report.counter = p_mxs_algo_now->counter;
                motion_x_key_event = p_mxs_algo_now->state;

                if (user_knob_callback != RT_NULL)
                {
                    int32_t ret = user_knob_callback(p_mxs_algo_now->state, p_mxs_algo_now->counter);
                    //MT3503_ALGO_LOG_D("mxs_x: report %d times = %d ...\n",motion_x_key_event, mxs_algo_report.times);
                }
            }
        }
        mxs_algo_report.time_gap += MXS_MOTION_SAMPLE_INTERVAL;
    }

    *event = motion_x_key_event;
    *value = p_mxs_algo_now->counter;
    return 0;
}
#endif