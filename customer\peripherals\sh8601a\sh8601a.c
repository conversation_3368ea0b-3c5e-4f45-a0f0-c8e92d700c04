/**
  ******************************************************************************
  * @file   sh8601a.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for SH8601A LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "sh8601a.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup SH8601A
  * @brief This file provides a set of functions needed to drive the
  *        SH8601A LCD.
  * @{
  */

/** @defgroup SH8601A_Private_TypesDefinitions
  * @{
  */

#define ROW_OFFSET  (0x00)
#define COL_OFFSET  (0x00)

typedef struct
{
    rt_uint16_t width;
    rt_uint16_t height;
    rt_uint16_t id;
    rt_uint8_t  dir;            //Horizontal or vertical screen control: 0, vertical; 1, horizontal
    rt_uint16_t wramcmd;
    rt_uint16_t setxcmd;
    rt_uint16_t setycmd;
} lcd_info_t;


/**
  * @}
  */

/** @defgroup SH8601A_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup SH8601A_Private_Macros
  * @{
  */
#define RGB_ARRAY_LEN (320)

//Definition of scan direction
#define L2R_U2D  0
#define L2R_D2U  1
#define R2L_U2D  2
#define R2L_D2U  3
#define U2D_L2R  4
#define U2D_R2L  5
#define D2U_L2R  6
#define D2U_R2L  7
#define DFT_SCAN_DIR  L2R_U2D




#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   HAL_DBG_printf(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

/*sh8601a start colume & row must can be divided by 2, and roi width&height too.*/
#define SH8601A_ALIGN2(x) //((x) = (x) & (~1))
#define SH8601A_ALIGN1(x) //((x) = (0 == ((x) & 1)) ? (x - 1) : x)

static lcd_info_t lcddev;

/**
  * @}
  */

/** @defgroup SH8601A_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef SH8601A_drv =
{
    SH8601A_Init,
    SH8601A_ReadID,
    SH8601A_DisplayOn,
    SH8601A_DisplayOff,

    SH8601A_SetRegion,
    SH8601A_WritePixel,
    SH8601A_WriteMultiplePixels,

    SH8601A_ReadPixel,

    SH8601A_SetColorMode,
    SH8601A_SetBrightness,
    NULL,
    NULL
};


static uint16_t ArrayRGB[RGB_ARRAY_LEN] = {0};


#ifdef BSP_LCDC_USING_DSI

static const LCDC_InitTypeDef lcdc_int_cfg_dsi =
{
    .lcd_itf = LCDC_INTF_DSI,
    .freq = DSI_FREQ_480Mbps, //SH8601A RGB565 only support 320Mbps,  RGB888 support 500Mbps
    .color_mode = LCDC_PIXEL_FORMAT_RGB888,  //DBI output color format,   should match with .cfg.dsi.CmdCfg.ColorCoding

    .cfg = {

        .dsi = {

            .Init = {
                .AutomaticClockLaneControl = DSI_AUTO_CLK_LANE_CTRL_ENABLE,
                .NumberOfLanes = DSI_ONE_DATA_LANE,
                .TXEscapeCkdiv = 0x4,
            },

            .CmdCfg = {
                .VirtualChannelID      = 0,
                .CommandSize           = 0xFFFF,
                .TearingEffectSource   = DSI_TE_DSILINK,
#if 0//def LCD_SH8601A_VSYNC_ENABLE
                .TEAcknowledgeRequest  = DSI_TE_ACKNOWLEDGE_ENABLE,     //Open TE
#else
                .TEAcknowledgeRequest  = DSI_TE_ACKNOWLEDGE_DISABLE,     //Close TE
#endif /* LCD_SH8601A_VSYNC_ENABLE */
                .ColorCoding           = DSI_RGB888,                    //DSI input & output color format
            },

            .PhyTimings = {
                .ClockLaneHS2LPTime = 35,
                .ClockLaneLP2HSTime = 35,
                .DataLaneHS2LPTime = 35,
                .DataLaneLP2HSTime = 35,
                .DataLaneMaxReadTime = 0,
                .StopWaitTime = 0,
            },

            .HostTimeouts = {
                .TimeoutCkdiv = 1,
                .HighSpeedTransmissionTimeout = 0,
                .LowPowerReceptionTimeout = 0,
                .HighSpeedReadTimeout = 0,
                .LowPowerReadTimeout = 0,
                .HighSpeedWriteTimeout = 0,
                //.HighSpeedWritePrespMode = DSI_HS_PM_DISABLE,
                .LowPowerWriteTimeout = 0,
                .BTATimeout = 0,
            },


            .LPCmd = {
                .LPGenShortWriteNoP    = DSI_LP_GSW0P_ENABLE,
                .LPGenShortWriteOneP   = DSI_LP_GSW1P_ENABLE,
                .LPGenShortWriteTwoP   = DSI_LP_GSW2P_ENABLE,
                .LPGenShortReadNoP     = DSI_LP_GSR0P_ENABLE,
                .LPGenShortReadOneP    = DSI_LP_GSR1P_ENABLE,
                .LPGenShortReadTwoP    = DSI_LP_GSR2P_ENABLE,
                .LPGenLongWrite        = DSI_LP_GLW_ENABLE,
                .LPDcsShortWriteNoP    = DSI_LP_DSW0P_ENABLE,
                .LPDcsShortWriteOneP   = DSI_LP_DSW1P_ENABLE,
                .LPDcsShortReadNoP     = DSI_LP_DSR0P_ENABLE,
                .LPDcsLongWrite        = DSI_LP_DLW_DISABLE,
                .LPMaxReadPacket       = DSI_LP_MRDP_ENABLE,
                .AcknowledgeRequest    = DSI_ACKNOWLEDGE_DISABLE, //disable LCD error reports
            },


            .vsyn_delay_us = 1000,
        },
    },
};
#endif /* BSP_LCDC_USING_DSI */



static LCDC_InitTypeDef lcdc_int_cfg;


static void     SH8601A_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
static uint32_t SH8601A_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);
static void SH8601A_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable);



void HAL_DBG_printf(const char *fmt, ...)
{
    va_list args;
    static char rt_log_buf[128];
    extern void rt_kputs(const char *str);

    va_start(args, fmt);
    rt_vsnprintf(rt_log_buf, sizeof(rt_log_buf) - 1, fmt, args);
    rt_kputs(rt_log_buf);
    rt_kputs("\r\n");
    va_end(args);
}

LCD_DRIVER_EXPORT(sh8601a, SH8601A_ID, &lcdc_int_cfg,
                  &SH8601A_drv,
                  SH8601A_LCD_PIXEL_WIDTH,
                  SH8601A_LCD_PIXEL_HEIGHT,
                  1);

/**
  * @}
  */


/** @defgroup SH8601A_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup SH8601A_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void SH8601A_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf) || HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 2000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }
    }
}


/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void SH8601A_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];


    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_dsi, sizeof(lcdc_int_cfg));


    /* Initialize SH8601A low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    LCD_DRIVER_DELAY_MS(10); //delay 10 ms

    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(20); //delay 20 ms
    BSP_LCD_Reset(1);

    LCD_DRIVER_DELAY_MS(10); //LCD must at sleep in mode after power on, 10ms is enough


    SH8601A_WriteReg(hlcdc, 0x11, (uint8_t *)NULL, 0);
    LCD_DRIVER_DELAY_MS(80);


    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x01;
    parameter[3] = 0xc5;
    SH8601A_WriteReg(hlcdc, 0x2a, parameter, 4);
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x01;
    parameter[3] = 0xc5;
    SH8601A_WriteReg(hlcdc, 0x2b, parameter, 4);

    parameter[0] = 0x01;
    parameter[1] = 0xc2;
    SH8601A_WriteReg(hlcdc, 0x44, parameter, 2);

    parameter[0] = 0x00;
    SH8601A_WriteReg(hlcdc, 0x35, parameter, 1); //enable TE




    parameter[0] = 0x28;
    SH8601A_WriteReg(hlcdc, 0x53, parameter, 1);

    parameter[0] = 0xFF;
    SH8601A_WriteReg(hlcdc, 0x51, parameter, 1); //ser brightness

    parameter[0] = 0x06;
    SH8601A_WriteReg(hlcdc, 0xb1, parameter, 1); //set back proch


    LCD_DRIVER_DELAY_MS(50);
    SH8601A_WriteReg(hlcdc, 0x29, (uint8_t *)NULL, 0);
    LCD_DRIVER_DELAY_MS(80);



#if 0//Bist mode
    parameter[0] = 0x5a;
    parameter[1] = 0x5a;
    SH8601A_WriteReg(hlcdc, 0xc0, parameter, 2);

    parameter[0] = 0x81;
    SH8601A_WriteReg(hlcdc, 0xba, parameter, 1);

    while (1);
#endif

    //SH8601A_WriteReg(hlcdc,0x23, (uint8_t *)NULL, 0);

    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    //parameter[0] = 0x02;
    // SH8601A_WriteReg(hlcdc,SH8601A_TEARING_EFFECT, parameter, 1);


}




/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void SH8601A_Init_new(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];




    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_dsi, sizeof(lcdc_int_cfg));





    /* Initialize SH8601A low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    LCD_DRIVER_DELAY_MS(10); //delay 10 ms

    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(20); //delay 20 ms
    BSP_LCD_Reset(1);

    LCD_DRIVER_DELAY_MS(10); //LCD must at sleep in mode after power on, 10ms is enough


//    parameter[0] = 0x01;
//    SH8601A_WriteReg(hlcdc, 0xFE, parameter, 1); //Page 0
//    {
//        parameter[0] = 19; //0,1,19
//        SH8601A_WriteReg(hlcdc, 0x6a, parameter, 1); //Turn on power ic
//
//        if (HAL_LCDC_IS_DSI_IF(lcdc_int_cfg.lcd_itf))
//        {
//            if (DSI_TWO_DATA_LANES == lcdc_int_cfg.cfg.dsi.Init.NumberOfLanes)
//                parameter[0] = 0x01;
//            else
//                parameter[0] = 0x00;
//
//            SH8601A_WriteReg(hlcdc, 0x0d, parameter, 1); //enable 2 lane
//        }
//    }


//    parameter[0] = 0x00;
//    SH8601A_WriteReg(hlcdc, 0xFE, parameter, 1); //User cmd
    {
        parameter[0] = 0x00;
        SH8601A_WriteReg(hlcdc, 0x35, parameter, 1); //enable TE

//        if (LCDC_PIXEL_FORMAT_RGB888 == lcdc_int_cfg.color_mode)
//            parameter[0] = 0x77; //24bit rgb
//        else if (LCDC_PIXEL_FORMAT_RGB565 == lcdc_int_cfg.color_mode)
//            parameter[0] = 0x75; //16bit rgb
//        else
//            RT_ASSERT(0); //fix me
//        SH8601A_WriteReg(hlcdc, SH8601A_COLOR_MODE, parameter, 1);


        parameter[0] = 0xFF;
        SH8601A_WriteReg(hlcdc, SH8601A_WBRIGHT, parameter, 1); //ser brightness


        /* Wait for 110ms */
        LCD_DRIVER_DELAY_MS(110);

        SH8601A_WriteReg(hlcdc, SH8601A_SLEEP_OUT, (uint8_t *)NULL, 0);

        LCD_DRIVER_DELAY_MS(5); //Delay 5 ms after sleep out

        /* Display ON command */
        SH8601A_DisplayOn(hlcdc);

        LCD_DRIVER_DELAY_MS(50); //Wait TE signal ready


    }



#if 0 //Draw an 32*32 rectangle area

    parameter[0] = 0;
    parameter[1] = 100;
    parameter[2] = 0;
    parameter[3] = 131;
    SH8601A_WriteReg(hlcdc, 0x2a, parameter, 4);
    parameter[0] = 0;
    parameter[1] = 100;
    parameter[2] = 0;
    parameter[3] = 131;
    SH8601A_WriteReg(hlcdc, 0x2b, parameter, 4);



    while (1)
    {
        parameter[0] = 0x5a;
        parameter[1] = 0x5a;
        parameter[2] = 0x5a;
        SH8601A_WriteReg(hlcdc, 0x2c, parameter, 3);

        for (uint32_t i = 0; i < ((32 * 32) - 1); i++)
        {
            parameter[0] = 0x5a;
            parameter[1] = 0x5a;
            parameter[2] = 0x5a;
            SH8601A_WriteReg(hlcdc, 0x3c, parameter, 3);
        }
        LCD_DRIVER_DELAY_MS(1000);
    }
#endif





#if 0//Bist mode
    parameter[0] = 0x5a;
    parameter[1] = 0x5a;
    SH8601A_WriteReg(hlcdc, 0xc0, parameter, 2);

    parameter[0] = 0x81;
    SH8601A_WriteReg(hlcdc, 0xba, parameter, 1);

    while (1);
#endif

    //SH8601A_WriteReg(hlcdc,0x23, (uint8_t *)NULL, 0);

    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    //parameter[0] = 0x02;
    // SH8601A_WriteReg(hlcdc,SH8601A_TEARING_EFFECT, parameter, 1);


}


/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t SH8601A_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;
    /*
        data = SH8601A_ReadData(hlcdc,SH8601A_CASET, 4);
        DEBUG_PRINTF("\nSH8601A_CASET 0x%x \n", data);


        data = SH8601A_ReadData(hlcdc,SH8601A_RASET, 4);
        DEBUG_PRINTF("\nSH8601A_RASET 0x%x \n", data);
    */
    data = SH8601A_ReadData(hlcdc, SH8601A_LCD_ID, 3);
    DEBUG_PRINTF("\nSH8601A_ReadID 0x%x \n", data);


    data = SH8601A_ReadData(hlcdc, 0x0a, 4);
    DEBUG_PRINTF("\nSH8601A_Read  0a 0x%x \n", data);

    if (data)
    {
        DEBUG_PRINTF("LCD module 0xa23099 use SH8601A IC \n");
        data = SH8601A_ID;
    }
    data = SH8601A_ID;

    return data;

}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void SH8601A_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    SH8601A_WriteReg(hlcdc, SH8601A_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void SH8601A_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    SH8601A_WriteReg(hlcdc, SH8601A_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void SH8601A_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];
    SH8601A_ALIGN2(Xpos0);
    SH8601A_ALIGN2(Ypos0);
    SH8601A_ALIGN1(Xpos1);
    SH8601A_ALIGN1(Ypos1);

    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Xpos0 += COL_OFFSET;
    Xpos1 += COL_OFFSET;

    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    SH8601A_WriteReg(hlcdc, SH8601A_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    SH8601A_WriteReg(hlcdc, SH8601A_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void SH8601A_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;
    uint8_t   parameter[4];

    SH8601A_ALIGN2(Xpos);
    SH8601A_ALIGN2(Ypos);

    /* Set Cursor */
    SH8601A_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    SH8601A_WriteReg(hlcdc, SH8601A_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void SH8601A_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;
    SH8601A_ALIGN2(Xpos0);
    SH8601A_ALIGN2(Ypos0);
    SH8601A_ALIGN1(Xpos1);
    SH8601A_ALIGN1(Ypos1);


    uint32_t data;
    //data = SH8601A_ReadData(hlcdc, 0x05, 3);
    //DEBUG_PRINTF("DSI ERROR= 0x%x \n", data);

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);

    if (0)
    {
    }
    else
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, SH8601A_WRITE_RAM, 1);
    }
}


/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void SH8601A_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
#if 0
    if (LCD_Reg == SH8601A_CASET)
    {
        DEBUG_PRINTF("SH8601A_SetX[%d,%d]\n", ((Parameters[0] << 8) | Parameters[1]),
                     ((Parameters[2] << 8) | Parameters[3]));
    }
    else if (LCD_Reg == SH8601A_RASET)
    {
        DEBUG_PRINTF("SH8601A_SetY[%d,%d]\n", ((Parameters[0] << 8) | Parameters[1]),
                     ((Parameters[2] << 8) | Parameters[3]));
    }
#endif

    if (0)
    {
    }
    else
    {
        HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
    }
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t SH8601A_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    SH8601A_ReadMode(hlcdc, true);
    if (0)
    {
    }
    else
    {
        HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    }
    SH8601A_ReadMode(hlcdc, false);
    return rd_data;
}



uint32_t SH8601A_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t  r, g, b;
    uint32_t ret_v, read_value;
    DEBUG_PRINTF("SH8601A_ReadPixel[%d,%d]\n", Xpos, Ypos);

    SH8601A_ALIGN2(Xpos);
    SH8601A_ALIGN2(Ypos);

    SH8601A_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    read_value = SH8601A_ReadData(hlcdc, SH8601A_READ_RAM, 4);
    DEBUG_PRINTF("result: [%x]\n", read_value);

    b = (read_value >> 0) & 0xFF;
    g = (read_value >> 8) & 0xFF;
    r = (read_value >> 16) & 0xFF;

    DEBUG_PRINTF("r=%d, g=%d, b=%d \n", r, g, b);

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        ret_v = (uint32_t)(((r << 11) & 0xF800) | ((g << 5) & 0x7E0) | ((b >> 3) & 0X1F));
        break;

    /*
       (8bit R + 3bit dummy + 8bit G + 3bit dummy + 8bit B)

    */
    case LCDC_PIXEL_FORMAT_RGB888:
        ret_v = (uint32_t)(((r << 16) & 0xFF0000) | ((g << 8) & 0xFF00) | ((b) & 0XFF));
        break;

    default:
        RT_ASSERT(0);
        break;
    }


    //SH8601A_WriteReg(hlcdc,SH8601A_COLOR_MODE, parameter, 1);

    return ret_v;
}


void SH8601A_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x75;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;

    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        parameter[0] = 0x77;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
        break;

    default:
        return; //unsupport
        break;
    }

    SH8601A_WriteReg(hlcdc, SH8601A_COLOR_MODE, parameter, 1);


    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

#define SH8601A_BRIGHTNESS_MAX 0xFF

void     SH8601A_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((int)SH8601A_BRIGHTNESS_MAX * br / 100);
    SH8601A_WriteReg(hlcdc, SH8601A_WBRIGHT, &bright, 1);
}



/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
