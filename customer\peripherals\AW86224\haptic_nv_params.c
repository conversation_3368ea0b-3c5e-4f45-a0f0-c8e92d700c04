/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   haptic_nv.h
@Time    :   2025/03/12 09:12:18
@Brief   :
@Details :
*
************************************************************/

#include <stdint.h>
#include "haptic_nv.h"

#define  USE_QW_MOTOR_RAM

#ifndef USE_QW_MOTOR_RAM
#define  MOTOR_RAM_NUM           4    //   3个测试波形 + F0检测波形
#else
#define  MOTOR_RAM_NUM           22   // 7*3个音符波形 + F0检测波形
#endif

/* #define HAPTIC_RAM_12K_0809_170 */
/* #define HAPTIC_RAM_12K_0815_170 */
/* #define HAPTIC_RAM_12K_9595_170 */
/* #define HAPTIC_RAM_12K_0832_205 */
/* #define HAPTIC_RAM_12K_0832_235 */
/* #define HAPTIC_RAM_12K_0832_260 */


/* #define HAPTIC_RAM_24K_0619_170 */
/* #define HAPTIC_RAM_24K_0809_170 */
/* #define HAPTIC_RAM_24K_0815_170 */
/* #define HAPTIC_RAM_24K_1010_170 */
/* #define HAPTIC_RAM_24K_1040_170 */
/* #define HAPTIC_RAM_24K_9595_170 */
/* #define HAPTIC_RAM_24K_0832_205 */
/* #define HAPTIC_RAM_24K_0832_235 */
/* #define HAPTIC_RAM_24K_0832_260 */

#ifdef AW862X_DRIVER
uint8_t aw862x_ram_data[] = {
#if defined HAPTIC_RAM_24K_0619_170
    0x85, 0x08, 0x11, 0x09, 0x8F, 0x09, 0x90, 0x0A,
	0xA0, 0x0A, 0xA1, 0x0B, 0x97, 0x0B, 0x98, 0x0C, 0x24, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x03,
	0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x0A, 0x0B, 0x0D, 0x0F, 0x11,
	0x14, 0x16, 0x1A, 0x1E, 0x22, 0x27, 0x2D, 0x33, 0x3B, 0x43, 0x4C, 0x53,
	0x59, 0x5E, 0x63, 0x67, 0x6B, 0x6E, 0x70, 0x73, 0x75, 0x76, 0x78, 0x79,
	0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F,
	0x7F, 0x7F, 0x7E, 0x7E, 0x7D, 0x7D, 0x7C, 0x7B, 0x7A, 0x79, 0x78, 0x76,
	0x74, 0x72, 0x70, 0x6D, 0x6A, 0x67, 0x62, 0x5E, 0x58, 0x52, 0x4B, 0x43,
	0x39, 0x2F, 0x22, 0x14, 0x04, 0xF4, 0xE5, 0xD7, 0xCC, 0xC2, 0xB9, 0xB1,
	0xAB, 0xA5, 0xA0, 0x9B, 0x98, 0x94, 0x91, 0x8F, 0x8C, 0x8B, 0x89, 0x87,
	0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x86,
	0x87, 0x89, 0x8A, 0x8C, 0x8F, 0x91, 0x94, 0x97, 0x9B, 0xA0, 0xA5, 0xAB,
	0xB1, 0xB9, 0xC2, 0xCC, 0xD7, 0xE4, 0xF3, 0x04, 0x14, 0x22, 0x2E, 0x39,
	0x42, 0x4A, 0x51, 0x57, 0x5D, 0x61, 0x65, 0x68, 0x6B, 0x6E, 0x70, 0x72,
	0x73, 0x74, 0x75, 0x75, 0x76, 0x76, 0x75, 0x75, 0x74, 0x73, 0x72, 0x70,
	0x6E, 0x6B, 0x68, 0x65, 0x61, 0x5C, 0x57, 0x51, 0x4A, 0x42, 0x39, 0x2E,
	0x21, 0x13, 0x03, 0xF5, 0xE8, 0xDD, 0xD3, 0xCA, 0xC3, 0xBC, 0xB7, 0xB2,
	0xAE, 0xAB, 0xA8, 0xA6, 0xA5, 0xA3, 0xA3, 0xA2, 0xA2, 0xA3, 0xA4, 0xA5,
	0xA7, 0xAA, 0xAD, 0xB1, 0xB5, 0xBA, 0xC0, 0xC7, 0xCF, 0xD9, 0xE3, 0xF0,
	0xFE, 0x0D, 0x1A, 0x26, 0x30, 0x39, 0x41, 0x47, 0x4D, 0x52, 0x57, 0x5B,
	0x5E, 0x61, 0x64, 0x66, 0x68, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70,
	0x70, 0x70, 0x71, 0x71, 0x71, 0x71, 0x71, 0x71, 0x70, 0x70, 0x6F, 0x6F,
	0x6E, 0x6D, 0x6C, 0x6A, 0x69, 0x67, 0x65, 0x63, 0x60, 0x5D, 0x59, 0x55,
	0x51, 0x4B, 0x45, 0x3E, 0x36, 0x2C, 0x22, 0x15, 0x07, 0xFB, 0xF0, 0xE6,
	0xDD, 0xD5, 0xCE, 0xC9, 0xC4, 0xBF, 0xBB, 0xB8, 0xB5, 0xB2, 0xB0, 0xAE,
	0xAD, 0xAB, 0xAA, 0xA9, 0xA8, 0xA7, 0xA6, 0xA5, 0xA5, 0xA4, 0xA4, 0xA4,
	0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3,
	0xA3, 0xA4, 0xA4, 0xA4, 0xA5, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB,
	0xAD, 0xAE, 0xB0, 0xB3, 0xB5, 0xB8, 0xBC, 0xBF, 0xC4, 0xC9, 0xCF, 0xD5,
	0xDB, 0xE0, 0xE4, 0xE7, 0xEB, 0xED, 0xF0, 0xF2, 0xF4, 0xF5, 0xF7, 0xF8,
	0xF9, 0xFA, 0xFB, 0xFC, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF,
	0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03, 0x03, 0x04, 0x05, 0x06, 0x08,
	0x09, 0x0B, 0x0E, 0x11, 0x14, 0x18, 0x1D, 0x23, 0x2A, 0x33, 0x3D, 0x48,
	0x51, 0x59, 0x5F, 0x65, 0x69, 0x6D, 0x70, 0x73, 0x75, 0x77, 0x78, 0x79,
	0x7A, 0x7B, 0x7C, 0x7C, 0x7D, 0x7D, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E,
	0x7D, 0x7D, 0x7D, 0x7C, 0x7B, 0x7B, 0x7A, 0x78, 0x77, 0x75, 0x73, 0x71,
	0x6E, 0x6A, 0x66, 0x61, 0x5A, 0x53, 0x4A, 0x3F, 0x32, 0x23, 0x10, 0xFB,
	0xE6, 0xD5, 0xC7, 0xBB, 0xB1, 0xA9, 0xA2, 0x9D, 0x98, 0x94, 0x91, 0x8E,
	0x8C, 0x8A, 0x88, 0x87, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x82, 0x82,
	0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82,
	0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x85, 0x86, 0x87, 0x89, 0x8B, 0x8D,
	0x8F, 0x92, 0x96, 0x9A, 0xA0, 0xA6, 0xAE, 0xB7, 0xC1, 0xCF, 0xDE, 0xF1,
	0x05, 0x15, 0x22, 0x2B, 0x32, 0x36, 0x38, 0x38, 0x35, 0x2F, 0x27, 0x1C,
	0x0D, 0xFC, 0xE7, 0xD6, 0xC8, 0xBC, 0xB2, 0xAA, 0xA3, 0x9D, 0x98, 0x94,
	0x91, 0x8E, 0x8C, 0x8A, 0x88, 0x87, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82,
	0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82,
	0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x85, 0x87, 0x88, 0x89, 0x8B, 0x8D,
	0x90, 0x93, 0x97, 0x9C, 0xA1, 0xA8, 0xB0, 0xBA, 0xC5, 0xD3, 0xE3, 0xF8,
	0x0C, 0x1E, 0x2D, 0x3A, 0x44, 0x4D, 0x54, 0x59, 0x5E, 0x62, 0x65, 0x67,
	0x69, 0x6A, 0x6A, 0x6A, 0x6A, 0x69, 0x67, 0x65, 0x62, 0x5E, 0x59, 0x53,
	0x4C, 0x43, 0x39, 0x2F, 0x27, 0x21, 0x1B, 0x17, 0x13, 0x10, 0x0D, 0x0B,
	0x09, 0x07, 0x06, 0x05, 0x04, 0x03, 0x03, 0x02, 0x02, 0x01, 0x01, 0x01,
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFE, 0xFE, 0xFD, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF7, 0xF5, 0xF4,
	0xF2, 0xF1, 0xF1, 0xF0, 0xF0, 0xEF, 0xEF, 0xEF, 0xF0, 0xF0, 0xF1, 0xF2,
	0xF3, 0xF4, 0xF6, 0xF8, 0xFB, 0xFE, 0x01, 0x06, 0x0B, 0x12, 0x1A, 0x24,
	0x2D, 0x33, 0x38, 0x3B, 0x3D, 0x3D, 0x3B, 0x38, 0x34, 0x2E, 0x26, 0x1B,
	0x0E, 0xFF, 0xF1, 0xE4, 0xDA, 0xD2, 0xCB, 0xC5, 0xC0, 0xBC, 0xB8, 0xB6,
	0xB3, 0xB1, 0xB0, 0xAE, 0xAD, 0xAC, 0xAB, 0xAB, 0xAA, 0xAA, 0xA9, 0xA9,
	0xA9, 0xA9, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8,
	0xA8, 0xA8, 0xA8, 0xA9, 0xA9, 0xA9, 0xA9, 0xAA, 0xAA, 0xAB, 0xAC, 0xAD,
	0xAE, 0xAF, 0xB0, 0xB2, 0xB4, 0xB7, 0xBA, 0xBD, 0xC2, 0xC7, 0xCE, 0xD5,
	0xDE, 0xE9, 0xF5, 0x00, 0x0C, 0x1A, 0x2A, 0x38, 0x43, 0x4C, 0x52, 0x57,
	0x5B, 0x5E, 0x60, 0x62, 0x63, 0x64, 0x65, 0x65, 0x66, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x5F, 0x5C, 0x59, 0x55, 0x51, 0x4B, 0x44, 0x3C, 0x33,
	0x27, 0x19, 0x08, 0xF4, 0xDE, 0xCC, 0xBC, 0xB0, 0xA5, 0x9D, 0x95, 0x90,
	0x8B, 0x87, 0x85, 0x83, 0x81, 0x81, 0x81, 0x81, 0x82, 0x84, 0x87, 0x8B,
	0x8F, 0x95, 0x9C, 0xA4, 0xAF, 0xBB, 0xCA, 0xDC, 0xF3, 0x09, 0x1D, 0x2E,
	0x3C, 0x47, 0x51, 0x59, 0x5F, 0x64, 0x69, 0x6C, 0x6F, 0x71, 0x72, 0x73,
	0x73, 0x73, 0x73, 0x71, 0x70, 0x6E, 0x6C, 0x6A, 0x68, 0x67, 0x65, 0x63,
	0x62, 0x60, 0x5D, 0x5B, 0x58, 0x54, 0x50, 0x4C, 0x46, 0x3F, 0x37, 0x2E,
	0x26, 0x20, 0x1A, 0x16, 0x12, 0x0F, 0x0C, 0x0A, 0x08, 0x07, 0x06, 0x05,
	0x04, 0x03, 0x03, 0x02, 0x02, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00,
	0x00, 0x02, 0x04, 0x06, 0x08, 0x0B, 0x0D, 0x0F, 0x11, 0x13, 0x15, 0x17,
	0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23, 0x25, 0x26, 0x28, 0x29, 0x2A,
	0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31, 0x31,
	0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x2F, 0x2F, 0x2E, 0x2D, 0x2C, 0x2B,
	0x2A, 0x28, 0x27, 0x26, 0x24, 0x23, 0x21, 0x1F, 0x1E, 0x1C, 0x1A, 0x18,
	0x16, 0x14, 0x12, 0x10, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x03, 0x01, 0x00,
	0xFD, 0xFB, 0xF9, 0xF7, 0xF5, 0xF2, 0xF0, 0xEE, 0xEC, 0xEA, 0xE8, 0xE6,
	0xE4, 0xE3, 0xE1, 0xDF, 0xDE, 0xDC, 0xDA, 0xD9, 0xD8, 0xD6, 0xD5, 0xD4,
	0xD3, 0xD2, 0xD2, 0xD1, 0xD0, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2, 0xD3, 0xD3, 0xD4, 0xD6, 0xD7,
	0xD8, 0xD9, 0xDB, 0xDC, 0xDE, 0xE0, 0xE1, 0xE3, 0xE5, 0xE7, 0xE9, 0xEB,
	0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF9, 0xFC, 0xFE,
#elif defined HAPTIC_RAM_24K_0809_170
    0x55, 0x08, 0x11, 0x09, 0x09, 0x09, 0x0A, 0x0A,
	0x68, 0x0A, 0x69, 0x0B, 0x08, 0x0B, 0x09, 0x0B, 0x95, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x02, 0x02, 0x02, 0x02, 0x03, 0x03, 0x04, 0x04, 0x05, 0x06,
	0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0D, 0x0F, 0x11, 0x14, 0x16, 0x1A, 0x1D,
	0x21, 0x26, 0x2B, 0x31, 0x38, 0x40, 0x47, 0x4C, 0x51, 0x55, 0x58, 0x5A,
	0x5C, 0x5D, 0x5D, 0x5C, 0x5C, 0x5B, 0x5A, 0x58, 0x56, 0x54, 0x51, 0x4E,
	0x4B, 0x48, 0x44, 0x41, 0x3D, 0x39, 0x35, 0x31, 0x2C, 0x28, 0x24, 0x1F,
	0x1B, 0x16, 0x11, 0x0C, 0x07, 0x02, 0xFD, 0xF7, 0xF1, 0xEB, 0xE4, 0xDD,
	0xD5, 0xCD, 0xC4, 0xB9, 0xAE, 0xA2, 0x99, 0x90, 0x8A, 0x85, 0x82, 0x81,
	0x81, 0x81, 0x81, 0x84, 0x87, 0x8B, 0x90, 0x95, 0x9B, 0xA2, 0xA9, 0xB0,
	0xB8, 0xC0, 0xC9, 0xD1, 0xDA, 0xE2, 0xEB, 0xF4, 0xFC, 0x05, 0x0D, 0x15,
	0x1D, 0x25, 0x2C, 0x33, 0x39, 0x3F, 0x44, 0x49, 0x4D, 0x51, 0x54, 0x56,
	0x58, 0x59, 0x59, 0x58, 0x56, 0x54, 0x51, 0x4E, 0x4B, 0x48, 0x44, 0x40,
	0x3B, 0x36, 0x31, 0x2C, 0x26, 0x20, 0x19, 0x13, 0x0C, 0x05, 0xFE, 0xF7,
	0xEF, 0xE8, 0xE0, 0xD8, 0xD1, 0xC9, 0xC2, 0xBB, 0xB4, 0xAD, 0xA7, 0xA1,
	0x9B, 0x96, 0x91, 0x8D, 0x89, 0x86, 0x84, 0x83, 0x83, 0x83, 0x85, 0x88,
	0x8C, 0x91, 0x98, 0xA1, 0xAB, 0xB6, 0xBF, 0xC8, 0xCF, 0xD6, 0xDD, 0xE2,
	0xE8, 0xED, 0xF1, 0xF6, 0xFA, 0xFD, 0x01, 0x05, 0x08, 0x0B, 0x0F, 0x12,
	0x15, 0x18, 0x1A, 0x1D, 0x20, 0x22, 0x25, 0x27, 0x29, 0x2B, 0x2D, 0x2F,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x36, 0x36, 0x36, 0x35, 0x34, 0x32,
	0x30, 0x2D, 0x2A, 0x26, 0x21, 0x1D, 0x19, 0x16, 0x13, 0x11, 0x0E, 0x0C,
	0x0B, 0x09, 0x08, 0x07, 0x06, 0x05, 0x04, 0x04, 0x03, 0x03, 0x02, 0x02,
	0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x03, 0x03, 0x03,
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0B, 0x0D, 0x0E, 0x11, 0x13, 0x16,
	0x1A, 0x1D, 0x22, 0x27, 0x2D, 0x32, 0x37, 0x3A, 0x3D, 0x3F, 0x40, 0x41,
	0x41, 0x41, 0x3F, 0x3D, 0x3A, 0x37, 0x32, 0x2D, 0x27, 0x1F, 0x16, 0x0C,
	0x00, 0xF4, 0xE9, 0xDF, 0xD6, 0xCF, 0xC9, 0xC3, 0xBE, 0xBA, 0xB6, 0xB3,
	0xB0, 0xAE, 0xAB, 0xAA, 0xA8, 0xA7, 0xA5, 0xA4, 0xA3, 0xA3, 0xA2, 0xA1,
	0xA1, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E, 0x9E,
	0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F,
	0xA0, 0xA0, 0xA0, 0xA1, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8,
	0xAA, 0xAC, 0xAE, 0xB1, 0xB3, 0xB7, 0xBB, 0xBF, 0xC4, 0xCA, 0xD0, 0xD8,
	0xE1, 0xEB, 0xF6, 0x03, 0x0F, 0x1A, 0x23, 0x2C, 0x33, 0x39, 0x3E, 0x43,
	0x47, 0x4A, 0x4D, 0x50, 0x52, 0x54, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B,
	0x5B, 0x5C, 0x5C, 0x5C, 0x5C, 0x5B, 0x5B, 0x5A, 0x5A, 0x59, 0x58, 0x56,
	0x55, 0x53, 0x51, 0x4F, 0x4C, 0x48, 0x45, 0x40, 0x3B, 0x35, 0x2F, 0x27,
	0x1E, 0x14, 0x08, 0xFD, 0xF2, 0xE9, 0xE2, 0xDC, 0xD7, 0xD3, 0xD0, 0xCE,
	0xCD, 0xCD, 0xCE, 0xD0, 0xD3, 0xD7, 0xDC, 0xE2, 0xE9, 0xF2, 0xFD, 0x08,
	0x14, 0x1E, 0x27, 0x2F, 0x36, 0x3B, 0x41, 0x45, 0x49, 0x4C, 0x4F, 0x52,
	0x54, 0x56, 0x57, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5D, 0x5E, 0x5E, 0x5E,
	0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5E, 0x5E, 0x5E, 0x5D, 0x5C, 0x5C,
	0x5B, 0x5A, 0x59, 0x57, 0x55, 0x54, 0x51, 0x4F, 0x4C, 0x48, 0x45, 0x40,
	0x3B, 0x35, 0x2E, 0x26, 0x1D, 0x13, 0x09, 0x01, 0xFB, 0xF4, 0xEF, 0xEA,
	0xE6, 0xE2, 0xDF, 0xDC, 0xDA, 0xD8, 0xD6, 0xD4, 0xD3, 0xD2, 0xD1, 0xD0,
	0xCF, 0xCE, 0xCE, 0xCD, 0xCD, 0xCD, 0xCC, 0xCC, 0xCC, 0xCB, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCC, 0xCC, 0xCC, 0xCD, 0xCD, 0xCD,
	0xCE, 0xCF, 0xCF, 0xD0, 0xD1, 0xD2, 0xD3, 0xD5, 0xD6, 0xD8, 0xDA, 0xDD,
	0xDF, 0xE2, 0xE6, 0xEA, 0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF8, 0xF9,
	0xFA, 0xFB, 0xFC, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03,
	0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0B, 0x0C, 0x0E, 0x10,
	0x13, 0x16, 0x19, 0x1C, 0x21, 0x25, 0x2B, 0x31, 0x38, 0x41, 0x48, 0x4D,
	0x50, 0x51, 0x51, 0x4F, 0x4B, 0x45, 0x3D, 0x33, 0x26, 0x17, 0x05, 0xF1,
	0xDE, 0xCD, 0xBE, 0xB2, 0xA7, 0x9E, 0x96, 0x90, 0x8B, 0x87, 0x84, 0x82,
	0x81, 0x81, 0x81, 0x83, 0x86, 0x8A, 0x8F, 0x95, 0x9C, 0xA5, 0xB0, 0xBC,
	0xCA, 0xDB, 0xEE, 0x03, 0x17, 0x29, 0x38, 0x45, 0x50, 0x59, 0x60, 0x66,
	0x6B, 0x6E, 0x70, 0x71, 0x71, 0x6F, 0x6C, 0x68, 0x62, 0x5C, 0x53, 0x49,
	0x3C, 0x2E, 0x1E, 0x11, 0x05, 0xFC, 0xF3, 0xEB, 0xE4, 0xDE, 0xD9, 0xD5,
	0xD1, 0xCE, 0xCB, 0xC9, 0xC8, 0xC6, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC6,
	0xC7, 0xC9, 0xCB, 0xCE, 0xD1, 0xD4, 0xD9, 0xDD, 0xE2, 0xE6, 0xEA, 0xED,
	0xEF, 0xF2, 0xF4, 0xF6, 0xF7, 0xF8, 0xFA, 0xFB, 0xFB, 0xFC, 0xFD, 0xFD,
	0xFE, 0xFE, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x02, 0x04, 0x06, 0x08, 0x0B, 0x0D, 0x0F, 0x11, 0x13, 0x15,
	0x17, 0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23, 0x25, 0x26, 0x28, 0x29,
	0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31,
	0x31, 0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x2F, 0x2F, 0x2E, 0x2D, 0x2C,
	0x2B, 0x2A, 0x28, 0x27, 0x26, 0x24, 0x23, 0x21, 0x1F, 0x1E, 0x1C, 0x1A,
	0x18, 0x16, 0x14, 0x12, 0x10, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x03, 0x01,
	0x00, 0xFD, 0xFB, 0xF9, 0xF7, 0xF5, 0xF2, 0xF0, 0xEE, 0xEC, 0xEA, 0xE8,
	0xE6, 0xE4, 0xE3, 0xE1, 0xDF, 0xDE, 0xDC, 0xDA, 0xD9, 0xD8, 0xD6, 0xD5,
	0xD4, 0xD3, 0xD2, 0xD2, 0xD1, 0xD0, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2, 0xD3, 0xD3, 0xD4, 0xD6,
	0xD7, 0xD8, 0xD9, 0xDB, 0xDC, 0xDE, 0xE0, 0xE1, 0xE3, 0xE5, 0xE7, 0xE9,
	0xEB, 0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF9, 0xFC, 0xFE,
#elif defined HAPTIC_RAM_24K_0815_170
    0x85, 0x08, 0x11, 0x09, 0x8F, 0x09, 0x90, 0x0A,
	0xA0, 0x0A, 0xA1, 0x0B, 0x97, 0x0B, 0x98, 0x0C, 0x24, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x03,
	0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x0A, 0x0B, 0x0D, 0x0F, 0x11,
	0x14, 0x16, 0x1A, 0x1E, 0x22, 0x27, 0x2D, 0x33, 0x3B, 0x43, 0x4C, 0x53,
	0x59, 0x5E, 0x63, 0x67, 0x6B, 0x6E, 0x70, 0x73, 0x75, 0x76, 0x78, 0x79,
	0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7F, 0x7F, 0x7F, 0x7F, 0x7F,
	0x7F, 0x7F, 0x7E, 0x7E, 0x7D, 0x7D, 0x7C, 0x7B, 0x7A, 0x79, 0x78, 0x76,
	0x74, 0x72, 0x70, 0x6D, 0x6A, 0x67, 0x62, 0x5E, 0x58, 0x52, 0x4B, 0x43,
	0x39, 0x2F, 0x22, 0x14, 0x04, 0xF4, 0xE5, 0xD7, 0xCC, 0xC2, 0xB9, 0xB1,
	0xAB, 0xA5, 0xA0, 0x9B, 0x98, 0x94, 0x91, 0x8F, 0x8C, 0x8B, 0x89, 0x87,
	0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x86,
	0x87, 0x89, 0x8A, 0x8C, 0x8F, 0x91, 0x94, 0x97, 0x9B, 0xA0, 0xA5, 0xAB,
	0xB1, 0xB9, 0xC2, 0xCC, 0xD7, 0xE4, 0xF3, 0x04, 0x14, 0x22, 0x2E, 0x39,
	0x42, 0x4A, 0x51, 0x57, 0x5D, 0x61, 0x65, 0x68, 0x6B, 0x6E, 0x70, 0x72,
	0x73, 0x74, 0x75, 0x75, 0x76, 0x76, 0x75, 0x75, 0x74, 0x73, 0x72, 0x70,
	0x6E, 0x6B, 0x68, 0x65, 0x61, 0x5C, 0x57, 0x51, 0x4A, 0x42, 0x39, 0x2E,
	0x21, 0x13, 0x03, 0xF5, 0xE8, 0xDD, 0xD3, 0xCA, 0xC3, 0xBC, 0xB7, 0xB2,
	0xAE, 0xAB, 0xA8, 0xA6, 0xA5, 0xA3, 0xA3, 0xA2, 0xA2, 0xA3, 0xA4, 0xA5,
	0xA7, 0xAA, 0xAD, 0xB1, 0xB5, 0xBA, 0xC0, 0xC7, 0xCF, 0xD9, 0xE3, 0xF0,
	0xFE, 0x0D, 0x1A, 0x26, 0x30, 0x39, 0x41, 0x47, 0x4D, 0x52, 0x57, 0x5B,
	0x5E, 0x61, 0x64, 0x66, 0x68, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70,
	0x70, 0x70, 0x71, 0x71, 0x71, 0x71, 0x71, 0x71, 0x70, 0x70, 0x6F, 0x6F,
	0x6E, 0x6D, 0x6C, 0x6A, 0x69, 0x67, 0x65, 0x63, 0x60, 0x5D, 0x59, 0x55,
	0x51, 0x4B, 0x45, 0x3E, 0x36, 0x2C, 0x22, 0x15, 0x07, 0xFB, 0xF0, 0xE6,
	0xDD, 0xD5, 0xCE, 0xC9, 0xC4, 0xBF, 0xBB, 0xB8, 0xB5, 0xB2, 0xB0, 0xAE,
	0xAD, 0xAB, 0xAA, 0xA9, 0xA8, 0xA7, 0xA6, 0xA5, 0xA5, 0xA4, 0xA4, 0xA4,
	0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA3,
	0xA3, 0xA4, 0xA4, 0xA4, 0xA5, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9, 0xAA, 0xAB,
	0xAD, 0xAE, 0xB0, 0xB3, 0xB5, 0xB8, 0xBC, 0xBF, 0xC4, 0xC9, 0xCF, 0xD5,
	0xDB, 0xE0, 0xE4, 0xE7, 0xEB, 0xED, 0xF0, 0xF2, 0xF4, 0xF5, 0xF7, 0xF8,
	0xF9, 0xFA, 0xFB, 0xFC, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF,
	0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03, 0x03, 0x04, 0x05, 0x06, 0x08,
	0x09, 0x0B, 0x0E, 0x11, 0x14, 0x18, 0x1D, 0x23, 0x2A, 0x33, 0x3D, 0x48,
	0x51, 0x59, 0x5F, 0x65, 0x69, 0x6D, 0x70, 0x73, 0x75, 0x77, 0x78, 0x79,
	0x7A, 0x7B, 0x7C, 0x7C, 0x7D, 0x7D, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E,
	0x7D, 0x7D, 0x7D, 0x7C, 0x7B, 0x7B, 0x7A, 0x78, 0x77, 0x75, 0x73, 0x71,
	0x6E, 0x6A, 0x66, 0x61, 0x5A, 0x53, 0x4A, 0x3F, 0x32, 0x23, 0x10, 0xFB,
	0xE6, 0xD5, 0xC7, 0xBB, 0xB1, 0xA9, 0xA2, 0x9D, 0x98, 0x94, 0x91, 0x8E,
	0x8C, 0x8A, 0x88, 0x87, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82, 0x82, 0x82,
	0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82,
	0x82, 0x82, 0x83, 0x83, 0x84, 0x84, 0x85, 0x86, 0x87, 0x89, 0x8B, 0x8D,
	0x8F, 0x92, 0x96, 0x9A, 0xA0, 0xA6, 0xAE, 0xB7, 0xC1, 0xCF, 0xDE, 0xF1,
	0x05, 0x15, 0x22, 0x2B, 0x32, 0x36, 0x38, 0x38, 0x35, 0x2F, 0x27, 0x1C,
	0x0D, 0xFC, 0xE7, 0xD6, 0xC8, 0xBC, 0xB2, 0xAA, 0xA3, 0x9D, 0x98, 0x94,
	0x91, 0x8E, 0x8C, 0x8A, 0x88, 0x87, 0x86, 0x85, 0x84, 0x83, 0x83, 0x82,
	0x82, 0x82, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82,
	0x82, 0x82, 0x83, 0x83, 0x84, 0x85, 0x85, 0x87, 0x88, 0x89, 0x8B, 0x8D,
	0x90, 0x93, 0x97, 0x9C, 0xA1, 0xA8, 0xB0, 0xBA, 0xC5, 0xD3, 0xE3, 0xF8,
	0x0C, 0x1E, 0x2D, 0x3A, 0x44, 0x4D, 0x54, 0x59, 0x5E, 0x62, 0x65, 0x67,
	0x69, 0x6A, 0x6A, 0x6A, 0x6A, 0x69, 0x67, 0x65, 0x62, 0x5E, 0x59, 0x53,
	0x4C, 0x43, 0x39, 0x2F, 0x27, 0x21, 0x1B, 0x17, 0x13, 0x10, 0x0D, 0x0B,
	0x09, 0x07, 0x06, 0x05, 0x04, 0x03, 0x03, 0x02, 0x02, 0x01, 0x01, 0x01,
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFE, 0xFE, 0xFD, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF7, 0xF5, 0xF4,
	0xF2, 0xF1, 0xF1, 0xF0, 0xF0, 0xEF, 0xEF, 0xEF, 0xF0, 0xF0, 0xF1, 0xF2,
	0xF3, 0xF4, 0xF6, 0xF8, 0xFB, 0xFE, 0x01, 0x06, 0x0B, 0x12, 0x1A, 0x24,
	0x2D, 0x33, 0x38, 0x3B, 0x3D, 0x3D, 0x3B, 0x38, 0x34, 0x2E, 0x26, 0x1B,
	0x0E, 0xFF, 0xF1, 0xE4, 0xDA, 0xD2, 0xCB, 0xC5, 0xC0, 0xBC, 0xB8, 0xB6,
	0xB3, 0xB1, 0xB0, 0xAE, 0xAD, 0xAC, 0xAB, 0xAB, 0xAA, 0xAA, 0xA9, 0xA9,
	0xA9, 0xA9, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8,
	0xA8, 0xA8, 0xA8, 0xA9, 0xA9, 0xA9, 0xA9, 0xAA, 0xAA, 0xAB, 0xAC, 0xAD,
	0xAE, 0xAF, 0xB0, 0xB2, 0xB4, 0xB7, 0xBA, 0xBD, 0xC2, 0xC7, 0xCE, 0xD5,
	0xDE, 0xE9, 0xF5, 0x00, 0x0C, 0x1A, 0x2A, 0x38, 0x43, 0x4C, 0x52, 0x57,
	0x5B, 0x5E, 0x60, 0x62, 0x63, 0x64, 0x65, 0x65, 0x66, 0x66, 0x65, 0x65,
	0x64, 0x62, 0x61, 0x5F, 0x5C, 0x59, 0x55, 0x51, 0x4B, 0x44, 0x3C, 0x33,
	0x27, 0x19, 0x08, 0xF4, 0xDE, 0xCC, 0xBC, 0xB0, 0xA5, 0x9D, 0x95, 0x90,
	0x8B, 0x87, 0x85, 0x83, 0x81, 0x81, 0x81, 0x81, 0x82, 0x84, 0x87, 0x8B,
	0x8F, 0x95, 0x9C, 0xA4, 0xAF, 0xBB, 0xCA, 0xDC, 0xF3, 0x09, 0x1D, 0x2E,
	0x3C, 0x47, 0x51, 0x59, 0x5F, 0x64, 0x69, 0x6C, 0x6F, 0x71, 0x72, 0x73,
	0x73, 0x73, 0x73, 0x71, 0x70, 0x6E, 0x6C, 0x6A, 0x68, 0x67, 0x65, 0x63,
	0x62, 0x60, 0x5D, 0x5B, 0x58, 0x54, 0x50, 0x4C, 0x46, 0x3F, 0x37, 0x2E,
	0x26, 0x20, 0x1A, 0x16, 0x12, 0x0F, 0x0C, 0x0A, 0x08, 0x07, 0x06, 0x05,
	0x04, 0x03, 0x03, 0x02, 0x02, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00,
	0x00, 0x02, 0x04, 0x06, 0x08, 0x0B, 0x0D, 0x0F, 0x11, 0x13, 0x15, 0x17,
	0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23, 0x25, 0x26, 0x28, 0x29, 0x2A,
	0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31, 0x31,
	0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x2F, 0x2F, 0x2E, 0x2D, 0x2C, 0x2B,
	0x2A, 0x28, 0x27, 0x26, 0x24, 0x23, 0x21, 0x1F, 0x1E, 0x1C, 0x1A, 0x18,
	0x16, 0x14, 0x12, 0x10, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x03, 0x01, 0x00,
	0xFD, 0xFB, 0xF9, 0xF7, 0xF5, 0xF2, 0xF0, 0xEE, 0xEC, 0xEA, 0xE8, 0xE6,
	0xE4, 0xE3, 0xE1, 0xDF, 0xDE, 0xDC, 0xDA, 0xD9, 0xD8, 0xD6, 0xD5, 0xD4,
	0xD3, 0xD2, 0xD2, 0xD1, 0xD0, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2, 0xD3, 0xD3, 0xD4, 0xD6, 0xD7,
	0xD8, 0xD9, 0xDB, 0xDC, 0xDE, 0xE0, 0xE1, 0xE3, 0xE5, 0xE7, 0xE9, 0xEB,
	0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF9, 0xFC, 0xFE,
#elif defined HAPTIC_RAM_24K_1010_170
    0x85, 0x08, 0x11, 0x09, 0x3D, 0x09, 0x3E, 0x0A,
	0x9C, 0x0A, 0x9D, 0x0B, 0x3C, 0x0B, 0x3D, 0x0B, 0xC9, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x03, 0x03,
	0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x0A, 0x0B, 0x0D, 0x0F, 0x11,
	0x14, 0x16, 0x1A, 0x1E, 0x22, 0x27, 0x2D, 0x33, 0x3A, 0x43, 0x4B, 0x52,
	0x58, 0x5D, 0x62, 0x66, 0x69, 0x6C, 0x6E, 0x70, 0x72, 0x73, 0x75, 0x75,
	0x76, 0x76, 0x76, 0x76, 0x76, 0x75, 0x74, 0x73, 0x72, 0x70, 0x6E, 0x6B,
	0x69, 0x65, 0x61, 0x5D, 0x57, 0x51, 0x4A, 0x42, 0x39, 0x2E, 0x22, 0x14,
	0x04, 0xF3, 0xE4, 0xD7, 0xCC, 0xC2, 0xB9, 0xB2, 0xAB, 0xA5, 0xA0, 0x9C,
	0x98, 0x94, 0x91, 0x8F, 0x8D, 0x8B, 0x89, 0x88, 0x87, 0x85, 0x85, 0x84,
	0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82, 0x83, 0x83, 0x84,
	0x84, 0x85, 0x86, 0x87, 0x89, 0x8A, 0x8C, 0x8E, 0x91, 0x94, 0x97, 0x9B,
	0x9F, 0xA4, 0xAA, 0xB0, 0xB8, 0xC0, 0xCA, 0xD5, 0xE2, 0xF0, 0xFE, 0x07,
	0x10, 0x16, 0x1A, 0x1C, 0x1D, 0x1C, 0x18, 0x13, 0x0C, 0x02, 0xF7, 0xE9,
	0xDB, 0xCF, 0xC5, 0xBC, 0xB4, 0xAD, 0xA7, 0xA1, 0x9D, 0x99, 0x95, 0x92,
	0x90, 0x8D, 0x8B, 0x89, 0x88, 0x87, 0x85, 0x84, 0x84, 0x83, 0x82, 0x82,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82,
	0x82, 0x83, 0x84, 0x85, 0x86, 0x87, 0x89, 0x8A, 0x8C, 0x8E, 0x91, 0x94,
	0x97, 0x9B, 0x9F, 0xA4, 0xA9, 0xB0, 0xB7, 0xC0, 0xC9, 0xD4, 0xDF, 0xE8,
	0xF0, 0xF7, 0xFD, 0x01, 0x06, 0x0A, 0x0D, 0x10, 0x13, 0x15, 0x17, 0x19,
	0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x21, 0x22, 0x22, 0x23, 0x23,
	0x23, 0x23, 0x24, 0x24, 0x24, 0x24, 0x24, 0x24, 0x25, 0x25, 0x25, 0x25,
	0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x25, 0x24, 0x24, 0x24, 0x24, 0x24,
	0x24, 0x23, 0x23, 0x23, 0x23, 0x22, 0x22, 0x21, 0x21, 0x20, 0x1F, 0x1E,
	0x1D, 0x1C, 0x1B, 0x19, 0x17, 0x15, 0x13, 0x11, 0x0E, 0x0D, 0x0B, 0x09,
	0x08, 0x07, 0x06, 0x05, 0x05, 0x04, 0x03, 0x03, 0x02, 0x02, 0x02, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02,
	0x02, 0x03, 0x03, 0x04, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0B, 0x0D,
	0x0E, 0x11, 0x13, 0x16, 0x1A, 0x1D, 0x22, 0x27, 0x2D, 0x32, 0x37, 0x3A,
	0x3D, 0x3F, 0x40, 0x41, 0x41, 0x40, 0x3F, 0x3D, 0x3A, 0x37, 0x32, 0x2D,
	0x27, 0x1F, 0x16, 0x0C, 0x00, 0xF4, 0xE9, 0xDF, 0xD6, 0xCF, 0xC9, 0xC3,
	0xBE, 0xBA, 0xB6, 0xB3, 0xB0, 0xAE, 0xAB, 0xAA, 0xA8, 0xA7, 0xA5, 0xA4,
	0xA3, 0xA3, 0xA2, 0xA1, 0xA1, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F, 0x9F,
	0x9F, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9F, 0x9F,
	0x9F, 0x9F, 0x9F, 0x9F, 0xA0, 0xA0, 0xA0, 0xA1, 0xA1, 0xA2, 0xA3, 0xA4,
	0xA5, 0xA6, 0xA7, 0xA8, 0xAA, 0xAC, 0xAE, 0xB1, 0xB3, 0xB7, 0xBB, 0xBF,
	0xC4, 0xCA, 0xD0, 0xD8, 0xE1, 0xEB, 0xF6, 0x03, 0x0F, 0x1A, 0x23, 0x2C,
	0x33, 0x39, 0x3E, 0x43, 0x47, 0x4A, 0x4D, 0x50, 0x52, 0x54, 0x56, 0x57,
	0x58, 0x59, 0x5A, 0x5B, 0x5B, 0x5C, 0x5C, 0x5C, 0x5C, 0x5B, 0x5B, 0x5A,
	0x5A, 0x59, 0x58, 0x56, 0x55, 0x53, 0x51, 0x4F, 0x4C, 0x48, 0x45, 0x40,
	0x3B, 0x35, 0x2F, 0x27, 0x1E, 0x14, 0x08, 0xFD, 0xF2, 0xE9, 0xE2, 0xDC,
	0xD7, 0xD3, 0xD0, 0xCE, 0xCD, 0xCD, 0xCE, 0xD0, 0xD3, 0xD7, 0xDC, 0xE2,
	0xE9, 0xF2, 0xFD, 0x08, 0x14, 0x1E, 0x27, 0x2F, 0x36, 0x3B, 0x41, 0x45,
	0x49, 0x4C, 0x4F, 0x52, 0x54, 0x56, 0x57, 0x59, 0x5A, 0x5B, 0x5C, 0x5D,
	0x5D, 0x5E, 0x5E, 0x5E, 0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5E, 0x5E,
	0x5E, 0x5D, 0x5C, 0x5C, 0x5B, 0x5A, 0x59, 0x57, 0x55, 0x54, 0x51, 0x4F,
	0x4C, 0x48, 0x45, 0x40, 0x3B, 0x35, 0x2E, 0x26, 0x1D, 0x13, 0x09, 0x01,
	0xFB, 0xF4, 0xEF, 0xEA, 0xE6, 0xE2, 0xDF, 0xDC, 0xDA, 0xD8, 0xD6, 0xD4,
	0xD3, 0xD2, 0xD1, 0xD0, 0xCF, 0xCE, 0xCE, 0xCD, 0xCD, 0xCD, 0xCC, 0xCC,
	0xCC, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCC, 0xCC,
	0xCC, 0xCD, 0xCD, 0xCD, 0xCE, 0xCF, 0xCF, 0xD0, 0xD1, 0xD2, 0xD3, 0xD5,
	0xD6, 0xD8, 0xDA, 0xDD, 0xDF, 0xE2, 0xE6, 0xEA, 0xED, 0xEF, 0xF1, 0xF3,
	0xF5, 0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE,
	0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x02, 0x02, 0x02, 0x03, 0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x09,
	0x0B, 0x0C, 0x0E, 0x10, 0x13, 0x16, 0x19, 0x1C, 0x21, 0x25, 0x2B, 0x31,
	0x38, 0x41, 0x48, 0x4D, 0x50, 0x51, 0x51, 0x4F, 0x4B, 0x45, 0x3D, 0x33,
	0x26, 0x17, 0x05, 0xF1, 0xDE, 0xCD, 0xBE, 0xB2, 0xA7, 0x9E, 0x96, 0x90,
	0x8B, 0x87, 0x84, 0x82, 0x81, 0x81, 0x81, 0x83, 0x86, 0x8A, 0x8F, 0x95,
	0x9C, 0xA5, 0xB0, 0xBC, 0xCA, 0xDB, 0xEE, 0x03, 0x17, 0x29, 0x38, 0x45,
	0x50, 0x59, 0x60, 0x66, 0x6B, 0x6E, 0x70, 0x71, 0x71, 0x6F, 0x6C, 0x68,
	0x62, 0x5C, 0x53, 0x49, 0x3C, 0x2E, 0x1E, 0x11, 0x05, 0xFC, 0xF3, 0xEB,
	0xE4, 0xDE, 0xD9, 0xD5, 0xD1, 0xCE, 0xCB, 0xC9, 0xC8, 0xC6, 0xC5, 0xC5,
	0xC5, 0xC5, 0xC5, 0xC6, 0xC7, 0xC9, 0xCB, 0xCE, 0xD1, 0xD4, 0xD9, 0xDD,
	0xE2, 0xE6, 0xEA, 0xED, 0xEF, 0xF2, 0xF4, 0xF6, 0xF7, 0xF8, 0xFA, 0xFB,
	0xFB, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x04, 0x06, 0x08, 0x0B, 0x0D,
	0x0F, 0x11, 0x13, 0x15, 0x17, 0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23,
	0x25, 0x26, 0x28, 0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x30,
	0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x2F,
	0x2F, 0x2E, 0x2D, 0x2C, 0x2B, 0x2A, 0x28, 0x27, 0x26, 0x24, 0x23, 0x21,
	0x1F, 0x1E, 0x1C, 0x1A, 0x18, 0x16, 0x14, 0x12, 0x10, 0x0E, 0x0C, 0x0A,
	0x07, 0x05, 0x03, 0x01, 0x00, 0xFD, 0xFB, 0xF9, 0xF7, 0xF5, 0xF2, 0xF0,
	0xEE, 0xEC, 0xEA, 0xE8, 0xE6, 0xE4, 0xE3, 0xE1, 0xDF, 0xDE, 0xDC, 0xDA,
	0xD9, 0xD8, 0xD6, 0xD5, 0xD4, 0xD3, 0xD2, 0xD2, 0xD1, 0xD0, 0xD0, 0xCF,
	0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2,
	0xD3, 0xD3, 0xD4, 0xD6, 0xD7, 0xD8, 0xD9, 0xDB, 0xDC, 0xDE, 0xE0, 0xE1,
	0xE3, 0xE5, 0xE7, 0xE9, 0xEB, 0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF9,
	0xFC, 0xFE,
#elif defined HAPTIC_RAM_24K_1040_170
    0x85, 0x08, 0x11, 0x09, 0x07, 0x09, 0x08, 0x09,
	0xE8, 0x09, 0xE9, 0x0B, 0x2C, 0x0B, 0x2D, 0x0B, 0xC3, 0x00, 0x00, 0x00,
	0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFE, 0xFE, 0xFD, 0xFD, 0xFC, 0xFB, 0xFA,
	0xF9, 0xF7, 0xF5, 0xF4, 0xF2, 0xF1, 0xF1, 0xF0, 0xF0, 0xEF, 0xEF, 0xEF,
	0xF0, 0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF6, 0xF8, 0xFB, 0xFE, 0x01, 0x06,
	0x0B, 0x12, 0x1A, 0x24, 0x2D, 0x33, 0x38, 0x3B, 0x3D, 0x3D, 0x3B, 0x38,
	0x34, 0x2E, 0x26, 0x1B, 0x0E, 0xFF, 0xF1, 0xE4, 0xDA, 0xD2, 0xCB, 0xC5,
	0xC0, 0xBC, 0xB8, 0xB6, 0xB3, 0xB1, 0xB0, 0xAE, 0xAD, 0xAC, 0xAB, 0xAB,
	0xAA, 0xAA, 0xA9, 0xA9, 0xA9, 0xA9, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8,
	0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA9, 0xA9, 0xA9, 0xA9, 0xAA,
	0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF, 0xB0, 0xB2, 0xB4, 0xB7, 0xBA, 0xBD,
	0xC2, 0xC7, 0xCE, 0xD5, 0xDE, 0xE9, 0xF5, 0x00, 0x0C, 0x1A, 0x2A, 0x38,
	0x43, 0x4C, 0x52, 0x57, 0x5B, 0x5E, 0x60, 0x62, 0x63, 0x64, 0x65, 0x65,
	0x66, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x5F, 0x5C, 0x59, 0x55, 0x51,
	0x4B, 0x44, 0x3C, 0x33, 0x27, 0x19, 0x08, 0xF4, 0xDE, 0xCC, 0xBC, 0xB0,
	0xA5, 0x9D, 0x95, 0x90, 0x8B, 0x87, 0x85, 0x83, 0x81, 0x81, 0x81, 0x81,
	0x82, 0x84, 0x87, 0x8B, 0x8F, 0x95, 0x9C, 0xA4, 0xAF, 0xBB, 0xCA, 0xDC,
	0xF3, 0x09, 0x1D, 0x2E, 0x3C, 0x47, 0x51, 0x59, 0x5F, 0x64, 0x69, 0x6C,
	0x6F, 0x71, 0x72, 0x73, 0x73, 0x73, 0x73, 0x71, 0x70, 0x6E, 0x6C, 0x6A,
	0x68, 0x67, 0x65, 0x63, 0x62, 0x60, 0x5D, 0x5B, 0x58, 0x54, 0x50, 0x4C,
	0x46, 0x3F, 0x37, 0x2E, 0x26, 0x20, 0x1A, 0x16, 0x12, 0x0F, 0x0C, 0x0A,
	0x08, 0x07, 0x06, 0x05, 0x04, 0x03, 0x03, 0x02, 0x02, 0x01, 0x01, 0x01,
	0x01, 0x00, 0x00, 0x00, 0xE5, 0xE0, 0xDC, 0xDA, 0xD7, 0xD5, 0xD4, 0xD3,
	0xD2, 0xD1, 0xD1, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0,
	0xD0, 0xD0, 0xD1, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD7, 0xD9, 0xDC, 0xE0,
	0xE5, 0xEA, 0xF2, 0xFB, 0x05, 0x0E, 0x16, 0x1B, 0x20, 0x24, 0x26, 0x29,
	0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x2F, 0x2F, 0x30, 0x30, 0x30, 0x30, 0x30,
	0x30, 0x2F, 0x2F, 0x2F, 0x2E, 0x2D, 0x2C, 0x2B, 0x29, 0x26, 0x24, 0x20,
	0x1B, 0x16, 0x0E, 0x05, 0xFB, 0xF2, 0xEA, 0xE5, 0xE0, 0xDC, 0xD9, 0xD7,
	0xD5, 0xD4, 0xD3, 0xD2, 0xD1, 0xD1, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0, 0xD0,
	0xD0, 0xD0, 0xD0, 0xD1, 0xD1, 0xD2, 0xD3, 0xD4, 0xD5, 0xD7, 0xD9, 0xDC,
	0xE0, 0xE4, 0xEA, 0xF1, 0xFA, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0C, 0x0D,
	0x0D, 0x0D, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E,
	0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E,
	0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0E, 0x0D, 0x0D, 0x0D,
	0x0C, 0x0C, 0x0B, 0x0A, 0x09, 0x08, 0x06, 0x04, 0x01, 0xFF, 0xFC, 0xFA,
	0xF8, 0xF7, 0xF6, 0xF5, 0xF4, 0xF4, 0xF3, 0xF3, 0xF3, 0xF2, 0xF2, 0xF2,
	0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2,
	0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2, 0xF2,
	0xF2, 0xF2, 0xF3, 0xF3, 0xF3, 0xF4, 0xF4, 0xF5, 0xF6, 0xF7, 0xF9, 0xFB,
	0xFD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0xFD, 0xF9, 0xF5, 0xF1, 0xED, 0xEA, 0xE6, 0xE3, 0xDF, 0xDC,
	0xD9, 0xD6, 0xD3, 0xD1, 0xCF, 0xCD, 0xCB, 0xC9, 0xC8, 0xC7, 0xC6, 0xC5,
	0xC5, 0xC5, 0xC5, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCB, 0xCD, 0xCF, 0xD1,
	0xD3, 0xD6, 0xD9, 0xDC, 0xDF, 0xE2, 0xE6, 0xEA, 0xED, 0xF1, 0xF5, 0xF9,
	0xFD, 0x00, 0x03, 0x07, 0x0B, 0x0F, 0x13, 0x16, 0x1A, 0x1D, 0x21, 0x24,
	0x27, 0x2A, 0x2D, 0x2F, 0x31, 0x33, 0x35, 0x37, 0x38, 0x39, 0x3A, 0x3B,
	0x3B, 0x3B, 0x3B, 0x3B, 0x3A, 0x39, 0x38, 0x37, 0x35, 0x33, 0x31, 0x2F,
	0x2D, 0x2A, 0x27, 0x24, 0x21, 0x1E, 0x1A, 0x16, 0x13, 0x0F, 0x0B, 0x07,
	0x03, 0x00, 0xFD, 0xF9, 0xF5, 0xF1, 0xED, 0xEA, 0xE6, 0xE3, 0xDF, 0xDC,
	0xD9, 0xD6, 0xD3, 0xD1, 0xCF, 0xCD, 0xCB, 0xC9, 0xC8, 0xC7, 0xC6, 0xC5,
	0xC5, 0xC5, 0xC5, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9, 0xCB, 0xCD, 0xCF, 0xD1,
	0xD3, 0xD6, 0xD9, 0xDC, 0xDF, 0xE2, 0xE6, 0xEA, 0xED, 0xF1, 0xF5, 0xF9,
	0xFD, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x07,
	0x08, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
	0x08, 0x08, 0x07, 0x07, 0x06, 0x06, 0x05, 0x04, 0x03, 0x02, 0x02, 0x01,
	0x00, 0xFF, 0xFE, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF8, 0xF6, 0xF5, 0xF4,
	0xF3, 0xF2, 0xF2, 0xF1, 0xF0, 0xEF, 0xEE, 0xED, 0xED, 0xEC, 0xEB, 0xEB,
	0xEA, 0xEA, 0xE9, 0xE9, 0xE9, 0xE8, 0xE8, 0xE8, 0xE8, 0xE8, 0xE8, 0xE8,
	0xE8, 0xE8, 0xE8, 0xE8, 0xE8, 0xE9, 0xE9, 0xE9, 0xEA, 0xEA, 0xEB, 0xEC,
	0xEC, 0xED, 0xEE, 0xEE, 0xEF, 0xF0, 0xF1, 0xF2, 0xF3, 0xF4, 0xF5, 0xF6,
	0xF7, 0xF8, 0xF9, 0xFA, 0xFB, 0xFC, 0xFE, 0xFF, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x02, 0x04, 0x07, 0x09, 0x0C, 0x0E, 0x11, 0x13, 0x15, 0x18,
	0x1A, 0x1C, 0x1E, 0x20, 0x22, 0x24, 0x26, 0x28, 0x2A, 0x2C, 0x2D, 0x2F,
	0x30, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3A, 0x3B,
	0x3B, 0x3B, 0x3B, 0x3B, 0x3B, 0x3B, 0x3B, 0x3B, 0x3A, 0x3A, 0x39, 0x38,
	0x37, 0x36, 0x35, 0x34, 0x33, 0x32, 0x30, 0x2F, 0x2D, 0x2C, 0x2A, 0x28,
	0x26, 0x24, 0x22, 0x20, 0x1E, 0x1C, 0x1A, 0x18, 0x15, 0x13, 0x11, 0x0E,
	0x0C, 0x09, 0x07, 0x04, 0x02, 0x00, 0xFE, 0xFB, 0xF9, 0xF6, 0xF4, 0xF2,
	0xEF, 0xED, 0xEB, 0xE8, 0xE6, 0xE4, 0xE2, 0xE0, 0xDE, 0xDC, 0xDA, 0xD8,
	0xD6, 0xD4, 0xD3, 0xD1, 0xD0, 0xCE, 0xCD, 0xCC, 0xCB, 0xC9, 0xC9, 0xC8,
	0xC7, 0xC6, 0xC6, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5,
	0xC6, 0xC6, 0xC7, 0xC8, 0xC9, 0xCA, 0xCB, 0xCC, 0xCD, 0xCE, 0xD0, 0xD1,
	0xD3, 0xD5, 0xD6, 0xD8, 0xDA, 0xDC, 0xDE, 0xE0, 0xE2, 0xE4, 0xE6, 0xE9,
	0xEB, 0xED, 0xF0, 0xF2, 0xF4, 0xF7, 0xF9, 0xFC,
#elif defined HAPTIC_RAM_24K_9595_170
    0x55, 0x08, 0x11, 0x09, 0x09, 0x09, 0x0A, 0x0A,
	0x68, 0x0A, 0x69, 0x0B, 0x08, 0x0B, 0x09, 0x0B, 0x95, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x02, 0x02, 0x02, 0x02, 0x03, 0x03, 0x04, 0x04, 0x05, 0x06,
	0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0D, 0x0F, 0x11, 0x14, 0x16, 0x1A, 0x1D,
	0x21, 0x26, 0x2B, 0x31, 0x38, 0x40, 0x47, 0x4C, 0x51, 0x55, 0x58, 0x5A,
	0x5C, 0x5D, 0x5D, 0x5C, 0x5C, 0x5B, 0x5A, 0x58, 0x56, 0x54, 0x51, 0x4E,
	0x4B, 0x48, 0x44, 0x41, 0x3D, 0x39, 0x35, 0x31, 0x2C, 0x28, 0x24, 0x1F,
	0x1B, 0x16, 0x11, 0x0C, 0x07, 0x02, 0xFD, 0xF7, 0xF1, 0xEB, 0xE4, 0xDD,
	0xD5, 0xCD, 0xC4, 0xB9, 0xAE, 0xA2, 0x99, 0x90, 0x8A, 0x85, 0x82, 0x81,
	0x81, 0x81, 0x81, 0x84, 0x87, 0x8B, 0x90, 0x95, 0x9B, 0xA2, 0xA9, 0xB0,
	0xB8, 0xC0, 0xC9, 0xD1, 0xDA, 0xE2, 0xEB, 0xF4, 0xFC, 0x05, 0x0D, 0x15,
	0x1D, 0x25, 0x2C, 0x33, 0x39, 0x3F, 0x44, 0x49, 0x4D, 0x51, 0x54, 0x56,
	0x58, 0x59, 0x59, 0x58, 0x56, 0x54, 0x51, 0x4E, 0x4B, 0x48, 0x44, 0x40,
	0x3B, 0x36, 0x31, 0x2C, 0x26, 0x20, 0x19, 0x13, 0x0C, 0x05, 0xFE, 0xF7,
	0xEF, 0xE8, 0xE0, 0xD8, 0xD1, 0xC9, 0xC2, 0xBB, 0xB4, 0xAD, 0xA7, 0xA1,
	0x9B, 0x96, 0x91, 0x8D, 0x89, 0x86, 0x84, 0x83, 0x83, 0x83, 0x85, 0x88,
	0x8C, 0x91, 0x98, 0xA1, 0xAB, 0xB6, 0xBF, 0xC8, 0xCF, 0xD6, 0xDD, 0xE2,
	0xE8, 0xED, 0xF1, 0xF6, 0xFA, 0xFD, 0x01, 0x05, 0x08, 0x0B, 0x0F, 0x12,
	0x15, 0x18, 0x1A, 0x1D, 0x20, 0x22, 0x25, 0x27, 0x29, 0x2B, 0x2D, 0x2F,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x36, 0x36, 0x36, 0x35, 0x34, 0x32,
	0x30, 0x2D, 0x2A, 0x26, 0x21, 0x1D, 0x19, 0x16, 0x13, 0x11, 0x0E, 0x0C,
	0x0B, 0x09, 0x08, 0x07, 0x06, 0x05, 0x04, 0x04, 0x03, 0x03, 0x02, 0x02,
	0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x02, 0x03, 0x03, 0x03,
	0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0B, 0x0D, 0x0E, 0x11, 0x13, 0x16,
	0x1A, 0x1D, 0x22, 0x27, 0x2D, 0x32, 0x37, 0x3A, 0x3D, 0x3F, 0x40, 0x41,
	0x41, 0x41, 0x3F, 0x3D, 0x3A, 0x37, 0x32, 0x2D, 0x27, 0x1F, 0x16, 0x0C,
	0x00, 0xF4, 0xE9, 0xDF, 0xD6, 0xCF, 0xC9, 0xC3, 0xBE, 0xBA, 0xB6, 0xB3,
	0xB0, 0xAE, 0xAB, 0xAA, 0xA8, 0xA7, 0xA5, 0xA4, 0xA3, 0xA3, 0xA2, 0xA1,
	0xA1, 0xA0, 0xA0, 0xA0, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E, 0x9E,
	0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F, 0x9F,
	0xA0, 0xA0, 0xA0, 0xA1, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8,
	0xAA, 0xAC, 0xAE, 0xB1, 0xB3, 0xB7, 0xBB, 0xBF, 0xC4, 0xCA, 0xD0, 0xD8,
	0xE1, 0xEB, 0xF6, 0x03, 0x0F, 0x1A, 0x23, 0x2C, 0x33, 0x39, 0x3E, 0x43,
	0x47, 0x4A, 0x4D, 0x50, 0x52, 0x54, 0x56, 0x57, 0x58, 0x59, 0x5A, 0x5B,
	0x5B, 0x5C, 0x5C, 0x5C, 0x5C, 0x5B, 0x5B, 0x5A, 0x5A, 0x59, 0x58, 0x56,
	0x55, 0x53, 0x51, 0x4F, 0x4C, 0x48, 0x45, 0x40, 0x3B, 0x35, 0x2F, 0x27,
	0x1E, 0x14, 0x08, 0xFD, 0xF2, 0xE9, 0xE2, 0xDC, 0xD7, 0xD3, 0xD0, 0xCE,
	0xCD, 0xCD, 0xCE, 0xD0, 0xD3, 0xD7, 0xDC, 0xE2, 0xE9, 0xF2, 0xFD, 0x08,
	0x14, 0x1E, 0x27, 0x2F, 0x36, 0x3B, 0x41, 0x45, 0x49, 0x4C, 0x4F, 0x52,
	0x54, 0x56, 0x57, 0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5D, 0x5E, 0x5E, 0x5E,
	0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5F, 0x5E, 0x5E, 0x5E, 0x5D, 0x5C, 0x5C,
	0x5B, 0x5A, 0x59, 0x57, 0x55, 0x54, 0x51, 0x4F, 0x4C, 0x48, 0x45, 0x40,
	0x3B, 0x35, 0x2E, 0x26, 0x1D, 0x13, 0x09, 0x01, 0xFB, 0xF4, 0xEF, 0xEA,
	0xE6, 0xE2, 0xDF, 0xDC, 0xDA, 0xD8, 0xD6, 0xD4, 0xD3, 0xD2, 0xD1, 0xD0,
	0xCF, 0xCE, 0xCE, 0xCD, 0xCD, 0xCD, 0xCC, 0xCC, 0xCC, 0xCB, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCC, 0xCC, 0xCC, 0xCD, 0xCD, 0xCD,
	0xCE, 0xCF, 0xCF, 0xD0, 0xD1, 0xD2, 0xD3, 0xD5, 0xD6, 0xD8, 0xDA, 0xDD,
	0xDF, 0xE2, 0xE6, 0xEA, 0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF8, 0xF9,
	0xFA, 0xFB, 0xFC, 0xFC, 0xFD, 0xFD, 0xFE, 0xFE, 0xFE, 0xFF, 0xFF, 0xFF,
	0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03,
	0x03, 0x04, 0x05, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0B, 0x0C, 0x0E, 0x10,
	0x13, 0x16, 0x19, 0x1C, 0x21, 0x25, 0x2B, 0x31, 0x38, 0x41, 0x48, 0x4D,
	0x50, 0x51, 0x51, 0x4F, 0x4B, 0x45, 0x3D, 0x33, 0x26, 0x17, 0x05, 0xF1,
	0xDE, 0xCD, 0xBE, 0xB2, 0xA7, 0x9E, 0x96, 0x90, 0x8B, 0x87, 0x84, 0x82,
	0x81, 0x81, 0x81, 0x83, 0x86, 0x8A, 0x8F, 0x95, 0x9C, 0xA5, 0xB0, 0xBC,
	0xCA, 0xDB, 0xEE, 0x03, 0x17, 0x29, 0x38, 0x45, 0x50, 0x59, 0x60, 0x66,
	0x6B, 0x6E, 0x70, 0x71, 0x71, 0x6F, 0x6C, 0x68, 0x62, 0x5C, 0x53, 0x49,
	0x3C, 0x2E, 0x1E, 0x11, 0x05, 0xFC, 0xF3, 0xEB, 0xE4, 0xDE, 0xD9, 0xD5,
	0xD1, 0xCE, 0xCB, 0xC9, 0xC8, 0xC6, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC6,
	0xC7, 0xC9, 0xCB, 0xCE, 0xD1, 0xD4, 0xD9, 0xDD, 0xE2, 0xE6, 0xEA, 0xED,
	0xEF, 0xF2, 0xF4, 0xF6, 0xF7, 0xF8, 0xFA, 0xFB, 0xFB, 0xFC, 0xFD, 0xFD,
	0xFE, 0xFE, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x02, 0x04, 0x06, 0x08, 0x0B, 0x0D, 0x0F, 0x11, 0x13, 0x15,
	0x17, 0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23, 0x25, 0x26, 0x28, 0x29,
	0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31,
	0x31, 0x31, 0x31, 0x31, 0x31, 0x30, 0x30, 0x2F, 0x2F, 0x2E, 0x2D, 0x2C,
	0x2B, 0x2A, 0x28, 0x27, 0x26, 0x24, 0x23, 0x21, 0x1F, 0x1E, 0x1C, 0x1A,
	0x18, 0x16, 0x14, 0x12, 0x10, 0x0E, 0x0C, 0x0A, 0x07, 0x05, 0x03, 0x01,
	0x00, 0xFD, 0xFB, 0xF9, 0xF7, 0xF5, 0xF2, 0xF0, 0xEE, 0xEC, 0xEA, 0xE8,
	0xE6, 0xE4, 0xE3, 0xE1, 0xDF, 0xDE, 0xDC, 0xDA, 0xD9, 0xD8, 0xD6, 0xD5,
	0xD4, 0xD3, 0xD2, 0xD2, 0xD1, 0xD0, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2, 0xD3, 0xD3, 0xD4, 0xD6,
	0xD7, 0xD8, 0xD9, 0xDB, 0xDC, 0xDE, 0xE0, 0xE1, 0xE3, 0xE5, 0xE7, 0xE9,
	0xEB, 0xED, 0xEF, 0xF1, 0xF3, 0xF5, 0xF7, 0xF9, 0xFC, 0xFE,
#elif defined HAPTIC_RAM_24K_0832_205
    0x85, 0x08, 0x11, 0x0A, 0x59, 0x0A, 0x5A, 0x0C,
	0x2D, 0x0C, 0x2E, 0x0D, 0xC6, 0x0D, 0xC7, 0x0E, 0x3B, 0x00, 0x06, 0x0D,
	0x14, 0x1A, 0x21, 0x27, 0x2D, 0x34, 0x3A, 0x3F, 0x45, 0x4B, 0x50, 0x55,
	0x5A, 0x5E, 0x62, 0x66, 0x6A, 0x6D, 0x70, 0x73, 0x75, 0x78, 0x79, 0x7B,
	0x7C, 0x7C, 0x7C, 0x7C, 0x7C, 0x7B, 0x7A, 0x78, 0x77, 0x74, 0x72, 0x6F,
	0x6C, 0x68, 0x65, 0x60, 0x5C, 0x57, 0x53, 0x4D, 0x48, 0x42, 0x3D, 0x37,
	0x31, 0x2A, 0x24, 0x1E, 0x17, 0x10, 0x0A, 0x03, 0xFD, 0xF7, 0xF0, 0xE9,
	0xE3, 0xDC, 0xD6, 0xD0, 0xCA, 0xC4, 0xBE, 0xB8, 0xB3, 0xAE, 0xA9, 0xA4,
	0xA0, 0x9C, 0x98, 0x94, 0x91, 0x8E, 0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84,
	0x84, 0x84, 0x84, 0x84, 0x85, 0x87, 0x88, 0x8A, 0x8D, 0x8F, 0x92, 0x96,
	0x99, 0x9D, 0xA2, 0xA6, 0xAB, 0xB0, 0xB5, 0xBB, 0xC0, 0xC6, 0xCC, 0xD2,
	0xD8, 0xDF, 0xE5, 0xEC, 0xF3, 0xF9, 0x00, 0x06, 0x0C, 0x13, 0x1A, 0x20,
	0x27, 0x2D, 0x33, 0x39, 0x3F, 0x45, 0x4A, 0x4F, 0x54, 0x59, 0x5E, 0x62,
	0x66, 0x6A, 0x6D, 0x70, 0x73, 0x75, 0x77, 0x79, 0x7A, 0x7C, 0x7C, 0x7C,
	0x7C, 0x7C, 0x7B, 0x7A, 0x79, 0x77, 0x75, 0x72, 0x6F, 0x6C, 0x69, 0x65,
	0x61, 0x5C, 0x58, 0x53, 0x4E, 0x48, 0x43, 0x3D, 0x37, 0x31, 0x2B, 0x25,
	0x1E, 0x18, 0x11, 0x0A, 0x04, 0xFE, 0xF7, 0xF1, 0xEA, 0xE3, 0xDD, 0xD6,
	0xD0, 0xCA, 0xC4, 0xBE, 0xB9, 0xB3, 0xAE, 0xA9, 0xA5, 0xA0, 0x9C, 0x98,
	0x95, 0x91, 0x8F, 0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84,
	0x84, 0x85, 0x87, 0x88, 0x8A, 0x8D, 0x8F, 0x92, 0x95, 0x99, 0x9D, 0xA1,
	0xA6, 0xAA, 0xAF, 0xB5, 0xBA, 0xC0, 0xC6, 0xCC, 0xD2, 0xD8, 0xDE, 0xE5,
	0xEB, 0xF2, 0xF9, 0x00, 0x05, 0x0C, 0x13, 0x19, 0x20, 0x26, 0x2C, 0x33,
	0x39, 0x3F, 0x44, 0x4A, 0x4F, 0x54, 0x59, 0x5D, 0x62, 0x66, 0x69, 0x6D,
	0x70, 0x73, 0x75, 0x77, 0x79, 0x7A, 0x7B, 0x7C, 0x7C, 0x7C, 0x7C, 0x7B,
	0x7A, 0x79, 0x77, 0x75, 0x72, 0x6F, 0x6C, 0x69, 0x65, 0x61, 0x5D, 0x58,
	0x53, 0x4E, 0x49, 0x43, 0x3E, 0x38, 0x32, 0x2B, 0x25, 0x1F, 0x18, 0x11,
	0x0B, 0x04, 0xFE, 0xF8, 0xF1, 0xEA, 0xE4, 0xDD, 0xD7, 0xD1, 0xCB, 0xC5,
	0xBF, 0xB9, 0xB4, 0xAF, 0xAA, 0xA5, 0xA0, 0x9C, 0x98, 0x95, 0x92, 0x8F,
	0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x85, 0x86,
	0x88, 0x8A, 0x8C, 0x8F, 0x92, 0x95, 0x99, 0x9D, 0xA1, 0xA5, 0xAA, 0xAF,
	0xB4, 0xBA, 0xBF, 0xC5, 0xCB, 0xD1, 0xD8, 0xDE, 0xE4, 0xEB, 0xF2, 0xF8,
	0x00, 0xFA, 0xF5, 0xEF, 0xE9, 0xE3, 0xDD, 0xD7, 0xD2, 0xCC, 0xC7, 0xC2,
	0xBD, 0xB9, 0xB4, 0xB0, 0xAC, 0xA8, 0xA4, 0xA1, 0x9E, 0x9B, 0x99, 0x97,
	0x95, 0x93, 0x92, 0x91, 0x91, 0x91, 0x91, 0x91, 0x92, 0x93, 0x94, 0x96,
	0x98, 0x9A, 0x9D, 0x9F, 0xA3, 0xA6, 0xAA, 0xAE, 0xB2, 0xB6, 0xBB, 0xC0,
	0xC4, 0xCA, 0xCF, 0xD4, 0xDA, 0xE0, 0xE5, 0xEB, 0xF1, 0xF7, 0xFD, 0x02,
	0x08, 0x0E, 0x14, 0x1A, 0x20, 0x26, 0x2B, 0x31, 0x36, 0x3B, 0x40, 0x45,
	0x4A, 0x4E, 0x52, 0x56, 0x5A, 0x5D, 0x60, 0x63, 0x66, 0x68, 0x6A, 0x6C,
	0x6D, 0x6E, 0x6F, 0x6F, 0x6F, 0x6F, 0x6F, 0x6E, 0x6D, 0x6B, 0x69, 0x67,
	0x65, 0x62, 0x5F, 0x5C, 0x58, 0x55, 0x51, 0x4C, 0x48, 0x43, 0x3E, 0x39,
	0x34, 0x2F, 0x29, 0x23, 0x1E, 0x18, 0x12, 0x0C, 0x06, 0x00, 0xFD, 0xFA,
	0xF6, 0xF3, 0xEF, 0xEC, 0xE9, 0xE5, 0xE2, 0xDF, 0xDC, 0xD9, 0xD7, 0xD4,
	0xD2, 0xCF, 0xCD, 0xCB, 0xC9, 0xC7, 0xC6, 0xC4, 0xC3, 0xC2, 0xC1, 0xC0,
	0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC7,
	0xC8, 0xCA, 0xCC, 0xCE, 0xD0, 0xD3, 0xD5, 0xD8, 0xDB, 0xDE, 0xE1, 0xE4,
	0xE7, 0xEA, 0xED, 0xF1, 0xF4, 0xF8, 0xFB, 0xFF, 0x01, 0x05, 0x08, 0x0C,
	0x0F, 0x12, 0x16, 0x19, 0x1C, 0x1F, 0x22, 0x25, 0x28, 0x2A, 0x2D, 0x2F,
	0x32, 0x34, 0x36, 0x38, 0x39, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F, 0x40, 0x40,
	0x40, 0x40, 0x40, 0x40, 0x40, 0x3F, 0x3E, 0x3D, 0x3C, 0x3A, 0x39, 0x37,
	0x35, 0x33, 0x31, 0x2F, 0x2C, 0x29, 0x27, 0x24, 0x21, 0x1E, 0x1B, 0x18,
	0x14, 0x11, 0x0E, 0x0A, 0x07, 0x03, 0x00, 0x06, 0x0D, 0x14, 0x1A, 0x21,
	0x27, 0x2D, 0x34, 0x3A, 0x3F, 0x45, 0x4B, 0x50, 0x55, 0x5A, 0x5E, 0x62,
	0x66, 0x6A, 0x6D, 0x70, 0x73, 0x75, 0x78, 0x79, 0x7B, 0x7C, 0x7C, 0x7C,
	0x7C, 0x7C, 0x7B, 0x7A, 0x78, 0x77, 0x74, 0x72, 0x6F, 0x6C, 0x68, 0x65,
	0x60, 0x5C, 0x57, 0x53, 0x4D, 0x48, 0x42, 0x3D, 0x37, 0x31, 0x2A, 0x24,
	0x1E, 0x17, 0x10, 0x0A, 0x03, 0xFD, 0xF7, 0xF0, 0xE9, 0xE3, 0xDC, 0xD6,
	0xD0, 0xCA, 0xC4, 0xBE, 0xB8, 0xB3, 0xAE, 0xA9, 0xA4, 0xA0, 0x9C, 0x98,
	0x94, 0x91, 0x8E, 0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84,
	0x84, 0x85, 0x87, 0x88, 0x8A, 0x8D, 0x8F, 0x92, 0x96, 0x99, 0x9D, 0xA2,
	0xA6, 0xAB, 0xB0, 0xB5, 0xBB, 0xC0, 0xC6, 0xCC, 0xD2, 0xD8, 0xDF, 0xE5,
	0xEC, 0xF3, 0xF9, 0x00, 0x06, 0x0C, 0x13, 0x1A, 0x20, 0x27, 0x2D, 0x33,
	0x39, 0x3F, 0x45, 0x4A, 0x4F, 0x54, 0x59, 0x5E, 0x62, 0x66, 0x6A, 0x6D,
	0x70, 0x73, 0x75, 0x77, 0x79, 0x7A, 0x7C, 0x7C, 0x7C, 0x7C, 0x7C, 0x7B,
	0x7A, 0x79, 0x77, 0x75, 0x72, 0x6F, 0x6C, 0x69, 0x65, 0x61, 0x5C, 0x58,
	0x53, 0x4E, 0x48, 0x43, 0x3D, 0x37, 0x31, 0x2B, 0x25, 0x1E, 0x18, 0x11,
	0x0A, 0x04, 0xFE, 0xF7, 0xF1, 0xEA, 0xE3, 0xDD, 0xD6, 0xD0, 0xCA, 0xC4,
	0xBE, 0xB9, 0xB3, 0xAE, 0xA9, 0xA5, 0xA0, 0x9C, 0x98, 0x95, 0x91, 0x8F,
	0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84, 0x84, 0x84, 0x85, 0x87,
	0x88, 0x8A, 0x8D, 0x8F, 0x92, 0x95, 0x99, 0x9D, 0xA1, 0xA6, 0xAA, 0xAF,
	0xB5, 0xBA, 0xC0, 0xC6, 0xCC, 0xD2, 0xD8, 0xDE, 0xE5, 0xEB, 0xF2, 0xF9,
	0x00, 0xFA, 0xF4, 0xEE, 0xE8, 0xE2, 0xDC, 0xD7, 0xD1, 0xCC, 0xC6, 0xC1,
	0xBC, 0xB7, 0xB3, 0xAE, 0xAA, 0xA6, 0xA3, 0x9F, 0x9C, 0x9A, 0x97, 0x95,
	0x93, 0x91, 0x90, 0x8F, 0x8F, 0x8F, 0x8F, 0x8F, 0x90, 0x91, 0x92, 0x94,
	0x96, 0x98, 0x9B, 0x9E, 0xA1, 0xA4, 0xA8, 0xAC, 0xB0, 0xB5, 0xB9, 0xBE,
	0xC3, 0xC9, 0xCE, 0xD4, 0xD9, 0xDF, 0xE5, 0xEB, 0xF1, 0xF7, 0xFD, 0x02,
	0x08, 0x0F, 0x15, 0x1B, 0x20, 0x26, 0x2C, 0x32, 0x37, 0x3C, 0x41, 0x46,
	0x4B, 0x4F, 0x54, 0x58, 0x5B, 0x5F, 0x62, 0x65, 0x68, 0x6A, 0x6C, 0x6E,
	0x6F, 0x70, 0x71, 0x71, 0x71, 0x71, 0x71, 0x70, 0x6F, 0x6D, 0x6B, 0x69,
	0x67, 0x64, 0x61, 0x5E, 0x5A, 0x56, 0x52, 0x4E, 0x49, 0x44, 0x3F, 0x3A,
	0x35, 0x2F, 0x2A, 0x24, 0x1E, 0x18, 0x12, 0x0C, 0x06, 0x00, 0xFD, 0xF9,
	0xF5, 0xF2, 0xEE, 0xEA, 0xE7, 0xE3, 0xE0, 0xDD, 0xDA, 0xD6, 0xD4, 0xD1,
	0xCE, 0xCC, 0xC9, 0xC7, 0xC5, 0xC3, 0xC1, 0xC0, 0xBE, 0xBD, 0xBC, 0xBC,
	0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF, 0xC0, 0xC2,
	0xC4, 0xC6, 0xC8, 0xCA, 0xCD, 0xCF, 0xD2, 0xD5, 0xD8, 0xDB, 0xDE, 0xE2,
	0xE5, 0xE8, 0xEC, 0xF0, 0xF3, 0xF7, 0xFB, 0xFE, 0x01, 0x05, 0x09, 0x0C,
	0x10, 0x14, 0x17, 0x1B, 0x1E, 0x22, 0x25, 0x28, 0x2B, 0x2E, 0x31, 0x33,
	0x36, 0x38, 0x3A, 0x3C, 0x3E, 0x3F, 0x41, 0x42, 0x43, 0x44, 0x45, 0x45,
	0x45, 0x45, 0x45, 0x45, 0x44, 0x44, 0x43, 0x42, 0x40, 0x3F, 0x3D, 0x3B,
	0x39, 0x37, 0x35, 0x32, 0x2F, 0x2D, 0x2A, 0x27, 0x24, 0x20, 0x1D, 0x19,
	0x16, 0x12, 0x0F, 0x0B, 0x07, 0x04, 0x00, 0x06, 0x0D, 0x14, 0x1B, 0x21,
	0x28, 0x2E, 0x34, 0x3A, 0x40, 0x46, 0x4C, 0x51, 0x56, 0x5B, 0x60, 0x64,
	0x68, 0x6C, 0x6F, 0x72, 0x75, 0x77, 0x79, 0x7B, 0x7D, 0x7E, 0x7E, 0x7E,
	0x7E, 0x7E, 0x7D, 0x7C, 0x7A, 0x79, 0x76, 0x74, 0x71, 0x6E, 0x6A, 0x66,
	0x62, 0x5E, 0x59, 0x54, 0x4F, 0x49, 0x44, 0x3E, 0x38, 0x31, 0x2B, 0x25,
	0x1E, 0x17, 0x11, 0x0A, 0x03, 0xFD, 0xF7, 0xF0, 0xE9, 0xE2, 0xDC, 0xD5,
	0xCF, 0xC9, 0xC3, 0xBD, 0xB7, 0xB2, 0xAD, 0xA8, 0xA3, 0x9E, 0x9A, 0x96,
	0x93, 0x8F, 0x8C, 0x8A, 0x88, 0x86, 0x84, 0x83, 0x82, 0x82, 0x82, 0x82,
	0x82, 0x83, 0x85, 0x86, 0x88, 0x8B, 0x8E, 0x91, 0x94, 0x98, 0x9C, 0xA0,
	0xA5, 0xA9, 0xAF, 0xB4, 0xB9, 0xBF, 0xC5, 0xCB, 0xD1, 0xD8, 0xDE, 0xE5,
	0xEC, 0xF2, 0xF9, 0x00, 0x06, 0x0D, 0x13, 0x1A, 0x21, 0x27, 0x2E, 0x34,
	0x3A, 0x40, 0x46, 0x4B, 0x51, 0x56, 0x5B, 0x5F, 0x64, 0x68, 0x6B, 0x6F,
	0x72, 0x75, 0x77, 0x79, 0x7B, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7D,
	0x7C, 0x7B, 0x79, 0x76, 0x74, 0x71, 0x6E, 0x6A, 0x66, 0x62, 0x5E, 0x59,
	0x54, 0x4F, 0x4A, 0x44, 0x3E, 0x38, 0x32, 0x2C, 0x25, 0x1F, 0x18, 0x11,
	0x0A, 0x00, 0x06, 0x0C, 0x12, 0x18, 0x1E, 0x24, 0x29, 0x2F, 0x34, 0x3A,
	0x3F, 0x44, 0x49, 0x4D, 0x52, 0x56, 0x5A, 0x5D, 0x61, 0x64, 0x66, 0x69,
	0x6B, 0x6D, 0x6F, 0x70, 0x71, 0x71, 0x71, 0x71, 0x71, 0x70, 0x6F, 0x6E,
	0x6C, 0x6A, 0x68, 0x65, 0x62, 0x5F, 0x5C, 0x58, 0x54, 0x50, 0x4B, 0x47,
	0x42, 0x3D, 0x37, 0x32, 0x2C, 0x27, 0x21, 0x1B, 0x15, 0x0F, 0x09, 0x03,
	0xFE, 0xF8, 0xF1, 0xEB, 0xE5, 0xE0, 0xDA, 0xD4, 0xCE, 0xC9, 0xC4, 0xBF,
	0xBA, 0xB5, 0xB1, 0xAC, 0xA8, 0xA5, 0xA1, 0x9E, 0x9B, 0x98, 0x96, 0x94,
	0x92, 0x91, 0x90, 0x8F, 0x8F, 0x8F, 0x8F, 0x8F, 0x90, 0x91, 0x93, 0x95,
	0x97, 0x99, 0x9C, 0x9F, 0xA2, 0xA6, 0xAA, 0xAE, 0xB2, 0xB7, 0xBC, 0xC1,
	0xC6, 0xCB, 0xD1, 0xD6, 0xDC, 0xE2, 0xE8, 0xEE, 0xF4, 0xFA, 0x00, 0x02,
	0x04, 0x06, 0x08, 0x0A, 0x0C, 0x0E, 0x10, 0x12, 0x14, 0x16, 0x18, 0x19,
	0x1B, 0x1C, 0x1E, 0x1F, 0x20, 0x22, 0x23, 0x24, 0x24, 0x25, 0x26, 0x26,
	0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x26, 0x25, 0x24,
	0x23, 0x22, 0x21, 0x20, 0x1F, 0x1D, 0x1C, 0x1A, 0x18, 0x17, 0x15, 0x13,
	0x11, 0x0F, 0x0D, 0x0B, 0x09, 0x07, 0x05, 0x03, 0x01, 0x00, 0xFD, 0xFB,
	0xF9, 0xF7, 0xF5, 0xF3, 0xF1, 0xEF, 0xED, 0xEB, 0xE9, 0xE8, 0xE6, 0xE4,
	0xE3, 0xE2, 0xE0, 0xDF, 0xDE, 0xDD, 0xDC, 0xDB, 0xDA, 0xDA, 0xD9, 0xD9,
	0xD9, 0xD9, 0xD9, 0xD9, 0xD9, 0xD9, 0xDA, 0xDA, 0xDB, 0xDB, 0xDC, 0xDD,
	0xDE, 0xE0, 0xE1, 0xE2, 0xE4, 0xE5, 0xE7, 0xE8, 0xEA, 0xEC, 0xEE, 0xF0,
	0xF2, 0xF4, 0xF6, 0xF8, 0xFA, 0xFC, 0xFE, 0x00, 0x03, 0x07, 0x0B, 0x0E,
	0x12, 0x16, 0x19, 0x1D, 0x20, 0x23, 0x26, 0x2A, 0x2C, 0x2F, 0x32, 0x34,
	0x37, 0x39, 0x3B, 0x3D, 0x3F, 0x40, 0x42, 0x43, 0x44, 0x44, 0x45, 0x45,
	0x45, 0x45, 0x45, 0x45, 0x44, 0x43, 0x42, 0x41, 0x40, 0x3E, 0x3C, 0x3A,
	0x38, 0x36, 0x33, 0x31, 0x2E, 0x2B, 0x28, 0x25, 0x22, 0x1E, 0x1B, 0x18,
	0x14, 0x10, 0x0D, 0x09, 0x05, 0x02, 0xFF, 0xFB, 0xF7, 0xF4, 0xF0, 0xEC,
	0xE9, 0xE5, 0xE2, 0xDE, 0xDB, 0xD8, 0xD5, 0xD2, 0xCF, 0xCD, 0xCA, 0xC8,
	0xC6, 0xC4, 0xC2, 0xC1, 0xBF, 0xBE, 0xBD, 0xBC, 0xBB, 0xBB, 0xBB, 0xBB,
	0xBB, 0xBB, 0xBC, 0xBC, 0xBD, 0xBE, 0xC0, 0xC1, 0xC3, 0xC5, 0xC7, 0xC9,
	0xCB, 0xCE, 0xD1, 0xD3, 0xD6, 0xD9, 0xDC, 0xE0, 0xE3, 0xE7, 0xEA, 0xEE,
	0xF1, 0xF5, 0xF9, 0xFC,
#elif defined HAPTIC_RAM_24K_0832_235
    0x85, 0x08, 0x11, 0x0A, 0xDC, 0x0A, 0xDD, 0x0C,
	0xF0, 0x0C, 0xF1, 0x0E, 0x4C, 0x0E, 0x4D, 0x0E, 0xB2, 0x00, 0x07, 0x0E,
	0x16, 0x1D, 0x24, 0x2B, 0x32, 0x38, 0x3F, 0x45, 0x4B, 0x50, 0x56, 0x5B,
	0x5F, 0x63, 0x67, 0x6B, 0x6E, 0x71, 0x73, 0x75, 0x76, 0x77, 0x77, 0x77,
	0x77, 0x76, 0x75, 0x73, 0x71, 0x6E, 0x6B, 0x68, 0x64, 0x5F, 0x5B, 0x56,
	0x51, 0x4B, 0x45, 0x3F, 0x39, 0x32, 0x2B, 0x24, 0x1D, 0x16, 0x0F, 0x07,
	0x00, 0xFA, 0xF2, 0xEB, 0xE4, 0xDD, 0xD6, 0xCF, 0xC8, 0xC2, 0xBC, 0xB6,
	0xB0, 0xAB, 0xA6, 0xA1, 0x9D, 0x99, 0x95, 0x92, 0x90, 0x8D, 0x8B, 0x8A,
	0x89, 0x89, 0x89, 0x89, 0x8A, 0x8B, 0x8D, 0x8F, 0x92, 0x95, 0x98, 0x9C,
	0xA0, 0xA5, 0xAA, 0xAF, 0xB5, 0xBA, 0xC1, 0xC7, 0xCE, 0xD4, 0xDB, 0xE2,
	0xEA, 0xF1, 0xF8, 0x00, 0x06, 0x0D, 0x15, 0x1C, 0x23, 0x2A, 0x31, 0x37,
	0x3E, 0x44, 0x4A, 0x50, 0x55, 0x5A, 0x5F, 0x63, 0x67, 0x6A, 0x6E, 0x70,
	0x73, 0x74, 0x76, 0x77, 0x77, 0x77, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6E,
	0x6B, 0x68, 0x64, 0x60, 0x5B, 0x57, 0x51, 0x4C, 0x46, 0x40, 0x39, 0x33,
	0x2C, 0x25, 0x1E, 0x17, 0x10, 0x08, 0x01, 0xFB, 0xF3, 0xEC, 0xE5, 0xDE,
	0xD7, 0xD0, 0xC9, 0xC3, 0xBC, 0xB6, 0xB1, 0xAB, 0xA6, 0xA2, 0x9D, 0x99,
	0x96, 0x93, 0x90, 0x8E, 0x8C, 0x8A, 0x89, 0x89, 0x89, 0x89, 0x8A, 0x8B,
	0x8D, 0x8F, 0x91, 0x94, 0x98, 0x9C, 0xA0, 0xA4, 0xA9, 0xAE, 0xB4, 0xBA,
	0xC0, 0xC6, 0xCD, 0xD3, 0xDA, 0xE1, 0xE9, 0xF0, 0xF7, 0xFF, 0x05, 0x0C,
	0x14, 0x1B, 0x22, 0x29, 0x30, 0x37, 0x3D, 0x43, 0x49, 0x4F, 0x54, 0x59,
	0x5E, 0x62, 0x66, 0x6A, 0x6D, 0x70, 0x72, 0x74, 0x76, 0x77, 0x77, 0x77,
	0x77, 0x76, 0x75, 0x73, 0x71, 0x6F, 0x6C, 0x69, 0x65, 0x61, 0x5C, 0x57,
	0x52, 0x4C, 0x47, 0x41, 0x3A, 0x34, 0x2D, 0x26, 0x1F, 0x18, 0x11, 0x09,
	0x02, 0xFB, 0xF4, 0xED, 0xE6, 0xDE, 0xD7, 0xD1, 0xCA, 0xC3, 0xBD, 0xB7,
	0xB1, 0xAC, 0xA7, 0xA2, 0x9E, 0x9A, 0x96, 0x93, 0x90, 0x8E, 0x8C, 0x8A,
	0x89, 0x89, 0x89, 0x89, 0x8A, 0x8B, 0x8C, 0x8E, 0x91, 0x94, 0x97, 0x9B,
	0x9F, 0xA4, 0xA8, 0xAE, 0xB3, 0xB9, 0xBF, 0xC5, 0xCC, 0xD3, 0xD9, 0xE1,
	0xE8, 0xEF, 0xF6, 0x00, 0xFA, 0xF3, 0xEC, 0xE5, 0xDE, 0xD8, 0xD1, 0xCB,
	0xC5, 0xBF, 0xB9, 0xB4, 0xAF, 0xAA, 0xA6, 0xA2, 0x9E, 0x9A, 0x97, 0x95,
	0x92, 0x91, 0x8F, 0x8E, 0x8E, 0x8E, 0x8E, 0x8E, 0x90, 0x91, 0x93, 0x95,
	0x98, 0x9B, 0x9F, 0xA3, 0xA7, 0xAB, 0xB0, 0xB5, 0xBB, 0xC0, 0xC6, 0xCD,
	0xD3, 0xD9, 0xE0, 0xE7, 0xEE, 0xF4, 0xFB, 0x01, 0x08, 0x0F, 0x16, 0x1D,
	0x24, 0x2A, 0x31, 0x37, 0x3D, 0x43, 0x48, 0x4E, 0x53, 0x57, 0x5C, 0x60,
	0x63, 0x67, 0x69, 0x6C, 0x6E, 0x70, 0x71, 0x72, 0x72, 0x72, 0x72, 0x71,
	0x70, 0x6E, 0x6C, 0x6A, 0x67, 0x64, 0x60, 0x5C, 0x58, 0x53, 0x4E, 0x49,
	0x44, 0x3E, 0x38, 0x32, 0x2B, 0x25, 0x1E, 0x17, 0x11, 0x0A, 0x03, 0xFD,
	0xF6, 0xEF, 0xE8, 0xE1, 0xDA, 0xD4, 0xCE, 0xC7, 0xC1, 0xBC, 0xB6, 0xB1,
	0xAC, 0xA8, 0xA3, 0x9F, 0x9C, 0x99, 0x96, 0x93, 0x91, 0x90, 0x8F, 0x8E,
	0x8E, 0x8E, 0x8E, 0x8F, 0x90, 0x92, 0x94, 0x97, 0x9A, 0x9D, 0xA1, 0xA5,
	0xA9, 0xAE, 0xB3, 0xB8, 0xBE, 0xC4, 0xCA, 0xD0, 0xD6, 0xDD, 0xE4, 0xEA,
	0xF1, 0xF8, 0xFF, 0x05, 0x0C, 0x13, 0x1A, 0x21, 0x27, 0x2E, 0x34, 0x3A,
	0x40, 0x46, 0x4B, 0x50, 0x55, 0x5A, 0x5E, 0x62, 0x65, 0x68, 0x6B, 0x6D,
	0x6F, 0x71, 0x72, 0x72, 0x72, 0x72, 0x72, 0x71, 0x6F, 0x6D, 0x6B, 0x68,
	0x65, 0x62, 0x5E, 0x5A, 0x55, 0x51, 0x4C, 0x46, 0x41, 0x3B, 0x35, 0x2E,
	0x28, 0x21, 0x1A, 0x14, 0x0D, 0x00, 0xFD, 0xF9, 0xF5, 0xF2, 0xEE, 0xEB,
	0xE7, 0xE4, 0xE1, 0xDE, 0xDB, 0xD8, 0xD5, 0xD3, 0xD1, 0xCF, 0xCD, 0xCB,
	0xC9, 0xC8, 0xC7, 0xC6, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC5, 0xC6, 0xC7,
	0xC8, 0xC9, 0xCB, 0xCC, 0xCE, 0xD1, 0xD3, 0xD5, 0xD8, 0xDB, 0xDE, 0xE1,
	0xE4, 0xE7, 0xEB, 0xEE, 0xF2, 0xF5, 0xF9, 0xFD, 0x00, 0x03, 0x07, 0x0A,
	0x0E, 0x11, 0x15, 0x18, 0x1C, 0x1F, 0x22, 0x25, 0x28, 0x2A, 0x2D, 0x2F,
	0x31, 0x33, 0x35, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3B, 0x3B, 0x3B, 0x3B,
	0x3B, 0x3A, 0x39, 0x38, 0x37, 0x35, 0x34, 0x32, 0x30, 0x2D, 0x2B, 0x28,
	0x25, 0x23, 0x1F, 0x1C, 0x19, 0x16, 0x12, 0x0F, 0x0B, 0x07, 0x04, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x07, 0x0E, 0x16, 0x1D, 0x24, 0x2B, 0x32, 0x38, 0x3F,
	0x45, 0x4B, 0x50, 0x56, 0x5B, 0x5F, 0x63, 0x67, 0x6B, 0x6E, 0x71, 0x73,
	0x75, 0x76, 0x77, 0x77, 0x77, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6E, 0x6B,
	0x68, 0x64, 0x5F, 0x5B, 0x56, 0x51, 0x4B, 0x45, 0x3F, 0x39, 0x32, 0x2B,
	0x24, 0x1D, 0x16, 0x0F, 0x07, 0x00, 0xFA, 0xF2, 0xEB, 0xE4, 0xDD, 0xD6,
	0xCF, 0xC8, 0xC2, 0xBC, 0xB6, 0xB0, 0xAB, 0xA6, 0xA1, 0x9D, 0x99, 0x95,
	0x92, 0x90, 0x8D, 0x8B, 0x8A, 0x89, 0x89, 0x89, 0x89, 0x8A, 0x8B, 0x8D,
	0x8F, 0x92, 0x95, 0x98, 0x9C, 0xA0, 0xA5, 0xAA, 0xAF, 0xB5, 0xBA, 0xC1,
	0xC7, 0xCE, 0xD4, 0xDB, 0xE2, 0xEA, 0xF1, 0xF8, 0x00, 0x06, 0x0D, 0x15,
	0x1C, 0x23, 0x2A, 0x31, 0x37, 0x3E, 0x44, 0x4A, 0x50, 0x55, 0x5A, 0x5F,
	0x63, 0x67, 0x6A, 0x6E, 0x70, 0x73, 0x74, 0x76, 0x77, 0x77, 0x77, 0x77,
	0x76, 0x75, 0x73, 0x71, 0x6E, 0x6B, 0x68, 0x64, 0x60, 0x5B, 0x57, 0x51,
	0x4C, 0x46, 0x40, 0x39, 0x33, 0x2C, 0x25, 0x1E, 0x17, 0x10, 0x08, 0x01,
	0xFB, 0xF3, 0xEC, 0xE5, 0xDE, 0xD7, 0xD0, 0xC9, 0xC3, 0xBC, 0xB6, 0xB1,
	0xAB, 0xA6, 0xA2, 0x9D, 0x99, 0x96, 0x93, 0x90, 0x8E, 0x8C, 0x8A, 0x89,
	0x89, 0x89, 0x89, 0x8A, 0x8B, 0x8D, 0x8F, 0x91, 0x94, 0x98, 0x9C, 0xA0,
	0xA4, 0xA9, 0xAE, 0xB4, 0xBA, 0xC0, 0xC6, 0xCD, 0xD3, 0xDA, 0xE1, 0xE9,
	0xF0, 0xF7, 0xFF, 0x05, 0x0C, 0x14, 0x1B, 0x22, 0x29, 0x30, 0x37, 0x3D,
	0x43, 0x49, 0x4F, 0x54, 0x59, 0x5E, 0x62, 0x66, 0x6A, 0x6D, 0x70, 0x72,
	0x74, 0x76, 0x77, 0x77, 0x77, 0x77, 0x76, 0x75, 0x73, 0x71, 0x6F, 0x6C,
	0x69, 0x65, 0x61, 0x5C, 0x57, 0x52, 0x4C, 0x47, 0x41, 0x3A, 0x34, 0x2D,
	0x26, 0x1F, 0x18, 0x11, 0x09, 0x00, 0x07, 0x0F, 0x16, 0x1E, 0x25, 0x2C,
	0x33, 0x3A, 0x41, 0x47, 0x4D, 0x53, 0x59, 0x5E, 0x63, 0x67, 0x6B, 0x6F,
	0x72, 0x75, 0x77, 0x79, 0x7B, 0x7C, 0x7C, 0x7C, 0x7C, 0x7B, 0x7A, 0x78,
	0x76, 0x74, 0x70, 0x6D, 0x69, 0x65, 0x60, 0x5B, 0x56, 0x50, 0x4A, 0x44,
	0x3D, 0x37, 0x30, 0x29, 0x21, 0x1A, 0x13, 0x0B, 0x03, 0xFD, 0xF5, 0xEE,
	0xE6, 0xDF, 0xD7, 0xD0, 0xC9, 0xC3, 0xBC, 0xB6, 0xB0, 0xAA, 0xA5, 0xA0,
	0x9B, 0x97, 0x93, 0x90, 0x8C, 0x8A, 0x88, 0x86, 0x85, 0x84, 0x84, 0x84,
	0x84, 0x85, 0x87, 0x89, 0x8B, 0x8E, 0x91, 0x95, 0x99, 0x9D, 0xA2, 0xA7,
	0xAD, 0xB3, 0xB9, 0xBF, 0xC6, 0xCD, 0xD4, 0xDB, 0xE2, 0xEA, 0xF1, 0xF9,
	0x00, 0x07, 0x0F, 0x16, 0x1E, 0x25, 0x2C, 0x33, 0x3A, 0x41, 0x47, 0x4D,
	0x53, 0x59, 0x5E, 0x63, 0x67, 0x6B, 0x6F, 0x72, 0x75, 0x77, 0x79, 0x7B,
	0x7C, 0x7C, 0x7C, 0x7C, 0x7B, 0x7A, 0x78, 0x76, 0x74, 0x70, 0x6D, 0x69,
	0x65, 0x60, 0x5B, 0x56, 0x50, 0x4A, 0x44, 0x3D, 0x37, 0x30, 0x29, 0x21,
	0x1A, 0x13, 0x0B, 0x00, 0xFD, 0xFA, 0xF7, 0xF4, 0xF1, 0xEE, 0xEC, 0xE9,
	0xE6, 0xE4, 0xE1, 0xDF, 0xDD, 0xDB, 0xD9, 0xD7, 0xD5, 0xD4, 0xD2, 0xD1,
	0xD0, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xCF, 0xD0, 0xD0, 0xD1, 0xD2,
	0xD4, 0xD5, 0xD7, 0xD9, 0xDA, 0xDD, 0xDF, 0xE1, 0xE3, 0xE6, 0xE9, 0xEB,
	0xEE, 0xF1, 0xF4, 0xF7, 0xFA, 0xFD, 0x00, 0x02, 0x05, 0x08, 0x0B, 0x0E,
	0x11, 0x14, 0x17, 0x1A, 0x1C, 0x1F, 0x21, 0x23, 0x25, 0x27, 0x29, 0x2B,
	0x2C, 0x2D, 0x2F, 0x30, 0x30, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x30,
	0x30, 0x2F, 0x2E, 0x2C, 0x2B, 0x29, 0x28, 0x26, 0x24, 0x21, 0x1F, 0x1D,
	0x1A, 0x17, 0x15, 0x12, 0x0F, 0x0C, 0x09, 0x06, 0x03, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x52, 0x5B, 0x63, 0x68, 0x6D,
	0x71, 0x73, 0x76, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7C, 0x7D, 0x7D, 0x7D,
	0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E,
	0x7D, 0x7D, 0x7D, 0x7C, 0x7C, 0x7B, 0x7A, 0x79, 0x78, 0x76, 0x74, 0x71,
	0x6D, 0x68, 0x63, 0x5B, 0x52, 0x47, 0x38, 0x26, 0x0E, 0xF2, 0xDB, 0xC8,
	0xB9, 0xAE, 0xA5, 0x9D, 0x98, 0x93, 0x8F, 0x8C, 0x8A, 0x88, 0x87, 0x86,
	0x85, 0x84, 0x84, 0x83, 0x83, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82, 0x82,
	0x82, 0x82, 0x82, 0x83, 0x83, 0x83, 0x83, 0x84, 0x85, 0x85, 0x86, 0x88,
	0x89, 0x8B, 0x8E, 0x91, 0x95, 0x9A, 0xA1, 0xA9, 0xB4, 0xC1, 0xD1, 0xE6,
	0x00, 0x07, 0x0F, 0x17, 0x1E, 0x26, 0x2D, 0x34, 0x3B, 0x42, 0x49, 0x4F,
	0x55, 0x5A, 0x60, 0x64, 0x69, 0x6D, 0x71, 0x74, 0x77, 0x79, 0x7B, 0x7D,
	0x7E, 0x7E, 0x7E, 0x7E, 0x7D, 0x7C, 0x7A, 0x78, 0x75, 0x72, 0x6E, 0x6A,
	0x66, 0x61, 0x5C, 0x56, 0x50, 0x4A, 0x44, 0x3D, 0x36, 0x2F, 0x28, 0x20,
	0x19, 0x11, 0x09, 0x00, 0x06, 0x0D, 0x14, 0x1A, 0x21, 0x27, 0x2D, 0x33,
	0x39, 0x3F, 0x44, 0x49, 0x4E, 0x53, 0x57, 0x5B, 0x5E, 0x62, 0x65, 0x67,
	0x69, 0x6B, 0x6C, 0x6D, 0x6D, 0x6D, 0x6D, 0x6C, 0x6B, 0x6A, 0x68, 0x65,
	0x63, 0x5F, 0x5C, 0x58, 0x54, 0x4F, 0x4B, 0x46, 0x40, 0x3B, 0x35, 0x2F,
	0x29, 0x22, 0x1C, 0x15, 0x0F, 0x08, 0x00, 0xF9, 0xF1, 0xE9, 0xE2, 0xDA,
	0xD3, 0xCC, 0xC5, 0xBE, 0xB7, 0xB1, 0xAB, 0xA6, 0xA0, 0x9C, 0x97, 0x93,
	0x8F, 0x8C, 0x89, 0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x84,
	0x86, 0x88, 0x8B, 0x8E, 0x92, 0x96, 0x9A, 0x9F, 0xA4, 0xAA, 0xB0, 0xB6,
	0xBC, 0xC3, 0xCA, 0xD1, 0xD8, 0xE0, 0xE7, 0xEF, 0xF7, 0x40, 0x4A, 0x53,
	0x59, 0x5E, 0x63, 0x66, 0x69, 0x6B, 0x6C, 0x6E, 0x6F, 0x6F, 0x70, 0x71,
	0x71, 0x71, 0x71, 0x72, 0x72, 0x72, 0x72, 0x72, 0x72, 0x72, 0x72, 0x72,
	0x72, 0x72, 0x72, 0x72, 0x71, 0x71, 0x71, 0x70, 0x70, 0x6F, 0x6E, 0x6D,
	0x6B, 0x6A, 0x67, 0x64, 0x61, 0x5C, 0x56, 0x4F, 0x45, 0x39, 0x2A, 0x17,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x08,
	0x0C, 0x11, 0x15, 0x19, 0x1D, 0x21, 0x24, 0x28, 0x2B, 0x2F, 0x32, 0x35,
	0x37, 0x3A, 0x3C, 0x3E, 0x40, 0x41, 0x43, 0x44, 0x45, 0x45, 0x45, 0x45,
	0x45, 0x45, 0x44, 0x43, 0x42, 0x40, 0x3E, 0x3C, 0x3A, 0x37, 0x35, 0x32,
	0x2F, 0x2C, 0x28, 0x25, 0x21, 0x1D, 0x19, 0x15, 0x11, 0x0D, 0x08, 0x04,
	0x00, 0xFC, 0xF8, 0xF4, 0xF0, 0xEC, 0xE7, 0xE4, 0xE0, 0xDC, 0xD8, 0xD5,
	0xD2, 0xCE, 0xCC, 0xC9, 0xC6, 0xC4, 0xC2, 0xC0, 0xBF, 0xBD, 0xBC, 0xBB,
	0xBB, 0xBB, 0xBB, 0xBB, 0xBB, 0xBC, 0xBD, 0xBE, 0xC0, 0xC2, 0xC4, 0xC6,
	0xC8, 0xCB, 0xCE, 0xD1, 0xD4, 0xD8, 0xDB, 0xDF, 0xE3, 0xE7, 0xEB, 0xEF,
	0xF3, 0xF7, 0xFC,
#elif defined HAPTIC_RAM_24K_0832_260
    0x85, 0x08, 0x11, 0x0A, 0x68, 0x0A, 0x69, 0x0C,
	0x08, 0x0C, 0x09, 0x0D, 0x7A, 0x0D, 0x7B, 0x0D, 0xD6, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x11,
	0x19, 0x22, 0x2A, 0x32, 0x3A, 0x41, 0x49, 0x4F, 0x56, 0x5C, 0x62, 0x67,
	0x6C, 0x70, 0x74, 0x77, 0x7A, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7D, 0x7C,
	0x7A, 0x77, 0x74, 0x71, 0x6C, 0x68, 0x63, 0x5D, 0x57, 0x50, 0x4A, 0x42,
	0x3B, 0x33, 0x2B, 0x23, 0x1B, 0x12, 0x09, 0x01, 0xF9, 0xF1, 0xE8, 0xE0,
	0xD7, 0xCF, 0xC7, 0xC0, 0xB9, 0xB2, 0xAB, 0xA5, 0x9F, 0x9A, 0x95, 0x91,
	0x8D, 0x89, 0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x84, 0x86, 0x88,
	0x8B, 0x8F, 0x93, 0x97, 0x9D, 0xA2, 0xA8, 0xAF, 0xB5, 0xBC, 0xC4, 0xCC,
	0xD4, 0xDC, 0xE4, 0xED, 0xF5, 0xFE, 0x05, 0x0E, 0x17, 0x1F, 0x27, 0x2F,
	0x37, 0x3F, 0x46, 0x4D, 0x54, 0x5A, 0x60, 0x65, 0x6A, 0x6F, 0x73, 0x76,
	0x79, 0x7B, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7C, 0x7B, 0x78, 0x75, 0x72,
	0x6E, 0x69, 0x64, 0x5F, 0x59, 0x52, 0x4C, 0x45, 0x3D, 0x36, 0x2E, 0x25,
	0x1D, 0x15, 0x0C, 0x03, 0xFC, 0xF3, 0xEB, 0xE2, 0xDA, 0xD2, 0xCA, 0xC2,
	0xBB, 0xB4, 0xAD, 0xA7, 0xA1, 0x9B, 0x96, 0x92, 0x8E, 0x8A, 0x88, 0x85,
	0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x85, 0x87, 0x8A, 0x8E, 0x92, 0x96,
	0x9B, 0xA0, 0xA6, 0xAD, 0xB3, 0xBA, 0xC2, 0xC9, 0xD1, 0xD9, 0xE2, 0xEA,
	0xF3, 0xFB, 0x03, 0x0B, 0x14, 0x1D, 0x25, 0x2D, 0x35, 0x3D, 0x44, 0x4B,
	0x52, 0x58, 0x5E, 0x64, 0x69, 0x6D, 0x72, 0x75, 0x78, 0x7B, 0x7C, 0x7E,
	0x7E, 0x7E, 0x7E, 0x7D, 0x7B, 0x79, 0x76, 0x73, 0x6F, 0x6B, 0x66, 0x61,
	0x5B, 0x54, 0x4E, 0x47, 0x40, 0x38, 0x30, 0x28, 0x20, 0x17, 0x0F, 0x06,
	0xFF, 0xF6, 0xED, 0xE5, 0xDC, 0xD4, 0xCC, 0xC4, 0xBD, 0xB6, 0xAF, 0xA9,
	0xA3, 0x9D, 0x98, 0x93, 0x8F, 0x8B, 0x88, 0x86, 0x84, 0x83, 0x82, 0x82,
	0x82, 0x83, 0x84, 0x87, 0x89, 0x8C, 0x90, 0x95, 0x99, 0x9F, 0xA4, 0xAB,
	0xB1, 0xB8, 0xBF, 0xC7, 0xCF, 0xD7, 0xDF, 0xE7, 0xF0, 0x00, 0xF8, 0xEF,
	0xE7, 0xDE, 0xD6, 0xCE, 0xC6, 0xBF, 0xB7, 0xB1, 0xAA, 0xA4, 0x9E, 0x99,
	0x94, 0x90, 0x8C, 0x89, 0x86, 0x84, 0x83, 0x82, 0x82, 0x82, 0x83, 0x84,
	0x86, 0x89, 0x8C, 0x8F, 0x94, 0x98, 0x9D, 0xA3, 0xA9, 0xB0, 0xB6, 0xBE,
	0xC5, 0xCD, 0xD5, 0xDD, 0xE5, 0xEE, 0xF7, 0xFF, 0x07, 0x0F, 0x18, 0x20,
	0x29, 0x31, 0x39, 0x40, 0x47, 0x4E, 0x55, 0x5B, 0x61, 0x66, 0x6B, 0x6F,
	0x73, 0x77, 0x79, 0x7B, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7C, 0x7A, 0x78,
	0x75, 0x71, 0x6D, 0x69, 0x63, 0x5E, 0x58, 0x51, 0x4B, 0x44, 0x3C, 0x34,
	0x2C, 0x24, 0x1C, 0x13, 0x0B, 0x00, 0xFB, 0xF5, 0xEF, 0xEA, 0xE4, 0xDF,
	0xDA, 0xD4, 0xD0, 0xCB, 0xC7, 0xC3, 0xBF, 0xBB, 0xB8, 0xB5, 0xB3, 0xB1,
	0xAF, 0xAD, 0xAC, 0xAC, 0xAC, 0xAC, 0xAC, 0xAD, 0xAF, 0xB0, 0xB2, 0xB5,
	0xB8, 0xBB, 0xBE, 0xC2, 0xC6, 0xCA, 0xCF, 0xD4, 0xD9, 0xDE, 0xE3, 0xE9,
	0xEE, 0xF4, 0xFA, 0x00, 0x04, 0x0A, 0x10, 0x15, 0x1B, 0x20, 0x26, 0x2B,
	0x30, 0x34, 0x39, 0x3D, 0x41, 0x44, 0x48, 0x4A, 0x4D, 0x4F, 0x51, 0x52,
	0x54, 0x54, 0x54, 0x54, 0x54, 0x53, 0x52, 0x50, 0x4E, 0x4C, 0x49, 0x46,
	0x42, 0x3F, 0x3B, 0x36, 0x32, 0x2D, 0x28, 0x23, 0x1E, 0x18, 0x12, 0x0D,
	0x07, 0x01, 0xFC, 0xF7, 0xF1, 0xEB, 0xE6, 0xE0, 0xDB, 0xD6, 0xD1, 0xCC,
	0xC8, 0xC4, 0xC0, 0xBC, 0xB9, 0xB6, 0xB3, 0xB1, 0xAF, 0xAE, 0xAD, 0xAC,
	0xAC, 0xAC, 0xAC, 0xAD, 0xAE, 0xB0, 0xB2, 0xB4, 0xB7, 0xBA, 0xBD, 0xC1,
	0xC5, 0xC9, 0xCD, 0xD2, 0xD7, 0xDC, 0xE2, 0xE7, 0xED, 0xF2, 0xF8, 0xFE,
	0x03, 0x08, 0x0E, 0x14, 0x19, 0x1F, 0x24, 0x29, 0x2E, 0x33, 0x37, 0x3C,
	0x40, 0x43, 0x47, 0x4A, 0x4C, 0x4F, 0x50, 0x52, 0x53, 0x54, 0x54, 0x54,
	0x54, 0x53, 0x52, 0x51, 0x4F, 0x4C, 0x4A, 0x47, 0x43, 0x40, 0x3C, 0x38,
	0x33, 0x2F, 0x2A, 0x24, 0x1F, 0x1A, 0x14, 0x0F, 0x09, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x11,
	0x19, 0x22, 0x2A, 0x32, 0x3A, 0x41, 0x49, 0x4F, 0x56, 0x5C, 0x62, 0x67,
	0x6C, 0x70, 0x74, 0x77, 0x7A, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7D, 0x7C,
	0x7A, 0x77, 0x74, 0x71, 0x6C, 0x68, 0x63, 0x5D, 0x57, 0x50, 0x4A, 0x42,
	0x3B, 0x33, 0x2B, 0x23, 0x1B, 0x12, 0x09, 0x01, 0xF9, 0xF1, 0xE8, 0xE0,
	0xD7, 0xCF, 0xC7, 0xC0, 0xB9, 0xB2, 0xAB, 0xA5, 0x9F, 0x9A, 0x95, 0x91,
	0x8D, 0x89, 0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x84, 0x86, 0x88,
	0x8B, 0x8F, 0x93, 0x97, 0x9D, 0xA2, 0xA8, 0xAF, 0xB5, 0xBC, 0xC4, 0xCC,
	0xD4, 0xDC, 0xE4, 0xED, 0xF5, 0xFE, 0x05, 0x0E, 0x17, 0x1F, 0x27, 0x2F,
	0x37, 0x3F, 0x46, 0x4D, 0x54, 0x5A, 0x60, 0x65, 0x6A, 0x6F, 0x73, 0x76,
	0x79, 0x7B, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7C, 0x7B, 0x78, 0x75, 0x72,
	0x6E, 0x69, 0x64, 0x5F, 0x59, 0x52, 0x4C, 0x45, 0x3D, 0x36, 0x2E, 0x25,
	0x1D, 0x15, 0x0C, 0x03, 0xFC, 0xF3, 0xEB, 0xE2, 0xDA, 0xD2, 0xCA, 0xC2,
	0xBB, 0xB4, 0xAD, 0xA7, 0xA1, 0x9B, 0x96, 0x92, 0x8E, 0x8A, 0x88, 0x85,
	0x83, 0x82, 0x82, 0x82, 0x82, 0x83, 0x85, 0x87, 0x8A, 0x8E, 0x92, 0x96,
	0x9B, 0xA0, 0xA6, 0xAD, 0xB3, 0xBA, 0xC2, 0xC9, 0xD1, 0xD9, 0xE2, 0xEA,
	0xF3, 0x00, 0xF8, 0xEF, 0xE7, 0xDE, 0xD6, 0xCE, 0xC6, 0xBF, 0xB7, 0xB1,
	0xAA, 0xA4, 0x9E, 0x99, 0x94, 0x90, 0x8C, 0x89, 0x86, 0x84, 0x83, 0x82,
	0x82, 0x82, 0x83, 0x84, 0x86, 0x89, 0x8C, 0x8F, 0x94, 0x98, 0x9D, 0xA3,
	0xA9, 0xB0, 0xB6, 0xBE, 0xC5, 0xCD, 0xD5, 0xDD, 0xE5, 0xEE, 0xF7, 0xFF,
	0x07, 0x0F, 0x18, 0x20, 0x29, 0x31, 0x39, 0x40, 0x47, 0x4E, 0x55, 0x5B,
	0x61, 0x66, 0x6B, 0x6F, 0x73, 0x77, 0x79, 0x7B, 0x7D, 0x7E, 0x7E, 0x7E,
	0x7E, 0x7C, 0x7A, 0x78, 0x75, 0x71, 0x6D, 0x69, 0x63, 0x5E, 0x58, 0x51,
	0x4B, 0x44, 0x3C, 0x34, 0x2C, 0x24, 0x1C, 0x13, 0x0B, 0x00, 0xFA, 0xF4,
	0xED, 0xE7, 0xE1, 0xDB, 0xD5, 0xCF, 0xCA, 0xC5, 0xC0, 0xBB, 0xB7, 0xB3,
	0xAF, 0xAC, 0xAA, 0xA7, 0xA5, 0xA4, 0xA2, 0xA2, 0xA2, 0xA2, 0xA2, 0xA3,
	0xA5, 0xA7, 0xA9, 0xAC, 0xAF, 0xB2, 0xB6, 0xBB, 0xBF, 0xC4, 0xC9, 0xCE,
	0xD4, 0xDA, 0xE0, 0xE6, 0xEC, 0xF3, 0xF9, 0x00, 0x05, 0x0B, 0x12, 0x18,
	0x1E, 0x24, 0x2A, 0x30, 0x35, 0x3B, 0x3F, 0x44, 0x48, 0x4C, 0x50, 0x53,
	0x56, 0x59, 0x5B, 0x5C, 0x5D, 0x5E, 0x5E, 0x5E, 0x5E, 0x5D, 0x5B, 0x5A,
	0x57, 0x55, 0x52, 0x4E, 0x4A, 0x46, 0x42, 0x3D, 0x38, 0x32, 0x2D, 0x27,
	0x21, 0x1B, 0x15, 0x0E, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x11, 0x19, 0x22, 0x2A, 0x32,
	0x3A, 0x41, 0x49, 0x4F, 0x56, 0x5C, 0x62, 0x67, 0x6C, 0x70, 0x74, 0x77,
	0x7A, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7D, 0x7C, 0x7A, 0x77, 0x74, 0x71,
	0x6C, 0x68, 0x63, 0x5D, 0x57, 0x50, 0x4A, 0x42, 0x3B, 0x33, 0x2B, 0x23,
	0x1B, 0x12, 0x09, 0x01, 0xF9, 0xF1, 0xE8, 0xE0, 0xD7, 0xCF, 0xC7, 0xC0,
	0xB9, 0xB2, 0xAB, 0xA5, 0x9F, 0x9A, 0x95, 0x91, 0x8D, 0x89, 0x87, 0x85,
	0x83, 0x82, 0x82, 0x82, 0x82, 0x84, 0x86, 0x88, 0x8B, 0x8F, 0x93, 0x97,
	0x9D, 0xA2, 0xA8, 0xAF, 0xB5, 0xBC, 0xC4, 0xCC, 0xD4, 0xDC, 0xE4, 0xED,
	0xF5, 0xFE, 0x05, 0x0E, 0x17, 0x1F, 0x27, 0x2F, 0x37, 0x3F, 0x46, 0x4D,
	0x54, 0x5A, 0x60, 0x65, 0x6A, 0x6F, 0x73, 0x76, 0x79, 0x7B, 0x7D, 0x7E,
	0x7E, 0x7E, 0x7E, 0x7C, 0x7B, 0x78, 0x75, 0x72, 0x6E, 0x69, 0x64, 0x5F,
	0x59, 0x52, 0x4C, 0x45, 0x3D, 0x36, 0x2E, 0x25, 0x1D, 0x15, 0x0C, 0x00,
	0x08, 0x11, 0x19, 0x22, 0x2A, 0x32, 0x3A, 0x41, 0x49, 0x4F, 0x56, 0x5C,
	0x62, 0x67, 0x6C, 0x70, 0x74, 0x77, 0x7A, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E,
	0x7D, 0x7C, 0x7A, 0x77, 0x74, 0x71, 0x6C, 0x68, 0x63, 0x5D, 0x57, 0x50,
	0x4A, 0x42, 0x3B, 0x33, 0x2B, 0x23, 0x1B, 0x12, 0x09, 0x01, 0xF9, 0xF1,
	0xE8, 0xE0, 0xD7, 0xCF, 0xC7, 0xC0, 0xB9, 0xB2, 0xAB, 0xA5, 0x9F, 0x9A,
	0x95, 0x91, 0x8D, 0x89, 0x87, 0x85, 0x83, 0x82, 0x82, 0x82, 0x82, 0x84,
	0x86, 0x88, 0x8B, 0x8F, 0x93, 0x97, 0x9D, 0xA2, 0xA8, 0xAF, 0xB5, 0xBC,
	0xC4, 0xCC, 0xD4, 0xDC, 0xE4, 0xED, 0xF5, 0x00, 0x02, 0x05, 0x08, 0x0A,
	0x0D, 0x0F, 0x12, 0x14, 0x17, 0x19, 0x1B, 0x1D, 0x1E, 0x20, 0x22, 0x23,
	0x24, 0x25, 0x26, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x27, 0x26, 0x25,
	0x24, 0x23, 0x22, 0x20, 0x1F, 0x1D, 0x1B, 0x19, 0x17, 0x15, 0x12, 0x10,
	0x0D, 0x0B, 0x08, 0x05, 0x03, 0x00, 0xFE, 0xFB, 0xF9, 0xF6, 0xF4, 0xF1,
	0xEF, 0xEC, 0xEA, 0xE8, 0xE6, 0xE4, 0xE2, 0xE0, 0xDF, 0xDD, 0xDC, 0xDB,
	0xDA, 0xD9, 0xD9, 0xD9, 0xD9, 0xD9, 0xD9, 0xD9, 0xDA, 0xDB, 0xDC, 0xDD,
	0xDE, 0xDF, 0xE1, 0xE3, 0xE5, 0xE7, 0xE9, 0xEB, 0xED, 0xF0, 0xF2, 0xF5,
	0xF8, 0xFA, 0xFD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x05, 0x0A, 0x0F, 0x14, 0x19, 0x1D, 0x22, 0x26,
	0x2B, 0x2F, 0x33, 0x36, 0x3A, 0x3D, 0x3F, 0x42, 0x44, 0x46, 0x48, 0x49,
	0x4A, 0x4A, 0x4A, 0x4A, 0x4A, 0x49, 0x48, 0x46, 0x44, 0x42, 0x40, 0x3D,
	0x3A, 0x37, 0x33, 0x2F, 0x2B, 0x27, 0x23, 0x1E, 0x19, 0x14, 0x0F, 0x0A,
	0x05, 0x00, 0xFC, 0xF7, 0xF2, 0xED, 0xE8, 0xE3, 0xDF, 0xDA, 0xD6, 0xD2,
	0xCE, 0xCA, 0xC7, 0xC4, 0xC1, 0xBE, 0xBC, 0xBA, 0xB9, 0xB7, 0xB6, 0xB6,
	0xB6, 0xB6, 0xB6, 0xB7, 0xB8, 0xB9, 0xBB, 0xBD, 0xC0, 0xC2, 0xC5, 0xC9,
	0xCC, 0xD0, 0xD4, 0xD8, 0xDD, 0xE1, 0xE6, 0xEB, 0xF0, 0xF5, 0xFA,
#else
0x85,0x08,0x11,0x0a,0xdc,0x0a,0xdd,0x0c,0xf0,0x0c,0xf1,0x0e,0x4c,0x0e,0x4d,0x0e,
0xb2,0x00,0x07,0x0e,0x16,0x1d,0x24,0x2b,0x32,0x38,0x3f,0x45,0x4b,0x50,0x56,0x5b,
0x5f,0x63,0x67,0x6b,0x6e,0x71,0x73,0x75,0x76,0x77,0x77,0x77,0x77,0x76,0x75,0x73,
0x71,0x6e,0x6b,0x68,0x64,0x5f,0x5b,0x56,0x51,0x4b,0x45,0x3f,0x39,0x32,0x2b,0x24,
0x1d,0x16,0x0f,0x07,0x00,0xfa,0xf2,0xeb,0xe4,0xdd,0xd6,0xcf,0xc8,0xc2,0xbc,0xb6,
0xb0,0xab,0xa6,0xa1,0x9d,0x99,0x95,0x92,0x90,0x8d,0x8b,0x8a,0x89,0x89,0x89,0x89,
0x8a,0x8b,0x8d,0x8f,0x92,0x95,0x98,0x9c,0xa0,0xa5,0xaa,0xaf,0xb5,0xba,0xc1,0xc7,
0xce,0xd4,0xdb,0xe2,0xea,0xf1,0xf8,0x00,0x06,0x0d,0x15,0x1c,0x23,0x2a,0x31,0x37,
0x3e,0x44,0x4a,0x50,0x55,0x5a,0x5f,0x63,0x67,0x6a,0x6e,0x70,0x73,0x74,0x76,0x77,
0x77,0x77,0x77,0x76,0x75,0x73,0x71,0x6e,0x6b,0x68,0x64,0x60,0x5b,0x57,0x51,0x4c,
0x46,0x40,0x39,0x33,0x2c,0x25,0x1e,0x17,0x10,0x08,0x01,0xfb,0xf3,0xec,0xe5,0xde,
0xd7,0xd0,0xc9,0xc3,0xbc,0xb6,0xb1,0xab,0xa6,0xa2,0x9d,0x99,0x96,0x93,0x90,0x8e,
0x8c,0x8a,0x89,0x89,0x89,0x89,0x8a,0x8b,0x8d,0x8f,0x91,0x94,0x98,0x9c,0xa0,0xa4,
0xa9,0xae,0xb4,0xba,0xc0,0xc6,0xcd,0xd3,0xda,0xe1,0xe9,0xf0,0xf7,0xff,0x05,0x0c,
0x14,0x1b,0x22,0x29,0x30,0x37,0x3d,0x43,0x49,0x4f,0x54,0x59,0x5e,0x62,0x66,0x6a,
0x6d,0x70,0x72,0x74,0x76,0x77,0x77,0x77,0x77,0x76,0x75,0x73,0x71,0x6f,0x6c,0x69,
0x65,0x61,0x5c,0x57,0x52,0x4c,0x47,0x41,0x3a,0x34,0x2d,0x26,0x1f,0x18,0x11,0x09,
0x02,0xfb,0xf4,0xed,0xe6,0xde,0xd7,0xd1,0xca,0xc3,0xbd,0xb7,0xb1,0xac,0xa7,0xa2,
0x9e,0x9a,0x96,0x93,0x90,0x8e,0x8c,0x8a,0x89,0x89,0x89,0x89,0x8a,0x8b,0x8c,0x8e,
0x91,0x94,0x97,0x9b,0x9f,0xa4,0xa8,0xae,0xb3,0xb9,0xbf,0xc5,0xcc,0xd3,0xd9,0xe1,
0xe8,0xef,0xf6,0x00,0xfa,0xf3,0xec,0xe5,0xde,0xd8,0xd1,0xcb,0xc5,0xbf,0xb9,0xb4,
0xaf,0xaa,0xa6,0xa2,0x9e,0x9a,0x97,0x95,0x92,0x91,0x8f,0x8e,0x8e,0x8e,0x8e,0x8e,
0x90,0x91,0x93,0x95,0x98,0x9b,0x9f,0xa3,0xa7,0xab,0xb0,0xb5,0xbb,0xc0,0xc6,0xcd,
0xd3,0xd9,0xe0,0xe7,0xee,0xf4,0xfb,0x01,0x08,0x0f,0x16,0x1d,0x24,0x2a,0x31,0x37,
0x3d,0x43,0x48,0x4e,0x53,0x57,0x5c,0x60,0x63,0x67,0x69,0x6c,0x6e,0x70,0x71,0x72,
0x72,0x72,0x72,0x71,0x70,0x6e,0x6c,0x6a,0x67,0x64,0x60,0x5c,0x58,0x53,0x4e,0x49,
0x44,0x3e,0x38,0x32,0x2b,0x25,0x1e,0x17,0x11,0x0a,0x03,0xfd,0xf6,0xef,0xe8,0xe1,
0xda,0xd4,0xce,0xc7,0xc1,0xbc,0xb6,0xb1,0xac,0xa8,0xa3,0x9f,0x9c,0x99,0x96,0x93,
0x91,0x90,0x8f,0x8e,0x8e,0x8e,0x8e,0x8f,0x90,0x92,0x94,0x97,0x9a,0x9d,0xa1,0xa5,
0xa9,0xae,0xb3,0xb8,0xbe,0xc4,0xca,0xd0,0xd6,0xdd,0xe4,0xea,0xf1,0xf8,0xff,0x05,
0x0c,0x13,0x1a,0x21,0x27,0x2e,0x34,0x3a,0x40,0x46,0x4b,0x50,0x55,0x5a,0x5e,0x62,
0x65,0x68,0x6b,0x6d,0x6f,0x71,0x72,0x72,0x72,0x72,0x72,0x71,0x6f,0x6d,0x6b,0x68,
0x65,0x62,0x5e,0x5a,0x55,0x51,0x4c,0x46,0x41,0x3b,0x35,0x2e,0x28,0x21,0x1a,0x14,
0x0d,0x00,0xfd,0xf9,0xf5,0xf2,0xee,0xeb,0xe7,0xe4,0xe1,0xde,0xdb,0xd8,0xd5,0xd3,
0xd1,0xcf,0xcd,0xcb,0xc9,0xc8,0xc7,0xc6,0xc5,0xc5,0xc5,0xc5,0xc5,0xc5,0xc6,0xc7,
0xc8,0xc9,0xcb,0xcc,0xce,0xd1,0xd3,0xd5,0xd8,0xdb,0xde,0xe1,0xe4,0xe7,0xeb,0xee,
0xf2,0xf5,0xf9,0xfd,0x00,0x03,0x07,0x0a,0x0e,0x11,0x15,0x18,0x1c,0x1f,0x22,0x25,
0x28,0x2a,0x2d,0x2f,0x31,0x33,0x35,0x37,0x38,0x39,0x3a,0x3b,0x3b,0x3b,0x3b,0x3b,
0x3b,0x3a,0x39,0x38,0x37,0x35,0x34,0x32,0x30,0x2d,0x2b,0x28,0x25,0x23,0x1f,0x1c,
0x19,0x16,0x12,0x0f,0x0b,0x07,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x07,0x0e,0x16,0x1d,0x24,0x2b,0x32,0x38,0x3f,0x45,0x4b,0x50,0x56,
0x5b,0x5f,0x63,0x67,0x6b,0x6e,0x71,0x73,0x75,0x76,0x77,0x77,0x77,0x77,0x76,0x75,
0x73,0x71,0x6e,0x6b,0x68,0x64,0x5f,0x5b,0x56,0x51,0x4b,0x45,0x3f,0x39,0x32,0x2b,
0x24,0x1d,0x16,0x0f,0x07,0x00,0xfa,0xf2,0xeb,0xe4,0xdd,0xd6,0xcf,0xc8,0xc2,0xbc,
0xb6,0xb0,0xab,0xa6,0xa1,0x9d,0x99,0x95,0x92,0x90,0x8d,0x8b,0x8a,0x89,0x89,0x89,
0x89,0x8a,0x8b,0x8d,0x8f,0x92,0x95,0x98,0x9c,0xa0,0xa5,0xaa,0xaf,0xb5,0xba,0xc1,
0xc7,0xce,0xd4,0xdb,0xe2,0xea,0xf1,0xf8,0x00,0x06,0x0d,0x15,0x1c,0x23,0x2a,0x31,
0x37,0x3e,0x44,0x4a,0x50,0x55,0x5a,0x5f,0x63,0x67,0x6a,0x6e,0x70,0x73,0x74,0x76,
0x77,0x77,0x77,0x77,0x76,0x75,0x73,0x71,0x6e,0x6b,0x68,0x64,0x60,0x5b,0x57,0x51,
0x4c,0x46,0x40,0x39,0x33,0x2c,0x25,0x1e,0x17,0x10,0x08,0x01,0xfb,0xf3,0xec,0xe5,
0xde,0xd7,0xd0,0xc9,0xc3,0xbc,0xb6,0xb1,0xab,0xa6,0xa2,0x9d,0x99,0x96,0x93,0x90,
0x8e,0x8c,0x8a,0x89,0x89,0x89,0x89,0x8a,0x8b,0x8d,0x8f,0x91,0x94,0x98,0x9c,0xa0,
0xa4,0xa9,0xae,0xb4,0xba,0xc0,0xc6,0xcd,0xd3,0xda,0xe1,0xe9,0xf0,0xf7,0xff,0x05,
0x0c,0x14,0x1b,0x22,0x29,0x30,0x37,0x3d,0x43,0x49,0x4f,0x54,0x59,0x5e,0x62,0x66,
0x6a,0x6d,0x70,0x72,0x74,0x76,0x77,0x77,0x77,0x77,0x76,0x75,0x73,0x71,0x6f,0x6c,
0x69,0x65,0x61,0x5c,0x57,0x52,0x4c,0x47,0x41,0x3a,0x34,0x2d,0x26,0x1f,0x18,0x11,
0x09,0x00,0x07,0x0f,0x16,0x1e,0x25,0x2c,0x33,0x3a,0x41,0x47,0x4d,0x53,0x59,0x5e,
0x63,0x67,0x6b,0x6f,0x72,0x75,0x77,0x79,0x7b,0x7c,0x7c,0x7c,0x7c,0x7b,0x7a,0x78,
0x76,0x74,0x70,0x6d,0x69,0x65,0x60,0x5b,0x56,0x50,0x4a,0x44,0x3d,0x37,0x30,0x29,
0x21,0x1a,0x13,0x0b,0x03,0xfd,0xf5,0xee,0xe6,0xdf,0xd7,0xd0,0xc9,0xc3,0xbc,0xb6,
0xb0,0xaa,0xa5,0xa0,0x9b,0x97,0x93,0x90,0x8c,0x8a,0x88,0x86,0x85,0x84,0x84,0x84,
0x84,0x85,0x87,0x89,0x8b,0x8e,0x91,0x95,0x99,0x9d,0xa2,0xa7,0xad,0xb3,0xb9,0xbf,
0xc6,0xcd,0xd4,0xdb,0xe2,0xea,0xf1,0xf9,0x00,0x07,0x0f,0x16,0x1e,0x25,0x2c,0x33,
0x3a,0x41,0x47,0x4d,0x53,0x59,0x5e,0x63,0x67,0x6b,0x6f,0x72,0x75,0x77,0x79,0x7b,
0x7c,0x7c,0x7c,0x7c,0x7b,0x7a,0x78,0x76,0x74,0x70,0x6d,0x69,0x65,0x60,0x5b,0x56,
0x50,0x4a,0x44,0x3d,0x37,0x30,0x29,0x21,0x1a,0x13,0x0b,0x00,0xfd,0xfa,0xf7,0xf4,
0xf1,0xee,0xec,0xe9,0xe6,0xe4,0xe1,0xdf,0xdd,0xdb,0xd9,0xd7,0xd5,0xd4,0xd2,0xd1,
0xd0,0xd0,0xcf,0xcf,0xcf,0xcf,0xcf,0xcf,0xd0,0xd0,0xd1,0xd2,0xd4,0xd5,0xd7,0xd9,
0xda,0xdd,0xdf,0xe1,0xe3,0xe6,0xe9,0xeb,0xee,0xf1,0xf4,0xf7,0xfa,0xfd,0x00,0x02,
0x05,0x08,0x0b,0x0e,0x11,0x14,0x17,0x1a,0x1c,0x1f,0x21,0x23,0x25,0x27,0x29,0x2b,
0x2c,0x2d,0x2f,0x30,0x30,0x31,0x31,0x31,0x31,0x31,0x31,0x30,0x30,0x2f,0x2e,0x2c,
0x2b,0x29,0x28,0x26,0x24,0x21,0x1f,0x1d,0x1a,0x17,0x15,0x12,0x0f,0x0c,0x09,0x06,
0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x46,0x52,0x5b,0x63,0x68,0x6d,0x71,0x73,0x76,0x78,
0x79,0x7a,0x7b,0x7c,0x7c,0x7d,0x7d,0x7d,0x7e,0x7e,0x7e,0x7e,0x7e,0x7e,0x7e,0x7e,
0x7e,0x7e,0x7e,0x7e,0x7d,0x7d,0x7d,0x7c,0x7c,0x7b,0x7a,0x79,0x78,0x76,0x74,0x71,
0x6d,0x68,0x63,0x5b,0x52,0x47,0x38,0x26,0x0e,0xf2,0xdb,0xc8,0xb9,0xae,0xa5,0x9d,
0x98,0x93,0x8f,0x8c,0x8a,0x88,0x87,0x86,0x85,0x84,0x84,0x83,0x83,0x83,0x82,0x82,
0x82,0x82,0x82,0x82,0x82,0x82,0x82,0x83,0x83,0x83,0x83,0x84,0x85,0x85,0x86,0x88,
0x89,0x8b,0x8e,0x91,0x95,0x9a,0xa1,0xa9,0xb4,0xc1,0xd1,0xe6,0x00,0x07,0x0f,0x17,
0x1e,0x26,0x2d,0x34,0x3b,0x42,0x49,0x4f,0x55,0x5a,0x60,0x64,0x69,0x6d,0x71,0x74,
0x77,0x79,0x7b,0x7d,0x7e,0x7e,0x7e,0x7e,0x7d,0x7c,0x7a,0x78,0x75,0x72,0x6e,0x6a,
0x66,0x61,0x5c,0x56,0x50,0x4a,0x44,0x3d,0x36,0x2f,0x28,0x20,0x19,0x11,0x09,0x00,
0x06,0x0d,0x14,0x1a,0x21,0x27,0x2d,0x33,0x39,0x3f,0x44,0x49,0x4e,0x53,0x57,0x5b,
0x5e,0x62,0x65,0x67,0x69,0x6b,0x6c,0x6d,0x6d,0x6d,0x6d,0x6c,0x6b,0x6a,0x68,0x65,
0x63,0x5f,0x5c,0x58,0x54,0x4f,0x4b,0x46,0x40,0x3b,0x35,0x2f,0x29,0x22,0x1c,0x15,
0x0f,0x08,0x00,0xf9,0xf1,0xe9,0xe2,0xda,0xd3,0xcc,0xc5,0xbe,0xb7,0xb1,0xab,0xa6,
0xa0,0x9c,0x97,0x93,0x8f,0x8c,0x89,0x87,0x85,0x83,0x82,0x82,0x82,0x82,0x83,0x84,
0x86,0x88,0x8b,0x8e,0x92,0x96,0x9a,0x9f,0xa4,0xaa,0xb0,0xb6,0xbc,0xc3,0xca,0xd1,
0xd8,0xe0,0xe7,0xef,0xf7,0x40,0x4a,0x53,0x59,0x5e,0x63,0x66,0x69,0x6b,0x6c,0x6e,
0x6f,0x6f,0x70,0x71,0x71,0x71,0x71,0x72,0x72,0x72,0x72,0x72,0x72,0x72,0x72,0x72,
0x72,0x72,0x72,0x72,0x71,0x71,0x71,0x70,0x70,0x6f,0x6e,0x6d,0x6b,0x6a,0x67,0x64,
0x61,0x5c,0x56,0x4f,0x45,0x39,0x2a,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x08,
0x0c,0x11,0x15,0x19,0x1d,0x21,0x24,0x28,0x2b,0x2f,0x32,0x35,0x37,0x3a,0x3c,0x3e,
0x40,0x41,0x43,0x44,0x45,0x45,0x45,0x45,0x45,0x45,0x44,0x43,0x42,0x40,0x3e,0x3c,
0x3a,0x37,0x35,0x32,0x2f,0x2c,0x28,0x25,0x21,0x1d,0x19,0x15,0x11,0x0d,0x08,0x04,
0x00,0xfc,0xf8,0xf4,0xf0,0xec,0xe7,0xe4,0xe0,0xdc,0xd8,0xd5,0xd2,0xce,0xcc,0xc9,
0xc6,0xc4,0xc2,0xc0,0xbf,0xbd,0xbc,0xbb,0xbb,0xbb,0xbb,0xbb,0xbb,0xbc,0xbd,0xbe,
0xc0,0xc2,0xc4,0xc6,0xc8,0xcb,0xce,0xd1,0xd4,0xd8,0xdb,0xdf,0xe3,0xe7,0xeb,0xef,
0xf3,0xf7,0xfc,
#endif
};
uint32_t aw862x_ram_len = sizeof(aw862x_ram_data);
#endif

#if defined (AW862XX_DRIVER) || defined (AW8623X_DRIVER)
uint8_t aw862xx_ram_data[] = {
#if defined HAPTIC_RAM_12K_0809_170
    0x55, 0x08, 0x11, 0x08, 0x8D, 0x08, 0x8E, 0x09,
	0x3D, 0x09, 0x3E, 0x09, 0x8D, 0x09, 0x8E, 0x09, 0xD4, 0x01, 0x01, 0x01,
	0x02, 0x02, 0x03, 0x04, 0x06, 0x08, 0x0A, 0x0D, 0x11, 0x16, 0x1D, 0x26,
	0x31, 0x40, 0x4C, 0x55, 0x5A, 0x5D, 0x5C, 0x5B, 0x58, 0x54, 0x4E, 0x48,
	0x41, 0x39, 0x31, 0x28, 0x1F, 0x16, 0x0C, 0x02, 0xF7, 0xEB, 0xDD, 0xCD,
	0xB9, 0xA2, 0x90, 0x85, 0x81, 0x81, 0x84, 0x8B, 0x95, 0xA2, 0xB0, 0xC0,
	0xD1, 0xE2, 0xF4, 0x05, 0x15, 0x25, 0x33, 0x3F, 0x49, 0x51, 0x56, 0x59,
	0x58, 0x54, 0x4E, 0x48, 0x40, 0x36, 0x2C, 0x20, 0x13, 0x05, 0xF7, 0xE8,
	0xD8, 0xC9, 0xBB, 0xAD, 0xA1, 0x96, 0x8D, 0x86, 0x83, 0x83, 0x88, 0x91,
	0xA1, 0xB6, 0xC8, 0xD6, 0xE2, 0xED, 0xF6, 0xFD, 0x05, 0x0B, 0x12, 0x18,
	0x1D, 0x22, 0x27, 0x2B, 0x2F, 0x32, 0x34, 0x36, 0x36, 0x35, 0x32, 0x2D,
	0x26, 0x1D, 0x16, 0x11, 0x0C, 0x09, 0x07, 0x05, 0x04, 0x03, 0x02, 0x01,
	0x01, 0x01, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03, 0x04,
	0x06, 0x08, 0x0B, 0x0E, 0x13, 0x1A, 0x22, 0x2D, 0x37, 0x3D, 0x40, 0x41,
	0x3F, 0x3A, 0x32, 0x27, 0x16, 0x00, 0xE9, 0xD6, 0xC9, 0xBE, 0xB6, 0xB0,
	0xAB, 0xA8, 0xA5, 0xA3, 0xA2, 0xA1, 0xA0, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E,
	0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0xA0, 0xA0, 0xA1, 0xA3, 0xA5, 0xA7, 0xAA,
	0xAE, 0xB3, 0xBB, 0xC4, 0xD0, 0xE1, 0xF6, 0x0F, 0x23, 0x33, 0x3E, 0x47,
	0x4D, 0x52, 0x56, 0x58, 0x5A, 0x5B, 0x5C, 0x5C, 0x5B, 0x5A, 0x58, 0x55,
	0x51, 0x4C, 0x45, 0x3B, 0x2F, 0x1E, 0x08, 0xF2, 0xE2, 0xD7, 0xD0, 0xCD,
	0xCE, 0xD3, 0xDC, 0xE9, 0xFD, 0x14, 0x27, 0x36, 0x41, 0x49, 0x4F, 0x54,
	0x57, 0x5A, 0x5C, 0x5D, 0x5E, 0x5F, 0x5F, 0x5F, 0x5E, 0x5E, 0x5C, 0x5B,
	0x59, 0x55, 0x51, 0x4C, 0x45, 0x3B, 0x2E, 0x1D, 0x09, 0xFB, 0xEF, 0xE6,
	0xDF, 0xDA, 0xD6, 0xD3, 0xD1, 0xCF, 0xCE, 0xCD, 0xCC, 0xCC, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCC, 0xCC, 0xCD, 0xCE,
	0xCF, 0xD1, 0xD3, 0xD6, 0xDA, 0xDF, 0xE6, 0xED, 0xF1, 0xF5, 0xF8, 0xFA,
	0xFC, 0xFD, 0xFE, 0xFE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x01, 0x02, 0x03, 0x04, 0x05, 0x07, 0x09, 0x0C, 0x10,
	0x16, 0x1C, 0x25, 0x31, 0x41, 0x4D, 0x51, 0x4F, 0x45, 0x33, 0x17, 0xF1,
	0xCD, 0xB2, 0x9E, 0x90, 0x87, 0x82, 0x81, 0x83, 0x8A, 0x95, 0xA5, 0xBC,
	0xDB, 0x03, 0x29, 0x45, 0x59, 0x66, 0x6E, 0x71, 0x6F, 0x68, 0x5C, 0x49,
	0x2E, 0x11, 0xFC, 0xEB, 0xDE, 0xD5, 0xCE, 0xC9, 0xC6, 0xC5, 0xC5, 0xC6,
	0xC9, 0xCE, 0xD4, 0xDD, 0xE6, 0xED, 0xF2, 0xF6, 0xF8, 0xFB, 0xFC, 0xFD,
	0xFE, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x04, 0x08, 0x0D, 0x11, 0x15,
	0x19, 0x1D, 0x20, 0x23, 0x26, 0x29, 0x2B, 0x2D, 0x2F, 0x30, 0x31, 0x31,
	0x31, 0x31, 0x30, 0x2F, 0x2E, 0x2C, 0x2A, 0x27, 0x24, 0x21, 0x1E, 0x1A,
	0x16, 0x12, 0x0E, 0x0A, 0x05, 0x01, 0xFD, 0xF9, 0xF5, 0xF0, 0xEC, 0xE8,
	0xE4, 0xE1, 0xDE, 0xDA, 0xD8, 0xD5, 0xD3, 0xD2, 0xD0, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xD0, 0xD2, 0xD3, 0xD6, 0xD8, 0xDB, 0xDE, 0xE1, 0xE5, 0xE9,
	0xED, 0xF1, 0xF5, 0xF9, 0xFE,
#elif defined HAPTIC_RAM_12K_0815_170
    0x55, 0x08, 0x11, 0x08, 0xCF, 0x08, 0xD0, 0x09,
	0x57, 0x09, 0x58, 0x09, 0xD3, 0x09, 0xD4, 0x0A, 0x19, 0x00, 0x00, 0x01,
	0x01, 0x01, 0x02, 0x02, 0x03, 0x05, 0x06, 0x08, 0x0B, 0x0F, 0x14, 0x1A,
	0x22, 0x2D, 0x3B, 0x4C, 0x59, 0x63, 0x6B, 0x70, 0x75, 0x78, 0x7A, 0x7C,
	0x7E, 0x7E, 0x7F, 0x7F, 0x7F, 0x7E, 0x7D, 0x7C, 0x7A, 0x78, 0x74, 0x70,
	0x6A, 0x62, 0x58, 0x4B, 0x39, 0x22, 0x04, 0xE5, 0xCC, 0xB9, 0xAB, 0xA0,
	0x98, 0x91, 0x8C, 0x89, 0x86, 0x84, 0x83, 0x82, 0x81, 0x81, 0x81, 0x81,
	0x81, 0x82, 0x83, 0x85, 0x87, 0x8A, 0x8F, 0x94, 0x9B, 0xA5, 0xB1, 0xC2,
	0xD7, 0xF3, 0x14, 0x2E, 0x42, 0x51, 0x5D, 0x65, 0x6B, 0x70, 0x73, 0x75,
	0x76, 0x75, 0x74, 0x72, 0x6E, 0x68, 0x61, 0x57, 0x4A, 0x39, 0x21, 0x03,
	0xE8, 0xD3, 0xC3, 0xB7, 0xAE, 0xA8, 0xA5, 0xA3, 0xA2, 0xA4, 0xA7, 0xAD,
	0xB5, 0xC0, 0xCF, 0xE3, 0xFE, 0x1A, 0x30, 0x41, 0x4D, 0x57, 0x5E, 0x64,
	0x68, 0x6B, 0x6D, 0x6F, 0x70, 0x71, 0x71, 0x71, 0x70, 0x6F, 0x6E, 0x6C,
	0x69, 0x65, 0x60, 0x59, 0x51, 0x45, 0x36, 0x22, 0x07, 0xF0, 0xDD, 0xCE,
	0xC4, 0xBB, 0xB5, 0xB0, 0xAD, 0xAA, 0xA8, 0xA6, 0xA5, 0xA4, 0xA3, 0xA3,
	0xA3, 0xA3, 0xA3, 0xA3, 0xA3, 0xA4, 0xA5, 0xA6, 0xA8, 0xAA, 0xAD, 0xB0,
	0xB5, 0xBC, 0xC4, 0xCF, 0xDB, 0xE4, 0xEB, 0xF0, 0xF4, 0xF7, 0xF9, 0xFB,
	0xFC, 0xFD, 0xFE, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x01, 0x01,
	0x02, 0x03, 0x05, 0x08, 0x0B, 0x11, 0x18, 0x23, 0x33, 0x48, 0x59, 0x65,
	0x6D, 0x73, 0x77, 0x79, 0x7B, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7D, 0x7C,
	0x7B, 0x78, 0x75, 0x71, 0x6A, 0x61, 0x53, 0x3F, 0x23, 0xFB, 0xD5, 0xBB,
	0xA9, 0x9D, 0x94, 0x8E, 0x8A, 0x87, 0x85, 0x83, 0x82, 0x82, 0x81, 0x81,
	0x81, 0x81, 0x81, 0x82, 0x82, 0x83, 0x84, 0x86, 0x89, 0x8D, 0x92, 0x9A,
	0xA6, 0xB7, 0xCF, 0xF1, 0x15, 0x2B, 0x36, 0x38, 0x2F, 0x1C, 0xFC, 0xD6,
	0xBC, 0xAA, 0x9D, 0x94, 0x8E, 0x8A, 0x87, 0x85, 0x83, 0x82, 0x82, 0x81,
	0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0x82, 0x82, 0x83,
	0x85, 0x87, 0x89, 0x8D, 0x93, 0x9C, 0xA8, 0xBA, 0xD3, 0xF8, 0x1E, 0x3A,
	0x4D, 0x59, 0x62, 0x67, 0x6A, 0x6A, 0x69, 0x65, 0x5E, 0x53, 0x43, 0x2F,
	0x21, 0x17, 0x10, 0x0B, 0x07, 0x05, 0x03, 0x02, 0x01, 0x01, 0x00, 0x00,
	0x00, 0x00, 0xFF, 0xFF, 0xFE, 0xFD, 0xFB, 0xF9, 0xF5, 0xF2, 0xF1, 0xF0,
	0xEF, 0xF0, 0xF1, 0xF3, 0xF6, 0xFB, 0x01, 0x0B, 0x1A, 0x2D, 0x38, 0x3D,
	0x3B, 0x34, 0x26, 0x0E, 0xF1, 0xDA, 0xCB, 0xC0, 0xB8, 0xB3, 0xB0, 0xAD,
	0xAB, 0xAA, 0xA9, 0xA9, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA8, 0xA9,
	0xA9, 0xAA, 0xAC, 0xAE, 0xB0, 0xB4, 0xBA, 0xC2, 0xCE, 0xDE, 0xF5, 0x0C,
	0x2A, 0x43, 0x52, 0x5B, 0x60, 0x63, 0x65, 0x66, 0x65, 0x64, 0x61, 0x5C,
	0x55, 0x4B, 0x3C, 0x27, 0x08, 0xDE, 0xBC, 0xA5, 0x95, 0x8B, 0x85, 0x81,
	0x81, 0x82, 0x87, 0x8F, 0x9C, 0xAF, 0xCA, 0xF3, 0x1D, 0x3C, 0x51, 0x5F,
	0x69, 0x6F, 0x72, 0x73, 0x73, 0x70, 0x6C, 0x68, 0x65, 0x62, 0x5D, 0x58,
	0x50, 0x46, 0x37, 0x26, 0x1A, 0x12, 0x0C, 0x08, 0x06, 0x04, 0x03, 0x02,
	0x01, 0x01, 0x00, 0x00, 0x02, 0x06, 0x0B, 0x0F, 0x13, 0x17, 0x1B, 0x1E,
	0x22, 0x25, 0x28, 0x2A, 0x2C, 0x2E, 0x30, 0x31, 0x31, 0x31, 0x31, 0x31,
	0x30, 0x2F, 0x2D, 0x2B, 0x28, 0x26, 0x23, 0x1F, 0x1C, 0x18, 0x14, 0x10,
	0x0C, 0x07, 0x03, 0x00, 0xFB, 0xF7, 0xF2, 0xEE, 0xEA, 0xE6, 0xE3, 0xDF,
	0xDC, 0xD9, 0xD6, 0xD4, 0xD2, 0xD1, 0xD0, 0xCF, 0xCF, 0xCF, 0xCF, 0xD0,
	0xD1, 0xD3, 0xD4, 0xD7, 0xD9, 0xDC, 0xE0, 0xE3, 0xE7, 0xEB, 0xEF, 0xF3,
	0xF7, 0xFC,
#elif defined HAPTIC_RAM_12K_9595_170
    0x55, 0x08, 0x11, 0x08, 0x8D, 0x08, 0x8E, 0x09,
	0x3D, 0x09, 0x3E, 0x09, 0x8D, 0x09, 0x8E, 0x09, 0xD4, 0x01, 0x01, 0x01,
	0x02, 0x02, 0x03, 0x04, 0x06, 0x08, 0x0A, 0x0D, 0x11, 0x16, 0x1D, 0x26,
	0x31, 0x40, 0x4C, 0x55, 0x5A, 0x5D, 0x5C, 0x5B, 0x58, 0x54, 0x4E, 0x48,
	0x41, 0x39, 0x31, 0x28, 0x1F, 0x16, 0x0C, 0x02, 0xF7, 0xEB, 0xDD, 0xCD,
	0xB9, 0xA2, 0x90, 0x85, 0x81, 0x81, 0x84, 0x8B, 0x95, 0xA2, 0xB0, 0xC0,
	0xD1, 0xE2, 0xF4, 0x05, 0x15, 0x25, 0x33, 0x3F, 0x49, 0x51, 0x56, 0x59,
	0x58, 0x54, 0x4E, 0x48, 0x40, 0x36, 0x2C, 0x20, 0x13, 0x05, 0xF7, 0xE8,
	0xD8, 0xC9, 0xBB, 0xAD, 0xA1, 0x96, 0x8D, 0x86, 0x83, 0x83, 0x88, 0x91,
	0xA1, 0xB6, 0xC8, 0xD6, 0xE2, 0xED, 0xF6, 0xFD, 0x05, 0x0B, 0x12, 0x18,
	0x1D, 0x22, 0x27, 0x2B, 0x2F, 0x32, 0x34, 0x36, 0x36, 0x35, 0x32, 0x2D,
	0x26, 0x1D, 0x16, 0x11, 0x0C, 0x09, 0x07, 0x05, 0x04, 0x03, 0x02, 0x01,
	0x01, 0x01, 0x00, 0x00, 0x00, 0x01, 0x01, 0x01, 0x02, 0x02, 0x03, 0x04,
	0x06, 0x08, 0x0B, 0x0E, 0x13, 0x1A, 0x22, 0x2D, 0x37, 0x3D, 0x40, 0x41,
	0x3F, 0x3A, 0x32, 0x27, 0x16, 0x00, 0xE9, 0xD6, 0xC9, 0xBE, 0xB6, 0xB0,
	0xAB, 0xA8, 0xA5, 0xA3, 0xA2, 0xA1, 0xA0, 0x9F, 0x9F, 0x9F, 0x9E, 0x9E,
	0x9E, 0x9E, 0x9F, 0x9F, 0x9F, 0xA0, 0xA0, 0xA1, 0xA3, 0xA5, 0xA7, 0xAA,
	0xAE, 0xB3, 0xBB, 0xC4, 0xD0, 0xE1, 0xF6, 0x0F, 0x23, 0x33, 0x3E, 0x47,
	0x4D, 0x52, 0x56, 0x58, 0x5A, 0x5B, 0x5C, 0x5C, 0x5B, 0x5A, 0x58, 0x55,
	0x51, 0x4C, 0x45, 0x3B, 0x2F, 0x1E, 0x08, 0xF2, 0xE2, 0xD7, 0xD0, 0xCD,
	0xCE, 0xD3, 0xDC, 0xE9, 0xFD, 0x14, 0x27, 0x36, 0x41, 0x49, 0x4F, 0x54,
	0x57, 0x5A, 0x5C, 0x5D, 0x5E, 0x5F, 0x5F, 0x5F, 0x5E, 0x5E, 0x5C, 0x5B,
	0x59, 0x55, 0x51, 0x4C, 0x45, 0x3B, 0x2E, 0x1D, 0x09, 0xFB, 0xEF, 0xE6,
	0xDF, 0xDA, 0xD6, 0xD3, 0xD1, 0xCF, 0xCE, 0xCD, 0xCC, 0xCC, 0xCB, 0xCB,
	0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCB, 0xCC, 0xCC, 0xCD, 0xCE,
	0xCF, 0xD1, 0xD3, 0xD6, 0xDA, 0xDF, 0xE6, 0xED, 0xF1, 0xF5, 0xF8, 0xFA,
	0xFC, 0xFD, 0xFE, 0xFE, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x01, 0x01, 0x02, 0x03, 0x04, 0x05, 0x07, 0x09, 0x0C, 0x10,
	0x16, 0x1C, 0x25, 0x31, 0x41, 0x4D, 0x51, 0x4F, 0x45, 0x33, 0x17, 0xF1,
	0xCD, 0xB2, 0x9E, 0x90, 0x87, 0x82, 0x81, 0x83, 0x8A, 0x95, 0xA5, 0xBC,
	0xDB, 0x03, 0x29, 0x45, 0x59, 0x66, 0x6E, 0x71, 0x6F, 0x68, 0x5C, 0x49,
	0x2E, 0x11, 0xFC, 0xEB, 0xDE, 0xD5, 0xCE, 0xC9, 0xC6, 0xC5, 0xC5, 0xC6,
	0xC9, 0xCE, 0xD4, 0xDD, 0xE6, 0xED, 0xF2, 0xF6, 0xF8, 0xFB, 0xFC, 0xFD,
	0xFE, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x04, 0x08, 0x0D, 0x11, 0x15,
	0x19, 0x1D, 0x20, 0x23, 0x26, 0x29, 0x2B, 0x2D, 0x2F, 0x30, 0x31, 0x31,
	0x31, 0x31, 0x30, 0x2F, 0x2E, 0x2C, 0x2A, 0x27, 0x24, 0x21, 0x1E, 0x1A,
	0x16, 0x12, 0x0E, 0x0A, 0x05, 0x01, 0xFD, 0xF9, 0xF5, 0xF0, 0xEC, 0xE8,
	0xE4, 0xE1, 0xDE, 0xDA, 0xD8, 0xD5, 0xD3, 0xD2, 0xD0, 0xCF, 0xCF, 0xCF,
	0xCF, 0xCF, 0xD0, 0xD2, 0xD3, 0xD6, 0xD8, 0xDB, 0xDE, 0xE1, 0xE5, 0xE9,
	0xED, 0xF1, 0xF5, 0xF9, 0xFE,
#elif defined HAPTIC_RAM_12K_0832_205
    0x55, 0x08, 0x11, 0x09, 0x34, 0x09, 0x35, 0x0A,
	0x1E, 0x0A, 0x1F, 0x0A, 0xEA, 0x0A, 0xEB, 0x0B, 0x24, 0x06, 0x14, 0x21,
	0x2D, 0x3A, 0x45, 0x50, 0x5A, 0x62, 0x6A, 0x70, 0x75, 0x79, 0x7C, 0x7C,
	0x7C, 0x7A, 0x77, 0x72, 0x6C, 0x65, 0x5C, 0x53, 0x48, 0x3D, 0x31, 0x24,
	0x17, 0x0A, 0xFD, 0xF0, 0xE3, 0xD6, 0xCA, 0xBE, 0xB3, 0xA9, 0xA0, 0x98,
	0x91, 0x8C, 0x88, 0x85, 0x84, 0x84, 0x85, 0x88, 0x8D, 0x92, 0x99, 0xA2,
	0xAB, 0xB5, 0xC0, 0xCC, 0xD8, 0xE5, 0xF3, 0x00, 0x0C, 0x1A, 0x27, 0x33,
	0x3F, 0x4A, 0x54, 0x5E, 0x66, 0x6D, 0x73, 0x77, 0x7A, 0x7C, 0x7C, 0x7B,
	0x79, 0x75, 0x6F, 0x69, 0x61, 0x58, 0x4E, 0x43, 0x37, 0x2B, 0x1E, 0x11,
	0x04, 0xF7, 0xEA, 0xDD, 0xD0, 0xC4, 0xB9, 0xAE, 0xA5, 0x9C, 0x95, 0x8F,
	0x8A, 0x86, 0x84, 0x84, 0x84, 0x87, 0x8A, 0x8F, 0x95, 0x9D, 0xA6, 0xAF,
	0xBA, 0xC6, 0xD2, 0xDE, 0xEB, 0xF9, 0x05, 0x13, 0x20, 0x2C, 0x39, 0x44,
	0x4F, 0x59, 0x62, 0x69, 0x70, 0x75, 0x79, 0x7B, 0x7C, 0x7C, 0x7A, 0x77,
	0x72, 0x6C, 0x65, 0x5D, 0x53, 0x49, 0x3E, 0x32, 0x25, 0x18, 0x0B, 0xFE,
	0xF1, 0xE4, 0xD7, 0xCB, 0xBF, 0xB4, 0xAA, 0xA0, 0x98, 0x92, 0x8C, 0x88,
	0x85, 0x84, 0x84, 0x85, 0x88, 0x8C, 0x92, 0x99, 0xA1, 0xAA, 0xB4, 0xBF,
	0xCB, 0xD8, 0xE4, 0xF2, 0x00, 0xF5, 0xE9, 0xDD, 0xD2, 0xC7, 0xBD, 0xB4,
	0xAC, 0xA4, 0x9E, 0x99, 0x95, 0x92, 0x91, 0x91, 0x92, 0x94, 0x98, 0x9D,
	0xA3, 0xAA, 0xB2, 0xBB, 0xC4, 0xCF, 0xDA, 0xE5, 0xF1, 0xFD, 0x08, 0x14,
	0x20, 0x2B, 0x36, 0x40, 0x4A, 0x52, 0x5A, 0x60, 0x66, 0x6A, 0x6D, 0x6F,
	0x6F, 0x6F, 0x6D, 0x69, 0x65, 0x5F, 0x58, 0x51, 0x48, 0x3E, 0x34, 0x29,
	0x1E, 0x12, 0x06, 0xFD, 0xF6, 0xEF, 0xE9, 0xE2, 0xDC, 0xD7, 0xD2, 0xCD,
	0xC9, 0xC6, 0xC3, 0xC1, 0xC0, 0xC0, 0xC0, 0xC1, 0xC3, 0xC5, 0xC8, 0xCC,
	0xD0, 0xD5, 0xDB, 0xE1, 0xE7, 0xED, 0xF4, 0xFB, 0x01, 0x08, 0x0F, 0x16,
	0x1C, 0x22, 0x28, 0x2D, 0x32, 0x36, 0x39, 0x3C, 0x3E, 0x40, 0x40, 0x40,
	0x40, 0x3E, 0x3C, 0x39, 0x35, 0x31, 0x2C, 0x27, 0x21, 0x1B, 0x14, 0x0E,
	0x07, 0x06, 0x14, 0x21, 0x2D, 0x3A, 0x45, 0x50, 0x5A, 0x62, 0x6A, 0x70,
	0x75, 0x79, 0x7C, 0x7C, 0x7C, 0x7A, 0x77, 0x72, 0x6C, 0x65, 0x5C, 0x53,
	0x48, 0x3D, 0x31, 0x24, 0x17, 0x0A, 0xFD, 0xF0, 0xE3, 0xD6, 0xCA, 0xBE,
	0xB3, 0xA9, 0xA0, 0x98, 0x91, 0x8C, 0x88, 0x85, 0x84, 0x84, 0x85, 0x88,
	0x8D, 0x92, 0x99, 0xA2, 0xAB, 0xB5, 0xC0, 0xCC, 0xD8, 0xE5, 0xF3, 0x00,
	0x0C, 0x1A, 0x27, 0x33, 0x3F, 0x4A, 0x54, 0x5E, 0x66, 0x6D, 0x73, 0x77,
	0x7A, 0x7C, 0x7C, 0x7B, 0x79, 0x75, 0x6F, 0x69, 0x61, 0x58, 0x4E, 0x43,
	0x37, 0x2B, 0x1E, 0x11, 0x04, 0xF7, 0xEA, 0xDD, 0xD0, 0xC4, 0xB9, 0xAE,
	0xA5, 0x9C, 0x95, 0x8F, 0x8A, 0x86, 0x84, 0x84, 0x84, 0x87, 0x8A, 0x8F,
	0x95, 0x9D, 0xA6, 0xAF, 0xBA, 0xC6, 0xD2, 0xDE, 0xEB, 0xF9, 0xFA, 0xEE,
	0xE2, 0xD7, 0xCC, 0xC1, 0xB7, 0xAE, 0xA6, 0x9F, 0x9A, 0x95, 0x91, 0x8F,
	0x8F, 0x8F, 0x91, 0x94, 0x98, 0x9E, 0xA4, 0xAC, 0xB5, 0xBE, 0xC9, 0xD4,
	0xDF, 0xEB, 0xF7, 0x02, 0x0F, 0x1B, 0x26, 0x32, 0x3C, 0x46, 0x4F, 0x58,
	0x5F, 0x65, 0x6A, 0x6E, 0x70, 0x71, 0x71, 0x70, 0x6D, 0x69, 0x64, 0x5E,
	0x56, 0x4E, 0x44, 0x3A, 0x2F, 0x24, 0x18, 0x0C, 0x00, 0xF9, 0xF2, 0xEA,
	0xE3, 0xDD, 0xD6, 0xD1, 0xCC, 0xC7, 0xC3, 0xC0, 0xBD, 0xBC, 0xBB, 0xBB,
	0xBB, 0xBD, 0xBF, 0xC2, 0xC6, 0xCA, 0xCF, 0xD5, 0xDB, 0xE2, 0xE8, 0xF0,
	0xF7, 0xFE, 0x05, 0x0C, 0x14, 0x1B, 0x22, 0x28, 0x2E, 0x33, 0x38, 0x3C,
	0x3F, 0x42, 0x44, 0x45, 0x45, 0x45, 0x44, 0x42, 0x3F, 0x3B, 0x37, 0x32,
	0x2D, 0x27, 0x20, 0x19, 0x12, 0x0B, 0x04, 0x06, 0x14, 0x21, 0x2E, 0x3A,
	0x46, 0x51, 0x5B, 0x64, 0x6C, 0x72, 0x77, 0x7B, 0x7E, 0x7E, 0x7E, 0x7C,
	0x79, 0x74, 0x6E, 0x66, 0x5E, 0x54, 0x49, 0x3E, 0x31, 0x25, 0x17, 0x0A,
	0xFD, 0xF0, 0xE2, 0xD5, 0xC9, 0xBD, 0xB2, 0xA8, 0x9E, 0x96, 0x8F, 0x8A,
	0x86, 0x83, 0x82, 0x82, 0x83, 0x86, 0x8B, 0x91, 0x98, 0xA0, 0xA9, 0xB4,
	0xBF, 0xCB, 0xD8, 0xE5, 0xF2, 0x00, 0x0D, 0x1A, 0x27, 0x34, 0x40, 0x4B,
	0x56, 0x5F, 0x68, 0x6F, 0x75, 0x79, 0x7C, 0x7E, 0x7E, 0x7D, 0x7B, 0x76,
	0x71, 0x6A, 0x62, 0x59, 0x4F, 0x44, 0x38, 0x2C, 0x1F, 0x11, 0x00, 0x0C,
	0x18, 0x24, 0x2F, 0x3A, 0x44, 0x4D, 0x56, 0x5D, 0x64, 0x69, 0x6D, 0x70,
	0x71, 0x71, 0x70, 0x6E, 0x6A, 0x65, 0x5F, 0x58, 0x50, 0x47, 0x3D, 0x32,
	0x27, 0x1B, 0x0F, 0x03, 0xF8, 0xEB, 0xE0, 0xD4, 0xC9, 0xBF, 0xB5, 0xAC,
	0xA5, 0x9E, 0x98, 0x94, 0x91, 0x8F, 0x8F, 0x8F, 0x91, 0x95, 0x99, 0x9F,
	0xA6, 0xAE, 0xB7, 0xC1, 0xCB, 0xD6, 0xE2, 0xEE, 0xFA, 0x02, 0x06, 0x0A,
	0x0E, 0x12, 0x16, 0x19, 0x1C, 0x1F, 0x22, 0x24, 0x25, 0x26, 0x27, 0x27,
	0x27, 0x27, 0x26, 0x24, 0x22, 0x20, 0x1D, 0x1A, 0x17, 0x13, 0x0F, 0x0B,
	0x07, 0x03, 0x00, 0xFB, 0xF7, 0xF3, 0xEF, 0xEB, 0xE8, 0xE4, 0xE2, 0xDF,
	0xDD, 0xDB, 0xDA, 0xD9, 0xD9, 0xD9, 0xD9, 0xDA, 0xDB, 0xDD, 0xE0, 0xE2,
	0xE5, 0xE8, 0xEC, 0xF0, 0xF4, 0xF8, 0xFC, 0x03, 0x0B, 0x12, 0x19, 0x20,
	0x26, 0x2C, 0x32, 0x37, 0x3B, 0x3F, 0x42, 0x44, 0x45, 0x45, 0x45, 0x44,
	0x42, 0x40, 0x3C, 0x38, 0x33, 0x2E, 0x28, 0x22, 0x1B, 0x14, 0x0D, 0x05,
	0xFF, 0xF7, 0xF0, 0xE9, 0xE2, 0xDB, 0xD5, 0xCF, 0xCA, 0xC6, 0xC2, 0xBF,
	0xBD, 0xBB, 0xBB, 0xBB, 0xBC, 0xBD, 0xC0, 0xC3, 0xC7, 0xCB, 0xD1, 0xD6,
	0xDC, 0xE3, 0xEA, 0xF1, 0xF9,
#elif defined HAPTIC_RAM_12K_0832_235
    0x55, 0x08, 0x11, 0x09, 0x76, 0x09, 0x77, 0x0A,
	0x80, 0x0A, 0x81, 0x0B, 0x2E, 0x0B, 0x2F, 0x0B, 0x61, 0x07, 0x16, 0x24,
	0x32, 0x3F, 0x4B, 0x56, 0x5F, 0x67, 0x6E, 0x73, 0x76, 0x77, 0x77, 0x75,
	0x71, 0x6B, 0x64, 0x5B, 0x51, 0x45, 0x39, 0x2B, 0x1D, 0x0F, 0x00, 0xF2,
	0xE4, 0xD6, 0xC8, 0xBC, 0xB0, 0xA6, 0x9D, 0x95, 0x90, 0x8B, 0x89, 0x89,
	0x8A, 0x8D, 0x92, 0x98, 0xA0, 0xAA, 0xB5, 0xC1, 0xCE, 0xDB, 0xEA, 0xF8,
	0x06, 0x15, 0x23, 0x31, 0x3E, 0x4A, 0x55, 0x5F, 0x67, 0x6E, 0x73, 0x76,
	0x77, 0x77, 0x75, 0x71, 0x6B, 0x64, 0x5B, 0x51, 0x46, 0x39, 0x2C, 0x1E,
	0x10, 0x01, 0xF3, 0xE5, 0xD7, 0xC9, 0xBC, 0xB1, 0xA6, 0x9D, 0x96, 0x90,
	0x8C, 0x89, 0x89, 0x8A, 0x8D, 0x91, 0x98, 0xA0, 0xA9, 0xB4, 0xC0, 0xCD,
	0xDA, 0xE9, 0xF7, 0x05, 0x14, 0x22, 0x30, 0x3D, 0x49, 0x54, 0x5E, 0x66,
	0x6D, 0x72, 0x76, 0x77, 0x77, 0x75, 0x71, 0x6C, 0x65, 0x5C, 0x52, 0x47,
	0x3A, 0x2D, 0x1F, 0x11, 0x02, 0xF4, 0xE6, 0xD7, 0xCA, 0xBD, 0xB1, 0xA7,
	0x9E, 0x96, 0x90, 0x8C, 0x89, 0x89, 0x8A, 0x8C, 0x91, 0x97, 0x9F, 0xA8,
	0xB3, 0xBF, 0xCC, 0xD9, 0xE8, 0xF6, 0xFA, 0xEC, 0xDE, 0xD1, 0xC5, 0xB9,
	0xAF, 0xA6, 0x9E, 0x97, 0x92, 0x8F, 0x8E, 0x8E, 0x90, 0x93, 0x98, 0x9F,
	0xA7, 0xB0, 0xBB, 0xC6, 0xD3, 0xE0, 0xEE, 0xFB, 0x08, 0x16, 0x24, 0x31,
	0x3D, 0x48, 0x53, 0x5C, 0x63, 0x69, 0x6E, 0x71, 0x72, 0x72, 0x70, 0x6C,
	0x67, 0x60, 0x58, 0x4E, 0x44, 0x38, 0x2B, 0x1E, 0x11, 0x03, 0xF6, 0xE8,
	0xDA, 0xCE, 0xC1, 0xB6, 0xAC, 0xA3, 0x9C, 0x96, 0x91, 0x8F, 0x8E, 0x8E,
	0x90, 0x94, 0x9A, 0xA1, 0xA9, 0xB3, 0xBE, 0xCA, 0xD6, 0xE4, 0xF1, 0xFF,
	0x0C, 0x1A, 0x27, 0x34, 0x40, 0x4B, 0x55, 0x5E, 0x65, 0x6B, 0x6F, 0x72,
	0x72, 0x72, 0x6F, 0x6B, 0x65, 0x5E, 0x55, 0x4C, 0x41, 0x35, 0x28, 0x1A,
	0x0D, 0xFD, 0xF5, 0xEE, 0xE7, 0xE1, 0xDB, 0xD5, 0xD1, 0xCD, 0xC9, 0xC7,
	0xC5, 0xC5, 0xC5, 0xC6, 0xC8, 0xCB, 0xCE, 0xD3, 0xD8, 0xDE, 0xE4, 0xEB,
	0xF2, 0xF9, 0x00, 0x07, 0x0E, 0x15, 0x1C, 0x22, 0x28, 0x2D, 0x31, 0x35,
	0x38, 0x3A, 0x3B, 0x3B, 0x3B, 0x39, 0x37, 0x34, 0x30, 0x2B, 0x25, 0x1F,
	0x19, 0x12, 0x0B, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0E, 0x1D, 0x2B, 0x38, 0x45, 0x50,
	0x5B, 0x63, 0x6B, 0x71, 0x75, 0x77, 0x77, 0x76, 0x73, 0x6E, 0x68, 0x5F,
	0x56, 0x4B, 0x3F, 0x32, 0x24, 0x16, 0x07, 0xFA, 0xEB, 0xDD, 0xCF, 0xC2,
	0xB6, 0xAB, 0xA1, 0x99, 0x92, 0x8D, 0x8A, 0x89, 0x89, 0x8B, 0x8F, 0x95,
	0x9C, 0xA5, 0xAF, 0xBA, 0xC7, 0xD4, 0xE2, 0xF1, 0x00, 0x0D, 0x1C, 0x2A,
	0x37, 0x44, 0x50, 0x5A, 0x63, 0x6A, 0x70, 0x74, 0x77, 0x77, 0x76, 0x73,
	0x6E, 0x68, 0x60, 0x57, 0x4C, 0x40, 0x33, 0x25, 0x17, 0x08, 0xFB, 0xEC,
	0xDE, 0xD0, 0xC3, 0xB6, 0xAB, 0xA2, 0x99, 0x93, 0x8E, 0x8A, 0x89, 0x89,
	0x8B, 0x8F, 0x94, 0x9C, 0xA4, 0xAE, 0xBA, 0xC6, 0xD3, 0xE1, 0xF0, 0xFF,
	0x0C, 0x1B, 0x29, 0x37, 0x43, 0x4F, 0x59, 0x62, 0x6A, 0x70, 0x74, 0x77,
	0x77, 0x76, 0x73, 0x6F, 0x69, 0x61, 0x57, 0x4C, 0x41, 0x34, 0x26, 0x18,
	0x09, 0x07, 0x16, 0x25, 0x33, 0x41, 0x4D, 0x59, 0x63, 0x6B, 0x72, 0x77,
	0x7B, 0x7C, 0x7C, 0x7A, 0x76, 0x70, 0x69, 0x60, 0x56, 0x4A, 0x3D, 0x30,
	0x21, 0x13, 0x03, 0xF5, 0xE6, 0xD7, 0xC9, 0xBC, 0xB0, 0xA5, 0x9B, 0x93,
	0x8C, 0x88, 0x85, 0x84, 0x84, 0x87, 0x8B, 0x91, 0x99, 0xA2, 0xAD, 0xB9,
	0xC6, 0xD4, 0xE2, 0xF1, 0x00, 0x0F, 0x1E, 0x2C, 0x3A, 0x47, 0x53, 0x5E,
	0x67, 0x6F, 0x75, 0x79, 0x7C, 0x7C, 0x7B, 0x78, 0x74, 0x6D, 0x65, 0x5B,
	0x50, 0x44, 0x37, 0x29, 0x1A, 0x0B, 0xFD, 0xF7, 0xF1, 0xEC, 0xE6, 0xE1,
	0xDD, 0xD9, 0xD5, 0xD2, 0xD0, 0xCF, 0xCF, 0xCF, 0xD0, 0xD1, 0xD4, 0xD7,
	0xDA, 0xDF, 0xE3, 0xE9, 0xEE, 0xF4, 0xFA, 0x00, 0x05, 0x0B, 0x11, 0x17,
	0x1C, 0x21, 0x25, 0x29, 0x2C, 0x2F, 0x30, 0x31, 0x31, 0x31, 0x30, 0x2E,
	0x2B, 0x28, 0x24, 0x1F, 0x1A, 0x15, 0x0F, 0x09, 0x03, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46, 0x5B, 0x68, 0x71, 0x76,
	0x79, 0x7B, 0x7C, 0x7D, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7E, 0x7D, 0x7D,
	0x7C, 0x7A, 0x78, 0x74, 0x6D, 0x63, 0x52, 0x38, 0x0E, 0xDB, 0xB9, 0xA5,
	0x98, 0x8F, 0x8A, 0x87, 0x85, 0x84, 0x83, 0x82, 0x82, 0x82, 0x82, 0x82,
	0x83, 0x83, 0x85, 0x86, 0x89, 0x8E, 0x95, 0xA1, 0xB4, 0xD1, 0x00, 0x0F,
	0x1E, 0x2D, 0x3B, 0x49, 0x55, 0x60, 0x69, 0x71, 0x77, 0x7B, 0x7E, 0x7E,
	0x7D, 0x7A, 0x75, 0x6E, 0x66, 0x5C, 0x50, 0x44, 0x36, 0x28, 0x19, 0x09,
	0x06, 0x14, 0x21, 0x2D, 0x39, 0x44, 0x4E, 0x57, 0x5E, 0x65, 0x69, 0x6C,
	0x6D, 0x6D, 0x6B, 0x68, 0x63, 0x5C, 0x54, 0x4B, 0x40, 0x35, 0x29, 0x1C,
	0x0F, 0x00, 0xF1, 0xE2, 0xD3, 0xC5, 0xB7, 0xAB, 0xA0, 0x97, 0x8F, 0x89,
	0x85, 0x82, 0x82, 0x83, 0x86, 0x8B, 0x92, 0x9A, 0xA4, 0xB0, 0xBC, 0xCA,
	0xD8, 0xE7, 0xF7, 0x4A, 0x59, 0x63, 0x69, 0x6C, 0x6F, 0x70, 0x71, 0x71,
	0x72, 0x72, 0x72, 0x72, 0x72, 0x72, 0x71, 0x71, 0x70, 0x6E, 0x6B, 0x67,
	0x61, 0x56, 0x45, 0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x04, 0x0C, 0x15, 0x1D, 0x24, 0x2B, 0x32, 0x37, 0x3C,
	0x40, 0x43, 0x45, 0x45, 0x45, 0x44, 0x42, 0x3E, 0x3A, 0x35, 0x2F, 0x28,
	0x21, 0x19, 0x11, 0x08, 0x00, 0xF8, 0xF0, 0xE7, 0xE0, 0xD8, 0xD2, 0xCC,
	0xC6, 0xC2, 0xBF, 0xBC, 0xBB, 0xBB, 0xBB, 0xBD, 0xC0, 0xC4, 0xC8, 0xCE,
	0xD4, 0xDB, 0xE3, 0xEB, 0xF3, 0xFC,
#elif defined HAPTIC_RAM_12K_0832_260
    0x55, 0x08, 0x11, 0x09, 0x3C, 0x09, 0x3D, 0x0A,
	0x0C, 0x0A, 0x0D, 0x0A, 0xC5, 0x0A, 0xC6, 0x0A, 0xF3, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x19, 0x2A,
	0x3A, 0x49, 0x56, 0x62, 0x6C, 0x74, 0x7A, 0x7D, 0x7E, 0x7D, 0x7A, 0x74,
	0x6C, 0x63, 0x57, 0x4A, 0x3B, 0x2B, 0x1B, 0x09, 0xF9, 0xE8, 0xD7, 0xC7,
	0xB9, 0xAB, 0x9F, 0x95, 0x8D, 0x87, 0x83, 0x82, 0x82, 0x86, 0x8B, 0x93,
	0x9D, 0xA8, 0xB5, 0xC4, 0xD4, 0xE4, 0xF5, 0x05, 0x17, 0x27, 0x37, 0x46,
	0x54, 0x60, 0x6A, 0x73, 0x79, 0x7D, 0x7E, 0x7E, 0x7B, 0x75, 0x6E, 0x64,
	0x59, 0x4C, 0x3D, 0x2E, 0x1D, 0x0C, 0xFC, 0xEB, 0xDA, 0xCA, 0xBB, 0xAD,
	0xA1, 0x96, 0x8E, 0x88, 0x83, 0x82, 0x82, 0x85, 0x8A, 0x92, 0x9B, 0xA6,
	0xB3, 0xC2, 0xD1, 0xE2, 0xF3, 0x03, 0x14, 0x25, 0x35, 0x44, 0x52, 0x5E,
	0x69, 0x72, 0x78, 0x7C, 0x7E, 0x7E, 0x7B, 0x76, 0x6F, 0x66, 0x5B, 0x4E,
	0x40, 0x30, 0x20, 0x0F, 0xFF, 0xED, 0xDC, 0xCC, 0xBD, 0xAF, 0xA3, 0x98,
	0x8F, 0x88, 0x84, 0x82, 0x82, 0x84, 0x89, 0x90, 0x99, 0xA4, 0xB1, 0xBF,
	0xCF, 0xDF, 0xF0, 0xF8, 0xE7, 0xD6, 0xC6, 0xB7, 0xAA, 0x9E, 0x94, 0x8C,
	0x86, 0x83, 0x82, 0x83, 0x86, 0x8C, 0x94, 0x9D, 0xA9, 0xB6, 0xC5, 0xD5,
	0xE5, 0xF7, 0x07, 0x18, 0x29, 0x39, 0x47, 0x55, 0x61, 0x6B, 0x73, 0x79,
	0x7D, 0x7E, 0x7E, 0x7A, 0x75, 0x6D, 0x63, 0x58, 0x4B, 0x3C, 0x2C, 0x1C,
	0x0B, 0xFB, 0xEF, 0xE4, 0xDA, 0xD0, 0xC7, 0xBF, 0xB8, 0xB3, 0xAF, 0xAC,
	0xAC, 0xAC, 0xAF, 0xB2, 0xB8, 0xBE, 0xC6, 0xCF, 0xD9, 0xE3, 0xEE, 0xFA,
	0x04, 0x10, 0x1B, 0x26, 0x30, 0x39, 0x41, 0x48, 0x4D, 0x51, 0x54, 0x54,
	0x54, 0x52, 0x4E, 0x49, 0x42, 0x3B, 0x32, 0x28, 0x1E, 0x12, 0x07, 0xFC,
	0xF1, 0xE6, 0xDB, 0xD1, 0xC8, 0xC0, 0xB9, 0xB3, 0xAF, 0xAD, 0xAC, 0xAC,
	0xAE, 0xB2, 0xB7, 0xBD, 0xC5, 0xCD, 0xD7, 0xE2, 0xED, 0xF8, 0x03, 0x0E,
	0x19, 0x24, 0x2E, 0x37, 0x40, 0x47, 0x4C, 0x50, 0x53, 0x54, 0x54, 0x52,
	0x4F, 0x4A, 0x43, 0x3C, 0x33, 0x2A, 0x1F, 0x14, 0x09, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x19, 0x2A,
	0x3A, 0x49, 0x56, 0x62, 0x6C, 0x74, 0x7A, 0x7D, 0x7E, 0x7D, 0x7A, 0x74,
	0x6C, 0x63, 0x57, 0x4A, 0x3B, 0x2B, 0x1B, 0x09, 0xF9, 0xE8, 0xD7, 0xC7,
	0xB9, 0xAB, 0x9F, 0x95, 0x8D, 0x87, 0x83, 0x82, 0x82, 0x86, 0x8B, 0x93,
	0x9D, 0xA8, 0xB5, 0xC4, 0xD4, 0xE4, 0xF5, 0x05, 0x17, 0x27, 0x37, 0x46,
	0x54, 0x60, 0x6A, 0x73, 0x79, 0x7D, 0x7E, 0x7E, 0x7B, 0x75, 0x6E, 0x64,
	0x59, 0x4C, 0x3D, 0x2E, 0x1D, 0x0C, 0xFC, 0xEB, 0xDA, 0xCA, 0xBB, 0xAD,
	0xA1, 0x96, 0x8E, 0x88, 0x83, 0x82, 0x82, 0x85, 0x8A, 0x92, 0x9B, 0xA6,
	0xB3, 0xC2, 0xD1, 0xE2, 0xF3, 0xF8, 0xE7, 0xD6, 0xC6, 0xB7, 0xAA, 0x9E,
	0x94, 0x8C, 0x86, 0x83, 0x82, 0x83, 0x86, 0x8C, 0x94, 0x9D, 0xA9, 0xB6,
	0xC5, 0xD5, 0xE5, 0xF7, 0x07, 0x18, 0x29, 0x39, 0x47, 0x55, 0x61, 0x6B,
	0x73, 0x79, 0x7D, 0x7E, 0x7E, 0x7A, 0x75, 0x6D, 0x63, 0x58, 0x4B, 0x3C,
	0x2C, 0x1C, 0x0B, 0xFA, 0xED, 0xE1, 0xD5, 0xCA, 0xC0, 0xB7, 0xAF, 0xAA,
	0xA5, 0xA2, 0xA2, 0xA2, 0xA5, 0xA9, 0xAF, 0xB6, 0xBF, 0xC9, 0xD4, 0xE0,
	0xEC, 0xF9, 0x05, 0x12, 0x1E, 0x2A, 0x35, 0x3F, 0x48, 0x50, 0x56, 0x5B,
	0x5D, 0x5E, 0x5E, 0x5B, 0x57, 0x52, 0x4A, 0x42, 0x38, 0x2D, 0x21, 0x15,
	0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x08, 0x19, 0x2A, 0x3A, 0x49, 0x56, 0x62, 0x6C, 0x74, 0x7A, 0x7D,
	0x7E, 0x7D, 0x7A, 0x74, 0x6C, 0x63, 0x57, 0x4A, 0x3B, 0x2B, 0x1B, 0x09,
	0xF9, 0xE8, 0xD7, 0xC7, 0xB9, 0xAB, 0x9F, 0x95, 0x8D, 0x87, 0x83, 0x82,
	0x82, 0x86, 0x8B, 0x93, 0x9D, 0xA8, 0xB5, 0xC4, 0xD4, 0xE4, 0xF5, 0x05,
	0x17, 0x27, 0x37, 0x46, 0x54, 0x60, 0x6A, 0x73, 0x79, 0x7D, 0x7E, 0x7E,
	0x7B, 0x75, 0x6E, 0x64, 0x59, 0x4C, 0x3D, 0x2E, 0x1D, 0x0C, 0x08, 0x19,
	0x2A, 0x3A, 0x49, 0x56, 0x62, 0x6C, 0x74, 0x7A, 0x7D, 0x7E, 0x7D, 0x7A,
	0x74, 0x6C, 0x63, 0x57, 0x4A, 0x3B, 0x2B, 0x1B, 0x09, 0xF9, 0xE8, 0xD7,
	0xC7, 0xB9, 0xAB, 0x9F, 0x95, 0x8D, 0x87, 0x83, 0x82, 0x82, 0x86, 0x8B,
	0x93, 0x9D, 0xA8, 0xB5, 0xC4, 0xD4, 0xE4, 0xF5, 0x02, 0x08, 0x0D, 0x12,
	0x17, 0x1B, 0x1E, 0x22, 0x24, 0x26, 0x27, 0x27, 0x27, 0x26, 0x24, 0x22,
	0x1F, 0x1B, 0x17, 0x12, 0x0D, 0x08, 0x03, 0xFE, 0xF9, 0xF4, 0xEF, 0xEA,
	0xE6, 0xE2, 0xDF, 0xDC, 0xDA, 0xD9, 0xD9, 0xD9, 0xDA, 0xDC, 0xDE, 0xE1,
	0xE5, 0xE9, 0xED, 0xF2, 0xF8, 0xFD, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x0F, 0x19, 0x22, 0x2B, 0x33,
	0x3A, 0x3F, 0x44, 0x48, 0x4A, 0x4A, 0x4A, 0x48, 0x44, 0x40, 0x3A, 0x33,
	0x2B, 0x23, 0x19, 0x0F, 0x05, 0xFC, 0xF2, 0xE8, 0xDF, 0xD6, 0xCE, 0xC7,
	0xC1, 0xBC, 0xB9, 0xB6, 0xB6, 0xB6, 0xB8, 0xBB, 0xC0, 0xC5, 0xCC, 0xD4,
	0xDD, 0xE6, 0xF0, 0xFA,
#else
#ifndef USE_QW_MOTOR_RAM
0x55,0x08,0x11,0x09,0x76,0x09,0x77,0x0a,0x80,0x0a,0x81,0x0b,0x2e,0x0b,0x2f,0x0b,
0x61,
     0x00,0x0e,0x1d,0x2b,0x38,0x45,0x50,0x5b,0x63,0x6b,0x71,0x75,0x77,0x77,0x76,
0x73,0x6e,0x68,0x5f,0x56,0x4b,0x3f,0x32,0x24,0x16,0x07,0xfa,0xeb,0xdd,0xcf,0xc2,
0xb6,0xab,0xa1,0x99,0x92,0x8d,0x8a,0x89,0x89,0x8b,0x8f,0x95,0x9c,0xa5,0xaf,0xba,

0xc7,0xd4,0xe2,0xf1,0x00,0x0d,0x1c,0x2a,0x37,0x44,0x50,0x5a,0x63,0x6a,0x70,0x74,
0x77,0x77,0x76,0x73,0x6e,0x68,0x60,0x57,0x4c,0x40,0x33,0x25,0x17,0x08,0xfb,0xec,
0xde,0xd0,0xc3,0xb6,0xab,0xa2,0x99,0x93,0x8e,0x8a,0x89,0x89,0x8b,0x8f,0x94,0x9c,
0xa4,0xae,0xba,0xc6,0xd3,0xe1,0xf0,0xff,0x0c,0x1b,0x29,0x37,0x43,0x4f,0x59,0x62,

0x6a,0x70,0x74,0x77,0x77,0x76,0x73,0x6f,0x69,0x61,0x57,0x4c,0x41,0x34,0x26,0x18,
0x09,0xfb,0xed,0xde,0xd1,0xc3,0xb7,0xac,0xa2,0x9a,0x93,0x8e,0x8a,0x89,0x89,0x8b,
0x8e,0x94,0x9b,0xa4,0xae,0xb9,0xc5,0xd3,0xe1,0xef,0x00,0xf3,0xe5,0xd8,0xcb,0xbf,
0xb4,0xaa,0xa2,0x9a,0x95,0x91,0x8e,0x8e,0x8e,0x91,0x95,0x9b,0xa3,0xab,0xb5,0xc0,

0xcd,0xd9,0xe7,0xf4,0x01,0x0f,0x1d,0x2a,0x37,0x43,0x4e,0x57,0x60,0x67,0x6c,0x70,
0x72,0x72,0x71,0x6e,0x6a,0x64,0x5c,0x53,0x49,0x3e,0x32,0x25,0x17,0x0a,0xfd,0xef,
0xe1,0xd4,0xc7,0xbc,0xb1,0xa8,0x9f,0x99,0x93,0x90,0x8e,0x8e,0x8f,0x92,0x97,0x9d,
0xa5,0xae,0xb8,0xc4,0xd0,0xdd,0xea,0xf8,0x05,0x13,0x21,0x2e,0x3a,0x46,0x50,0x5a,

0x62,0x68,0x6d,0x71,0x72,0x72,0x71,0x6d,0x68,0x62,0x5a,0x51,0x46,0x3b,0x2e,0x21,
0x14,0x00,0xf9,0xf2,0xeb,0xe4,0xde,



                                   0xd8,0xd3,0xcf,0xcb,0xc8,0xc6,0xc5,0xc5,0xc5,
0xc7,0xc9,0xcc,0xd1,0xd5,0xdb,0xe1,0xe7,0xee,0xf5,0xfd,0x03,0x0a,0x11,0x18,0x1f,
0x25,0x2a,0x2f,0x33,0x37,0x39,0x3b,0x3b,0x3b,0x3a,0x38,0x35,0x32,0x2d,0x28,0x23,

0x1c,0x16,0x0f,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

0x00,0x00,0x07,0x16,0x24,0x32,0x3f,0x4b,0x56,0x5f,0x67,0x6e,0x73,0x76,0x77,0x77,
0x75,0x71,0x6b,0x64,0x5b,0x51,0x45,0x39,0x2b,0x1d,0x0f,0x00,0xf2,0xe4,0xd6,0xc8,
0xbc,0xb0,0xa6,0x9d,0x95,0x90,0x8b,0x89,0x89,0x8a,0x8d,0x92,0x98,0xa0,0xaa,0xb5,
0xc1,0xce,0xdb,0xea,0xf8,0x06,0x15,0x23,0x31,0x3e,0x4a,0x55,0x5f,0x67,0x6e,0x73,

0x76,0x77,0x77,0x75,0x71,0x6b,0x64,0x5b,0x51,0x46,0x39,0x2c,0x1e,0x10,0x01,0xf3,
0xe5,0xd7,0xc9,0xbc,0xb1,0xa6,0x9d,0x96,0x90,0x8c,0x89,0x89,0x8a,0x8d,0x91,0x98,
0xa0,0xa9,0xb4,0xc0,0xcd,0xda,0xe9,0xf7,0x05,0x14,0x22,0x30,0x3d,0x49,0x54,0x5e,
0x66,0x6d,0x72,0x76,0x77,0x77,0x75,0x71,0x6c,0x65,0x5c,0x52,0x47,0x3a,0x2d,0x1f,

0x11,0x00,0x0f,0x1e,0x2c,0x3a,0x47,0x53,0x5e,0x67,0x6f,0x75,0x79,0x7c,0x7c,0x7b,
0x78,0x74,0x6d,0x65,0x5b,0x50,0x44,0x37,0x29,0x1a,0x0b,0xfd,0xee,0xdf,0xd0,0xc3,
0xb6,0xaa,0xa0,0x97,0x90,0x8a,0x86,0x84,0x84,0x85,0x89,0x8e,0x95,0x9d,0xa7,0xb3,
0xbf,0xcd,0xdb,0xea,0xf9,0x07,0x16,0x25,0x33,0x41,0x4d,0x59,0x63,0x6b,0x72,0x77,

0x7b,0x7c,0x7c,0x7a,0x76,0x70,0x69,0x60,0x56,0x4a,0x3d,0x30,0x21,0x13,0x00,0xfa,
0xf4,0xee,0xe9,0xe4,0xdf,0xdb,0xd7,0xd4,0xd1,0xd0,0xcf,0xcf,0xcf,0xd0,0xd2,0xd5,
0xd9,0xdd,0xe1,0xe6,0xeb,0xf1,0xf7,0xfd,0x02,0x08,0x0e,0x14,0x1a,0x1f,0x23,0x27,
0x2b,0x2d,0x30,0x31,0x31,0x31,0x30,0x2f,0x2c,0x29,0x26,0x21,0x1d,0x17,0x12,0x0c,



0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x52,0x63,0x6d,0x73,
0x78,0x7a,0x7c,0x7d,0x7d,0x7e,0x7e,0x7e,0x7e,0x7e,0x7e,0x7d,0x7c,0x7b,0x79,0x76,
0x71,0x68,0x5b,0x47,0x26,0xf2,0xc8,0xae,0x9d,0x93,0x8c,0x88,0x86,0x84,0x83,0x83,
0x82,0x82,0x82,0x82,0x83,0x83,0x84,0x85,0x88,0x8b,0x91,0x9a,0xa9,0xc1,0xe6,0x07,

0x17,0x26,0x34,0x42,0x4f,0x5a,0x64,0x6d,0x74,0x79,0x7d,0x7e,0x7e,0x7c,0x78,0x72,
0x6a,0x61,0x56,0x4a,0x3d,0x2f,0x20,0x11,0x00,0x0d,0x1a,0x27,0x33,0x3f,0x49,0x53,
0x5b,0x62,0x67,0x6b,0x6d,0x6d,0x6c,0x6a,0x65,0x5f,0x58,0x4f,0x46,0x3b,0x2f,0x22,
0x15,0x08,0xf9,0xe9,0xda,0xcc,0xbe,0xb1,0xa6,0x9c,0x93,0x8c,0x87,0x83,0x82,0x82,

0x84,0x88,0x8e,0x96,0x9f,0xaa,0xb6,0xc3,0xd1,0xe0,0xef,0x40,0x53,0x5e,0x66,0x6b,
0x6e,0x6f,0x71,0x71,0x72,0x72,0x72,0x72,0x72,0x72,0x72,0x71,0x70,0x6f,0x6d,0x6a,
0x64,0x5c,0x4f,0x39,0x17,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,


                                                                           0x00,
0x08,0x11,0x19,0x21,0x28,0x2f,0x35,0x3a,0x3e,0x41,0x44,0x45,0x45,0x45,0x43,0x40,
0x3c,0x37,0x32,0x2c,0x25,0x1d,0x15,0x0d,0x04,0xfc,0xf4,0xec,0xe4,0xdc,0xd5,0xce,
0xc9,0xc4,0xc0,0xbd,0xbb,0xbb,0xbb,0xbc,0xbe,0xc2,0xc6,0xcb,0xd1,0xd8,0xdf,0xe7,
0xef,0xf7,
#else
0x01,0x08,0x59,0x08,0x85,0x08,0x86,0x08,0xAD,0x08,0xAE,0x08,0xD1,0x08,0xD2,0x08,
0xF3,0x08,0xF4,0x09,0x11,0x09,0x12,0x09,0x2C,0x09,0x2D,0x09,0x44,0x09,0x45,0x09,
0x5A,0x09,0x5B,0x09,0x6E,0x09,0x6F,0x09,0x80,0x09,0x81,0x09,0x91,0x09,0x92,0x09,
0xA0,0x09,0xA1,0x09,0xAD,0x09,0xAE,0x09,0xB9,0x09,0xBA,0x09,0xC4,0x09,0xC5,0x09,
0xCE,0x09,0xCF,0x09,0xD7,0x09,0xD8,0x09,0xDF,0x09,0xE0,0x09,0xE6,0x09,0xE7,0x09,
0xEC,0x09,0xED,0x09,0xF2,0x09,0xF3,0x0A,0x25,
0x00,0x11,0x23,0x33,0x43,0x51,0x5E,0x69,0x72,0x79,0x7D,0x7F,0x7E,0x7B,0x76,0x6E,
0x64,0x58,0x4A,0x3B,0x2B,0x1A,0x08,0xF7,0xE5,0xD4,0xC4,0xB5,0xA7,0x9B,0x91,0x89,
0x84,0x81,0x80,0x82,0x86,0x8D,0x96,0xA1,0xAE,0xBC,0xCC,0xDC,0xEE,0x00,0x13,0x27,
0x39,0x4A,0x5A,0x67,0x71,0x79,0x7D,0x7F,0x7D,0x79,0x71,0x67,0x5A,0x4A,0x39,0x27,
0x13,0x00,0xEC,0xD8,0xC6,0xB5,0xA5,0x98,0x8E,0x86,0x82,0x80,0x82,0x86,0x8E,0x98,
0xA5,0xB5,0xC6,0xD8,0xEC,0x00,0x16,0x2B,0x3F,0x51,0x61,0x6E,0x77,0x7D,0x7F,0x7D,
0x77,0x6E,0x61,0x51,0x3F,0x2B,0x16,0x00,0xE9,0xD4,0xC0,0xAE,0x9E,0x91,0x88,0x82,
0x80,0x82,0x88,0x91,0x9E,0xAE,0xC0,0xD4,0xE9,0x00,0x17,0x2E,0x43,0x55,0x65,0x72,
0x7A,0x7E,0x7E,0x7A,0x72,0x65,0x55,0x43,0x2E,0x17,0x00,0xE8,0xD1,0xBC,0xAA,0x9A,
0x8D,0x85,0x81,0x81,0x85,0x8D,0x9A,0xAA,0xBC,0xD1,0xE8,0x00,0x1A,0x33,0x4A,0x5E,
0x6E,0x79,0x7E,0x7E,0x79,0x6E,0x5E,0x4A,0x33,0x1A,0x00,0xE5,0xCC,0xB5,0xA1,0x91,
0x86,0x81,0x81,0x86,0x91,0xA1,0xB5,0xCC,0xE5,0x00,0x1D,0x39,0x51,0x66,0x75,0x7D,
0x7F,0x7A,0x6E,0x5C,0x46,0x2B,0x0E,0xF1,0xD4,0xB9,0xA3,0x91,0x85,0x80,0x82,0x8A,
0x99,0xAE,0xC6,0xE2,0x00,0x20,0x3F,0x5A,0x6E,0x7B,0x7F,0x7B,0x6E,0x5A,0x3F,0x20,
0x00,0xDF,0xC0,0xA5,0x91,0x84,0x80,0x84,0x91,0xA5,0xC0,0xDF,0x00,0x23,0x44,0x60,
0x73,0x7E,0x7E,0x73,0x60,0x44,0x23,0x00,0xDC,0xBB,0x9F,0x8C,0x81,0x81,0x8C,0x9F,
0xBB,0xDC,0x00,0x27,0x4A,0x67,0x79,0x7F,0x79,0x67,0x4A,0x27,0x00,0xD8,0xB5,0x98,
0x86,0x80,0x86,0x98,0xB5,0xD8,0x00,0x2B,0x51,0x6E,0x7D,0x7D,0x6E,0x51,0x2B,0x00,
0xD4,0xAE,0x91,0x82,0x82,0x91,0xAE,0xD4,0x00,0x2E,0x55,0x72,0x7E,0x7A,0x65,0x43,
0x17,0xE8,0xBC,0x9A,0x85,0x81,0x8D,0xAA,0xD1,0x00,0x33,0x5E,0x79,0x7E,0x6E,0x4A,
0x1A,0xE5,0xB5,0x91,0x81,0x86,0xA1,0xCC,0x00,0x3B,0x68,0x7E,0x77,0x54,0x1E,0xE1,
0xAB,0x88,0x81,0x97,0xC4,0x00,0x3F,0x6E,0x7F,0x6E,0x3F,0x00,0xC0,0x91,0x80,0x91,
0xC0,0x00,0x44,0x73,0x7E,0x60,0x23,0xDC,0x9F,0x81,0x8C,0xBB,0x00,0x4A,0x79,0x79,
0x4A,0xFF,0xB5,0x86,0x86,0xB5,0x00,0x51,0x7D,0x6E,0x2B,0xD4,0x91,0x82,0xAE,0x00,
0x5A,0x7F,0x5A,0x00,0xA5,0x80,0xA5,0x00,0x63,0x7C,0x37,0xC8,0x83,0x9C,0x00,0x6E,
0x6E,0x00,0x91,0x91,0x00,0x6E,0x6E,0x00,0x91,0x91,0x00,0x08,0x11,0x19,0x21,0x28,
0x2F,0x35,0x3A,0x3E,0x41,0x44,0x45,0x45,0x45,0x43,0x40,0x3C,0x37,0x32,0x2C,0x25,
0x1D,0x15,0x0D,0x04,0xFC,0xF4,0xEC,0xE4,0xDC,0xD5,0xCE,0xC9,0xC4,0xC0,0xBD,0xBB,
0xBB,0xBB,0xBC,0xBE,0xC2,0xC6,0xCB,0xD1,0xD8,0xDF,0xE7,0xEF,0xF7,
#endif
#endif
};
uint32_t aw862xx_ram_len = sizeof(aw862xx_ram_data);
#endif

const uint8_t haptic_nv_rtp_data[] = {
0x00,0x00,0x00,0x00,0xff,0xff,0xff,0xff,0xfe,0xfe,0xfd,0xfd,0xfc,0xfb,
0xfa,0xf9,0xf7,0xf5,0xf4,0xf2,0xf1,0xf1,0xf0,0xf0,0xef,0xef,0xef,0xf0,
0xf0,0xf1,0xf2,0xf3,0xf4,0xf6,0xf8,0xfb,0xfe,0x01,0x06,0x0b,0x12,0x1a,
0x24,0x2d,0x33,0x38,0x3b,0x3d,0x3d,0x3b,0x38,0x34,0x2e,0x26,0x1b,0x0e,
0xff,0xf1,0xe4,0xda,0xd2,0xcb,0xc5,0xc0,0xbc,0xb8,0xb6,0xb3,0xb1,0xb0,
0xae,0xad,0xac,0xab,0xab,0xaa,0xaa,0xa9,0xa9,0xa9,0xa9,0xa8,0xa8,0xa8,
0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0xa9,0xa9,0xa9,0xa9,
0xaa,0xaa,0xab,0xac,0xad,0xae,0xaf,0xb0,0xb2,0xb4,0xb7,0xba,0xbd,0xc2,
0xc7,0xce,0xd5,0xde,0xe9,0xf5,0x00,0x0c,0x1a,0x2a,0x38,0x43,0x4c,0x52,
0x57,0x5b,0x5e,0x60,0x62,0x63,0x64,0x65,0x65,0x66,0x66,0x65,0x65,0x64,
0x62,0x61,0x5f,0x5c,0x59,0x55,0x51,0x4b,0x44,0x3c,0x33,0x27,0x19,0x08,
0xf4,0xde,0xcc,0xbc,0xb0,0xa5,0x9d,0x95,0x90,0x8b,0x87,0x85,0x83,0x81,
0x81,0x81,0x81,0x82,0x84,0x87,0x8b,0x8f,0x95,0x9c,0xa4,0xaf,0xbb,0xca,
0xdc,0xf3,0x09,0x1d,0x2e,0x3c,0x47,0x51,0x59,0x5f,0x64,0x69,0x6c,0x6f,
0x71,0x72,0x73,0x73,0x73,0x73,0x71,0x70,0x6e,0x6c,0x6a,0x68,0x67,0x65,
0x63,0x62,0x60,0x5d,0x5b,0x58,0x54,0x50,0x4c,0x46,0x3f,0x37,0x2e,0x26,
0x20,0x1a,0x16,0x12,0x0f,0x0c,0x0a,0x08,0x07,0x06,0x05,0x04,0x03,0x03,
0x02,0x02,0x01,0x01,0x01,0x01,0x00,0x00,0x00,
};
uint32_t haptic_nv_rtp_len = sizeof(haptic_nv_rtp_data);
