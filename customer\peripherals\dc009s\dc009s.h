/**
  ******************************************************************************
  * @file   dc009s.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __DC009S_BEEP_H__
#define __DC009S_BEEP_H__


#include <stdint.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "board.h"
#include "qw_log.h"

/*******************************************************************************
 * Definitions
 ******************************************************************************/
#define DC009S_LVL               LOG_LVL_DBG
#define DC009S_TAG               "DC009S"

#if (DC009S_LVL >= LOG_LVL_DBG)
    #define DC009S_D(...)        QW_LOG_D(DC009S_TAG, __VA_ARGS__)
#else
    #define DC009S_D(...)
#endif

#if (DC009S_LVL >= LOG_LVL_INFO)
    #define DC009S_I(...)        QW_LOG_I(DC009S_TAG, __VA_ARGS__)
#else
    #define DC009S_I(...)
#endif

#if (DC009S_LVL >= LOG_LVL_WARNING)
    #define DC009S_W(...)        QW_LOG_W(DC009S_TAG, __VA_ARGS__)
#else
    #define DC009S_W(...)
#endif

#if (DC009S_LVL >= LOG_LVL_ERROR)
    #define DC009S_E(...)        QW_LOG_E(DC009S_TAG, __VA_ARGS__)
#else
    #define DC009S_E(...)
#endif

int dc009s_init();
int dc009s_open();
void dc009s_set_volume(uint8_t volume);
int dc009s_set_pwm(uint32_t freq, uint8_t duty_cycle);
int dc009s_close();
int dc009s_deinit();

#endif /* __DC009S_BEEP_H__ */
