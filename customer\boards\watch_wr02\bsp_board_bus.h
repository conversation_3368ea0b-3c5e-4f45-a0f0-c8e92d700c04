/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   bsp_board_bus.h
@Time    :   2025/03/25 14:02:28
@Brief   :
@Details :
*
************************************************************/

#ifndef __BSP_BORAD_BUS_H__
#define __BSP_BORAD_BUS_H__

#include <rtconfig.h>
#include <bf0_hal.h>
#include "bsp_board.h"
#include "bsp_pinmux.h"
#include "qw_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define BSP_BUS_LVL               LOG_LVL_DBG
#define BSP_BUS_TAG               "BSP_BUS"

#if (BSP_BUS_LVL >= LOG_LVL_DBG)
    #define BSP_BUS_D(...)        QW_LOG_D(BSP_BUS_TAG, __VA_ARGS__)
#else
    #define BSP_BUS_D(...)
#endif

#if (BSP_BUS_LVL >= LOG_LVL_INFO)
    #define BSP_BUS_I(...)        QW_LOG_I(BSP_BUS_TAG, __VA_ARGS__)
#else
    #define BSP_BUS_I(...)
#endif

#if (BSP_BUS_LVL >= LOG_LVL_WARNING)
    #define BSP_BUS_W(...)        QW_LOG_W(BSP_BUS_TAG, __VA_ARGS__)
#else
    #define BSP_BUS_W(...)
#endif

#if (BSP_BUS_LVL >= LOG_LVL_ERROR)
    #define BSP_BUS_E(...)        QW_LOG_E(BSP_BUS_TAG, __VA_ARGS__)
#else
    #define BSP_BUS_E(...)
#endif

#define USE_BSP_BORAD_BUS_INIT

#ifdef USE_BSP_BORAD_BUS_INIT

// motor and alipay ic
int bsp_i2c1_bus_init(void);
int bsp_i2c1_bus_uninit(void);
/************************************************************************
 *@function:void i2c1_device_rstpin_init(void)
 *@brief:I2C1总线挂载了马达和支付宝，其中马达复位异常时会导致I2C1总线异常，
 * 所以需要单独处理I2C1总线复位
 * 在马达和支付宝驱动INIT之前调用,保证I2C1总线正常
*************************************************************************/
void i2c1_device_rstpin_init(void);

#endif

#ifdef __cplusplus
}
#endif

#endif /* __BSP_BORAD_BUS_H__ */
