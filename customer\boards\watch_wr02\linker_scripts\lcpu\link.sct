#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc
#include "../../../../../drivers/cmsis/sf32lb55x/mem_map.h"

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM0 LCPU_ROM_CODE_START_ADDR LCPU_ROM_CODE_SIZE  {    ; load region size_region
  ER_IROM0 LCPU_ROM_CODE_START_ADDR LCPU_ROM_CODE_SIZE  {  ; load address = execution address
   *.o (RESET2, +First)
   startup_bf0_lcpu_rom.o
   system_bf0_ap.o (.text.lcpu_rom_jump)
  }
}

LR_IROM1 LCPU_CODE_START_ADDR LCPU_CODE_SIZE  {    ; load region size_region
  ER_IROM1 LCPU_CODE_START_ADDR LCPU_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  RW_IRAM1 LCPU_RAM_DATA_START_ADDR LCPU_RAM_DATA_SIZE-0x100  {  ; RW data
   .ANY (+RW +ZI)
  }
  RW_IRAM2 LCPU_RAM_DATA_START_ADDR+LCPU_RAM_DATA_SIZE-0x100 UNINIT 0x100  {  ; RW data  
  test_ctrl.o(UNINITZI)
  }
}

