#ifndef __ALIPAY_PROVISION_OVERTHEAIR_H__
#define __ALIPAY_PROVISION_OVERTHEAIR_H__

#include "alipay_common.h"


/**
 * @brief 调用支付宝设备管理服务进行OTA升级
 * 
 * @param appid 应用ID
 * @return retval_e 返回值枚举类型，RV_OK表示成功，其他值表示失败
 */
EXTERNC retval_e alipay_provision_ota(const char* appid);


/**
 * @brief 调用支付宝设备OTA接口
 * 
 * @param appid 应用ID
 * @return retval_e 返回值，RV_OK表示成功，其他值表示失败
 */
EXTERNC retval_e alipay_active_device(const char* appid);


EXTERNC retval_e alipay_direct_provision(const char* data);

#endif