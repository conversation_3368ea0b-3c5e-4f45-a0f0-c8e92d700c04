/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ag333x_mount.c
@Time    :   2025/3/4 11:15:11
*
**************************************************************************/
#include <stdint.h>
#include <rtthread.h>
#include "airoha_ag333x.h"
#include "ag333x_mount.h"
#include "gps_api.h"
#include "rtconfig.h"
#include "ag333x_pair.h"
#include "gps_dev.h"
#if GPS_USING_AG333X

static bool ag335x_is_configed = false; //用于识别gps是否正常启动，收到gps上电成功报文后开始配置
static gps_mode_type_e ag335x_gps_mode;

static int _ag333x_init(void)
{
    //GPS上电
    ag333x_gps_poweron();
    //上电完成初始化GPS
    if (ag333x_init() == 0) {
        // rt_thread_delay(100);
        ag333x_parser_pair_init();
        return true;
    }
    ag335x_gps_mode = enum_gps_mode_3;
    return 0;
}

static int _ag333x_uninit(void)
{
    ag333x_uninit();
    ag335x_is_configed = false;
    return 0;
}

static int _ag333x_poweron(void)
{
    ag333x_gps_poweron();
    ag333x_gps_wakeup();
    return 0;
}

static int _ag333x_sleep_save(void)
{
    if(ag335x_is_configed)
    {
        // ag333x_gps_sleep_save();
        ag333x_parser_pair_close();
    }
    return 0;
}

static int _ag333x_poweroff(void)
{
    _ag333x_sleep_save();
    // ag333x_gps_sleep();
    ag333x_gps_poweroff();
    return 0;
}

static int _ag333x_gnss_recv(uint8_t *data, uint16_t length)
{
    return ag333x_gps_get(data, length);
}

static int _ag333x_gnss_send(uint8_t *data, uint16_t length)
{
    if(ag335x_is_configed)
    {
        return ag333x_gps_set(data, length);
    }
    return 0;
}

static int _ag333x_cmd_ctrl(uint8_t cmd)
{
    if(ag335x_is_configed)
    {
        ag333x_gps_cmd_ctrl((enum_airoha_cmd)cmd);
    }
    return 0;
}

static int _ag333x_gnss_work_mode(gps_mode_type_e mode)
{
    if(ag335x_gps_mode == mode){
        return true;
    }
    if (enum_gps_mode_close == mode) {
        if(ag335x_is_configed){
            _ag333x_poweroff();
            ag335x_is_configed = false;
        }
    }
    else {
        if(ag335x_is_configed)
        {
            ag333x_gps_gnss_set(mode);
        }
    }
    ag335x_gps_mode = mode;
    return 0;
}

static int _ag333x_config_default(void)
{
    ag333x_config_default();
    ag335x_is_configed = true;
    return 0;
}

static int _ag333x_gnss_setup_mode(gps_setup_mode mode)
{
    switch (mode)
    {
    case enum_setup_cold:
        ag333x_gps_cmd_ctrl(enum_AIROHA_COLDSTART);
        break;
    case enum_setup_warm:
        ag333x_gps_cmd_ctrl(enum_AIROHA_WARMSTART);
        break;
    case enum_setup_hot:
        ag333x_gps_cmd_ctrl(enum_AIROHA_HOTSTART);
        break;
    }
    // ag333x_parser_pair_init();
    return 0;
}

static int _ag333x_gsv_mode_set(GPS_GSV_MODE mode)
{
    if(ag335x_is_configed){
        if(mode == enum_NMEA_GSV_ON){
            ag333x_gps_cmd_ctrl(enum_AIROHA_GxGSV_ON);
        }else {
            ag333x_gps_cmd_ctrl(enum_AIROHA_GxGSV_OFF);
        }
    }
    return 0;
}

static int _ag333x_set_time_utc(char* utc_time)
{
    if(ag335x_is_configed)
    {
        ag333x_gps_mcu_wakeup_gnss();
        ag333x_set_time_utc(utc_time);
    }
    return 0;
}

static int _ag333x_set_location(char* location)
{
    if(ag335x_is_configed)
    {
        ag333x_gps_mcu_wakeup_gnss();
        ag333x_set_location(location);
    }
    return 0;
}

static int _ag333x_set_navigation_mode(char mode)
{
    if(ag335x_is_configed)
    {
        ag333x_gps_mcu_wakeup_gnss();
        ag333x_set_navigation_mode(mode);
    }
    return 0;
}

static int _ag333x_indication_system_wakeup()
{
    if(ag335x_is_configed)
    {
        ag333x_gps_mcu_wakeup_gnss();
    }
    return 0;
}

/* -------------------------------- register -------------------------------- */
static gps_driver_api_t gps_ag333x = {
    .dev_name = "ag333x",
    .sw_version = {0, 0, 0},
    .gps_init = _ag333x_init,
    .gps_uninit = _ag333x_uninit,
    .gps_poweron = _ag333x_poweron,
    .gps_poweroff = _ag333x_poweroff,
    .gps_gnss_recv = _ag333x_gnss_recv,
    .gps_gnss_send = _ag333x_gnss_send,
    .gps_cmd_ctrl = _ag333x_cmd_ctrl,
    .gps_gnss_work_mode = _ag333x_gnss_work_mode,
    .gps_cmd_sleep_save = _ag333x_sleep_save,
    .gps_cmd_config_default = _ag333x_config_default,
    .gps_gnss_power_setup_mode = _ag333x_gnss_setup_mode,
    .gps_gnss_gsv_mode = _ag333x_gsv_mode_set,
    .gps_set_time_utc = _ag333x_set_time_utc,
    .gps_set_location = _ag333x_set_location,
    .gps_set_navigation_mode = _ag333x_set_navigation_mode,
    .gps_indication_system_wakeup = _ag333x_indication_system_wakeup,
};

void ag333x_api_register(void)
{
    gps_api_register(&gps_ag333x);
    gps_dev_init();
}

#endif
