/**
  ******************************************************************************
  * @file   ili8688e.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the ili8688e.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __ILI8688E_H
#define __ILI8688E_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ILI8688E
  * @{
  */

/** @defgroup ILI8688E_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ILI8688E_Exported_Constants
  * @{
  */

/**
  * @brief ILI8688E chip IDs
  */
#define ILI8688E_ID                  0x1190a7

/**
  * @brief  ILI8688E Size
  */
#define  ILI8688E_LCD_PIXEL_WIDTH    (368)
#define  ILI8688E_LCD_PIXEL_HEIGHT   (448)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ILI8688E_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define ILI8688E_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define ILI8688E_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ILI8688E Registers
  */
#define ILI8688E_SW_RESET           0x01
#define ILI8688E_LCD_ID             0x04
#define ILI8688E_DSI_ERR            0x05
#define ILI8688E_POWER_MODE         0x0A
#define ILI8688E_SLEEP_IN           0x10
#define ILI8688E_SLEEP_OUT          0x11
#define ILI8688E_PARTIAL_DISPLAY    0x12
#define ILI8688E_DISPLAY_INVERSION  0x21
#define ILI8688E_DISPLAY_OFF        0x28
#define ILI8688E_DISPLAY_ON         0x29
#define ILI8688E_WRITE_RAM          0x2C
#define ILI8688E_READ_RAM           0x2E
#define ILI8688E_CASET              0x2A
#define ILI8688E_RASET              0x2B
#define ILI8688E_PART_CASET              0x30
#define ILI8688E_PART_RASET              0x31
#define ILI8688E_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define ILI8688E_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define ILI8688E_TEARING_EFFECT     0x35
#define ILI8688E_NORMAL_DISPLAY     0x36
#define ILI8688E_IDLE_MODE_OFF      0x38
#define ILI8688E_IDLE_MODE_ON       0x39
#define ILI8688E_COLOR_MODE         0x3A
#define ILI8688E_CONTINUE_WRITE_RAM 0x3C
#define ILI8688E_WBRIGHT            0x51 /* Write brightness*/
#define ILI8688E_RBRIGHT            0x53 /* Read brightness*/
#define ILI8688E_PORCH_CTRL         0xB2
#define ILI8688E_FRAME_CTRL         0xB3
#define ILI8688E_GATE_CTRL          0xB7
#define ILI8688E_VCOM_SET           0xBB
#define ILI8688E_LCM_CTRL           0xC0
#define ILI8688E_SET_TIME_SRC       0xC2
#define ILI8688E_SET_DISP_MODE      0xC4
#define ILI8688E_VCOMH_OFFSET_SET   0xC5
#define ILI8688E_FR_CTRL            0xC6
#define ILI8688E_POWER_CTRL         0xD0
#define ILI8688E_PV_GAMMA_CTRL      0xE0
#define ILI8688E_NV_GAMMA_CTRL      0xE1
#define ILI8688E_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup ILI8688E_Exported_Functions
  * @{
  */
void     ILI8688E_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ILI8688E_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ILI8688E_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ILI8688E_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ILI8688E_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ILI8688E_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ILI8688E_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ILI8688E_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ILI8688E_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ILI8688E_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);
void ILI8688E_IdleModeOn(LCDC_HandleTypeDef *hlcdc);
void ILI8688E_IdleModeOff(LCDC_HandleTypeDef *hlcdc);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __ILI8688E_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
