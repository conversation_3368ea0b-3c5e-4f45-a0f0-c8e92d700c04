/**
  ******************************************************************************
  * @file   hpsys_pinmux.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __HPSYS_PINMUX_H
#define __HPSYS_PINMUX_H

typedef struct
{
    __IO uint32_t PAD_SIP00;
    __IO uint32_t PAD_SIP01;
    __IO uint32_t PAD_SIP02;
    __IO uint32_t PAD_SIP03;
    __IO uint32_t PAD_SIP04;
    __IO uint32_t PAD_SIP05;
    __IO uint32_t PAD_SIP06;
    __IO uint32_t PAD_SIP07;
    __IO uint32_t PAD_SIP08;
    __IO uint32_t PAD_SIP09;
    __IO uint32_t PAD_SIP10;
    __IO uint32_t PAD_SIP11;
    __IO uint32_t PAD_SIP12;
    __IO uint32_t PAD_SIP13;
    __IO uint32_t PAD_SIP14;
    __IO uint32_t PAD_SIP15;
    __IO uint32_t PAD_SIP16;
    __IO uint32_t PAD_SIP17;
    __IO uint32_t PAD_SIP18;
    __IO uint32_t PAD_PA00;
    __IO uint32_t PAD_PA01;
    __IO uint32_t PAD_PA02;
    __IO uint32_t PAD_PA03;
    __IO uint32_t PAD_PA04;
    __IO uint32_t PAD_PA05;
    __IO uint32_t PAD_PA06;
    __IO uint32_t PAD_PA07;
    __IO uint32_t PAD_PA08;
    __IO uint32_t PAD_PA09;
    __IO uint32_t PAD_PA10;
    __IO uint32_t PAD_PA11;
    __IO uint32_t PAD_PA12;
    __IO uint32_t PAD_PA13;
    __IO uint32_t PAD_PA14;
    __IO uint32_t PAD_PA15;
    __IO uint32_t PAD_PA16;
    __IO uint32_t PAD_PA17;
    __IO uint32_t PAD_PA18;
    __IO uint32_t PAD_PA19;
    __IO uint32_t PAD_PA20;
    __IO uint32_t PAD_PA21;
    __IO uint32_t PAD_PA22;
    __IO uint32_t PAD_PA23;
    __IO uint32_t PAD_PA24;
    __IO uint32_t PAD_PA25;
    __IO uint32_t PAD_PA26;
    __IO uint32_t PAD_PA27;
    __IO uint32_t PAD_PA28;
    __IO uint32_t PAD_PA29;
    __IO uint32_t PAD_PA30;
    __IO uint32_t PAD_PA31;
    __IO uint32_t PAD_PA32;
    __IO uint32_t PAD_PA33;
    __IO uint32_t PAD_PA34;
    __IO uint32_t PAD_PA35;
    __IO uint32_t PAD_PA36;
    __IO uint32_t PAD_PA37;
    __IO uint32_t PAD_PA38;
    __IO uint32_t PAD_PA39;
    __IO uint32_t PAD_PA40;
    __IO uint32_t PAD_PA41;
    __IO uint32_t PAD_PA42;
    __IO uint32_t PAD_PA43;
    __IO uint32_t PAD_PA44;
    __IO uint32_t PAD_PA45;
    __IO uint32_t PAD_PA46;
    __IO uint32_t PAD_PA47;
    __IO uint32_t PAD_PA48;
    __IO uint32_t PAD_PA49;
    __IO uint32_t PAD_PA50;
    __IO uint32_t PAD_PA51;
    __IO uint32_t PAD_PA52;
    __IO uint32_t PAD_PA53;
    __IO uint32_t PAD_PA54;
    __IO uint32_t PAD_PA55;
    __IO uint32_t PAD_PA56;
    __IO uint32_t PAD_PA57;
    __IO uint32_t PAD_PA58;
    __IO uint32_t PAD_PA59;
    __IO uint32_t PAD_PA60;
    __IO uint32_t PAD_PA61;
    __IO uint32_t PAD_PA62;
    __IO uint32_t PAD_PA63;
    __IO uint32_t PAD_PA64;
    __IO uint32_t PAD_PA65;
    __IO uint32_t PAD_PA66;
    __IO uint32_t PAD_PA67;
    __IO uint32_t PAD_PA68;
    __IO uint32_t PAD_PA69;
    __IO uint32_t PAD_PA70;
    __IO uint32_t PAD_PA71;
    __IO uint32_t PAD_PA72;
    __IO uint32_t PAD_PA73;
    __IO uint32_t PAD_PA74;
    __IO uint32_t PAD_PA75;
    __IO uint32_t PAD_PA76;
    __IO uint32_t PAD_PA77;
    __IO uint32_t PAD_PA78;
    __IO uint32_t PAD_PA79;
    __IO uint32_t PAD_PA80;
    __IO uint32_t CR;
} HPSYS_PINMUX_TypeDef;

#define HPSYS_PAD_NUM    (100)

/************* Bit definition for HPSYS_PINMUX_PAD_SIP00 register *************/
#define HPSYS_PINMUX_PAD_SIP00_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP00_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP00_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP00_FSEL     HPSYS_PINMUX_PAD_SIP00_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP00_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP00_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP00_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP00_PE       HPSYS_PINMUX_PAD_SIP00_PE_Msk
#define HPSYS_PINMUX_PAD_SIP00_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP00_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP00_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP00_PS       HPSYS_PINMUX_PAD_SIP00_PS_Msk
#define HPSYS_PINMUX_PAD_SIP00_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP00_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP00_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP00_IE       HPSYS_PINMUX_PAD_SIP00_IE_Msk
#define HPSYS_PINMUX_PAD_SIP00_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP00_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP00_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP00_IS       HPSYS_PINMUX_PAD_SIP00_IS_Msk
#define HPSYS_PINMUX_PAD_SIP00_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP00_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP00_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP00_SR       HPSYS_PINMUX_PAD_SIP00_SR_Msk
#define HPSYS_PINMUX_PAD_SIP00_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP00_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP00_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP00_DS0      HPSYS_PINMUX_PAD_SIP00_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP00_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP00_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP00_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP00_DS1      HPSYS_PINMUX_PAD_SIP00_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP00_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP00_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP00_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP00_POE      HPSYS_PINMUX_PAD_SIP00_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP01 register *************/
#define HPSYS_PINMUX_PAD_SIP01_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP01_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP01_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP01_FSEL     HPSYS_PINMUX_PAD_SIP01_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP01_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP01_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP01_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP01_PE       HPSYS_PINMUX_PAD_SIP01_PE_Msk
#define HPSYS_PINMUX_PAD_SIP01_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP01_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP01_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP01_PS       HPSYS_PINMUX_PAD_SIP01_PS_Msk
#define HPSYS_PINMUX_PAD_SIP01_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP01_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP01_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP01_IE       HPSYS_PINMUX_PAD_SIP01_IE_Msk
#define HPSYS_PINMUX_PAD_SIP01_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP01_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP01_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP01_IS       HPSYS_PINMUX_PAD_SIP01_IS_Msk
#define HPSYS_PINMUX_PAD_SIP01_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP01_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP01_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP01_SR       HPSYS_PINMUX_PAD_SIP01_SR_Msk
#define HPSYS_PINMUX_PAD_SIP01_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP01_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP01_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP01_DS0      HPSYS_PINMUX_PAD_SIP01_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP01_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP01_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP01_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP01_DS1      HPSYS_PINMUX_PAD_SIP01_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP01_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP01_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP01_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP01_POE      HPSYS_PINMUX_PAD_SIP01_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP02 register *************/
#define HPSYS_PINMUX_PAD_SIP02_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP02_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP02_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP02_FSEL     HPSYS_PINMUX_PAD_SIP02_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP02_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP02_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP02_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP02_PE       HPSYS_PINMUX_PAD_SIP02_PE_Msk
#define HPSYS_PINMUX_PAD_SIP02_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP02_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP02_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP02_PS       HPSYS_PINMUX_PAD_SIP02_PS_Msk
#define HPSYS_PINMUX_PAD_SIP02_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP02_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP02_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP02_IE       HPSYS_PINMUX_PAD_SIP02_IE_Msk
#define HPSYS_PINMUX_PAD_SIP02_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP02_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP02_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP02_IS       HPSYS_PINMUX_PAD_SIP02_IS_Msk
#define HPSYS_PINMUX_PAD_SIP02_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP02_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP02_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP02_SR       HPSYS_PINMUX_PAD_SIP02_SR_Msk
#define HPSYS_PINMUX_PAD_SIP02_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP02_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP02_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP02_DS0      HPSYS_PINMUX_PAD_SIP02_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP02_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP02_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP02_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP02_DS1      HPSYS_PINMUX_PAD_SIP02_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP02_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP02_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP02_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP02_POE      HPSYS_PINMUX_PAD_SIP02_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP03 register *************/
#define HPSYS_PINMUX_PAD_SIP03_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP03_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP03_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP03_FSEL     HPSYS_PINMUX_PAD_SIP03_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP03_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP03_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP03_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP03_PE       HPSYS_PINMUX_PAD_SIP03_PE_Msk
#define HPSYS_PINMUX_PAD_SIP03_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP03_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP03_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP03_PS       HPSYS_PINMUX_PAD_SIP03_PS_Msk
#define HPSYS_PINMUX_PAD_SIP03_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP03_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP03_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP03_IE       HPSYS_PINMUX_PAD_SIP03_IE_Msk
#define HPSYS_PINMUX_PAD_SIP03_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP03_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP03_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP03_IS       HPSYS_PINMUX_PAD_SIP03_IS_Msk
#define HPSYS_PINMUX_PAD_SIP03_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP03_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP03_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP03_SR       HPSYS_PINMUX_PAD_SIP03_SR_Msk
#define HPSYS_PINMUX_PAD_SIP03_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP03_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP03_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP03_DS0      HPSYS_PINMUX_PAD_SIP03_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP03_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP03_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP03_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP03_DS1      HPSYS_PINMUX_PAD_SIP03_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP03_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP03_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP03_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP03_POE      HPSYS_PINMUX_PAD_SIP03_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP04 register *************/
#define HPSYS_PINMUX_PAD_SIP04_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP04_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP04_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP04_FSEL     HPSYS_PINMUX_PAD_SIP04_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP04_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP04_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP04_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP04_PE       HPSYS_PINMUX_PAD_SIP04_PE_Msk
#define HPSYS_PINMUX_PAD_SIP04_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP04_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP04_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP04_PS       HPSYS_PINMUX_PAD_SIP04_PS_Msk
#define HPSYS_PINMUX_PAD_SIP04_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP04_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP04_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP04_IE       HPSYS_PINMUX_PAD_SIP04_IE_Msk
#define HPSYS_PINMUX_PAD_SIP04_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP04_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP04_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP04_IS       HPSYS_PINMUX_PAD_SIP04_IS_Msk
#define HPSYS_PINMUX_PAD_SIP04_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP04_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP04_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP04_SR       HPSYS_PINMUX_PAD_SIP04_SR_Msk
#define HPSYS_PINMUX_PAD_SIP04_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP04_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP04_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP04_DS0      HPSYS_PINMUX_PAD_SIP04_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP04_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP04_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP04_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP04_DS1      HPSYS_PINMUX_PAD_SIP04_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP04_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP04_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP04_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP04_POE      HPSYS_PINMUX_PAD_SIP04_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP05 register *************/
#define HPSYS_PINMUX_PAD_SIP05_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP05_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP05_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP05_FSEL     HPSYS_PINMUX_PAD_SIP05_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP05_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP05_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP05_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP05_PE       HPSYS_PINMUX_PAD_SIP05_PE_Msk
#define HPSYS_PINMUX_PAD_SIP05_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP05_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP05_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP05_PS       HPSYS_PINMUX_PAD_SIP05_PS_Msk
#define HPSYS_PINMUX_PAD_SIP05_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP05_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP05_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP05_IE       HPSYS_PINMUX_PAD_SIP05_IE_Msk
#define HPSYS_PINMUX_PAD_SIP05_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP05_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP05_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP05_IS       HPSYS_PINMUX_PAD_SIP05_IS_Msk
#define HPSYS_PINMUX_PAD_SIP05_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP05_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP05_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP05_SR       HPSYS_PINMUX_PAD_SIP05_SR_Msk
#define HPSYS_PINMUX_PAD_SIP05_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP05_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP05_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP05_DS0      HPSYS_PINMUX_PAD_SIP05_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP05_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP05_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP05_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP05_DS1      HPSYS_PINMUX_PAD_SIP05_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP05_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP05_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP05_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP05_POE      HPSYS_PINMUX_PAD_SIP05_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP06 register *************/
#define HPSYS_PINMUX_PAD_SIP06_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP06_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP06_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP06_FSEL     HPSYS_PINMUX_PAD_SIP06_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP06_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP06_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP06_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP06_PE       HPSYS_PINMUX_PAD_SIP06_PE_Msk
#define HPSYS_PINMUX_PAD_SIP06_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP06_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP06_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP06_PS       HPSYS_PINMUX_PAD_SIP06_PS_Msk
#define HPSYS_PINMUX_PAD_SIP06_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP06_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP06_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP06_IE       HPSYS_PINMUX_PAD_SIP06_IE_Msk
#define HPSYS_PINMUX_PAD_SIP06_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP06_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP06_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP06_IS       HPSYS_PINMUX_PAD_SIP06_IS_Msk
#define HPSYS_PINMUX_PAD_SIP06_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP06_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP06_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP06_SR       HPSYS_PINMUX_PAD_SIP06_SR_Msk
#define HPSYS_PINMUX_PAD_SIP06_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP06_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP06_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP06_DS0      HPSYS_PINMUX_PAD_SIP06_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP06_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP06_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP06_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP06_DS1      HPSYS_PINMUX_PAD_SIP06_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP06_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP06_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP06_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP06_POE      HPSYS_PINMUX_PAD_SIP06_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP07 register *************/
#define HPSYS_PINMUX_PAD_SIP07_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP07_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP07_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP07_FSEL     HPSYS_PINMUX_PAD_SIP07_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP07_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP07_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP07_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP07_PE       HPSYS_PINMUX_PAD_SIP07_PE_Msk
#define HPSYS_PINMUX_PAD_SIP07_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP07_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP07_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP07_PS       HPSYS_PINMUX_PAD_SIP07_PS_Msk
#define HPSYS_PINMUX_PAD_SIP07_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP07_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP07_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP07_IE       HPSYS_PINMUX_PAD_SIP07_IE_Msk
#define HPSYS_PINMUX_PAD_SIP07_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP07_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP07_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP07_IS       HPSYS_PINMUX_PAD_SIP07_IS_Msk
#define HPSYS_PINMUX_PAD_SIP07_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP07_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP07_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP07_SR       HPSYS_PINMUX_PAD_SIP07_SR_Msk
#define HPSYS_PINMUX_PAD_SIP07_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP07_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP07_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP07_DS0      HPSYS_PINMUX_PAD_SIP07_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP07_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP07_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP07_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP07_DS1      HPSYS_PINMUX_PAD_SIP07_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP07_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP07_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP07_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP07_POE      HPSYS_PINMUX_PAD_SIP07_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP08 register *************/
#define HPSYS_PINMUX_PAD_SIP08_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP08_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP08_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP08_FSEL     HPSYS_PINMUX_PAD_SIP08_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP08_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP08_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP08_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP08_PE       HPSYS_PINMUX_PAD_SIP08_PE_Msk
#define HPSYS_PINMUX_PAD_SIP08_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP08_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP08_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP08_PS       HPSYS_PINMUX_PAD_SIP08_PS_Msk
#define HPSYS_PINMUX_PAD_SIP08_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP08_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP08_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP08_IE       HPSYS_PINMUX_PAD_SIP08_IE_Msk
#define HPSYS_PINMUX_PAD_SIP08_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP08_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP08_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP08_IS       HPSYS_PINMUX_PAD_SIP08_IS_Msk
#define HPSYS_PINMUX_PAD_SIP08_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP08_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP08_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP08_SR       HPSYS_PINMUX_PAD_SIP08_SR_Msk
#define HPSYS_PINMUX_PAD_SIP08_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP08_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP08_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP08_DS0      HPSYS_PINMUX_PAD_SIP08_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP08_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP08_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP08_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP08_DS1      HPSYS_PINMUX_PAD_SIP08_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP08_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP08_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP08_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP08_POE      HPSYS_PINMUX_PAD_SIP08_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP09 register *************/
#define HPSYS_PINMUX_PAD_SIP09_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP09_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP09_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP09_FSEL     HPSYS_PINMUX_PAD_SIP09_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP09_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP09_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP09_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP09_PE       HPSYS_PINMUX_PAD_SIP09_PE_Msk
#define HPSYS_PINMUX_PAD_SIP09_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP09_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP09_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP09_PS       HPSYS_PINMUX_PAD_SIP09_PS_Msk
#define HPSYS_PINMUX_PAD_SIP09_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP09_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP09_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP09_IE       HPSYS_PINMUX_PAD_SIP09_IE_Msk
#define HPSYS_PINMUX_PAD_SIP09_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP09_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP09_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP09_IS       HPSYS_PINMUX_PAD_SIP09_IS_Msk
#define HPSYS_PINMUX_PAD_SIP09_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP09_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP09_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP09_SR       HPSYS_PINMUX_PAD_SIP09_SR_Msk
#define HPSYS_PINMUX_PAD_SIP09_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP09_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP09_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP09_DS0      HPSYS_PINMUX_PAD_SIP09_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP09_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP09_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP09_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP09_DS1      HPSYS_PINMUX_PAD_SIP09_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP09_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP09_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP09_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP09_POE      HPSYS_PINMUX_PAD_SIP09_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP10 register *************/
#define HPSYS_PINMUX_PAD_SIP10_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP10_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP10_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP10_FSEL     HPSYS_PINMUX_PAD_SIP10_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP10_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP10_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP10_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP10_PE       HPSYS_PINMUX_PAD_SIP10_PE_Msk
#define HPSYS_PINMUX_PAD_SIP10_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP10_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP10_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP10_PS       HPSYS_PINMUX_PAD_SIP10_PS_Msk
#define HPSYS_PINMUX_PAD_SIP10_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP10_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP10_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP10_IE       HPSYS_PINMUX_PAD_SIP10_IE_Msk
#define HPSYS_PINMUX_PAD_SIP10_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP10_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP10_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP10_IS       HPSYS_PINMUX_PAD_SIP10_IS_Msk
#define HPSYS_PINMUX_PAD_SIP10_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP10_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP10_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP10_SR       HPSYS_PINMUX_PAD_SIP10_SR_Msk
#define HPSYS_PINMUX_PAD_SIP10_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP10_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP10_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP10_DS0      HPSYS_PINMUX_PAD_SIP10_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP10_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP10_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP10_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP10_DS1      HPSYS_PINMUX_PAD_SIP10_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP10_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP10_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP10_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP10_POE      HPSYS_PINMUX_PAD_SIP10_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP11 register *************/
#define HPSYS_PINMUX_PAD_SIP11_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP11_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP11_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP11_FSEL     HPSYS_PINMUX_PAD_SIP11_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP11_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP11_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP11_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP11_PE       HPSYS_PINMUX_PAD_SIP11_PE_Msk
#define HPSYS_PINMUX_PAD_SIP11_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP11_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP11_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP11_PS       HPSYS_PINMUX_PAD_SIP11_PS_Msk
#define HPSYS_PINMUX_PAD_SIP11_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP11_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP11_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP11_IE       HPSYS_PINMUX_PAD_SIP11_IE_Msk
#define HPSYS_PINMUX_PAD_SIP11_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP11_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP11_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP11_IS       HPSYS_PINMUX_PAD_SIP11_IS_Msk
#define HPSYS_PINMUX_PAD_SIP11_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP11_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP11_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP11_SR       HPSYS_PINMUX_PAD_SIP11_SR_Msk
#define HPSYS_PINMUX_PAD_SIP11_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP11_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP11_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP11_DS0      HPSYS_PINMUX_PAD_SIP11_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP11_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP11_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP11_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP11_DS1      HPSYS_PINMUX_PAD_SIP11_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP11_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP11_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP11_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP11_POE      HPSYS_PINMUX_PAD_SIP11_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP12 register *************/
#define HPSYS_PINMUX_PAD_SIP12_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP12_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP12_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP12_FSEL     HPSYS_PINMUX_PAD_SIP12_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP12_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP12_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP12_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP12_PE       HPSYS_PINMUX_PAD_SIP12_PE_Msk
#define HPSYS_PINMUX_PAD_SIP12_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP12_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP12_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP12_PS       HPSYS_PINMUX_PAD_SIP12_PS_Msk
#define HPSYS_PINMUX_PAD_SIP12_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP12_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP12_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP12_IE       HPSYS_PINMUX_PAD_SIP12_IE_Msk
#define HPSYS_PINMUX_PAD_SIP12_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP12_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP12_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP12_IS       HPSYS_PINMUX_PAD_SIP12_IS_Msk
#define HPSYS_PINMUX_PAD_SIP12_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP12_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP12_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP12_SR       HPSYS_PINMUX_PAD_SIP12_SR_Msk
#define HPSYS_PINMUX_PAD_SIP12_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP12_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP12_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP12_DS0      HPSYS_PINMUX_PAD_SIP12_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP12_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP12_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP12_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP12_DS1      HPSYS_PINMUX_PAD_SIP12_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP12_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP12_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP12_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP12_POE      HPSYS_PINMUX_PAD_SIP12_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP13 register *************/
#define HPSYS_PINMUX_PAD_SIP13_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP13_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP13_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP13_FSEL     HPSYS_PINMUX_PAD_SIP13_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP13_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP13_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP13_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP13_PE       HPSYS_PINMUX_PAD_SIP13_PE_Msk
#define HPSYS_PINMUX_PAD_SIP13_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP13_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP13_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP13_PS       HPSYS_PINMUX_PAD_SIP13_PS_Msk
#define HPSYS_PINMUX_PAD_SIP13_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP13_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP13_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP13_IE       HPSYS_PINMUX_PAD_SIP13_IE_Msk
#define HPSYS_PINMUX_PAD_SIP13_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP13_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP13_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP13_IS       HPSYS_PINMUX_PAD_SIP13_IS_Msk
#define HPSYS_PINMUX_PAD_SIP13_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP13_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP13_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP13_SR       HPSYS_PINMUX_PAD_SIP13_SR_Msk
#define HPSYS_PINMUX_PAD_SIP13_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP13_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP13_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP13_DS0      HPSYS_PINMUX_PAD_SIP13_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP13_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP13_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP13_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP13_DS1      HPSYS_PINMUX_PAD_SIP13_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP13_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP13_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP13_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP13_POE      HPSYS_PINMUX_PAD_SIP13_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP14 register *************/
#define HPSYS_PINMUX_PAD_SIP14_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP14_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP14_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP14_FSEL     HPSYS_PINMUX_PAD_SIP14_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP14_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP14_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP14_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP14_PE       HPSYS_PINMUX_PAD_SIP14_PE_Msk
#define HPSYS_PINMUX_PAD_SIP14_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP14_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP14_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP14_PS       HPSYS_PINMUX_PAD_SIP14_PS_Msk
#define HPSYS_PINMUX_PAD_SIP14_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP14_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP14_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP14_IE       HPSYS_PINMUX_PAD_SIP14_IE_Msk
#define HPSYS_PINMUX_PAD_SIP14_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP14_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP14_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP14_IS       HPSYS_PINMUX_PAD_SIP14_IS_Msk
#define HPSYS_PINMUX_PAD_SIP14_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP14_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP14_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP14_SR       HPSYS_PINMUX_PAD_SIP14_SR_Msk
#define HPSYS_PINMUX_PAD_SIP14_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP14_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP14_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP14_DS0      HPSYS_PINMUX_PAD_SIP14_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP14_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP14_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP14_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP14_DS1      HPSYS_PINMUX_PAD_SIP14_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP14_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP14_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP14_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP14_POE      HPSYS_PINMUX_PAD_SIP14_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP15 register *************/
#define HPSYS_PINMUX_PAD_SIP15_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP15_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP15_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP15_FSEL     HPSYS_PINMUX_PAD_SIP15_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP15_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP15_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP15_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP15_PE       HPSYS_PINMUX_PAD_SIP15_PE_Msk
#define HPSYS_PINMUX_PAD_SIP15_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP15_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP15_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP15_PS       HPSYS_PINMUX_PAD_SIP15_PS_Msk
#define HPSYS_PINMUX_PAD_SIP15_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP15_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP15_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP15_IE       HPSYS_PINMUX_PAD_SIP15_IE_Msk
#define HPSYS_PINMUX_PAD_SIP15_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP15_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP15_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP15_IS       HPSYS_PINMUX_PAD_SIP15_IS_Msk
#define HPSYS_PINMUX_PAD_SIP15_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP15_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP15_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP15_SR       HPSYS_PINMUX_PAD_SIP15_SR_Msk
#define HPSYS_PINMUX_PAD_SIP15_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP15_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP15_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP15_DS0      HPSYS_PINMUX_PAD_SIP15_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP15_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP15_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP15_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP15_DS1      HPSYS_PINMUX_PAD_SIP15_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP15_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP15_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP15_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP15_POE      HPSYS_PINMUX_PAD_SIP15_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP16 register *************/
#define HPSYS_PINMUX_PAD_SIP16_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP16_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP16_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP16_FSEL     HPSYS_PINMUX_PAD_SIP16_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP16_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP16_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP16_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP16_PE       HPSYS_PINMUX_PAD_SIP16_PE_Msk
#define HPSYS_PINMUX_PAD_SIP16_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP16_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP16_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP16_PS       HPSYS_PINMUX_PAD_SIP16_PS_Msk
#define HPSYS_PINMUX_PAD_SIP16_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP16_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP16_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP16_IE       HPSYS_PINMUX_PAD_SIP16_IE_Msk
#define HPSYS_PINMUX_PAD_SIP16_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP16_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP16_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP16_IS       HPSYS_PINMUX_PAD_SIP16_IS_Msk
#define HPSYS_PINMUX_PAD_SIP16_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP16_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP16_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP16_SR       HPSYS_PINMUX_PAD_SIP16_SR_Msk
#define HPSYS_PINMUX_PAD_SIP16_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP16_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP16_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP16_DS0      HPSYS_PINMUX_PAD_SIP16_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP16_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP16_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP16_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP16_DS1      HPSYS_PINMUX_PAD_SIP16_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP16_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP16_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP16_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP16_POE      HPSYS_PINMUX_PAD_SIP16_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP17 register *************/
#define HPSYS_PINMUX_PAD_SIP17_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP17_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP17_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP17_FSEL     HPSYS_PINMUX_PAD_SIP17_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP17_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP17_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP17_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP17_PE       HPSYS_PINMUX_PAD_SIP17_PE_Msk
#define HPSYS_PINMUX_PAD_SIP17_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP17_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP17_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP17_PS       HPSYS_PINMUX_PAD_SIP17_PS_Msk
#define HPSYS_PINMUX_PAD_SIP17_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP17_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP17_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP17_IE       HPSYS_PINMUX_PAD_SIP17_IE_Msk
#define HPSYS_PINMUX_PAD_SIP17_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP17_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP17_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP17_IS       HPSYS_PINMUX_PAD_SIP17_IS_Msk
#define HPSYS_PINMUX_PAD_SIP17_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP17_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP17_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP17_SR       HPSYS_PINMUX_PAD_SIP17_SR_Msk
#define HPSYS_PINMUX_PAD_SIP17_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP17_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP17_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP17_DS0      HPSYS_PINMUX_PAD_SIP17_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP17_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP17_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP17_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP17_DS1      HPSYS_PINMUX_PAD_SIP17_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP17_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP17_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP17_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP17_POE      HPSYS_PINMUX_PAD_SIP17_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SIP18 register *************/
#define HPSYS_PINMUX_PAD_SIP18_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SIP18_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SIP18_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SIP18_FSEL     HPSYS_PINMUX_PAD_SIP18_FSEL_Msk
#define HPSYS_PINMUX_PAD_SIP18_PE_Pos   (4U)
#define HPSYS_PINMUX_PAD_SIP18_PE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP18_PE_Pos)
#define HPSYS_PINMUX_PAD_SIP18_PE       HPSYS_PINMUX_PAD_SIP18_PE_Msk
#define HPSYS_PINMUX_PAD_SIP18_PS_Pos   (5U)
#define HPSYS_PINMUX_PAD_SIP18_PS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP18_PS_Pos)
#define HPSYS_PINMUX_PAD_SIP18_PS       HPSYS_PINMUX_PAD_SIP18_PS_Msk
#define HPSYS_PINMUX_PAD_SIP18_IE_Pos   (6U)
#define HPSYS_PINMUX_PAD_SIP18_IE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP18_IE_Pos)
#define HPSYS_PINMUX_PAD_SIP18_IE       HPSYS_PINMUX_PAD_SIP18_IE_Msk
#define HPSYS_PINMUX_PAD_SIP18_IS_Pos   (7U)
#define HPSYS_PINMUX_PAD_SIP18_IS_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP18_IS_Pos)
#define HPSYS_PINMUX_PAD_SIP18_IS       HPSYS_PINMUX_PAD_SIP18_IS_Msk
#define HPSYS_PINMUX_PAD_SIP18_SR_Pos   (8U)
#define HPSYS_PINMUX_PAD_SIP18_SR_Msk   (0x1UL << HPSYS_PINMUX_PAD_SIP18_SR_Pos)
#define HPSYS_PINMUX_PAD_SIP18_SR       HPSYS_PINMUX_PAD_SIP18_SR_Msk
#define HPSYS_PINMUX_PAD_SIP18_DS0_Pos  (9U)
#define HPSYS_PINMUX_PAD_SIP18_DS0_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP18_DS0_Pos)
#define HPSYS_PINMUX_PAD_SIP18_DS0      HPSYS_PINMUX_PAD_SIP18_DS0_Msk
#define HPSYS_PINMUX_PAD_SIP18_DS1_Pos  (10U)
#define HPSYS_PINMUX_PAD_SIP18_DS1_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP18_DS1_Pos)
#define HPSYS_PINMUX_PAD_SIP18_DS1      HPSYS_PINMUX_PAD_SIP18_DS1_Msk
#define HPSYS_PINMUX_PAD_SIP18_POE_Pos  (11U)
#define HPSYS_PINMUX_PAD_SIP18_POE_Msk  (0x1UL << HPSYS_PINMUX_PAD_SIP18_POE_Pos)
#define HPSYS_PINMUX_PAD_SIP18_POE      HPSYS_PINMUX_PAD_SIP18_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA00 register **************/
#define HPSYS_PINMUX_PAD_PA00_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA00_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA00_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA00_FSEL      HPSYS_PINMUX_PAD_PA00_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA00_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA00_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_PE_Pos)
#define HPSYS_PINMUX_PAD_PA00_PE        HPSYS_PINMUX_PAD_PA00_PE_Msk
#define HPSYS_PINMUX_PAD_PA00_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA00_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_PS_Pos)
#define HPSYS_PINMUX_PAD_PA00_PS        HPSYS_PINMUX_PAD_PA00_PS_Msk
#define HPSYS_PINMUX_PAD_PA00_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA00_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_IE_Pos)
#define HPSYS_PINMUX_PAD_PA00_IE        HPSYS_PINMUX_PAD_PA00_IE_Msk
#define HPSYS_PINMUX_PAD_PA00_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA00_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_IS_Pos)
#define HPSYS_PINMUX_PAD_PA00_IS        HPSYS_PINMUX_PAD_PA00_IS_Msk
#define HPSYS_PINMUX_PAD_PA00_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA00_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_SR_Pos)
#define HPSYS_PINMUX_PAD_PA00_SR        HPSYS_PINMUX_PAD_PA00_SR_Msk
#define HPSYS_PINMUX_PAD_PA00_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA00_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA00_DS0       HPSYS_PINMUX_PAD_PA00_DS0_Msk
#define HPSYS_PINMUX_PAD_PA00_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA00_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA00_DS1       HPSYS_PINMUX_PAD_PA00_DS1_Msk
#define HPSYS_PINMUX_PAD_PA00_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA00_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_POE_Pos)
#define HPSYS_PINMUX_PAD_PA00_POE       HPSYS_PINMUX_PAD_PA00_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA01 register **************/
#define HPSYS_PINMUX_PAD_PA01_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA01_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA01_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA01_FSEL      HPSYS_PINMUX_PAD_PA01_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA01_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA01_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_PE_Pos)
#define HPSYS_PINMUX_PAD_PA01_PE        HPSYS_PINMUX_PAD_PA01_PE_Msk
#define HPSYS_PINMUX_PAD_PA01_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA01_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_PS_Pos)
#define HPSYS_PINMUX_PAD_PA01_PS        HPSYS_PINMUX_PAD_PA01_PS_Msk
#define HPSYS_PINMUX_PAD_PA01_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA01_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_IE_Pos)
#define HPSYS_PINMUX_PAD_PA01_IE        HPSYS_PINMUX_PAD_PA01_IE_Msk
#define HPSYS_PINMUX_PAD_PA01_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA01_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_IS_Pos)
#define HPSYS_PINMUX_PAD_PA01_IS        HPSYS_PINMUX_PAD_PA01_IS_Msk
#define HPSYS_PINMUX_PAD_PA01_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA01_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_SR_Pos)
#define HPSYS_PINMUX_PAD_PA01_SR        HPSYS_PINMUX_PAD_PA01_SR_Msk
#define HPSYS_PINMUX_PAD_PA01_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA01_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA01_DS0       HPSYS_PINMUX_PAD_PA01_DS0_Msk
#define HPSYS_PINMUX_PAD_PA01_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA01_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA01_DS1       HPSYS_PINMUX_PAD_PA01_DS1_Msk
#define HPSYS_PINMUX_PAD_PA01_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA01_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_POE_Pos)
#define HPSYS_PINMUX_PAD_PA01_POE       HPSYS_PINMUX_PAD_PA01_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA02 register **************/
#define HPSYS_PINMUX_PAD_PA02_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA02_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA02_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA02_FSEL      HPSYS_PINMUX_PAD_PA02_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA02_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA02_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_PE_Pos)
#define HPSYS_PINMUX_PAD_PA02_PE        HPSYS_PINMUX_PAD_PA02_PE_Msk
#define HPSYS_PINMUX_PAD_PA02_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA02_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_PS_Pos)
#define HPSYS_PINMUX_PAD_PA02_PS        HPSYS_PINMUX_PAD_PA02_PS_Msk
#define HPSYS_PINMUX_PAD_PA02_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA02_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_IE_Pos)
#define HPSYS_PINMUX_PAD_PA02_IE        HPSYS_PINMUX_PAD_PA02_IE_Msk
#define HPSYS_PINMUX_PAD_PA02_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA02_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_IS_Pos)
#define HPSYS_PINMUX_PAD_PA02_IS        HPSYS_PINMUX_PAD_PA02_IS_Msk
#define HPSYS_PINMUX_PAD_PA02_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA02_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_SR_Pos)
#define HPSYS_PINMUX_PAD_PA02_SR        HPSYS_PINMUX_PAD_PA02_SR_Msk
#define HPSYS_PINMUX_PAD_PA02_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA02_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA02_DS0       HPSYS_PINMUX_PAD_PA02_DS0_Msk
#define HPSYS_PINMUX_PAD_PA02_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA02_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA02_DS1       HPSYS_PINMUX_PAD_PA02_DS1_Msk
#define HPSYS_PINMUX_PAD_PA02_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA02_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_POE_Pos)
#define HPSYS_PINMUX_PAD_PA02_POE       HPSYS_PINMUX_PAD_PA02_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA03 register **************/
#define HPSYS_PINMUX_PAD_PA03_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA03_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA03_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA03_FSEL      HPSYS_PINMUX_PAD_PA03_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA03_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA03_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_PE_Pos)
#define HPSYS_PINMUX_PAD_PA03_PE        HPSYS_PINMUX_PAD_PA03_PE_Msk
#define HPSYS_PINMUX_PAD_PA03_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA03_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_PS_Pos)
#define HPSYS_PINMUX_PAD_PA03_PS        HPSYS_PINMUX_PAD_PA03_PS_Msk
#define HPSYS_PINMUX_PAD_PA03_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA03_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_IE_Pos)
#define HPSYS_PINMUX_PAD_PA03_IE        HPSYS_PINMUX_PAD_PA03_IE_Msk
#define HPSYS_PINMUX_PAD_PA03_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA03_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_IS_Pos)
#define HPSYS_PINMUX_PAD_PA03_IS        HPSYS_PINMUX_PAD_PA03_IS_Msk
#define HPSYS_PINMUX_PAD_PA03_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA03_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_SR_Pos)
#define HPSYS_PINMUX_PAD_PA03_SR        HPSYS_PINMUX_PAD_PA03_SR_Msk
#define HPSYS_PINMUX_PAD_PA03_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA03_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA03_DS0       HPSYS_PINMUX_PAD_PA03_DS0_Msk
#define HPSYS_PINMUX_PAD_PA03_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA03_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA03_DS1       HPSYS_PINMUX_PAD_PA03_DS1_Msk
#define HPSYS_PINMUX_PAD_PA03_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA03_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_POE_Pos)
#define HPSYS_PINMUX_PAD_PA03_POE       HPSYS_PINMUX_PAD_PA03_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA04 register **************/
#define HPSYS_PINMUX_PAD_PA04_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA04_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA04_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA04_FSEL      HPSYS_PINMUX_PAD_PA04_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA04_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA04_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_PE_Pos)
#define HPSYS_PINMUX_PAD_PA04_PE        HPSYS_PINMUX_PAD_PA04_PE_Msk
#define HPSYS_PINMUX_PAD_PA04_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA04_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_PS_Pos)
#define HPSYS_PINMUX_PAD_PA04_PS        HPSYS_PINMUX_PAD_PA04_PS_Msk
#define HPSYS_PINMUX_PAD_PA04_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA04_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_IE_Pos)
#define HPSYS_PINMUX_PAD_PA04_IE        HPSYS_PINMUX_PAD_PA04_IE_Msk
#define HPSYS_PINMUX_PAD_PA04_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA04_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_IS_Pos)
#define HPSYS_PINMUX_PAD_PA04_IS        HPSYS_PINMUX_PAD_PA04_IS_Msk
#define HPSYS_PINMUX_PAD_PA04_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA04_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_SR_Pos)
#define HPSYS_PINMUX_PAD_PA04_SR        HPSYS_PINMUX_PAD_PA04_SR_Msk
#define HPSYS_PINMUX_PAD_PA04_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA04_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA04_DS0       HPSYS_PINMUX_PAD_PA04_DS0_Msk
#define HPSYS_PINMUX_PAD_PA04_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA04_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA04_DS1       HPSYS_PINMUX_PAD_PA04_DS1_Msk
#define HPSYS_PINMUX_PAD_PA04_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA04_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_POE_Pos)
#define HPSYS_PINMUX_PAD_PA04_POE       HPSYS_PINMUX_PAD_PA04_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA05 register **************/
#define HPSYS_PINMUX_PAD_PA05_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA05_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA05_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA05_FSEL      HPSYS_PINMUX_PAD_PA05_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA05_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA05_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_PE_Pos)
#define HPSYS_PINMUX_PAD_PA05_PE        HPSYS_PINMUX_PAD_PA05_PE_Msk
#define HPSYS_PINMUX_PAD_PA05_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA05_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_PS_Pos)
#define HPSYS_PINMUX_PAD_PA05_PS        HPSYS_PINMUX_PAD_PA05_PS_Msk
#define HPSYS_PINMUX_PAD_PA05_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA05_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_IE_Pos)
#define HPSYS_PINMUX_PAD_PA05_IE        HPSYS_PINMUX_PAD_PA05_IE_Msk
#define HPSYS_PINMUX_PAD_PA05_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA05_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_IS_Pos)
#define HPSYS_PINMUX_PAD_PA05_IS        HPSYS_PINMUX_PAD_PA05_IS_Msk
#define HPSYS_PINMUX_PAD_PA05_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA05_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_SR_Pos)
#define HPSYS_PINMUX_PAD_PA05_SR        HPSYS_PINMUX_PAD_PA05_SR_Msk
#define HPSYS_PINMUX_PAD_PA05_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA05_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA05_DS0       HPSYS_PINMUX_PAD_PA05_DS0_Msk
#define HPSYS_PINMUX_PAD_PA05_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA05_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA05_DS1       HPSYS_PINMUX_PAD_PA05_DS1_Msk
#define HPSYS_PINMUX_PAD_PA05_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA05_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_POE_Pos)
#define HPSYS_PINMUX_PAD_PA05_POE       HPSYS_PINMUX_PAD_PA05_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA06 register **************/
#define HPSYS_PINMUX_PAD_PA06_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA06_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA06_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA06_FSEL      HPSYS_PINMUX_PAD_PA06_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA06_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA06_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_PE_Pos)
#define HPSYS_PINMUX_PAD_PA06_PE        HPSYS_PINMUX_PAD_PA06_PE_Msk
#define HPSYS_PINMUX_PAD_PA06_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA06_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_PS_Pos)
#define HPSYS_PINMUX_PAD_PA06_PS        HPSYS_PINMUX_PAD_PA06_PS_Msk
#define HPSYS_PINMUX_PAD_PA06_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA06_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_IE_Pos)
#define HPSYS_PINMUX_PAD_PA06_IE        HPSYS_PINMUX_PAD_PA06_IE_Msk
#define HPSYS_PINMUX_PAD_PA06_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA06_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_IS_Pos)
#define HPSYS_PINMUX_PAD_PA06_IS        HPSYS_PINMUX_PAD_PA06_IS_Msk
#define HPSYS_PINMUX_PAD_PA06_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA06_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_SR_Pos)
#define HPSYS_PINMUX_PAD_PA06_SR        HPSYS_PINMUX_PAD_PA06_SR_Msk
#define HPSYS_PINMUX_PAD_PA06_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA06_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA06_DS0       HPSYS_PINMUX_PAD_PA06_DS0_Msk
#define HPSYS_PINMUX_PAD_PA06_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA06_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA06_DS1       HPSYS_PINMUX_PAD_PA06_DS1_Msk
#define HPSYS_PINMUX_PAD_PA06_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA06_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_POE_Pos)
#define HPSYS_PINMUX_PAD_PA06_POE       HPSYS_PINMUX_PAD_PA06_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA07 register **************/
#define HPSYS_PINMUX_PAD_PA07_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA07_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA07_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA07_FSEL      HPSYS_PINMUX_PAD_PA07_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA07_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA07_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_PE_Pos)
#define HPSYS_PINMUX_PAD_PA07_PE        HPSYS_PINMUX_PAD_PA07_PE_Msk
#define HPSYS_PINMUX_PAD_PA07_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA07_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_PS_Pos)
#define HPSYS_PINMUX_PAD_PA07_PS        HPSYS_PINMUX_PAD_PA07_PS_Msk
#define HPSYS_PINMUX_PAD_PA07_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA07_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_IE_Pos)
#define HPSYS_PINMUX_PAD_PA07_IE        HPSYS_PINMUX_PAD_PA07_IE_Msk
#define HPSYS_PINMUX_PAD_PA07_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA07_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_IS_Pos)
#define HPSYS_PINMUX_PAD_PA07_IS        HPSYS_PINMUX_PAD_PA07_IS_Msk
#define HPSYS_PINMUX_PAD_PA07_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA07_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_SR_Pos)
#define HPSYS_PINMUX_PAD_PA07_SR        HPSYS_PINMUX_PAD_PA07_SR_Msk
#define HPSYS_PINMUX_PAD_PA07_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA07_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA07_DS0       HPSYS_PINMUX_PAD_PA07_DS0_Msk
#define HPSYS_PINMUX_PAD_PA07_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA07_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA07_DS1       HPSYS_PINMUX_PAD_PA07_DS1_Msk
#define HPSYS_PINMUX_PAD_PA07_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA07_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_POE_Pos)
#define HPSYS_PINMUX_PAD_PA07_POE       HPSYS_PINMUX_PAD_PA07_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA08 register **************/
#define HPSYS_PINMUX_PAD_PA08_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA08_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA08_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA08_FSEL      HPSYS_PINMUX_PAD_PA08_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA08_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA08_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_PE_Pos)
#define HPSYS_PINMUX_PAD_PA08_PE        HPSYS_PINMUX_PAD_PA08_PE_Msk
#define HPSYS_PINMUX_PAD_PA08_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA08_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_PS_Pos)
#define HPSYS_PINMUX_PAD_PA08_PS        HPSYS_PINMUX_PAD_PA08_PS_Msk
#define HPSYS_PINMUX_PAD_PA08_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA08_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_IE_Pos)
#define HPSYS_PINMUX_PAD_PA08_IE        HPSYS_PINMUX_PAD_PA08_IE_Msk
#define HPSYS_PINMUX_PAD_PA08_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA08_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_IS_Pos)
#define HPSYS_PINMUX_PAD_PA08_IS        HPSYS_PINMUX_PAD_PA08_IS_Msk
#define HPSYS_PINMUX_PAD_PA08_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA08_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_SR_Pos)
#define HPSYS_PINMUX_PAD_PA08_SR        HPSYS_PINMUX_PAD_PA08_SR_Msk
#define HPSYS_PINMUX_PAD_PA08_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA08_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA08_DS0       HPSYS_PINMUX_PAD_PA08_DS0_Msk
#define HPSYS_PINMUX_PAD_PA08_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA08_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA08_DS1       HPSYS_PINMUX_PAD_PA08_DS1_Msk
#define HPSYS_PINMUX_PAD_PA08_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA08_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_POE_Pos)
#define HPSYS_PINMUX_PAD_PA08_POE       HPSYS_PINMUX_PAD_PA08_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA09 register **************/
#define HPSYS_PINMUX_PAD_PA09_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA09_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA09_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA09_FSEL      HPSYS_PINMUX_PAD_PA09_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA09_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA09_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_PE_Pos)
#define HPSYS_PINMUX_PAD_PA09_PE        HPSYS_PINMUX_PAD_PA09_PE_Msk
#define HPSYS_PINMUX_PAD_PA09_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA09_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_PS_Pos)
#define HPSYS_PINMUX_PAD_PA09_PS        HPSYS_PINMUX_PAD_PA09_PS_Msk
#define HPSYS_PINMUX_PAD_PA09_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA09_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_IE_Pos)
#define HPSYS_PINMUX_PAD_PA09_IE        HPSYS_PINMUX_PAD_PA09_IE_Msk
#define HPSYS_PINMUX_PAD_PA09_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA09_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_IS_Pos)
#define HPSYS_PINMUX_PAD_PA09_IS        HPSYS_PINMUX_PAD_PA09_IS_Msk
#define HPSYS_PINMUX_PAD_PA09_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA09_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_SR_Pos)
#define HPSYS_PINMUX_PAD_PA09_SR        HPSYS_PINMUX_PAD_PA09_SR_Msk
#define HPSYS_PINMUX_PAD_PA09_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA09_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA09_DS0       HPSYS_PINMUX_PAD_PA09_DS0_Msk
#define HPSYS_PINMUX_PAD_PA09_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA09_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA09_DS1       HPSYS_PINMUX_PAD_PA09_DS1_Msk
#define HPSYS_PINMUX_PAD_PA09_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA09_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_POE_Pos)
#define HPSYS_PINMUX_PAD_PA09_POE       HPSYS_PINMUX_PAD_PA09_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA10 register **************/
#define HPSYS_PINMUX_PAD_PA10_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA10_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA10_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA10_FSEL      HPSYS_PINMUX_PAD_PA10_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA10_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA10_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_PE_Pos)
#define HPSYS_PINMUX_PAD_PA10_PE        HPSYS_PINMUX_PAD_PA10_PE_Msk
#define HPSYS_PINMUX_PAD_PA10_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA10_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_PS_Pos)
#define HPSYS_PINMUX_PAD_PA10_PS        HPSYS_PINMUX_PAD_PA10_PS_Msk
#define HPSYS_PINMUX_PAD_PA10_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA10_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_IE_Pos)
#define HPSYS_PINMUX_PAD_PA10_IE        HPSYS_PINMUX_PAD_PA10_IE_Msk
#define HPSYS_PINMUX_PAD_PA10_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA10_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_IS_Pos)
#define HPSYS_PINMUX_PAD_PA10_IS        HPSYS_PINMUX_PAD_PA10_IS_Msk
#define HPSYS_PINMUX_PAD_PA10_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA10_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA10_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA10_MODE      HPSYS_PINMUX_PAD_PA10_MODE_Msk
#define HPSYS_PINMUX_PAD_PA10_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA10_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_DS_Pos)
#define HPSYS_PINMUX_PAD_PA10_DS        HPSYS_PINMUX_PAD_PA10_DS_Msk
#define HPSYS_PINMUX_PAD_PA10_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA10_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA10_POE_Pos)
#define HPSYS_PINMUX_PAD_PA10_POE       HPSYS_PINMUX_PAD_PA10_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA11 register **************/
#define HPSYS_PINMUX_PAD_PA11_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA11_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA11_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA11_FSEL      HPSYS_PINMUX_PAD_PA11_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA11_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA11_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_PE_Pos)
#define HPSYS_PINMUX_PAD_PA11_PE        HPSYS_PINMUX_PAD_PA11_PE_Msk
#define HPSYS_PINMUX_PAD_PA11_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA11_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_PS_Pos)
#define HPSYS_PINMUX_PAD_PA11_PS        HPSYS_PINMUX_PAD_PA11_PS_Msk
#define HPSYS_PINMUX_PAD_PA11_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA11_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_IE_Pos)
#define HPSYS_PINMUX_PAD_PA11_IE        HPSYS_PINMUX_PAD_PA11_IE_Msk
#define HPSYS_PINMUX_PAD_PA11_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA11_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_IS_Pos)
#define HPSYS_PINMUX_PAD_PA11_IS        HPSYS_PINMUX_PAD_PA11_IS_Msk
#define HPSYS_PINMUX_PAD_PA11_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA11_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_SR_Pos)
#define HPSYS_PINMUX_PAD_PA11_SR        HPSYS_PINMUX_PAD_PA11_SR_Msk
#define HPSYS_PINMUX_PAD_PA11_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA11_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA11_DS0       HPSYS_PINMUX_PAD_PA11_DS0_Msk
#define HPSYS_PINMUX_PAD_PA11_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA11_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA11_DS1       HPSYS_PINMUX_PAD_PA11_DS1_Msk
#define HPSYS_PINMUX_PAD_PA11_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA11_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_POE_Pos)
#define HPSYS_PINMUX_PAD_PA11_POE       HPSYS_PINMUX_PAD_PA11_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA12 register **************/
#define HPSYS_PINMUX_PAD_PA12_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA12_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA12_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA12_FSEL      HPSYS_PINMUX_PAD_PA12_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA12_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA12_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_PE_Pos)
#define HPSYS_PINMUX_PAD_PA12_PE        HPSYS_PINMUX_PAD_PA12_PE_Msk
#define HPSYS_PINMUX_PAD_PA12_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA12_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_PS_Pos)
#define HPSYS_PINMUX_PAD_PA12_PS        HPSYS_PINMUX_PAD_PA12_PS_Msk
#define HPSYS_PINMUX_PAD_PA12_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA12_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_IE_Pos)
#define HPSYS_PINMUX_PAD_PA12_IE        HPSYS_PINMUX_PAD_PA12_IE_Msk
#define HPSYS_PINMUX_PAD_PA12_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA12_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_IS_Pos)
#define HPSYS_PINMUX_PAD_PA12_IS        HPSYS_PINMUX_PAD_PA12_IS_Msk
#define HPSYS_PINMUX_PAD_PA12_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA12_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_SR_Pos)
#define HPSYS_PINMUX_PAD_PA12_SR        HPSYS_PINMUX_PAD_PA12_SR_Msk
#define HPSYS_PINMUX_PAD_PA12_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA12_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA12_DS0       HPSYS_PINMUX_PAD_PA12_DS0_Msk
#define HPSYS_PINMUX_PAD_PA12_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA12_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA12_DS1       HPSYS_PINMUX_PAD_PA12_DS1_Msk
#define HPSYS_PINMUX_PAD_PA12_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA12_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_POE_Pos)
#define HPSYS_PINMUX_PAD_PA12_POE       HPSYS_PINMUX_PAD_PA12_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA13 register **************/
#define HPSYS_PINMUX_PAD_PA13_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA13_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA13_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA13_FSEL      HPSYS_PINMUX_PAD_PA13_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA13_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA13_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_PE_Pos)
#define HPSYS_PINMUX_PAD_PA13_PE        HPSYS_PINMUX_PAD_PA13_PE_Msk
#define HPSYS_PINMUX_PAD_PA13_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA13_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_PS_Pos)
#define HPSYS_PINMUX_PAD_PA13_PS        HPSYS_PINMUX_PAD_PA13_PS_Msk
#define HPSYS_PINMUX_PAD_PA13_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA13_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_IE_Pos)
#define HPSYS_PINMUX_PAD_PA13_IE        HPSYS_PINMUX_PAD_PA13_IE_Msk
#define HPSYS_PINMUX_PAD_PA13_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA13_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_IS_Pos)
#define HPSYS_PINMUX_PAD_PA13_IS        HPSYS_PINMUX_PAD_PA13_IS_Msk
#define HPSYS_PINMUX_PAD_PA13_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA13_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_SR_Pos)
#define HPSYS_PINMUX_PAD_PA13_SR        HPSYS_PINMUX_PAD_PA13_SR_Msk
#define HPSYS_PINMUX_PAD_PA13_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA13_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA13_DS0       HPSYS_PINMUX_PAD_PA13_DS0_Msk
#define HPSYS_PINMUX_PAD_PA13_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA13_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA13_DS1       HPSYS_PINMUX_PAD_PA13_DS1_Msk
#define HPSYS_PINMUX_PAD_PA13_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA13_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_POE_Pos)
#define HPSYS_PINMUX_PAD_PA13_POE       HPSYS_PINMUX_PAD_PA13_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA14 register **************/
#define HPSYS_PINMUX_PAD_PA14_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA14_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA14_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA14_FSEL      HPSYS_PINMUX_PAD_PA14_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA14_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA14_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_PE_Pos)
#define HPSYS_PINMUX_PAD_PA14_PE        HPSYS_PINMUX_PAD_PA14_PE_Msk
#define HPSYS_PINMUX_PAD_PA14_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA14_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_PS_Pos)
#define HPSYS_PINMUX_PAD_PA14_PS        HPSYS_PINMUX_PAD_PA14_PS_Msk
#define HPSYS_PINMUX_PAD_PA14_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA14_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_IE_Pos)
#define HPSYS_PINMUX_PAD_PA14_IE        HPSYS_PINMUX_PAD_PA14_IE_Msk
#define HPSYS_PINMUX_PAD_PA14_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA14_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_IS_Pos)
#define HPSYS_PINMUX_PAD_PA14_IS        HPSYS_PINMUX_PAD_PA14_IS_Msk
#define HPSYS_PINMUX_PAD_PA14_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA14_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA14_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA14_MODE      HPSYS_PINMUX_PAD_PA14_MODE_Msk
#define HPSYS_PINMUX_PAD_PA14_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA14_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_DS_Pos)
#define HPSYS_PINMUX_PAD_PA14_DS        HPSYS_PINMUX_PAD_PA14_DS_Msk
#define HPSYS_PINMUX_PAD_PA14_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA14_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA14_POE_Pos)
#define HPSYS_PINMUX_PAD_PA14_POE       HPSYS_PINMUX_PAD_PA14_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA15 register **************/
#define HPSYS_PINMUX_PAD_PA15_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA15_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA15_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA15_FSEL      HPSYS_PINMUX_PAD_PA15_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA15_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA15_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_PE_Pos)
#define HPSYS_PINMUX_PAD_PA15_PE        HPSYS_PINMUX_PAD_PA15_PE_Msk
#define HPSYS_PINMUX_PAD_PA15_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA15_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_PS_Pos)
#define HPSYS_PINMUX_PAD_PA15_PS        HPSYS_PINMUX_PAD_PA15_PS_Msk
#define HPSYS_PINMUX_PAD_PA15_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA15_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_IE_Pos)
#define HPSYS_PINMUX_PAD_PA15_IE        HPSYS_PINMUX_PAD_PA15_IE_Msk
#define HPSYS_PINMUX_PAD_PA15_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA15_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_IS_Pos)
#define HPSYS_PINMUX_PAD_PA15_IS        HPSYS_PINMUX_PAD_PA15_IS_Msk
#define HPSYS_PINMUX_PAD_PA15_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA15_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_SR_Pos)
#define HPSYS_PINMUX_PAD_PA15_SR        HPSYS_PINMUX_PAD_PA15_SR_Msk
#define HPSYS_PINMUX_PAD_PA15_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA15_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA15_DS0       HPSYS_PINMUX_PAD_PA15_DS0_Msk
#define HPSYS_PINMUX_PAD_PA15_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA15_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA15_DS1       HPSYS_PINMUX_PAD_PA15_DS1_Msk
#define HPSYS_PINMUX_PAD_PA15_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA15_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_POE_Pos)
#define HPSYS_PINMUX_PAD_PA15_POE       HPSYS_PINMUX_PAD_PA15_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA16 register **************/
#define HPSYS_PINMUX_PAD_PA16_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA16_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA16_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA16_FSEL      HPSYS_PINMUX_PAD_PA16_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA16_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA16_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_PE_Pos)
#define HPSYS_PINMUX_PAD_PA16_PE        HPSYS_PINMUX_PAD_PA16_PE_Msk
#define HPSYS_PINMUX_PAD_PA16_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA16_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_PS_Pos)
#define HPSYS_PINMUX_PAD_PA16_PS        HPSYS_PINMUX_PAD_PA16_PS_Msk
#define HPSYS_PINMUX_PAD_PA16_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA16_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_IE_Pos)
#define HPSYS_PINMUX_PAD_PA16_IE        HPSYS_PINMUX_PAD_PA16_IE_Msk
#define HPSYS_PINMUX_PAD_PA16_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA16_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_IS_Pos)
#define HPSYS_PINMUX_PAD_PA16_IS        HPSYS_PINMUX_PAD_PA16_IS_Msk
#define HPSYS_PINMUX_PAD_PA16_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA16_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_SR_Pos)
#define HPSYS_PINMUX_PAD_PA16_SR        HPSYS_PINMUX_PAD_PA16_SR_Msk
#define HPSYS_PINMUX_PAD_PA16_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA16_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA16_DS0       HPSYS_PINMUX_PAD_PA16_DS0_Msk
#define HPSYS_PINMUX_PAD_PA16_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA16_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA16_DS1       HPSYS_PINMUX_PAD_PA16_DS1_Msk
#define HPSYS_PINMUX_PAD_PA16_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA16_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_POE_Pos)
#define HPSYS_PINMUX_PAD_PA16_POE       HPSYS_PINMUX_PAD_PA16_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA17 register **************/
#define HPSYS_PINMUX_PAD_PA17_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA17_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA17_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA17_FSEL      HPSYS_PINMUX_PAD_PA17_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA17_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA17_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_PE_Pos)
#define HPSYS_PINMUX_PAD_PA17_PE        HPSYS_PINMUX_PAD_PA17_PE_Msk
#define HPSYS_PINMUX_PAD_PA17_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA17_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_PS_Pos)
#define HPSYS_PINMUX_PAD_PA17_PS        HPSYS_PINMUX_PAD_PA17_PS_Msk
#define HPSYS_PINMUX_PAD_PA17_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA17_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_IE_Pos)
#define HPSYS_PINMUX_PAD_PA17_IE        HPSYS_PINMUX_PAD_PA17_IE_Msk
#define HPSYS_PINMUX_PAD_PA17_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA17_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_IS_Pos)
#define HPSYS_PINMUX_PAD_PA17_IS        HPSYS_PINMUX_PAD_PA17_IS_Msk
#define HPSYS_PINMUX_PAD_PA17_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA17_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_SR_Pos)
#define HPSYS_PINMUX_PAD_PA17_SR        HPSYS_PINMUX_PAD_PA17_SR_Msk
#define HPSYS_PINMUX_PAD_PA17_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA17_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA17_DS0       HPSYS_PINMUX_PAD_PA17_DS0_Msk
#define HPSYS_PINMUX_PAD_PA17_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA17_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA17_DS1       HPSYS_PINMUX_PAD_PA17_DS1_Msk
#define HPSYS_PINMUX_PAD_PA17_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA17_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_POE_Pos)
#define HPSYS_PINMUX_PAD_PA17_POE       HPSYS_PINMUX_PAD_PA17_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA18 register **************/
#define HPSYS_PINMUX_PAD_PA18_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA18_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA18_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA18_FSEL      HPSYS_PINMUX_PAD_PA18_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA18_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA18_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_PE_Pos)
#define HPSYS_PINMUX_PAD_PA18_PE        HPSYS_PINMUX_PAD_PA18_PE_Msk
#define HPSYS_PINMUX_PAD_PA18_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA18_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_PS_Pos)
#define HPSYS_PINMUX_PAD_PA18_PS        HPSYS_PINMUX_PAD_PA18_PS_Msk
#define HPSYS_PINMUX_PAD_PA18_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA18_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_IE_Pos)
#define HPSYS_PINMUX_PAD_PA18_IE        HPSYS_PINMUX_PAD_PA18_IE_Msk
#define HPSYS_PINMUX_PAD_PA18_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA18_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_IS_Pos)
#define HPSYS_PINMUX_PAD_PA18_IS        HPSYS_PINMUX_PAD_PA18_IS_Msk
#define HPSYS_PINMUX_PAD_PA18_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA18_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_SR_Pos)
#define HPSYS_PINMUX_PAD_PA18_SR        HPSYS_PINMUX_PAD_PA18_SR_Msk
#define HPSYS_PINMUX_PAD_PA18_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA18_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA18_DS0       HPSYS_PINMUX_PAD_PA18_DS0_Msk
#define HPSYS_PINMUX_PAD_PA18_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA18_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA18_DS1       HPSYS_PINMUX_PAD_PA18_DS1_Msk
#define HPSYS_PINMUX_PAD_PA18_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA18_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_POE_Pos)
#define HPSYS_PINMUX_PAD_PA18_POE       HPSYS_PINMUX_PAD_PA18_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA19 register **************/
#define HPSYS_PINMUX_PAD_PA19_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA19_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA19_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA19_FSEL      HPSYS_PINMUX_PAD_PA19_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA19_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA19_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_PE_Pos)
#define HPSYS_PINMUX_PAD_PA19_PE        HPSYS_PINMUX_PAD_PA19_PE_Msk
#define HPSYS_PINMUX_PAD_PA19_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA19_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_PS_Pos)
#define HPSYS_PINMUX_PAD_PA19_PS        HPSYS_PINMUX_PAD_PA19_PS_Msk
#define HPSYS_PINMUX_PAD_PA19_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA19_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_IE_Pos)
#define HPSYS_PINMUX_PAD_PA19_IE        HPSYS_PINMUX_PAD_PA19_IE_Msk
#define HPSYS_PINMUX_PAD_PA19_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA19_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_IS_Pos)
#define HPSYS_PINMUX_PAD_PA19_IS        HPSYS_PINMUX_PAD_PA19_IS_Msk
#define HPSYS_PINMUX_PAD_PA19_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA19_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_SR_Pos)
#define HPSYS_PINMUX_PAD_PA19_SR        HPSYS_PINMUX_PAD_PA19_SR_Msk
#define HPSYS_PINMUX_PAD_PA19_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA19_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA19_DS0       HPSYS_PINMUX_PAD_PA19_DS0_Msk
#define HPSYS_PINMUX_PAD_PA19_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA19_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA19_DS1       HPSYS_PINMUX_PAD_PA19_DS1_Msk
#define HPSYS_PINMUX_PAD_PA19_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA19_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_POE_Pos)
#define HPSYS_PINMUX_PAD_PA19_POE       HPSYS_PINMUX_PAD_PA19_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA20 register **************/
#define HPSYS_PINMUX_PAD_PA20_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA20_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA20_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA20_FSEL      HPSYS_PINMUX_PAD_PA20_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA20_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA20_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_PE_Pos)
#define HPSYS_PINMUX_PAD_PA20_PE        HPSYS_PINMUX_PAD_PA20_PE_Msk
#define HPSYS_PINMUX_PAD_PA20_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA20_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_PS_Pos)
#define HPSYS_PINMUX_PAD_PA20_PS        HPSYS_PINMUX_PAD_PA20_PS_Msk
#define HPSYS_PINMUX_PAD_PA20_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA20_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_IE_Pos)
#define HPSYS_PINMUX_PAD_PA20_IE        HPSYS_PINMUX_PAD_PA20_IE_Msk
#define HPSYS_PINMUX_PAD_PA20_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA20_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_IS_Pos)
#define HPSYS_PINMUX_PAD_PA20_IS        HPSYS_PINMUX_PAD_PA20_IS_Msk
#define HPSYS_PINMUX_PAD_PA20_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA20_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_SR_Pos)
#define HPSYS_PINMUX_PAD_PA20_SR        HPSYS_PINMUX_PAD_PA20_SR_Msk
#define HPSYS_PINMUX_PAD_PA20_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA20_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA20_DS0       HPSYS_PINMUX_PAD_PA20_DS0_Msk
#define HPSYS_PINMUX_PAD_PA20_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA20_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA20_DS1       HPSYS_PINMUX_PAD_PA20_DS1_Msk
#define HPSYS_PINMUX_PAD_PA20_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA20_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_POE_Pos)
#define HPSYS_PINMUX_PAD_PA20_POE       HPSYS_PINMUX_PAD_PA20_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA21 register **************/
#define HPSYS_PINMUX_PAD_PA21_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA21_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA21_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA21_FSEL      HPSYS_PINMUX_PAD_PA21_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA21_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA21_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_PE_Pos)
#define HPSYS_PINMUX_PAD_PA21_PE        HPSYS_PINMUX_PAD_PA21_PE_Msk
#define HPSYS_PINMUX_PAD_PA21_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA21_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_PS_Pos)
#define HPSYS_PINMUX_PAD_PA21_PS        HPSYS_PINMUX_PAD_PA21_PS_Msk
#define HPSYS_PINMUX_PAD_PA21_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA21_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_IE_Pos)
#define HPSYS_PINMUX_PAD_PA21_IE        HPSYS_PINMUX_PAD_PA21_IE_Msk
#define HPSYS_PINMUX_PAD_PA21_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA21_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_IS_Pos)
#define HPSYS_PINMUX_PAD_PA21_IS        HPSYS_PINMUX_PAD_PA21_IS_Msk
#define HPSYS_PINMUX_PAD_PA21_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA21_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_SR_Pos)
#define HPSYS_PINMUX_PAD_PA21_SR        HPSYS_PINMUX_PAD_PA21_SR_Msk
#define HPSYS_PINMUX_PAD_PA21_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA21_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA21_DS0       HPSYS_PINMUX_PAD_PA21_DS0_Msk
#define HPSYS_PINMUX_PAD_PA21_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA21_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA21_DS1       HPSYS_PINMUX_PAD_PA21_DS1_Msk
#define HPSYS_PINMUX_PAD_PA21_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA21_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_POE_Pos)
#define HPSYS_PINMUX_PAD_PA21_POE       HPSYS_PINMUX_PAD_PA21_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA22 register **************/
#define HPSYS_PINMUX_PAD_PA22_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA22_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA22_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA22_FSEL      HPSYS_PINMUX_PAD_PA22_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA22_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA22_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_PE_Pos)
#define HPSYS_PINMUX_PAD_PA22_PE        HPSYS_PINMUX_PAD_PA22_PE_Msk
#define HPSYS_PINMUX_PAD_PA22_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA22_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_PS_Pos)
#define HPSYS_PINMUX_PAD_PA22_PS        HPSYS_PINMUX_PAD_PA22_PS_Msk
#define HPSYS_PINMUX_PAD_PA22_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA22_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_IE_Pos)
#define HPSYS_PINMUX_PAD_PA22_IE        HPSYS_PINMUX_PAD_PA22_IE_Msk
#define HPSYS_PINMUX_PAD_PA22_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA22_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_IS_Pos)
#define HPSYS_PINMUX_PAD_PA22_IS        HPSYS_PINMUX_PAD_PA22_IS_Msk
#define HPSYS_PINMUX_PAD_PA22_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA22_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_SR_Pos)
#define HPSYS_PINMUX_PAD_PA22_SR        HPSYS_PINMUX_PAD_PA22_SR_Msk
#define HPSYS_PINMUX_PAD_PA22_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA22_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA22_DS0       HPSYS_PINMUX_PAD_PA22_DS0_Msk
#define HPSYS_PINMUX_PAD_PA22_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA22_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA22_DS1       HPSYS_PINMUX_PAD_PA22_DS1_Msk
#define HPSYS_PINMUX_PAD_PA22_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA22_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_POE_Pos)
#define HPSYS_PINMUX_PAD_PA22_POE       HPSYS_PINMUX_PAD_PA22_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA23 register **************/
#define HPSYS_PINMUX_PAD_PA23_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA23_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA23_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA23_FSEL      HPSYS_PINMUX_PAD_PA23_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA23_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA23_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_PE_Pos)
#define HPSYS_PINMUX_PAD_PA23_PE        HPSYS_PINMUX_PAD_PA23_PE_Msk
#define HPSYS_PINMUX_PAD_PA23_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA23_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_PS_Pos)
#define HPSYS_PINMUX_PAD_PA23_PS        HPSYS_PINMUX_PAD_PA23_PS_Msk
#define HPSYS_PINMUX_PAD_PA23_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA23_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_IE_Pos)
#define HPSYS_PINMUX_PAD_PA23_IE        HPSYS_PINMUX_PAD_PA23_IE_Msk
#define HPSYS_PINMUX_PAD_PA23_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA23_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_IS_Pos)
#define HPSYS_PINMUX_PAD_PA23_IS        HPSYS_PINMUX_PAD_PA23_IS_Msk
#define HPSYS_PINMUX_PAD_PA23_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA23_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_SR_Pos)
#define HPSYS_PINMUX_PAD_PA23_SR        HPSYS_PINMUX_PAD_PA23_SR_Msk
#define HPSYS_PINMUX_PAD_PA23_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA23_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA23_DS0       HPSYS_PINMUX_PAD_PA23_DS0_Msk
#define HPSYS_PINMUX_PAD_PA23_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA23_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA23_DS1       HPSYS_PINMUX_PAD_PA23_DS1_Msk
#define HPSYS_PINMUX_PAD_PA23_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA23_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_POE_Pos)
#define HPSYS_PINMUX_PAD_PA23_POE       HPSYS_PINMUX_PAD_PA23_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA24 register **************/
#define HPSYS_PINMUX_PAD_PA24_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA24_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA24_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA24_FSEL      HPSYS_PINMUX_PAD_PA24_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA24_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA24_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_PE_Pos)
#define HPSYS_PINMUX_PAD_PA24_PE        HPSYS_PINMUX_PAD_PA24_PE_Msk
#define HPSYS_PINMUX_PAD_PA24_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA24_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_PS_Pos)
#define HPSYS_PINMUX_PAD_PA24_PS        HPSYS_PINMUX_PAD_PA24_PS_Msk
#define HPSYS_PINMUX_PAD_PA24_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA24_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_IE_Pos)
#define HPSYS_PINMUX_PAD_PA24_IE        HPSYS_PINMUX_PAD_PA24_IE_Msk
#define HPSYS_PINMUX_PAD_PA24_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA24_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_IS_Pos)
#define HPSYS_PINMUX_PAD_PA24_IS        HPSYS_PINMUX_PAD_PA24_IS_Msk
#define HPSYS_PINMUX_PAD_PA24_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA24_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_SR_Pos)
#define HPSYS_PINMUX_PAD_PA24_SR        HPSYS_PINMUX_PAD_PA24_SR_Msk
#define HPSYS_PINMUX_PAD_PA24_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA24_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA24_DS0       HPSYS_PINMUX_PAD_PA24_DS0_Msk
#define HPSYS_PINMUX_PAD_PA24_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA24_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA24_DS1       HPSYS_PINMUX_PAD_PA24_DS1_Msk
#define HPSYS_PINMUX_PAD_PA24_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA24_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_POE_Pos)
#define HPSYS_PINMUX_PAD_PA24_POE       HPSYS_PINMUX_PAD_PA24_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA25 register **************/
#define HPSYS_PINMUX_PAD_PA25_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA25_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA25_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA25_FSEL      HPSYS_PINMUX_PAD_PA25_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA25_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA25_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_PE_Pos)
#define HPSYS_PINMUX_PAD_PA25_PE        HPSYS_PINMUX_PAD_PA25_PE_Msk
#define HPSYS_PINMUX_PAD_PA25_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA25_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_PS_Pos)
#define HPSYS_PINMUX_PAD_PA25_PS        HPSYS_PINMUX_PAD_PA25_PS_Msk
#define HPSYS_PINMUX_PAD_PA25_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA25_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_IE_Pos)
#define HPSYS_PINMUX_PAD_PA25_IE        HPSYS_PINMUX_PAD_PA25_IE_Msk
#define HPSYS_PINMUX_PAD_PA25_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA25_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_IS_Pos)
#define HPSYS_PINMUX_PAD_PA25_IS        HPSYS_PINMUX_PAD_PA25_IS_Msk
#define HPSYS_PINMUX_PAD_PA25_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA25_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_SR_Pos)
#define HPSYS_PINMUX_PAD_PA25_SR        HPSYS_PINMUX_PAD_PA25_SR_Msk
#define HPSYS_PINMUX_PAD_PA25_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA25_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA25_DS0       HPSYS_PINMUX_PAD_PA25_DS0_Msk
#define HPSYS_PINMUX_PAD_PA25_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA25_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA25_DS1       HPSYS_PINMUX_PAD_PA25_DS1_Msk
#define HPSYS_PINMUX_PAD_PA25_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA25_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_POE_Pos)
#define HPSYS_PINMUX_PAD_PA25_POE       HPSYS_PINMUX_PAD_PA25_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA26 register **************/
#define HPSYS_PINMUX_PAD_PA26_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA26_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA26_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA26_FSEL      HPSYS_PINMUX_PAD_PA26_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA26_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA26_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_PE_Pos)
#define HPSYS_PINMUX_PAD_PA26_PE        HPSYS_PINMUX_PAD_PA26_PE_Msk
#define HPSYS_PINMUX_PAD_PA26_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA26_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_PS_Pos)
#define HPSYS_PINMUX_PAD_PA26_PS        HPSYS_PINMUX_PAD_PA26_PS_Msk
#define HPSYS_PINMUX_PAD_PA26_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA26_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_IE_Pos)
#define HPSYS_PINMUX_PAD_PA26_IE        HPSYS_PINMUX_PAD_PA26_IE_Msk
#define HPSYS_PINMUX_PAD_PA26_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA26_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_IS_Pos)
#define HPSYS_PINMUX_PAD_PA26_IS        HPSYS_PINMUX_PAD_PA26_IS_Msk
#define HPSYS_PINMUX_PAD_PA26_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA26_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_SR_Pos)
#define HPSYS_PINMUX_PAD_PA26_SR        HPSYS_PINMUX_PAD_PA26_SR_Msk
#define HPSYS_PINMUX_PAD_PA26_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA26_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA26_DS0       HPSYS_PINMUX_PAD_PA26_DS0_Msk
#define HPSYS_PINMUX_PAD_PA26_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA26_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA26_DS1       HPSYS_PINMUX_PAD_PA26_DS1_Msk
#define HPSYS_PINMUX_PAD_PA26_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA26_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_POE_Pos)
#define HPSYS_PINMUX_PAD_PA26_POE       HPSYS_PINMUX_PAD_PA26_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA27 register **************/
#define HPSYS_PINMUX_PAD_PA27_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA27_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA27_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA27_FSEL      HPSYS_PINMUX_PAD_PA27_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA27_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA27_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_PE_Pos)
#define HPSYS_PINMUX_PAD_PA27_PE        HPSYS_PINMUX_PAD_PA27_PE_Msk
#define HPSYS_PINMUX_PAD_PA27_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA27_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_PS_Pos)
#define HPSYS_PINMUX_PAD_PA27_PS        HPSYS_PINMUX_PAD_PA27_PS_Msk
#define HPSYS_PINMUX_PAD_PA27_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA27_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_IE_Pos)
#define HPSYS_PINMUX_PAD_PA27_IE        HPSYS_PINMUX_PAD_PA27_IE_Msk
#define HPSYS_PINMUX_PAD_PA27_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA27_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_IS_Pos)
#define HPSYS_PINMUX_PAD_PA27_IS        HPSYS_PINMUX_PAD_PA27_IS_Msk
#define HPSYS_PINMUX_PAD_PA27_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA27_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_SR_Pos)
#define HPSYS_PINMUX_PAD_PA27_SR        HPSYS_PINMUX_PAD_PA27_SR_Msk
#define HPSYS_PINMUX_PAD_PA27_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA27_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA27_DS0       HPSYS_PINMUX_PAD_PA27_DS0_Msk
#define HPSYS_PINMUX_PAD_PA27_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA27_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA27_DS1       HPSYS_PINMUX_PAD_PA27_DS1_Msk
#define HPSYS_PINMUX_PAD_PA27_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA27_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_POE_Pos)
#define HPSYS_PINMUX_PAD_PA27_POE       HPSYS_PINMUX_PAD_PA27_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA28 register **************/
#define HPSYS_PINMUX_PAD_PA28_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA28_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA28_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA28_FSEL      HPSYS_PINMUX_PAD_PA28_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA28_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA28_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_PE_Pos)
#define HPSYS_PINMUX_PAD_PA28_PE        HPSYS_PINMUX_PAD_PA28_PE_Msk
#define HPSYS_PINMUX_PAD_PA28_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA28_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_PS_Pos)
#define HPSYS_PINMUX_PAD_PA28_PS        HPSYS_PINMUX_PAD_PA28_PS_Msk
#define HPSYS_PINMUX_PAD_PA28_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA28_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_IE_Pos)
#define HPSYS_PINMUX_PAD_PA28_IE        HPSYS_PINMUX_PAD_PA28_IE_Msk
#define HPSYS_PINMUX_PAD_PA28_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA28_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_IS_Pos)
#define HPSYS_PINMUX_PAD_PA28_IS        HPSYS_PINMUX_PAD_PA28_IS_Msk
#define HPSYS_PINMUX_PAD_PA28_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA28_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_SR_Pos)
#define HPSYS_PINMUX_PAD_PA28_SR        HPSYS_PINMUX_PAD_PA28_SR_Msk
#define HPSYS_PINMUX_PAD_PA28_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA28_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA28_DS0       HPSYS_PINMUX_PAD_PA28_DS0_Msk
#define HPSYS_PINMUX_PAD_PA28_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA28_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA28_DS1       HPSYS_PINMUX_PAD_PA28_DS1_Msk
#define HPSYS_PINMUX_PAD_PA28_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA28_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_POE_Pos)
#define HPSYS_PINMUX_PAD_PA28_POE       HPSYS_PINMUX_PAD_PA28_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA29 register **************/
#define HPSYS_PINMUX_PAD_PA29_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA29_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA29_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA29_FSEL      HPSYS_PINMUX_PAD_PA29_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA29_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA29_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_PE_Pos)
#define HPSYS_PINMUX_PAD_PA29_PE        HPSYS_PINMUX_PAD_PA29_PE_Msk
#define HPSYS_PINMUX_PAD_PA29_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA29_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_PS_Pos)
#define HPSYS_PINMUX_PAD_PA29_PS        HPSYS_PINMUX_PAD_PA29_PS_Msk
#define HPSYS_PINMUX_PAD_PA29_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA29_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_IE_Pos)
#define HPSYS_PINMUX_PAD_PA29_IE        HPSYS_PINMUX_PAD_PA29_IE_Msk
#define HPSYS_PINMUX_PAD_PA29_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA29_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_IS_Pos)
#define HPSYS_PINMUX_PAD_PA29_IS        HPSYS_PINMUX_PAD_PA29_IS_Msk
#define HPSYS_PINMUX_PAD_PA29_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA29_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_SR_Pos)
#define HPSYS_PINMUX_PAD_PA29_SR        HPSYS_PINMUX_PAD_PA29_SR_Msk
#define HPSYS_PINMUX_PAD_PA29_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA29_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA29_DS0       HPSYS_PINMUX_PAD_PA29_DS0_Msk
#define HPSYS_PINMUX_PAD_PA29_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA29_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA29_DS1       HPSYS_PINMUX_PAD_PA29_DS1_Msk
#define HPSYS_PINMUX_PAD_PA29_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA29_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_POE_Pos)
#define HPSYS_PINMUX_PAD_PA29_POE       HPSYS_PINMUX_PAD_PA29_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA30 register **************/
#define HPSYS_PINMUX_PAD_PA30_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA30_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA30_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA30_FSEL      HPSYS_PINMUX_PAD_PA30_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA30_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA30_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_PE_Pos)
#define HPSYS_PINMUX_PAD_PA30_PE        HPSYS_PINMUX_PAD_PA30_PE_Msk
#define HPSYS_PINMUX_PAD_PA30_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA30_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_PS_Pos)
#define HPSYS_PINMUX_PAD_PA30_PS        HPSYS_PINMUX_PAD_PA30_PS_Msk
#define HPSYS_PINMUX_PAD_PA30_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA30_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_IE_Pos)
#define HPSYS_PINMUX_PAD_PA30_IE        HPSYS_PINMUX_PAD_PA30_IE_Msk
#define HPSYS_PINMUX_PAD_PA30_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA30_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_IS_Pos)
#define HPSYS_PINMUX_PAD_PA30_IS        HPSYS_PINMUX_PAD_PA30_IS_Msk
#define HPSYS_PINMUX_PAD_PA30_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA30_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_SR_Pos)
#define HPSYS_PINMUX_PAD_PA30_SR        HPSYS_PINMUX_PAD_PA30_SR_Msk
#define HPSYS_PINMUX_PAD_PA30_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA30_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA30_DS0       HPSYS_PINMUX_PAD_PA30_DS0_Msk
#define HPSYS_PINMUX_PAD_PA30_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA30_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA30_DS1       HPSYS_PINMUX_PAD_PA30_DS1_Msk
#define HPSYS_PINMUX_PAD_PA30_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA30_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_POE_Pos)
#define HPSYS_PINMUX_PAD_PA30_POE       HPSYS_PINMUX_PAD_PA30_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA31 register **************/
#define HPSYS_PINMUX_PAD_PA31_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA31_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA31_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA31_FSEL      HPSYS_PINMUX_PAD_PA31_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA31_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA31_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_PE_Pos)
#define HPSYS_PINMUX_PAD_PA31_PE        HPSYS_PINMUX_PAD_PA31_PE_Msk
#define HPSYS_PINMUX_PAD_PA31_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA31_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_PS_Pos)
#define HPSYS_PINMUX_PAD_PA31_PS        HPSYS_PINMUX_PAD_PA31_PS_Msk
#define HPSYS_PINMUX_PAD_PA31_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA31_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_IE_Pos)
#define HPSYS_PINMUX_PAD_PA31_IE        HPSYS_PINMUX_PAD_PA31_IE_Msk
#define HPSYS_PINMUX_PAD_PA31_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA31_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_IS_Pos)
#define HPSYS_PINMUX_PAD_PA31_IS        HPSYS_PINMUX_PAD_PA31_IS_Msk
#define HPSYS_PINMUX_PAD_PA31_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA31_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_SR_Pos)
#define HPSYS_PINMUX_PAD_PA31_SR        HPSYS_PINMUX_PAD_PA31_SR_Msk
#define HPSYS_PINMUX_PAD_PA31_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA31_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA31_DS0       HPSYS_PINMUX_PAD_PA31_DS0_Msk
#define HPSYS_PINMUX_PAD_PA31_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA31_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA31_DS1       HPSYS_PINMUX_PAD_PA31_DS1_Msk
#define HPSYS_PINMUX_PAD_PA31_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA31_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_POE_Pos)
#define HPSYS_PINMUX_PAD_PA31_POE       HPSYS_PINMUX_PAD_PA31_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA32 register **************/
#define HPSYS_PINMUX_PAD_PA32_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA32_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA32_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA32_FSEL      HPSYS_PINMUX_PAD_PA32_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA32_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA32_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_PE_Pos)
#define HPSYS_PINMUX_PAD_PA32_PE        HPSYS_PINMUX_PAD_PA32_PE_Msk
#define HPSYS_PINMUX_PAD_PA32_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA32_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_PS_Pos)
#define HPSYS_PINMUX_PAD_PA32_PS        HPSYS_PINMUX_PAD_PA32_PS_Msk
#define HPSYS_PINMUX_PAD_PA32_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA32_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_IE_Pos)
#define HPSYS_PINMUX_PAD_PA32_IE        HPSYS_PINMUX_PAD_PA32_IE_Msk
#define HPSYS_PINMUX_PAD_PA32_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA32_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_IS_Pos)
#define HPSYS_PINMUX_PAD_PA32_IS        HPSYS_PINMUX_PAD_PA32_IS_Msk
#define HPSYS_PINMUX_PAD_PA32_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA32_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_SR_Pos)
#define HPSYS_PINMUX_PAD_PA32_SR        HPSYS_PINMUX_PAD_PA32_SR_Msk
#define HPSYS_PINMUX_PAD_PA32_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA32_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA32_DS0       HPSYS_PINMUX_PAD_PA32_DS0_Msk
#define HPSYS_PINMUX_PAD_PA32_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA32_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA32_DS1       HPSYS_PINMUX_PAD_PA32_DS1_Msk
#define HPSYS_PINMUX_PAD_PA32_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA32_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_POE_Pos)
#define HPSYS_PINMUX_PAD_PA32_POE       HPSYS_PINMUX_PAD_PA32_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA33 register **************/
#define HPSYS_PINMUX_PAD_PA33_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA33_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA33_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA33_FSEL      HPSYS_PINMUX_PAD_PA33_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA33_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA33_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_PE_Pos)
#define HPSYS_PINMUX_PAD_PA33_PE        HPSYS_PINMUX_PAD_PA33_PE_Msk
#define HPSYS_PINMUX_PAD_PA33_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA33_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_PS_Pos)
#define HPSYS_PINMUX_PAD_PA33_PS        HPSYS_PINMUX_PAD_PA33_PS_Msk
#define HPSYS_PINMUX_PAD_PA33_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA33_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_IE_Pos)
#define HPSYS_PINMUX_PAD_PA33_IE        HPSYS_PINMUX_PAD_PA33_IE_Msk
#define HPSYS_PINMUX_PAD_PA33_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA33_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_IS_Pos)
#define HPSYS_PINMUX_PAD_PA33_IS        HPSYS_PINMUX_PAD_PA33_IS_Msk
#define HPSYS_PINMUX_PAD_PA33_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA33_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_SR_Pos)
#define HPSYS_PINMUX_PAD_PA33_SR        HPSYS_PINMUX_PAD_PA33_SR_Msk
#define HPSYS_PINMUX_PAD_PA33_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA33_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA33_DS0       HPSYS_PINMUX_PAD_PA33_DS0_Msk
#define HPSYS_PINMUX_PAD_PA33_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA33_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA33_DS1       HPSYS_PINMUX_PAD_PA33_DS1_Msk
#define HPSYS_PINMUX_PAD_PA33_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA33_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_POE_Pos)
#define HPSYS_PINMUX_PAD_PA33_POE       HPSYS_PINMUX_PAD_PA33_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA34 register **************/
#define HPSYS_PINMUX_PAD_PA34_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA34_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA34_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA34_FSEL      HPSYS_PINMUX_PAD_PA34_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA34_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA34_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_PE_Pos)
#define HPSYS_PINMUX_PAD_PA34_PE        HPSYS_PINMUX_PAD_PA34_PE_Msk
#define HPSYS_PINMUX_PAD_PA34_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA34_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_PS_Pos)
#define HPSYS_PINMUX_PAD_PA34_PS        HPSYS_PINMUX_PAD_PA34_PS_Msk
#define HPSYS_PINMUX_PAD_PA34_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA34_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_IE_Pos)
#define HPSYS_PINMUX_PAD_PA34_IE        HPSYS_PINMUX_PAD_PA34_IE_Msk
#define HPSYS_PINMUX_PAD_PA34_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA34_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_IS_Pos)
#define HPSYS_PINMUX_PAD_PA34_IS        HPSYS_PINMUX_PAD_PA34_IS_Msk
#define HPSYS_PINMUX_PAD_PA34_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA34_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_SR_Pos)
#define HPSYS_PINMUX_PAD_PA34_SR        HPSYS_PINMUX_PAD_PA34_SR_Msk
#define HPSYS_PINMUX_PAD_PA34_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA34_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA34_DS0       HPSYS_PINMUX_PAD_PA34_DS0_Msk
#define HPSYS_PINMUX_PAD_PA34_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA34_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA34_DS1       HPSYS_PINMUX_PAD_PA34_DS1_Msk
#define HPSYS_PINMUX_PAD_PA34_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA34_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_POE_Pos)
#define HPSYS_PINMUX_PAD_PA34_POE       HPSYS_PINMUX_PAD_PA34_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA35 register **************/
#define HPSYS_PINMUX_PAD_PA35_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA35_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA35_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA35_FSEL      HPSYS_PINMUX_PAD_PA35_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA35_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA35_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_PE_Pos)
#define HPSYS_PINMUX_PAD_PA35_PE        HPSYS_PINMUX_PAD_PA35_PE_Msk
#define HPSYS_PINMUX_PAD_PA35_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA35_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_PS_Pos)
#define HPSYS_PINMUX_PAD_PA35_PS        HPSYS_PINMUX_PAD_PA35_PS_Msk
#define HPSYS_PINMUX_PAD_PA35_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA35_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_IE_Pos)
#define HPSYS_PINMUX_PAD_PA35_IE        HPSYS_PINMUX_PAD_PA35_IE_Msk
#define HPSYS_PINMUX_PAD_PA35_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA35_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_IS_Pos)
#define HPSYS_PINMUX_PAD_PA35_IS        HPSYS_PINMUX_PAD_PA35_IS_Msk
#define HPSYS_PINMUX_PAD_PA35_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA35_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_SR_Pos)
#define HPSYS_PINMUX_PAD_PA35_SR        HPSYS_PINMUX_PAD_PA35_SR_Msk
#define HPSYS_PINMUX_PAD_PA35_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA35_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA35_DS0       HPSYS_PINMUX_PAD_PA35_DS0_Msk
#define HPSYS_PINMUX_PAD_PA35_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA35_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA35_DS1       HPSYS_PINMUX_PAD_PA35_DS1_Msk
#define HPSYS_PINMUX_PAD_PA35_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA35_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_POE_Pos)
#define HPSYS_PINMUX_PAD_PA35_POE       HPSYS_PINMUX_PAD_PA35_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA36 register **************/
#define HPSYS_PINMUX_PAD_PA36_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA36_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA36_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA36_FSEL      HPSYS_PINMUX_PAD_PA36_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA36_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA36_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_PE_Pos)
#define HPSYS_PINMUX_PAD_PA36_PE        HPSYS_PINMUX_PAD_PA36_PE_Msk
#define HPSYS_PINMUX_PAD_PA36_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA36_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_PS_Pos)
#define HPSYS_PINMUX_PAD_PA36_PS        HPSYS_PINMUX_PAD_PA36_PS_Msk
#define HPSYS_PINMUX_PAD_PA36_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA36_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_IE_Pos)
#define HPSYS_PINMUX_PAD_PA36_IE        HPSYS_PINMUX_PAD_PA36_IE_Msk
#define HPSYS_PINMUX_PAD_PA36_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA36_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_IS_Pos)
#define HPSYS_PINMUX_PAD_PA36_IS        HPSYS_PINMUX_PAD_PA36_IS_Msk
#define HPSYS_PINMUX_PAD_PA36_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA36_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_SR_Pos)
#define HPSYS_PINMUX_PAD_PA36_SR        HPSYS_PINMUX_PAD_PA36_SR_Msk
#define HPSYS_PINMUX_PAD_PA36_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA36_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA36_DS0       HPSYS_PINMUX_PAD_PA36_DS0_Msk
#define HPSYS_PINMUX_PAD_PA36_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA36_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA36_DS1       HPSYS_PINMUX_PAD_PA36_DS1_Msk
#define HPSYS_PINMUX_PAD_PA36_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA36_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_POE_Pos)
#define HPSYS_PINMUX_PAD_PA36_POE       HPSYS_PINMUX_PAD_PA36_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA37 register **************/
#define HPSYS_PINMUX_PAD_PA37_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA37_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA37_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA37_FSEL      HPSYS_PINMUX_PAD_PA37_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA37_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA37_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_PE_Pos)
#define HPSYS_PINMUX_PAD_PA37_PE        HPSYS_PINMUX_PAD_PA37_PE_Msk
#define HPSYS_PINMUX_PAD_PA37_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA37_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_PS_Pos)
#define HPSYS_PINMUX_PAD_PA37_PS        HPSYS_PINMUX_PAD_PA37_PS_Msk
#define HPSYS_PINMUX_PAD_PA37_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA37_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_IE_Pos)
#define HPSYS_PINMUX_PAD_PA37_IE        HPSYS_PINMUX_PAD_PA37_IE_Msk
#define HPSYS_PINMUX_PAD_PA37_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA37_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_IS_Pos)
#define HPSYS_PINMUX_PAD_PA37_IS        HPSYS_PINMUX_PAD_PA37_IS_Msk
#define HPSYS_PINMUX_PAD_PA37_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA37_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_SR_Pos)
#define HPSYS_PINMUX_PAD_PA37_SR        HPSYS_PINMUX_PAD_PA37_SR_Msk
#define HPSYS_PINMUX_PAD_PA37_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA37_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA37_DS0       HPSYS_PINMUX_PAD_PA37_DS0_Msk
#define HPSYS_PINMUX_PAD_PA37_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA37_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA37_DS1       HPSYS_PINMUX_PAD_PA37_DS1_Msk
#define HPSYS_PINMUX_PAD_PA37_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA37_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_POE_Pos)
#define HPSYS_PINMUX_PAD_PA37_POE       HPSYS_PINMUX_PAD_PA37_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA38 register **************/
#define HPSYS_PINMUX_PAD_PA38_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA38_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA38_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA38_FSEL      HPSYS_PINMUX_PAD_PA38_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA38_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA38_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_PE_Pos)
#define HPSYS_PINMUX_PAD_PA38_PE        HPSYS_PINMUX_PAD_PA38_PE_Msk
#define HPSYS_PINMUX_PAD_PA38_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA38_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_PS_Pos)
#define HPSYS_PINMUX_PAD_PA38_PS        HPSYS_PINMUX_PAD_PA38_PS_Msk
#define HPSYS_PINMUX_PAD_PA38_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA38_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_IE_Pos)
#define HPSYS_PINMUX_PAD_PA38_IE        HPSYS_PINMUX_PAD_PA38_IE_Msk
#define HPSYS_PINMUX_PAD_PA38_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA38_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_IS_Pos)
#define HPSYS_PINMUX_PAD_PA38_IS        HPSYS_PINMUX_PAD_PA38_IS_Msk
#define HPSYS_PINMUX_PAD_PA38_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA38_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_SR_Pos)
#define HPSYS_PINMUX_PAD_PA38_SR        HPSYS_PINMUX_PAD_PA38_SR_Msk
#define HPSYS_PINMUX_PAD_PA38_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA38_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA38_DS0       HPSYS_PINMUX_PAD_PA38_DS0_Msk
#define HPSYS_PINMUX_PAD_PA38_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA38_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA38_DS1       HPSYS_PINMUX_PAD_PA38_DS1_Msk
#define HPSYS_PINMUX_PAD_PA38_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA38_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_POE_Pos)
#define HPSYS_PINMUX_PAD_PA38_POE       HPSYS_PINMUX_PAD_PA38_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA39 register **************/
#define HPSYS_PINMUX_PAD_PA39_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA39_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA39_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA39_FSEL      HPSYS_PINMUX_PAD_PA39_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA39_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA39_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_PE_Pos)
#define HPSYS_PINMUX_PAD_PA39_PE        HPSYS_PINMUX_PAD_PA39_PE_Msk
#define HPSYS_PINMUX_PAD_PA39_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA39_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_PS_Pos)
#define HPSYS_PINMUX_PAD_PA39_PS        HPSYS_PINMUX_PAD_PA39_PS_Msk
#define HPSYS_PINMUX_PAD_PA39_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA39_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_IE_Pos)
#define HPSYS_PINMUX_PAD_PA39_IE        HPSYS_PINMUX_PAD_PA39_IE_Msk
#define HPSYS_PINMUX_PAD_PA39_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA39_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_IS_Pos)
#define HPSYS_PINMUX_PAD_PA39_IS        HPSYS_PINMUX_PAD_PA39_IS_Msk
#define HPSYS_PINMUX_PAD_PA39_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA39_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_SR_Pos)
#define HPSYS_PINMUX_PAD_PA39_SR        HPSYS_PINMUX_PAD_PA39_SR_Msk
#define HPSYS_PINMUX_PAD_PA39_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA39_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA39_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA39_DS0       HPSYS_PINMUX_PAD_PA39_DS0_Msk
#define HPSYS_PINMUX_PAD_PA39_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA39_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA39_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA39_DS1       HPSYS_PINMUX_PAD_PA39_DS1_Msk
#define HPSYS_PINMUX_PAD_PA39_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA39_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA39_POE_Pos)
#define HPSYS_PINMUX_PAD_PA39_POE       HPSYS_PINMUX_PAD_PA39_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA40 register **************/
#define HPSYS_PINMUX_PAD_PA40_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA40_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA40_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA40_FSEL      HPSYS_PINMUX_PAD_PA40_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA40_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA40_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_PE_Pos)
#define HPSYS_PINMUX_PAD_PA40_PE        HPSYS_PINMUX_PAD_PA40_PE_Msk
#define HPSYS_PINMUX_PAD_PA40_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA40_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_PS_Pos)
#define HPSYS_PINMUX_PAD_PA40_PS        HPSYS_PINMUX_PAD_PA40_PS_Msk
#define HPSYS_PINMUX_PAD_PA40_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA40_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_IE_Pos)
#define HPSYS_PINMUX_PAD_PA40_IE        HPSYS_PINMUX_PAD_PA40_IE_Msk
#define HPSYS_PINMUX_PAD_PA40_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA40_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_IS_Pos)
#define HPSYS_PINMUX_PAD_PA40_IS        HPSYS_PINMUX_PAD_PA40_IS_Msk
#define HPSYS_PINMUX_PAD_PA40_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA40_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_SR_Pos)
#define HPSYS_PINMUX_PAD_PA40_SR        HPSYS_PINMUX_PAD_PA40_SR_Msk
#define HPSYS_PINMUX_PAD_PA40_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA40_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA40_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA40_DS0       HPSYS_PINMUX_PAD_PA40_DS0_Msk
#define HPSYS_PINMUX_PAD_PA40_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA40_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA40_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA40_DS1       HPSYS_PINMUX_PAD_PA40_DS1_Msk
#define HPSYS_PINMUX_PAD_PA40_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA40_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA40_POE_Pos)
#define HPSYS_PINMUX_PAD_PA40_POE       HPSYS_PINMUX_PAD_PA40_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA41 register **************/
#define HPSYS_PINMUX_PAD_PA41_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA41_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA41_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA41_FSEL      HPSYS_PINMUX_PAD_PA41_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA41_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA41_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_PE_Pos)
#define HPSYS_PINMUX_PAD_PA41_PE        HPSYS_PINMUX_PAD_PA41_PE_Msk
#define HPSYS_PINMUX_PAD_PA41_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA41_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_PS_Pos)
#define HPSYS_PINMUX_PAD_PA41_PS        HPSYS_PINMUX_PAD_PA41_PS_Msk
#define HPSYS_PINMUX_PAD_PA41_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA41_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_IE_Pos)
#define HPSYS_PINMUX_PAD_PA41_IE        HPSYS_PINMUX_PAD_PA41_IE_Msk
#define HPSYS_PINMUX_PAD_PA41_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA41_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_IS_Pos)
#define HPSYS_PINMUX_PAD_PA41_IS        HPSYS_PINMUX_PAD_PA41_IS_Msk
#define HPSYS_PINMUX_PAD_PA41_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA41_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_SR_Pos)
#define HPSYS_PINMUX_PAD_PA41_SR        HPSYS_PINMUX_PAD_PA41_SR_Msk
#define HPSYS_PINMUX_PAD_PA41_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA41_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA41_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA41_DS0       HPSYS_PINMUX_PAD_PA41_DS0_Msk
#define HPSYS_PINMUX_PAD_PA41_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA41_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA41_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA41_DS1       HPSYS_PINMUX_PAD_PA41_DS1_Msk
#define HPSYS_PINMUX_PAD_PA41_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA41_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA41_POE_Pos)
#define HPSYS_PINMUX_PAD_PA41_POE       HPSYS_PINMUX_PAD_PA41_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA42 register **************/
#define HPSYS_PINMUX_PAD_PA42_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA42_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA42_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA42_FSEL      HPSYS_PINMUX_PAD_PA42_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA42_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA42_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_PE_Pos)
#define HPSYS_PINMUX_PAD_PA42_PE        HPSYS_PINMUX_PAD_PA42_PE_Msk
#define HPSYS_PINMUX_PAD_PA42_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA42_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_PS_Pos)
#define HPSYS_PINMUX_PAD_PA42_PS        HPSYS_PINMUX_PAD_PA42_PS_Msk
#define HPSYS_PINMUX_PAD_PA42_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA42_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_IE_Pos)
#define HPSYS_PINMUX_PAD_PA42_IE        HPSYS_PINMUX_PAD_PA42_IE_Msk
#define HPSYS_PINMUX_PAD_PA42_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA42_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_IS_Pos)
#define HPSYS_PINMUX_PAD_PA42_IS        HPSYS_PINMUX_PAD_PA42_IS_Msk
#define HPSYS_PINMUX_PAD_PA42_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA42_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_SR_Pos)
#define HPSYS_PINMUX_PAD_PA42_SR        HPSYS_PINMUX_PAD_PA42_SR_Msk
#define HPSYS_PINMUX_PAD_PA42_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA42_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA42_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA42_DS0       HPSYS_PINMUX_PAD_PA42_DS0_Msk
#define HPSYS_PINMUX_PAD_PA42_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA42_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA42_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA42_DS1       HPSYS_PINMUX_PAD_PA42_DS1_Msk
#define HPSYS_PINMUX_PAD_PA42_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA42_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA42_POE_Pos)
#define HPSYS_PINMUX_PAD_PA42_POE       HPSYS_PINMUX_PAD_PA42_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA43 register **************/
#define HPSYS_PINMUX_PAD_PA43_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA43_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA43_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA43_FSEL      HPSYS_PINMUX_PAD_PA43_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA43_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA43_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_PE_Pos)
#define HPSYS_PINMUX_PAD_PA43_PE        HPSYS_PINMUX_PAD_PA43_PE_Msk
#define HPSYS_PINMUX_PAD_PA43_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA43_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_PS_Pos)
#define HPSYS_PINMUX_PAD_PA43_PS        HPSYS_PINMUX_PAD_PA43_PS_Msk
#define HPSYS_PINMUX_PAD_PA43_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA43_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_IE_Pos)
#define HPSYS_PINMUX_PAD_PA43_IE        HPSYS_PINMUX_PAD_PA43_IE_Msk
#define HPSYS_PINMUX_PAD_PA43_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA43_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_IS_Pos)
#define HPSYS_PINMUX_PAD_PA43_IS        HPSYS_PINMUX_PAD_PA43_IS_Msk
#define HPSYS_PINMUX_PAD_PA43_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA43_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_SR_Pos)
#define HPSYS_PINMUX_PAD_PA43_SR        HPSYS_PINMUX_PAD_PA43_SR_Msk
#define HPSYS_PINMUX_PAD_PA43_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA43_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA43_DS0       HPSYS_PINMUX_PAD_PA43_DS0_Msk
#define HPSYS_PINMUX_PAD_PA43_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA43_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA43_DS1       HPSYS_PINMUX_PAD_PA43_DS1_Msk
#define HPSYS_PINMUX_PAD_PA43_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA43_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_POE_Pos)
#define HPSYS_PINMUX_PAD_PA43_POE       HPSYS_PINMUX_PAD_PA43_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA44 register **************/
#define HPSYS_PINMUX_PAD_PA44_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA44_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA44_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA44_FSEL      HPSYS_PINMUX_PAD_PA44_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA44_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA44_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_PE_Pos)
#define HPSYS_PINMUX_PAD_PA44_PE        HPSYS_PINMUX_PAD_PA44_PE_Msk
#define HPSYS_PINMUX_PAD_PA44_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA44_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_PS_Pos)
#define HPSYS_PINMUX_PAD_PA44_PS        HPSYS_PINMUX_PAD_PA44_PS_Msk
#define HPSYS_PINMUX_PAD_PA44_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA44_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_IE_Pos)
#define HPSYS_PINMUX_PAD_PA44_IE        HPSYS_PINMUX_PAD_PA44_IE_Msk
#define HPSYS_PINMUX_PAD_PA44_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA44_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_IS_Pos)
#define HPSYS_PINMUX_PAD_PA44_IS        HPSYS_PINMUX_PAD_PA44_IS_Msk
#define HPSYS_PINMUX_PAD_PA44_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA44_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_SR_Pos)
#define HPSYS_PINMUX_PAD_PA44_SR        HPSYS_PINMUX_PAD_PA44_SR_Msk
#define HPSYS_PINMUX_PAD_PA44_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA44_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA44_DS0       HPSYS_PINMUX_PAD_PA44_DS0_Msk
#define HPSYS_PINMUX_PAD_PA44_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA44_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA44_DS1       HPSYS_PINMUX_PAD_PA44_DS1_Msk
#define HPSYS_PINMUX_PAD_PA44_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA44_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_POE_Pos)
#define HPSYS_PINMUX_PAD_PA44_POE       HPSYS_PINMUX_PAD_PA44_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA45 register **************/
#define HPSYS_PINMUX_PAD_PA45_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA45_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA45_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA45_FSEL      HPSYS_PINMUX_PAD_PA45_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA45_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA45_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA45_PE_Pos)
#define HPSYS_PINMUX_PAD_PA45_PE        HPSYS_PINMUX_PAD_PA45_PE_Msk
#define HPSYS_PINMUX_PAD_PA45_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA45_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA45_PS_Pos)
#define HPSYS_PINMUX_PAD_PA45_PS        HPSYS_PINMUX_PAD_PA45_PS_Msk
#define HPSYS_PINMUX_PAD_PA45_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA45_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA45_IE_Pos)
#define HPSYS_PINMUX_PAD_PA45_IE        HPSYS_PINMUX_PAD_PA45_IE_Msk
#define HPSYS_PINMUX_PAD_PA45_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA45_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA45_IS_Pos)
#define HPSYS_PINMUX_PAD_PA45_IS        HPSYS_PINMUX_PAD_PA45_IS_Msk
#define HPSYS_PINMUX_PAD_PA45_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA45_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA45_SR_Pos)
#define HPSYS_PINMUX_PAD_PA45_SR        HPSYS_PINMUX_PAD_PA45_SR_Msk
#define HPSYS_PINMUX_PAD_PA45_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA45_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA45_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA45_DS0       HPSYS_PINMUX_PAD_PA45_DS0_Msk
#define HPSYS_PINMUX_PAD_PA45_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA45_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA45_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA45_DS1       HPSYS_PINMUX_PAD_PA45_DS1_Msk
#define HPSYS_PINMUX_PAD_PA45_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA45_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA45_POE_Pos)
#define HPSYS_PINMUX_PAD_PA45_POE       HPSYS_PINMUX_PAD_PA45_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA46 register **************/
#define HPSYS_PINMUX_PAD_PA46_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA46_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA46_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA46_FSEL      HPSYS_PINMUX_PAD_PA46_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA46_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA46_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA46_PE_Pos)
#define HPSYS_PINMUX_PAD_PA46_PE        HPSYS_PINMUX_PAD_PA46_PE_Msk
#define HPSYS_PINMUX_PAD_PA46_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA46_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA46_PS_Pos)
#define HPSYS_PINMUX_PAD_PA46_PS        HPSYS_PINMUX_PAD_PA46_PS_Msk
#define HPSYS_PINMUX_PAD_PA46_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA46_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA46_IE_Pos)
#define HPSYS_PINMUX_PAD_PA46_IE        HPSYS_PINMUX_PAD_PA46_IE_Msk
#define HPSYS_PINMUX_PAD_PA46_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA46_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA46_IS_Pos)
#define HPSYS_PINMUX_PAD_PA46_IS        HPSYS_PINMUX_PAD_PA46_IS_Msk
#define HPSYS_PINMUX_PAD_PA46_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA46_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA46_SR_Pos)
#define HPSYS_PINMUX_PAD_PA46_SR        HPSYS_PINMUX_PAD_PA46_SR_Msk
#define HPSYS_PINMUX_PAD_PA46_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA46_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA46_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA46_DS0       HPSYS_PINMUX_PAD_PA46_DS0_Msk
#define HPSYS_PINMUX_PAD_PA46_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA46_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA46_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA46_DS1       HPSYS_PINMUX_PAD_PA46_DS1_Msk
#define HPSYS_PINMUX_PAD_PA46_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA46_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA46_POE_Pos)
#define HPSYS_PINMUX_PAD_PA46_POE       HPSYS_PINMUX_PAD_PA46_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA47 register **************/
#define HPSYS_PINMUX_PAD_PA47_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA47_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA47_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA47_FSEL      HPSYS_PINMUX_PAD_PA47_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA47_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA47_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA47_PE_Pos)
#define HPSYS_PINMUX_PAD_PA47_PE        HPSYS_PINMUX_PAD_PA47_PE_Msk
#define HPSYS_PINMUX_PAD_PA47_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA47_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA47_PS_Pos)
#define HPSYS_PINMUX_PAD_PA47_PS        HPSYS_PINMUX_PAD_PA47_PS_Msk
#define HPSYS_PINMUX_PAD_PA47_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA47_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA47_IE_Pos)
#define HPSYS_PINMUX_PAD_PA47_IE        HPSYS_PINMUX_PAD_PA47_IE_Msk
#define HPSYS_PINMUX_PAD_PA47_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA47_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA47_IS_Pos)
#define HPSYS_PINMUX_PAD_PA47_IS        HPSYS_PINMUX_PAD_PA47_IS_Msk
#define HPSYS_PINMUX_PAD_PA47_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA47_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA47_SR_Pos)
#define HPSYS_PINMUX_PAD_PA47_SR        HPSYS_PINMUX_PAD_PA47_SR_Msk
#define HPSYS_PINMUX_PAD_PA47_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA47_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA47_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA47_DS0       HPSYS_PINMUX_PAD_PA47_DS0_Msk
#define HPSYS_PINMUX_PAD_PA47_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA47_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA47_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA47_DS1       HPSYS_PINMUX_PAD_PA47_DS1_Msk
#define HPSYS_PINMUX_PAD_PA47_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA47_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA47_POE_Pos)
#define HPSYS_PINMUX_PAD_PA47_POE       HPSYS_PINMUX_PAD_PA47_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA48 register **************/
#define HPSYS_PINMUX_PAD_PA48_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA48_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA48_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA48_FSEL      HPSYS_PINMUX_PAD_PA48_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA48_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA48_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA48_PE_Pos)
#define HPSYS_PINMUX_PAD_PA48_PE        HPSYS_PINMUX_PAD_PA48_PE_Msk
#define HPSYS_PINMUX_PAD_PA48_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA48_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA48_PS_Pos)
#define HPSYS_PINMUX_PAD_PA48_PS        HPSYS_PINMUX_PAD_PA48_PS_Msk
#define HPSYS_PINMUX_PAD_PA48_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA48_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA48_IE_Pos)
#define HPSYS_PINMUX_PAD_PA48_IE        HPSYS_PINMUX_PAD_PA48_IE_Msk
#define HPSYS_PINMUX_PAD_PA48_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA48_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA48_IS_Pos)
#define HPSYS_PINMUX_PAD_PA48_IS        HPSYS_PINMUX_PAD_PA48_IS_Msk
#define HPSYS_PINMUX_PAD_PA48_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA48_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA48_SR_Pos)
#define HPSYS_PINMUX_PAD_PA48_SR        HPSYS_PINMUX_PAD_PA48_SR_Msk
#define HPSYS_PINMUX_PAD_PA48_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA48_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA48_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA48_DS0       HPSYS_PINMUX_PAD_PA48_DS0_Msk
#define HPSYS_PINMUX_PAD_PA48_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA48_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA48_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA48_DS1       HPSYS_PINMUX_PAD_PA48_DS1_Msk
#define HPSYS_PINMUX_PAD_PA48_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA48_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA48_POE_Pos)
#define HPSYS_PINMUX_PAD_PA48_POE       HPSYS_PINMUX_PAD_PA48_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA49 register **************/
#define HPSYS_PINMUX_PAD_PA49_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA49_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA49_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA49_FSEL      HPSYS_PINMUX_PAD_PA49_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA49_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA49_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA49_PE_Pos)
#define HPSYS_PINMUX_PAD_PA49_PE        HPSYS_PINMUX_PAD_PA49_PE_Msk
#define HPSYS_PINMUX_PAD_PA49_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA49_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA49_PS_Pos)
#define HPSYS_PINMUX_PAD_PA49_PS        HPSYS_PINMUX_PAD_PA49_PS_Msk
#define HPSYS_PINMUX_PAD_PA49_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA49_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA49_IE_Pos)
#define HPSYS_PINMUX_PAD_PA49_IE        HPSYS_PINMUX_PAD_PA49_IE_Msk
#define HPSYS_PINMUX_PAD_PA49_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA49_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA49_IS_Pos)
#define HPSYS_PINMUX_PAD_PA49_IS        HPSYS_PINMUX_PAD_PA49_IS_Msk
#define HPSYS_PINMUX_PAD_PA49_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA49_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA49_SR_Pos)
#define HPSYS_PINMUX_PAD_PA49_SR        HPSYS_PINMUX_PAD_PA49_SR_Msk
#define HPSYS_PINMUX_PAD_PA49_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA49_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA49_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA49_DS0       HPSYS_PINMUX_PAD_PA49_DS0_Msk
#define HPSYS_PINMUX_PAD_PA49_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA49_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA49_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA49_DS1       HPSYS_PINMUX_PAD_PA49_DS1_Msk
#define HPSYS_PINMUX_PAD_PA49_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA49_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA49_POE_Pos)
#define HPSYS_PINMUX_PAD_PA49_POE       HPSYS_PINMUX_PAD_PA49_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA50 register **************/
#define HPSYS_PINMUX_PAD_PA50_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA50_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA50_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA50_FSEL      HPSYS_PINMUX_PAD_PA50_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA50_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA50_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA50_PE_Pos)
#define HPSYS_PINMUX_PAD_PA50_PE        HPSYS_PINMUX_PAD_PA50_PE_Msk
#define HPSYS_PINMUX_PAD_PA50_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA50_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA50_PS_Pos)
#define HPSYS_PINMUX_PAD_PA50_PS        HPSYS_PINMUX_PAD_PA50_PS_Msk
#define HPSYS_PINMUX_PAD_PA50_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA50_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA50_IE_Pos)
#define HPSYS_PINMUX_PAD_PA50_IE        HPSYS_PINMUX_PAD_PA50_IE_Msk
#define HPSYS_PINMUX_PAD_PA50_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA50_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA50_IS_Pos)
#define HPSYS_PINMUX_PAD_PA50_IS        HPSYS_PINMUX_PAD_PA50_IS_Msk
#define HPSYS_PINMUX_PAD_PA50_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA50_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA50_SR_Pos)
#define HPSYS_PINMUX_PAD_PA50_SR        HPSYS_PINMUX_PAD_PA50_SR_Msk
#define HPSYS_PINMUX_PAD_PA50_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA50_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA50_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA50_DS0       HPSYS_PINMUX_PAD_PA50_DS0_Msk
#define HPSYS_PINMUX_PAD_PA50_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA50_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA50_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA50_DS1       HPSYS_PINMUX_PAD_PA50_DS1_Msk
#define HPSYS_PINMUX_PAD_PA50_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA50_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA50_POE_Pos)
#define HPSYS_PINMUX_PAD_PA50_POE       HPSYS_PINMUX_PAD_PA50_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA51 register **************/
#define HPSYS_PINMUX_PAD_PA51_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA51_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA51_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA51_FSEL      HPSYS_PINMUX_PAD_PA51_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA51_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA51_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA51_PE_Pos)
#define HPSYS_PINMUX_PAD_PA51_PE        HPSYS_PINMUX_PAD_PA51_PE_Msk
#define HPSYS_PINMUX_PAD_PA51_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA51_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA51_PS_Pos)
#define HPSYS_PINMUX_PAD_PA51_PS        HPSYS_PINMUX_PAD_PA51_PS_Msk
#define HPSYS_PINMUX_PAD_PA51_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA51_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA51_IE_Pos)
#define HPSYS_PINMUX_PAD_PA51_IE        HPSYS_PINMUX_PAD_PA51_IE_Msk
#define HPSYS_PINMUX_PAD_PA51_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA51_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA51_IS_Pos)
#define HPSYS_PINMUX_PAD_PA51_IS        HPSYS_PINMUX_PAD_PA51_IS_Msk
#define HPSYS_PINMUX_PAD_PA51_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA51_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA51_SR_Pos)
#define HPSYS_PINMUX_PAD_PA51_SR        HPSYS_PINMUX_PAD_PA51_SR_Msk
#define HPSYS_PINMUX_PAD_PA51_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA51_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA51_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA51_DS0       HPSYS_PINMUX_PAD_PA51_DS0_Msk
#define HPSYS_PINMUX_PAD_PA51_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA51_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA51_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA51_DS1       HPSYS_PINMUX_PAD_PA51_DS1_Msk
#define HPSYS_PINMUX_PAD_PA51_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA51_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA51_POE_Pos)
#define HPSYS_PINMUX_PAD_PA51_POE       HPSYS_PINMUX_PAD_PA51_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA52 register **************/
#define HPSYS_PINMUX_PAD_PA52_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA52_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA52_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA52_FSEL      HPSYS_PINMUX_PAD_PA52_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA52_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA52_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA52_PE_Pos)
#define HPSYS_PINMUX_PAD_PA52_PE        HPSYS_PINMUX_PAD_PA52_PE_Msk
#define HPSYS_PINMUX_PAD_PA52_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA52_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA52_PS_Pos)
#define HPSYS_PINMUX_PAD_PA52_PS        HPSYS_PINMUX_PAD_PA52_PS_Msk
#define HPSYS_PINMUX_PAD_PA52_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA52_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA52_IE_Pos)
#define HPSYS_PINMUX_PAD_PA52_IE        HPSYS_PINMUX_PAD_PA52_IE_Msk
#define HPSYS_PINMUX_PAD_PA52_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA52_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA52_IS_Pos)
#define HPSYS_PINMUX_PAD_PA52_IS        HPSYS_PINMUX_PAD_PA52_IS_Msk
#define HPSYS_PINMUX_PAD_PA52_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA52_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA52_SR_Pos)
#define HPSYS_PINMUX_PAD_PA52_SR        HPSYS_PINMUX_PAD_PA52_SR_Msk
#define HPSYS_PINMUX_PAD_PA52_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA52_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA52_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA52_DS0       HPSYS_PINMUX_PAD_PA52_DS0_Msk
#define HPSYS_PINMUX_PAD_PA52_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA52_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA52_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA52_DS1       HPSYS_PINMUX_PAD_PA52_DS1_Msk
#define HPSYS_PINMUX_PAD_PA52_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA52_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA52_POE_Pos)
#define HPSYS_PINMUX_PAD_PA52_POE       HPSYS_PINMUX_PAD_PA52_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA53 register **************/
#define HPSYS_PINMUX_PAD_PA53_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA53_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA53_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA53_FSEL      HPSYS_PINMUX_PAD_PA53_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA53_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA53_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA53_PE_Pos)
#define HPSYS_PINMUX_PAD_PA53_PE        HPSYS_PINMUX_PAD_PA53_PE_Msk
#define HPSYS_PINMUX_PAD_PA53_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA53_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA53_PS_Pos)
#define HPSYS_PINMUX_PAD_PA53_PS        HPSYS_PINMUX_PAD_PA53_PS_Msk
#define HPSYS_PINMUX_PAD_PA53_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA53_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA53_IE_Pos)
#define HPSYS_PINMUX_PAD_PA53_IE        HPSYS_PINMUX_PAD_PA53_IE_Msk
#define HPSYS_PINMUX_PAD_PA53_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA53_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA53_IS_Pos)
#define HPSYS_PINMUX_PAD_PA53_IS        HPSYS_PINMUX_PAD_PA53_IS_Msk
#define HPSYS_PINMUX_PAD_PA53_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA53_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA53_SR_Pos)
#define HPSYS_PINMUX_PAD_PA53_SR        HPSYS_PINMUX_PAD_PA53_SR_Msk
#define HPSYS_PINMUX_PAD_PA53_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA53_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA53_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA53_DS0       HPSYS_PINMUX_PAD_PA53_DS0_Msk
#define HPSYS_PINMUX_PAD_PA53_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA53_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA53_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA53_DS1       HPSYS_PINMUX_PAD_PA53_DS1_Msk
#define HPSYS_PINMUX_PAD_PA53_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA53_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA53_POE_Pos)
#define HPSYS_PINMUX_PAD_PA53_POE       HPSYS_PINMUX_PAD_PA53_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA54 register **************/
#define HPSYS_PINMUX_PAD_PA54_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA54_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA54_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA54_FSEL      HPSYS_PINMUX_PAD_PA54_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA54_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA54_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA54_PE_Pos)
#define HPSYS_PINMUX_PAD_PA54_PE        HPSYS_PINMUX_PAD_PA54_PE_Msk
#define HPSYS_PINMUX_PAD_PA54_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA54_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA54_PS_Pos)
#define HPSYS_PINMUX_PAD_PA54_PS        HPSYS_PINMUX_PAD_PA54_PS_Msk
#define HPSYS_PINMUX_PAD_PA54_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA54_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA54_IE_Pos)
#define HPSYS_PINMUX_PAD_PA54_IE        HPSYS_PINMUX_PAD_PA54_IE_Msk
#define HPSYS_PINMUX_PAD_PA54_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA54_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA54_IS_Pos)
#define HPSYS_PINMUX_PAD_PA54_IS        HPSYS_PINMUX_PAD_PA54_IS_Msk
#define HPSYS_PINMUX_PAD_PA54_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA54_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA54_SR_Pos)
#define HPSYS_PINMUX_PAD_PA54_SR        HPSYS_PINMUX_PAD_PA54_SR_Msk
#define HPSYS_PINMUX_PAD_PA54_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA54_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA54_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA54_DS0       HPSYS_PINMUX_PAD_PA54_DS0_Msk
#define HPSYS_PINMUX_PAD_PA54_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA54_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA54_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA54_DS1       HPSYS_PINMUX_PAD_PA54_DS1_Msk
#define HPSYS_PINMUX_PAD_PA54_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA54_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA54_POE_Pos)
#define HPSYS_PINMUX_PAD_PA54_POE       HPSYS_PINMUX_PAD_PA54_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA55 register **************/
#define HPSYS_PINMUX_PAD_PA55_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA55_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA55_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA55_FSEL      HPSYS_PINMUX_PAD_PA55_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA55_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA55_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA55_PE_Pos)
#define HPSYS_PINMUX_PAD_PA55_PE        HPSYS_PINMUX_PAD_PA55_PE_Msk
#define HPSYS_PINMUX_PAD_PA55_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA55_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA55_PS_Pos)
#define HPSYS_PINMUX_PAD_PA55_PS        HPSYS_PINMUX_PAD_PA55_PS_Msk
#define HPSYS_PINMUX_PAD_PA55_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA55_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA55_IE_Pos)
#define HPSYS_PINMUX_PAD_PA55_IE        HPSYS_PINMUX_PAD_PA55_IE_Msk
#define HPSYS_PINMUX_PAD_PA55_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA55_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA55_IS_Pos)
#define HPSYS_PINMUX_PAD_PA55_IS        HPSYS_PINMUX_PAD_PA55_IS_Msk
#define HPSYS_PINMUX_PAD_PA55_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA55_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA55_SR_Pos)
#define HPSYS_PINMUX_PAD_PA55_SR        HPSYS_PINMUX_PAD_PA55_SR_Msk
#define HPSYS_PINMUX_PAD_PA55_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA55_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA55_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA55_DS0       HPSYS_PINMUX_PAD_PA55_DS0_Msk
#define HPSYS_PINMUX_PAD_PA55_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA55_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA55_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA55_DS1       HPSYS_PINMUX_PAD_PA55_DS1_Msk
#define HPSYS_PINMUX_PAD_PA55_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA55_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA55_POE_Pos)
#define HPSYS_PINMUX_PAD_PA55_POE       HPSYS_PINMUX_PAD_PA55_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA56 register **************/
#define HPSYS_PINMUX_PAD_PA56_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA56_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA56_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA56_FSEL      HPSYS_PINMUX_PAD_PA56_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA56_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA56_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA56_PE_Pos)
#define HPSYS_PINMUX_PAD_PA56_PE        HPSYS_PINMUX_PAD_PA56_PE_Msk
#define HPSYS_PINMUX_PAD_PA56_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA56_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA56_PS_Pos)
#define HPSYS_PINMUX_PAD_PA56_PS        HPSYS_PINMUX_PAD_PA56_PS_Msk
#define HPSYS_PINMUX_PAD_PA56_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA56_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA56_IE_Pos)
#define HPSYS_PINMUX_PAD_PA56_IE        HPSYS_PINMUX_PAD_PA56_IE_Msk
#define HPSYS_PINMUX_PAD_PA56_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA56_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA56_IS_Pos)
#define HPSYS_PINMUX_PAD_PA56_IS        HPSYS_PINMUX_PAD_PA56_IS_Msk
#define HPSYS_PINMUX_PAD_PA56_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA56_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA56_SR_Pos)
#define HPSYS_PINMUX_PAD_PA56_SR        HPSYS_PINMUX_PAD_PA56_SR_Msk
#define HPSYS_PINMUX_PAD_PA56_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA56_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA56_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA56_DS0       HPSYS_PINMUX_PAD_PA56_DS0_Msk
#define HPSYS_PINMUX_PAD_PA56_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA56_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA56_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA56_DS1       HPSYS_PINMUX_PAD_PA56_DS1_Msk
#define HPSYS_PINMUX_PAD_PA56_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA56_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA56_POE_Pos)
#define HPSYS_PINMUX_PAD_PA56_POE       HPSYS_PINMUX_PAD_PA56_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA57 register **************/
#define HPSYS_PINMUX_PAD_PA57_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA57_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA57_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA57_FSEL      HPSYS_PINMUX_PAD_PA57_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA57_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA57_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA57_PE_Pos)
#define HPSYS_PINMUX_PAD_PA57_PE        HPSYS_PINMUX_PAD_PA57_PE_Msk
#define HPSYS_PINMUX_PAD_PA57_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA57_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA57_PS_Pos)
#define HPSYS_PINMUX_PAD_PA57_PS        HPSYS_PINMUX_PAD_PA57_PS_Msk
#define HPSYS_PINMUX_PAD_PA57_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA57_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA57_IE_Pos)
#define HPSYS_PINMUX_PAD_PA57_IE        HPSYS_PINMUX_PAD_PA57_IE_Msk
#define HPSYS_PINMUX_PAD_PA57_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA57_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA57_IS_Pos)
#define HPSYS_PINMUX_PAD_PA57_IS        HPSYS_PINMUX_PAD_PA57_IS_Msk
#define HPSYS_PINMUX_PAD_PA57_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA57_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA57_SR_Pos)
#define HPSYS_PINMUX_PAD_PA57_SR        HPSYS_PINMUX_PAD_PA57_SR_Msk
#define HPSYS_PINMUX_PAD_PA57_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA57_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA57_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA57_DS0       HPSYS_PINMUX_PAD_PA57_DS0_Msk
#define HPSYS_PINMUX_PAD_PA57_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA57_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA57_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA57_DS1       HPSYS_PINMUX_PAD_PA57_DS1_Msk
#define HPSYS_PINMUX_PAD_PA57_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA57_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA57_POE_Pos)
#define HPSYS_PINMUX_PAD_PA57_POE       HPSYS_PINMUX_PAD_PA57_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA58 register **************/
#define HPSYS_PINMUX_PAD_PA58_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA58_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA58_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA58_FSEL      HPSYS_PINMUX_PAD_PA58_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA58_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA58_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA58_PE_Pos)
#define HPSYS_PINMUX_PAD_PA58_PE        HPSYS_PINMUX_PAD_PA58_PE_Msk
#define HPSYS_PINMUX_PAD_PA58_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA58_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA58_PS_Pos)
#define HPSYS_PINMUX_PAD_PA58_PS        HPSYS_PINMUX_PAD_PA58_PS_Msk
#define HPSYS_PINMUX_PAD_PA58_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA58_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA58_IE_Pos)
#define HPSYS_PINMUX_PAD_PA58_IE        HPSYS_PINMUX_PAD_PA58_IE_Msk
#define HPSYS_PINMUX_PAD_PA58_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA58_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA58_IS_Pos)
#define HPSYS_PINMUX_PAD_PA58_IS        HPSYS_PINMUX_PAD_PA58_IS_Msk
#define HPSYS_PINMUX_PAD_PA58_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA58_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA58_SR_Pos)
#define HPSYS_PINMUX_PAD_PA58_SR        HPSYS_PINMUX_PAD_PA58_SR_Msk
#define HPSYS_PINMUX_PAD_PA58_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA58_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA58_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA58_DS0       HPSYS_PINMUX_PAD_PA58_DS0_Msk
#define HPSYS_PINMUX_PAD_PA58_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA58_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA58_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA58_DS1       HPSYS_PINMUX_PAD_PA58_DS1_Msk
#define HPSYS_PINMUX_PAD_PA58_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA58_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA58_POE_Pos)
#define HPSYS_PINMUX_PAD_PA58_POE       HPSYS_PINMUX_PAD_PA58_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA59 register **************/
#define HPSYS_PINMUX_PAD_PA59_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA59_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA59_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA59_FSEL      HPSYS_PINMUX_PAD_PA59_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA59_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA59_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA59_PE_Pos)
#define HPSYS_PINMUX_PAD_PA59_PE        HPSYS_PINMUX_PAD_PA59_PE_Msk
#define HPSYS_PINMUX_PAD_PA59_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA59_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA59_PS_Pos)
#define HPSYS_PINMUX_PAD_PA59_PS        HPSYS_PINMUX_PAD_PA59_PS_Msk
#define HPSYS_PINMUX_PAD_PA59_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA59_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA59_IE_Pos)
#define HPSYS_PINMUX_PAD_PA59_IE        HPSYS_PINMUX_PAD_PA59_IE_Msk
#define HPSYS_PINMUX_PAD_PA59_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA59_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA59_IS_Pos)
#define HPSYS_PINMUX_PAD_PA59_IS        HPSYS_PINMUX_PAD_PA59_IS_Msk
#define HPSYS_PINMUX_PAD_PA59_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA59_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA59_SR_Pos)
#define HPSYS_PINMUX_PAD_PA59_SR        HPSYS_PINMUX_PAD_PA59_SR_Msk
#define HPSYS_PINMUX_PAD_PA59_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA59_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA59_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA59_DS0       HPSYS_PINMUX_PAD_PA59_DS0_Msk
#define HPSYS_PINMUX_PAD_PA59_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA59_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA59_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA59_DS1       HPSYS_PINMUX_PAD_PA59_DS1_Msk
#define HPSYS_PINMUX_PAD_PA59_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA59_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA59_POE_Pos)
#define HPSYS_PINMUX_PAD_PA59_POE       HPSYS_PINMUX_PAD_PA59_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA60 register **************/
#define HPSYS_PINMUX_PAD_PA60_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA60_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA60_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA60_FSEL      HPSYS_PINMUX_PAD_PA60_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA60_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA60_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA60_PE_Pos)
#define HPSYS_PINMUX_PAD_PA60_PE        HPSYS_PINMUX_PAD_PA60_PE_Msk
#define HPSYS_PINMUX_PAD_PA60_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA60_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA60_PS_Pos)
#define HPSYS_PINMUX_PAD_PA60_PS        HPSYS_PINMUX_PAD_PA60_PS_Msk
#define HPSYS_PINMUX_PAD_PA60_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA60_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA60_IE_Pos)
#define HPSYS_PINMUX_PAD_PA60_IE        HPSYS_PINMUX_PAD_PA60_IE_Msk
#define HPSYS_PINMUX_PAD_PA60_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA60_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA60_IS_Pos)
#define HPSYS_PINMUX_PAD_PA60_IS        HPSYS_PINMUX_PAD_PA60_IS_Msk
#define HPSYS_PINMUX_PAD_PA60_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA60_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA60_SR_Pos)
#define HPSYS_PINMUX_PAD_PA60_SR        HPSYS_PINMUX_PAD_PA60_SR_Msk
#define HPSYS_PINMUX_PAD_PA60_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA60_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA60_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA60_DS0       HPSYS_PINMUX_PAD_PA60_DS0_Msk
#define HPSYS_PINMUX_PAD_PA60_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA60_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA60_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA60_DS1       HPSYS_PINMUX_PAD_PA60_DS1_Msk
#define HPSYS_PINMUX_PAD_PA60_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA60_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA60_POE_Pos)
#define HPSYS_PINMUX_PAD_PA60_POE       HPSYS_PINMUX_PAD_PA60_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA61 register **************/
#define HPSYS_PINMUX_PAD_PA61_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA61_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA61_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA61_FSEL      HPSYS_PINMUX_PAD_PA61_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA61_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA61_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA61_PE_Pos)
#define HPSYS_PINMUX_PAD_PA61_PE        HPSYS_PINMUX_PAD_PA61_PE_Msk
#define HPSYS_PINMUX_PAD_PA61_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA61_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA61_PS_Pos)
#define HPSYS_PINMUX_PAD_PA61_PS        HPSYS_PINMUX_PAD_PA61_PS_Msk
#define HPSYS_PINMUX_PAD_PA61_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA61_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA61_IE_Pos)
#define HPSYS_PINMUX_PAD_PA61_IE        HPSYS_PINMUX_PAD_PA61_IE_Msk
#define HPSYS_PINMUX_PAD_PA61_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA61_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA61_IS_Pos)
#define HPSYS_PINMUX_PAD_PA61_IS        HPSYS_PINMUX_PAD_PA61_IS_Msk
#define HPSYS_PINMUX_PAD_PA61_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA61_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA61_SR_Pos)
#define HPSYS_PINMUX_PAD_PA61_SR        HPSYS_PINMUX_PAD_PA61_SR_Msk
#define HPSYS_PINMUX_PAD_PA61_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA61_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA61_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA61_DS0       HPSYS_PINMUX_PAD_PA61_DS0_Msk
#define HPSYS_PINMUX_PAD_PA61_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA61_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA61_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA61_DS1       HPSYS_PINMUX_PAD_PA61_DS1_Msk
#define HPSYS_PINMUX_PAD_PA61_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA61_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA61_POE_Pos)
#define HPSYS_PINMUX_PAD_PA61_POE       HPSYS_PINMUX_PAD_PA61_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA62 register **************/
#define HPSYS_PINMUX_PAD_PA62_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA62_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA62_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA62_FSEL      HPSYS_PINMUX_PAD_PA62_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA62_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA62_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA62_PE_Pos)
#define HPSYS_PINMUX_PAD_PA62_PE        HPSYS_PINMUX_PAD_PA62_PE_Msk
#define HPSYS_PINMUX_PAD_PA62_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA62_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA62_PS_Pos)
#define HPSYS_PINMUX_PAD_PA62_PS        HPSYS_PINMUX_PAD_PA62_PS_Msk
#define HPSYS_PINMUX_PAD_PA62_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA62_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA62_IE_Pos)
#define HPSYS_PINMUX_PAD_PA62_IE        HPSYS_PINMUX_PAD_PA62_IE_Msk
#define HPSYS_PINMUX_PAD_PA62_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA62_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA62_IS_Pos)
#define HPSYS_PINMUX_PAD_PA62_IS        HPSYS_PINMUX_PAD_PA62_IS_Msk
#define HPSYS_PINMUX_PAD_PA62_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA62_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA62_SR_Pos)
#define HPSYS_PINMUX_PAD_PA62_SR        HPSYS_PINMUX_PAD_PA62_SR_Msk
#define HPSYS_PINMUX_PAD_PA62_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA62_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA62_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA62_DS0       HPSYS_PINMUX_PAD_PA62_DS0_Msk
#define HPSYS_PINMUX_PAD_PA62_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA62_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA62_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA62_DS1       HPSYS_PINMUX_PAD_PA62_DS1_Msk
#define HPSYS_PINMUX_PAD_PA62_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA62_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA62_POE_Pos)
#define HPSYS_PINMUX_PAD_PA62_POE       HPSYS_PINMUX_PAD_PA62_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA63 register **************/
#define HPSYS_PINMUX_PAD_PA63_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA63_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA63_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA63_FSEL      HPSYS_PINMUX_PAD_PA63_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA63_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA63_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA63_PE_Pos)
#define HPSYS_PINMUX_PAD_PA63_PE        HPSYS_PINMUX_PAD_PA63_PE_Msk
#define HPSYS_PINMUX_PAD_PA63_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA63_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA63_PS_Pos)
#define HPSYS_PINMUX_PAD_PA63_PS        HPSYS_PINMUX_PAD_PA63_PS_Msk
#define HPSYS_PINMUX_PAD_PA63_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA63_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA63_IE_Pos)
#define HPSYS_PINMUX_PAD_PA63_IE        HPSYS_PINMUX_PAD_PA63_IE_Msk
#define HPSYS_PINMUX_PAD_PA63_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA63_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA63_IS_Pos)
#define HPSYS_PINMUX_PAD_PA63_IS        HPSYS_PINMUX_PAD_PA63_IS_Msk
#define HPSYS_PINMUX_PAD_PA63_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA63_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA63_SR_Pos)
#define HPSYS_PINMUX_PAD_PA63_SR        HPSYS_PINMUX_PAD_PA63_SR_Msk
#define HPSYS_PINMUX_PAD_PA63_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA63_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA63_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA63_DS0       HPSYS_PINMUX_PAD_PA63_DS0_Msk
#define HPSYS_PINMUX_PAD_PA63_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA63_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA63_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA63_DS1       HPSYS_PINMUX_PAD_PA63_DS1_Msk
#define HPSYS_PINMUX_PAD_PA63_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA63_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA63_POE_Pos)
#define HPSYS_PINMUX_PAD_PA63_POE       HPSYS_PINMUX_PAD_PA63_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA64 register **************/
#define HPSYS_PINMUX_PAD_PA64_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA64_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA64_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA64_FSEL      HPSYS_PINMUX_PAD_PA64_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA64_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA64_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA64_PE_Pos)
#define HPSYS_PINMUX_PAD_PA64_PE        HPSYS_PINMUX_PAD_PA64_PE_Msk
#define HPSYS_PINMUX_PAD_PA64_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA64_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA64_PS_Pos)
#define HPSYS_PINMUX_PAD_PA64_PS        HPSYS_PINMUX_PAD_PA64_PS_Msk
#define HPSYS_PINMUX_PAD_PA64_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA64_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA64_IE_Pos)
#define HPSYS_PINMUX_PAD_PA64_IE        HPSYS_PINMUX_PAD_PA64_IE_Msk
#define HPSYS_PINMUX_PAD_PA64_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA64_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA64_IS_Pos)
#define HPSYS_PINMUX_PAD_PA64_IS        HPSYS_PINMUX_PAD_PA64_IS_Msk
#define HPSYS_PINMUX_PAD_PA64_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA64_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA64_SR_Pos)
#define HPSYS_PINMUX_PAD_PA64_SR        HPSYS_PINMUX_PAD_PA64_SR_Msk
#define HPSYS_PINMUX_PAD_PA64_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA64_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA64_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA64_DS0       HPSYS_PINMUX_PAD_PA64_DS0_Msk
#define HPSYS_PINMUX_PAD_PA64_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA64_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA64_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA64_DS1       HPSYS_PINMUX_PAD_PA64_DS1_Msk
#define HPSYS_PINMUX_PAD_PA64_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA64_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA64_POE_Pos)
#define HPSYS_PINMUX_PAD_PA64_POE       HPSYS_PINMUX_PAD_PA64_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA65 register **************/
#define HPSYS_PINMUX_PAD_PA65_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA65_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA65_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA65_FSEL      HPSYS_PINMUX_PAD_PA65_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA65_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA65_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA65_PE_Pos)
#define HPSYS_PINMUX_PAD_PA65_PE        HPSYS_PINMUX_PAD_PA65_PE_Msk
#define HPSYS_PINMUX_PAD_PA65_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA65_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA65_PS_Pos)
#define HPSYS_PINMUX_PAD_PA65_PS        HPSYS_PINMUX_PAD_PA65_PS_Msk
#define HPSYS_PINMUX_PAD_PA65_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA65_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA65_IE_Pos)
#define HPSYS_PINMUX_PAD_PA65_IE        HPSYS_PINMUX_PAD_PA65_IE_Msk
#define HPSYS_PINMUX_PAD_PA65_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA65_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA65_IS_Pos)
#define HPSYS_PINMUX_PAD_PA65_IS        HPSYS_PINMUX_PAD_PA65_IS_Msk
#define HPSYS_PINMUX_PAD_PA65_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA65_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA65_SR_Pos)
#define HPSYS_PINMUX_PAD_PA65_SR        HPSYS_PINMUX_PAD_PA65_SR_Msk
#define HPSYS_PINMUX_PAD_PA65_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA65_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA65_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA65_DS0       HPSYS_PINMUX_PAD_PA65_DS0_Msk
#define HPSYS_PINMUX_PAD_PA65_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA65_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA65_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA65_DS1       HPSYS_PINMUX_PAD_PA65_DS1_Msk
#define HPSYS_PINMUX_PAD_PA65_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA65_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA65_POE_Pos)
#define HPSYS_PINMUX_PAD_PA65_POE       HPSYS_PINMUX_PAD_PA65_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA66 register **************/
#define HPSYS_PINMUX_PAD_PA66_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA66_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA66_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA66_FSEL      HPSYS_PINMUX_PAD_PA66_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA66_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA66_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA66_PE_Pos)
#define HPSYS_PINMUX_PAD_PA66_PE        HPSYS_PINMUX_PAD_PA66_PE_Msk
#define HPSYS_PINMUX_PAD_PA66_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA66_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA66_PS_Pos)
#define HPSYS_PINMUX_PAD_PA66_PS        HPSYS_PINMUX_PAD_PA66_PS_Msk
#define HPSYS_PINMUX_PAD_PA66_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA66_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA66_IE_Pos)
#define HPSYS_PINMUX_PAD_PA66_IE        HPSYS_PINMUX_PAD_PA66_IE_Msk
#define HPSYS_PINMUX_PAD_PA66_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA66_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA66_IS_Pos)
#define HPSYS_PINMUX_PAD_PA66_IS        HPSYS_PINMUX_PAD_PA66_IS_Msk
#define HPSYS_PINMUX_PAD_PA66_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA66_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA66_SR_Pos)
#define HPSYS_PINMUX_PAD_PA66_SR        HPSYS_PINMUX_PAD_PA66_SR_Msk
#define HPSYS_PINMUX_PAD_PA66_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA66_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA66_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA66_DS0       HPSYS_PINMUX_PAD_PA66_DS0_Msk
#define HPSYS_PINMUX_PAD_PA66_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA66_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA66_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA66_DS1       HPSYS_PINMUX_PAD_PA66_DS1_Msk
#define HPSYS_PINMUX_PAD_PA66_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA66_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA66_POE_Pos)
#define HPSYS_PINMUX_PAD_PA66_POE       HPSYS_PINMUX_PAD_PA66_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA67 register **************/
#define HPSYS_PINMUX_PAD_PA67_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA67_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA67_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA67_FSEL      HPSYS_PINMUX_PAD_PA67_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA67_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA67_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA67_PE_Pos)
#define HPSYS_PINMUX_PAD_PA67_PE        HPSYS_PINMUX_PAD_PA67_PE_Msk
#define HPSYS_PINMUX_PAD_PA67_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA67_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA67_PS_Pos)
#define HPSYS_PINMUX_PAD_PA67_PS        HPSYS_PINMUX_PAD_PA67_PS_Msk
#define HPSYS_PINMUX_PAD_PA67_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA67_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA67_IE_Pos)
#define HPSYS_PINMUX_PAD_PA67_IE        HPSYS_PINMUX_PAD_PA67_IE_Msk
#define HPSYS_PINMUX_PAD_PA67_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA67_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA67_IS_Pos)
#define HPSYS_PINMUX_PAD_PA67_IS        HPSYS_PINMUX_PAD_PA67_IS_Msk
#define HPSYS_PINMUX_PAD_PA67_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA67_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA67_SR_Pos)
#define HPSYS_PINMUX_PAD_PA67_SR        HPSYS_PINMUX_PAD_PA67_SR_Msk
#define HPSYS_PINMUX_PAD_PA67_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA67_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA67_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA67_DS0       HPSYS_PINMUX_PAD_PA67_DS0_Msk
#define HPSYS_PINMUX_PAD_PA67_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA67_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA67_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA67_DS1       HPSYS_PINMUX_PAD_PA67_DS1_Msk
#define HPSYS_PINMUX_PAD_PA67_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA67_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA67_POE_Pos)
#define HPSYS_PINMUX_PAD_PA67_POE       HPSYS_PINMUX_PAD_PA67_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA68 register **************/
#define HPSYS_PINMUX_PAD_PA68_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA68_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA68_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA68_FSEL      HPSYS_PINMUX_PAD_PA68_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA68_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA68_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA68_PE_Pos)
#define HPSYS_PINMUX_PAD_PA68_PE        HPSYS_PINMUX_PAD_PA68_PE_Msk
#define HPSYS_PINMUX_PAD_PA68_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA68_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA68_PS_Pos)
#define HPSYS_PINMUX_PAD_PA68_PS        HPSYS_PINMUX_PAD_PA68_PS_Msk
#define HPSYS_PINMUX_PAD_PA68_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA68_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA68_IE_Pos)
#define HPSYS_PINMUX_PAD_PA68_IE        HPSYS_PINMUX_PAD_PA68_IE_Msk
#define HPSYS_PINMUX_PAD_PA68_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA68_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA68_IS_Pos)
#define HPSYS_PINMUX_PAD_PA68_IS        HPSYS_PINMUX_PAD_PA68_IS_Msk
#define HPSYS_PINMUX_PAD_PA68_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA68_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA68_SR_Pos)
#define HPSYS_PINMUX_PAD_PA68_SR        HPSYS_PINMUX_PAD_PA68_SR_Msk
#define HPSYS_PINMUX_PAD_PA68_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA68_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA68_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA68_DS0       HPSYS_PINMUX_PAD_PA68_DS0_Msk
#define HPSYS_PINMUX_PAD_PA68_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA68_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA68_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA68_DS1       HPSYS_PINMUX_PAD_PA68_DS1_Msk
#define HPSYS_PINMUX_PAD_PA68_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA68_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA68_POE_Pos)
#define HPSYS_PINMUX_PAD_PA68_POE       HPSYS_PINMUX_PAD_PA68_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA69 register **************/
#define HPSYS_PINMUX_PAD_PA69_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA69_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA69_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA69_FSEL      HPSYS_PINMUX_PAD_PA69_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA69_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA69_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA69_PE_Pos)
#define HPSYS_PINMUX_PAD_PA69_PE        HPSYS_PINMUX_PAD_PA69_PE_Msk
#define HPSYS_PINMUX_PAD_PA69_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA69_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA69_PS_Pos)
#define HPSYS_PINMUX_PAD_PA69_PS        HPSYS_PINMUX_PAD_PA69_PS_Msk
#define HPSYS_PINMUX_PAD_PA69_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA69_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA69_IE_Pos)
#define HPSYS_PINMUX_PAD_PA69_IE        HPSYS_PINMUX_PAD_PA69_IE_Msk
#define HPSYS_PINMUX_PAD_PA69_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA69_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA69_IS_Pos)
#define HPSYS_PINMUX_PAD_PA69_IS        HPSYS_PINMUX_PAD_PA69_IS_Msk
#define HPSYS_PINMUX_PAD_PA69_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA69_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA69_SR_Pos)
#define HPSYS_PINMUX_PAD_PA69_SR        HPSYS_PINMUX_PAD_PA69_SR_Msk
#define HPSYS_PINMUX_PAD_PA69_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA69_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA69_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA69_DS0       HPSYS_PINMUX_PAD_PA69_DS0_Msk
#define HPSYS_PINMUX_PAD_PA69_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA69_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA69_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA69_DS1       HPSYS_PINMUX_PAD_PA69_DS1_Msk
#define HPSYS_PINMUX_PAD_PA69_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA69_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA69_POE_Pos)
#define HPSYS_PINMUX_PAD_PA69_POE       HPSYS_PINMUX_PAD_PA69_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA70 register **************/
#define HPSYS_PINMUX_PAD_PA70_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA70_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA70_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA70_FSEL      HPSYS_PINMUX_PAD_PA70_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA70_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA70_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA70_PE_Pos)
#define HPSYS_PINMUX_PAD_PA70_PE        HPSYS_PINMUX_PAD_PA70_PE_Msk
#define HPSYS_PINMUX_PAD_PA70_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA70_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA70_PS_Pos)
#define HPSYS_PINMUX_PAD_PA70_PS        HPSYS_PINMUX_PAD_PA70_PS_Msk
#define HPSYS_PINMUX_PAD_PA70_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA70_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA70_IE_Pos)
#define HPSYS_PINMUX_PAD_PA70_IE        HPSYS_PINMUX_PAD_PA70_IE_Msk
#define HPSYS_PINMUX_PAD_PA70_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA70_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA70_IS_Pos)
#define HPSYS_PINMUX_PAD_PA70_IS        HPSYS_PINMUX_PAD_PA70_IS_Msk
#define HPSYS_PINMUX_PAD_PA70_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA70_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA70_SR_Pos)
#define HPSYS_PINMUX_PAD_PA70_SR        HPSYS_PINMUX_PAD_PA70_SR_Msk
#define HPSYS_PINMUX_PAD_PA70_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA70_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA70_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA70_DS0       HPSYS_PINMUX_PAD_PA70_DS0_Msk
#define HPSYS_PINMUX_PAD_PA70_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA70_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA70_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA70_DS1       HPSYS_PINMUX_PAD_PA70_DS1_Msk
#define HPSYS_PINMUX_PAD_PA70_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA70_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA70_POE_Pos)
#define HPSYS_PINMUX_PAD_PA70_POE       HPSYS_PINMUX_PAD_PA70_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA71 register **************/
#define HPSYS_PINMUX_PAD_PA71_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA71_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA71_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA71_FSEL      HPSYS_PINMUX_PAD_PA71_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA71_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA71_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA71_PE_Pos)
#define HPSYS_PINMUX_PAD_PA71_PE        HPSYS_PINMUX_PAD_PA71_PE_Msk
#define HPSYS_PINMUX_PAD_PA71_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA71_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA71_PS_Pos)
#define HPSYS_PINMUX_PAD_PA71_PS        HPSYS_PINMUX_PAD_PA71_PS_Msk
#define HPSYS_PINMUX_PAD_PA71_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA71_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA71_IE_Pos)
#define HPSYS_PINMUX_PAD_PA71_IE        HPSYS_PINMUX_PAD_PA71_IE_Msk
#define HPSYS_PINMUX_PAD_PA71_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA71_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA71_IS_Pos)
#define HPSYS_PINMUX_PAD_PA71_IS        HPSYS_PINMUX_PAD_PA71_IS_Msk
#define HPSYS_PINMUX_PAD_PA71_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA71_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA71_SR_Pos)
#define HPSYS_PINMUX_PAD_PA71_SR        HPSYS_PINMUX_PAD_PA71_SR_Msk
#define HPSYS_PINMUX_PAD_PA71_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA71_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA71_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA71_DS0       HPSYS_PINMUX_PAD_PA71_DS0_Msk
#define HPSYS_PINMUX_PAD_PA71_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA71_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA71_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA71_DS1       HPSYS_PINMUX_PAD_PA71_DS1_Msk
#define HPSYS_PINMUX_PAD_PA71_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA71_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA71_POE_Pos)
#define HPSYS_PINMUX_PAD_PA71_POE       HPSYS_PINMUX_PAD_PA71_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA72 register **************/
#define HPSYS_PINMUX_PAD_PA72_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA72_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA72_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA72_FSEL      HPSYS_PINMUX_PAD_PA72_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA72_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA72_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA72_PE_Pos)
#define HPSYS_PINMUX_PAD_PA72_PE        HPSYS_PINMUX_PAD_PA72_PE_Msk
#define HPSYS_PINMUX_PAD_PA72_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA72_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA72_PS_Pos)
#define HPSYS_PINMUX_PAD_PA72_PS        HPSYS_PINMUX_PAD_PA72_PS_Msk
#define HPSYS_PINMUX_PAD_PA72_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA72_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA72_IE_Pos)
#define HPSYS_PINMUX_PAD_PA72_IE        HPSYS_PINMUX_PAD_PA72_IE_Msk
#define HPSYS_PINMUX_PAD_PA72_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA72_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA72_IS_Pos)
#define HPSYS_PINMUX_PAD_PA72_IS        HPSYS_PINMUX_PAD_PA72_IS_Msk
#define HPSYS_PINMUX_PAD_PA72_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA72_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA72_SR_Pos)
#define HPSYS_PINMUX_PAD_PA72_SR        HPSYS_PINMUX_PAD_PA72_SR_Msk
#define HPSYS_PINMUX_PAD_PA72_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA72_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA72_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA72_DS0       HPSYS_PINMUX_PAD_PA72_DS0_Msk
#define HPSYS_PINMUX_PAD_PA72_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA72_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA72_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA72_DS1       HPSYS_PINMUX_PAD_PA72_DS1_Msk
#define HPSYS_PINMUX_PAD_PA72_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA72_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA72_POE_Pos)
#define HPSYS_PINMUX_PAD_PA72_POE       HPSYS_PINMUX_PAD_PA72_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA73 register **************/
#define HPSYS_PINMUX_PAD_PA73_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA73_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA73_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA73_FSEL      HPSYS_PINMUX_PAD_PA73_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA73_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA73_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA73_PE_Pos)
#define HPSYS_PINMUX_PAD_PA73_PE        HPSYS_PINMUX_PAD_PA73_PE_Msk
#define HPSYS_PINMUX_PAD_PA73_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA73_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA73_PS_Pos)
#define HPSYS_PINMUX_PAD_PA73_PS        HPSYS_PINMUX_PAD_PA73_PS_Msk
#define HPSYS_PINMUX_PAD_PA73_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA73_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA73_IE_Pos)
#define HPSYS_PINMUX_PAD_PA73_IE        HPSYS_PINMUX_PAD_PA73_IE_Msk
#define HPSYS_PINMUX_PAD_PA73_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA73_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA73_IS_Pos)
#define HPSYS_PINMUX_PAD_PA73_IS        HPSYS_PINMUX_PAD_PA73_IS_Msk
#define HPSYS_PINMUX_PAD_PA73_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA73_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA73_SR_Pos)
#define HPSYS_PINMUX_PAD_PA73_SR        HPSYS_PINMUX_PAD_PA73_SR_Msk
#define HPSYS_PINMUX_PAD_PA73_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA73_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA73_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA73_DS0       HPSYS_PINMUX_PAD_PA73_DS0_Msk
#define HPSYS_PINMUX_PAD_PA73_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA73_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA73_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA73_DS1       HPSYS_PINMUX_PAD_PA73_DS1_Msk
#define HPSYS_PINMUX_PAD_PA73_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA73_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA73_POE_Pos)
#define HPSYS_PINMUX_PAD_PA73_POE       HPSYS_PINMUX_PAD_PA73_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA74 register **************/
#define HPSYS_PINMUX_PAD_PA74_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA74_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA74_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA74_FSEL      HPSYS_PINMUX_PAD_PA74_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA74_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA74_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA74_PE_Pos)
#define HPSYS_PINMUX_PAD_PA74_PE        HPSYS_PINMUX_PAD_PA74_PE_Msk
#define HPSYS_PINMUX_PAD_PA74_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA74_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA74_PS_Pos)
#define HPSYS_PINMUX_PAD_PA74_PS        HPSYS_PINMUX_PAD_PA74_PS_Msk
#define HPSYS_PINMUX_PAD_PA74_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA74_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA74_IE_Pos)
#define HPSYS_PINMUX_PAD_PA74_IE        HPSYS_PINMUX_PAD_PA74_IE_Msk
#define HPSYS_PINMUX_PAD_PA74_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA74_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA74_IS_Pos)
#define HPSYS_PINMUX_PAD_PA74_IS        HPSYS_PINMUX_PAD_PA74_IS_Msk
#define HPSYS_PINMUX_PAD_PA74_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA74_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA74_SR_Pos)
#define HPSYS_PINMUX_PAD_PA74_SR        HPSYS_PINMUX_PAD_PA74_SR_Msk
#define HPSYS_PINMUX_PAD_PA74_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA74_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA74_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA74_DS0       HPSYS_PINMUX_PAD_PA74_DS0_Msk
#define HPSYS_PINMUX_PAD_PA74_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA74_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA74_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA74_DS1       HPSYS_PINMUX_PAD_PA74_DS1_Msk
#define HPSYS_PINMUX_PAD_PA74_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA74_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA74_POE_Pos)
#define HPSYS_PINMUX_PAD_PA74_POE       HPSYS_PINMUX_PAD_PA74_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA75 register **************/
#define HPSYS_PINMUX_PAD_PA75_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA75_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA75_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA75_FSEL      HPSYS_PINMUX_PAD_PA75_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA75_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA75_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA75_PE_Pos)
#define HPSYS_PINMUX_PAD_PA75_PE        HPSYS_PINMUX_PAD_PA75_PE_Msk
#define HPSYS_PINMUX_PAD_PA75_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA75_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA75_PS_Pos)
#define HPSYS_PINMUX_PAD_PA75_PS        HPSYS_PINMUX_PAD_PA75_PS_Msk
#define HPSYS_PINMUX_PAD_PA75_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA75_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA75_IE_Pos)
#define HPSYS_PINMUX_PAD_PA75_IE        HPSYS_PINMUX_PAD_PA75_IE_Msk
#define HPSYS_PINMUX_PAD_PA75_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA75_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA75_IS_Pos)
#define HPSYS_PINMUX_PAD_PA75_IS        HPSYS_PINMUX_PAD_PA75_IS_Msk
#define HPSYS_PINMUX_PAD_PA75_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA75_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA75_SR_Pos)
#define HPSYS_PINMUX_PAD_PA75_SR        HPSYS_PINMUX_PAD_PA75_SR_Msk
#define HPSYS_PINMUX_PAD_PA75_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA75_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA75_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA75_DS0       HPSYS_PINMUX_PAD_PA75_DS0_Msk
#define HPSYS_PINMUX_PAD_PA75_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA75_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA75_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA75_DS1       HPSYS_PINMUX_PAD_PA75_DS1_Msk
#define HPSYS_PINMUX_PAD_PA75_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA75_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA75_POE_Pos)
#define HPSYS_PINMUX_PAD_PA75_POE       HPSYS_PINMUX_PAD_PA75_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA76 register **************/
#define HPSYS_PINMUX_PAD_PA76_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA76_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA76_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA76_FSEL      HPSYS_PINMUX_PAD_PA76_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA76_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA76_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA76_PE_Pos)
#define HPSYS_PINMUX_PAD_PA76_PE        HPSYS_PINMUX_PAD_PA76_PE_Msk
#define HPSYS_PINMUX_PAD_PA76_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA76_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA76_PS_Pos)
#define HPSYS_PINMUX_PAD_PA76_PS        HPSYS_PINMUX_PAD_PA76_PS_Msk
#define HPSYS_PINMUX_PAD_PA76_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA76_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA76_IE_Pos)
#define HPSYS_PINMUX_PAD_PA76_IE        HPSYS_PINMUX_PAD_PA76_IE_Msk
#define HPSYS_PINMUX_PAD_PA76_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA76_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA76_IS_Pos)
#define HPSYS_PINMUX_PAD_PA76_IS        HPSYS_PINMUX_PAD_PA76_IS_Msk
#define HPSYS_PINMUX_PAD_PA76_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA76_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA76_SR_Pos)
#define HPSYS_PINMUX_PAD_PA76_SR        HPSYS_PINMUX_PAD_PA76_SR_Msk
#define HPSYS_PINMUX_PAD_PA76_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA76_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA76_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA76_DS0       HPSYS_PINMUX_PAD_PA76_DS0_Msk
#define HPSYS_PINMUX_PAD_PA76_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA76_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA76_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA76_DS1       HPSYS_PINMUX_PAD_PA76_DS1_Msk
#define HPSYS_PINMUX_PAD_PA76_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA76_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA76_POE_Pos)
#define HPSYS_PINMUX_PAD_PA76_POE       HPSYS_PINMUX_PAD_PA76_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA77 register **************/
#define HPSYS_PINMUX_PAD_PA77_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA77_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA77_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA77_FSEL      HPSYS_PINMUX_PAD_PA77_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA77_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA77_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA77_PE_Pos)
#define HPSYS_PINMUX_PAD_PA77_PE        HPSYS_PINMUX_PAD_PA77_PE_Msk
#define HPSYS_PINMUX_PAD_PA77_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA77_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA77_PS_Pos)
#define HPSYS_PINMUX_PAD_PA77_PS        HPSYS_PINMUX_PAD_PA77_PS_Msk
#define HPSYS_PINMUX_PAD_PA77_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA77_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA77_IE_Pos)
#define HPSYS_PINMUX_PAD_PA77_IE        HPSYS_PINMUX_PAD_PA77_IE_Msk
#define HPSYS_PINMUX_PAD_PA77_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA77_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA77_IS_Pos)
#define HPSYS_PINMUX_PAD_PA77_IS        HPSYS_PINMUX_PAD_PA77_IS_Msk
#define HPSYS_PINMUX_PAD_PA77_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA77_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA77_SR_Pos)
#define HPSYS_PINMUX_PAD_PA77_SR        HPSYS_PINMUX_PAD_PA77_SR_Msk
#define HPSYS_PINMUX_PAD_PA77_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA77_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA77_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA77_DS0       HPSYS_PINMUX_PAD_PA77_DS0_Msk
#define HPSYS_PINMUX_PAD_PA77_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA77_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA77_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA77_DS1       HPSYS_PINMUX_PAD_PA77_DS1_Msk
#define HPSYS_PINMUX_PAD_PA77_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA77_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA77_POE_Pos)
#define HPSYS_PINMUX_PAD_PA77_POE       HPSYS_PINMUX_PAD_PA77_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA78 register **************/
#define HPSYS_PINMUX_PAD_PA78_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA78_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA78_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA78_FSEL      HPSYS_PINMUX_PAD_PA78_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA78_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA78_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA78_PE_Pos)
#define HPSYS_PINMUX_PAD_PA78_PE        HPSYS_PINMUX_PAD_PA78_PE_Msk
#define HPSYS_PINMUX_PAD_PA78_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA78_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA78_PS_Pos)
#define HPSYS_PINMUX_PAD_PA78_PS        HPSYS_PINMUX_PAD_PA78_PS_Msk
#define HPSYS_PINMUX_PAD_PA78_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA78_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA78_IE_Pos)
#define HPSYS_PINMUX_PAD_PA78_IE        HPSYS_PINMUX_PAD_PA78_IE_Msk
#define HPSYS_PINMUX_PAD_PA78_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA78_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA78_IS_Pos)
#define HPSYS_PINMUX_PAD_PA78_IS        HPSYS_PINMUX_PAD_PA78_IS_Msk
#define HPSYS_PINMUX_PAD_PA78_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA78_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA78_SR_Pos)
#define HPSYS_PINMUX_PAD_PA78_SR        HPSYS_PINMUX_PAD_PA78_SR_Msk
#define HPSYS_PINMUX_PAD_PA78_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA78_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA78_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA78_DS0       HPSYS_PINMUX_PAD_PA78_DS0_Msk
#define HPSYS_PINMUX_PAD_PA78_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA78_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA78_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA78_DS1       HPSYS_PINMUX_PAD_PA78_DS1_Msk
#define HPSYS_PINMUX_PAD_PA78_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA78_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA78_POE_Pos)
#define HPSYS_PINMUX_PAD_PA78_POE       HPSYS_PINMUX_PAD_PA78_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA79 register **************/
#define HPSYS_PINMUX_PAD_PA79_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA79_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA79_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA79_FSEL      HPSYS_PINMUX_PAD_PA79_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA79_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA79_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA79_PE_Pos)
#define HPSYS_PINMUX_PAD_PA79_PE        HPSYS_PINMUX_PAD_PA79_PE_Msk
#define HPSYS_PINMUX_PAD_PA79_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA79_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA79_PS_Pos)
#define HPSYS_PINMUX_PAD_PA79_PS        HPSYS_PINMUX_PAD_PA79_PS_Msk
#define HPSYS_PINMUX_PAD_PA79_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA79_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA79_IE_Pos)
#define HPSYS_PINMUX_PAD_PA79_IE        HPSYS_PINMUX_PAD_PA79_IE_Msk
#define HPSYS_PINMUX_PAD_PA79_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA79_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA79_IS_Pos)
#define HPSYS_PINMUX_PAD_PA79_IS        HPSYS_PINMUX_PAD_PA79_IS_Msk
#define HPSYS_PINMUX_PAD_PA79_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA79_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA79_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA79_MODE      HPSYS_PINMUX_PAD_PA79_MODE_Msk
#define HPSYS_PINMUX_PAD_PA79_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA79_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA79_DS_Pos)
#define HPSYS_PINMUX_PAD_PA79_DS        HPSYS_PINMUX_PAD_PA79_DS_Msk
#define HPSYS_PINMUX_PAD_PA79_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA79_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA79_POE_Pos)
#define HPSYS_PINMUX_PAD_PA79_POE       HPSYS_PINMUX_PAD_PA79_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA80 register **************/
#define HPSYS_PINMUX_PAD_PA80_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA80_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA80_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA80_FSEL      HPSYS_PINMUX_PAD_PA80_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA80_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA80_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA80_PE_Pos)
#define HPSYS_PINMUX_PAD_PA80_PE        HPSYS_PINMUX_PAD_PA80_PE_Msk
#define HPSYS_PINMUX_PAD_PA80_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA80_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA80_PS_Pos)
#define HPSYS_PINMUX_PAD_PA80_PS        HPSYS_PINMUX_PAD_PA80_PS_Msk
#define HPSYS_PINMUX_PAD_PA80_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA80_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA80_IE_Pos)
#define HPSYS_PINMUX_PAD_PA80_IE        HPSYS_PINMUX_PAD_PA80_IE_Msk
#define HPSYS_PINMUX_PAD_PA80_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA80_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA80_IS_Pos)
#define HPSYS_PINMUX_PAD_PA80_IS        HPSYS_PINMUX_PAD_PA80_IS_Msk
#define HPSYS_PINMUX_PAD_PA80_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA80_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA80_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA80_MODE      HPSYS_PINMUX_PAD_PA80_MODE_Msk
#define HPSYS_PINMUX_PAD_PA80_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA80_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA80_DS_Pos)
#define HPSYS_PINMUX_PAD_PA80_DS        HPSYS_PINMUX_PAD_PA80_DS_Msk
#define HPSYS_PINMUX_PAD_PA80_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA80_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA80_POE_Pos)
#define HPSYS_PINMUX_PAD_PA80_POE       HPSYS_PINMUX_PAD_PA80_POE_Msk

/**************** Bit definition for HPSYS_PINMUX_CR register *****************/
#define HPSYS_PINMUX_CR_CTRL_Pos        (0U)
#define HPSYS_PINMUX_CR_CTRL_Msk        (0xFFUL << HPSYS_PINMUX_CR_CTRL_Pos)
#define HPSYS_PINMUX_CR_CTRL            HPSYS_PINMUX_CR_CTRL_Msk

#endif
/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
