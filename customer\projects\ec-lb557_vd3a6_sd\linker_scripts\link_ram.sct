#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc
#include "../rtconfig.h"
#include "../../../../drivers/cmsis/sf32lb55x/mem_map.h"


; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 HCPU_CODE_START_ADDR HCPU_CODE_SIZE  {    ; load region size_region
  ER_IROM1 HCPU_CODE_START_ADDR HCPU_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   *.o (.rodata.*)
  }
  ER_IROM1_EX HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
   ;drv_spi_flash.o (.text.*)
   ;drv_spi_flash.o (.rodata.*)
   bf0_hal_qspi.o (.text.*)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)
   ;bf0_hal_dma.o   (.text.HAL_DMA_PollForTransfer)
   ;drv_common.o    (.text.HAL_GetTick)
   ;clock.o         (.text.rt_tick_get)
   bf0_hal_rcc.o   (.text.*)
   tc_hal_epic_filling.o (+RO)
   tc_hal_epic_rotation.o (+RO)
   tc_hal_epic_scaling.o (+RO)
   tc_hal_epic_blending.o (+RO)
   tc_hal_epic.o (.text.*)
   bf0_hal_epic.o (+RO)   
  }  
  RW_IRAM1 HCPU_RAM_DATA_START_ADDR HCPU_RAM_DATA_SIZE-0x100  {  ; RW data  
   *.o (Jlink_RTT, +First)
   .ANY (+RW +ZI)
  }
  RW_IRAM2 HCPU_RAM_DATA_START_ADDR+HCPU_RAM_DATA_SIZE-0x100 UNINIT 0x100  {  ; RW data  
  test_ctrl.o(UNINITZI)
  }
}

