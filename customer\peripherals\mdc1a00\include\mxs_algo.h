/*
 * Copyright (c) 2021 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#ifndef _MXS_ALGO_H_
#define _MXS_ALGO_H_

#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

// motion状态信息
typedef enum mxs_algo_motion_event
{
    MXS_ALGO_MOTION_NONE = 0,
    MXS_ALGO_MOTION_PRESS,
    MXS_ALGO_MOTION_RELEASE,
    MXS_ALGO_MOTION_ROTATE
} mxs_algo_motion_event_t;

/* 初始化*/
int mxs_algo_config(int cpi_x, int cpi_y);

/* 获取事件
   只有当event !=  MXS_MOTION_ROTATE 时， displacement = 0
*/
int mxs_algo_get_event(int16_t dx,
                       int16_t dy,
                       uint32_t timestamp,
                       mxs_algo_motion_event_t* event,
                       int* displacement);

#endif  // _MXS_ALGO_H_
