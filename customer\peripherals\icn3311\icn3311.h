/**
  ******************************************************************************
  * @file   icn3311.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the icn3311.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __ICN3311_H
#define __ICN3311_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ICN3311
  * @{
  */

/** @defgroup ICN3311_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ICN3311_Exported_Constants
  * @{
  */

/**
  * @brief ICN3311 chip IDs
  */
#define ICN3311_ID                  0x1133

/**
  * @brief  ICN3311 Size
  */
#define  ICN3311_LCD_PIXEL_WIDTH    (454)
#define  ICN3311_LCD_PIXEL_HEIGHT   (454)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ICN3311_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define ICN3311_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define ICN3311_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ICN3311 Registers
  */
#define ICN3311_SW_RESET           0x01
#define ICN3311_LCD_ID             0x04
#define ICN3311_DSI_ERR            0x05
#define ICN3311_POWER_MODE         0x0A
#define ICN3311_SLEEP_IN           0x10
#define ICN3311_SLEEP_OUT          0x11
#define ICN3311_PARTIAL_DISPLAY    0x12
#define ICN3311_DISPLAY_INVERSION  0x21
#define ICN3311_DISPLAY_OFF        0x28
#define ICN3311_DISPLAY_ON         0x29
#define ICN3311_WRITE_RAM          0x2C
#define ICN3311_READ_RAM           0x2E
#define ICN3311_CASET              0x2A
#define ICN3311_RASET              0x2B
#define ICN3311_PART_CASET              0x30
#define ICN3311_PART_RASET              0x31
#define ICN3311_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define ICN3311_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define ICN3311_TEARING_EFFECT     0x35
#define ICN3311_NORMAL_DISPLAY     0x36
#define ICN3311_IDLE_MODE_OFF      0x38
#define ICN3311_IDLE_MODE_ON       0x39
#define ICN3311_COLOR_MODE         0x3A
#define ICN3311_CONTINUE_WRITE_RAM 0x3C
#define ICN3311_WBRIGHT            0x51 /* Write brightness*/
#define ICN3311_RBRIGHT            0x53 /* Read brightness*/
#define ICN3311_PORCH_CTRL         0xB2
#define ICN3311_FRAME_CTRL         0xB3
#define ICN3311_GATE_CTRL          0xB7
#define ICN3311_VCOM_SET           0xBB
#define ICN3311_LCM_CTRL           0xC0
#define ICN3311_SET_TIME_SRC       0xC2
#define ICN3311_SET_DISP_MODE      0xC4
#define ICN3311_VCOMH_OFFSET_SET   0xC5
#define ICN3311_FR_CTRL            0xC6
#define ICN3311_POWER_CTRL         0xD0
#define ICN3311_PV_GAMMA_CTRL      0xE0
#define ICN3311_NV_GAMMA_CTRL      0xE1
#define ICN3311_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup ICN3311_Exported_Functions
  * @{
  */
void     ICN3311_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ICN3311_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ICN3311_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ICN3311_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ICN3311_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ICN3311_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ICN3311_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ICN3311_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ICN3311_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ICN3311_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __ICN3311_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
