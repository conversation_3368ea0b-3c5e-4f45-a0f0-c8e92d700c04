#<SYMDEFS># ARM Linker, 6140101: Last Updated: Wed Jul 21 20:29:46 2021
0x0000225d T HAL_CRC_Accumulate
0x00002325 T HAL_CRC_Calculate
0x00002339 T HAL_CRC_DeInit
0x00002355 T HAL_CRC_GetState
0x00002359 T HAL_CRC_Init
0x0000239d T HAL_CRC_MspDeInit
0x0000239f T HAL_CRC_MspInit
0x000023a1 T HAL_CRC_Setmode
0x000023c5 T HAL_CRC_Setmode_Customized
0x000023e9 T HAL_DBG_i2c_pins
0x0000246d T HAL_DBG_i2c_print
0x00002485 T HAL_DBG_i2c_print_byte
0x00002509 T HAL_DBG_print_data
0x000025bd T HAL_DBG_printf
0x000025bf T HAL_DMA_Abort
0x000025f9 T HAL_DMA_Abort_IT
0x00002649 T HAL_DMA_DeInit
0x000026e5 T HAL_DMA_GetError
0x000026e9 T HAL_DMA_GetState
0x000026ef T HAL_DMA_IRQHandler
0x00002785 T HAL_DMA_Init
0x0000284d T HAL_DMA_PollForTransfer
0x00002915 T HAL_DMA_RegisterCallback
0x0000295b T HAL_DMA_Select_Source
0x00002999 T HAL_DMA_Start
0x000029e5 T HAL_DMA_Start_IT
0x00002a4d T HAL_DMA_UnRegisterCallback
0x00002b09 T HAL_Delay_us2_
0x00002b45 T HAL_Delay_us_
0x00002b95 T HAL_FLASH_AES_CFG
0x00002bb3 T HAL_FLASH_ALIAS_CFG
0x00002bcd T HAL_FLASH_BOOT
0x00002ca3 T HAL_FLASH_CFG_AHB_RCMD
0x00002cd9 T HAL_FLASH_CFG_AHB_WCMD
0x00002d0f T HAL_FLASH_CLEAR_FIFO
0x00002d3f T HAL_FLASH_CLR_CMD_DONE
0x00002d4d T HAL_FLASH_CLR_PROTECT
0x00002ea9 T HAL_FLASH_CLR_STATUS
0x00002eb5 T HAL_FLASH_CONFIG_AHB_READ
0x00002f43 T HAL_FLASH_CONFIG_FULL_AHB_READ
0x00002fc5 T HAL_FLASH_DETECT_DUAL
0x00003001 T HAL_FLASH_DETECT_SINGLE
0x00003045 T HAL_FLASH_DMA_START
0x000030c5 T HAL_FLASH_DMA_WAIT_DONE
0x000030e7 T HAL_FLASH_DeInit
0x000030f3 T HAL_FLASH_ENABLE_AES
0x0000310f T HAL_FLASH_ENABLE_CMD2
0x0000312b T HAL_FLASH_ENABLE_QSPI
0x00003147 T HAL_FLASH_FADDR_SET_QSPI
0x0000315d T HAL_FLASH_GET_DIV
0x0000316b T HAL_FLASH_GET_DUAL_MODE
0x0000317b T HAL_FLASH_GET_NOR_ID
0x000031d5 T HAL_FLASH_GET_WDT_STATUS
0x000031e5 T HAL_FLASH_ID_DUAL_ID
0x00003241 T HAL_FLASH_ISSUE_CMD
0x000032ab T HAL_FLASH_ISSUE_CMD_SEQ
0x00003385 T HAL_FLASH_IS_BUSY
0x00003393 T HAL_FLASH_IS_CMD_DONE
0x000033a3 T HAL_FLASH_IS_PROG_DONE
0x000033b1 T HAL_FLASH_IS_RX_EMPTY
0x000033c1 T HAL_FLASH_IS_TX_EMPTY
0x000033d1 T HAL_FLASH_IS_TX_FULL
0x000033e1 T HAL_FLASH_Init
0x00003569 T HAL_FLASH_MANUAL_CMD
0x000035a9 T HAL_FLASH_MANUAL_CMD2
0x000035e9 T HAL_FLASH_NONCE_CFG
0x0000360b T HAL_FLASH_PRE_CMD
0x00003659 T HAL_FLASH_READ32
0x00003665 T HAL_FLASH_SET_AES
0x00003681 T HAL_FLASH_SET_AHB_RCMD
0x00003697 T HAL_FLASH_SET_AHB_WCMD
0x000036af T HAL_FLASH_SET_ALIAS_OFFSET
0x000036c3 T HAL_FLASH_SET_ALIAS_RANGE
0x000036e5 T HAL_FLASH_SET_CLK_rom
0x000036ed T HAL_FLASH_SET_CMD
0x0000370b T HAL_FLASH_SET_CS_TIME
0x0000372d T HAL_FLASH_SET_CTR
0x0000374b T HAL_FLASH_SET_DUAL_MODE
0x00003767 T HAL_FLASH_SET_NONCE
0x00003781 T HAL_FLASH_SET_QUAL_SPI
0x00003797 T HAL_FLASH_SET_ROW_BOUNDARY
0x000037ab T HAL_FLASH_SET_TXSLOT
0x000037c1 T HAL_FLASH_SET_WDT
0x000037d5 T HAL_FLASH_STATUS_MATCH
0x000037e5 T HAL_FLASH_WRITE_DLEN
0x000037f9 T HAL_FLASH_WRITE_DLEN2
0x0000380d T HAL_FLASH_WRITE_WORD
0x0000381b T HAL_GPIO_DeInit_rom
0x00003841 T HAL_GPIO_EXTI_Callback_rom
0x00003871 T HAL_GPIO_GetMode_rom
0x000038f1 T HAL_GPIO_Init_rom
0x000039e9 T HAL_GPIO_ReadPin_rom
0x00003a15 T HAL_GPIO_TogglePin_rom
0x00003a33 T HAL_GPIO_WritePin_rom
0x00003a5b T HAL_GPT_Base_DeInit
0x00003a97 T HAL_GPT_Base_GetState
0x00003a9d T HAL_GPT_Base_Init
0x00003ae1 T HAL_GPT_Base_MspDeInit
0x00003ae3 T HAL_GPT_Base_MspInit
0x00003ae5 T HAL_GPT_Base_Start
0x00003b01 T HAL_GPT_Base_Start_DMA
0x00003b5d T HAL_GPT_Base_Start_IT
0x00003b73 T HAL_GPT_Base_Stop
0x00003ba1 T HAL_GPT_Base_Stop_DMA
0x00003bd1 T HAL_GPT_Base_Stop_IT
0x00003bfd T HAL_GPT_ConfigClockSource
0x00003d21 T HAL_GPT_ConfigOCrefClear
0x00003dc7 T HAL_GPT_ConfigTI1Input
0x00003dd7 T HAL_GPT_DMABurst_ReadStart
0x00003ddb T HAL_GPT_DMABurst_ReadStop
0x00003e45 T HAL_GPT_DMABurst_WriteStart
0x00003e49 T HAL_GPT_DMABurst_WriteStop
0x00003eb3 T HAL_GPT_Encoder_DeInit
0x00003eef T HAL_GPT_Encoder_GetState
0x00003ef5 T HAL_GPT_Encoder_Init
0x00003f85 T HAL_GPT_Encoder_MspDeInit
0x00003f87 T HAL_GPT_Encoder_MspInit
0x00003f89 T HAL_GPT_Encoder_Start
0x00003fb9 T HAL_GPT_Encoder_Start_DMA
0x000040e5 T HAL_GPT_Encoder_Start_IT
0x00004141 T HAL_GPT_Encoder_Stop
0x00004185 T HAL_GPT_Encoder_Stop_DMA
0x000041fb T HAL_GPT_Encoder_Stop_IT
0x00004271 T HAL_GPT_ErrorCallback
0x00004273 T HAL_GPT_GenerateEvent
0x00004295 T HAL_GPT_IC_CaptureCallback
0x00004297 T HAL_GPT_IC_ConfigChannel
0x00004371 T HAL_GPT_IC_DeInit
0x000043ad T HAL_GPT_IC_GetState
0x000043b3 T HAL_GPT_IC_Init
0x000043e7 T HAL_GPT_IC_MspDeInit
0x000043e9 T HAL_GPT_IC_MspInit
0x000043eb T HAL_GPT_IC_Start
0x00004405 T HAL_GPT_IC_Start_DMA
0x000044e1 T HAL_GPT_IC_Start_IT
0x00004535 T HAL_GPT_IC_Stop
0x00004565 T HAL_GPT_IC_Stop_DMA
0x000045d5 T HAL_GPT_IC_Stop_IT
0x000047a9 T HAL_GPT_OC_ConfigChannel
0x000047fb T HAL_GPT_OC_DeInit
0x00004837 T HAL_GPT_OC_DelayElapsedCallback
0x00004839 T HAL_GPT_OC_GetState
0x0000483f T HAL_GPT_OC_Init
0x00004873 T HAL_GPT_OC_MspDeInit
0x00004875 T HAL_GPT_OC_MspInit
0x00004877 T HAL_GPT_OC_Start
0x00004891 T HAL_GPT_OC_Start_DMA
0x00004985 T HAL_GPT_OC_Start_IT
0x000049d9 T HAL_GPT_OC_Stop
0x00004a09 T HAL_GPT_OC_Stop_DMA
0x00004a79 T HAL_GPT_OC_Stop_IT
0x00004ae1 T HAL_GPT_OnePulse_ConfigChannel
0x00004b8d T HAL_GPT_OnePulse_DeInit
0x00004bc9 T HAL_GPT_OnePulse_GetState
0x00004bcf T HAL_GPT_OnePulse_Init
0x00004c15 T HAL_GPT_OnePulse_MspDeInit
0x00004c17 T HAL_GPT_OnePulse_MspInit
0x00004c19 T HAL_GPT_OnePulse_Start
0x00004c35 T HAL_GPT_OnePulse_Start_IT
0x00004c61 T HAL_GPT_OnePulse_Stop
0x00004c9b T HAL_GPT_OnePulse_Stop_IT
0x00004ce5 T HAL_GPT_PWM_ConfigChannel
0x00004d59 T HAL_GPT_PWM_DeInit
0x00004d95 T HAL_GPT_PWM_GetState
0x00004d9b T HAL_GPT_PWM_Init
0x00004dcf T HAL_GPT_PWM_MspDeInit
0x00004dd1 T HAL_GPT_PWM_MspInit
0x00004dd3 T HAL_GPT_PWM_PulseFinishedCallback
0x00004dd5 T HAL_GPT_PWM_Start
0x00004df1 T HAL_GPT_PWM_Start_DMA
0x00004ee5 T HAL_GPT_PWM_Start_IT
0x00004f39 T HAL_GPT_PWM_Stop
0x00004f6d T HAL_GPT_PWM_Stop_DMA
0x00004fdd T HAL_GPT_PWM_Stop_IT
0x00005049 T HAL_GPT_ReadCapturedValue
0x00005091 T HAL_GPT_SlaveConfigSynchronization
0x000050cd T HAL_GPT_SlaveConfigSynchronization_IT
0x00005109 T HAL_GPT_TriggerCallback
0x0000511d T HAL_HPAON_Deactivate
0x00005131 T HAL_HPAON_DisableRC
0x0000513d T HAL_HPAON_DisableWakeupSrc
0x00005155 T HAL_HPAON_DisableXT48
0x00005161 T HAL_HPAON_EnableRC
0x00005175 T HAL_HPAON_EnableWakeupSrc
0x000051a9 T HAL_HPAON_EnableXT48
0x000051c1 T HAL_HPAON_EnterDeepSleep
0x000051cd T HAL_HPAON_EnterLightSleep
0x000051d9 T HAL_HPAON_EnterStandby
0x000051e9 T HAL_HPAON_QueryWakeupGpioPin
0x00005205 T HAL_HPAON_QueryWakeupPin
0x00005231 T HAL_HPAON_StartGTimer
0x00005255 T HAL_HPAON_StopGTimer
0x00005281 T HAL_HPAON_WakeCore
0x000052a1 T HAL_HalfDuplex_EnableReceiver
0x000052d5 T HAL_HalfDuplex_EnableTransmitter
0x00005309 T HAL_HalfDuplex_Init
0x0000537b T HAL_I2C_AbortCpltCallback
0x0000537d T HAL_I2C_AddrCallback
0x0000537f T HAL_I2C_DMA_Init
0x000053c1 T HAL_I2C_DeInit
0x000053f5 T HAL_I2C_ErrorCallback
0x000053f7 T HAL_I2C_GetError
0x000053fb T HAL_I2C_GetMode
0x00005401 T HAL_I2C_GetState
0x0000541d T HAL_I2C_Init
0x000054f1 T HAL_I2C_ListenCpltCallback
0x000054f3 T HAL_I2C_MasterRxCpltCallback
0x000054f5 T HAL_I2C_MasterTxCpltCallback
0x000054f7 T HAL_I2C_Master_Receive
0x000055ed T HAL_I2C_Master_Receive_DMA
0x000056dd T HAL_I2C_Master_Receive_IT
0x00005785 T HAL_I2C_Master_Transmit
0x00005879 T HAL_I2C_Master_Transmit_DMA
0x00005979 T HAL_I2C_Master_Transmit_IT
0x00005a41 T HAL_I2C_MemRxCpltCallback
0x00005a43 T HAL_I2C_MemTxCpltCallback
0x00005a45 T HAL_I2C_Mem_Read
0x00005b71 T HAL_I2C_Mem_Read_DMA
0x00005c99 T HAL_I2C_Mem_Read_IT
0x00005d9d T HAL_I2C_Mem_Write
0x00005eb1 T HAL_I2C_Mem_Write_DMA
0x00005fd9 T HAL_I2C_Mem_Write_IT
0x000060c5 T HAL_I2C_MspDeInit
0x000060c7 T HAL_I2C_MspInit
0x000060c9 T HAL_I2S_Config_Receive
0x0000612b T HAL_I2S_Config_Transmit
0x0000619d T HAL_I2S_DMAPause
0x000061fd T HAL_I2S_DMAResume
0x00006275 T HAL_I2S_DMAStop
0x00006309 T HAL_I2S_DeInit
0x0000635d T HAL_I2S_ErrorCallback
0x0000635f T HAL_I2S_GetError
0x00006363 T HAL_I2S_GetState
0x00006369 T HAL_I2S_IRQHandler
0x000063f1 T HAL_I2S_Init
0x000064ab T HAL_I2S_MspDeInit
0x000064ad T HAL_I2S_MspInit
0x000064af T HAL_I2S_RX_DMAPause
0x000064ed T HAL_I2S_RX_DMAResume
0x00006537 T HAL_I2S_RX_DMAStop
0x0000659b T HAL_I2S_Receive
0x00006665 T HAL_I2S_Receive_DMA
0x00006731 T HAL_I2S_Receive_IT
0x0000679b T HAL_I2S_RxCpltCallback
0x0000679d T HAL_I2S_RxHalfCpltCallback
0x0000679f T HAL_I2S_TX_DMAPause
0x000067dd T HAL_I2S_TX_DMAResume
0x00006827 T HAL_I2S_TX_DMAStop
0x0000688b T HAL_I2S_Transmit
0x00006945 T HAL_I2S_Transmit_DMA
0x000069f9 T HAL_I2S_Transmit_IT
0x00006a61 T HAL_I2S_TxCpltCallback
0x00006a63 T HAL_I2S_TxHalfCpltCallback
0x00006a97 T HAL_LCDC_Enable_TE
0x00006ab1 T HAL_LCDC_IRQHandler
0x00006b39 T HAL_LCDC_Init
0x00006d65 T HAL_LCDC_Invert_TE_Pol
0x00006d81 T HAL_LCDC_LayerGetCmpr
0x00006d91 T HAL_LCDC_LayerSetCmpr
0x00006dad T HAL_LCDC_LayerSetData
0x00006dcb T HAL_LCDC_LayerSetFormat
0x00006dd3 T HAL_LCDC_ReadDatas
0x00006e7f T HAL_LCDC_ResetLCD
0x00006ecd T HAL_LCDC_SPI_Sequence
0x00006eeb T HAL_LCDC_SendLayerData2Reg_IT
0x00006f15 T HAL_LCDC_SendLayerDataCpltCbk
0x00006f17 T HAL_LCDC_SendLayerData_IT
0x00006f1b T HAL_LCDC_SetFreq
0x00006f31 T HAL_LCDC_SetOutFormat
0x00006f47 T HAL_LCDC_SetROI
0x00006f65 T HAL_LCDC_SetROIArea
0x00006f83 T HAL_LCDC_SetSPICfg
0x00006fa1 T HAL_LCDC_WriteDatas
0x00007031 T HAL_LIN_Init
0x000070c1 T HAL_LIN_SendBreak
0x000070ed T HAL_LPAON_ConfigStartAddr
0x000070fd T HAL_LPAON_Deactivate
0x00007109 T HAL_LPAON_DisableRC
0x00007115 T HAL_LPAON_DisableWakeupSrc
0x0000712d T HAL_LPAON_DisableXT48
0x00007139 T HAL_LPAON_EnableRC
0x0000714d T HAL_LPAON_EnableWakeupSrc_rom
0x00007185 T HAL_LPAON_EnableXT48
0x0000719d T HAL_LPAON_EnterDeepSleep
0x000071a9 T HAL_LPAON_EnterLightSleep
0x000071b5 T HAL_LPAON_EnterStandby
0x000071c5 T HAL_LPAON_QueryWakeupGpioPin
0x000071e1 T HAL_LPAON_QueryWakeupPin
0x0000720d T HAL_LPAON_WakeCore
0x0000722d T HAL_LPTIM_AutoReloadMatchCallback
0x00007231 T HAL_LPTIM_CompareMatchCallback
0x00007233 T HAL_LPTIM_CompareWriteCallback
0x00007235 T HAL_LPTIM_Counter_Start
0x00007271 T HAL_LPTIM_Counter_Start_IT
0x000072d7 T HAL_LPTIM_Counter_Stop
0x000072f1 T HAL_LPTIM_Counter_Stop_IT
0x0000731b T HAL_LPTIM_DeInit
0x00007347 T HAL_LPTIM_DirectionDownCallback
0x00007349 T HAL_LPTIM_DirectionUpCallback
0x0000734b T HAL_LPTIM_GetState
0x00007415 T HAL_LPTIM_Init
0x000074ad T HAL_LPTIM_InitDefault
0x000074cf T HAL_LPTIM_MspDeInit
0x000074d1 T HAL_LPTIM_MspInit
0x000074d3 T HAL_LPTIM_OnePulse_Start
0x00007503 T HAL_LPTIM_OnePulse_Start_IT
0x0000755d T HAL_LPTIM_OnePulse_Stop
0x00007577 T HAL_LPTIM_OnePulse_Stop_IT
0x000075bb T HAL_LPTIM_OutCompareCallback
0x000075bd T HAL_LPTIM_OverFlowWakeUpCallback
0x000075bf T HAL_LPTIM_PWM_Start
0x000075fd T HAL_LPTIM_PWM_Start_IT
0x00007657 T HAL_LPTIM_PWM_Stop
0x00007671 T HAL_LPTIM_PWM_Stop_IT
0x000076b5 T HAL_LPTIM_ReadAutoReload
0x000076bb T HAL_LPTIM_ReadCompare
0x000076c1 T HAL_LPTIM_ReadCounter
0x000076c7 T HAL_LPTIM_SetOnce_Start
0x000076f7 T HAL_LPTIM_SetOnce_Start_IT
0x00007751 T HAL_LPTIM_SetOnce_Stop
0x0000776b T HAL_LPTIM_SetOnce_Stop_IT
0x000077af T HAL_LPTIM_TimeOut_Start
0x000077df T HAL_LPTIM_TimeOut_Start_IT
0x00007839 T HAL_LPTIM_TimeOut_Stop
0x0000785b T HAL_LPTIM_TimeOut_Stop_IT
0x000078a7 T HAL_LPTIM_TriggerCallback
0x000078a9 T HAL_LPTIM_UpdateEventCallback
0x000078ab T HAL_MAILBOX_DeInit
0x000078b1 T HAL_MAILBOX_GetMutex
0x000078dd T HAL_MAILBOX_GetState
0x000078e1 T HAL_MAILBOX_IRQHandler
0x00007905 T HAL_MAILBOX_Init
0x00007909 T HAL_MAILBOX_Lock
0x0000792d T HAL_MAILBOX_LockEx
0x0000794d T HAL_MAILBOX_MspDeInit
0x0000794f T HAL_MAILBOX_MspInit
0x00007951 T HAL_MAILBOX_UnLock
0x00007971 T HAL_MPU_ConfigRegion
0x0000797d T HAL_MPU_Disable
0x00007991 T HAL_MPU_Enable
0x000079b7 T HAL_MultiProcessor_Init
0x00007a43 T HAL_NAND_CONF_BUF
0x00007ab1 T HAL_NAND_CONF_ECC
0x00007b31 T HAL_NAND_EN_QUAL
0x00007b93 T HAL_NAND_ERASE_BLK
0x00007be7 T HAL_NAND_GET_BADBLK
0x00007c1b T HAL_NAND_GET_ECC_RESULT
0x00007c75 T HAL_NAND_GET_ECC_STATUS
0x00007cad T HAL_NAND_MARK_BADBLK
0x00007ced T HAL_NAND_READ_PAGE
0x00007dcb T HAL_NAND_READ_WITHOOB
0x00007f01 T HAL_NAND_SET_CONTINUE
0x00007ffd T HAL_NAND_WRITE_PAGE
0x00008153 T HAL_NAND_WRITE_WITHOOB
0x000082f1 T HAL_NVIC_ClearPendingIRQ
0x0000830d T HAL_NVIC_DisableIRQ
0x00008331 T HAL_NVIC_EnableIRQ
0x0000834d T HAL_NVIC_GetActive
0x00008351 T HAL_NVIC_GetPendingIRQ
0x00008371 T HAL_NVIC_GetPriority
0x000083c5 T HAL_NVIC_GetPriorityGrouping
0x000083d1 T HAL_NVIC_SetPendingIRQ
0x000083ed T HAL_NVIC_SetPriority
0x00008425 T HAL_NVIC_SetPriorityGrouping
0x00008445 T HAL_NVIC_SystemReset
0x0000853d T HAL_PIN_Get
0x000085a1 T HAL_PIN_Get_Base
0x000085cd T HAL_PIN_Select
0x000085ed T HAL_PIN_Set
0x00008685 T HAL_PIN_SetFlash4
0x000086cd T HAL_PIN_SetMode
0x00008721 T HAL_PIN_Set_DS0
0x00008751 T HAL_PIN_Set_DS1
0x00008781 T HAL_PIN_Set_Dual_flash1
0x000087c9 T HAL_PIN_Set_Dual_flash1_default
0x00008811 T HAL_PIN_Set_Dual_flash2
0x00008845 T HAL_PIN_Set_Dual_flash2_default
0x00008879 T HAL_PIN_Set_Single_flash2
0x000088c1 T HAL_PIN_Set_Single_flash2_default
0x0000890d T HAL_PIN_Update
0x0000895d T HAL_PMU_CheckBootMode
0x000089c9 T HAL_PMU_DisablePinWakeup
0x000089e5 T HAL_PMU_DisableRtcWakeup
0x000089f5 T HAL_PMU_DisableXTAL32
0x00008a05 T HAL_PMU_EnableBuck2
0x00008a25 T HAL_PMU_EnableDLL
0x00008a81 T HAL_PMU_EnableRtcWakeup
0x00008a95 T HAL_PMU_EnableXTAL32
0x00008ab5 T HAL_PMU_EnterHibernate
0x00008ac1 T HAL_PMU_EnterShutdown
0x00008af1 T HAL_PMU_LpCLockSelect
0x00008b21 T HAL_PMU_Reboot
0x00008b49 T HAL_PTC_Enable
0x00008b91 T HAL_PTC_IRQHandler
0x00008ba3 T HAL_PTC_Init
0x00008bc7 T HAL_PostMspInit_rom
0x00008bf9 T HAL_QSPIEX_BLK32_ERASE
0x00008c5f T HAL_QSPIEX_BLK64_ERASE
0x00008ccd T HAL_QSPIEX_CALCUL_ECC
0x00008d7d T HAL_QSPIEX_CHIP_ERASE
0x00008df7 T HAL_QSPIEX_CORRECT_DATA
0x00008eff T HAL_QSPIEX_FILL_EVEN
0x00008f31 T HAL_QSPIEX_FLASH_ERASE
0x00008f6d T HAL_QSPIEX_FLASH_RESET
0x00008fc7 T HAL_QSPIEX_FLASH_WRITE
0x00008feb T HAL_QSPIEX_SECT_ERASE
0x00009057 T HAL_QSPIEX_WRITE_PAGE
0x00009119 T HAL_QSPI_ENABLE_WDT
0x00009155 T HAL_QSPI_ERASE_OTP
0x000091e5 T HAL_QSPI_GET_CLK
0x0000923d T HAL_QSPI_GET_MEMSIZE
0x00009249 T HAL_QSPI_GET_OTP_LB
0x000092c5 T HAL_QSPI_GET_PSRAM_ID
0x00009301 T HAL_QSPI_GET_SR
0x0000935d T HAL_QSPI_GET_UID
0x000093a9 T HAL_QSPI_Init
0x000093e3 T HAL_QSPI_LOCK_OTP
0x00009577 T HAL_QSPI_READ_ID
0x0000958d T HAL_QSPI_READ_OTP
0x00009625 T HAL_QSPI_SET_DMABURST
0x00009649 T HAL_QSPI_SET_RXDELAY
0x00009685 T HAL_QSPI_WRITE_OTP
0x0000974d T HAL_RCC_CalibrateRC48
0x000097f1 T HAL_RCC_DisableModule_rom
0x00009807 T HAL_RCC_EnableModule
0x0000981d T HAL_RCC_GetHCLKFreq
0x00009855 T HAL_RCC_GetModuleFreq
0x00009859 T HAL_RCC_GetPCLKFreq
0x00009895 T HAL_RCC_GetSysCLKFreq
0x000098b1 T HAL_RCC_HCPU_ClockSelect
0x000098e5 T HAL_RCC_HCPU_GetClockSrc
0x00009901 T HAL_RCC_HCPU_GetDLL1Freq
0x00009907 T HAL_RCC_HCPU_GetDLL2Freq
0x0000990d T HAL_RCC_HCPU_GetDLL3Freq
0x00009915 T HAL_RCC_HCPU_GetDLLFreq
0x0000995d T HAL_RCC_LCPU_GetClockSrc
0x00009975 T HAL_RCC_LCPU_SetDiv
0x000099e9 T HAL_RCC_ReleaseLCPU
0x000099f5 T HAL_RCC_ResetBLERF
0x00009a15 T HAL_RCC_ResetLCPU
0x00009a51 T HAL_RCC_ResetModule
0x00009a55 T HAL_RCC_SetMacFreq
0x00009aa5 T HAL_RCC_SetModuleFreq
0x00009aa9 T HAL_RTC_AlarmAEventCallback
0x00009aab T HAL_RTC_DeInit
0x00009b27 T HAL_RTC_DeactivateAlarm
0x00009b77 T HAL_RTC_DeactivateWakeUpTimer
0x00009bc7 T HAL_RTC_GetAlarm
0x00009c1d T HAL_RTC_GetState
0x00009c53 T HAL_RTC_GetWakeUpTimer
0x00009c5d T HAL_RTC_IRQHandler
0x00009d59 T HAL_RTC_MspDeInit
0x00009d5b T HAL_RTC_MspInit
0x00009d5d T HAL_RTC_PollForAlarmAEvent
0x00009da1 T HAL_RTC_RegCallback
0x00009da9 T HAL_RTC_SetAlarm
0x00009e25 T HAL_RTC_SetDate
0x00009e7d T HAL_RTC_SetTime
0x00009ee5 T HAL_RTC_SetWakeUpTimer
0x00009f85 T HAL_RTC_WaitForSynchro
0x00009fb7 T HAL_RTC_WakeupTimerEventCallback
0x00009fbd T HAL_SDADC_ConfigAccu
0x0000a00d T HAL_SDADC_ConfigChannel
0x0000a065 T HAL_SDADC_ConfigGain
0x0000a08f T HAL_SDADC_ConvCpltCallback
0x0000a091 T HAL_SDADC_DeInit
0x0000a0c1 T HAL_SDADC_EnableSlot
0x0000a10f T HAL_SDADC_GetError
0x0000a113 T HAL_SDADC_GetState
0x0000a117 T HAL_SDADC_GetValue
0x0000a14b T HAL_SDADC_IRQHandler
0x0000a165 T HAL_SDADC_Init
0x0000a25d T HAL_SDADC_MspDeInit
0x0000a261 T HAL_SDADC_MspInit
0x0000a2dd T HAL_SDADC_PollForConversion
0x0000a337 T HAL_SDADC_SetSource
0x0000a347 T HAL_SDADC_SetTimer
0x0000a361 T HAL_SDADC_Start
0x0000a3b9 T HAL_SDADC_Start_DMA
0x0000a42d T HAL_SDADC_Start_IT
0x0000a457 T HAL_SDADC_Stop
0x0000a47f T HAL_SDADC_Stop_DMA
0x0000a4b9 T HAL_SDADC_Stop_IT
0x0000a4e5 T HAL_SPI_Abort
0x0000a57d T HAL_SPI_AbortCpltCallback
0x0000a581 T HAL_SPI_Abort_IT
0x0000a625 T HAL_SPI_DMAPause
0x0000a639 T HAL_SPI_DMAResume
0x0000a64d T HAL_SPI_DMAStop
0x0000a66b T HAL_SPI_DeInit
0x0000a699 T HAL_SPI_ErrorCallback
0x0000a69b T HAL_SPI_FlushRxFifo
0x0000a6c3 T HAL_SPI_GetError
0x0000a6c7 T HAL_SPI_GetState
0x0000a6cd T HAL_SPI_IRQHandler
0x0000a701 T HAL_SPI_Init
0x0000a793 T HAL_SPI_MspDeInit
0x0000a795 T HAL_SPI_MspInit
0x0000a799 T HAL_SPI_PSRAM_Init
0x0000a811 T HAL_SPI_Receive
0x0000a939 T HAL_SPI_Receive_DMA
0x0000aa35 T HAL_SPI_Receive_IT
0x0000aaf1 T HAL_SPI_RxCpltCallback
0x0000aaf3 T HAL_SPI_RxHalfCpltCallback
0x0000aaf5 T HAL_SPI_Transmit
0x0000ac2b T HAL_SPI_TransmitReceive
0x0000adcd T HAL_SPI_TransmitReceive_DMA
0x0000af0d T HAL_SPI_TransmitReceive_IT
0x0000afbd T HAL_SPI_Transmit_DMA
0x0000b099 T HAL_SPI_Transmit_IT
0x0000b125 T HAL_SPI_TxCpltCallback
0x0000b127 T HAL_SPI_TxHalfCpltCallback
0x0000b129 T HAL_SPI_TxRxCpltCallback
0x0000b12b T HAL_SPI_TxRxHalfCpltCallback
0x0000b12d T HAL_SYSTICK_CLKSourceConfig
0x0000b141 T HAL_SYSTICK_Callback
0x0000b145 T HAL_SYSTICK_Config
0x0000b16d T HAL_SYSTICK_IRQHandler
0x0000b173 T HAL_TIMEx_BreakCallback
0x0000b175 T HAL_TIMEx_CommutationCallback
0x0000b177 T HAL_TSEN_DeInit
0x0000b1f1 T HAL_TSEN_GetState
0x0000b1f5 T HAL_TSEN_IRQHandler
0x0000b22d T HAL_TSEN_Init
0x0000b261 T HAL_TSEN_Read
0x0000b2d9 T HAL_TSEN_Read_IT
0x0000b30d T HAL_UART_Abort
0x0000b381 T HAL_UART_AbortCpltCallback
0x0000b383 T HAL_UART_AbortReceive
0x0000b3d1 T HAL_UART_AbortReceiveCpltCallback
0x0000b3d5 T HAL_UART_AbortReceive_IT
0x0000b445 T HAL_UART_AbortTransmit
0x0000b47d T HAL_UART_AbortTransmitCpltCallback
0x0000b481 T HAL_UART_AbortTransmit_IT
0x0000b4d1 T HAL_UART_Abort_IT
0x0000b585 T HAL_UART_DMAPause
0x0000b5e1 T HAL_UART_DMAResume
0x0000b635 T HAL_UART_DMAStop
0x0000b693 T HAL_UART_DeInit
0x0000b6cd T HAL_UART_DmaTransmit
0x0000b725 T HAL_UART_ErrorCallback
0x0000b751 T HAL_UART_GetError
0x0000b755 T HAL_UART_GetState
0x0000b765 T HAL_UART_IRQHandler
0x0000b8b5 T HAL_UART_Init
0x0000b96d T HAL_UART_Receive
0x0000ba4d T HAL_UART_Receive_DMA
0x0000bacd T HAL_UART_Receive_IT
0x0000bb71 T HAL_UART_RxCpltCallback
0x0000bb75 T HAL_UART_RxHalfCpltCallback
0x0000bbdd T HAL_UART_Transmit
0x0000bc91 T HAL_UART_Transmit_DMA
0x0000bd0d T HAL_UART_Transmit_IT
0x0000bd75 T HAL_UART_TxCpltCallback
0x0000bd77 T HAL_UART_TxHalfCpltCallback
0x0000bd79 T HAL_UART_WakeupCallback
0x0000bd7d T HAL_WDT_Init_rom
0x0000bddd T HAL_WDT_Refresh_rom
0x0000bde9 T HCPU2LCPU_IRQHandler
0x0000e1d1 T ble_stack_msg_alloc
0x00014fc5 T ble_stack_msg_send
0x00015a89 T atoh
0x000186dd T bf0_gptimer_init2
0x00018771 T bf0_lptimer_init2
0x000188f9 T ble_aon_irq_handler
0x00018905 T ble_boot
0x00018925 T ble_deep_sleep_after_handler
0x00018929 T ble_deep_sleep_pre_handler
0x00018941 T ble_memory_config
0x00018955 T ble_port_config
0x0001896d T ble_standby_sleep_after_handler_rom
0x00018991 T ble_standby_sleep_pre_handler_rom
0x000193b5 T cmd_pin
0x0002c285 T hex2data
0x0002c85d T ipc_queue_check_idle_rom
0x0002c861 T ipc_queue_close
0x0002c8ad T ipc_queue_data_ind
0x0002c8e1 T ipc_queue_deinit
0x0002c90d T ipc_queue_get_user_data
0x0002c93d T ipc_queue_init
0x0002c9dd T ipc_queue_is_open
0x0002ca05 T ipc_queue_open_rom
0x0002ca7d T ipc_queue_read
0x0002cab9 T ipc_queue_restore_all_rom1
0x0002cb49 T ipc_queue_set_user_data
0x0002fcf5 T libc_system_init
0x0002fcf9 T list
0x0002fd69 T list_date
0x0002fd89 T list_device
0x0002fe5d T list_event
0x0002ff65 T list_mailbox
0x0003006d T list_mem
0x000300dd T list_memheap
0x000301b1 T list_mempool
0x000302d5 T list_msgqueue
0x000303c5 T list_mutex
0x00030491 T list_sem
0x00030579 T list_thread
0x00030689 T list_timer
0x0003ef75 T msh_auto_complete
0x0003f025 T msh_exec
0x0003f1fd T msh_help
0x0003f279 T msh_is_used
0x00040221 T print_sysinfo
0x000403fb T reboot
0x0004065d T rom_config_set_ble_service_working_core
0x00040665 T rom_config_set_default_rc_cycle
0x00040675 T rom_config_set_default_xtal_enabled
0x0004067d T rom_config_set_lld_prog_delay
0x00040691 T rom_scatterload
0x000406b5 T rt_alarm_control
0x0004074d T rt_alarm_create
0x0004079d T rt_alarm_delete
0x000407f9 T rt_alarm_dump
0x00040909 T rt_alarm_start
0x000409d5 T rt_alarm_stop
0x00040a21 T rt_alarm_system_init
0x00040a6d T rt_alarm_update
0x00040b11 T rt_assert_handler_rom
0x00040ba5 T rt_assert_set_hook_rom
0x00040cf1 T rt_calloc
0x00040d11 T rt_completion_done
0x00040d71 T rt_completion_init
0x00040db1 T rt_completion_wait
0x00040f15 T rt_console_get_device
0x00040f1d T rt_console_set_device
0x00040f4d T rt_critical_level
0x00040f59 T rt_data_queue_init
0x00040fbd T rt_data_queue_peak
0x00041021 T rt_data_queue_pop
0x000411b5 T rt_data_queue_push
0x000412e5 T rt_data_queue_reset
0x00041345 T rt_delayed_work_init
0x00041355 T rt_device_bidir_hwmailbox_register
0x000413f1 T rt_device_close
0x0004144d T rt_device_control
0x00041495 T rt_device_create
0x000414b9 T rt_device_destroy
0x00041541 T rt_device_find
0x000415c5 T rt_device_hwmailbox_isr
0x00041605 T rt_device_hwmailbox_register
0x0004169d T rt_device_hwtimer_isr
0x00041709 T rt_device_hwtimer_register
0x000417c5 T rt_device_init
0x00041815 T rt_device_init_all
0x00041819 T rt_device_open
0x000418dd T rt_device_pin_register_rom
0x0004193d T rt_device_read
0x000419a1 T rt_device_register
0x000419cd T rt_device_set_rx_indicate
0x00041a01 T rt_device_set_tx_complete
0x00041a35 T rt_device_unregister
0x00041aa9 T rt_device_write
0x00041b75 T rt_enter_critical
0x00041b91 T rt_event_control
0x00041be9 T rt_event_create
0x00041c35 T rt_event_delete
0x00041cf9 T rt_event_detach
0x00041d89 T rt_event_init
0x00041dd1 T rt_event_recv
0x00041f51 T rt_event_send
0x00042029 T rt_exit_critical
0x00042061 T rt_free
0x000421f9 T rt_free_align
0x00042201 T rt_free_sethook
0x0004220d T rt_get_errno
0x0004222d T rt_hexdump
0x00042459 T rt_hw_backtrace
0x000424b5 T rt_hw_ble_int_init
0x0004250d T rt_hw_console_output
0x00042511 T rt_hw_cpu_reset
0x00042521 T rt_hw_cpu_shutdown
0x000426a1 T rt_hw_exception_install_rom
0x00042895 T rt_hw_i2c_init2
0x000429ed T rt_hw_pin_init_rom
0x00042ab5 T rt_hw_sensor_register
0x00042c49 T rt_hw_serial_isr
0x00042e7d T rt_hw_serial_register
0x00042f0d T rt_hw_show_memory
0x00042f9d T rt_hw_spi_bus_init
0x00043075 T rt_hw_spi_device_attach
0x00043151 T rt_hw_stack_init_rom
0x0004318d T rt_hw_systick_init
0x000431b9 T rt_hw_us_delay
0x000431f1 T rt_hw_usart_init2
0x000437c9 T rt_i2c_bus_device_device_init
0x00043845 T rt_i2c_bus_device_find
0x0004385d T rt_i2c_bus_device_register
0x00043899 T rt_i2c_close
0x00043905 T rt_i2c_configure
0x00043969 T rt_i2c_control_rom
0x00043a05 T rt_i2c_core_init
0x00043a09 T rt_i2c_master_recv
0x00043a69 T rt_i2c_master_send
0x00043a91 T rt_i2c_mem_read
0x00043afd T rt_i2c_mem_write
0x00043b71 T rt_i2c_open
0x00043c0d T rt_i2c_transfer
0x00043d21 T rt_kprintf
0x00043d75 T rt_kputs
0x00043dd5 T rt_malloc
0x00043fe1 T rt_malloc_align
0x00044015 T rt_malloc_sethook
0x00044021 T rt_mb_control
0x00044085 T rt_mb_create
0x00044105 T rt_mb_delete
0x000441d9 T rt_mb_detach
0x0004426d T rt_mb_init
0x000442cd T rt_mb_recv
0x00044489 T rt_mb_send
0x00044491 T rt_mb_send_wait
0x0004462d T rt_mem_backup
0x0004468d T rt_mem_base
0x00044695 T rt_mem_restore
0x000446f5 T rt_mem_size
0x0004477d T rt_memcmp
0x00044797 T rt_memcpy
0x000447ed T rt_memheap_alloc
0x0004491d T rt_memheap_backup
0x00044985 T rt_memheap_calloc
0x000449a5 T rt_memheap_detach
0x00044a25 T rt_memheap_free
0x00044be1 T rt_memheap_init
0x00044c95 T rt_memheap_realloc
0x00044ed5 T rt_memheap_restore
0x00044f45 T rt_memmove
0x00044f71 T rt_memory_info
0x00044f91 T rt_memset
0x00044fd5 T rt_mp_alloc
0x000450fd T rt_mp_alloc_sethook
0x00045109 T rt_mp_create
0x0004519d T rt_mp_delete
0x00045285 T rt_mp_detach
0x00045325 T rt_mp_free
0x00045379 T rt_mp_free_sethook
0x00045385 T rt_mp_init
0x00045405 T rt_mq_control
0x00045479 T rt_mq_create
0x0004551d T rt_mq_delete
0x000455e9 T rt_mq_detach
0x00045675 T rt_mq_init
0x000456f5 T rt_mq_recv_rom
0x000458e9 T rt_mq_send
0x000459b5 T rt_mq_urgent
0x00045a9d T rt_mutex_control
0x00045ad1 T rt_mutex_create
0x00045b21 T rt_mutex_delete
0x00045bed T rt_mutex_detach
0x00045c7d T rt_mutex_init
0x00045ccd T rt_mutex_release
0x00045df1 T rt_mutex_take
0x00045f41 T rt_object_allocate
0x00045fe1 T rt_object_attach_sethook
0x00045fe9 T rt_object_delete
0x00046079 T rt_object_detach
0x000460cd T rt_object_detach_sethook
0x000460d9 T rt_object_find
0x0004615d T rt_object_get_information
0x00046185 T rt_object_get_type
0x000461b9 T rt_object_init
0x00046255 T rt_object_is_systemobject
0x0004628d T rt_object_put_sethook
0x00046299 T rt_object_take_sethook
0x000462a5 T rt_object_trytake_sethook
0x000462b1 T rt_pin_attach_irq_rom
0x00046309 T rt_pin_detach_irq_rom
0x00046351 T rt_pin_irq_enable_rom
0x000463e5 T rt_pin_mode_rom
0x00046485 T rt_pin_read_rom
0x000464c1 T rt_pin_write_rom
0x00046505 T rt_pipe_close
0x00046539 T rt_pipe_control
0x0004653d T rt_pipe_create
0x00046605 T rt_pipe_delete
0x00046649 T rt_pipe_open
0x0004667b T rt_pipe_read
0x000466cb T rt_pipe_write
0x00046a51 T rt_rbb_blk_alloc
0x00046b5d T rt_rbb_blk_buf
0x00046b89 T rt_rbb_blk_free
0x00046c11 T rt_rbb_blk_get
0x00046c71 T rt_rbb_blk_put
0x00046cd1 T rt_rbb_blk_queue_buf
0x00046d05 T rt_rbb_blk_queue_free
0x00046d49 T rt_rbb_blk_queue_get
0x00046df9 T rt_rbb_blk_queue_len
0x00046e41 T rt_rbb_blk_size
0x00046e6d T rt_rbb_create
0x00046e9d T rt_rbb_destroy
0x00046ed1 T rt_rbb_get_buf_size
0x00046f01 T rt_rbb_init
0x00046f3d T rt_rbb_next_blk_queue_len
0x00046fb9 T rt_realloc
0x000470dd T rt_ringbuffer_create
0x00047139 T rt_ringbuffer_data_len
0x00047165 T rt_ringbuffer_destroy
0x000471a5 T rt_ringbuffer_get
0x00047231 T rt_ringbuffer_get_and_update_len
0x00047311 T rt_ringbuffer_getchar
0x00047375 T rt_ringbuffer_init
0x000473ad T rt_ringbuffer_put
0x0004744d T rt_ringbuffer_put_force
0x00047501 T rt_ringbuffer_putchar
0x00047569 T rt_ringbuffer_putchar_force
0x000475dd T rt_ringbuffer_rd_init
0x000475e1 T rt_ringbuffer_reset
0x0004762d T rt_ringbuffer_wr_init
0x000477f5 T rt_schedule
0x00047861 T rt_schedule_insert_thread
0x000478d1 T rt_schedule_remove_thread
0x00047945 T rt_scheduler_sethook
0x00047951 T rt_sem_control
0x000479a9 T rt_sem_create
0x00047a0d T rt_sem_delete
0x00047ad5 T rt_sem_detach
0x00047b59 T rt_sem_init
0x00047ba1 T rt_sem_release
0x00047c0d T rt_sem_take
0x00047d2d T rt_sem_trytake
0x000488b9 T rt_set_errno
0x000488d9 T rt_show_sys_info
0x00048a01 T rt_show_version
0x00048aa1 T rt_snprintf
0x00048ab9 T rt_soft_timer_check
0x00048b41 T rt_spi_bus_attach_device
0x00048b89 T rt_spi_bus_device_init
0x00048bed T rt_spi_bus_register
0x00048c21 T rt_spi_configure
0x00048c89 T rt_spi_release
0x00048cd9 T rt_spi_release_bus
0x00048d49 T rt_spi_send_then_recv
0x00048dfd T rt_spi_send_then_send
0x00048eb1 T rt_spi_take
0x00048f01 T rt_spi_take_bus
0x00048f81 T rt_spi_transfer
0x00049021 T rt_spi_transfer_message
0x000490c1 T rt_spidev_device_init
0x00049135 T rt_sprintf
0x0004914d T rt_strcasecmp
0x00049175 T rt_strcmp
0x0004918b T rt_strdup
0x000491a7 T rt_strlen
0x000491b5 T rt_strncmp
0x000491d3 T rt_strncpy
0x000491fd T rt_strnlen
0x00049213 T rt_strstr
0x00049249 T rt_system_heap_init
0x00049331 T rt_system_object_init
0x00049525 T rt_system_scheduler_init
0x00049551 T rt_system_scheduler_start
0x0004957d T rt_system_tick_init
0x00049581 T rt_system_timer_init
0x0004958d T rt_system_timer_recovery_compensation
0x000495cd T rt_system_timer_thread_init
0x0004961d T rt_thread_control
0x000496ad T rt_thread_create
0x000496ed T rt_thread_delay
0x000496f9 T rt_thread_delete
0x000497b5 T rt_thread_detach
0x00049879 T rt_thread_exit
0x000498cd T rt_thread_find
0x0004995d T rt_thread_idle_delhook
0x000499ad T rt_thread_idle_excute
0x00049a81 T rt_thread_idle_gethandler
0x00049ac5 T rt_thread_idle_sethook
0x00049af9 T rt_thread_init
0x00049b71 T rt_thread_inited_sethook
0x00049b7d T rt_thread_mdelay
0x00049b8d T rt_thread_resume
0x00049c05 T rt_thread_resume_sethook
0x00049c11 T rt_thread_self
0x00049c1d T rt_thread_sleep
0x00049c85 T rt_thread_stack_backup
0x00049cb5 T rt_thread_stack_restore
0x00049d4d T rt_thread_startup
0x00049df5 T rt_thread_suspend
0x00049e69 T rt_thread_suspend_sethook
0x00049e75 T rt_thread_timeout
0x00049f5d T rt_thread_yield
0x00049fb5 T rt_tick_from_millisecond
0x00049fdd T rt_tick_get
0x00049fe5 T rt_tick_increase
0x0004a00d T rt_tick_set
0x0004a025 T rt_timer_check
0x0004a0a1 T rt_timer_control
0x0004a0fd T rt_timer_create
0x0004a17d T rt_timer_delete
0x0004a219 T rt_timer_detach
0x0004a2a5 T rt_timer_enter_sethook
0x0004a2ad T rt_timer_exit_sethook
0x0004a2b9 T rt_timer_init
0x0004a311 T rt_timer_next_timeout_tick
0x0004a329 T rt_timer_start
0x0004a461 T rt_timer_start_int
0x0004a481 T rt_timer_stop
0x0004a4f1 T rt_vsnprintf
0x0004a9ad T rt_vsprintf
0x0004a9b9 T rt_workqueue_cancel_all_work
0x0004aa0d T rt_workqueue_cancel_work
0x0004aa75 T rt_workqueue_cancel_work_sync
0x0004aacd T rt_workqueue_create
0x0004ab29 T rt_workqueue_critical_work
0x0004ab91 T rt_workqueue_destroy
0x0004abd1 T rt_workqueue_dowork
0x0004ac05 T rt_workqueue_submit_work
0x0004acc1 T rt_wqueue_add
0x0004ace1 T rt_wqueue_remove
0x0004ad01 T rt_wqueue_wait
0x0004aded T rt_wqueue_wakeup
0x0004aff9 T rwble_isr
0x0004c069 T sensor_cmd
0x0004c355 T sensor_cmd_fifo
0x0004c3e5 T sensor_cmd_int
0x0004c485 T sensor_cmd_polling
0x0004c75d T set_date
0x0004c7b9 T set_time
0x0004c829 T sf_register_access
0x0004c9ad T show_date
0x00052219 T sysinfo
0x000526d9 T uart_isr
0x000009bd T mktime
0x00000a73 T __aeabi_memcpy
0x00000a73 T __aeabi_memcpy4
0x00000a73 T __aeabi_memcpy8
0x00000a73 T __aeabi_memmove
0x00000a73 T __aeabi_memmove4
0x00000a73 T __aeabi_memmove8
0x00000ab3 T __aeabi_memset
0x00000ab3 T __aeabi_memset4
0x00000ab3 T __aeabi_memset8
0x00000ac1 T __aeabi_memclr
0x00000ac1 T __aeabi_memclr4
0x00000ac1 T __aeabi_memclr8
0x00000ad7 T strcat
0x00000aef T strncpy
0x00000b07 T strlen
0x00000b15 T strcmp
0x00000b31 T memcmp
0x00000b4b T strcpy
0x00000b5d T strncmp
0x00000b7b T atoi
0x00000b95 T __aeabi_dadd
0x00000cd7 T __aeabi_dsub
0x00000cdd T __aeabi_drsub
0x00000ce3 T __aeabi_dmul
0x00000dc7 T __aeabi_ddiv
0x00000ea5 T __aeabi_dcmple
0x00000edb T __aeabi_dcmplt
0x00000f11 T __aeabi_dcmpge
0x00000f47 T __aeabi_dcmpeq
0x00000f7f T __aeabi_i2d
0x00000fa1 T __aeabi_d2iz
0x00000fdf T __aeabi_f2d
0x00001005 T __aeabi_d2f
0x0000103d T __aeabi_uidiv
0x0000103d T __aeabi_uidivmod
0x00001069 T __aeabi_uldivmod
0x000010cb T __aeabi_llsl
0x000010cb T _ll_shift_l                
0x000010e9 T __aeabi_llsr               
0x000010e9 T _ll_ushift_r               
0x00001109 T __aeabi_lasr               
0x00001109 T _ll_sshift_r               
0x000011c5 T asctime                    
0x00001281 T strtol                     
0x000012f1 T _float_round               
0x00001303 T _float_epilogue            
0x0000135f T _double_round              
0x0000137d T _double_epilogue           
0x00001419 T __aeabi_d2ulz              
0x00001449 T __aeabi_cdrcmple           
0x000014a5 T _strtoul                   
#0x000531a1 T vsnprintf
#0x000531d5 T __aeabi_errno_addr
0x000531dd T __hardfp_modf
0x00054530 D HAL_HPAON_WakeupPinMapTbl
0x00054550 D HAL_LPAON_WakeupPinMapTbl
0x000547d4 D _lp_ops
0x2012d690 D ipc_hw_obj
0x2012d6dc D pin_irq_hdr_tab
0x2012d960 D rt_interrupt_nest
0x2012d970 D rt_assert_hook_rom1
0x2012d9c0 D rt_current_priority
0x2012d9c8 D rt_current_thread
0x2012d9cc D rt_thread_ready_priority_group
0x2012d9d0 D rt_thread_defunct
0x2012d9e4 D rt_timer_list
0x2012d9ec D rt_soft_timer_list
0x2012d9f4 D RTC_Handler
0x2012dd30 D _pm_notify
0x2012dd38 D _pm
0x2012de60 D _hw_pin
0x2012df24 D _syscall_table_begin
0x2012df28 D _syscall_table_end
0x2012df2c D _sysvar_table_begin
0x2012df30 D _sysvar_table_end
0x2012ec44 D idle_hook_list
0x2012ec54 D ipc_ctx
0x2012f260 D rt_interrupt_from_thread
0x2012f264 D rt_interrupt_to_thread
0x2012f2f8 D rt_object_put_hook
0x2012f2fc D rt_object_take_hook
0x2012f300 D rt_object_trytake_hook
0x2012f308 D rt_thread_priority_table
0x2012f410 D rt_thread_switch_interrupt_flag
0x00058930 D i2c_ops_rom