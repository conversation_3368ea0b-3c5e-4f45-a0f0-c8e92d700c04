/**
 ******************************************************************************
 * @file   bsp_pinmux.c
 * <AUTHOR> software development team
 ******************************************************************************
 */
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "bsp_board.h"
#include "bsp_pinmux.h"
#include "rtthread.h"
#include "version_check_api.h"
#include "drv_io.h"
#include "bsp_board_bus.h"

/**
 * @brief 合封与固定引脚
 *
 */
static void BSP_PIN_FixedInit(void)
{
    static bool fixio_init_flag = false;
    //  if (fixio_init_flag)
    //      return;
    GPIO_InitTypeDef GPIO_InitStruct;
#ifdef BF0_HCPU
    // 用于HCPU PSRAM合封的固定引脚
    uint32_t pid = (hwp_hpsys_cfg->IDR & HPSYS_CFG_IDR_PID) >> HPSYS_CFG_IDR_PID_Pos;

    if (pid == 0x1)
    {
        HAL_PIN_Set(PAD_SIP01, PSRAM_DQ0, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP02, PSRAM_DQ1, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP03, PSRAM_DQ2, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP04, PSRAM_DQ3, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP05, PSRAM_CS, PIN_NOPULL, 1);

        HAL_PIN_Set(PAD_SIP07, PSRAM_CLK, PIN_NOPULL, 1);
        HAL_PIN_Set(PAD_SIP08, PSRAM_DQ4, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP09, PSRAM_DQ5, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP10, PSRAM_DQ6, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP11, PSRAM_DQ7, PIN_PULLDOWN, 1);
        HAL_PIN_Set(PAD_SIP12, PSRAM_DQS0, PIN_PULLDOWN, 1);
    }

#else /*BF0_LCPU*/

#endif
    // LCPU QSPI4 PSRAM
    HAL_PIN_Set(PAD_PB24, GPIO_B24, PIN_NOPULL, 0);
    HAL_PIN_Set(PAD_PB25, GPIO_B25, PIN_NOPULL, 0);
    // PB00~PB03 PB07 PB21为内部IO,外部禁止使用
    HAL_PIN_Set(PAD_PB00, QSPI4_CLK, PIN_NOPULL, 0);
    HAL_PIN_Set(PAD_PB01, QSPI4_CS, PIN_NOPULL, 0);
    HAL_PIN_Set(PAD_PB02, QSPI4_DIO0, PIN_PULLDOWN, 0);
    HAL_PIN_Set(PAD_PB03, QSPI4_DIO1, PIN_PULLDOWN, 0);
    HAL_PIN_Set(PAD_PB21, QSPI4_DIO2, PIN_PULLUP, 0);
    HAL_PIN_Set(PAD_PB07, QSPI4_DIO3, PIN_PULLUP, 0);
    fixio_init_flag = true;
}

/**
 * @brief 用户自定义引脚
 *
 */
static void BSP_PIN_CustomInit(void)
{
#ifdef BF0_HCPU /*大核初始化*/
    // 蜂鸣器音量ENA
    qw_gpio_set(Beep_volume_ENA, GPIO_MODE_OUTPUT, PIN_NOPULL); // see dc009.c // 在BOOT里APP复位后模块未初始化前关闭蜂鸣器倍压
    // TP复位脚
    qw_gpio_set(CTP_RST_PIN, GPIO_MODE_OUTPUT, PIN_NOPULL);
    // I2C1 SCL
    //   qw_special_pin_set(I2C1_SCL_PIN, I2C1_SCL, PIN_PULLUP); //标准I2C接口会在I2C驱动初始化
    // 蜂鸣器音量ENB
    qw_gpio_set(Beep_volume_ENB, GPIO_MODE_OUTPUT, PIN_NOPULL); // see dc009.c

    BSP_GPIO_Set(Beep_volume_ENA, 0, 1);
    BSP_GPIO_Set(Beep_volume_ENB, 0, 1);

    // I2C1 SDA
    //   qw_special_pin_set(I2C1_SDA_PIN, I2C1_SDA, PIN_PULLUP);
    // 大核调试口TX
    qw_special_pin_set(DBUG_A_UART_TXD, USART1_TXD, PIN_NOPULL);
    // 大核调试口RX
    qw_special_pin_set(DBUG_A_UART_RXD, USART1_RXD, PIN_PULLUP);
    // 预留外部RTC I2C CLK
    qw_special_pin_set(I2C3_SCL_PIN, I2C3_SCL, PIN_PULLUP);
    //
    qw_special_pin_set(I2C3_SDA_PIN, I2C3_SDA, PIN_PULLUP);
    // 内核存储供电使能
    qw_gpio_set(MCU_MEM_1V8_EN, GPIO_MODE_OUTPUT, PIN_PULLUP);
    // OTS_INT_PIN TODO挪到光旋钮驱动
    qw_gpio_set(OTS_INT_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
    // TP唤醒脚 TODO 旧代码PIN_NOPULL, BSP_IO_Power_Down 配置下拉输入
    qw_gpio_set(CTP_INT_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
    // BACK唤醒脚  低电平有效
    //qw_gpio_set(BACK_KEY_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
    // BLE_SPI3_DO
    // qw_special_pin_set(BLE_SPI3_DO, SPI3_DO, PIN_NOPULL);   //see ble_spi_io_init
    //
    // qw_gpio_set(GPS_RTC_EINT_PIN, GPIO_MODE_OUTPUT, PIN_NOPULL); // see gps_io_init
    //
    // qw_special_pin_set(BLE_SPI3_CLK, SPI3_CLK, PIN_NOPULL); //see ble_spi_io_init
    //
    // qw_gpio_set(NORDIC_AIN0, GPIO_MODE_INPUT, PIN_PULLUP);  //see stm32_ble_firmware_update_ready_init
    //
    //  qw_special_pin_set(BLE_SPI3_DI_PIN, SPI3_DI, PIN_NOPULL);// see ble_spi_io_init
    // clone
    qw_gpio_set(GPS_POWER_PIN, GPIO_MODE_OUTPUT, PIN_NOPULL);
    //
    // qw_special_pin_set(BLE_SPI3_CS, SPI3_CS, PIN_NOPULL);   // see ble_spi_io_init
    // 用于pmic的软件模拟I2C_SCL
    // qw_gpio_set(PMIC_I2C_SCL, GPIO_MODE_OUTPUT, PIN_PULLUP); // see BSP_PMIC_Init
    // qw_gpio_set(PMIC_I2C_SDA, GPIO_MODE_OUTPUT, PIN_PULLUP); // see BSP_PMIC_Init
    // TODO
    //  qw_special_pin_set(BEEP_PWM_PIN, GPTIM5_CH4, PIN_NOPULL); // see dc009.c
    //
#ifdef I2C6_SCL_PIN
    qw_special_pin_set(I2C6_SCL_PIN, I2C6_SCL, PIN_PULLUP);
    //
    qw_special_pin_set(I2C6_SDA_PIN, I2C6_SDA, PIN_PULLUP);
#endif
    //
    qw_gpio_set(GNSS_CHIP_EN, GPIO_MODE_OUTPUT, PIN_NOPULL);
    //
    qw_gpio_set(LCD_RST_PIN, GPIO_MODE_OUTPUT, PIN_NOPULL);
    qw_gpio_set(LCD_VDDIO_EN, GPIO_MODE_OUTPUT, PIN_NOPULL);

    //
    // qw_gpio_set(NORDIC_AIN1, GPIO_MODE_INPUT, PIN_PULLUP); // see stm32_phy_spi.c
    //
    // qw_gpio_set(NORDIC_AIN2, GPIO_MODE_OUTPUT, PIN_PULLUP);

#else /* BF0_LCPU */
    // I2C4 SCL
    qw_special_pin_set(I2C4_SCL_PIN, I2C4_SCL, PIN_PULLUP);
    // I2C4 SDA
    qw_special_pin_set(I2C4_SDA_PIN, I2C4_SDA, PIN_PULLUP);
    //
    qw_gpio_set(PPG_VLED_BOOST_EN, GPIO_MODE_OUTPUT, PIN_NOPULL);
    //
    qw_special_pin_set(DBUG_B_UART_TXD, USART4_TXD, PIN_NOPULL);
    //
    qw_special_pin_set(DBUG_B_UART_RXD, USART4_RXD, PIN_PULLUP);
    //
    qw_gpio_set(PPG_LDO_EN, GPIO_MODE_OUTPUT, PIN_PULLUP);
    //
    qw_gpio_set(PPG_RST, GPIO_MODE_OUTPUT, PIN_NOPULL);
    //
    qw_special_pin_set(I2C6_SCL_PIN, I2C6_SCL, PIN_PULLUP);
    //
    qw_special_pin_set(I2C6_SDA_PIN, I2C6_SDA, PIN_PULLUP);
    //
    qw_special_pin_set(PPG_SPI4_CLK, SPI4_CLK, PIN_NOPULL);
    //
    qw_special_pin_set(PPG_SPI4_CS, QSPI4_CS, PIN_NOPULL);
    //
    qw_special_pin_set(PPG_SPI4_DI, SPI4_DI, PIN_NOPULL);
    //
    qw_special_pin_set(PPG_SPI4_DO, SPI4_DO, PIN_NOPULL);
    //
    qw_special_pin_set(I2C5_SCL_PIN, I2C5_SCL, PIN_PULLUP);
    //
    qw_special_pin_set(I2C5_SDA_PIN, I2C5_SDA, PIN_PULLUP);
    // OK唤醒脚  低电平有效
    // qw_gpio_set(OK_KEY_PIN, GPIO_MODE_INPUT, PIN_PULLUP);
    // POWER唤醒脚  低电平有效
    qw_gpio_set(POWER_KEY_PIN, GPIO_MODE_INPUT, PIN_NOPULL);
    //
    qw_gpio_set(LDS6DSO_INT_PIN, GPIO_MODE_INPUT, PIN_NOPULL);

#endif
}

// A3和A4板的差异部分放这里
void BSP_PIN_CustomInit_V2(int hw_ver)
{
#ifdef BF0_HCPU
    //马达复位管脚初始化
    i2c1_device_rstpin_init();

    // 支付宝复位使能
    int pin = (hw_ver == HARDWARE_VERSION_A3) ? ZFB_RST_PIN : ZFB_RST_PIN_A4;
    qw_gpio_set(pin, GPIO_MODE_OUTPUT, PIN_PULLUP);     // 在BOOT里和APP复位后模块未初始化前拉低RST

#ifdef IGS_BOOT
    BSP_GPIO_Set(pin, 0, 1);
#endif
    pin = (hw_ver == HARDWARE_VERSION_A3) ? LCD_VCI_EN : LCD_VCI_EN_A4;
    qw_gpio_set(pin, GPIO_MODE_OUTPUT, PIN_NOPULL);

    pin = (hw_ver == HARDWARE_VERSION_A3) ? OK_KEY_PIN : OK_KEY_PIN_A4;
    //qw_gpio_set(pin, GPIO_MODE_INPUT, PIN_NOPULL);

    if (hw_ver == HARDWARE_VERSION_A3) {
        HAL_PIN_Set_Analog(PAD_PA31, 1);
        HAL_PIN_Set_Analog(PAD_PA33, 1);
        HAL_PIN_Set_Analog(PAD_PA34, 1);
        HAL_PIN_Set_Analog(PAD_PA63, 1);
        HAL_PIN_Set_Analog(PAD_PA77, 1);
        HAL_PIN_Set_Analog(PAD_PA78, 1);
    }
    else {
        HAL_PIN_Set_Analog(PAD_PA04, 1);
        HAL_PIN_Set_Analog(PAD_PA21, 1);
        HAL_PIN_Set_Analog(PAD_PA27, 1);
        HAL_PIN_Set_Analog(PAD_PA67, 1);
        HAL_PIN_Set_Analog(PAD_PB15, 0);
        HAL_PIN_Set_Analog(PAD_PB28, 0);
    }
#else /* BF0_LCPU */
#endif
}

/**
 * @brief 未使用引脚高阻初始化
 *
 */
static void BSP_PIN_UnusedInit(void)
{
    static bool unsed_init_flag = false;
    //  if(unsed_init_flag)
    //      return;
    GPIO_InitTypeDef GPIO_InitStruct;
#ifdef BF0_HCPU
    HAL_PIN_Set_Analog(PAD_PA01, 1); // USB is not used in APP
    HAL_PIN_Set_Analog(PAD_PA02, 1); //
    HAL_PIN_Set_Analog(PAD_PA03, 1); // USB is not used in APP
    HAL_PIN_Set_Analog(PAD_PA05, 1); //
    HAL_PIN_Set_Analog(PAD_PA07, 1); //
    HAL_PIN_Set_Analog(PAD_PA12, 1); //
    HAL_PIN_Set_Analog(PAD_PA13, 1); //
    HAL_PIN_Set_Analog(PAD_PA15, 1); //
    HAL_PIN_Set_Analog(PAD_PA18, 1); //
    HAL_PIN_Set_Analog(PAD_PA20, 1); //
    HAL_PIN_Set_Analog(PAD_PA23, 1); //
    HAL_PIN_Set_Analog(PAD_PA25, 1); //
    HAL_PIN_Set_Analog(PAD_PA26, 1); //
    // HAL_PIN_Set_Analog(PAD_PA31, 1); // 预留LCD控制
    HAL_PIN_Set_Analog(PAD_PA32, 1); //
    // HAL_PIN_Set_Analog(PAD_PA33, 1); //
    // HAL_PIN_Set_Analog(PAD_PA34, 1); //
    HAL_PIN_Set_Analog(PAD_PA35, 1); // LCD同步中断
    HAL_PIN_Set_Analog(PAD_PA36, 1); //
    HAL_PIN_Set_Analog(PAD_PA37, 1); //
    HAL_PIN_Set_Analog(PAD_PA38, 1); //
    HAL_PIN_Set_Analog(PAD_PA41, 1); // 预留flash使能
    HAL_PIN_Set_Analog(PAD_PA42, 1); //
    HAL_PIN_Set_Analog(PAD_PA43, 1); // 预留外部RTC中断
    HAL_PIN_Set_Analog(PAD_PA44, 1); // 预留flash QSPI_CLK
    HAL_PIN_Set_Analog(PAD_PA45, 1); // 预留flash QSPI_CS
    HAL_PIN_Set_Analog(PAD_PA46, 1); // 外部RTC不用
    HAL_PIN_Set_Analog(PAD_PA47, 1); // 预留flash QSPI_CS
    HAL_PIN_Set_Analog(PAD_PA48, 1); // 预留外部RTC I2C SDA
    HAL_PIN_Set_Analog(PAD_PA49, 1); // 预留flash QSPI_DIO1
    HAL_PIN_Set_Analog(PAD_PA51, 1); // 预留flash QSPI_DIO1
    HAL_PIN_Set_Analog(PAD_PA52, 1); //
    HAL_PIN_Set_Analog(PAD_PA53, 1); //
    HAL_PIN_Set_Analog(PAD_PA54, 1); //
    HAL_PIN_Set_Analog(PAD_PA55, 1); // 预留flash QSPI_DIO3
    HAL_PIN_Set_Analog(PAD_PA56, 1); //
    HAL_PIN_Set_Analog(PAD_PA59, 1); //
    // HAL_PIN_Set_Analog(PAD_PA63, 1); //
    HAL_PIN_Set_Analog(PAD_PA65, 1); //
    HAL_PIN_Set_Analog(PAD_PA66, 1); //
    // HAL_PIN_Set_Analog(PAD_PA77, 1); //
    // HAL_PIN_Set_Analog(PAD_PA78, 1); //

#else /* BF0_LCPU */
    //HAL_PIN_Set_Analog(PAD_PB16, 0); //
    HAL_PIN_Set_Analog(PAD_PB17, 0); //
    HAL_PIN_Set_Analog(PAD_PB41, 0); //
#endif
    unsed_init_flag = true;

#ifdef IGS_BOOT
    HAL_Delay_us(100);
#endif
}

void BSP_PIN_Init(void)
{
    // rt_kprintf("into %s %d\n", __func__, __LINE__);
    BSP_PIN_FixedInit();  // 固定引脚初始化
    BSP_PIN_CustomInit(); // 用户自定义引脚初始化
    BSP_PIN_UnusedInit(); // 未使用引脚高阻初始化
}
