#include "hynitron_core.h"

#if HYNITRON_ENABLE_UPGRADE
#include "hynitron_firmware.h"
#endif


struct hyn_chip g_chip_obj;
struct hyn_chip* p_g_chip_obj = NULL;
static void (* const g_init_chip_obj[])(struct hyn_chip*) = {
    hyn_cst92xx_init_obj,
};

//----------------------------------------------------------------------------------------------------------------
static struct rt_i2c_bus_device* ft_bus = NULL;
//----------------------IIC转接适配
static int32_t _write_reg(uint8_t reg, uint8_t* bufp, uint16_t len)
{
    rt_int8_t res = 0;
    struct rt_i2c_msg msgs[1];

    msgs[0].addr = reg;
    msgs[0].flags = RT_I2C_WR;
    msgs[0].buf = bufp;
    msgs[0].len = len;

    if (rt_i2c_transfer(ft_bus, msgs, 1) == 1)
    {
        res = RT_EOK;
    }
    else
    {
        res = -RT_ERROR;
    }
    return res;
}

static int _read_reg(uint8_t reg, uint8_t* bufp, uint16_t len)
{
    rt_int8_t res = 0;
    rt_uint8_t reg_buf[2];
    struct rt_i2c_msg msgs[2];
    reg_buf[0] = bufp[0];
    reg_buf[0] = bufp[1];

    msgs[0].addr = reg;
    msgs[0].flags = RT_I2C_WR;
    msgs[0].buf = reg_buf;
    msgs[0].len = 2;

    msgs[1].addr = reg;
    msgs[1].flags = RT_I2C_RD;
    msgs[1].buf = bufp;
    msgs[1].len = len;

    if (rt_i2c_transfer(ft_bus, msgs, 2) == 2)
    {
        res = RT_EOK;
    }
    else
    {
        res = -RT_ERROR;
    }
    return res;


}

//---------------------------------------------------------------------------------------------------------------------
// Function to be modified for drive migration
int16_t hyn_i2c_write_port(uint8_t addr, uint8_t* buf, uint16_t len)
{
    int16_t ret = -1;
    HYN_FUNC_ENTER;
    ret = _write_reg(addr, buf, len);
    // HYNITRON_DEBUG("iic_write_data RET %d 0x%x 0x%x",ret,*buf,*(buf+1));
    if (ret)
    {
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}
// Function to be modified for drive migration
int16_t hyn_i2c_read_port(uint8_t addr, uint8_t* buf, uint16_t len)
{
    int16_t ret = -1;
    HYN_FUNC_ENTER;
    ret = _read_reg(addr, buf, len);
    // HYNITRON_DEBUG("iic_read_data RET %d 0x%x 0x%x",ret,*buf,*(buf+1));
    if (ret)
    {
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}

/**
 * @brief iic写
 * @param  addr[in]:   从设备地址
 * @param  buf[0-1][in]:    寄存器地址
 * @param  buf[2-len][out]:    读到的数据
 * @param  len[in]:    长度
 * @return  0 : success  -1 : fail
*/
int16_t hyn_i2c_write(uint8_t addr, uint8_t* buf, uint16_t len)
{
    int16_t ret = 0;
    HYN_FUNC_ENTER;
    for (uint8_t i = 0;; i++)
    {
        if (i >= HYN_IIC_RETRY_NUM)
        {
            return -1;
        }
        ret = hyn_i2c_write_port(addr, buf, len);
        HYNITRON_DEBUG("iic_write_data:%d", ret);
        if (ret)
        {
            DELAY_US(200);
            continue;
        }
        break;
    }
    HYN_FUNC_EXIT;
    return 0;
}

/**
 * @brief iic读
 * @param  addr[in]:   从设备地址
 * @param  buf[in 0:1] [out 2:len]:    寄存器地址 + 数据 指针
 * @param  len[in]:    长度
 * @return  0 : success  -1 : fail
 * @note 传入参数buf有两重含义，首先是buf的前两个u8代表的是要读的寄存器的地址（16位）
 *       后面的len个字节是传出，函数内部会使buf指向读出的数据
*/
int16_t hyn_i2c_read(uint8_t addr, uint8_t* buf, uint16_t len)
{
    int16_t ret = 0;
    HYN_FUNC_ENTER;
    for (uint8_t i = 0;; i++)
    {
        if (i >= HYN_IIC_RETRY_NUM)
        {
            return -1;
        }
        ret = hyn_i2c_read_port(addr, buf, len);
        HYNITRON_DEBUG("iic_read_data:%d", ret);
        if (ret)
        {
            DELAY_US(200);
            continue;
        }
        break;
    }
    HYN_FUNC_EXIT;
    return 0;
}
//----------------------IIC转接适配




// Function to be modified for drive migration
// reset ic    拉低10ms RST脚 再拉高
void hyn_reset_ic(void)
{
    HYN_FUNC_ENTER;
    BSP_TP_Reset(0);
    DELAY_MS(10);
    BSP_TP_Reset(1);
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    p_g_chip_obj->esd_value = 0;
    p_g_chip_obj->esd_value_pre = 0;
}
// Function to be modified for drive migration
// 电源on/off
int16_t hyn_poweron_ic(bool on)
{
    HYN_FUNC_ENTER;
    if (on == false)
    {
        // set power pin low
        BSP_TP_PowerDown();
    }
    else
    {
        // set power pin high
        BSP_TP_PowerUp();
    }
    HYN_FUNC_EXIT;
    return 0;
}
/********************************************************/


void hyn_tp_esd_check(void)
{
    HYN_FUNC_ENTER;
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    p_g_chip_obj->esd_check();
    HYN_FUNC_EXIT;
}
void hyn_tp_suspend(void)
{
    int16_t ret = 0;
    HYN_FUNC_ENTER;
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    ret = p_g_chip_obj->enter_sleep(DEEP_SLEEP);
    if (ret)
    {
        HYNITRON_ERROR("enter_sleep failed");
    }
    HYN_FUNC_EXIT;
}
void hyn_tp_resume(void)
{
    int16_t ret = 0;
    HYN_FUNC_ENTER;
    if (!p_g_chip_obj)
    {
        HYNITRON_ERROR("p_g_chip_obj NULL return");
        return;
    }
    ret = p_g_chip_obj->wake_up();
    if (ret)
    {
        HYNITRON_ERROR("wake_up failed");
    }
    HYN_FUNC_EXIT;
}

#if (CONFIG_TP_FACTORY)

#define LOG_SIZE 5 * 1024 * 1024

int32_t hyn_get_file_size(FILE* stream)
{
    int32_t file_size = 0;
    int32_t cur_offset = ftell(stream);
    if (cur_offset == -1) {
        printf("ftell failed :%s\n", strerror(errno));
        return -1;
    }
    if (fseek(stream, 0, SEEK_END) != 0) {
        printf("fseek failed :%s\n", strerror(errno));
        return -1;
    }
    file_size = ftell(stream);
    if (file_size == -1) {
        printf("ftell failed: %s\n", strerror(errno));
    }
    if (fseek(stream, cur_offset, SEEK_SET) != 0) {
        printf("fseek failed: %s\n", strerror(errno));
        return -1;
    }
    return file_size;
}


int16_t get_debug_data(void)
{
    /**********************Catch debug information while interrupt pull down*********/
    int16_t raw_data[TRX_NUM + HYN_TX_NUM + HYN_RX_NUM + 1];
    const char* path = "/data/cst92xx_data.txt";
    int32_t file_length;
    int16_t ret = -1;
    uint8_t i2c_buf[4];
    uint16_t* read_buf;

    read_buf = (uint8_t*)raw_data;
    i2c_buf[0] = 0x10;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, (uint8_t*)read_buf, HYN_TX_NUM * HYN_RX_NUM << 1);
    if (ret)
    {
        HYNITRON_ERROR("hyn_get_debug_data 0x1000 error");
        return -1;
    }
    //selfCap
    read_buf += HYN_TX_NUM * HYN_RX_NUM << 1;
    i2c_buf[0] = 0x72;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, (uint8_t*)read_buf, HYN_TX_NUM << 1);
    if (ret)
    {
        HYNITRON_ERROR("hyn_get_debug_data 0x7200 error");
        return -1;
    }

    read_buf += HYN_TX_NUM << 1;
    i2c_buf[0] = 0x70;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, (uint8_t*)read_buf, HYN_RX_NUM << 1);
    if (ret)
    {
        HYNITRON_ERROR("hyn_get_debug_data 0x7000 error");
        return -1;
    }

    read_buf += HYN_RX_NUM << 1;
    i2c_buf[0] = 0x00;
    i2c_buf[1] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 2);
    ret |= p_g_chip_obj->i2c_read(HYNITRON_I2C_ADDR, (uint8_t*)read_buf, 2);
    if (ret)
    {
        HYNITRON_ERROR("hyn_get_debug_data 0x0000 error");
        return -1;
    }
    i2c_buf[0] = 0x00;
    i2c_buf[1] = 0x05;
    i2c_buf[2] = 0x00;
    ret = p_g_chip_obj->i2c_write(HYNITRON_I2C_ADDR, i2c_buf, 3);
    if (ret)
    {
        HYNITRON_ERROR("hyn_get_debug_data 0x000500 error");
        return -1;
    }
    //HYNITRON_DEBUG("-----Start record hybrid data!!!\r\n");
    FILE* file = fopen(path, "a");
    if (file == NULL) {
        HYNITRON_ERROR(" failed to open %s\n", path);
        return -1;
    }
    file_length = hyn_get_file_size(file);
    if (file_length > LOG_SIZE) {
        HYNITRON_ERROR("get file size oversize = %u\n", file_length);
        ret = unlink(path);
        if (ret != 0) {
            HYNITRON_ERROR(" failed to remove %s\n", path);
        }
    }
    else if (file_length == 0) {
        HYNITRON_ERROR("start save data,get file size = %u\n", file_length);
    }
    for (int i = 0; i < HYN_TX_NUM; i++) {
        for (int j = 0; j < HYN_RX_NUM; j++)
        {
            fprintf(file, "%d,", (uint16_t)raw_data[i * HYN_RX_NUM + j]);
        }
        fprintf(file, "%c", '\n');
    }

    for (int i = 0; i < HYN_TX_NUM; i++) {
        fprintf(file, "%d,", (uint16_t)raw_data[HYN_TX_NUM * HYN_RX_NUM + i]);
    }
    fprintf(file, "%c", '\n');

    for (int i = 0; i < HYN_RX_NUM; i++) {
        fprintf(file, "%d,", (uint16_t)raw_data[HYN_TX_NUM * HYN_RX_NUM + HYN_TX_NUM + i]);
    }
    fprintf(file, "%c", '\n');

    fprintf(file, "%d;,", (uint16_t)raw_data[HYN_TX_NUM * HYN_RX_NUM + HYN_RX_NUM + HYN_TX_NUM]);
    fprintf(file, "%c", '\n');

    fprintf(file, "%c", '\n');
    fclose(file);
    //HYNITRON_DEBUG("-----Finished record hybrid data!!!\r\n");


/*******************Catch debug information while interrupt pull down************/
    return OK;
}
#endif


//触摸上报函数
void hyn_worker_process(void)
{
    int16_t ret = 0;

    HYN_FUNC_ENTER;
    p_g_chip_obj->status.int_trig = 1;//外部的中断中触发，故将int_trig在这里置为1，本来应该在中断中的
#if (CONFIG_TP_FACTORY)
    if ((p_g_chip_obj->chip_ic_workmode == ENUM_MODE_DEBUG_RAWDATA) && (p_g_chip_obj->status.sleep_done == 0))
    {
        get_debug_data();
        //clear watch dog 
    }
#endif
    //
    if (p_g_chip_obj->chip_ic_workmode != ENUM_MODE_NORMAL)
    {
        HYNITRON_ERROR("hyn_worker_process chip_ic_workmode %d error", p_g_chip_obj->chip_ic_workmode);
        return;
    }
    ret = p_g_chip_obj->read_point();
    if (ret)
    {
        HYNITRON_ERROR("read_point failed");
        p_g_chip_obj->status.int_trig = 0;
        return;
    }
    p_g_chip_obj->status.int_trig = 0;
    // LCD OFF--detect gesture  手势检测
    if (p_g_chip_obj->status.sleep_done > 0)
    {
        HYNITRON_DEBUG("LCD OFF--detect gesture :%d", p_g_chip_obj->status.sleep_done);
        if (p_g_chip_obj->touch_info.gesture)
        {
            uint8_t gestrue_report = 0;
            switch (p_g_chip_obj->touch_info.gesture)
            {
            case SINGLE_CLICK:
            case DOUBLE_CLICK:
            case SLIDE_UP:
            case SLIDE_DOWN:
            case SLIDE_LEFT:
            case SLIDE_RIGHT:
                gestrue_report = 1;
                break;
            default:
                gestrue_report = 0;
                break;
            }
            if (gestrue_report)
            {
                HYNITRON_DEBUG("report gesture:%d", p_g_chip_obj->touch_info.gesture);
                {
                    /*************report gesture process***************/
                }
            }
            else
            {
                HYNITRON_DEBUG("gesture no valid:%d", p_g_chip_obj->touch_info.gesture);
            }
        }
        return;
    }
    else
    {
        HYNITRON_DEBUG("LCD ON---to report point and palm");
        // LCD ON---to report point and palm
        if (p_g_chip_obj->touch_info.palm)
        {
            HYNITRON_DEBUG("report palm:%d", p_g_chip_obj->touch_info.palm);
            {
                /*************report palm process,need report palm and point at the same time***************/
            }
        }

        if (p_g_chip_obj->touch_info.finger_num)
        {
            HYNITRON_DEBUG("finger num:%d", p_g_chip_obj->touch_info.finger_num);
            HYNITRON_DEBUG("report id:%d", p_g_chip_obj->point_info[0].finger_id);
            HYNITRON_DEBUG("valid :%d", p_g_chip_obj->point_info[0].valid);
            HYNITRON_DEBUG("report evt:%d", p_g_chip_obj->point_info[0].evt);
            HYNITRON_DEBUG("report x:%d", p_g_chip_obj->point_info[0].x);
            HYNITRON_DEBUG("report y:%d", p_g_chip_obj->point_info[0].y);

            if (p_g_chip_obj->point_info[0].evt == 0)
            {
                // touch up
            }
            else if (p_g_chip_obj->point_info[0].evt == 6)
            {
                // touch down
            }
            else if (p_g_chip_obj->point_info[0].evt == 7)
            {
                // touch move
            }
        }
        else
        {
            // report all release
        }
    }
    HYN_FUNC_EXIT;
}

void hyn_tp_irq_handler(void)
{
    HYN_FUNC_ENTER;
    p_g_chip_obj->status.int_trig = 1;
    hyn_worker_process();
    HYN_FUNC_EXIT;
}

// if Divided into multiple times Update Firmware, need edit.
//p_g_chip_obj->bin_data.data:  the header pointer of the data_seq packet(The packet length is data_len)
int16_t hyn_get_bin_addr(uint8_t data_seq, uint16_t data_len)
{
    HYN_FUNC_ENTER;
    if (p_g_chip_obj->bin_data.head_data == NULL)
    {
        // GET firmware bin data piont
        HYNITRON_ERROR("hyn_get_bin_addr data NULL or len error return");
        return -1;
    }
    p_g_chip_obj->bin_data.data = (uint8_t*)p_g_chip_obj->bin_data.head_data + (data_seq * data_len);
    // HYNITRON_DEBUG("get_bin_addr data_seq:0x%04x,data_len:0x%04x.data_point:0x%04x",data_seq,data_len,(data_seq*data_len));
    if ((p_g_chip_obj->bin_data.data == NULL) || ((data_seq * data_len) > MEM_SIZE))
    {
        HYNITRON_ERROR("hyn_get_bin_addr data NULL or len error return");
        return -1;
    }
    HYN_FUNC_EXIT;
    return 0;
}

int16_t hyn_check_ic(void)
{
    int16_t ret = -1;
    uint8_t retry;
    uint8_t buf[2];

    HYN_FUNC_ENTER;
    buf[0] = 0xD0;
    buf[1] = 0x00;
    for (retry = 10; retry > 0; retry--)
    {
        ret = hyn_i2c_write_port(HYNITRON_I2C_ADDR, buf, 2);
        if (ret)
        {
            HYNITRON_ERROR("test iic retry: %d", retry);
            DELAY_MS(2);
            continue;
        }
        else
        {
            break;
        }
    }
    if (ret)
    {
        HYNITRON_ERROR("hyn_check_ic fail: %d", retry);
        return -1;
    }
    HYNITRON_DEBUG("hyn_check_ic OK");
    return 0;
}

//检查固件是否需要升级
int16_t hyn_cst92xx_update(void)
{
    HYN_FUNC_ENTER;
#if (HYNITRON_ENABLE_UPGRADE == 1)
    {
        uint8_t need_upgrade = 0;
        int16_t ret = -1;

        //define h file array pointer,if bin file need to edit hyn_get_bin_addr
        p_g_chip_obj->bin_data.head_data = (uint8_t*)fw_data;

        ret = p_g_chip_obj->bin_firmware_parse();
        if (ret < 0)
        {
            HYNITRON_ERROR("bin_firmware_parse fail.");
            goto END_UPGRADE;
        }
        ret = p_g_chip_obj->upgrade_firmware_judge();
        if (ret)
        {
            HYNITRON_DEBUG("upgrade_firmware_judge return,no need update fw.");
            goto END_UPGRADE;
        }
        else
        {
            need_upgrade = 1;
        }
        HYNITRON_DEBUG("need_upgrade=%d, firmware_version=0x%04X.", need_upgrade, p_g_chip_obj->bin_data.version);
        if (need_upgrade)
        {
            ret = p_g_chip_obj->upgrade_firmware();
            if (ret)
            {
                HYNITRON_ERROR("upgrade_firmware failed");
                goto END_UPGRADE;
            }
            HYNITRON_DEBUG("upgrade_firmware OK done.");
            p_g_chip_obj->reset_ic();
            DELAY_MS(40);
            ret = p_g_chip_obj->get_firmware_info();
            if (ret)
            {
                HYNITRON_ERROR("get_firmware_info failed");
                p_g_chip_obj->reset_ic();
            }
        }
        return 0;

    END_UPGRADE:
        p_g_chip_obj->reset_ic();
    }
#endif
    HYN_FUNC_EXIT;
    return -1;
}


int16_t hyn_tp_init(void)
{
    // 
    /* get i2c bus device */
    ft_bus = (struct rt_i2c_bus_device*)rt_device_find(TOUCH_DEVICE_NAME);
    if (RT_Device_Class_I2CBUS != ft_bus->parent.type)
    {
        ft_bus = NULL;
    }
    if (ft_bus)
    {
        rt_device_open((rt_device_t)ft_bus, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_INT_TX | RT_DEVICE_FLAG_INT_RX);
    }
    else
    {
        LOG_I("bus not find\n");
        return RT_FALSE;
    }

    {
        struct rt_i2c_configuration configuration =
        {
            .mode = 0,
            .addr = 0,
            .timeout = 500,
            .max_hz = 400000,
        };

        rt_i2c_configure(ft_bus, &configuration);
    }

    int16_t ret = 0;

    HYNITRON_DEBUG("%s:start driver init.", HYNITRON_DRIVER_VERSION);
    memset(&g_chip_obj, 0, sizeof(g_chip_obj));
    p_g_chip_obj = &g_chip_obj;
    g_init_chip_obj[0](&g_chip_obj);//调用 void hyn_cst92xx_init_obj(struct hyn_chip *chip)  参数chip = &g_chip_obj
    p_g_chip_obj->reset_ic = hyn_reset_ic;
    p_g_chip_obj->poweron_ic = hyn_poweron_ic;
    p_g_chip_obj->i2c_write = hyn_i2c_write;
    p_g_chip_obj->i2c_read = hyn_i2c_read;
    p_g_chip_obj->get_bin_addr = hyn_get_bin_addr;
    p_g_chip_obj->status.ic_init_done = 0;
    p_g_chip_obj->poweron_ic(true);
    // ret = p_g_chip_obj->enter_boot();
    // if (ret)
    // {
    //     p_g_chip_obj->reset_ic();
    //     HYNITRON_ERROR("enter_boot fail,error return");
    //     return -1;
    // }
    // ret = p_g_chip_obj->read_chip_id();
    // if (ret)
    // {
    //     p_g_chip_obj->reset_ic();
    //     HYNITRON_ERROR("read_chip_id fail,error return");
    //     return -1;
    // }
    p_g_chip_obj->reset_ic();
    DELAY_MS(HYN_RESET_DELAY_TIME);
    // ret = p_g_chip_obj->get_firmware_info();
    // if (ret)
    // {
    //     HYNITRON_ERROR("get_firmware_info fail");
    // }
    // ret = hyn_cst92xx_update();
    // if (ret)
    // {
    //     HYNITRON_ERROR("hyn_cst92xx_update fail");
    // }
    p_g_chip_obj->chip_ic_workmode = ENUM_MODE_NORMAL;
    p_g_chip_obj->status.ic_init_done = 1;
    p_g_chip_obj->status.esd_enable = true;
    HYNITRON_DEBUG("hyn_tp_init*************DONE*************");
    return 0;
}

#if (CONFIG_TP_FACTORY)
static int cst92xx_control(int cmd, unsigned long arg)
{
    uint32_t* ptr = (uint32_t*)((uintptr_t)arg);
    int ret = 0;

    /*process the IOCTL by command*/
    switch (cmd) {
    case TSIOC_SETCALIB: /*arg: Pointer to int calibration value*/
        break;

    case TSIOC_GETCALIB: /*arg: Pointer to int calibration value*/
        break;

    case TSIOC_SETFREQUENCY: /*arg: Pointer to uint32_t frequency*/
        break;

    case TSIOC_GETFREQUENCY: /*arg: Pointer to uint32_t frequency*/
        break;

    case TSIOC_GETFWVERSION: /*arg: Pointer to uint32_t fw version value*/
        break;
#ifdef CONFIG_PM
    case TSIOC_ENABLEGESTURE:
        break;
#endif
    case TSIOC_READID:
#if(HYNITRON_READ_INFO_FROM_FLASH)
        p_g_chip_obj->status.esd_enable = false;
        p_g_chip_obj->enter_boot();
        p_g_chip_obj->read_chip_id();
        p_g_chip_obj->reset_ic();
        DELAY_MS(HYN_RESET_DELAY_TIME);
        p_g_chip_obj->status.esd_enable = true;
#endif
        * ptr = p_g_chip_obj->partno_chip_type;
        break;

    case TSIOC_GET_VERSION:
    {
#if(HYNITRON_READ_INFO_FROM_FLASH)
        p_g_chip_obj->status.esd_enable = false;
        DELAY_MS(5);
        if (p_g_chip_obj->get_firmware_info())
        {
            HYNITRON_ERROR("TSIOC_GET_VERSION get_firmware_info error");
            p_g_chip_obj->set_work_mode(ENUM_MODE_NORMAL);
        }
        p_g_chip_obj->status.esd_enable = true;
#endif
        * ptr = p_g_chip_obj->IC_firmware.firmware_version;
        break;
    }
    case TSIOC_GET_HWVERSION:
#if(HYNITRON_READ_INFO_FROM_FLASH)
        p_g_chip_obj->status.esd_enable = false;
        p_g_chip_obj->enter_boot();
        p_g_chip_obj->read_chip_id();
        p_g_chip_obj->reset_ic();
        DELAY_MS(HYN_RESET_DELAY_TIME);
        p_g_chip_obj->status.esd_enable = true;
#endif
        * ptr = p_g_chip_obj->module_id;
        break;
#if (CONFIG_TP_FACTORY)
    case TSIOC_GET_CM:
    {
        uint8_t* buf = (uint8_t*)((uintptr_t)arg);
        p_g_chip_obj->status.esd_enable = false;
        DELAY_MS(5);
        ret = g_chip_obj->get_factory_test_result(buf, TRX_NUM, 1);
        if (ret < 0) {
            HYNITRON_ERROR("get_factory_test_result ERROR\n");
        }
        p_g_chip_obj->status.esd_enable = true;
    }
    break;
    case TSIOC_GET_SHORT:
    {
        uint8_t* buf = (uint8_t*)((uintptr_t)arg);
        p_g_chip_obj->status.esd_enable = false;
        ret = g_chip_obj->get_factory_test_result(buf, TRX_KEY_NUM, 0);
        if (ret < 0) {
            HYNITRON_ERROR("get_factory_test_result ERROR\n");
        }
        p_g_chip_obj->status.esd_enable = true;
    }
    break;
    case TSIOC_GET_NODE_NUM:
        *ptr = TRX_NUM;
        break;
    case TSIOC_GET_NODE_SHORT_NUM:
        *ptr = TRX_KEY_NUM;
        break;
    case TSIOC_GET_X_CHANNEL_NUM:
        *ptr = p_g_chip_obj->IC_firmware.rx_num;
        break;

    case TSIOC_GET_Y_CHANNEL_NUM:
        *ptr = p_g_chip_obj->IC_firmware.tx_num;
        break;
        /******Add AT command control touch mode start *********/
    case TSIOC_HYBRID_MODE:
    {
        const char* path = "/data/cst92xx_data.txt";
        FILE* file = fopen(path, "w");
        if (file == NULL)
        {
            HYNITRON_ERROR("fopen error %s", path);
            return -1;
        }
        fclose(file);
        p_g_chip_obj->set_work_mode(EMUM_MODE_DEBUG_RAWDATA);
        break;
    }
    case TSIOC_POINT_MODE:
        p_g_chip_obj->set_work_mode(EMUM_MODE_NORMAL);
        break;
        /******Add AT command control touch mode end**********/
#endif

    case TSIOC_IDLE:
        hyn_tp_suspend();
        break;

    case TSIOC_WAKEUP:
        hyn_tp_resume();
        break;

    default:
        ret = -ENOTTY;
        break;
    }

    return ret;
}
#endif
