import rtconfig
Import('RTT_ROOT')
from building import *

# get current directory
cwd = GetCurrentDir()

try:
    if rtconfig.CORE=='LCPU':
         AddDepend('BF0_LCPU')
    elif rtconfig.CORE=='HCPU':
         AddDepend('BF0_HCPU')
except:
    if GetDepend("BF0_HCPU"):
        rtconfig.CORE='HCPU'
    elif Get<PERSON>end("BF0_LCPU"):
        rtconfig.CORE='LCPU'
    else:
        print('Warning: Rtconfig.core not defined')
     
src=['bf0_pin_const.c']
src += ['Templates/system_bf0_ap.c']
if GetDepend('SOC_SF32LB55X') and GetDepend(['BF0_HCPU']):
    if not GetDepend('BSP_CHIP_ID_COMPATIBLE'):
        if GetConfigValue('BSP_LB55X_CHIP_ID') >= 3:
            src += Glob('lcpu_a3_patch.c')
        else:
            src += Glob('lcpu_patch.c')
    else:
        src += Glob('lcpu_a3_patch.c')
        src += Glob('lcpu_patch.c')
    src += Glob('ble_rf_fulcal.c')

ASFLAGS = ''

if GetDepend(['BF0_HCPU']):
    if  rtconfig.PLATFORM != 'gcc':
        ASFLAGS = ' --cpreproc'
    if rtconfig.PLATFORM == 'gcc':
        src += ['Templates/gcc/startup_bf0_hcpu.s']
    elif rtconfig.PLATFORM == 'armcc':
        src += ['Templates/arm/startup_bf0_hcpu.S']
    elif rtconfig.PLATFORM == 'iar':
        src += ['Templates/iar/startup_bf0_hcpu.s']
elif GetDepend(['BF0_LCPU']):
    if  rtconfig.PLATFORM != 'gcc':
        ASFLAGS = ' --cpreproc'
    if rtconfig.PLATFORM == 'gcc':
        src += ['Templates/gcc/startup_bf0_lcpu.s']
        #src += ['Templates/gcc/startup_bf0_lcpu_rom.s']
    elif rtconfig.PLATFORM == 'armcc':
        src += ['Templates/arm/startup_bf0_lcpu.S']
        src += ['Templates/arm/startup_bf0_lcpu_rom.S']
    elif rtconfig.PLATFORM == 'iar':
        src += ['Templates/iar/startup_bf0_lcpu.s']
        src += ['Templates/iar/startup_bf0_lcpu_rom.s']       

path = [cwd, cwd + '/../Include']
path = path + [cwd + '/../../../external/CMSIS/Include']

if len(src) > 0:
    group = DefineGroup('CMSIS_BF0', src, depend = ['SOC_SF32LB55X'], ASFLAGS = ASFLAGS, CPPPATH=path)
else :
    group =[]

Return('group')

