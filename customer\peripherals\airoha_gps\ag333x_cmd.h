
/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ag333x_cmd.h
@Time    :   2025/02/27 13:44:13
*
**************************************************************************/
#ifndef __AG333X_H
#define __AG333X_H

#include <stdint.h>
#include "rtconfig.h"

#ifdef __cplusplus
extern "C" {
#endif
#if GPS_USING_AG333X
    const uint8_t GNSS_POWER_ON[] = "PAIR002";

    const uint8_t GNSS_POWER_OFF[] = "PAIR003";

    const uint8_t GNSS_NVRAM_SAVE_NAVIGATION_DATA[] = "PAIR511";

    const uint8_t GNSS_HOT_START[] = "PAIR004";

    const uint8_t GNSS_WARM_START[] = "PAIR005";

    const uint8_t GNSS_COLD_START[] = "PAIR006";

    const uint8_t GNSS_STATUS_GET[] = "PAIR008";

    const uint8_t GNSS_AIDING_REQUEST[] = "PAIR010";

    const uint8_t SYSTEM_REBOOT[] = "PAIR023";

    const uint8_t NEMA_4_1[] = "PAIR100,1,0";

    //NMEA报文
    const uint8_t GxGSV_ON[] = "PAIR062,3,1";
    const uint8_t GxGSV_OFF[] = "PAIR062,3,0";

    const uint8_t GxGGA_ON[] = "PAIR062,0,1";
    const uint8_t GxGGA_OFF[] = "PAIR062,0,0";

    const uint8_t GxGLL_ON[] = "PAIR062,1,1";
    const uint8_t GxGLL_OFF[] = "PAIR062,1,0";

    const uint8_t GxGSA_ON[] = "PAIR062,2,1";
    const uint8_t GxGSA_OFF[] = "PAIR062,2,0";

    const uint8_t GxRMC_ON[] = "PAIR062,4,1";
    const uint8_t GxRMC_OFF[] = "PAIR062,4,0";

    const uint8_t GxVTG_ON[] = "PAIR062,5,1";
    const uint8_t GxVTG_OFF[] = "PAIR062,5,0";

    //卫星模式
    /**************************/
    //$PAIR066, <GPS_Enabled>, <GLONASS_Enabled>, <Galileo_Enabled>, <BeiDou_Enabled>, <QZSS_Enabled>, <NavIC_Enabled>* CS<CR><LF>
    //    "0", disable
    //    "1", search
    /***************************/

    //GPS+GLONASS+galileo+beidou+QZSS
    const uint8_t GPS_BD_GL_GA_QZSS_ON[] = "PAIR066,1,1,1,1,1,0";

    //GPS+galileo+QZSS+beidou
    const uint8_t GPS_GA_QZSS_BEIDOU_ON[] = "PAIR066,1,0,1,1,1,0";

    //GPS+galileo+QZSS
    const uint8_t GPS_BD_GA_QZSS_ON[] = "PAIR066,1,0,1,0,1,0";

    //GPS+QZSS+beidou
    const uint8_t GPS_GA_QZSS_ON[] = "PAIR066,1,0,0,1,1,0";

    //GPS+GLONASS+galileo
    const uint8_t GPS_GLONASS_GA_ON[] = "PAIR066,1,1,1,0,0,0";

    //GPS+galileo
    const uint8_t GPS_GL_ON[] = "PAIR066,1,0,1,0,0,0";

    //GPS+beidou
    const uint8_t GPS_BD_ON[] = "PAIR066,1,0,0,1,0,0";

    //GPS
    const uint8_t GPS_ON[] = "PAIR066,1,0,0,0,0,0";

    //beidou
    const uint8_t BD_ON[] = "PAIR066,0,0,0,1,0,0";

    //GPS+galileo+beidou
    const uint8_t GPS_BD_GA_ON[] = "PAIR066,1,0,1,1,0,0";

    //GPS+galileo
    const uint8_t GPS_GA_ON[] = "PAIR066,1,0,1,0,0,0";

    //SBAS ON 健身和游泳模式无效
    const uint8_t SBAS_ON[] = "PAIR410,1";

    //SBAS OFF
    const uint8_t SBAS_OFF[] = "PAIR410,0";

    //运动模式,正常模式 PAIR_COMMON_SET_NAVIGATION_MODE
    const uint8_t NAVIGATION_MODE[] = "PAIR080,0";

    //运动模式,bike模式 PAIR_COMMON_SET_NAVIGATION_MODE
    const uint8_t NAVIGATION_MODE_BIKE[] = "PAIR080,9";

    //运动模式,健身模式 PAIR_COMMON_SET_NAVIGATION_MODE
    const uint8_t NAVIGATION_MODE_FITNESS[] = "PAIR080,1";

    //运动模式,swim模式 PAIR_COMMON_SET_NAVIGATION_MODE
    const uint8_t NAVIGATION_MODE_SWIMMING[] = "PAIR080,7";

    //低功耗模式打开
    const uint8_t PAIR_ALP_ENABLE[] = "PAIR732,2";

    //低功耗模式关闭
    const uint8_t PAIR_ALP_DISABLE[] = "PAIR732,0";

    // 主动干扰消除打开
    const uint8_t AIC_ON[] = "PAIR074,1";

    // 主动干扰消除关闭
    const uint8_t AIC_OFF[] = "PAIR074,0";

    // 正常模式
    const uint8_t PWR_NORMAL[] = "PAIR106,0";

    // 性能模式
    const uint8_t PWR_HIGH_CPU[] = "PAIR106,1";

    //单GPS
    //GPS 8~16 SBAS 1~3 QZSS 0~3
    // const uint8_t GPS_ON[] = {};

    //单北斗
    // const uint8_t BEIDOU_ON[] = {};

    // const uint8_t GPS_GA_ON[] = { };

    // const uint8_t GPS_BD_GA_QZSS_SBAS_ON[] = { };

    // const uint8_t GPS_GA_SBAS_ON[] = {};

    // const uint8_t GPS_BD_SBAS_ON[] = {};

    // const uint8_t GPS_GA_QZSS_SBAS_ON[] = {};

    // const uint8_t GPS_BD_GAL_SBAS_ON[] = {};

    // const uint8_t GPS_GLONASS_GA_SBAS_ON[] = {};

    //获取卫星使用情况L1+L5两个通道情况 PAIR_COMMON_GET_GNSS_SATS_USED
    const uint8_t GNSS_SATS_USED_GET[] = "PAIR032,0";

    const uint8_t GNSS_EPO_STATUS[] = "PAIR470,0";

    const uint8_t GNSS_EPO_CLEAR[] = "PAIR472";

    const uint8_t GNSS_RTC_MODE[] = "PAIR650,0";

    const uint8_t GNSS_GET_REF_UTC[] = "PAIR591";

    const uint8_t GNSS_DCB_ENABLE_OUTPUT[] = "PAIR380,1";

    const uint8_t GNSS_IMMEDIATE_SPEED_MODE[] = "PAIR160,1";

    const uint8_t GNSS_EPOC_PREDICT_ENABLE[] = "PAIR496,1";

    const uint8_t GNSS_EPOC_PREDICT_DISABLE[] = "PAIR496,0";

    const uint8_t GNSS_EPOC_MODE_NORMAL[] = "PAIR497,1";

    const uint8_t GNSS_EPOC_CONFIG[] = "PAIR498,1";

    const uint8_t GNSS_EPOC_GET_STATUS[] = "PAIR508";

    const uint8_t GNSS_EPOC_SAVE_CONFIG[] = "PAIR513";

    const uint8_t GNSS_EPOC_NAVIDATA_AUTO_SAVE[] = "PAIR510,1";

    const uint8_t GNSS_EPOC_NAVIDATA_AUTO_SAVE_DISABLE[] = "PAIR510,0";

    const uint8_t GNSS_EPOC_NAVIDATA_SAVE[] = "PAIR511";

    const uint8_t GNSS_EPOC_CLEAR_NAVIDATA_DATA[] = "PAIR512";    
    
    const uint8_t GNSS_DEBUG_LOG_ENABLE[] = "PAIR086,1";

    const uint8_t GNSS_DEBUG_LOG_DISABLE[] = "PAIR086,0";

    const uint8_t GNSS_PERIODIC_SET_MODE[] = "PAIR690,1,5,20,60,120";

    const uint8_t GNSS_PERIODIC_MODE_CANCEL[] = "PAIR690,0";

    const uint8_t GNSS_PERIODIC_GET_MODE[] = "PAIR691";

    const uint8_t GNSS_TIME_SET_REF_UTC[] = "PAIR590,";

    const uint8_t GNSS_LOC_SET_REF[] = "PAIR600,";

    //开启双频
    const uint8_t GNSS_SET_DUAL_BAND_EN[] = "PAIR104,1";

    //关闭双频
    const uint8_t GNSS_SET_DUAL_BAND_DIS[] = "PAIR104,0";

    //获取双频状态
    const uint8_t GNSS_GET_DUAL_BAND[] = "PAIR105";

    //修改导航模式
    const uint8_t GNSS_SET_NAVIGATION_MODE[] = "PAIR080,";

    //查询导航模式
    const uint8_t GNSS_GET_NAVIGATION_MODE[] = "PAIR081";

    //使能ULP模式
    const uint8_t GNSS_ULP_ENABLE_EN[] = "PAIR700,1";

    //关闭ULP模式
    const uint8_t GNSS_ULP_ENABLE_DIS[] = "PAIR700,0";
    
    //查询ULP模式
    const uint8_t GNSS_ULP_GET_STATUS[] = "PAIR701";

    //lock sleep
    const uint8_t GNSS_LOCK_SYSTEM_SLEEP_EN[] = "PAIR382,1";
    
    //unlock sleep
    const uint8_t GNSS_LOCK_SYSTEM_SLEEP_DIS[] = "PAIR382,0";
#endif
#ifdef __cplusplus
}
#endif


#endif 