/**
  ******************************************************************************
  * @file   gc9b71.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the rm69090.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON>li nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __GC9B71_H
#define __GC9B71_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup GC9B71
  * @{
  */

/** @defgroup GC9B71_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup GC9B71_Exported_Constants
  * @{
  */

/**
  * @brief GC9B71 chip IDs
  */
#define GC9B71_ID                  0x1190a7

/**
  * @brief  GC9B71 Size
  */
#define  GC9B71_LCD_PIXEL_WIDTH    (320)
#define  GC9B71_LCD_PIXEL_HEIGHT   (390)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define GC9B71_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define GC9B71_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define GC9B71_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  GC9B71 Registers
  */
//#define GC9B71_SW_RESET           0x01
#define GC9B71_LCD_ID             0xA1
//#define GC9B71_DSI_ERR            0x05
#define GC9B71_POWER_MODE         0x0A
#define GC9B71_SLEEP_IN           0x10
#define GC9B71_SLEEP_OUT          0x11
//#define GC9B71_PARTIAL_DISPLAY    0x12
//#define GC9B71_DISPLAY_INVERSION  0x21
#define GC9B71_DISPLAY_OFF        0x28
#define GC9B71_DISPLAY_ON         0x29
#define GC9B71_WRITE_RAM          0x2C
#define GC9B71_READ_RAM           0x2E
#define GC9B71_CASET              0x2A
#define GC9B71_RASET              0x2B
//#define GC9B71_PART_CASET              0x30
//#define GC9B71_PART_RASET              0x31
//#define GC9B71_VSCRDEF            0x33 /* Vertical Scroll Definition */
//#define GC9B71_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define GC9B71_TEARING_EFFECT     0x35
#define GC9B71_MACTL     0x36
//#define GC9B71_IDLE_MODE_OFF      0x38
//#define GC9B71_IDLE_MODE_ON       0x39
#define GC9B71_COLOR_MODE         0x3A
#define GC9B71_CONTINUE_WRITE_RAM 0x3C
#define GC9B71_WBRIGHT            0x51 /* Write brightness*/
//#define GC9B71_RBRIGHT            0x53 /* Read brightness*/
//#define GC9B71_PORCH_CTRL         0xB2
//#define GC9B71_FRAME_CTRL         0xB3
//#define GC9B71_GATE_CTRL          0xB7
//#define GC9B71_VCOM_SET           0xBB
//#define GC9B71_LCM_CTRL           0xC0
//#define GC9B71_SET_TIME_SRC       0xC2
//#define GC9B71_SET_DISP_MODE      0xC4
//#define GC9B71_VCOMH_OFFSET_SET   0xC5
//#define GC9B71_FR_CTRL            0xC6
//#define GC9B71_POWER_CTRL         0xD0
//#define GC9B71_PV_GAMMA_CTRL      0xE0
//#define GC9B71_NV_GAMMA_CTRL      0xE1
//#define GC9B71_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup GC9B71_Exported_Functions
  * @{
  */
void     GC9B71_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t GC9B71_ReadID(LCDC_HandleTypeDef *hlcdc);

void     GC9B71_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     GC9B71_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void GC9B71_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void GC9B71_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void GC9B71_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t GC9B71_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void GC9B71_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void GC9B71_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);
void GC9B71_Rotate(LCDC_HandleTypeDef *hlcdc, LCD_DrvRotateTypeDef degree);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __GC9B71_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/

