/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503_reg.h
@Time    :   2025/02/13 20:31:13
@Brief   :   光旋钮寄存器驱动模块
@Details :
*
************************************************************/

#ifndef __MT3503_REG_H__
#define __MT3503_REG_H__

#ifdef __cplusplus
extern "C" {
#endif


#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <math.h>

//--------------------------大小端-------------------------
#define DRV_LITTLE_ENDIAN  1234
#define DRV_BIG_ENDIAN     4321
#define DRV_BYTE_ORDER     DRV_LITTLE_ENDIAN


    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
        uint8_t bit0 : 1;
        uint8_t bit1 : 1;
        uint8_t bit2 : 1;
        uint8_t bit3 : 1;
        uint8_t bit4 : 1;
        uint8_t bit5 : 1;
        uint8_t bit6 : 1;
        uint8_t bit7 : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
        uint8_t bit7 : 1;
        uint8_t bit6 : 1;
        uint8_t bit5 : 1;
        uint8_t bit4 : 1;
        uint8_t bit3 : 1;
        uint8_t bit2 : 1;
        uint8_t bit1 : 1;
        uint8_t bit0 : 1;
#endif /* DRV_BYTE_ORDER */
    } bitwise_t;

          /** I2C Device Address 7 bit format **/
#define MT3503_I2C_ADD_H              0x73U
#define MT3503_I2C_ADD_L              0x75U
#define MT3503_I2C_ADD_F              0x79U

#define MT3503_PID1                     0x41U
#define MT3503_PID2                     0xB0U
#define MT3503_VID                     0x02U


//----------------------------------------------------------
    typedef int32_t(*stmdev_write_ptr)(void*, uint8_t, const uint8_t*, uint16_t);
    typedef int32_t(*stmdev_read_ptr)(void*, uint8_t, uint8_t*, uint16_t);
    typedef void (*stmdev_mdelay_ptr)(uint32_t millisec);

    typedef struct
    {
        /** Component mandatory fields **/
        stmdev_write_ptr  write_reg;
        stmdev_read_ptr   read_reg;
        /** Component optional fields **/
        stmdev_mdelay_ptr   mdelay;
        /** Customizable optional pointer **/
        void* handle;
    } stmdev_ctx_t;




#define MT3503_Product_ID1                    0x00U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t pid : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t pid : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_product_id1_t;

#define MT3503_Product_ID2                    0x01U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t vid : 4;
    uint8_t pid : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t pid : 4;
    uint8_t vid : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_product_id2_t;

#define MT3503_Motion_Status                0x02U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t not_used_02 : 3;
    uint8_t dxovf : 1;
    uint8_t dyovf : 1;
    uint8_t not_used_01 : 2;
    uint8_t motion : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t motion : 1;
    uint8_t not_used_01 : 2;
    uint8_t dyovf : 1;
    uint8_t dxovf : 1;
    uint8_t not_used_02 : 3;
#endif /* DRV_BYTE_ORDER */
    }mt3503_motion_status_t;

#define MT3503_Delta_X_Lo                    0x03U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t delta_x_lo : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t delta_x_lo : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_delta_x_lo_t;

#define MT3503_Delta_Y_Lo                    0x04U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t delta_y_lo : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t delta_y_lo : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_delta_y_lo_t;

#define MT3503_Operation_Mode                   0x05U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t wakeup : 1;
    uint8_t slp1mu : 1;
    uint8_t slp2mu : 1;
    uint8_t slp2_en : 1;
    uint8_t slp_en : 1;
    uint8_t not_used_01 : 3;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t not_used_01 : 3;
    uint8_t slp_en : 1;
    uint8_t slp2_en : 1;
    uint8_t slp2mu : 1;
    uint8_t slp1mu : 1;
    uint8_t wakeup : 1;
#endif /* DRV_BYTE_ORDER */
    }mt3503_operation_mode_t;

#define MT3503_Configuration                    0x06U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t slp3_en : 1;
    uint8_t not_used_02 : 2;
    uint8_t pd_en : 1;
    uint8_t not_used_01 : 3;
    uint8_t reset : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t reset : 1;
    uint8_t not_used_01 : 3;
    uint8_t pd_en : 1;
    uint8_t not_used_02 : 2;
    uint8_t slp3_en : 1;
#endif /* DRV_BYTE_ORDER */
    }mt3503_configuration_t;

#define MT3503_Write_Protect                    0x09U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t wp : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t wp : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_write_protect_t;

#define MT3503_Sleep1                    0x0AU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t slp1_etm : 4;
    uint8_t slp1_freq : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t slp1_freq : 4;
    uint8_t slp1_etm : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_sleep1_t;

#define MT3503_Sleep2                    0x0BU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t slp2_etm : 4;
    uint8_t slp2_freq : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t slp2_freq : 4;
    uint8_t slp2_etm : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_sleep2_t;

#define MT3503_Sleep3                    0x0CU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t slp3_etm : 4;
    uint8_t slp3_freq : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t slp3_freq : 4;
    uint8_t slp3_etm : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_sleep3_t;


#define MT3503_RES_X_Lo                   0x0DU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t res_x : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t res_x : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_res_x_lo_t;

#define MT3503_RES_Y_Lo                   0x0EU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t res_y : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t res_y : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_res_y_lo_t;

#define MT3503_RES_XY_Hi                   0x0FU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t res_y_hi : 4;
    uint8_t res_x_hi : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t res_x_hi : 4;
    uint8_t res_y_hi : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_res_xy_hi_t;

#define MT3503_Delta_XY_Hi                   0x12U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t delta_y_hi : 4;
    uint8_t delta_x_hi : 4;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t delta_x_hi : 4;
    uint8_t delta_y_hi : 4;
#endif /* DRV_BYTE_ORDER */
    }mt3503_delta_xy_hi_t;

#define MT3503_FrameAvg                   0x1AU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t favg : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t favg : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_frameAvg_t;

#define MT3503_Key_WP                   0x7FU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t key_wp : 8;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t key_wp : 8;
#endif /* DRV_BYTE_ORDER */
    }mt3503_key_wp_t;

#define MT3503_KEY_DET_CFG                   0x28U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t time : 2;
    uint8_t th : 1;
    uint8_t key_neg : 1;
    uint8_t key_pos : 1;
    uint8_t key_trig : 1;
    uint8_t key_hl : 1;
    uint8_t key_en : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t key_en : 1;
    uint8_t key_hl : 1;
    uint8_t key_trig : 1;
    uint8_t key_pos : 1;
    uint8_t key_neg : 1;
    uint8_t th : 1;
    uint8_t time : 2;
#endif /* DRV_BYTE_ORDER */
    }mt3503_key_det_cfg_t;

#define MT3503_KEY_INT_FLAG                   0x29U
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t key_int_en : 1;
    uint8_t key_clr : 1;
    uint8_t not_use_01 : 5;
    uint8_t key_flag : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t key_flag : 1;
    uint8_t not_use_01 : 5;
    uint8_t key_clr : 1;
    uint8_t key_int_en : 1;
#endif /* DRV_BYTE_ORDER */
    }mt3503_key_int_flag_t;

#define MT3503_KEY_STATUS                   0x2AU
    typedef struct
    {
#if DRV_BYTE_ORDER == DRV_LITTLE_ENDIAN
    uint8_t not_use_01 : 7;
    uint8_t key_status : 1;
#elif DRV_BYTE_ORDER == DRV_BIG_ENDIAN
    uint8_t key_status : 1;
    uint8_t not_use_01 : 7;
#endif /* DRV_BYTE_ORDER */
    }mt3503_key_status_t;

#ifndef __weak
#define __weak __attribute__((weak))
#endif /* __weak */


int32_t mt3503_read_reg(const stmdev_ctx_t* ctx, uint8_t reg,
                              uint8_t* data, uint16_t len);
int32_t mt3503_write_reg(const stmdev_ctx_t* ctx, uint8_t reg,
                               uint8_t* data, uint16_t len);


    typedef struct
    {
        uint8_t whoami;
    } mt3503_id_t;
    int32_t mt3503_id_get(const stmdev_ctx_t* ctx, mt3503_id_t* val);


    void mt3503_state_init(const stmdev_ctx_t* ctx);

    typedef struct
    {
        uint8_t is_motion : 1;
        uint8_t over_flow_y:1;
        uint8_t over_flow_x:1;
    }mt3503_v_motion_status_t;
    int32_t mt3503_motion_status_get(const stmdev_ctx_t* ctx, mt3503_v_motion_status_t* val);


    typedef struct
    {
        int16_t x;
        int16_t y;
        bool motion;
    }mt3503_delta_xy_t;

    int32_t mt3503_data_get(const stmdev_ctx_t* ctx, mt3503_delta_xy_t* val);

    typedef struct
    {
        uint8_t etm:4;
        uint8_t freq:4;
    } sleep_cfg_t;
    typedef struct
    {
        uint8_t level:4;
        uint8_t enanle:1;
    }sleep_enable_t;
    typedef struct
    {   bool change_cfg;
        sleep_enable_t sleep_enable;
        sleep_cfg_t sleep_cfg;
    }mt3503_sleep_cfg_t;
    /**设置各睡眠模式的激活状态和参数配置(参数仅在该睡眠模式enable时生效)
     * 采样率 =（采样倍数+1）* 采样步长
     * 进入时间 = （进入倍数+1） * 进入步长
     *
     *          采样倍数   采样步长   默认采样倍数   进入倍数  进入步长    默认进入倍数
     *sleep 1  （0-15）    4ms/次       15         （0-15）    32ms         7
     *sleep 2  （0-15）    64ms/次      3          （0-15）    20.48s       0
     *sleep 3  （0-15）    64ms/次      7          （0-15）    20.48s       0
     *
     * note：模式3 enable 前提是 模式2 enable，
     * note：模式2 enable 前提是 模式1 enable，
     * note：模式1 enable 无前提
     *
     * 默认模式1、2 enable
     *
     **/
    int32_t mt3503_sleep_mode_set(const stmdev_ctx_t* ctx, mt3503_sleep_cfg_t* val);



    typedef enum
    {
        MT3503_MODE_POWER_DOWN = 0,
        MT3503_MODE_SLEEP1,
        MT3503_MODE_SLEEP2,
        MT3503_MODE_WAKEUP,
        MT3503_MODE_RESET,
        MT3503_MODE_MAX,
    }mt3503_mode_e;

    int32_t mt3503_mode_enter(const stmdev_ctx_t* ctx, mt3503_mode_e val);

    typedef enum
    {
        MT3503_WP_DISABLE = 0x5AU,
        MT3503_WP_ENABLE = 0x00U,

    }mt3503_wp_e;
    typedef enum
    {
        MT3503_WP_TYPE_XY,
        MT3503_WP_TYPE_KEY,
    }mt3503_wp_type_e;
    typedef struct
    {
        mt3503_wp_type_e type;
        mt3503_wp_e state;
    }mt3503_wp_t;
    int32_t mt3503_Write_protect_set(const stmdev_ctx_t* ctx, mt3503_wp_t* val);

    int32_t mt3503_res_x_set(const stmdev_ctx_t* ctx, uint16_t val);

    int32_t mt3503_res_x_get(const stmdev_ctx_t* ctx, uint16_t *val);



#ifdef __cplusplus
}
#endif

#endif