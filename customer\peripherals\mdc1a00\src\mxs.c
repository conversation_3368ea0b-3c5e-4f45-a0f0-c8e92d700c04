/*
 * Copyright (c) 2019 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

 /*-----------------------------------------------------------------------------
  * HEARDER FILES
  *---------------------------------------------------------------------------*/
#include "mxs.h"

#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "mixo_hal_timer.h"
#include "mixo_log.h"
#include "mxs_algo.h"
#include "mxs_reg_access.h"

#ifdef CONFIG_MXS_USE_SPI
#include "mixo_hal_spi.h"
#include "board.h"
#endif

  /*-----------------------------------------------------------------------------
   * MACRO DEFINITIONS
   *---------------------------------------------------------------------------*/
   // macro definitions about the register of the chip
#define REG_ADDR_PRODUCT_ID_HIGH 0x00
#define REG_ADDR_PRODUCT_ID_LOW  0x01
#define REG_ADDR_MOTION_STATUS   0x02
#define REG_ADDR_DELTA_X         0x03
#define REG_ADDR_DELTA_Y         0x04
#define REG_ADDR_OPERATION_MODE  0x05
#define REG_ADDR_CONFIGURATION   0x06
#define REG_ADDR_WRITE_PROTECT   0x09
#define REG_ADDR_SLEEP1_SETTING  0x0A
#define REG_ADDR_SLEEP2_SETTING  0x0B
#define REG_ADDR_SLEEP3_SETTING  0x0C
#define REG_ADDR_CPI_X           0x0D
#define REG_ADDR_CPI_Y           0x0E
#define REG_ADDR_DELTA_XY_HIGH   0x12
#define REG_ADDR_IQC             0x13
#define REG_ADDR_SHUTTER         0x14
#define REG_ADDR_FRAME_AVG       0x17
#define REG_ADDR_SENSOR_OPTION   0x19
#define REG_ADDR_TWO_WIRED_SPI   0x46
#define REG_ADDR_READ_PROTECT    0x6D
#define REG_ADDR_VCSEL_CONTROL   0x7B
#define REG_ADDR_EXPOSURE_PERIOD 0x51
#define REG_ADDR_EXPOSURE_ROW    0x4D
#define REG_ADDR_EXPOSURE_COLUMN 0x4F
#define REG_ADDR_RESULT_EXP_PRD  0x21
#define REG_ADDR_RESULT_EXP_ROW  0x22
#define REG_ADDR_RESULT_EXP_COL  0x23

// macro definitions about reigster data
#define REG_WRITE_PROTECT_DISABLE 0xA5
#define REG_WRITE_PROTECT_ENABLE  0x00
#define REG_READ_PROTECT_DISABLE  0x39
#define REG_READ_PROTECT_ENABLE   0x19
#define REG_TWO_WIRED_SPI_ENABLE  0x34
#define REG_TWO_WIRED_SPI_DISABLE 0xB4
#define REG_12_BIT_MODE_ENABLE    0x04
#define REG_12_BIT_MODE_DISABLE   0xFB
#define REG_SWAP_XY_ENABLE        0x20
#define REG_SWAP_XY_DISABLE       0xDF
#define REG_INV_X_ENABLE          0x08
#define REG_INV_X_DISABLE         0xF7
#define REG_INV_Y_ENABLE          0x10
#define REG_INV_Y_DISABLE         0xEF
#define REG_CPI_DEFAULT           0x20
#define REG_SOFT_RESET            0x91

// Error codes
#define MXS_ERR_NONE              (0)
#define MXS_ERR_GENERAL           (-1)
#define MXS_ERR_INTERNAL          (-2)
#define MXS_ERR_NOT_IMPLEMENTED   (-3)
#define MXS_ERR_OUT_OF_MEMORY     (-4)
#define MXS_ERR_INVALID_PARAMETER (-5)
#define MXS_ERR_INVALID_FORMAT    (-6)
#define MXS_ERR_BUFFER_TOO_SMALL  (-7)
#define MXS_ERR_INVALID_VERSION   (-8)


//#define CONFIG_MXS_USE_SPI_2WIRE          // 两线SPI模式需要开启此宏
//#define CONFIG_MXS_USE_SPI_RESYNC         // 如果是两线SPI，需要开启此宏去做重同步机制


//旋钮停止检测计数阈值
#define MDC1A00_MOTION_DETECTED_COUNTER  20

//旋转增量检测间隔时间MS
#define MDC1A00_MOTION_DETECTED_INTERVAL  10

//x轴误触噪声阈值
#define MDC1A00_X_KEY_TOUCH_NOISE   1.0f

//x轴脉冲抑制阈值

//y轴按键误触限制参数，数值越大越防止按键动作误触发旋钮，但是降低灵敏度。
#define MDC1A00_Y_KEY_TOUCH_LIMIT  10.0f

//用户回调触发步进阈值
#define MDC1A00_MOTION_DETECTED_STEP  25

static mxs_t* mxs_dev = NULL;

//外部运动中断检测标志位
static bool motion_detected_flag = false;

//旋钮增量检测计数器
static uint8_t motion_detected_counter = 0;

static rt_timer_t mdc1a00_timer = RT_NULL;






//用户注册的回调
static void (*user_trigger_callback)(bool step) = RT_NULL;

#define MDC1A00_SPI_INT_PIN 70


/*-----------------------------------------------------------------------------
 * ENUM
 *---------------------------------------------------------------------------*/
 // 定义用户对表冠的触发动作
enum sensor_trigger
{
    SENSOR_TRIGGER_REST = 0,  // 静止
    SENSOR_TRIGGER_REVOLVE,   // 旋转
    SENSOR_TRIGGER_PRESS,     // 按压
    SENSOR_TRIGGER_RELEASE    // 释放
};

/*-----------------------------------------------------------------------------
 * DATA STRUCT
 *---------------------------------------------------------------------------*/
typedef struct
{
    uint8_t version;
} mxs_st;

/*-----------------------------------------------------------------------------
 * FUNCTIONS DEFINITION
 *---------------------------------------------------------------------------*/


static rt_thread_t mdc1a00_thread = RT_NULL;

/**
 * @brief 外部中断回调函数，检测移动检测传感器中断
 */
static void mdc1a00_int_callback(void)
{
    if (motion_detected_flag == false)
    {
        //关中断
        rt_pin_irq_enable(MDC1A00_SPI_INT_PIN, PIN_IRQ_DISABLE);
        motion_detected_flag = true;
        motion_detected_counter = MDC1A00_MOTION_DETECTED_COUNTER;
    }
}

// 状态机的四种状态
enum mdc1a00_state {
    STATE_IDLE,     // 空闲状态
    STATE_DETECT,   // 检测状态
    STATE_CALLBACK  // 回调状态
};

static enum mdc1a00_state current_state = STATE_IDLE; // 初始状态为IDLE

/**
 * @brief 定时运行的线程，检测移动检测传感器中断
 */
static void mdc1a00_thread_(void* parameter) {
    int32_t ret = 0;
    mxs_raw_data_t raw_data;
    //旋转增量值
    static int16_t mdc1a00_detected_dx = 0;
    static int16_t mdc1a00_detected_dy = 0;

    //上次旋转增量
    static int16_t last_dx = 0;
    // static int16_t last_dy = 0;

    while (1) {
        //线程间隔
        rt_thread_mdelay(MDC1A00_MOTION_DETECTED_INTERVAL);

        switch (current_state) 
        {
        // 空闲状态
        case STATE_IDLE:
            {
                // 如果外部中断触发了，切换到检测状态
                if (motion_detected_flag) {
                    current_state = STATE_DETECT;
                }
            }
            break;
        // 检测状态
        case STATE_DETECT:
            {
                // 获取原始数据
                ret = mxs_get_raw_data(mxs_dev, &raw_data);
                // 如果数据有效，累加增量值
                if (ret == 0 && (raw_data.status & 0x80))
                {
                    last_dx = mdc1a00_detected_dx;
                    //如果 |dx| 小于 |dy|*MDC1A00_Y_KEY_TOUCH_LIMIT
                    if ((abs(raw_data.dx) < abs(raw_data.dy) * MDC1A00_Y_KEY_TOUCH_LIMIT) &&
                        (abs(raw_data.dx) > MDC1A00_X_KEY_TOUCH_NOISE))
                    {
                        //过滤抖动和误触
                        mdc1a00_detected_dx += raw_data.dx;
                        motion_detected_counter = MDC1A00_MOTION_DETECTED_COUNTER;
                    }
                }
                // 如果数据无效，计数器减一
                else
                {
                    motion_detected_counter--;
                }

                //如果x增量值满足条件
                if ((mdc1a00_detected_dx > MDC1A00_MOTION_DETECTED_STEP) ||
                    (mdc1a00_detected_dx < (-1 * MDC1A00_MOTION_DETECTED_STEP)))
                {
                    
                    current_state = STATE_CALLBACK; // 切换到回调状态
                    break;
                }

                //中断开启进入空闲状态
                if (motion_detected_counter == 0)
                {
                    motion_detected_flag = false;
                    //开中断
                    rt_pin_irq_enable(MDC1A00_SPI_INT_PIN, PIN_IRQ_ENABLE);
                }

                current_state = STATE_IDLE;
            }
            break;
        // 回调状态
        case STATE_CALLBACK:
            {
                // 调用注册的回调函数
                if (user_trigger_callback != RT_NULL)
                {
                    rt_kprintf("mdc1a00_detected_dx:  %d\n", mdc1a00_detected_dx);
                    //user_trigger_callback(mdc1a00_detected_dx > 0);
                }
                mdc1a00_detected_dx = 0;
                current_state = STATE_IDLE; // 执行完回调，切换回空闲状态
            }
            break;
        default:
            // 默认情况，切换回空闲状态
            current_state = STATE_IDLE;
            break;
        }
    }
}


/**
 * @brief 显示所有的寄存器列表
 * */
static void mxs_chip_reg_lists(void)
{
    uint8_t tmp = 0;
    uint8_t reg_addr_lists[22] = { REG_ADDR_PRODUCT_ID_HIGH,
                                   REG_ADDR_PRODUCT_ID_LOW,
                                   REG_ADDR_OPERATION_MODE,
                                   REG_ADDR_CONFIGURATION,
                                   REG_ADDR_WRITE_PROTECT,
                                   REG_ADDR_SLEEP1_SETTING,
                                   REG_ADDR_SLEEP2_SETTING,
                                   REG_ADDR_SLEEP3_SETTING,
                                   REG_ADDR_CPI_X,
                                   REG_ADDR_CPI_Y,
                                   REG_ADDR_IQC,
                                   REG_ADDR_SHUTTER,
                                   REG_ADDR_FRAME_AVG,
                                   REG_ADDR_SENSOR_OPTION,
                                   REG_ADDR_TWO_WIRED_SPI,
                                   0x1D,
                                   0x7B,
                                   0x3C,
                                   0x3D,
                                   0x3E,
                                   0x70,
                                   0x69

    };
    char reg_name_lists[22][15] = {
        "product_id0\0",
        "product_id1\0",
        "operation_mode\0",
        "configuration\0",
        "write_protect\0",
        "sleep1_setting\0",
        "sleep2_setting\0",
        "sleep3_setting\0",
        "cpix\0",
        "cpiy\0",
        "iqc\0",
        "shutter\0",
        "frame_avg\0",
        "sensor_option\0",
        "2_wired_spi\0",
        "\0",
        "\0",
        "\0",
        "\0",
        "\0",
        "\0",
        "\0",
    };
    LOGD("registers:");

#ifdef CONFIG_MXS_CALLISTO
    for (int i = 0; i < 22; i++)
#else
    for (int i = 0; i < 15; i++)
#endif
    {
        mxs_reg_read(reg_addr_lists[i], &tmp);
        LOGD("- [0x%02X,%s,0x%02X]", reg_addr_lists[i], reg_name_lists[i], tmp);
    }
}

/**
 * @brief 接收1byte数据
 * @param[in] original_dx   低8bit的dx数据
 * @param[in] original_dy   低8bit的dy数据
 * @param[in] high_bit      高4bit的dx/dy数据
 * @param[out] dx           合成后的dx数据
 * @param[out] dx           合成后的dy数据
 * */
static void data_synthesis(uint8_t original_dx,
                           uint8_t original_dy,
                           uint8_t high_bit,
                           int16_t* dx,
                           int16_t* dy)
{
    // 高位数据处理
    int16_t dx_high = (high_bit << 4) & 0x0F00;
    int16_t dy_high = (high_bit << 8) & 0x0F00;

    // 如果第 12 bit 为1，则为负数
    if (dx_high & 0x0800)
    {
        dx_high |= 0xf000;
    }
    if (dy_high & 0x0800)
    {
        dy_high |= 0xf000;
    }

    // 数据合成
    *dx = -(dx_high | (int16_t)original_dx);
    *dy = -(dy_high | (int16_t)original_dy);
}

/**
 * @brief sensor 初始化
 *
 * @return 0即成功，其他即失败
 */
#ifdef CONFIG_MXS_CALLISTO
static int callisto_init()
{
    int ret = MXS_ERR_NONE;

    uint16_t chipid = 0;
    uint8_t data = 0;
    int times = 0;

    for (int i = 0; i < 10; i++)
    {
        chipid = 0x0000;
        times++;

#ifdef CONFIG_MXS_USE_SPI_RESYNC
        mixo_hal_spi_resync();
#else // CONFIG_MXS_USE_SPI, CONFIG_MXS_USE_I2C
        mxs_reg_write(REG_ADDR_CONFIGURATION, REG_SOFT_RESET);
#endif

        // 芯片写保护解锁
        ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_DISABLE);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }

        ret = mxs_reg_write(0x3C, 0xCC);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }

#ifdef CONFIG_MXS_USE_SPI_2WIRE
        ret = mxs_reg_write(REG_ADDR_TWO_WIRED_SPI, REG_TWO_WIRED_SPI_ENABLE);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }
#endif

        ret = mxs_reg_read(REG_ADDR_PRODUCT_ID_HIGH, &data);
        chipid |= (uint16_t)(data << 8);
        ret = mxs_reg_read(REG_ADDR_PRODUCT_ID_LOW, &data);
        chipid |= data;

        if (chipid != 0x585B)
        {
            times++;
        }
        else
        {
            break;
        }
    }

    if (times >= 10)
    {
        ret = MXS_ERR_NOT_IMPLEMENTED;
        goto ERROR;
    }

    ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_DISABLE);
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(REG_ADDR_READ_PROTECT, REG_READ_PROTECT_DISABLE);
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(0x1D, 0x04);
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(0x7B, 0xF8);  // max led current , enable shutter
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(0x3D, 0x0C);
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(0x3E, 0xF5);  // mot output, output to simulate
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    // reference voltage = 010, enhanced gpio power
    // of driver, enable ADC test enable signal
    ret = mxs_reg_write(0x70, 0x2A);
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

    ret = mxs_reg_write(0x69, 0x22);  // low freq clock, LDO voltage
    if (ret != 0)
    {
        ret = MXS_ERR_GENERAL;
        goto ERROR;
    }

ERROR:
    return ret;
}

#else
static int galileo_init(mxs_init_parameter_t* init_parameter)
{
    int ret = 0;
    uint16_t chipid = 0;
    uint8_t data = 0;
    int times = 0;

    for (int i = 0; i < 10; i++)
    {
        chipid = 0x0000;
        times++;

#ifdef CONFIG_MXS_USE_SPI_RESYNC
        mixo_hal_spi_resync();

        // 芯片 software reset
        mxs_reg_write(REG_ADDR_CONFIGURATION, REG_SOFT_RESET);

        // 芯片写保护解锁
        ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_DISABLE);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }
#endif

#ifdef CONFIG_MXS_USE_SPI_2WIRE
        ret = mxs_reg_write(REG_ADDR_TWO_WIRED_SPI, REG_TWO_WIRED_SPI_ENABLE);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }
#endif

#ifdef CONFIG_MXS_USE_SPI_RESYNC
        // 芯片写保护锁定
        ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_ENABLE);
        if (ret != 0)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }
#endif

        ret = mxs_reg_read(REG_ADDR_PRODUCT_ID_HIGH, &data);
        chipid |= (uint16_t)(data << 8);
        ret = mxs_reg_read(REG_ADDR_PRODUCT_ID_LOW, &data);
        chipid |= data;

        if (chipid != 0x5859)
        {
            rt_kprintf("chipid: 0x%04X\r\n", chipid);
            times++;
        }
        else
        {
            break;
        }
    }

    if (times >= 10)
    {
        ret = MXS_ERR_NOT_IMPLEMENTED;
        goto ERROR;
    }
    else
    {
        ret = MXS_ERR_NONE;
    }

ERROR:
    return ret;
}
#endif

mxs_t* mxs_init(mxs_init_parameter_t* init_parameter)
{
    int ret = 0;
    uint8_t tmp = 0;
    mxs_st* mxs_instance = NULL;
    LOGD("MXS init.---------");
    if (init_parameter == NULL)
    {
        LOGE("Invalid parameter!");
        goto ERROR;
    }

    mxs_instance = malloc(sizeof(mxs_st));
    if (mxs_instance == NULL)
    {
        ret = MXS_ERR_OUT_OF_MEMORY;
        LOGE("Failed to malloc for mxs_instance.");
        return NULL;
    }

    memset(mxs_instance, 0, sizeof(mxs_st));

    mxs_instance->version = 0x01;
    mxs_reg_init();

    //初始化中断引脚
    //rt_pin_mode(MDC1A00_SPI_INT_PIN, PIN_MODE_INPUT_PULLUP);
    //rt_pin_attach_irq(MDC1A00_SPI_INT_PIN, PIN_IRQ_MODE_FALLING, mdc1a00_int_callback, RT_NULL);

    //使能中断
    //rt_pin_irq_enable(MDC1A00_SPI_INT_PIN, PIN_IRQ_ENABLE);

    if (rt_thread_find("mdc1a00_thread") != RT_NULL)
    {
        goto ERROR;
    }
    //创建一个线程
    rt_thread_t mdc1a00_thread = rt_thread_create("mdc1a00_thread", mdc1a00_thread_, RT_NULL, 1024, 20, 10);

    //启动线程
    rt_thread_startup(mdc1a00_thread);

#ifdef CONFIG_MXS_CALLISTO
    ret = callisto_init();
#else
    ret = galileo_init(init_parameter);
#endif
    if (ret != 0)
    {
        LOGE("Sensor activate failed, please check the physical connection.err:%d", ret);
        goto ERROR;
    }

    // 关闭读写保护

    ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_DISABLE);
    if (ret != 0)
    {
        LOGE("Register write operated failed.");
        goto ERROR;
    }

    ret = mxs_reg_write(REG_ADDR_READ_PROTECT, REG_READ_PROTECT_DISABLE);
    if (ret != 0)
    {
        LOGE("Register write operated failed.");
        goto ERROR;
    }

    // 如果cpix值未被赋予，则使用默认值
    if (init_parameter->cpix == 0)
    {
        ret = mxs_reg_write(REG_ADDR_CPI_X, REG_CPI_DEFAULT);
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }
    else
    {
        ret = mxs_reg_write(REG_ADDR_CPI_X, init_parameter->cpix);
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }

    // 如果cpiy值未被赋予，则使用默认值
    if (init_parameter->cpiy == 0)
    {
        ret = mxs_reg_write(REG_ADDR_CPI_Y, REG_CPI_DEFAULT);
        if (ret) goto ERROR;
    }
    else
    {
        ret = mxs_reg_write(REG_ADDR_CPI_Y, init_parameter->cpiy);
        if (ret) goto ERROR;
    }

    // 开启12bit模式
    // 读取原数值，对寄存器值进行位操作后写回原寄存器
    if (init_parameter->twelve_bit_mode == true)
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION,
                            (tmp | REG_12_BIT_MODE_ENABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }
    else
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION,
                            (tmp & REG_12_BIT_MODE_DISABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }

    // 交换dx和dy的输出
    if (init_parameter->swap_xy == true)
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp | REG_SWAP_XY_ENABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }
    else
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret =
            mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp & REG_SWAP_XY_DISABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }

    // 反向dx的输出
    if (init_parameter->inv_x == true)
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp | REG_INV_X_ENABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }
    else
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp & REG_INV_X_DISABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }

    // 反向dy的输出
    if (init_parameter->inv_y == true)
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp | REG_INV_Y_ENABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }
    else
    {
        mxs_reg_read(REG_ADDR_SENSOR_OPTION, &tmp);
        ret = mxs_reg_write(REG_ADDR_SENSOR_OPTION, (tmp & REG_INV_Y_DISABLE));
        if (ret != 0)
        {
            LOGE("Register write operated failed.");
            goto ERROR;
        }
    }

    // 关闭读写保护
    ret = mxs_reg_write(REG_ADDR_WRITE_PROTECT, REG_WRITE_PROTECT_DISABLE);
    if (ret != 0)
    {
        LOGE("Register write operated failed.");
        goto ERROR;
    }

    ret = mxs_reg_write(REG_ADDR_READ_PROTECT, REG_READ_PROTECT_DISABLE);
    if (ret != 0)
    {
        LOGE("Register write operated failed.");
        goto ERROR;
    }

    //mxs_chip_reg_lists();  // yaml格式输出寄存器数值列表

    //等待
    rt_thread_mdelay(1);
    //读取一次数据，使芯片进入正常工作状态
    mxs_raw_data_t raw_data;
    mxs_get_raw_data(mxs_dev, &raw_data);
    rt_kprintf("init mxs dx:%d,dy:%d\n", raw_data.dx, raw_data.dy);

    return (mxs_t*)mxs_instance;

ERROR:
    LOGE("MXS instance created failed.");

    mxs_reg_deinit();

    if (mxs_instance != NULL)
    {
        free(mxs_instance);
    }

    return NULL;
}

/**
 * @brief  销毁mxs实例
 *
 * @param mxs
 */
void mxs_deinit(mxs_t* mxs)
{
    if (mxs == NULL)
    {
        LOGE("NULL handle of pass-in parameter `mxs`");
        return;
    }
    mxs_st* mxs_instance = (mxs_st*)mxs;

    mxs_reg_deinit();
    free(mxs_instance);
}


/**
 * @brief  获取原始数据
 *
 * @param mxs
 * @param raw_data
 * @return int
 */
int mxs_get_raw_data(mxs_t* mxs, mxs_raw_data_t* raw_data)
{
    uint8_t tmp_dx = 0;
    uint8_t tmp_dy = 0;
    uint8_t tmp_dx_dy_hight = 0;
    int ret = 0;


    if (mxs == NULL || raw_data == NULL)
    {
        LOGE("null `mxs` or `raw_data`!");
        ret = MXS_ERR_INVALID_PARAMETER;
        goto ERROR;
    }

    memset(raw_data, 0, sizeof(mxs_raw_data_t));

    // 必须要先读取motion status，sensor才能够读取数据。
    ret = mxs_reg_read(REG_ADDR_MOTION_STATUS, &(raw_data->status));
    if (ret != 0)
    {
        LOGE("mxs read register failed!");
        goto ERROR;
    }

    if (((raw_data->status >> 7) & 0x01) == 0)
    {
        ret = 1;
        goto ERROR;
    }

    ret = mxs_reg_read(REG_ADDR_DELTA_X, &tmp_dx);
    if (ret != 0)
    {
        LOGE("mxs read register failed!");
        goto ERROR;
    }
    ret = mxs_reg_read(REG_ADDR_DELTA_Y, &tmp_dy);
    if (ret != 0)
    {
        LOGE("mxs read register failed!");
        goto ERROR;
    }

    ret = mxs_reg_read(REG_ADDR_DELTA_XY_HIGH, &tmp_dx_dy_hight);
    if (ret != 0)
    {
        LOGE("mxs read register failed!");
        goto ERROR;
    }

    data_synthesis(
        tmp_dx, tmp_dy, tmp_dx_dy_hight, &(raw_data->dx), &(raw_data->dy));

ERROR:
    return ret;
}

/**
 * @brief  获取motion数据
 *
 * @param mxs
 * @param motion_data
 * @return int
 */
int mxs_get_motion_data(mxs_t* mxs, mxs_motion_data_t* motion_data)
{
    int ret = 0;
    uint32_t timestamp = mixo_hal_get_tick_ms();
    mxs_algo_motion_event_t algo_event = MXS_ALGO_MOTION_NONE;

    mxs_raw_data_t* raw_data = malloc(sizeof(mxs_raw_data_t));
    memset(raw_data, 0, sizeof(mxs_raw_data_t));
    mxs_get_raw_data(mxs, raw_data);

    ret = mxs_algo_config(40, 40);
    if (ret)
    {
        LOGE("Set angle config failed.");
        ret = MXS_ERR_NOT_IMPLEMENTED;
        goto ERROR;
    }

    ret = mxs_algo_get_event(raw_data->dx,
                             raw_data->dy,
                             timestamp,
                             &algo_event,
                             &motion_data->displacement);
    if (ret)
    {
        LOGE("Get algo event failed.");
        goto ERROR;
    }

    motion_data->motion_event = (int)algo_event;

ERROR:
    if (raw_data != NULL)
    {
        free(raw_data);
    }
    return 0;
}

/**
 * @brief 注册用户回调函数
 */
int  mxs_register_trigger_callback(void (*callback)(bool step))
{
    if (callback == RT_NULL)
    {
        LOGE("Invalid parameter!");
        return MXS_ERR_INVALID_PARAMETER;
    }
    user_trigger_callback = callback;

    return  MXS_ERR_NONE;
}

/**
 * @brief  设置电源开关
 *
 * @param true_or_false
 * @return int
 */
int mxs_set_power_up(bool true_or_false)
{
    int ret = 0;
    uint8_t tmp = 0;
    // 将数据从寄存器中读出后进行位操作，再重新写回
    if (true_or_false)
    {
        // bit 3 写 0， 打开电源
        mxs_reg_read(REG_ADDR_CONFIGURATION, &tmp);
        ret = mxs_reg_write(REG_ADDR_CONFIGURATION, (tmp & 0xF7));
        if (ret)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }
    }
    else
    {
        // bit 3 写 1， 关闭电源
        mxs_reg_read(REG_ADDR_CONFIGURATION, &tmp);
        ret = mxs_reg_write(REG_ADDR_CONFIGURATION, (tmp | 0x08));
        if (ret)
        {
            ret = MXS_ERR_GENERAL;
            goto ERROR;
        }

        //高阻IO
        HAL_PIN_Set_Analog(PAD_PA63, 1);
        HAL_PIN_Set_Analog(PAD_PA77, 1);
        HAL_PIN_Set_Analog(PAD_PA65, 1);


        HAL_PIN_Set_Analog(PAD_PA66, 1);
        HAL_PIN_Set_Analog(PAD_PA70, 1);
    }

ERROR:
    return ret;
}





#define POLLING_FREQ 100   // 采集频率(Hz)
#define CPI_X        0x40  // 分辨率
#define CPI_Y        0x40  // 分辨率

#define SAMPLING_DELAY_MS \
    ((1000 / POLLING_FREQ) - 1)  // 采样延时时间(million second)


static uint8_t flag_data_collection;

/**
 * @brief mdc1a00初始化
 *
 * @return int
 */
int mdc1a00_init(void)
{

    mxs_init_parameter_t init_parameter;
    memset(&init_parameter, 0, sizeof(mxs_init_parameter_t));
    init_parameter.cpix = CPI_X;
    init_parameter.cpiy = CPI_Y;
    init_parameter.twelve_bit_mode = true;
    init_parameter.swap_xy = false;
    mxs_dev = mxs_init(&init_parameter);
    if (mxs_dev == NULL)
    {
        LOGE("mxs init failed.");
        return -1;
    }
    return 0;
}

#define MXS_UNIT_TEST
#ifdef MXS_UNIT_TEST
/**测试项:
 * 1.初始化
 * 2.获取原始数据
 */
int cmd_mxs(int argc, char** argv)
{
    int ret = 0;
    int i = 0;
    int mode = 0;
    int watchdog = 0;
    int watchdog_time = 0;
    int vol = 0;
    if (argc > 1)
    {
        if (strcmp(argv[1], "init") == 0)
        {
            mxs_init_parameter_t init_parameter;
            memset(&init_parameter, 0, sizeof(mxs_init_parameter_t));
            init_parameter.cpix = CPI_X;
            init_parameter.cpiy = CPI_Y;
            init_parameter.twelve_bit_mode = true;
            init_parameter.swap_xy = false;
            mxs_dev = mxs_init(&init_parameter);
            if (mxs_dev == NULL)
            {
                LOGE("mxs init failed.");
                return -1;
            }
            LOGD("mxs init success.");
        }
        else if (strcmp(argv[1], "get_raw_data") == 0)
        {
            rt_kprintf("motion_detected_flag:%d\n", motion_detected_flag);
            rt_kprintf("motion_detected_counter:%d\n", motion_detected_counter);
            mxs_raw_data_t raw_data;
            mxs_get_raw_data(mxs_dev, &raw_data);
            rt_kprintf("Raw Data: dx = %d, dy = %d\n", raw_data.dx, raw_data.dy);
        }
    }

    return 0;
}

FINSH_FUNCTION_EXPORT(cmd_mxs, mxs_test);
MSH_CMD_EXPORT(cmd_mxs, mxs_test);

/**
 * 命令行进行测试应该发送这些
 * cmd_mxs init
 * cmd_mxs get_raw_data
 */

#endif
