/**
  ******************************************************************************
  * @file   sh8601z.h
  * <AUTHOR> development team
  * @brief   This file contains all the functions prototypes for the sh8601z.c
  *          driver.
  ******************************************************************************
*/


#ifndef __ft2308_H
#define __ft2308_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ft2308
  * @{
  */

/** @defgroup ft2308_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ft2308_Exported_Constants
  * @{
  */

/**
  * @brief ft2308 chip IDs
  */
#define ft2308_ID                  0x19386

/**
  * @brief  ft2308 Size
  */
#define  ft2308_LCD_PIXEL_WIDTH    (360)
#define  ft2308_LCD_PIXEL_HEIGHT   (360)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ft2308_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define ft2308_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define ft2308_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ft2308 Registers
  */
#define ft2308_SW_RESET           0x0100
#define ft2308_LCD_ID             0x0400
#define ft2308_DSI_ERR            0x0500
#define ft2308_POWER_MODE         0x0A00
#define ft2308_SLEEP_IN           0x1000
#define ft2308_SLEEP_OUT          0x1100
#define ft2308_PARTIAL_DISPLAY    0x1200
#define ft2308_DISPLAY_INVERSION  0x2100
#define ft2308_DISPLAY_OFF        0x2800
#define ft2308_DISPLAY_ON         0x2900
#define ft2308_WRITE_RAM          0x2C00
#define ft2308_READ_RAM           0x2E00
#define ft2308_CASET              0x2A00
#define ft2308_RASET              0x2B00
#define ft2308_PART_CASET              0x3000
#define ft2308_PART_RASET              0x3100
#define ft2308_VSCRDEF            0x3300 /* Vertical Scroll Definition */
#define ft2308_VSCSAD             0x3700 /* Vertical Scroll Start Address of RAM */
#define ft2308_TEARING_EFFECT     0x3500
#define ft2308_NORMAL_DISPLAY     0x3600
#define ft2308_IDLE_MODE_OFF      0x3800
#define ft2308_IDLE_MODE_ON       0x3900
#define ft2308_COLOR_MODE         0x3A00
#define ft2308_CONTINUE_WRITE_RAM 0x3C00
#define ft2308_WBRIGHT            0x5100 /* Write brightness*/
#define ft2308_RBRIGHT            0x5300 /* Read brightness*/
#define ft2308_PORCH_CTRL         0xB200
#define ft2308_FRAME_CTRL         0xB300
#define ft2308_GATE_CTRL          0xB700
#define ft2308_VCOM_SET           0xBB00
#define ft2308_LCM_CTRL           0xC000
#define ft2308_SET_TIME_SRC       0xC200
#define ft2308_SET_DISP_MODE      0xC400
#define ft2308_VCOMH_OFFSET_SET   0xC500
#define ft2308_FR_CTRL            0xC600
#define ft2308_POWER_CTRL         0xD000
#define ft2308_PV_GAMMA_CTRL      0xE000
#define ft2308_NV_GAMMA_CTRL      0xE100
#define ft2308_SPI2EN             0xE700

/**
  * @}
  */

/** @defgroup ft2308_Exported_Functions
  * @{
  */
void     ft2308_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ft2308_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ft2308_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ft2308_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ft2308_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ft2308_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ft2308_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ft2308_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ft2308_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ft2308_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

void     ft2308_TimeoutDbg(LCDC_HandleTypeDef *hlcdc);
void     ft2308_TimeoutReset(LCDC_HandleTypeDef *hlcdc);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __ft2308_H */


