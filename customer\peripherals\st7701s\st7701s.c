/**
  ******************************************************************************
  * @file   st7701s.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for ST7701S LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON>li nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st7701s.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7701S
  * @brief This file provides a set of functions needed to drive the
  *        ST7701S LCD.
  * @{
  */

/** @defgroup ST7701S_Private_TypesDefinitions
  * @{
  */

#define ROW_OFFSET  (0x00)
#define COL_OFFSET  (0x0E)


/**
  * @}
  */

/** @defgroup ST7701S_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ST7701S_Private_Macros
  * @{
  */


#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

/*st7701s start colume & row must can be divided by 2, and roi width&height too.*/
#define ST7701S_ALIGN2(x) ((x) = (x) & (~1))
#define ST7701S_ALIGN1(x) ((x) = (0 == ((x) & 1)) ? (x - 1) : x)


/**
  * @}
  */

/** @defgroup ST7701S_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ST7701S_drv =
{
    ST7701S_Init,
    ST7701S_ReadID,
    ST7701S_DisplayOn,
    ST7701S_DisplayOff,

    ST7701S_SetRegion,
    ST7701S_WritePixel,
    ST7701S_WriteMultiplePixels,

    ST7701S_ReadPixel,

    ST7701S_SetColorMode,
    ST7701S_SetBrightness,
    ST7701S_IdleModeOn,
    ST7701S_IdleModeOff
};


#ifdef BSP_LCDC_USING_DSI


static const LCDC_InitTypeDef lcdc_int_cfg_dsi_cmd =
{
    .lcd_itf = LCDC_INTF_DSI,
    .freq = DSI_FREQ_480Mbps,
    .color_mode = LCDC_PIXEL_FORMAT_RGB888,  //DBI output color format,   should match with .cfg.dsi.CmdCfg.ColorCoding

    .cfg = {

        .dsi = {

            .Init = {
                .AutomaticClockLaneControl = DSI_AUTO_CLK_LANE_CTRL_ENABLE,
                .NumberOfLanes = DSI_ONE_DATA_LANE,
                .TXEscapeCkdiv = 0x4,
            },


            .CmdCfg = {
                .VirtualChannelID      = 0,
                .CommandSize           = 0xFFFF,
                .TearingEffectSource   = DSI_TE_DSILINK,
                .TEAcknowledgeRequest  = DSI_TE_ACKNOWLEDGE_DISABLE,     //Close TE
                .ColorCoding           = DSI_RGB888,                    //DSI input & output color format
            },


            .PhyTimings = {
                .ClockLaneHS2LPTime = 35,
                .ClockLaneLP2HSTime = 35,
                .DataLaneHS2LPTime = 35,
                .DataLaneLP2HSTime = 35,
                .DataLaneMaxReadTime = 0,
                .StopWaitTime = 0,
            },

            .HostTimeouts = {
                .TimeoutCkdiv = 1,
                .HighSpeedTransmissionTimeout = 0,
                .LowPowerReceptionTimeout = 0,
                .HighSpeedReadTimeout = 0,
                .LowPowerReadTimeout = 0,
                .HighSpeedWriteTimeout = 0,
                //.HighSpeedWritePrespMode = DSI_HS_PM_DISABLE,
                .LowPowerWriteTimeout = 0,
                .BTATimeout = 0,
            },


            .LPCmd = {
                .LPGenShortWriteNoP    = DSI_LP_GSW0P_ENABLE,
                .LPGenShortWriteOneP   = DSI_LP_GSW1P_ENABLE,
                .LPGenShortWriteTwoP   = DSI_LP_GSW2P_ENABLE,
                .LPGenShortReadNoP     = DSI_LP_GSR0P_ENABLE,
                .LPGenShortReadOneP    = DSI_LP_GSR1P_ENABLE,
                .LPGenShortReadTwoP    = DSI_LP_GSR2P_ENABLE,
                .LPGenLongWrite        = DSI_LP_GLW_ENABLE,
                .LPDcsShortWriteNoP    = DSI_LP_DSW0P_ENABLE,
                .LPDcsShortWriteOneP   = DSI_LP_DSW1P_ENABLE,
                .LPDcsShortReadNoP     = DSI_LP_DSR0P_ENABLE,
                .LPDcsLongWrite        = DSI_LP_DLW_DISABLE,
                .LPMaxReadPacket       = DSI_LP_MRDP_ENABLE,
                .AcknowledgeRequest    = DSI_ACKNOWLEDGE_DISABLE, //disable LCD error reports
            },

        },
    },
};

static const LCDC_InitTypeDef lcdc_int_cfg_dsi_video =
{
    .lcd_itf = LCDC_INTF_DSI_VIDEO,
    .freq = DSI_FREQ_480Mbps,
    .color_mode = LCDC_PIXEL_FORMAT_RGB888,  //DBI output color format,   should match with .cfg.dsi.CmdCfg.ColorCoding

    .cfg = {

        .dsi_vid = {

            .Init = {
                .AutomaticClockLaneControl = DSI_AUTO_CLK_LANE_CTRL_ENABLE,
                .NumberOfLanes = DSI_ONE_DATA_LANE,
                .TXEscapeCkdiv = 0x4,
            },


            .VidCfg = {
                .VirtualChannelID      = 0,
                .ColorCoding           = DSI_RGB888,                    //DSI input & output color format

                .VS_width      = 4,    //VLW
                .HS_width      = 12,   //HLW

                .bank_col_head = 20,   //VBP
                .valid_columns = LCD_HOR_RES_MAX,
                .bank_col_tail = 20,   //VFP

                .bank_row_head = 30,  //HBP
                .valid_rows    = LCD_VER_RES_MAX,
                .bank_row_tail = 30,  //HFP

                .interrupt_line_num = 1,
            },

            .PhyTimings = {
                .ClockLaneHS2LPTime = 35,
                .ClockLaneLP2HSTime = 35,
                .DataLaneHS2LPTime = 35,
                .DataLaneLP2HSTime = 35,
                .DataLaneMaxReadTime = 0,
                .StopWaitTime = 0,
            },

            .HostTimeouts = {
                .TimeoutCkdiv = 1,
                .HighSpeedTransmissionTimeout = 0,
                .LowPowerReceptionTimeout = 0,
                .HighSpeedReadTimeout = 0,
                .LowPowerReadTimeout = 0,
                .HighSpeedWriteTimeout = 0,
                //.HighSpeedWritePrespMode = DSI_HS_PM_DISABLE,
                .LowPowerWriteTimeout = 0,
                .BTATimeout = 0,
            },


            .LPCmd = {
                .LPGenShortWriteNoP    = DSI_LP_GSW0P_ENABLE,
                .LPGenShortWriteOneP   = DSI_LP_GSW1P_ENABLE,
                .LPGenShortWriteTwoP   = DSI_LP_GSW2P_ENABLE,
                .LPGenShortReadNoP     = DSI_LP_GSR0P_ENABLE,
                .LPGenShortReadOneP    = DSI_LP_GSR1P_ENABLE,
                .LPGenShortReadTwoP    = DSI_LP_GSR2P_ENABLE,
                .LPGenLongWrite        = DSI_LP_GLW_ENABLE,
                .LPDcsShortWriteNoP    = DSI_LP_DSW0P_ENABLE,
                .LPDcsShortWriteOneP   = DSI_LP_DSW1P_ENABLE,
                .LPDcsShortReadNoP     = DSI_LP_DSR0P_ENABLE,
                .LPDcsLongWrite        = DSI_LP_DLW_DISABLE,
                .LPMaxReadPacket       = DSI_LP_MRDP_ENABLE,
                .AcknowledgeRequest    = DSI_ACKNOWLEDGE_DISABLE, //disable LCD error reports
            },

        },
    },
};
#endif /* BSP_LCDC_USING_DSI */


#define MAX_CMD_LEN 18

#define GP_COMMAD_PA(...)   {__VA_ARGS__}

static const uint8_t lcd_init_cmds1[][MAX_CMD_LEN] =
{

//---------------------------------------Bank0 Setting-------------------------------------------------//
//------------------------------------Display Control setting----------------------------------------------//
    GP_COMMAD_PA(6, 0xFF, 0x77, 0x01, 0x00, 0x00, 0x10),
    GP_COMMAD_PA(3, 0xC0, 0x63, 0x00),
    GP_COMMAD_PA(3, 0xC1, 0x11, 0x02),
    GP_COMMAD_PA(3, 0xC2, 0x31, 0x08),
    GP_COMMAD_PA(2, 0xCC, 0x10),
    GP_COMMAD_PA(17, 0xB0, 0x40, 0x01, 0x46, 0x0D, 0x13, 0x09, 0x05, 0x09, 0x09, 0x1B, 0x07, 0x15, 0x12, 0x4C, 0x10, 0xC8),
    GP_COMMAD_PA(17, 0xB1, 0x40, 0x02, 0x86, 0x0D, 0x13, 0x09, 0x05, 0x09, 0x09, 0x1F, 0x07, 0x15, 0x12, 0x15, 0x19, 0x08),
//---------------------------------------End Gamma Setting----------------------------------------------//
//------------------------------------End Display Control setting----------------------------------------//
//-----------------------------------------Bank0 Setting End---------------------------------------------//
//-------------------------------------------Bank1 Setting---------------------------------------------------//
//-------------------------------- Power Control Registers Initial --------------------------------------//
    GP_COMMAD_PA(6, 0xFF, 0x77, 0x01, 0x00, 0x00, 0x11),
    GP_COMMAD_PA(2, 0xB0, 0x50),
//-------------------------------------------Vcom Setting---------------------------------------------------//
    GP_COMMAD_PA(2, 0xB1, 0x68),
//-----------------------------------------End Vcom Setting-----------------------------------------------//
    GP_COMMAD_PA(2, 0xB2, 0x07),
    GP_COMMAD_PA(2, 0xB3, 0x80),
    GP_COMMAD_PA(2, 0xB5, 0x47),
    GP_COMMAD_PA(2, 0xB7, 0x85),
    GP_COMMAD_PA(2, 0xB8, 0x21),
    GP_COMMAD_PA(2, 0xB9, 0x10),
    GP_COMMAD_PA(2, 0xC1, 0x78),
    GP_COMMAD_PA(2, 0xC2, 0x78),
    GP_COMMAD_PA(2, 0xD0, 0x88),
//---------------------------------End Power Control Registers Initial -------------------------------//
};

static const uint8_t lcd_init_cmds2[][MAX_CMD_LEN] =
{
//---------------------------------------------GIP Setting----------------------------------------------------//
    GP_COMMAD_PA(4, 0xE0, 0x00, 0x00, 0x02),
    GP_COMMAD_PA(12, 0xE1, 0x08, 0x00, 0x0A, 0x00, 0x07, 0x00, 0x09, 0x00, 0x00, 0x33, 0x33),
    GP_COMMAD_PA(14, 0xE2, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00),
    GP_COMMAD_PA(5, 0xE3, 0x00, 0x00, 0x33, 0x33),
    GP_COMMAD_PA(3, 0xE4, 0x44, 0x44),
    GP_COMMAD_PA(17, 0xE5, 0x0E, 0x2D, 0xA0, 0xA0, 0x10, 0x2D, 0xA0, 0xA0, 0x0A, 0x2D, 0xA0, 0xA0, 0x0C, 0x2D, 0xA0, 0xA0),
    GP_COMMAD_PA(5, 0xE6, 0x00, 0x00, 0x33, 0x33),
    GP_COMMAD_PA(3, 0xE7, 0x44, 0x44),
    GP_COMMAD_PA(17, 0xE8, 0x0D, 0x2D, 0xA0, 0xA0, 0x0F, 0x2D, 0xA0, 0xA0, 0x09, 0x2D, 0xA0, 0xA0, 0x0B, 0x2D, 0xA0, 0xA0),
    GP_COMMAD_PA(8, 0xEB, 0x02, 0x01, 0xE4, 0xE4, 0x44, 0x00, 0x40),
    GP_COMMAD_PA(3, 0xEC, 0x02, 0x01),
    GP_COMMAD_PA(17, 0xED, 0xAB, 0x89, 0x76, 0x54, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x10, 0x45, 0x67, 0x98, 0xBA),
//--------------------------------------------End GIP Setting-----------------------------------------------//
//------------------------------ Power Control Registers Initial End-----------------------------------//
//------------------------------------------Bank1 Setting----------------------------------------------------//
    GP_COMMAD_PA(6, 0xFF, 0x77, 0x01, 0x00, 0x00, 0x00),

    GP_COMMAD_PA(1, 0x11),

};

static LCDC_InitTypeDef lcdc_int_cfg;


static void     ST7701S_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
static uint32_t ST7701S_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);
static void ST7701S_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable);

#if 1//def ST7701S_DEBUG

void HAL_DBG_printf(const char *fmt, ...)
{
    va_list args;

    va_start(args, fmt);
    rt_kprintf(fmt, args);
    rt_kputs("\r\n");
    va_end(args);
}
#endif /* ST7701S_DEBUG */




LCD_DRIVER_EXPORT(st7701s, ST7701S_ID, &lcdc_int_cfg,
                  &ST7701S_drv,
                  ST7701S_LCD_PIXEL_WIDTH,
                  ST7701S_LCD_PIXEL_HEIGHT,
                  2);

/**
  * @}
  */


/** @defgroup ST7701S_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7701S_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ST7701S_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf) || HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 2000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }
    }


}


/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ST7701S_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];


    /* Initialize ST7701S to cmd mode    ----------------------------------*/
    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_dsi_cmd, sizeof(lcdc_int_cfg));
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(10);
    BSP_LCD_Reset(1);

    LCD_DRIVER_DELAY_MS(10); //LCD must at sleep in mode after power on, 10ms is enough


    for (uint32_t i = 0; i < sizeof(lcd_init_cmds1) / MAX_CMD_LEN; i++)
    {
        ST7701S_WriteReg(hlcdc, lcd_init_cmds1[i][1], (uint8_t *)&lcd_init_cmds1[i][2], lcd_init_cmds1[i][0] - 1);
    }

    LCD_DRIVER_DELAY_MS(100);

    for (uint32_t i = 0; i < sizeof(lcd_init_cmds2) / MAX_CMD_LEN; i++)
    {
        ST7701S_WriteReg(hlcdc, lcd_init_cmds2[i][1], (uint8_t *)&lcd_init_cmds2[i][2], lcd_init_cmds2[i][0] - 1);
    }



    LCD_DRIVER_DELAY_MS(120); //Delay 5 ms after sleep out

    /* Display ON command */
    ST7701S_WriteReg(hlcdc, ST7701S_DISPLAY_ON, (uint8_t *)NULL, 0);

    LCD_DRIVER_DELAY_MS(10); //Wait TE signal ready


#if 0

    /*Clear gram*/
    HAL_LCDC_Enable_TE(hlcdc, 0);
    ST7701S_SetRegion(hlcdc, 0, 0, ST7701S_LCD_PIXEL_WIDTH - 1, ST7701S_LCD_PIXEL_HEIGHT - 1);
    HAL_LCDC_LayerSetFormat(hlcdc, HAL_LCDC_LAYER_DEFAULT, LCDC_PIXEL_FORMAT_RGB565);
    HAL_LCDC_LayerDisable(hlcdc, HAL_LCDC_LAYER_DEFAULT);
    HAL_LCDC_SetBgColor(hlcdc, 0, 255, 0);
    HAL_LCDC_SendLayerData2Reg(hlcdc, ST7701S_WRITE_RAM, 1);

    HAL_LCDC_LayerEnable(hlcdc, HAL_LCDC_LAYER_DEFAULT);
#ifdef LCD_ST7701S_VSYNC_ENABLE
    HAL_LCDC_Enable_TE(hlcdc, 1);
#endif /* LCD_ST7701S_VSYNC_ENABLE */

    LCD_DRIVER_DELAY_MS(1000);
#endif /* 0 */


    {
        uint32_t data = 0;
        /*

            Bit Description Value
            D7 Booster Voltage Status “1”=Booster On, “0”=Booster Off
            D6 Not Defined Set to “0” (not used)
            D5 Not Defined Set to “0” (not used)
            D4 Sleep In/Out “1” = Sleep Out Mode, “0” = Sleep In Mode
            D3 Not Defined Set to “1” (not used)
            D2 Display On/Off “1” = Display is On, “0” = Display is Off
            D1 Not Defined Set to “0” (not used)
            D0 Not Defined Set to “0” (not used)

        */
        data = ST7701S_ReadData(hlcdc, ST7701S_POWER_MODE, 1);
        DEBUG_PRINTF("PowerMode 0x%x", data);
    }

    HAL_LCDC_SetROIArea(hlcdc, 0, 0, LCD_HOR_RES_MAX - 1, LCD_VER_RES_MAX - 1);












}



/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ST7701S_ReadID(LCDC_HandleTypeDef *hlcdc)
{
#ifdef DSI_HW_TEST
    return ST7701S_ID;
#else
    uint32_t data;
    /*
        data = ST7701S_ReadData(hlcdc,ST7701S_CASET, 4);
        DEBUG_PRINTF("\nST7701S_CASET 0x%x \n", data);


        data = ST7701S_ReadData(hlcdc,ST7701S_RASET, 4);
        DEBUG_PRINTF("\nST7701S_RASET 0x%x \n", data);
    */
    data = ST7701S_ReadData(hlcdc, ST7701S_LCD_ID, 3);
    DEBUG_PRINTF("\nST7701S_ReadID 0x%x \n", data);

    if (data)
    {
        DEBUG_PRINTF("LCD module use ST7701S IC \n");
        data = ST7701S_ID;
    }


    return data;
#endif /* DSI_HW_TEST */

}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ST7701S_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    //ST7701S_WriteReg(hlcdc, ST7701S_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ST7701S_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    //ST7701S_WriteReg(hlcdc, ST7701S_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void ST7701S_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    /*Invalid*/
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ST7701S_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;
    uint8_t   parameter[4];


    /* Set Cursor */
    ST7701S_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ST7701S_WriteReg(hlcdc, ST7701S_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void ST7701S_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;
    static bool video_mode = false;

    if (!video_mode)
    {
        video_mode = true;
        /* Switch to video mode ----------------------------------*/
        //DEBUG_PRINTF("Switch to video mode \n");
        memcpy(&lcdc_int_cfg, &lcdc_int_cfg_dsi_video, sizeof(lcdc_int_cfg));
        memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
        HAL_LCDC_Init(hlcdc);
    }


    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);

    if (0)
    {
    }
    else
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST7701S_WRITE_RAM, 1);
    }
}


/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ST7701S_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    if (0)
    {
    }
    else
    {
        HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
    }
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ST7701S_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ST7701S_ReadMode(hlcdc, true);
    if (0)
    {
    }
    else
    {
        HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    }
    ST7701S_ReadMode(hlcdc, false);
    return rd_data;
}



uint32_t ST7701S_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    DEBUG_PRINTF("NOT support read pixel\n");

    return 0; //Not support read pixel

#if 0
    uint8_t  r, g, b;
    uint32_t ret_v, read_value;
    DEBUG_PRINTF("ST7701S_ReadPixel[%d,%d]\n", Xpos, Ypos);

    ST7701S_ALIGN2(Xpos);
    ST7701S_ALIGN2(Ypos);

    ST7701S_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    read_value = ST7701S_ReadData(hlcdc, ST7701S_READ_RAM, 4);
    DEBUG_PRINTF("result: [%x]\n", read_value);

    b = (read_value >> 0) & 0xFF;
    g = (read_value >> 8) & 0xFF;
    r = (read_value >> 16) & 0xFF;

    DEBUG_PRINTF("r=%d, g=%d, b=%d \n", r, g, b);

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        ret_v = (uint32_t)(((r << 11) & 0xF800) | ((g << 5) & 0x7E0) | ((b >> 3) & 0X1F));
        break;

    /*
       (8bit R + 3bit dummy + 8bit G + 3bit dummy + 8bit B)

    */
    case LCDC_PIXEL_FORMAT_RGB888:
        ret_v = (uint32_t)(((r << 16) & 0xFF0000) | ((g << 8) & 0xFF00) | ((b) & 0XFF));
        break;

    default:
        RT_ASSERT(0);
        break;
    }


    //ST7701S_WriteReg(hlcdc,ST7701S_COLOR_MODE, parameter, 1);

    return ret_v;
#endif /* 0 */
}


void ST7701S_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x75;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565_SWAP;

#ifdef BSP_LCDC_USING_DSI
        if (HAL_LCDC_IS_DSI_IF(lcdc_int_cfg.lcd_itf) && (lcdc_int_cfg.freq > DSI_FREQ_336Mbps))
            HAL_LCDC_SetFreq(hlcdc, DSI_FREQ_336Mbps);  //RGB565 only support 320Mbps,  RGB888 support 500Mbps
#endif /* BSP_LCDC_USING_DSI */
        break;

    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        parameter[0] = 0x77;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
#ifdef BSP_LCDC_USING_DSI
        HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq);  //Revert freq in case of change in RGB565
#endif /* BSP_LCDC_USING_DSI */
        break;

    default:
        return; //unsupport
        break;
    }

    ST7701S_WriteReg(hlcdc, ST7701S_COLOR_MODE, parameter, 1);


    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

#define ST7701S_BRIGHTNESS_MAX 0xFF

void     ST7701S_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((int)ST7701S_BRIGHTNESS_MAX * br / 100);
    //ST7701S_WriteReg(hlcdc, ST7701S_WBRIGHT, &bright, 1);
}

/**
  * @brief  Enable the Display idle mode.
  * @param  None
  * @retval None
  */
void ST7701S_IdleModeOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Idle mode On */
    ST7701S_WriteReg(hlcdc, ST7701S_IDLE_MODE_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display idle mode.
  * @param  None
  * @retval None
  */
void ST7701S_IdleModeOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Idle mode Off */
    ST7701S_WriteReg(hlcdc, ST7701S_IDLE_MODE_OFF, (uint8_t *)NULL, 0);
}

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
