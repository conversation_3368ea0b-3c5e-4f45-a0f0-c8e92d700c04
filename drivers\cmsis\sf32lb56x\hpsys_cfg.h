#ifndef __HPSYS_CFG_H
#define __HPSYS_CFG_H

typedef struct
{
    __IO uint32_t BMR;
    __IO uint32_t IDR;
    __IO uint32_t SCR;
    __IO uint32_t USBCR;
    __IO uint32_t MCR;
    __IO uint32_t ULPMCR;
    __IO uint32_t RTC_TR;
    __IO uint32_t RTC_DR;
    __IO uint32_t DBGR;
    __IO uint32_t CAU2_CR;
    __IO uint32_t CAU2_RSVD1;
    __IO uint32_t CAU2_RSVD2;
    __IO uint32_t BISTCR;
    __IO uint32_t BISTR;
    __IO uint32_t SYS_RSVD;
    __IO uint32_t LPIRQ;
    __IO uint32_t PATCH1;
    __IO uint32_t PATCH2;
    __IO uint32_t PATCH3;
    __IO uint32_t ROMCR0;
    __IO uint32_t ROMCR1;
    __IO uint32_t ROMCR2;
    __IO uint32_t ROMCR3;
    __IO uint32_t SYSCR;
    __IO uint32_t I2C1_PINR;
    __IO uint32_t I2C2_PINR;
    __IO uint32_t I2C3_PINR;
    __IO uint32_t I2C4_PINR;
    __IO uint32_t USART1_PINR;
    __IO uint32_t USART2_PINR;
    __IO uint32_t USART3_PINR;
    __IO uint32_t GPTIM1_PINR;
    __IO uint32_t GPTIM2_PINR;
    __IO uint32_t ETR_PINR;
    __IO uint32_t LPTIM1_PINR;
} HPSYS_CFG_TypeDef;


/***************** Bit definition for HPSYS_CFG_BMR register ******************/
#define HPSYS_CFG_BMR_BOOT_MODE_Pos     (0U)
#define HPSYS_CFG_BMR_BOOT_MODE_Msk     (0x1UL << HPSYS_CFG_BMR_BOOT_MODE_Pos)
#define HPSYS_CFG_BMR_BOOT_MODE         HPSYS_CFG_BMR_BOOT_MODE_Msk

/***************** Bit definition for HPSYS_CFG_IDR register ******************/
#define HPSYS_CFG_IDR_REVID_Pos         (0U)
#define HPSYS_CFG_IDR_REVID_Msk         (0xFFUL << HPSYS_CFG_IDR_REVID_Pos)
#define HPSYS_CFG_IDR_REVID             HPSYS_CFG_IDR_REVID_Msk
#define HPSYS_CFG_IDR_PID_Pos           (8U)
#define HPSYS_CFG_IDR_PID_Msk           (0xFFUL << HPSYS_CFG_IDR_PID_Pos)
#define HPSYS_CFG_IDR_PID               HPSYS_CFG_IDR_PID_Msk
#define HPSYS_CFG_IDR_CID_Pos           (16U)
#define HPSYS_CFG_IDR_CID_Msk           (0xFFUL << HPSYS_CFG_IDR_CID_Pos)
#define HPSYS_CFG_IDR_CID               HPSYS_CFG_IDR_CID_Msk
#define HPSYS_CFG_IDR_SID_Pos           (24U)
#define HPSYS_CFG_IDR_SID_Msk           (0xFFUL << HPSYS_CFG_IDR_SID_Pos)
#define HPSYS_CFG_IDR_SID               HPSYS_CFG_IDR_SID_Msk

/***************** Bit definition for HPSYS_CFG_SCR register ******************/
#define HPSYS_CFG_SCR_FKEY_MODE_Pos     (0U)
#define HPSYS_CFG_SCR_FKEY_MODE_Msk     (0x1UL << HPSYS_CFG_SCR_FKEY_MODE_Pos)
#define HPSYS_CFG_SCR_FKEY_MODE         HPSYS_CFG_SCR_FKEY_MODE_Msk

/**************** Bit definition for HPSYS_CFG_USBCR register *****************/
#define HPSYS_CFG_USBCR_USB_EN_Pos      (0U)
#define HPSYS_CFG_USBCR_USB_EN_Msk      (0x1UL << HPSYS_CFG_USBCR_USB_EN_Pos)
#define HPSYS_CFG_USBCR_USB_EN          HPSYS_CFG_USBCR_USB_EN_Msk
#define HPSYS_CFG_USBCR_LDO_VSEL_Pos    (1U)
#define HPSYS_CFG_USBCR_LDO_VSEL_Msk    (0x7UL << HPSYS_CFG_USBCR_LDO_VSEL_Pos)
#define HPSYS_CFG_USBCR_LDO_VSEL        HPSYS_CFG_USBCR_LDO_VSEL_Msk
#define HPSYS_CFG_USBCR_LDO_LP_EN_Pos   (4U)
#define HPSYS_CFG_USBCR_LDO_LP_EN_Msk   (0x1UL << HPSYS_CFG_USBCR_LDO_LP_EN_Pos)
#define HPSYS_CFG_USBCR_LDO_LP_EN       HPSYS_CFG_USBCR_LDO_LP_EN_Msk
#define HPSYS_CFG_USBCR_DM_PD_Pos       (5U)
#define HPSYS_CFG_USBCR_DM_PD_Msk       (0x1UL << HPSYS_CFG_USBCR_DM_PD_Pos)
#define HPSYS_CFG_USBCR_DM_PD           HPSYS_CFG_USBCR_DM_PD_Msk
#define HPSYS_CFG_USBCR_DP_EN_Pos       (6U)
#define HPSYS_CFG_USBCR_DP_EN_Msk       (0x1UL << HPSYS_CFG_USBCR_DP_EN_Pos)
#define HPSYS_CFG_USBCR_DP_EN           HPSYS_CFG_USBCR_DP_EN_Msk
#define HPSYS_CFG_USBCR_TX_RTUNE_Pos    (8U)
#define HPSYS_CFG_USBCR_TX_RTUNE_Msk    (0x7UL << HPSYS_CFG_USBCR_TX_RTUNE_Pos)
#define HPSYS_CFG_USBCR_TX_RTUNE        HPSYS_CFG_USBCR_TX_RTUNE_Msk
#define HPSYS_CFG_USBCR_DC_TE_Pos       (12U)
#define HPSYS_CFG_USBCR_DC_TE_Msk       (0x1UL << HPSYS_CFG_USBCR_DC_TE_Pos)
#define HPSYS_CFG_USBCR_DC_TE           HPSYS_CFG_USBCR_DC_TE_Msk
#define HPSYS_CFG_USBCR_DC_TR_Pos       (13U)
#define HPSYS_CFG_USBCR_DC_TR_Msk       (0x7UL << HPSYS_CFG_USBCR_DC_TR_Pos)
#define HPSYS_CFG_USBCR_DC_TR           HPSYS_CFG_USBCR_DC_TR_Msk
#define HPSYS_CFG_USBCR_RSVD0_Pos       (16U)
#define HPSYS_CFG_USBCR_RSVD0_Msk       (0xFFUL << HPSYS_CFG_USBCR_RSVD0_Pos)
#define HPSYS_CFG_USBCR_RSVD0           HPSYS_CFG_USBCR_RSVD0_Msk
#define HPSYS_CFG_USBCR_RSVD1_Pos       (24U)
#define HPSYS_CFG_USBCR_RSVD1_Msk       (0xFFUL << HPSYS_CFG_USBCR_RSVD1_Pos)
#define HPSYS_CFG_USBCR_RSVD1           HPSYS_CFG_USBCR_RSVD1_Msk

/***************** Bit definition for HPSYS_CFG_MCR register ******************/
#define HPSYS_CFG_MCR_PD_ROM_Pos        (0U)
#define HPSYS_CFG_MCR_PD_ROM_Msk        (0x1UL << HPSYS_CFG_MCR_PD_ROM_Pos)
#define HPSYS_CFG_MCR_PD_ROM            HPSYS_CFG_MCR_PD_ROM_Msk
#define HPSYS_CFG_MCR_PD_ITCM_Pos       (1U)
#define HPSYS_CFG_MCR_PD_ITCM_Msk       (0x1UL << HPSYS_CFG_MCR_PD_ITCM_Pos)
#define HPSYS_CFG_MCR_PD_ITCM           HPSYS_CFG_MCR_PD_ITCM_Msk
#define HPSYS_CFG_MCR_PD_CACHE_Pos      (2U)
#define HPSYS_CFG_MCR_PD_CACHE_Msk      (0x1UL << HPSYS_CFG_MCR_PD_CACHE_Pos)
#define HPSYS_CFG_MCR_PD_CACHE          HPSYS_CFG_MCR_PD_CACHE_Msk
#define HPSYS_CFG_MCR_PD_RAM0_Pos       (3U)
#define HPSYS_CFG_MCR_PD_RAM0_Msk       (0x1UL << HPSYS_CFG_MCR_PD_RAM0_Pos)
#define HPSYS_CFG_MCR_PD_RAM0           HPSYS_CFG_MCR_PD_RAM0_Msk
#define HPSYS_CFG_MCR_PD_RAM1_Pos       (4U)
#define HPSYS_CFG_MCR_PD_RAM1_Msk       (0x1UL << HPSYS_CFG_MCR_PD_RAM1_Pos)
#define HPSYS_CFG_MCR_PD_RAM1           HPSYS_CFG_MCR_PD_RAM1_Msk
#define HPSYS_CFG_MCR_PD_RAM2_Pos       (5U)
#define HPSYS_CFG_MCR_PD_RAM2_Msk       (0x1UL << HPSYS_CFG_MCR_PD_RAM2_Pos)
#define HPSYS_CFG_MCR_PD_RAM2           HPSYS_CFG_MCR_PD_RAM2_Msk
#define HPSYS_CFG_MCR_PD_RAM3_Pos       (6U)
#define HPSYS_CFG_MCR_PD_RAM3_Msk       (0x1UL << HPSYS_CFG_MCR_PD_RAM3_Pos)
#define HPSYS_CFG_MCR_PD_RAM3           HPSYS_CFG_MCR_PD_RAM3_Msk
#define HPSYS_CFG_MCR_PD_OTHER_Pos      (13U)
#define HPSYS_CFG_MCR_PD_OTHER_Msk      (0x1UL << HPSYS_CFG_MCR_PD_OTHER_Pos)
#define HPSYS_CFG_MCR_PD_OTHER          HPSYS_CFG_MCR_PD_OTHER_Msk
#define HPSYS_CFG_MCR_FORCE_ON_Pos      (31U)
#define HPSYS_CFG_MCR_FORCE_ON_Msk      (0x1UL << HPSYS_CFG_MCR_FORCE_ON_Pos)
#define HPSYS_CFG_MCR_FORCE_ON          HPSYS_CFG_MCR_FORCE_ON_Msk

/**************** Bit definition for HPSYS_CFG_ULPMCR register ****************/
#define HPSYS_CFG_ULPMCR_RM_Pos         (0U)
#define HPSYS_CFG_ULPMCR_RM_Msk         (0x3UL << HPSYS_CFG_ULPMCR_RM_Pos)
#define HPSYS_CFG_ULPMCR_RM             HPSYS_CFG_ULPMCR_RM_Msk
#define HPSYS_CFG_ULPMCR_RME_Pos        (4U)
#define HPSYS_CFG_ULPMCR_RME_Msk        (0x1UL << HPSYS_CFG_ULPMCR_RME_Pos)
#define HPSYS_CFG_ULPMCR_RME            HPSYS_CFG_ULPMCR_RME_Msk
#define HPSYS_CFG_ULPMCR_RA_Pos         (5U)
#define HPSYS_CFG_ULPMCR_RA_Msk         (0x3UL << HPSYS_CFG_ULPMCR_RA_Pos)
#define HPSYS_CFG_ULPMCR_RA             HPSYS_CFG_ULPMCR_RA_Msk
#define HPSYS_CFG_ULPMCR_WA_Pos         (7U)
#define HPSYS_CFG_ULPMCR_WA_Msk         (0x7UL << HPSYS_CFG_ULPMCR_WA_Pos)
#define HPSYS_CFG_ULPMCR_WA             HPSYS_CFG_ULPMCR_WA_Msk
#define HPSYS_CFG_ULPMCR_WPULSE_Pos     (10U)
#define HPSYS_CFG_ULPMCR_WPULSE_Msk     (0x7UL << HPSYS_CFG_ULPMCR_WPULSE_Pos)
#define HPSYS_CFG_ULPMCR_WPULSE         HPSYS_CFG_ULPMCR_WPULSE_Msk

/**************** Bit definition for HPSYS_CFG_RTC_TR register ****************/
#define HPSYS_CFG_RTC_TR_SS_Pos         (0U)
#define HPSYS_CFG_RTC_TR_SS_Msk         (0x3FFUL << HPSYS_CFG_RTC_TR_SS_Pos)
#define HPSYS_CFG_RTC_TR_SS             HPSYS_CFG_RTC_TR_SS_Msk
#define HPSYS_CFG_RTC_TR_SU_Pos         (11U)
#define HPSYS_CFG_RTC_TR_SU_Msk         (0xFUL << HPSYS_CFG_RTC_TR_SU_Pos)
#define HPSYS_CFG_RTC_TR_SU             HPSYS_CFG_RTC_TR_SU_Msk
#define HPSYS_CFG_RTC_TR_ST_Pos         (15U)
#define HPSYS_CFG_RTC_TR_ST_Msk         (0x7UL << HPSYS_CFG_RTC_TR_ST_Pos)
#define HPSYS_CFG_RTC_TR_ST             HPSYS_CFG_RTC_TR_ST_Msk
#define HPSYS_CFG_RTC_TR_MNU_Pos        (18U)
#define HPSYS_CFG_RTC_TR_MNU_Msk        (0xFUL << HPSYS_CFG_RTC_TR_MNU_Pos)
#define HPSYS_CFG_RTC_TR_MNU            HPSYS_CFG_RTC_TR_MNU_Msk
#define HPSYS_CFG_RTC_TR_MNT_Pos        (22U)
#define HPSYS_CFG_RTC_TR_MNT_Msk        (0x7UL << HPSYS_CFG_RTC_TR_MNT_Pos)
#define HPSYS_CFG_RTC_TR_MNT            HPSYS_CFG_RTC_TR_MNT_Msk
#define HPSYS_CFG_RTC_TR_HU_Pos         (25U)
#define HPSYS_CFG_RTC_TR_HU_Msk         (0xFUL << HPSYS_CFG_RTC_TR_HU_Pos)
#define HPSYS_CFG_RTC_TR_HU             HPSYS_CFG_RTC_TR_HU_Msk
#define HPSYS_CFG_RTC_TR_HT_Pos         (29U)
#define HPSYS_CFG_RTC_TR_HT_Msk         (0x3UL << HPSYS_CFG_RTC_TR_HT_Pos)
#define HPSYS_CFG_RTC_TR_HT             HPSYS_CFG_RTC_TR_HT_Msk
#define HPSYS_CFG_RTC_TR_PM_Pos         (31U)
#define HPSYS_CFG_RTC_TR_PM_Msk         (0x1UL << HPSYS_CFG_RTC_TR_PM_Pos)
#define HPSYS_CFG_RTC_TR_PM             HPSYS_CFG_RTC_TR_PM_Msk

/**************** Bit definition for HPSYS_CFG_RTC_DR register ****************/
#define HPSYS_CFG_RTC_DR_DU_Pos         (0U)
#define HPSYS_CFG_RTC_DR_DU_Msk         (0xFUL << HPSYS_CFG_RTC_DR_DU_Pos)
#define HPSYS_CFG_RTC_DR_DU             HPSYS_CFG_RTC_DR_DU_Msk
#define HPSYS_CFG_RTC_DR_DT_Pos         (4U)
#define HPSYS_CFG_RTC_DR_DT_Msk         (0x3UL << HPSYS_CFG_RTC_DR_DT_Pos)
#define HPSYS_CFG_RTC_DR_DT             HPSYS_CFG_RTC_DR_DT_Msk
#define HPSYS_CFG_RTC_DR_MU_Pos         (8U)
#define HPSYS_CFG_RTC_DR_MU_Msk         (0xFUL << HPSYS_CFG_RTC_DR_MU_Pos)
#define HPSYS_CFG_RTC_DR_MU             HPSYS_CFG_RTC_DR_MU_Msk
#define HPSYS_CFG_RTC_DR_MT_Pos         (12U)
#define HPSYS_CFG_RTC_DR_MT_Msk         (0x1UL << HPSYS_CFG_RTC_DR_MT_Pos)
#define HPSYS_CFG_RTC_DR_MT             HPSYS_CFG_RTC_DR_MT_Msk
#define HPSYS_CFG_RTC_DR_WD_Pos         (13U)
#define HPSYS_CFG_RTC_DR_WD_Msk         (0x7UL << HPSYS_CFG_RTC_DR_WD_Pos)
#define HPSYS_CFG_RTC_DR_WD             HPSYS_CFG_RTC_DR_WD_Msk
#define HPSYS_CFG_RTC_DR_YU_Pos         (16U)
#define HPSYS_CFG_RTC_DR_YU_Msk         (0xFUL << HPSYS_CFG_RTC_DR_YU_Pos)
#define HPSYS_CFG_RTC_DR_YU             HPSYS_CFG_RTC_DR_YU_Msk
#define HPSYS_CFG_RTC_DR_YT_Pos         (20U)
#define HPSYS_CFG_RTC_DR_YT_Msk         (0xFUL << HPSYS_CFG_RTC_DR_YT_Pos)
#define HPSYS_CFG_RTC_DR_YT             HPSYS_CFG_RTC_DR_YT_Msk
#define HPSYS_CFG_RTC_DR_CB_Pos         (24U)
#define HPSYS_CFG_RTC_DR_CB_Msk         (0x1UL << HPSYS_CFG_RTC_DR_CB_Pos)
#define HPSYS_CFG_RTC_DR_CB             HPSYS_CFG_RTC_DR_CB_Msk
#define HPSYS_CFG_RTC_DR_ERR_Pos        (31U)
#define HPSYS_CFG_RTC_DR_ERR_Msk        (0x1UL << HPSYS_CFG_RTC_DR_ERR_Pos)
#define HPSYS_CFG_RTC_DR_ERR            HPSYS_CFG_RTC_DR_ERR_Msk

/***************** Bit definition for HPSYS_CFG_DBGR register *****************/
#define HPSYS_CFG_DBGR_HP2LP_NMI_Pos    (28U)
#define HPSYS_CFG_DBGR_HP2LP_NMI_Msk    (0x1UL << HPSYS_CFG_DBGR_HP2LP_NMI_Pos)
#define HPSYS_CFG_DBGR_HP2LP_NMI        HPSYS_CFG_DBGR_HP2LP_NMI_Msk
#define HPSYS_CFG_DBGR_LP2HP_NMIE_Pos   (29U)
#define HPSYS_CFG_DBGR_LP2HP_NMIE_Msk   (0x1UL << HPSYS_CFG_DBGR_LP2HP_NMIE_Pos)
#define HPSYS_CFG_DBGR_LP2HP_NMIE       HPSYS_CFG_DBGR_LP2HP_NMIE_Msk
#define HPSYS_CFG_DBGR_LP2HP_NMIF_Pos   (30U)
#define HPSYS_CFG_DBGR_LP2HP_NMIF_Msk   (0x1UL << HPSYS_CFG_DBGR_LP2HP_NMIF_Pos)
#define HPSYS_CFG_DBGR_LP2HP_NMIF       HPSYS_CFG_DBGR_LP2HP_NMIF_Msk

/*************** Bit definition for HPSYS_CFG_CAU2_CR register ****************/
#define HPSYS_CFG_CAU2_CR_HPBG_VDDPSW_EN_Pos  (0U)
#define HPSYS_CFG_CAU2_CR_HPBG_VDDPSW_EN_Msk  (0x1UL << HPSYS_CFG_CAU2_CR_HPBG_VDDPSW_EN_Pos)
#define HPSYS_CFG_CAU2_CR_HPBG_VDDPSW_EN  HPSYS_CFG_CAU2_CR_HPBG_VDDPSW_EN_Msk
#define HPSYS_CFG_CAU2_CR_HPBG_EN_Pos   (1U)
#define HPSYS_CFG_CAU2_CR_HPBG_EN_Msk   (0x1UL << HPSYS_CFG_CAU2_CR_HPBG_EN_Pos)
#define HPSYS_CFG_CAU2_CR_HPBG_EN       HPSYS_CFG_CAU2_CR_HPBG_EN_Msk
#define HPSYS_CFG_CAU2_CR_DC_TR_Pos     (4U)
#define HPSYS_CFG_CAU2_CR_DC_TR_Msk     (0x7UL << HPSYS_CFG_CAU2_CR_DC_TR_Pos)
#define HPSYS_CFG_CAU2_CR_DC_TR         HPSYS_CFG_CAU2_CR_DC_TR_Msk
#define HPSYS_CFG_CAU2_CR_DC_BR_Pos     (7U)
#define HPSYS_CFG_CAU2_CR_DC_BR_Msk     (0x7UL << HPSYS_CFG_CAU2_CR_DC_BR_Pos)
#define HPSYS_CFG_CAU2_CR_DC_BR         HPSYS_CFG_CAU2_CR_DC_BR_Msk
#define HPSYS_CFG_CAU2_CR_DC_MR_Pos     (10U)
#define HPSYS_CFG_CAU2_CR_DC_MR_Msk     (0x7UL << HPSYS_CFG_CAU2_CR_DC_MR_Pos)
#define HPSYS_CFG_CAU2_CR_DC_MR         HPSYS_CFG_CAU2_CR_DC_MR_Msk

/************** Bit definition for HPSYS_CFG_CAU2_RSVD1 register **************/
#define HPSYS_CFG_CAU2_RSVD1_RESERVE0_Pos  (0U)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE0_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD1_RESERVE0_Pos)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE0   HPSYS_CFG_CAU2_RSVD1_RESERVE0_Msk
#define HPSYS_CFG_CAU2_RSVD1_RESERVE1_Pos  (8U)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE1_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD1_RESERVE1_Pos)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE1   HPSYS_CFG_CAU2_RSVD1_RESERVE1_Msk
#define HPSYS_CFG_CAU2_RSVD1_RESERVE2_Pos  (16U)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE2_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD1_RESERVE2_Pos)
#define HPSYS_CFG_CAU2_RSVD1_RESERVE2   HPSYS_CFG_CAU2_RSVD1_RESERVE2_Msk

/************** Bit definition for HPSYS_CFG_CAU2_RSVD2 register **************/
#define HPSYS_CFG_CAU2_RSVD2_RESERVE3_Pos  (0U)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE3_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD2_RESERVE3_Pos)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE3   HPSYS_CFG_CAU2_RSVD2_RESERVE3_Msk
#define HPSYS_CFG_CAU2_RSVD2_RESERVE4_Pos  (8U)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE4_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD2_RESERVE4_Pos)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE4   HPSYS_CFG_CAU2_RSVD2_RESERVE4_Msk
#define HPSYS_CFG_CAU2_RSVD2_RESERVE5_Pos  (16U)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE5_Msk  (0xFFUL << HPSYS_CFG_CAU2_RSVD2_RESERVE5_Pos)
#define HPSYS_CFG_CAU2_RSVD2_RESERVE5   HPSYS_CFG_CAU2_RSVD2_RESERVE5_Msk

/**************** Bit definition for HPSYS_CFG_BISTCR register ****************/
#define HPSYS_CFG_BISTCR_BIST_MODE_Pos  (0U)
#define HPSYS_CFG_BISTCR_BIST_MODE_Msk  (0x1UL << HPSYS_CFG_BISTCR_BIST_MODE_Pos)
#define HPSYS_CFG_BISTCR_BIST_MODE      HPSYS_CFG_BISTCR_BIST_MODE_Msk
#define HPSYS_CFG_BISTCR_BIST_DONE_Pos  (1U)
#define HPSYS_CFG_BISTCR_BIST_DONE_Msk  (0x1UL << HPSYS_CFG_BISTCR_BIST_DONE_Pos)
#define HPSYS_CFG_BISTCR_BIST_DONE      HPSYS_CFG_BISTCR_BIST_DONE_Msk
#define HPSYS_CFG_BISTCR_BIST_FAIL_Pos  (2U)
#define HPSYS_CFG_BISTCR_BIST_FAIL_Msk  (0x1UL << HPSYS_CFG_BISTCR_BIST_FAIL_Pos)
#define HPSYS_CFG_BISTCR_BIST_FAIL      HPSYS_CFG_BISTCR_BIST_FAIL_Msk

/**************** Bit definition for HPSYS_CFG_BISTR register *****************/
#define HPSYS_CFG_BISTR_BIST_FAIL_ITCM_Pos  (0U)
#define HPSYS_CFG_BISTR_BIST_FAIL_ITCM_Msk  (0x3UL << HPSYS_CFG_BISTR_BIST_FAIL_ITCM_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_ITCM  HPSYS_CFG_BISTR_BIST_FAIL_ITCM_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_DTCM_Pos  (2U)
#define HPSYS_CFG_BISTR_BIST_FAIL_DTCM_Msk  (0xFUL << HPSYS_CFG_BISTR_BIST_FAIL_DTCM_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_DTCM  HPSYS_CFG_BISTR_BIST_FAIL_DTCM_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_RAM_Pos  (6U)
#define HPSYS_CFG_BISTR_BIST_FAIL_RAM_Msk  (0xFUL << HPSYS_CFG_BISTR_BIST_FAIL_RAM_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_RAM   HPSYS_CFG_BISTR_BIST_FAIL_RAM_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_HCPU_Pos  (12U)
#define HPSYS_CFG_BISTR_BIST_FAIL_HCPU_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_HCPU_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_HCPU  HPSYS_CFG_BISTR_BIST_FAIL_HCPU_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_EPIC_Pos  (13U)
#define HPSYS_CFG_BISTR_BIST_FAIL_EPIC_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_EPIC_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_EPIC  HPSYS_CFG_BISTR_BIST_FAIL_EPIC_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_EZIP_Pos  (14U)
#define HPSYS_CFG_BISTR_BIST_FAIL_EZIP_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_EZIP_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_EZIP  HPSYS_CFG_BISTR_BIST_FAIL_EZIP_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_EXTDMA_Pos  (15U)
#define HPSYS_CFG_BISTR_BIST_FAIL_EXTDMA_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_EXTDMA_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_EXTDMA  HPSYS_CFG_BISTR_BIST_FAIL_EXTDMA_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC1_Pos  (16U)
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC1_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_SDMMC1_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC1  HPSYS_CFG_BISTR_BIST_FAIL_SDMMC1_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC2_Pos  (17U)
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC2_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_SDMMC2_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_SDMMC2  HPSYS_CFG_BISTR_BIST_FAIL_SDMMC2_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_USBC_Pos  (18U)
#define HPSYS_CFG_BISTR_BIST_FAIL_USBC_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_USBC_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_USBC  HPSYS_CFG_BISTR_BIST_FAIL_USBC_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_CAN1_Pos  (19U)
#define HPSYS_CFG_BISTR_BIST_FAIL_CAN1_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_CAN1_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_CAN1  HPSYS_CFG_BISTR_BIST_FAIL_CAN1_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_FFT1_Pos  (20U)
#define HPSYS_CFG_BISTR_BIST_FAIL_FFT1_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_FFT1_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_FFT1  HPSYS_CFG_BISTR_BIST_FAIL_FFT1_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_AUD_Pos  (21U)
#define HPSYS_CFG_BISTR_BIST_FAIL_AUD_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_AUD_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_AUD   HPSYS_CFG_BISTR_BIST_FAIL_AUD_Msk
#define HPSYS_CFG_BISTR_BIST_FAIL_LCDC1_Pos  (25U)
#define HPSYS_CFG_BISTR_BIST_FAIL_LCDC1_Msk  (0x1UL << HPSYS_CFG_BISTR_BIST_FAIL_LCDC1_Pos)
#define HPSYS_CFG_BISTR_BIST_FAIL_LCDC1  HPSYS_CFG_BISTR_BIST_FAIL_LCDC1_Msk

/*************** Bit definition for HPSYS_CFG_SYS_RSVD register ***************/
#define HPSYS_CFG_SYS_RSVD_RESERVE0_Pos  (0U)
#define HPSYS_CFG_SYS_RSVD_RESERVE0_Msk  (0xFFUL << HPSYS_CFG_SYS_RSVD_RESERVE0_Pos)
#define HPSYS_CFG_SYS_RSVD_RESERVE0     HPSYS_CFG_SYS_RSVD_RESERVE0_Msk
#define HPSYS_CFG_SYS_RSVD_RESERVE1_Pos  (8U)
#define HPSYS_CFG_SYS_RSVD_RESERVE1_Msk  (0xFFUL << HPSYS_CFG_SYS_RSVD_RESERVE1_Pos)
#define HPSYS_CFG_SYS_RSVD_RESERVE1     HPSYS_CFG_SYS_RSVD_RESERVE1_Msk
#define HPSYS_CFG_SYS_RSVD_RESERVE2_Pos  (16U)
#define HPSYS_CFG_SYS_RSVD_RESERVE2_Msk  (0xFFUL << HPSYS_CFG_SYS_RSVD_RESERVE2_Pos)
#define HPSYS_CFG_SYS_RSVD_RESERVE2     HPSYS_CFG_SYS_RSVD_RESERVE2_Msk
#define HPSYS_CFG_SYS_RSVD_RESERVE3_Pos  (24U)
#define HPSYS_CFG_SYS_RSVD_RESERVE3_Msk  (0xFFUL << HPSYS_CFG_SYS_RSVD_RESERVE3_Pos)
#define HPSYS_CFG_SYS_RSVD_RESERVE3     HPSYS_CFG_SYS_RSVD_RESERVE3_Msk

/**************** Bit definition for HPSYS_CFG_LPIRQ register *****************/
#define HPSYS_CFG_LPIRQ_SEL0_Pos        (0U)
#define HPSYS_CFG_LPIRQ_SEL0_Msk        (0x3FUL << HPSYS_CFG_LPIRQ_SEL0_Pos)
#define HPSYS_CFG_LPIRQ_SEL0            HPSYS_CFG_LPIRQ_SEL0_Msk
#define HPSYS_CFG_LPIRQ_IF0_Pos         (7U)
#define HPSYS_CFG_LPIRQ_IF0_Msk         (0x1UL << HPSYS_CFG_LPIRQ_IF0_Pos)
#define HPSYS_CFG_LPIRQ_IF0             HPSYS_CFG_LPIRQ_IF0_Msk
#define HPSYS_CFG_LPIRQ_SEL1_Pos        (8U)
#define HPSYS_CFG_LPIRQ_SEL1_Msk        (0x3FUL << HPSYS_CFG_LPIRQ_SEL1_Pos)
#define HPSYS_CFG_LPIRQ_SEL1            HPSYS_CFG_LPIRQ_SEL1_Msk
#define HPSYS_CFG_LPIRQ_IF1_Pos         (15U)
#define HPSYS_CFG_LPIRQ_IF1_Msk         (0x1UL << HPSYS_CFG_LPIRQ_IF1_Pos)
#define HPSYS_CFG_LPIRQ_IF1             HPSYS_CFG_LPIRQ_IF1_Msk
#define HPSYS_CFG_LPIRQ_SEL2_Pos        (16U)
#define HPSYS_CFG_LPIRQ_SEL2_Msk        (0x3FUL << HPSYS_CFG_LPIRQ_SEL2_Pos)
#define HPSYS_CFG_LPIRQ_SEL2            HPSYS_CFG_LPIRQ_SEL2_Msk
#define HPSYS_CFG_LPIRQ_IF2_Pos         (23U)
#define HPSYS_CFG_LPIRQ_IF2_Msk         (0x1UL << HPSYS_CFG_LPIRQ_IF2_Pos)
#define HPSYS_CFG_LPIRQ_IF2             HPSYS_CFG_LPIRQ_IF2_Msk
#define HPSYS_CFG_LPIRQ_SEL3_Pos        (24U)
#define HPSYS_CFG_LPIRQ_SEL3_Msk        (0x3FUL << HPSYS_CFG_LPIRQ_SEL3_Pos)
#define HPSYS_CFG_LPIRQ_SEL3            HPSYS_CFG_LPIRQ_SEL3_Msk
#define HPSYS_CFG_LPIRQ_IF3_Pos         (31U)
#define HPSYS_CFG_LPIRQ_IF3_Msk         (0x1UL << HPSYS_CFG_LPIRQ_IF3_Pos)
#define HPSYS_CFG_LPIRQ_IF3             HPSYS_CFG_LPIRQ_IF3_Msk

/**************** Bit definition for HPSYS_CFG_PATCH1 register ****************/
#define HPSYS_CFG_PATCH1_ADDR_Pos       (2U)
#define HPSYS_CFG_PATCH1_ADDR_Msk       (0x7FFFUL << HPSYS_CFG_PATCH1_ADDR_Pos)
#define HPSYS_CFG_PATCH1_ADDR           HPSYS_CFG_PATCH1_ADDR_Msk
#define HPSYS_CFG_PATCH1_EN_Pos         (31U)
#define HPSYS_CFG_PATCH1_EN_Msk         (0x1UL << HPSYS_CFG_PATCH1_EN_Pos)
#define HPSYS_CFG_PATCH1_EN             HPSYS_CFG_PATCH1_EN_Msk

/**************** Bit definition for HPSYS_CFG_PATCH2 register ****************/
#define HPSYS_CFG_PATCH2_ADDR_Pos       (2U)
#define HPSYS_CFG_PATCH2_ADDR_Msk       (0xFFFFUL << HPSYS_CFG_PATCH2_ADDR_Pos)
#define HPSYS_CFG_PATCH2_ADDR           HPSYS_CFG_PATCH2_ADDR_Msk
#define HPSYS_CFG_PATCH2_EN_Pos         (31U)
#define HPSYS_CFG_PATCH2_EN_Msk         (0x1UL << HPSYS_CFG_PATCH2_EN_Pos)
#define HPSYS_CFG_PATCH2_EN             HPSYS_CFG_PATCH2_EN_Msk

/**************** Bit definition for HPSYS_CFG_PATCH3 register ****************/
#define HPSYS_CFG_PATCH3_ADDR_Pos       (2U)
#define HPSYS_CFG_PATCH3_ADDR_Msk       (0x1FFFFUL << HPSYS_CFG_PATCH3_ADDR_Pos)
#define HPSYS_CFG_PATCH3_ADDR           HPSYS_CFG_PATCH3_ADDR_Msk
#define HPSYS_CFG_PATCH3_EN_Pos         (31U)
#define HPSYS_CFG_PATCH3_EN_Msk         (0x1UL << HPSYS_CFG_PATCH3_EN_Pos)
#define HPSYS_CFG_PATCH3_EN             HPSYS_CFG_PATCH3_EN_Msk

/**************** Bit definition for HPSYS_CFG_ROMCR0 register ****************/
#define HPSYS_CFG_ROMCR0_CMP_Pos        (0U)
#define HPSYS_CFG_ROMCR0_CMP_Msk        (0xFFFFFFFFUL << HPSYS_CFG_ROMCR0_CMP_Pos)
#define HPSYS_CFG_ROMCR0_CMP            HPSYS_CFG_ROMCR0_CMP_Msk

/**************** Bit definition for HPSYS_CFG_ROMCR1 register ****************/
#define HPSYS_CFG_ROMCR1_CMP_Pos        (0U)
#define HPSYS_CFG_ROMCR1_CMP_Msk        (0xFFFFFFFFUL << HPSYS_CFG_ROMCR1_CMP_Pos)
#define HPSYS_CFG_ROMCR1_CMP            HPSYS_CFG_ROMCR1_CMP_Msk

/**************** Bit definition for HPSYS_CFG_ROMCR2 register ****************/
#define HPSYS_CFG_ROMCR2_CMP_Pos        (0U)
#define HPSYS_CFG_ROMCR2_CMP_Msk        (0xFFFFFFFFUL << HPSYS_CFG_ROMCR2_CMP_Pos)
#define HPSYS_CFG_ROMCR2_CMP            HPSYS_CFG_ROMCR2_CMP_Msk

/**************** Bit definition for HPSYS_CFG_ROMCR3 register ****************/
#define HPSYS_CFG_ROMCR3_CMP_Pos        (0U)
#define HPSYS_CFG_ROMCR3_CMP_Msk        (0xFFFFFFFFUL << HPSYS_CFG_ROMCR3_CMP_Pos)
#define HPSYS_CFG_ROMCR3_CMP            HPSYS_CFG_ROMCR3_CMP_Msk

/**************** Bit definition for HPSYS_CFG_SYSCR register *****************/
#define HPSYS_CFG_SYSCR_WDT1_REBOOT_Pos  (0U)
#define HPSYS_CFG_SYSCR_WDT1_REBOOT_Msk  (0x1UL << HPSYS_CFG_SYSCR_WDT1_REBOOT_Pos)
#define HPSYS_CFG_SYSCR_WDT1_REBOOT     HPSYS_CFG_SYSCR_WDT1_REBOOT_Msk
#define HPSYS_CFG_SYSCR_HEXOKAYC_Pos    (1U)
#define HPSYS_CFG_SYSCR_HEXOKAYC_Msk    (0x1UL << HPSYS_CFG_SYSCR_HEXOKAYC_Pos)
#define HPSYS_CFG_SYSCR_HEXOKAYC        HPSYS_CFG_SYSCR_HEXOKAYC_Msk
#define HPSYS_CFG_SYSCR_HEXOKAYS_Pos    (2U)
#define HPSYS_CFG_SYSCR_HEXOKAYS_Msk    (0x1UL << HPSYS_CFG_SYSCR_HEXOKAYS_Pos)
#define HPSYS_CFG_SYSCR_HEXOKAYS        HPSYS_CFG_SYSCR_HEXOKAYS_Msk
#define HPSYS_CFG_SYSCR_REMAP_Pos       (3U)
#define HPSYS_CFG_SYSCR_REMAP_Msk       (0x1UL << HPSYS_CFG_SYSCR_REMAP_Pos)
#define HPSYS_CFG_SYSCR_REMAP           HPSYS_CFG_SYSCR_REMAP_Msk
#define HPSYS_CFG_SYSCR_SDNAND_Pos      (4U)
#define HPSYS_CFG_SYSCR_SDNAND_Msk      (0x1UL << HPSYS_CFG_SYSCR_SDNAND_Pos)
#define HPSYS_CFG_SYSCR_SDNAND          HPSYS_CFG_SYSCR_SDNAND_Msk

/************** Bit definition for HPSYS_CFG_I2C1_PINR register ***************/
#define HPSYS_CFG_I2C1_PINR_SCL_PIN_Pos  (0U)
#define HPSYS_CFG_I2C1_PINR_SCL_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C1_PINR_SCL_PIN_Pos)
#define HPSYS_CFG_I2C1_PINR_SCL_PIN     HPSYS_CFG_I2C1_PINR_SCL_PIN_Msk
#define HPSYS_CFG_I2C1_PINR_SDA_PIN_Pos  (8U)
#define HPSYS_CFG_I2C1_PINR_SDA_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C1_PINR_SDA_PIN_Pos)
#define HPSYS_CFG_I2C1_PINR_SDA_PIN     HPSYS_CFG_I2C1_PINR_SDA_PIN_Msk

/************** Bit definition for HPSYS_CFG_I2C2_PINR register ***************/
#define HPSYS_CFG_I2C2_PINR_SCL_PIN_Pos  (0U)
#define HPSYS_CFG_I2C2_PINR_SCL_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C2_PINR_SCL_PIN_Pos)
#define HPSYS_CFG_I2C2_PINR_SCL_PIN     HPSYS_CFG_I2C2_PINR_SCL_PIN_Msk
#define HPSYS_CFG_I2C2_PINR_SDA_PIN_Pos  (8U)
#define HPSYS_CFG_I2C2_PINR_SDA_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C2_PINR_SDA_PIN_Pos)
#define HPSYS_CFG_I2C2_PINR_SDA_PIN     HPSYS_CFG_I2C2_PINR_SDA_PIN_Msk

/************** Bit definition for HPSYS_CFG_I2C3_PINR register ***************/
#define HPSYS_CFG_I2C3_PINR_SCL_PIN_Pos  (0U)
#define HPSYS_CFG_I2C3_PINR_SCL_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C3_PINR_SCL_PIN_Pos)
#define HPSYS_CFG_I2C3_PINR_SCL_PIN     HPSYS_CFG_I2C3_PINR_SCL_PIN_Msk
#define HPSYS_CFG_I2C3_PINR_SDA_PIN_Pos  (8U)
#define HPSYS_CFG_I2C3_PINR_SDA_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C3_PINR_SDA_PIN_Pos)
#define HPSYS_CFG_I2C3_PINR_SDA_PIN     HPSYS_CFG_I2C3_PINR_SDA_PIN_Msk

/************** Bit definition for HPSYS_CFG_I2C4_PINR register ***************/
#define HPSYS_CFG_I2C4_PINR_SCL_PIN_Pos  (0U)
#define HPSYS_CFG_I2C4_PINR_SCL_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C4_PINR_SCL_PIN_Pos)
#define HPSYS_CFG_I2C4_PINR_SCL_PIN     HPSYS_CFG_I2C4_PINR_SCL_PIN_Msk
#define HPSYS_CFG_I2C4_PINR_SDA_PIN_Pos  (8U)
#define HPSYS_CFG_I2C4_PINR_SDA_PIN_Msk  (0x7FUL << HPSYS_CFG_I2C4_PINR_SDA_PIN_Pos)
#define HPSYS_CFG_I2C4_PINR_SDA_PIN     HPSYS_CFG_I2C4_PINR_SDA_PIN_Msk

/************* Bit definition for HPSYS_CFG_USART1_PINR register **************/
#define HPSYS_CFG_USART1_PINR_TXD_PIN_Pos  (0U)
#define HPSYS_CFG_USART1_PINR_TXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART1_PINR_TXD_PIN_Pos)
#define HPSYS_CFG_USART1_PINR_TXD_PIN   HPSYS_CFG_USART1_PINR_TXD_PIN_Msk
#define HPSYS_CFG_USART1_PINR_RXD_PIN_Pos  (8U)
#define HPSYS_CFG_USART1_PINR_RXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART1_PINR_RXD_PIN_Pos)
#define HPSYS_CFG_USART1_PINR_RXD_PIN   HPSYS_CFG_USART1_PINR_RXD_PIN_Msk
#define HPSYS_CFG_USART1_PINR_RTS_PIN_Pos  (16U)
#define HPSYS_CFG_USART1_PINR_RTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART1_PINR_RTS_PIN_Pos)
#define HPSYS_CFG_USART1_PINR_RTS_PIN   HPSYS_CFG_USART1_PINR_RTS_PIN_Msk
#define HPSYS_CFG_USART1_PINR_CTS_PIN_Pos  (24U)
#define HPSYS_CFG_USART1_PINR_CTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART1_PINR_CTS_PIN_Pos)
#define HPSYS_CFG_USART1_PINR_CTS_PIN   HPSYS_CFG_USART1_PINR_CTS_PIN_Msk

/************* Bit definition for HPSYS_CFG_USART2_PINR register **************/
#define HPSYS_CFG_USART2_PINR_TXD_PIN_Pos  (0U)
#define HPSYS_CFG_USART2_PINR_TXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART2_PINR_TXD_PIN_Pos)
#define HPSYS_CFG_USART2_PINR_TXD_PIN   HPSYS_CFG_USART2_PINR_TXD_PIN_Msk
#define HPSYS_CFG_USART2_PINR_RXD_PIN_Pos  (8U)
#define HPSYS_CFG_USART2_PINR_RXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART2_PINR_RXD_PIN_Pos)
#define HPSYS_CFG_USART2_PINR_RXD_PIN   HPSYS_CFG_USART2_PINR_RXD_PIN_Msk
#define HPSYS_CFG_USART2_PINR_RTS_PIN_Pos  (16U)
#define HPSYS_CFG_USART2_PINR_RTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART2_PINR_RTS_PIN_Pos)
#define HPSYS_CFG_USART2_PINR_RTS_PIN   HPSYS_CFG_USART2_PINR_RTS_PIN_Msk
#define HPSYS_CFG_USART2_PINR_CTS_PIN_Pos  (24U)
#define HPSYS_CFG_USART2_PINR_CTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART2_PINR_CTS_PIN_Pos)
#define HPSYS_CFG_USART2_PINR_CTS_PIN   HPSYS_CFG_USART2_PINR_CTS_PIN_Msk

/************* Bit definition for HPSYS_CFG_USART3_PINR register **************/
#define HPSYS_CFG_USART3_PINR_TXD_PIN_Pos  (0U)
#define HPSYS_CFG_USART3_PINR_TXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART3_PINR_TXD_PIN_Pos)
#define HPSYS_CFG_USART3_PINR_TXD_PIN   HPSYS_CFG_USART3_PINR_TXD_PIN_Msk
#define HPSYS_CFG_USART3_PINR_RXD_PIN_Pos  (8U)
#define HPSYS_CFG_USART3_PINR_RXD_PIN_Msk  (0x7FUL << HPSYS_CFG_USART3_PINR_RXD_PIN_Pos)
#define HPSYS_CFG_USART3_PINR_RXD_PIN   HPSYS_CFG_USART3_PINR_RXD_PIN_Msk
#define HPSYS_CFG_USART3_PINR_RTS_PIN_Pos  (16U)
#define HPSYS_CFG_USART3_PINR_RTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART3_PINR_RTS_PIN_Pos)
#define HPSYS_CFG_USART3_PINR_RTS_PIN   HPSYS_CFG_USART3_PINR_RTS_PIN_Msk
#define HPSYS_CFG_USART3_PINR_CTS_PIN_Pos  (24U)
#define HPSYS_CFG_USART3_PINR_CTS_PIN_Msk  (0x7FUL << HPSYS_CFG_USART3_PINR_CTS_PIN_Pos)
#define HPSYS_CFG_USART3_PINR_CTS_PIN   HPSYS_CFG_USART3_PINR_CTS_PIN_Msk

/************* Bit definition for HPSYS_CFG_GPTIM1_PINR register **************/
#define HPSYS_CFG_GPTIM1_PINR_CH1_PIN_Pos  (0U)
#define HPSYS_CFG_GPTIM1_PINR_CH1_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM1_PINR_CH1_PIN_Pos)
#define HPSYS_CFG_GPTIM1_PINR_CH1_PIN   HPSYS_CFG_GPTIM1_PINR_CH1_PIN_Msk
#define HPSYS_CFG_GPTIM1_PINR_CH2_PIN_Pos  (8U)
#define HPSYS_CFG_GPTIM1_PINR_CH2_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM1_PINR_CH2_PIN_Pos)
#define HPSYS_CFG_GPTIM1_PINR_CH2_PIN   HPSYS_CFG_GPTIM1_PINR_CH2_PIN_Msk
#define HPSYS_CFG_GPTIM1_PINR_CH3_PIN_Pos  (16U)
#define HPSYS_CFG_GPTIM1_PINR_CH3_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM1_PINR_CH3_PIN_Pos)
#define HPSYS_CFG_GPTIM1_PINR_CH3_PIN   HPSYS_CFG_GPTIM1_PINR_CH3_PIN_Msk
#define HPSYS_CFG_GPTIM1_PINR_CH4_PIN_Pos  (24U)
#define HPSYS_CFG_GPTIM1_PINR_CH4_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM1_PINR_CH4_PIN_Pos)
#define HPSYS_CFG_GPTIM1_PINR_CH4_PIN   HPSYS_CFG_GPTIM1_PINR_CH4_PIN_Msk

/************* Bit definition for HPSYS_CFG_GPTIM2_PINR register **************/
#define HPSYS_CFG_GPTIM2_PINR_CH1_PIN_Pos  (0U)
#define HPSYS_CFG_GPTIM2_PINR_CH1_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM2_PINR_CH1_PIN_Pos)
#define HPSYS_CFG_GPTIM2_PINR_CH1_PIN   HPSYS_CFG_GPTIM2_PINR_CH1_PIN_Msk
#define HPSYS_CFG_GPTIM2_PINR_CH2_PIN_Pos  (8U)
#define HPSYS_CFG_GPTIM2_PINR_CH2_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM2_PINR_CH2_PIN_Pos)
#define HPSYS_CFG_GPTIM2_PINR_CH2_PIN   HPSYS_CFG_GPTIM2_PINR_CH2_PIN_Msk
#define HPSYS_CFG_GPTIM2_PINR_CH3_PIN_Pos  (16U)
#define HPSYS_CFG_GPTIM2_PINR_CH3_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM2_PINR_CH3_PIN_Pos)
#define HPSYS_CFG_GPTIM2_PINR_CH3_PIN   HPSYS_CFG_GPTIM2_PINR_CH3_PIN_Msk
#define HPSYS_CFG_GPTIM2_PINR_CH4_PIN_Pos  (24U)
#define HPSYS_CFG_GPTIM2_PINR_CH4_PIN_Msk  (0x7FUL << HPSYS_CFG_GPTIM2_PINR_CH4_PIN_Pos)
#define HPSYS_CFG_GPTIM2_PINR_CH4_PIN   HPSYS_CFG_GPTIM2_PINR_CH4_PIN_Msk

/*************** Bit definition for HPSYS_CFG_ETR_PINR register ***************/
#define HPSYS_CFG_ETR_PINR_ETR1_PIN_Pos  (0U)
#define HPSYS_CFG_ETR_PINR_ETR1_PIN_Msk  (0x7FUL << HPSYS_CFG_ETR_PINR_ETR1_PIN_Pos)
#define HPSYS_CFG_ETR_PINR_ETR1_PIN     HPSYS_CFG_ETR_PINR_ETR1_PIN_Msk
#define HPSYS_CFG_ETR_PINR_ETR2_PIN_Pos  (8U)
#define HPSYS_CFG_ETR_PINR_ETR2_PIN_Msk  (0x7FUL << HPSYS_CFG_ETR_PINR_ETR2_PIN_Pos)
#define HPSYS_CFG_ETR_PINR_ETR2_PIN     HPSYS_CFG_ETR_PINR_ETR2_PIN_Msk

/************* Bit definition for HPSYS_CFG_LPTIM1_PINR register **************/
#define HPSYS_CFG_LPTIM1_PINR_IN_PIN_Pos  (0U)
#define HPSYS_CFG_LPTIM1_PINR_IN_PIN_Msk  (0x7FUL << HPSYS_CFG_LPTIM1_PINR_IN_PIN_Pos)
#define HPSYS_CFG_LPTIM1_PINR_IN_PIN    HPSYS_CFG_LPTIM1_PINR_IN_PIN_Msk
#define HPSYS_CFG_LPTIM1_PINR_OUT_PIN_Pos  (8U)
#define HPSYS_CFG_LPTIM1_PINR_OUT_PIN_Msk  (0x7FUL << HPSYS_CFG_LPTIM1_PINR_OUT_PIN_Pos)
#define HPSYS_CFG_LPTIM1_PINR_OUT_PIN   HPSYS_CFG_LPTIM1_PINR_OUT_PIN_Msk
#define HPSYS_CFG_LPTIM1_PINR_ETR_PIN_Pos  (16U)
#define HPSYS_CFG_LPTIM1_PINR_ETR_PIN_Msk  (0x7FUL << HPSYS_CFG_LPTIM1_PINR_ETR_PIN_Pos)
#define HPSYS_CFG_LPTIM1_PINR_ETR_PIN   HPSYS_CFG_LPTIM1_PINR_ETR_PIN_Msk

#endif
