/**
  ******************************************************************************
  * @file   st7789v.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for st7789v LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st7789v.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7789V
  * @brief This file provides a set of functions needed to drive the
  *        ST7789V LCD.
  * @{
  */

/** @defgroup ST7789V_Private_TypesDefinitions
  * @{
  */

#ifdef ROW_OFFSET_PLUS
    #define ROW_OFFSET  (ROW_OFFSET_PLUS)
#else
    #define ROW_OFFSET  (0)
#endif


/**
  * @}
  */

/** @defgroup ST7789V_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ST7789V_Private_Macros
  * @{
  */


//#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

void ST7789V_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
uint32_t ST7789V_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);


/**
  * @}
  */

/** @defgroup ST7789V_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ST7789V_drv =
{
    ST7789V_Init,
    ST7789V_ReadID,
    ST7789V_DisplayOn,
    ST7789V_DisplayOff,

    ST7789V_SetRegion,
    ST7789V_WritePixel,
    ST7789V_WriteMultiplePixels,

    ST7789V_ReadPixel,

    ST7789V_SetColorMode,
    ST7789V_SetBrightness,
    NULL,
    NULL

};


static LCDC_InitTypeDef lcdc_int_cfg =
{
    .lcd_itf = LCDC_INTF_SPI_NODCX_2DATA,
    .freq = 24000000,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,

    .cfg = {
        .spi = {
            .dummy_clock = 0,
            .syn_mode = HAL_LCDC_SYNC_VER,
            .vsyn_polarity = 0,
            //default_vbp=2, frame rate=82, delay=115us,
            //TODO: use us to define delay instead of cycle, delay_cycle=115*48
            .vsyn_delay_us = 1000,
            .hsyn_num = 0,
        },
    },

};

LCD_DRIVER_EXPORT(st7789v, 0x858552, &lcdc_int_cfg,
                  &ST7789V_drv,
                  ST7789V_LCD_PIXEL_WIDTH,
                  ST7789V_LCD_PIXEL_HEIGHT,
                  1);
/**
  * @}
  */

/** @defgroup ST7789V_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7789V_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ST7789V_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 4000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }

    }
}

/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ST7789V_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];

    /* Initialize ST7789V low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    HAL_Delay_us(10);
    BSP_LCD_Reset(1);

    /* Sleep In Command */
    ST7789V_WriteReg(hlcdc, ST7789V_SLEEP_IN, (uint8_t *)NULL, 0);
    /* Wait for 10ms */
    LCD_DRIVER_DELAY_MS(10);

    /* SW Reset Command */
    ST7789V_WriteReg(hlcdc, 0x01, (uint8_t *)NULL, 0);
    /* Wait for 200ms */
    LCD_DRIVER_DELAY_MS(200);

    /* Sleep Out Command */
    ST7789V_WriteReg(hlcdc, ST7789V_SLEEP_OUT, (uint8_t *)NULL, 0);
    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);

    /* Normal display for Driver Down side */
    parameter[0] = 0x00;
    ST7789V_WriteReg(hlcdc, ST7789V_NORMAL_DISPLAY, parameter, 1);

    /* Color mode 16bits/pixel */
    parameter[0] = 0x55;
    ST7789V_WriteReg(hlcdc, ST7789V_COLOR_MODE, parameter, 1);

    /* Display inversion On */
    ST7789V_WriteReg(hlcdc, ST7789V_DISPLAY_INVERSION, (uint8_t *)NULL, 0);

#if 0
    /* set LCD RAM buffer to black */
    /* Set Column address CASET */
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x00;
    parameter[3] = ST7789V_LCD_PIXEL_WIDTH - 1;
    ST7789V_WriteReg(hlcdc, ST7789V_CASET, parameter, 4);
    /* Set Row address RASET */
    parameter[0] = 0x00;
    parameter[1] = ROW_OFFSET;
    parameter[2] = ((ST7789V_LCD_PIXEL_HEIGHT + ROW_OFFSET - 1) >> 8) & 0xFF;
    parameter[3] = ((ST7789V_LCD_PIXEL_HEIGHT + ROW_OFFSET - 1) & 0xFF);
    ST7789V_WriteReg(hlcdc, ST7789V_RASET, parameter, 4);

    ST7789V_WriteReg(hlcdc, ST7789V_WRITE_RAM, (uint8_t *)NULL, 0);

    for (uint32_t i = 0; i < ST7789V_LCD_PIXEL_WIDTH * ST7789V_LCD_PIXEL_WIDTH; i++)
    {
        LCD_IO_WriteData16(0);
    }
#endif

    /* Enable SPI 2 data lane mode */
    parameter[0] = 0x10;
    ST7789V_WriteReg(hlcdc, ST7789V_SPI2EN, parameter, 1);

#if 0

    /*--------------- ST7789V Frame rate setting -------------------------------*/
    /* PORCH control setting */
    parameter[0] = 0x0C;
    parameter[1] = 0x0C;
    parameter[2] = 0x00;
    parameter[3] = 0x33;
    parameter[4] = 0x33;
    ST7789V_WriteReg(hlcdc, ST7789V_PORCH_CTRL, parameter, 5);

    /* GATE control setting */
    parameter[0] = 0x35;
    ST7789V_WriteReg(hlcdc, ST7789V_GATE_CTRL, parameter, 1);

    /*--------------- ST7789V Power setting ------------------------------------*/
    /* VCOM setting */
    parameter[0] = 0x1F;
    ST7789V_WriteReg(hlcdc, ST7789V_VCOM_SET, parameter, 1);

    /* LCM Control setting */
    parameter[0] = 0x2C;
    ST7789V_WriteReg(hlcdc, ST7789V_LCM_CTRL, parameter, 1);

    /* VDV and VRH Command Enable */
    parameter[0] = 0x01;
    parameter[1] = 0xC3;
    ST7789V_WriteReg(hlcdc, ST7789V_VDV_VRH_EN, parameter, 2);

    /* VDV Set */
    parameter[0] = 0x20;
    ST7789V_WriteReg(hlcdc, ST7789V_VDV_SET, parameter, 1);

    /* Frame Rate Control in normal mode */
    parameter[0] = 0x0F;
    ST7789V_WriteReg(hlcdc, ST7789V_FR_CTRL, parameter, 1);

    /* Power Control */
    parameter[0] = 0xA4;
    parameter[1] = 0xA1;
    ST7789V_WriteReg(hlcdc, ST7789V_POWER_CTRL, parameter, 1);

#endif


    //TODO: configuration can be different though the LCD chip is same
#ifdef LCD_USING_ROUND_TYPE1

    /*--------------- ST7789V Gamma setting ------------------------------------*/
    /* Positive Voltage Gamma Control */
    parameter[0] = 0x70;
    parameter[1] = 0x04;
    parameter[2] = 0x08;
    parameter[3] = 0x09;
    parameter[4] = 0x09;
    parameter[5] = 0x05;
    parameter[6] = 0x2A;
    parameter[7] = 0x33;
    parameter[8] = 0x41;
    parameter[9] = 0x07;
    parameter[10] = 0x13;
    parameter[11] = 0x13;
    parameter[12] = 0x29;
    parameter[13] = 0x2F;
    ST7789V_WriteReg(hlcdc, ST7789V_PV_GAMMA_CTRL, parameter, 14);

    /* Negative Voltage Gamma Control */
    parameter[0] = 0x70;
    parameter[1] = 0x03;
    parameter[2] = 0x09;
    parameter[3] = 0x0A;
    parameter[4] = 0x09;
    parameter[5] = 0x06;
    parameter[6] = 0x2B;
    parameter[7] = 0x34;
    parameter[8] = 0x41;
    parameter[9] = 0x07;
    parameter[10] = 0x12;
    parameter[11] = 0x14;
    parameter[12] = 0x28;
    parameter[13] = 0x2E;
    ST7789V_WriteReg(hlcdc, ST7789V_NV_GAMMA_CTRL, parameter, 14);

#else

    /*--------------- ST7789V Gamma setting ------------------------------------*/
    /* Positive Voltage Gamma Control */
    parameter[0] = 0xD0;
    parameter[1] = 0x13;
    parameter[2] = 0x1A;
    parameter[3] = 0x0A;
    parameter[4] = 0x0A;
    parameter[5] = 0x26;
    parameter[6] = 0x3F;
    parameter[7] = 0x54;
    parameter[8] = 0x54;
    parameter[9] = 0x18;
    parameter[10] = 0x14;
    parameter[11] = 0x14;
    parameter[12] = 0x30;
    parameter[13] = 0x33;
    ST7789V_WriteReg(hlcdc, ST7789V_PV_GAMMA_CTRL, parameter, 14);

    /* Negative Voltage Gamma Control */
    parameter[0] = 0xD0;
    parameter[1] = 0x13;
    parameter[2] = 0x1A;
    parameter[3] = 0x0A;
    parameter[4] = 0x0A;
    parameter[5] = 0x26;
    parameter[6] = 0x3F;
    parameter[7] = 0x54;
    parameter[8] = 0x54;
    parameter[9] = 0x1A;
    parameter[10] = 0x16;
    parameter[11] = 0x16;
    parameter[12] = 0x32;
    parameter[13] = 0x35;
    ST7789V_WriteReg(hlcdc, ST7789V_NV_GAMMA_CTRL, parameter, 14);
#endif

#if 0

    /* Set Column address CASET */
    parameter[0] = 0x00;
    parameter[1] = 0x00;
    parameter[2] = 0x00;
    parameter[3] = ST7789V_LCD_PIXEL_WIDTH - 1;
    ST7789V_WriteReg(hlcdc, ST7789V_CASET, parameter, 4);
    /* Set Row address RASET */
    parameter[0] = 0x00;
    parameter[1] = ROW_OFFSET;
    parameter[2] = ((ST7789V_LCD_PIXEL_HEIGHT + ROW_OFFSET - 1) >> 8) & 0xFF;
    parameter[3] = ((ST7789V_LCD_PIXEL_HEIGHT + ROW_OFFSET - 1) & 0xFF);
    ST7789V_WriteReg(hlcdc, ST7789V_RASET, parameter, 4);

    ST7789V_WriteReg(hlcdc, ST7789V_WRITE_RAM, (uint8_t *)NULL, 0);


    hwp_lcdc->CANVAS_BG = (0xFF << LCD_IF_CANVAS_BG_RED_Pos) | (0 << LCD_IF_CANVAS_BG_GREEN_Pos) | (0 << LCD_IF_CANVAS_BG_BLUE_Pos);
    hwp_lcdc->CANVAS_TL_POS = (0    << LCD_IF_CANVAS_TL_POS_X0_Pos) | (0  << LCD_IF_CANVAS_TL_POS_Y0_Pos);
    hwp_lcdc->CANVAS_BR_POS = (239  << LCD_IF_CANVAS_BR_POS_X1_Pos) | ((239) << LCD_IF_CANVAS_BR_POS_Y1_Pos);

    hwp_lcdc->COMMAND = 0x1;
#endif

    /* Set frame control, refresh rate: 82Hz*/
    parameter[0] = 0x7;
    ST7789V_WriteReg(hlcdc, ST7789V_FR_CTRL, parameter, 1);

    /* Tearing Effect Line On: Option (00h:VSYNC Only, 01h:VSYNC & HSYNC ) */
    parameter[0] = 0x00;
    ST7789V_WriteReg(hlcdc, ST7789V_TEARING_EFFECT, parameter, 1);

    /* Display ON command */
    ST7789V_DisplayOn(hlcdc);

}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ST7789V_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    data = ST7789V_ReadData(hlcdc, ST7789V_LCD_ID, 4);
    data = ((data << 1) >> 8) & 0xFFFFFF;

    return data;
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ST7789V_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    ST7789V_WriteReg(hlcdc, ST7789V_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ST7789V_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    ST7789V_WriteReg(hlcdc, ST7789V_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void ST7789V_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];

    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    ST7789V_WriteReg(hlcdc, ST7789V_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    ST7789V_WriteReg(hlcdc, ST7789V_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ST7789V_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;

    /* Set Cursor */
    ST7789V_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ST7789V_WriteReg(hlcdc, ST7789V_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void ST7789V_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST7789V_WRITE_RAM, 1);
}



/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ST7789V_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ST7789V_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ST7789V_ReadMode(hlcdc, true);

    HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);

    ST7789V_ReadMode(hlcdc, false);

    return rd_data;
}



uint32_t ST7789V_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t   parameter[2];
    uint32_t c;
    uint32_t ret_v;

    parameter[0] = 0x66;
    ST7789V_WriteReg(hlcdc, ST7789V_COLOR_MODE, parameter, 1);

    ST7789V_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    /*
        read ram need 7 dummy cycle, and it's result is 24bit color which format is:

        6bit red + 2bit dummy + 6bit green + 2bit dummy + 6bit blue + 2bit dummy

    */
    c =  ST7789V_ReadData(hlcdc, ST7789V_READ_RAM, 4);
    c <<= 1;
    //c >>= lcdc_int_cfg.dummy_clock; //revert fixed dummy cycle

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        parameter[0] = 0x55;
        ret_v = (uint32_t)(((c >> 8) & 0xF800) | ((c >> 5) & 0x7E0) | ((c >> 3) & 0X1F));
        break;

    case LCDC_PIXEL_FORMAT_RGB666:
        parameter[0] = 0x66;
        ret_v = (uint32_t)(((c >> 6) & 0x3F000) | ((c >> 4) & 0xFC0) | ((c >> 2) & 0X3F));
        break;

    case LCDC_PIXEL_FORMAT_RGB888:
        /*
           pretend that st7789v can support RGB888,

           treated as RGB666 actually(6bit R + 2bit dummy + 6bit G + 2bit dummy + 6bit B + 2bit dummy )

           lcdc NOT support RGB666
        */
        /*
            st7789 NOT support RGB888：

            fill 2bit dummy bit with 2bit MSB of color
        */
        parameter[0] = 0x66;
        ret_v = (uint32_t)((c & 0xFCFCFC) | ((c >> 6) & 0x030303));
        break;

    default:
        RT_ASSERT(0);
        break;
    }

    //rt_kprintf("ST7789V_ReadPixel %x -> %x\n",c, ret_v);


    ST7789V_WriteReg(hlcdc, ST7789V_COLOR_MODE, parameter, 1);

    return ret_v;
}


void ST7789V_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    /*

    Control interface color format
    ‘011’ = 12bit/pixel ‘101’ = 16bit/pixel ‘110’ = 18bit/pixel ‘111’ = 16M truncated

    */
    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x55;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;


    default:
        RT_ASSERT(0);
        return; //unsupport
        break;

    }

    ST7789V_WriteReg(hlcdc, ST7789V_COLOR_MODE, parameter, 1);
    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

void     ST7789V_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((uint16_t)UINT8_MAX * br / 100);
    ST7789V_WriteReg(hlcdc, ST7789V_WBRIGHT, &br, 1);
}

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
