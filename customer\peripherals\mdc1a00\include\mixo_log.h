/*
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#ifndef MIXO_COMMON_INCLUDE_MIXO_LOG_H_
#define MIXO_COMMON_INCLUDE_MIXO_LOG_H_

#include <stdarg.h>
#include <stdio.h>
#include <rtthread.h>



#ifndef MIXO_LOG_STR_SIZE_MAX
#define MIXO_LOG_STR_SIZE_MAX (120)
#endif

#define MIXO_LOG_LEVEL_DEBUG (1)
#define MIXO_LOG_LEVEL_ERROR (2)
#define MIXO_LOG_LEVEL_NONE  (3)

#ifndef MIXO_LOG_LEVEL
#define MIXO_LOG_LEVEL MIXO_LOG_LEVEL_DEBUG
#endif

#ifndef MIXO_LOG_TAG
#define MIXO_LOG_TAG "mixo_"
#endif

#define LOG_ENTER() LOGD("--> %s\n", __func__)
#define LOG_LEAVE() LOGD("<-- %s\n", __func__)

/*define weak, packed */
#if defined(__ARMCC_VERSION) && \
    (__ARMCC_VERSION >= 6010050) /* ARM Compiler V6 */
#ifndef __weak
#define __weak __attribute__((weak))
#endif
#ifndef __packed
#define __packed __attribute__((packed))
#endif
#elif defined(__GNUC__) && !defined(__CC_ARM) /* GNU Compiler */
#ifndef __weak
#define __weak __attribute__((weak))
#endif /* __weak */
#ifndef __packed
#define __packed __attribute__((__packed__))
#endif /* __packed */
#endif /* __GNUC__ */

#ifdef __cplusplus
extern "C" {
#endif

//extern void mixo_hal_puts(char* str, int len);
__weak void mixo_pal_log(const char* tag,
                         int log_level,
                         const char* format,
                         ...)
{
    (void)tag;
    (void)log_level;
    va_list arg;
    va_start(arg, format);
    char log_str[MIXO_LOG_STR_SIZE_MAX];
    int len          = vsnprintf(log_str, MIXO_LOG_STR_SIZE_MAX, format, arg);
    log_str[len]     = 0x0D;
    log_str[len + 1] = 0x0A;
    va_end(arg);

    rt_kprintf(log_str);
    //mixo_hal_puts(log_str, len + 2);
}

#ifdef __cplusplus
}
#endif

#if (MIXO_LOG_LEVEL > MIXO_LOG_LEVEL_DEBUG)
#define LOGD(...)                                                          \
    {                                                                      \
        if (0)                                                             \
        {                                                                  \
            mixo_pal_log(MIXO_LOG_TAG, MIXO_LOG_LEVEL_DEBUG, __VA_ARGS__); \
        }                                                                  \
    }
#else
#define LOGD(...)                                                      \
    {                                                                  \
        mixo_pal_log(MIXO_LOG_TAG, MIXO_LOG_LEVEL_DEBUG, __VA_ARGS__); \
    }
#endif

#if (MIXO_LOG_LEVEL > MIXO_LOG_LEVEL_ERROR)
#define LOGE(...)                                                          \
    {                                                                      \
        if (0)                                                             \
        {                                                                  \
            mixo_pal_log(MIXO_LOG_TAG, MIXO_LOG_LEVEL_ERROR, __VA_ARGS__); \
        }                                                                  \
    }
#else
#define LOGE(...)                                                      \
    {                                                                  \
        mixo_pal_log(MIXO_LOG_TAG,                                     \
                     MIXO_LOG_LEVEL_ERROR,                             \
                     "LOGE (%s:%i) ",                                  \
                     __func__,                                         \
                     __LINE__);                                        \
        mixo_pal_log(MIXO_LOG_TAG, MIXO_LOG_LEVEL_ERROR, __VA_ARGS__); \
    }
#endif

#endif  // MIXO_COMMON_INCLUDE_MIXO_LOG_H_
