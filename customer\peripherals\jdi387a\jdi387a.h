/**
  ******************************************************************************
  * @file   jdi387a.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the jdi387a.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __JDI387A_H
#define __JDI387A_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup JDI387A
  * @{
  */

/** @defgroup JDI387A_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup JDI387A_Exported_Constants
  * @{
  */

/**
  * @brief JDI387A chip IDs
  */
#define JDI387A_ID                  0x1d1

/**
  * @brief  JDI387A Size
  */
#define  JDI387A_LCD_PIXEL_WIDTH    ((uint16_t)240)
#define  JDI387A_LCD_PIXEL_HEIGHT   ((uint16_t)240)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define JDI387A_ORIENTATION_PORTRAIT         ((uint32_t)0x00) /* Portrait orientation choice of LCD screen  */
#define JDI387A_ORIENTATION_LANDSCAPE        ((uint32_t)0x01) /* Landscape orientation choice of LCD screen */
#define JDI387A_ORIENTATION_LANDSCAPE_ROT180 ((uint32_t)0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  JDI387A Registers
  */
#define JDI387A_LCD_ID             0x04
#define JDI387A_SLEEP_IN           0x10
#define JDI387A_SLEEP_OUT          0x11
#define JDI387A_PARTIAL_DISPLAY    0x12
#define JDI387A_DISPLAY_INVERSION  0x21
#define JDI387A_DISPLAY_OFF        0x28
#define JDI387A_DISPLAY_ON         0x29
#define JDI387A_WRITE_RAM          0x2C
#define JDI387A_READ_RAM           0x2E
#define JDI387A_CASET              0x2A
#define JDI387A_RASET              0x2B
#define JDI387A_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define JDI387A_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define JDI387A_TEARING_EFFECT     0x35
#define JDI387A_NORMAL_DISPLAY     0x36
#define JDI387A_IDLE_MODE_OFF      0x38
#define JDI387A_IDLE_MODE_ON       0x39
#define JDI387A_COLOR_MODE         0x3A
#define JDI387A_WBRIGHT            0x51
#define JDI387A_WCTRL              0x53
#define JDI387A_PORCH_CTRL         0xB2
#define JDI387A_FRAME_CTRL         0xB3
#define JDI387A_GATE_CTRL          0xB7
#define JDI387A_VCOM_SET           0xBB
#define JDI387A_LCM_CTRL           0xC0
#define JDI387A_VDV_VRH_EN         0xC2
#define JDI387A_VDV_SET            0xC4
#define JDI387A_VCOMH_OFFSET_SET   0xC5
#define JDI387A_FR_CTRL            0xC6
#define JDI387A_POWER_CTRL         0xD0
#define JDI387A_PV_GAMMA_CTRL      0xE0
#define JDI387A_NV_GAMMA_CTRL      0xE1
#define JDI387A_SPI2EN             0xE7


/**
  * @}
  */

/** @defgroup JDI387A_Exported_Functions
  * @{
  */
void     JDI387A_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t JDI387A_ReadID(LCDC_HandleTypeDef *hlcdc);

void     JDI387A_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     JDI387A_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void JDI387A_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void JDI387A_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void JDI387A_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t JDI387A_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void JDI387A_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void JDI387A_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */



#ifdef __cplusplus
}
#endif

#endif /* __JDI387A_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
