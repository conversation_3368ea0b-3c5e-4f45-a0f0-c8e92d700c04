/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   co5300.c
@Time    :   2025/01/06 21:23:56
*
**************************************************************************/
#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "co5300.h"
#include "log.h"

#define BSP_LCDC_USING_DSI 1
/** @addtogroup BSP
 * @{
 */

/** @addtogroup Components
 * @{
 */

/** @addtogroup CO5300
 * @brief This file provides a set of functions needed to drive the
 *        CO5300 LCD.
 * @{
 */
#define CO5300_LCD_ID 0x04
#define CO5300_SLEEP_IN 0x10
#define CO5300_SLEEP_OUT 0x11
#define CO5300_DISPLAY_OFF 0x28
#define CO5300_DISPLAY_ON 0x29
#define CO5300_WRITE_RAM 0x2C
#define CO5300_READ_RAM 0x2E
#define CO5300_CASET 0x2A
#define CO5300_RASET 0x2B
#define CO5300_TEARING_EFFECT 0x35
#define CO5300_NORMAL_DISPLAY 0x36
#define CO5300_IDLE_MODE_OFF 0x38
#define CO5300_IDLE_MODE_ON 0x39
#define CO5300_COLOR_MODE 0x3A
#define CO5300_WBRIGHT 0x51
#define CO5300_DISPLAY_CTRL 0x53
#define CO5300_HBM_WBRIGHT 0x63
#define CO5300_HBM_ENABLE 0x66
#define CO5300_CMD_PAGE_SWITCH 0xFE

/** @defgroup CO5300_Private_TypesDefinitions
 * @{
 */
// #ifdef LCD_USE_GPIO_TE
// #define LCD_CO5300_VSYNC_ENABLE 1
// #endif

#define ROW_OFFSET (0x00)
#define COL_OFFSET (0x08)

/**
 * @}
 */

/** @defgroup CO5300_Private_Defines
 * @{
 */
/**
 * @}
 */

/** @defgroup CO5300_Private_Macros
 * @{
 */
#define CO5300_LCD_PIXEL_WIDTH LCD_HOR_RES_MAX
#define CO5300_LCD_PIXEL_HEIGHT LCD_VER_RES_MAX

#define RGB_ARRAY_LEN (CO5300_LCD_PIXEL_WIDTH)

#define DEBUG

#ifdef DEBUG
#define DEBUG_PRINTF(...) LOG_I(__VA_ARGS__)
#else
#define DEBUG_PRINTF(...)
#endif

/*rm69330 start colume & row must can be divided by 2, and roi width&height too.*/
#define CO5300_ALIGN2(x) ((x) = (x) & (~1))
#define CO5300_ALIGN1(x) ((x) = (0 == ((x) & 1)) ? (x - 1) : x)

/**
 * @}
 */

/** @defgroup CO5300_Private_Variables
 * @{
 */

static const LCD_DrvOpsDef CO5300_drv =
    {
        CO5300_Init,
        CO5300_ReadID,
        CO5300_DisplayOn,
        CO5300_DisplayOff,

        CO5300_SetRegion,
        CO5300_WritePixel,
        CO5300_WriteMultiplePixels,

        CO5300_ReadPixel,

        CO5300_SetColorMode,
        CO5300_SetBrightness,
        CO5300_IdleModeOn,
        CO5300_IdleModeOff,
        NULL,
        NULL,
        NULL,
        CO5300_SetHBM,

};

// static uint16_t ArrayRGB[RGB_ARRAY_LEN] = {0};

#ifdef BSP_LCDC_USING_DSI

static const LCDC_InitTypeDef lcdc_int_cfg_dsi =
    {
        .lcd_itf = LCDC_INTF_DSI,
        .freq = DSI_FREQ_480Mbps,
        .color_mode = LCDC_PIXEL_FORMAT_RGB888, // DBI output color format,   should match with .cfg.dsi.CmdCfg.ColorCoding

        .cfg = {

            .dsi = {

                .Init = {
                    .AutomaticClockLaneControl = DSI_AUTO_CLK_LANE_CTRL_ENABLE,
                    .NumberOfLanes = DSI_ONE_DATA_LANE,
                    .TXEscapeCkdiv = 0x4,
                },

                .CmdCfg = {
                    .VirtualChannelID = 0, .CommandSize = 0xFFFF, .TearingEffectSource = DSI_TE_DSILINK,
#ifdef LCD_CO5300_VSYNC_ENABLE
                    .TEAcknowledgeRequest = DSI_TE_ACKNOWLEDGE_ENABLE, // Open TE
#else
                    .TEAcknowledgeRequest = DSI_TE_ACKNOWLEDGE_DISABLE, // Close TE
#endif                                         /* LCD_CO5300_VSYNC_ENABLE */
                    .ColorCoding = DSI_RGB888, // DSI input & output color format
                },

                .PhyTimings = {
                    .ClockLaneHS2LPTime = 35,
                    .ClockLaneLP2HSTime = 35,
                    .DataLaneHS2LPTime = 35,
                    .DataLaneLP2HSTime = 35,
                    .DataLaneMaxReadTime = 0,
                    .StopWaitTime = 0,
                },

                .HostTimeouts = {
                    .TimeoutCkdiv = 1,
                    .HighSpeedTransmissionTimeout = 0,
                    .LowPowerReceptionTimeout = 0,
                    .HighSpeedReadTimeout = 0,
                    .LowPowerReadTimeout = 0,
                    .HighSpeedWriteTimeout = 0,
                    //.HighSpeedWritePrespMode = DSI_HS_PM_DISABLE,
                    .LowPowerWriteTimeout = 0,
                    .BTATimeout = 0,
                },

                //  .LPCmd = {
                //      .LPGenShortWriteNoP    = DSI_LP_GSW0P_ENABLE,
                //      .LPGenShortWriteOneP   = DSI_LP_GSW1P_ENABLE,
                //      .LPGenShortWriteTwoP   = DSI_LP_GSW2P_ENABLE,
                //      .LPGenShortReadNoP     = DSI_LP_GSR0P_ENABLE,
                //      .LPGenShortReadOneP    = DSI_LP_GSR1P_ENABLE,
                //      .LPGenShortReadTwoP    = DSI_LP_GSR2P_ENABLE,
                //      .LPGenLongWrite        = DSI_LP_GLW_ENABLE,
                //      .LPDcsShortWriteNoP    = DSI_LP_DSW0P_ENABLE,
                //      .LPDcsShortWriteOneP   = DSI_LP_DSW1P_ENABLE,
                //      .LPDcsShortReadNoP     = DSI_LP_DSR0P_ENABLE,
                //      .LPDcsLongWrite        = DSI_LP_DLW_ENABLE,      //DSI_LP_DLW_DISABLE,      ENABLE - LP mode， DISABLE - high speed mode
                //      .LPMaxReadPacket       = DSI_LP_MRDP_ENABLE,
                //      .AcknowledgeRequest    = DSI_ACKNOWLEDGE_ENABLE, //Enable LCD error reports
                //  },

                .LPCmd = {
                    .LPGenShortWriteNoP = DSI_LP_GSW0P_ENABLE, .LPGenShortWriteOneP = DSI_LP_GSW1P_ENABLE, .LPGenShortWriteTwoP = DSI_LP_GSW2P_ENABLE, .LPGenShortReadNoP = DSI_LP_GSR0P_ENABLE, .LPGenShortReadOneP = DSI_LP_GSR1P_ENABLE, .LPGenShortReadTwoP = DSI_LP_GSR2P_ENABLE, .LPGenLongWrite = DSI_LP_GLW_ENABLE, .LPDcsShortWriteNoP = DSI_LP_DSW0P_ENABLE, .LPDcsShortWriteOneP = DSI_LP_DSW1P_ENABLE, .LPDcsShortReadNoP = DSI_LP_DSR0P_ENABLE, .LPDcsLongWrite = DSI_LP_DLW_DISABLE, .LPMaxReadPacket = DSI_LP_MRDP_ENABLE,
                    .AcknowledgeRequest = DSI_ACKNOWLEDGE_DISABLE, // disable LCD error reports
                },

                .vsyn_delay_us = 0,
            },
        },
};
#endif /* BSP_LCDC_USING_DSI */

static void CO5300_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
static uint32_t CO5300_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);
static void CO5300_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable);

LCD_DRIVER_EXPORT(co5300, CO5300_ID, &lcdc_int_cfg_dsi,
                  &CO5300_drv,
                  CO5300_LCD_PIXEL_WIDTH,
                  CO5300_LCD_PIXEL_HEIGHT,
                  1);

LCD_DRIVER_EXPORT(co5300_product, CO5300_ID_PRODUCT, &lcdc_int_cfg_dsi,
                  &CO5300_drv,
                  CO5300_LCD_PIXEL_WIDTH,
                  CO5300_LCD_PIXEL_HEIGHT,
                  1);
/**
 * @}
 */

/** @defgroup CO5300_Private_FunctionPrototypes
 * @{
 */

/**
 * @}
 */

/** @defgroup CO5300_Private_Functions
 * @{
 */

/**
 * @brief  spi read/write mode
 * @param  enable: false - write spi mode |  true - read spi mode
 * @retval None
 */
void CO5300_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
}

void CO5300_Clear(LCDC_HandleTypeDef *hlcdc)
{
    HAL_LCDC_Next_Frame_TE(hlcdc, 0);
    HAL_LCDC_LayerSetFormat(hlcdc, HAL_LCDC_LAYER_DEFAULT, LCDC_PIXEL_FORMAT_RGB565);
    HAL_LCDC_LayerDisable(hlcdc, HAL_LCDC_LAYER_DEFAULT);
    HAL_LCDC_SetBgColor(hlcdc, 0x0, 0x0, 0x0);
    HAL_LCDC_SendLayerData2Reg(hlcdc, CO5300_WRITE_RAM, 1);
    HAL_LCDC_LayerEnable(hlcdc, HAL_LCDC_LAYER_DEFAULT);
}

/**
 * @brief  Power on the LCD.
 * @param  None
 * @retval None
 */
void CO5300_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t parameter[4];

    // rt_kprintf("CO5300_Init\n");
    /* Initialize CO5300 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg_dsi, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0); // Reset LCD
    LCD_DRIVER_DELAY_MS(10);
    BSP_LCD_Reset(1);
    LCD_DRIVER_DELAY_MS(10); // LCD must at sleep in mode after power on, 10ms is enough

    parameter[0] = 0x00;
    CO5300_WriteReg(hlcdc, CO5300_CMD_PAGE_SWITCH, parameter, 1);

#ifdef LCD_CO5300_VSYNC_ENABLE
    parameter[0] = 0x00;
    CO5300_WriteReg(hlcdc, CO5300_TEARING_EFFECT, parameter, 1);
    HAL_LCDC_Next_Frame_TE(hlcdc, 0);
#endif

    parameter[0] = 0x20;
    CO5300_WriteReg(hlcdc, CO5300_DISPLAY_CTRL, parameter, 1);

    parameter[0] = 0x00;
    CO5300_WriteReg(hlcdc, CO5300_WBRIGHT, parameter, 1);

    parameter[0] = 0x00;
    CO5300_WriteReg(hlcdc, CO5300_HBM_WBRIGHT, parameter, 1);

    CO5300_SetRegion(hlcdc, 0, 0, CO5300_LCD_PIXEL_WIDTH - 1, CO5300_LCD_PIXEL_HEIGHT - 1);

    CO5300_WriteReg(hlcdc, CO5300_SLEEP_OUT, (uint8_t *)NULL, 0);
    /* Do not enable lcd display on here, it may lead to unexpected white line */
    //LCD_DRIVER_DELAY_MS(60);
    //CO5300_DisplayOn(hlcdc);
    //LCD_DRIVER_DELAY_MS(50);
    CO5300_Clear(hlcdc);
}

/**
 * @brief  Disables the Display.
 * @param  None
 * @retval LCD Register Value.
 */
uint32_t CO5300_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data = 0;
    data = CO5300_ReadData(hlcdc, CO5300_LCD_ID, 3);
    rt_kprintf("CO5300_ReadID 0x%x \n", data);

    return data;
}

/**
 * @brief  Enables the Display.
 * @param  None
 * @retval None
 */
void CO5300_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    CO5300_WriteReg(hlcdc, CO5300_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
 * @brief  Disables the Display.
 * @param  None
 * @retval None
 */
void CO5300_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    CO5300_WriteReg(hlcdc, CO5300_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

/**
 * @brief  Set the hbm mode
 * @param  enable: true - enable hbm mode | false - disable hbm mode
 * @retval error code
 */
void CO5300_SetHBM(LCDC_HandleTypeDef *hlcdc, bool enable, uint8_t br)
{
    uint8_t arg = 0x00;
    CO5300_WriteReg(hlcdc, CO5300_CMD_PAGE_SWITCH, &arg, 1);
    if (enable)
    {
        arg = 0x02;
        CO5300_WriteReg(hlcdc, CO5300_HBM_ENABLE, &arg, 1);
        arg = (uint8_t)((int)255 * br / 100);
        rt_kprintf("set hbm mode,br %d bright:0x%02x\n",br, arg);
        CO5300_WriteReg(hlcdc, CO5300_HBM_WBRIGHT, &arg, 1);
    }
    else
    {
        arg = 0x00;
        CO5300_WriteReg(hlcdc, CO5300_HBM_ENABLE, &arg, 1);
    }

}

void CO5300_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t parameter[4];

    //  rt_kprintf("region:%d,%d,%d,%d\n",Xpos0, Ypos0, Xpos1, Ypos1);

    RT_ASSERT(0 == (Xpos0 & 1));
    RT_ASSERT(0 == (Ypos0 & 1));
    RT_ASSERT(1 == (Xpos1 & 1));
    RT_ASSERT(1 == (Ypos1 & 1));

    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Xpos0 += COL_OFFSET;
    Xpos1 += COL_OFFSET;

    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    CO5300_WriteReg(hlcdc, CO5300_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    CO5300_WriteReg(hlcdc, CO5300_RASET, parameter, 4);
}

/**
 * @brief  Writes pixel.
 * @param  Xpos: specifies the X position.
 * @param  Ypos: specifies the Y position.
 * @param  RGBCode: the RGB pixel color
 * @retval None
 */
void CO5300_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;
    uint8_t parameter[4];

    /* Set Cursor */
    CO5300_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    CO5300_WriteReg(hlcdc, CO5300_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void CO5300_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;
    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    HAL_LCDC_SendLayerData2Reg_IT(hlcdc, CO5300_WRITE_RAM, 1);
}

/**
 * @brief  Writes  to the selected LCD register.
 * @param  LCD_Reg: address of the selected register.
 * @retval None
 */
void CO5300_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
}

/**
 * @brief  Reads the selected LCD Register.
 * @param  RegValue: Address of the register to read
 * @param  ReadSize: Number of bytes to read
 * @retval LCD Register Value.
 */
uint32_t CO5300_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    CO5300_ReadMode(hlcdc, false);
    return rd_data;
}

uint32_t CO5300_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    DEBUG_PRINTF("NOT support read pixel\n");

    return 0; // Not support read pixel
}

void CO5300_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    // uint8_t   parameter[2];
    // switch (color_mode)
    // {
    // case RTGRAPHIC_PIXEL_FORMAT_RGB565:
    //     /* Color mode 16bits/pixel */
    //     parameter[0] = 0x77;
    //     break;
    // default:
    //     parameter[0] = 0x77;
    //     break;
    // }

    // CO5300_WriteReg(hlcdc, CO5300_COLOR_MODE, parameter, 1);

    // HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg_dsi.color_mode);
}

#define CO5300_BRIGHTNESS_MAX 0xFF

void CO5300_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    // rt_kprintf("CO5300_SetBrightness = %d\n", br);
    uint8_t bright = (uint8_t)((int)CO5300_BRIGHTNESS_MAX * br / 100);
    CO5300_WriteReg(hlcdc, CO5300_WBRIGHT, &bright, 1);
}
/**
 * @brief  Enable the Display idle mode.
 * @param  None
 * @retval None
 */
void CO5300_IdleModeOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Idle mode On */
    CO5300_WriteReg(hlcdc, CO5300_IDLE_MODE_ON, (uint8_t *)NULL, 0);
}

/**
 * @brief  Disables the Display idle mode.
 * @param  None
 * @retval None
 */
void CO5300_IdleModeOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Idle mode Off */
    CO5300_WriteReg(hlcdc, CO5300_IDLE_MODE_OFF, (uint8_t *)NULL, 0);
}

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
