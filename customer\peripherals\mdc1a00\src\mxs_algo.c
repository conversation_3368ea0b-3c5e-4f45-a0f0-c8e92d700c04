/*
 * Copyright (c) 2021 MixoSense Technology Ltd <<EMAIL>>.
 *
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology
 * Ltd.
 *
 */

#include "mxs_algo.h"

#include "mixo_log.h"

#ifndef ABS
#define ABS(x) ((x) > 0 ? (x) : -(x))
#endif

typedef struct
{
    int press_th;
    int count;
    bool dx_dominate;
    bool press;
    int y;
    int theta;
    int t;
} angle_press_t;

static angle_press_t angle_press_default = {
    .press_th    = 10,
    .count       = 0,
    .dx_dominate = false,
    .press       = false,
    .y           = 0,
    .theta       = 0,
    .t           = 100,
};

int mxs_algo_config(int cpi_x, int cpi_y)
{
    (void)cpi_x;
    (void)cpi_y;
    return 0;
}

int mxs_algo_get_event(int16_t dx,
                       int16_t dy,
                       uint32_t timestamp,
                       mxs_algo_motion_event_t* event,
                       int* displacement)
{
    (void)timestamp;
    angle_press_t* ap = &angle_press_default;
    if (ABS(dx) > ABS(dy) - 1)  // 使采样间隔为1ms的时候能成功识别和计算
    {
        ap->dx_dominate = true;
        if (ABS(ap->t) >= 100)
        {
            ap->t = 100;
            // 为了适应主控采集频率和后端使用频率不一致的情况，可能存在精度问题
            ap->theta = dx;
        }
        else
        {
            ap->theta = 0;
        }
    }
    else
    {
        ap->dx_dominate = false;
        ap->y += dy;
    }
    if (ap->y <= -ap->press_th)
    {
        ap->press = true;
        ap->y     = -ap->press_th;
        if (ap->dx_dominate)
        {
            ap->y = 0;
            ap->t = 0;
        }
    }
    else if (ap->y >= ap->press_th)
    {
        ap->y     = ap->press_th;
        ap->press = false;
        if (ap->dx_dominate)
        {
            ap->y = 0;
            ap->count += 1;
            ap->t = 0;
        }
    }
    if (ABS(ap->t) < 100)
    {
        ap->t += dx;
    }

    // The logic comes from the SDK
    *displacement = 0;

    bool pressed = ap->press;
    int angle    = ap->theta;
    *event       = MXS_ALGO_MOTION_NONE;
    if (pressed == false && angle == 0)
    {
        *event = MXS_ALGO_MOTION_RELEASE;
    }
    else if (pressed == false && angle != 0)
    {
        *event        = MXS_ALGO_MOTION_ROTATE;
        *displacement = dx;
    }
    else if (pressed == true && angle == 0)
    {
        *event = MXS_ALGO_MOTION_PRESS;
    }
    else
    {
        *event = MXS_ALGO_MOTION_PRESS;
    }

    return 0;
}
