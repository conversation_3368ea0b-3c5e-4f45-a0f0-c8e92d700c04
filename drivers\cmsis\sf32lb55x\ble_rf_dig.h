/**
  ******************************************************************************
  * @file   ble_rf_dig.h
  * <AUTHOR> software development team
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */
#ifndef __BLE_RF_DIG_H
#define __BLE_RF_DIG_H

typedef struct
{
    __IO uint32_t DCO_REG1;
    __IO uint32_t DCO_REG2;
    __IO uint32_t MISC_CTRL_REG;
    __IO uint32_t FBDV_REG1;
    __IO uint32_t FBDV_REG2;
    __IO uint32_t PFDCP_REG;
    __IO uint32_t LPF_REG;
    __IO uint32_t ATEST_REG;
    __IO uint32_t DTEST_REG;
    __IO uint32_t TRF_REG1;
    __IO uint32_t TRF_REG2;
    __IO uint32_t RRF_REG;
    __IO uint32_t RBB_REG1;
    __IO uint32_t RBB_REG2;
    __IO uint32_t RBB_REG3;
    __IO uint32_t RBB_REG4;
    __IO uint32_t RBB_REG5;
    __IO uint32_t ADC_REG;
    __IO uint32_t ATSTBUF_REG;
    __IO uint32_t RSVD_REG1;
    __IO uint32_t RSVD_REG2;
    __IO uint32_t FULCAL_REG0;
    __IO uint32_t FULCAL_REG1;
    __IO uint32_t FULCAL_REG2;
    __IO uint32_t FULCAL_REG3;
    __IO uint32_t FULCAL_REG4;
    __IO uint32_t FULCAL_REG5;
    __IO uint32_t FULCAL_REG6;
    __IO uint32_t FULCAL_REG7;
    __IO uint32_t FULCAL_REG8;
    __IO uint32_t FULCAL_REG9;
    __IO uint32_t FULCAL_REG10;
    __IO uint32_t FULCAL_REG11;
    __IO uint32_t FULCAL_REG12;
    __IO uint32_t FULCAL_REG13;
    __IO uint32_t FULCAL_REG14;
    __IO uint32_t FULCAL_REG15;
    __IO uint32_t FULCAL_REG16;
    __IO uint32_t FULCAL_REG17;
    __IO uint32_t FULCAL_REG18;
    __IO uint32_t FULCAL_REG19;
    __IO uint32_t FULCAL_REG20;
    __IO uint32_t FULCAL_REG21;
    __IO uint32_t FULCAL_REG22;
    __IO uint32_t FULCAL_REG23;
    __IO uint32_t FULCAL_REG24;
    __IO uint32_t FULCAL_REG25;
    __IO uint32_t FULCAL_REG26;
    __IO uint32_t FULCAL_REG27;
    __IO uint32_t FULCAL_REG28;
    __IO uint32_t FULCAL_REG29;
    __IO uint32_t FULCAL_REG30;
    __IO uint32_t FULCAL_REG31;
    __IO uint32_t FULCAL_REG32;
    __IO uint32_t FULCAL_REG33;
    __IO uint32_t FULCAL_REG34;
    __IO uint32_t FULCAL_REG35;
    __IO uint32_t FULCAL_REG36;
    __IO uint32_t FULCAL_REG37;
    __IO uint32_t FULCAL_REG38;
    __IO uint32_t FULCAL_REG39;
    __IO uint32_t INCCAL_REG1;
    __IO uint32_t ROSCAL_REG1;
    __IO uint32_t ROSCAL_REG2;
    __IO uint32_t RCROSCAL_REG;
    __IO uint32_t PACAL_REG;
    __IO uint32_t CU_ADDR_REG0;
    __IO uint32_t CU_ADDR_REG1;
    __IO uint32_t CFG_CMD0;
    __IO uint32_t CFG_CMD1;
    __IO uint32_t CFG_CMD2;
    __IO uint32_t CFG_CMD3;
    __IO uint32_t CFG_CMD4;
    __IO uint32_t CFG_CMD5;
    __IO uint32_t CFG_CMD6;
    __IO uint32_t CFG_CMD7;
    __IO uint32_t CFG_CMD8;
    __IO uint32_t CFG_CMD9;
    __IO uint32_t CFG_CMD10;
    __IO uint32_t CFG_CMD11;
    __IO uint32_t CFG_CMD12;
    __IO uint32_t CFG_CMD13;
    __IO uint32_t CFG_CMD14;
    __IO uint32_t CFG_CMD15;
    __IO uint32_t CFG_CMD16;
    __IO uint32_t CFG_CMD17;
    __IO uint32_t CFG_CMD18;
    __IO uint32_t CFG_CMD19;
    __IO uint32_t CFG_CMD20;
    __IO uint32_t CFG_CMD21;
    __IO uint32_t CFG_CMD22;
    __IO uint32_t CFG_CMD23;
    __IO uint32_t CFG_CMD24;
    __IO uint32_t CFG_CMD25;
    __IO uint32_t CFG_CMD26;
    __IO uint32_t CFG_CMD27;
    __IO uint32_t CFG_CMD28;
    __IO uint32_t CFG_CMD29;
    __IO uint32_t CFG_CMD30;
    __IO uint32_t CFG_CMD31;
    __IO uint32_t CFG_CMD32;
    __IO uint32_t CFG_CMD33;
    __IO uint32_t CFG_CMD34;
    __IO uint32_t CFG_CMD35;
    __IO uint32_t CFG_CMD36;
    __IO uint32_t CFG_CMD37;
    __IO uint32_t CFG_CMD38;
    __IO uint32_t CFG_CMD39;
    __IO uint32_t CFG_CMD40;
    __IO uint32_t CFG_CMD41;
    __IO uint32_t CFG_CMD42;
    __IO uint32_t CFG_CMD43;
    __IO uint32_t CFG_CMD44;
    __IO uint32_t CFG_CMD45;
    __IO uint32_t CFG_CMD46;
    __IO uint32_t CFG_CMD47;
    __IO uint32_t CFG_CMD48;
    __IO uint32_t CFG_CMD49;
    __IO uint32_t CFG_CMD50;
    __IO uint32_t CFG_CMD51;
    __IO uint32_t CFG_CMD52;
    __IO uint32_t CFG_CMD53;
    __IO uint32_t CFG_CMD54;
    __IO uint32_t CFG_CMD55;
    __IO uint32_t CFG_CMD56;
    __IO uint32_t CFG_CMD57;
    __IO uint32_t CFG_CMD58;
    __IO uint32_t CFG_CMD59;
    __IO uint32_t CFG_CMD60;
    __IO uint32_t CFG_CMD61;
    __IO uint32_t CFG_CMD62;
    __IO uint32_t CFG_CMD63;
    __IO uint32_t CFG_CMD64;
    __IO uint32_t CFG_CMD65;
    __IO uint32_t CFG_CMD66;
    __IO uint32_t CFG_CMD67;
    __IO uint32_t CFG_CMD68;
    __IO uint32_t CFG_CMD69;
    __IO uint32_t CFG_CMD70;
    __IO uint32_t CFG_CMD71;
    __IO uint32_t CFG_CMD72;
    __IO uint32_t CFG_CMD73;
    __IO uint32_t CFG_CMD74;
    __IO uint32_t CFG_CMD75;
    __IO uint32_t CFG_CMD76;
    __IO uint32_t CFG_CMD77;
    __IO uint32_t CFG_CMD78;
    __IO uint32_t CFG_CMD79;
    __IO uint32_t CFG_CMD80;
    __IO uint32_t CFG_CMD81;
    __IO uint32_t CFG_CMD82;
    __IO uint32_t CFG_CMD83;
    __IO uint32_t CFG_CMD84;
    __IO uint32_t CFG_CMD85;
    __IO uint32_t CFG_CMD86;
    __IO uint32_t CFG_CMD87;
    __IO uint32_t CFG_CMD88;
    __IO uint32_t CFG_CMD89;
    __IO uint32_t CFG_CMD90;
    __IO uint32_t CFG_CMD91;
    __IO uint32_t CFG_CMD92;
    __IO uint32_t CFG_CMD93;
    __IO uint32_t CFG_CMD94;
    __IO uint32_t CFG_CMD95;
    __IO uint32_t CFG_CMD96;
    __IO uint32_t CFG_CMD97;
    __IO uint32_t CFG_CMD98;
    __IO uint32_t CFG_CMD99;
    __IO uint32_t FULCAL_REG_RX0;
    __IO uint32_t FULCAL_REG_RX1;
    __IO uint32_t FULCAL_REG_RX2;
    __IO uint32_t FULCAL_REG_RX3;
    __IO uint32_t FULCAL_REG_RX4;
    __IO uint32_t FULCAL_REG_RX5;
    __IO uint32_t FULCAL_REG_RX6;
    __IO uint32_t FULCAL_REG_RX7;
    __IO uint32_t FULCAL_REG_RX8;
    __IO uint32_t FULCAL_REG_RX9;
    __IO uint32_t FULCAL_REG_RX10;
    __IO uint32_t FULCAL_REG_RX11;
    __IO uint32_t FULCAL_REG_RX12;
    __IO uint32_t FULCAL_REG_RX13;
    __IO uint32_t FULCAL_REG_RX14;
    __IO uint32_t FULCAL_REG_RX15;
    __IO uint32_t FULCAL_REG_RX16;
    __IO uint32_t FULCAL_REG_RX17;
    __IO uint32_t FULCAL_REG_RX18;
    __IO uint32_t FULCAL_REG_RX19;
    __IO uint32_t FULCAL_REG_RX20;
    __IO uint32_t FULCAL_REG_RX21;
    __IO uint32_t FULCAL_REG_RX22;
    __IO uint32_t FULCAL_REG_RX23;
    __IO uint32_t FULCAL_REG_RX24;
    __IO uint32_t FULCAL_REG_RX25;
    __IO uint32_t FULCAL_REG_RX26;
    __IO uint32_t FULCAL_REG_RX27;
    __IO uint32_t FULCAL_REG_RX28;
    __IO uint32_t FULCAL_REG_RX29;
    __IO uint32_t FULCAL_REG_RX30;
    __IO uint32_t FULCAL_REG_RX31;
    __IO uint32_t FULCAL_REG_RX32;
    __IO uint32_t FULCAL_REG_RX33;
    __IO uint32_t FULCAL_REG_RX34;
    __IO uint32_t FULCAL_REG_RX35;
    __IO uint32_t FULCAL_REG_RX36;
    __IO uint32_t FULCAL_REG_RX37;
    __IO uint32_t FULCAL_REG_RX38;
    __IO uint32_t FULCAL_REG_RX39;
    __IO uint32_t AGC_REG;
} BLE_RF_DIG_TypeDef;


/************** Bit definition for BLE_RF_DIG_DCO_REG1 register ***************/
#define BLE_RF_DIG_DCO_REG1_BRF_EN_2M_MOD_LV_Pos  (0U)
#define BLE_RF_DIG_DCO_REG1_BRF_EN_2M_MOD_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG1_BRF_EN_2M_MOD_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_EN_2M_MOD_LV  BLE_RF_DIG_DCO_REG1_BRF_EN_2M_MOD_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Pos  (1U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Msk  (0x7UL << BLE_RF_DIG_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_VAR_VVN_BM_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Pos  (4U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Msk  (0x7UL << BLE_RF_DIG_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_CBANK_VVN_BM_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_PDX_LV_Pos  (7U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_PDX_LV_Msk  (0xFFUL << BLE_RF_DIG_DCO_REG1_BRF_VCO_PDX_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_PDX_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_PDX_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_IDAC_LV_Pos  (15U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_IDAC_LV_Msk  (0x7FUL << BLE_RF_DIG_DCO_REG1_BRF_VCO_IDAC_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_IDAC_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_IDAC_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_FLR_EN_LV_Pos  (22U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_FLR_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG1_BRF_VCO_FLR_EN_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_FLR_EN_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_FLR_EN_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_LDO_VREF_LV_Pos  (23U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_LDO_VREF_LV_Msk  (0xFUL << BLE_RF_DIG_DCO_REG1_BRF_VCO_LDO_VREF_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_LDO_VREF_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_LDO_VREF_LV_Msk
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_EN_LV_Pos  (27U)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG1_BRF_VCO_EN_LV_Pos)
#define BLE_RF_DIG_DCO_REG1_BRF_VCO_EN_LV  BLE_RF_DIG_DCO_REG1_BRF_VCO_EN_LV_Msk

/************** Bit definition for BLE_RF_DIG_DCO_REG2 register ***************/
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_UP_LV_Pos  (0U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_UP_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_UP_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_UP_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_UP_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Pos  (1U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_INCAL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_INCAL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Pos  (2U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_UP_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_UP_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Pos  (3U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_INCAL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Pos  (4U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Msk  (0xFUL << BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VL_SEL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Pos  (8U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Msk  (0xFUL << BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_VH_SEL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_EN_LV_Pos  (12U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_EN_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_EN_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_ACAL_EN_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Pos  (13U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VL_SEL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Pos  (16U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_VH_SEL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Pos  (19U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_EN_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_INCFCAL_EN_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Pos  (20U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_VC_SEL_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Pos  (23U)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_EN_LV  BLE_RF_DIG_DCO_REG2_BRF_VCO_FKCAL_EN_LV_Msk
#define BLE_RF_DIG_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Pos  (24U)
#define BLE_RF_DIG_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Msk  (0x1UL << BLE_RF_DIG_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Pos)
#define BLE_RF_DIG_DCO_REG2_BRF_EN_MOD_INPHASE_LV  BLE_RF_DIG_DCO_REG2_BRF_EN_MOD_INPHASE_LV_Msk

/************ Bit definition for BLE_RF_DIG_MISC_CTRL_REG register ************/
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_FBDV_STR_LV_Pos  (0U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_FBDV_STR_LV_Msk  (0x3UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_FBDV_STR_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_FBDV_STR_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_FBDV_STR_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_STR_LV_Pos  (2U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_STR_LV_Msk  (0x3UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_STR_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_STR_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_STR_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_EN_LV_Pos  (4U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_EN_LV_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_EN_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_EN_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_TX_EN_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_STR_LV_Pos  (5U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_STR_LV_Msk  (0x3UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_STR_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_STR_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_STR_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_EN_LV_Pos  (7U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_EN_LV_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_EN_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_EN_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_LODIST_RX_EN_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_RFBG_LV_Pos  (8U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_RFBG_LV_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_RFBG_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_RFBG_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_RFBG_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_VDDSW_LV_Pos  (9U)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_VDDSW_LV_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_VDDSW_LV_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_VDDSW_LV  BLE_RF_DIG_MISC_CTRL_REG_BRF_EN_VDDSW_LV_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_PDX_FORCE_EN_Pos  (10U)
#define BLE_RF_DIG_MISC_CTRL_REG_PDX_FORCE_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_PDX_FORCE_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_PDX_FORCE_EN  BLE_RF_DIG_MISC_CTRL_REG_PDX_FORCE_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_IDAC_FORCE_EN_Pos  (11U)
#define BLE_RF_DIG_MISC_CTRL_REG_IDAC_FORCE_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_IDAC_FORCE_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_IDAC_FORCE_EN  BLE_RF_DIG_MISC_CTRL_REG_IDAC_FORCE_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_Pos  (12U)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN  BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_Pos  (13U)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN  BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Pos  (14U)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL  BLE_RF_DIG_MISC_CTRL_REG_ADC_FIFO_CLK_PHASE_SEL_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Pos  (15U)
#define BLE_RF_DIG_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_UNLOCK_FLAG_CLR  BLE_RF_DIG_MISC_CTRL_REG_UNLOCK_FLAG_CLR_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Pos  (16U)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_XTAL_REF_EN_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Pos  (17U)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_EN_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_Pos  (18U)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL  BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Pos  (19U)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_ADC_CLK_SEL_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_BW_FRC_EN_Pos  (20U)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_BW_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_CBPF_BW_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_BW_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_CBPF_BW_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Pos  (21U)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG1_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Pos  (22U)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_CBPF_WX2_STG2_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Pos  (23U)
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG1_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Pos  (24U)
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN  BLE_RF_DIG_MISC_CTRL_REG_RVGA_WX2_STG2_FRC_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Pos  (25U)
#define BLE_RF_DIG_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN  BLE_RF_DIG_MISC_CTRL_REG_PKDET_EN_EARLY_OFF_EN_Msk
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Pos  (26U)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Msk  (0x1UL << BLE_RF_DIG_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Pos)
#define BLE_RF_DIG_MISC_CTRL_REG_XTAL_RFCH_SEL_EN  BLE_RF_DIG_MISC_CTRL_REG_XTAL_RFCH_SEL_EN_Msk

/************** Bit definition for BLE_RF_DIG_FBDV_REG1 register **************/
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Pos  (0U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV  BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RDY_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Pos  (1U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV  BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_RSTB_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Pos  (2U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_EN_LV  BLE_RF_DIG_FBDV_REG1_BRF_FKCAL_CNT_EN_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Pos  (3U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Msk  (0x3UL << BLE_RF_DIG_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_MOD_STG_LV  BLE_RF_DIG_FBDV_REG1_BRF_FBDV_MOD_STG_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Pos  (5U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV  BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_SYNC_EN_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_LV_Pos  (6U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_LV  BLE_RF_DIG_FBDV_REG1_BRF_FBDV_RSTB_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Pos  (7U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Msk  (0xFUL << BLE_RF_DIG_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_LDO_VREF_LV  BLE_RF_DIG_FBDV_REG1_BRF_FBDV_LDO_VREF_LV_Msk
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_EN_LV_Pos  (11U)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_EN_LV_Msk  (0x1UL << BLE_RF_DIG_FBDV_REG1_BRF_FBDV_EN_LV_Pos)
#define BLE_RF_DIG_FBDV_REG1_BRF_FBDV_EN_LV  BLE_RF_DIG_FBDV_REG1_BRF_FBDV_EN_LV_Msk

/************** Bit definition for BLE_RF_DIG_FBDV_REG2 register **************/
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Pos  (0U)
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Msk  (0xFFFFUL << BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Pos)
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_OP_LV  BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_OP_LV_Msk
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Pos  (16U)
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Msk  (0xFFFFUL << BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Pos)
#define BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV  BLE_RF_DIG_FBDV_REG2_BRF_FKCAL_CNT_DIVN_LV_Msk

/************** Bit definition for BLE_RF_DIG_PFDCP_REG register **************/
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_DN_LV_Pos  (0U)
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_DN_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_CSD_DN_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_DN_LV  BLE_RF_DIG_PFDCP_REG_BRF_CSD_DN_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_UP_LV_Pos  (1U)
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_UP_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_CSD_UP_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_CSD_UP_LV  BLE_RF_DIG_PFDCP_REG_BRF_CSD_UP_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_LO_UNLOCK_LV_Pos  (2U)
#define BLE_RF_DIG_PFDCP_REG_BRF_LO_UNLOCK_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_LO_UNLOCK_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_LO_UNLOCK_LV  BLE_RF_DIG_PFDCP_REG_BRF_LO_UNLOCK_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Pos  (3U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_RESET_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Pos  (4U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_EN_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_CSD_EN_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Pos  (5U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Msk  (0x3FUL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_OS_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_OS_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Pos  (11U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Msk  (0xFUL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_SET_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_ICP_SET_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Pos  (15U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Msk  (0xFUL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_LDO_VREF_LV_Msk
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_EN_LV_Pos  (19U)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_EN_LV_Msk  (0x1UL << BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_EN_LV_Pos)
#define BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_EN_LV  BLE_RF_DIG_PFDCP_REG_BRF_PFDCP_EN_LV_Msk

/*************** Bit definition for BLE_RF_DIG_LPF_REG register ***************/
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RZ_SEL_LV_Pos  (0U)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RZ_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_LPF_REG_BRF_LPF_RZ_SEL_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RZ_SEL_LV  BLE_RF_DIG_LPF_REG_BRF_LPF_RZ_SEL_LV_Msk
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RP4_SEL_LV_Pos  (3U)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RP4_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_LPF_REG_BRF_LPF_RP4_SEL_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_RP4_SEL_LV  BLE_RF_DIG_LPF_REG_BRF_LPF_RP4_SEL_LV_Msk
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CZ_SEL_LV_Pos  (6U)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CZ_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_LPF_REG_BRF_LPF_CZ_SEL_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CZ_SEL_LV  BLE_RF_DIG_LPF_REG_BRF_LPF_CZ_SEL_LV_Msk
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP4_SEL_LV_Pos  (9U)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP4_SEL_LV_Msk  (0x3UL << BLE_RF_DIG_LPF_REG_BRF_LPF_CP4_SEL_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP4_SEL_LV  BLE_RF_DIG_LPF_REG_BRF_LPF_CP4_SEL_LV_Msk
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP3_SEL_LV_Pos  (11U)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP3_SEL_LV_Msk  (0x7UL << BLE_RF_DIG_LPF_REG_BRF_LPF_CP3_SEL_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LPF_CP3_SEL_LV  BLE_RF_DIG_LPF_REG_BRF_LPF_CP3_SEL_LV_Msk
#define BLE_RF_DIG_LPF_REG_BRF_LO_OPEN_LV_Pos  (14U)
#define BLE_RF_DIG_LPF_REG_BRF_LO_OPEN_LV_Msk  (0x1UL << BLE_RF_DIG_LPF_REG_BRF_LO_OPEN_LV_Pos)
#define BLE_RF_DIG_LPF_REG_BRF_LO_OPEN_LV  BLE_RF_DIG_LPF_REG_BRF_LO_OPEN_LV_Msk

/************** Bit definition for BLE_RF_DIG_ATEST_REG register **************/
#define BLE_RF_DIG_ATEST_REG_BRF_DC_TR_LV_Pos  (0U)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_TR_LV_Msk  (0x7UL << BLE_RF_DIG_ATEST_REG_BRF_DC_TR_LV_Pos)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_TR_LV  BLE_RF_DIG_ATEST_REG_BRF_DC_TR_LV_Msk
#define BLE_RF_DIG_ATEST_REG_BRF_DC_BR_LV_Pos  (3U)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_BR_LV_Msk  (0x7UL << BLE_RF_DIG_ATEST_REG_BRF_DC_BR_LV_Pos)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_BR_LV  BLE_RF_DIG_ATEST_REG_BRF_DC_BR_LV_Msk
#define BLE_RF_DIG_ATEST_REG_BRF_DC_MR_LV_Pos  (6U)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_MR_LV_Msk  (0x7UL << BLE_RF_DIG_ATEST_REG_BRF_DC_MR_LV_Pos)
#define BLE_RF_DIG_ATEST_REG_BRF_DC_MR_LV  BLE_RF_DIG_ATEST_REG_BRF_DC_MR_LV_Msk

/************** Bit definition for BLE_RF_DIG_DTEST_REG register **************/
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Pos  (0U)
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Msk  (0xFUL << BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Pos)
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_TR_LV  BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_TR_LV_Msk
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Pos  (4U)
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Msk  (0x1UL << BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Pos)
#define BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_EN_LV  BLE_RF_DIG_DTEST_REG_BRF_FBDV_DTEST_EN_LV_Msk

/************** Bit definition for BLE_RF_DIG_TRF_REG1 register ***************/
#define BLE_RF_DIG_TRF_REG1_BRF_PA_CAS_BP_LV_Pos  (0U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_CAS_BP_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_CAS_BP_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_CAS_BP_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_CAS_BP_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_PM_LV_Pos  (1U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_PM_LV_Msk  (0x3UL << BLE_RF_DIG_TRF_REG1_BRF_PA_PM_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_PM_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_PM_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_VC_LV_Pos  (3U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_VC_LV_Msk  (0x3FUL << BLE_RF_DIG_TRF_REG1_BRF_PA_VC_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_VC_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_VC_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_RSTN_LV_Pos  (9U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_RSTN_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_RSTN_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_RSTN_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_RSTN_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETBC_LV_Pos  (10U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETBC_LV_Msk  (0xFUL << BLE_RF_DIG_TRF_REG1_BRF_PA_SETBC_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETBC_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_SETBC_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETSGN_LV_Pos  (14U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETSGN_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_SETSGN_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_SETSGN_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_SETSGN_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BCSEL_LV_Pos  (15U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BCSEL_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_BCSEL_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BCSEL_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_BCSEL_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_SIG_EN_LV_Pos  (16U)
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_SIG_EN_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_TRF_SIG_EN_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_SIG_EN_LV  BLE_RF_DIG_TRF_REG1_BRF_TRF_SIG_EN_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Pos  (17U)
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Msk  (0xFUL << BLE_RF_DIG_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV  BLE_RF_DIG_TRF_REG1_BRF_TRF_LDO_VREF_SEL_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_OUT_PU_LV_Pos  (21U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_OUT_PU_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_OUT_PU_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_OUT_PU_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_OUT_PU_LV_Msk
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BUF_PU_LV_Pos  (22U)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BUF_PU_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG1_BRF_PA_BUF_PU_LV_Pos)
#define BLE_RF_DIG_TRF_REG1_BRF_PA_BUF_PU_LV  BLE_RF_DIG_TRF_REG1_BRF_PA_BUF_PU_LV_Msk

/************** Bit definition for BLE_RF_DIG_TRF_REG2 register ***************/
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Pos  (0U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Msk  (0xFUL << BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_GAIN_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_GAIN_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_EN_LV_Pos  (4U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_EN_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_EN_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_EN_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_ATTEN_EN_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH2_LV_Pos  (5U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH2_LV_Msk  (0x3UL << BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH2_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH2_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH2_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH1_LV_Pos  (7U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH1_LV_Msk  (0x3UL << BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH1_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH1_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_MATCH1_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_TX_RX_LV_Pos  (9U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_TX_RX_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG2_BRF_PA_TX_RX_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_TX_RX_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_TX_RX_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MCAP_LV_Pos  (10U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MCAP_LV_Msk  (0x1UL << BLE_RF_DIG_TRF_REG2_BRF_PA_MCAP_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_MCAP_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_MCAP_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_UNIT_SEL_LV_Pos  (11U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_UNIT_SEL_LV_Msk  (0x1FUL << BLE_RF_DIG_TRF_REG2_BRF_PA_UNIT_SEL_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_UNIT_SEL_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_UNIT_SEL_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Pos  (16U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Msk  (0x3UL << BLE_RF_DIG_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_BUFLOAD_SEL_LV_Msk
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BM_LV_Pos  (18U)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BM_LV_Msk  (0x3UL << BLE_RF_DIG_TRF_REG2_BRF_PA_BM_LV_Pos)
#define BLE_RF_DIG_TRF_REG2_BRF_PA_BM_LV  BLE_RF_DIG_TRF_REG2_BRF_PA_BM_LV_Msk

/*************** Bit definition for BLE_RF_DIG_RRF_REG register ***************/
#define BLE_RF_DIG_RRF_REG_BRF_MX_BM_LV_Pos  (0U)
#define BLE_RF_DIG_RRF_REG_BRF_MX_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RRF_REG_BRF_MX_BM_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_MX_BM_LV  BLE_RF_DIG_RRF_REG_BRF_MX_BM_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_MX_PU_LV_Pos  (3U)
#define BLE_RF_DIG_RRF_REG_BRF_MX_PU_LV_Msk  (0x1UL << BLE_RF_DIG_RRF_REG_BRF_MX_PU_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_MX_PU_LV  BLE_RF_DIG_RRF_REG_BRF_MX_PU_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_MATCH_LV_Pos  (4U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_MATCH_LV_Msk  (0x3UL << BLE_RF_DIG_RRF_REG_BRF_LNA_MATCH_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_MATCH_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_MATCH_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_SHUNTSW_LV_Pos  (6U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_SHUNTSW_LV_Msk  (0x1UL << BLE_RF_DIG_RRF_REG_BRF_LNA_SHUNTSW_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_SHUNTSW_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_SHUNTSW_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_FBRTRIM_LV_Pos  (7U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_FBRTRIM_LV_Msk  (0x7UL << BLE_RF_DIG_RRF_REG_BRF_LNA_FBRTRIM_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_FBRTRIM_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_FBRTRIM_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_GC_LV_Pos  (10U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_GC_LV_Msk  (0xFUL << BLE_RF_DIG_RRF_REG_BRF_LNA_GC_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_GC_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_GC_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_BM_LV_Pos  (14U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RRF_REG_BRF_LNA_BM_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_BM_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_BM_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_LNA_PU_LV_Pos  (17U)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_PU_LV_Msk  (0x1UL << BLE_RF_DIG_RRF_REG_BRF_LNA_PU_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_LNA_PU_LV  BLE_RF_DIG_RRF_REG_BRF_LNA_PU_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Pos  (18U)
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Msk  (0xFUL << BLE_RF_DIG_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV  BLE_RF_DIG_RRF_REG_BRF_RRF_LDO_VREF_SEL_LV_Msk
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO11_EN_LV_Pos  (22U)
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO11_EN_LV_Msk  (0x1UL << BLE_RF_DIG_RRF_REG_BRF_RRF_LDO11_EN_LV_Pos)
#define BLE_RF_DIG_RRF_REG_BRF_RRF_LDO11_EN_LV  BLE_RF_DIG_RRF_REG_BRF_RRF_LDO11_EN_LV_Msk

/************** Bit definition for BLE_RF_DIG_RBB_REG1 register ***************/
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_FC_LV_2M_Pos  (0U)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_FC_LV_2M_Msk  (0x3UL << BLE_RF_DIG_RBB_REG1_BRF_CBPF_FC_LV_2M_Pos)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_FC_LV_2M  BLE_RF_DIG_RBB_REG1_BRF_CBPF_FC_LV_2M_Msk
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_BM_LV_2M_Pos  (2U)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_BM_LV_2M_Msk  (0x7UL << BLE_RF_DIG_RBB_REG1_BRF_CBPF_BM_LV_2M_Pos)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_BM_LV_2M  BLE_RF_DIG_RBB_REG1_BRF_CBPF_BM_LV_2M_Msk
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_CC_LV_2M_Pos  (5U)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_CC_LV_2M_Msk  (0xFUL << BLE_RF_DIG_RBB_REG1_BRF_CBPF_CC_LV_2M_Pos)
#define BLE_RF_DIG_RBB_REG1_BRF_CBPF_CC_LV_2M  BLE_RF_DIG_RBB_REG1_BRF_CBPF_CC_LV_2M_Msk
#define BLE_RF_DIG_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Pos  (9U)
#define BLE_RF_DIG_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Pos)
#define BLE_RF_DIG_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV  BLE_RF_DIG_RBB_REG1_BRF_SEL_LDOVREF_RBB_LV_Msk
#define BLE_RF_DIG_RBB_REG1_BRF_EN_LDO_RBB_LV_Pos  (13U)
#define BLE_RF_DIG_RBB_REG1_BRF_EN_LDO_RBB_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG1_BRF_EN_LDO_RBB_LV_Pos)
#define BLE_RF_DIG_RBB_REG1_BRF_EN_LDO_RBB_LV  BLE_RF_DIG_RBB_REG1_BRF_EN_LDO_RBB_LV_Msk

/************** Bit definition for BLE_RF_DIG_RBB_REG2 register ***************/
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Pos  (0U)
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV  BLE_RF_DIG_RBB_REG2_BRF_RVGA_MAN_CFSEL_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_GC_LV_Pos  (1U)
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_GC_LV_Msk  (0x1FUL << BLE_RF_DIG_RBB_REG2_BRF_RVGA_GC_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_RVGA_GC_LV  BLE_RF_DIG_RBB_REG2_BRF_RVGA_GC_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_Q_LV_Pos  (6U)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_Q_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_Q_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_Q_LV  BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_Q_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_I_LV_Pos  (7U)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_I_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_I_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_I_LV  BLE_RF_DIG_RBB_REG2_BRF_EN_RVGA_I_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Pos  (8U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG2_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG2_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Pos  (9U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG1_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_W2X_STG1_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_GC_LV_Pos  (10U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_GC_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_GC_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_GC_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_GC_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_EN_RC_Pos  (12U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_EN_RC_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_EN_RC_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_EN_RC  BLE_RF_DIG_RBB_REG2_BRF_CBPF_EN_RC_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_FC_LV_Pos  (13U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_FC_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_FC_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_FC_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_FC_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BW_LV_Pos  (15U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BW_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_BW_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BW_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_BW_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VSTART_LV_Pos  (16U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VSTART_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_VSTART_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VSTART_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_VSTART_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VCMREF_LV_Pos  (18U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VCMREF_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_VCMREF_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_VCMREF_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_VCMREF_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BM_LV_Pos  (20U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_BM_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_BM_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_BM_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_CC_LV_Pos  (23U)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_CC_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG2_BRF_CBPF_CC_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_CBPF_CC_LV  BLE_RF_DIG_RBB_REG2_BRF_CBPF_CC_LV_Msk
#define BLE_RF_DIG_RBB_REG2_BRF_EN_CBPF_LV_Pos  (27U)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_CBPF_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG2_BRF_EN_CBPF_LV_Pos)
#define BLE_RF_DIG_RBB_REG2_BRF_EN_CBPF_LV  BLE_RF_DIG_RBB_REG2_BRF_EN_CBPF_LV_Msk

/************** Bit definition for BLE_RF_DIG_RBB_REG3 register ***************/
#define BLE_RF_DIG_RBB_REG3_BRF_EN_PKDET_LV_Pos  (0U)
#define BLE_RF_DIG_RBB_REG3_BRF_EN_PKDET_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG3_BRF_EN_PKDET_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_EN_PKDET_LV  BLE_RF_DIG_RBB_REG3_BRF_EN_PKDET_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Pos  (4U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG2_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG2_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Pos  (5U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG1_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_W2X_STG1_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VSTART_LV_Pos  (6U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VSTART_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_VSTART_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VSTART_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_VSTART_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VCMREF_LV_Pos  (8U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VCMREF_LV_Msk  (0x3UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_VCMREF_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_VCMREF_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_VCMREF_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_BM_LV_Pos  (10U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_BM_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_BM_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_BM_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_RZ_LV_Pos  (13U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_RZ_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_RZ_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_RZ_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_RZ_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CC_LV_Pos  (16U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CC_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_CC_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CC_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_CC_LV_Msk
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CFMAN_LV_Pos  (20U)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CFMAN_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG3_BRF_RVGA_CFMAN_LV_Pos)
#define BLE_RF_DIG_RBB_REG3_BRF_RVGA_CFMAN_LV  BLE_RF_DIG_RBB_REG3_BRF_RVGA_CFMAN_LV_Msk

/************** Bit definition for BLE_RF_DIG_RBB_REG4 register ***************/
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2Q_LV_Pos  (0U)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2Q_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2Q_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2Q_LV  BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2Q_LV_Msk
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2I_LV_Pos  (4U)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2I_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2I_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2I_LV  BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH2I_LV_Msk
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1Q_LV_Pos  (8U)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1Q_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1Q_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1Q_LV  BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1Q_LV_Msk
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1I_LV_Pos  (12U)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1I_LV_Msk  (0xFUL << BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1I_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1I_LV  BLE_RF_DIG_RBB_REG4_BRF_PKDET_VTH1I_LV_Msk
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_Q_LV_Pos  (16U)
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_Q_LV_Msk  (0x7FUL << BLE_RF_DIG_RBB_REG4_BRF_DOS_Q_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_Q_LV  BLE_RF_DIG_RBB_REG4_BRF_DOS_Q_LV_Msk
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_I_LV_Pos  (23U)
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_I_LV_Msk  (0x7FUL << BLE_RF_DIG_RBB_REG4_BRF_DOS_I_LV_Pos)
#define BLE_RF_DIG_RBB_REG4_BRF_DOS_I_LV  BLE_RF_DIG_RBB_REG4_BRF_DOS_I_LV_Msk

/************** Bit definition for BLE_RF_DIG_RBB_REG5 register ***************/
#define BLE_RF_DIG_RBB_REG5_BRF_IARY_BM_LV_Pos  (0U)
#define BLE_RF_DIG_RBB_REG5_BRF_IARY_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG5_BRF_IARY_BM_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_IARY_BM_LV  BLE_RF_DIG_RBB_REG5_BRF_IARY_BM_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_EN_IARRAY_LV_Pos  (3U)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_IARRAY_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_EN_IARRAY_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_IARRAY_LV  BLE_RF_DIG_RBB_REG5_BRF_EN_IARRAY_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACQ_LV_Pos  (4U)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACQ_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACQ_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACQ_LV  BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACQ_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACI_LV_Pos  (5U)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACI_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACI_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACI_LV  BLE_RF_DIG_RBB_REG5_BRF_EN_OSDACI_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_RSTB_RCCAL_LV_Pos  (6U)
#define BLE_RF_DIG_RBB_REG5_BRF_RSTB_RCCAL_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_RSTB_RCCAL_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_RSTB_RCCAL_LV  BLE_RF_DIG_RBB_REG5_BRF_RSTB_RCCAL_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_CBPF_CAPMAN_LV_Pos  (7U)
#define BLE_RF_DIG_RBB_REG5_BRF_CBPF_CAPMAN_LV_Msk  (0x1FUL << BLE_RF_DIG_RBB_REG5_BRF_CBPF_CAPMAN_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_CBPF_CAPMAN_LV  BLE_RF_DIG_RBB_REG5_BRF_CBPF_CAPMAN_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_MANCAP_LV_Pos  (12U)
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_MANCAP_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_RCCAL_MANCAP_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_MANCAP_LV  BLE_RF_DIG_RBB_REG5_BRF_RCCAL_MANCAP_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_SELXO_LV_Pos  (13U)
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_SELXO_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_RCCAL_SELXO_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_RCCAL_SELXO_LV  BLE_RF_DIG_RBB_REG5_BRF_RCCAL_SELXO_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_EN_RCCAL_LV_Pos  (14U)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_RCCAL_LV_Msk  (0x1UL << BLE_RF_DIG_RBB_REG5_BRF_EN_RCCAL_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_EN_RCCAL_LV  BLE_RF_DIG_RBB_REG5_BRF_EN_RCCAL_LV_Msk
#define BLE_RF_DIG_RBB_REG5_BRF_PKDET_BM_LV_Pos  (15U)
#define BLE_RF_DIG_RBB_REG5_BRF_PKDET_BM_LV_Msk  (0x7UL << BLE_RF_DIG_RBB_REG5_BRF_PKDET_BM_LV_Pos)
#define BLE_RF_DIG_RBB_REG5_BRF_PKDET_BM_LV  BLE_RF_DIG_RBB_REG5_BRF_PKDET_BM_LV_Msk

/*************** Bit definition for BLE_RF_DIG_ADC_REG register ***************/
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Pos  (0U)
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Msk  (0xFUL << BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV  BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADCREF_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADCREF_LV_Pos  (4U)
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADCREF_LV_Msk  (0x1UL << BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADCREF_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADCREF_LV  BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADCREF_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Pos  (5U)
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Msk  (0xFUL << BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADC_LV  BLE_RF_DIG_ADC_REG_BRF_SEL_LDOVREF_ADC_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADC_LV_Pos  (9U)
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADC_LV_Msk  (0x1UL << BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADC_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADC_LV  BLE_RF_DIG_ADC_REG_BRF_EN_LDO_ADC_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_RSTB_ADC_LV_Pos  (10U)
#define BLE_RF_DIG_ADC_REG_BRF_RSTB_ADC_LV_Msk  (0x1UL << BLE_RF_DIG_ADC_REG_BRF_RSTB_ADC_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_RSTB_ADC_LV  BLE_RF_DIG_ADC_REG_BRF_RSTB_ADC_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_ADC_VSP_LV_Pos  (11U)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_VSP_LV_Msk  (0x3UL << BLE_RF_DIG_ADC_REG_BRF_ADC_VSP_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_VSP_LV  BLE_RF_DIG_ADC_REG_BRF_ADC_VSP_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMPCL_LV_Pos  (13U)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMPCL_LV_Msk  (0x7UL << BLE_RF_DIG_ADC_REG_BRF_ADC_CMPCL_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMPCL_LV  BLE_RF_DIG_ADC_REG_BRF_ADC_CMPCL_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMM_LV_Pos  (16U)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMM_LV_Msk  (0xFUL << BLE_RF_DIG_ADC_REG_BRF_ADC_CMM_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_ADC_CMM_LV  BLE_RF_DIG_ADC_REG_BRF_ADC_CMM_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_Q_LV_Pos  (20U)
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_Q_LV_Msk  (0x1UL << BLE_RF_DIG_ADC_REG_BRF_EN_ADC_Q_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_Q_LV  BLE_RF_DIG_ADC_REG_BRF_EN_ADC_Q_LV_Msk
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_I_LV_Pos  (21U)
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_I_LV_Msk  (0x1UL << BLE_RF_DIG_ADC_REG_BRF_EN_ADC_I_LV_Pos)
#define BLE_RF_DIG_ADC_REG_BRF_EN_ADC_I_LV  BLE_RF_DIG_ADC_REG_BRF_EN_ADC_I_LV_Msk

/************* Bit definition for BLE_RF_DIG_ATSTBUF_REG register *************/
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Pos  (0U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Msk  (0x1UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG2_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Pos  (1U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Msk  (0x1UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_W2X_STG1_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Pos  (2U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Msk  (0x3UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VSTART_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Pos  (4U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Msk  (0x3UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_VCMREF_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Pos  (6U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Msk  (0x7UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_BM_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_BM_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Pos  (9U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Msk  (0x7UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_RZ_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Pos  (12U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Msk  (0xFUL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CC_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CC_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Pos  (16U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Msk  (0x7UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CFMAN_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Pos  (19U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Msk  (0x1UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_MAN_CFSEL_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Pos  (20U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Msk  (0x1FUL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_GC_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_GC_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Pos  (25U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Msk  (0x1UL << BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_ATSTBUF_CH_SEL_LV_Msk
#define BLE_RF_DIG_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Pos  (26U)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Msk  (0x1UL << BLE_RF_DIG_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Pos)
#define BLE_RF_DIG_ATSTBUF_REG_BRF_EN_ATSTBUF_LV  BLE_RF_DIG_ATSTBUF_REG_BRF_EN_ATSTBUF_LV_Msk

/************** Bit definition for BLE_RF_DIG_RSVD_REG1 register **************/
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE3_LV_Pos  (0U)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE3_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG1_BRF_RESERVE3_LV_Pos)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE3_LV  BLE_RF_DIG_RSVD_REG1_BRF_RESERVE3_LV_Msk
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE2_LV_Pos  (8U)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE2_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG1_BRF_RESERVE2_LV_Pos)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE2_LV  BLE_RF_DIG_RSVD_REG1_BRF_RESERVE2_LV_Msk
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE1_LV_Pos  (16U)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE1_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG1_BRF_RESERVE1_LV_Pos)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE1_LV  BLE_RF_DIG_RSVD_REG1_BRF_RESERVE1_LV_Msk
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE0_LV_Pos  (24U)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE0_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG1_BRF_RESERVE0_LV_Pos)
#define BLE_RF_DIG_RSVD_REG1_BRF_RESERVE0_LV  BLE_RF_DIG_RSVD_REG1_BRF_RESERVE0_LV_Msk

/************** Bit definition for BLE_RF_DIG_RSVD_REG2 register **************/
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE7_LV_Pos  (0U)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE7_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG2_BRF_RESERVE7_LV_Pos)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE7_LV  BLE_RF_DIG_RSVD_REG2_BRF_RESERVE7_LV_Msk
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE6_LV_Pos  (8U)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE6_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG2_BRF_RESERVE6_LV_Pos)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE6_LV  BLE_RF_DIG_RSVD_REG2_BRF_RESERVE6_LV_Msk
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE5_LV_Pos  (16U)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE5_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG2_BRF_RESERVE5_LV_Pos)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE5_LV  BLE_RF_DIG_RSVD_REG2_BRF_RESERVE5_LV_Msk
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE4_LV_Pos  (24U)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE4_LV_Msk  (0xFFUL << BLE_RF_DIG_RSVD_REG2_BRF_RESERVE4_LV_Pos)
#define BLE_RF_DIG_RSVD_REG2_BRF_RESERVE4_LV  BLE_RF_DIG_RSVD_REG2_BRF_RESERVE4_LV_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG0 register *************/
#define BLE_RF_DIG_FULCAL_REG0_FCAL_CHNL0_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG0_FCAL_CHNL0_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG0_FCAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG0_FCAL_CHNL0  BLE_RF_DIG_FULCAL_REG0_FCAL_CHNL0_Msk
#define BLE_RF_DIG_FULCAL_REG0_ACAL_CHNL0_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG0_ACAL_CHNL0_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG0_ACAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG0_ACAL_CHNL0  BLE_RF_DIG_FULCAL_REG0_ACAL_CHNL0_Msk
#define BLE_RF_DIG_FULCAL_REG0_KCAL_CHNL0_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG0_KCAL_CHNL0_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG0_KCAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG0_KCAL_CHNL0  BLE_RF_DIG_FULCAL_REG0_KCAL_CHNL0_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG1 register *************/
#define BLE_RF_DIG_FULCAL_REG1_FCAL_CHNL1_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG1_FCAL_CHNL1_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG1_FCAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG1_FCAL_CHNL1  BLE_RF_DIG_FULCAL_REG1_FCAL_CHNL1_Msk
#define BLE_RF_DIG_FULCAL_REG1_ACAL_CHNL1_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG1_ACAL_CHNL1_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG1_ACAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG1_ACAL_CHNL1  BLE_RF_DIG_FULCAL_REG1_ACAL_CHNL1_Msk
#define BLE_RF_DIG_FULCAL_REG1_KCAL_CHNL1_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG1_KCAL_CHNL1_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG1_KCAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG1_KCAL_CHNL1  BLE_RF_DIG_FULCAL_REG1_KCAL_CHNL1_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG2 register *************/
#define BLE_RF_DIG_FULCAL_REG2_FCAL_CHNL2_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG2_FCAL_CHNL2_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG2_FCAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG2_FCAL_CHNL2  BLE_RF_DIG_FULCAL_REG2_FCAL_CHNL2_Msk
#define BLE_RF_DIG_FULCAL_REG2_ACAL_CHNL2_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG2_ACAL_CHNL2_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG2_ACAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG2_ACAL_CHNL2  BLE_RF_DIG_FULCAL_REG2_ACAL_CHNL2_Msk
#define BLE_RF_DIG_FULCAL_REG2_KCAL_CHNL2_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG2_KCAL_CHNL2_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG2_KCAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG2_KCAL_CHNL2  BLE_RF_DIG_FULCAL_REG2_KCAL_CHNL2_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG3 register *************/
#define BLE_RF_DIG_FULCAL_REG3_FCAL_CHNL3_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG3_FCAL_CHNL3_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG3_FCAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG3_FCAL_CHNL3  BLE_RF_DIG_FULCAL_REG3_FCAL_CHNL3_Msk
#define BLE_RF_DIG_FULCAL_REG3_ACAL_CHNL3_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG3_ACAL_CHNL3_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG3_ACAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG3_ACAL_CHNL3  BLE_RF_DIG_FULCAL_REG3_ACAL_CHNL3_Msk
#define BLE_RF_DIG_FULCAL_REG3_KCAL_CHNL3_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG3_KCAL_CHNL3_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG3_KCAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG3_KCAL_CHNL3  BLE_RF_DIG_FULCAL_REG3_KCAL_CHNL3_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG4 register *************/
#define BLE_RF_DIG_FULCAL_REG4_FCAL_CHNL4_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG4_FCAL_CHNL4_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG4_FCAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG4_FCAL_CHNL4  BLE_RF_DIG_FULCAL_REG4_FCAL_CHNL4_Msk
#define BLE_RF_DIG_FULCAL_REG4_ACAL_CHNL4_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG4_ACAL_CHNL4_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG4_ACAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG4_ACAL_CHNL4  BLE_RF_DIG_FULCAL_REG4_ACAL_CHNL4_Msk
#define BLE_RF_DIG_FULCAL_REG4_KCAL_CHNL4_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG4_KCAL_CHNL4_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG4_KCAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG4_KCAL_CHNL4  BLE_RF_DIG_FULCAL_REG4_KCAL_CHNL4_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG5 register *************/
#define BLE_RF_DIG_FULCAL_REG5_FCAL_CHNL5_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG5_FCAL_CHNL5_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG5_FCAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG5_FCAL_CHNL5  BLE_RF_DIG_FULCAL_REG5_FCAL_CHNL5_Msk
#define BLE_RF_DIG_FULCAL_REG5_ACAL_CHNL5_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG5_ACAL_CHNL5_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG5_ACAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG5_ACAL_CHNL5  BLE_RF_DIG_FULCAL_REG5_ACAL_CHNL5_Msk
#define BLE_RF_DIG_FULCAL_REG5_KCAL_CHNL5_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG5_KCAL_CHNL5_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG5_KCAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG5_KCAL_CHNL5  BLE_RF_DIG_FULCAL_REG5_KCAL_CHNL5_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG6 register *************/
#define BLE_RF_DIG_FULCAL_REG6_FCAL_CHNL6_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG6_FCAL_CHNL6_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG6_FCAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG6_FCAL_CHNL6  BLE_RF_DIG_FULCAL_REG6_FCAL_CHNL6_Msk
#define BLE_RF_DIG_FULCAL_REG6_ACAL_CHNL6_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG6_ACAL_CHNL6_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG6_ACAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG6_ACAL_CHNL6  BLE_RF_DIG_FULCAL_REG6_ACAL_CHNL6_Msk
#define BLE_RF_DIG_FULCAL_REG6_KCAL_CHNL6_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG6_KCAL_CHNL6_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG6_KCAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG6_KCAL_CHNL6  BLE_RF_DIG_FULCAL_REG6_KCAL_CHNL6_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG7 register *************/
#define BLE_RF_DIG_FULCAL_REG7_FCAL_CHNL7_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG7_FCAL_CHNL7_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG7_FCAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG7_FCAL_CHNL7  BLE_RF_DIG_FULCAL_REG7_FCAL_CHNL7_Msk
#define BLE_RF_DIG_FULCAL_REG7_ACAL_CHNL7_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG7_ACAL_CHNL7_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG7_ACAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG7_ACAL_CHNL7  BLE_RF_DIG_FULCAL_REG7_ACAL_CHNL7_Msk
#define BLE_RF_DIG_FULCAL_REG7_KCAL_CHNL7_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG7_KCAL_CHNL7_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG7_KCAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG7_KCAL_CHNL7  BLE_RF_DIG_FULCAL_REG7_KCAL_CHNL7_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG8 register *************/
#define BLE_RF_DIG_FULCAL_REG8_FCAL_CHNL8_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG8_FCAL_CHNL8_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG8_FCAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG8_FCAL_CHNL8  BLE_RF_DIG_FULCAL_REG8_FCAL_CHNL8_Msk
#define BLE_RF_DIG_FULCAL_REG8_ACAL_CHNL8_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG8_ACAL_CHNL8_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG8_ACAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG8_ACAL_CHNL8  BLE_RF_DIG_FULCAL_REG8_ACAL_CHNL8_Msk
#define BLE_RF_DIG_FULCAL_REG8_KCAL_CHNL8_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG8_KCAL_CHNL8_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG8_KCAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG8_KCAL_CHNL8  BLE_RF_DIG_FULCAL_REG8_KCAL_CHNL8_Msk

/************* Bit definition for BLE_RF_DIG_FULCAL_REG9 register *************/
#define BLE_RF_DIG_FULCAL_REG9_FCAL_CHNL9_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG9_FCAL_CHNL9_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG9_FCAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG9_FCAL_CHNL9  BLE_RF_DIG_FULCAL_REG9_FCAL_CHNL9_Msk
#define BLE_RF_DIG_FULCAL_REG9_ACAL_CHNL9_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG9_ACAL_CHNL9_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG9_ACAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG9_ACAL_CHNL9  BLE_RF_DIG_FULCAL_REG9_ACAL_CHNL9_Msk
#define BLE_RF_DIG_FULCAL_REG9_KCAL_CHNL9_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG9_KCAL_CHNL9_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG9_KCAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG9_KCAL_CHNL9  BLE_RF_DIG_FULCAL_REG9_KCAL_CHNL9_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG10 register *************/
#define BLE_RF_DIG_FULCAL_REG10_FCAL_CHNL10_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG10_FCAL_CHNL10_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG10_FCAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG10_FCAL_CHNL10  BLE_RF_DIG_FULCAL_REG10_FCAL_CHNL10_Msk
#define BLE_RF_DIG_FULCAL_REG10_ACAL_CHNL10_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG10_ACAL_CHNL10_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG10_ACAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG10_ACAL_CHNL10  BLE_RF_DIG_FULCAL_REG10_ACAL_CHNL10_Msk
#define BLE_RF_DIG_FULCAL_REG10_KCAL_CHNL10_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG10_KCAL_CHNL10_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG10_KCAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG10_KCAL_CHNL10  BLE_RF_DIG_FULCAL_REG10_KCAL_CHNL10_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG11 register *************/
#define BLE_RF_DIG_FULCAL_REG11_FCAL_CHNL11_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG11_FCAL_CHNL11_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG11_FCAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG11_FCAL_CHNL11  BLE_RF_DIG_FULCAL_REG11_FCAL_CHNL11_Msk
#define BLE_RF_DIG_FULCAL_REG11_ACAL_CHNL11_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG11_ACAL_CHNL11_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG11_ACAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG11_ACAL_CHNL11  BLE_RF_DIG_FULCAL_REG11_ACAL_CHNL11_Msk
#define BLE_RF_DIG_FULCAL_REG11_KCAL_CHNL11_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG11_KCAL_CHNL11_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG11_KCAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG11_KCAL_CHNL11  BLE_RF_DIG_FULCAL_REG11_KCAL_CHNL11_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG12 register *************/
#define BLE_RF_DIG_FULCAL_REG12_FCAL_CHNL12_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG12_FCAL_CHNL12_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG12_FCAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG12_FCAL_CHNL12  BLE_RF_DIG_FULCAL_REG12_FCAL_CHNL12_Msk
#define BLE_RF_DIG_FULCAL_REG12_ACAL_CHNL12_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG12_ACAL_CHNL12_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG12_ACAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG12_ACAL_CHNL12  BLE_RF_DIG_FULCAL_REG12_ACAL_CHNL12_Msk
#define BLE_RF_DIG_FULCAL_REG12_KCAL_CHNL12_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG12_KCAL_CHNL12_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG12_KCAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG12_KCAL_CHNL12  BLE_RF_DIG_FULCAL_REG12_KCAL_CHNL12_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG13 register *************/
#define BLE_RF_DIG_FULCAL_REG13_FCAL_CHNL13_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG13_FCAL_CHNL13_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG13_FCAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG13_FCAL_CHNL13  BLE_RF_DIG_FULCAL_REG13_FCAL_CHNL13_Msk
#define BLE_RF_DIG_FULCAL_REG13_ACAL_CHNL13_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG13_ACAL_CHNL13_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG13_ACAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG13_ACAL_CHNL13  BLE_RF_DIG_FULCAL_REG13_ACAL_CHNL13_Msk
#define BLE_RF_DIG_FULCAL_REG13_KCAL_CHNL13_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG13_KCAL_CHNL13_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG13_KCAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG13_KCAL_CHNL13  BLE_RF_DIG_FULCAL_REG13_KCAL_CHNL13_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG14 register *************/
#define BLE_RF_DIG_FULCAL_REG14_FCAL_CHNL14_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG14_FCAL_CHNL14_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG14_FCAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG14_FCAL_CHNL14  BLE_RF_DIG_FULCAL_REG14_FCAL_CHNL14_Msk
#define BLE_RF_DIG_FULCAL_REG14_ACAL_CHNL14_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG14_ACAL_CHNL14_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG14_ACAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG14_ACAL_CHNL14  BLE_RF_DIG_FULCAL_REG14_ACAL_CHNL14_Msk
#define BLE_RF_DIG_FULCAL_REG14_KCAL_CHNL14_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG14_KCAL_CHNL14_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG14_KCAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG14_KCAL_CHNL14  BLE_RF_DIG_FULCAL_REG14_KCAL_CHNL14_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG15 register *************/
#define BLE_RF_DIG_FULCAL_REG15_FCAL_CHNL15_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG15_FCAL_CHNL15_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG15_FCAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG15_FCAL_CHNL15  BLE_RF_DIG_FULCAL_REG15_FCAL_CHNL15_Msk
#define BLE_RF_DIG_FULCAL_REG15_ACAL_CHNL15_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG15_ACAL_CHNL15_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG15_ACAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG15_ACAL_CHNL15  BLE_RF_DIG_FULCAL_REG15_ACAL_CHNL15_Msk
#define BLE_RF_DIG_FULCAL_REG15_KCAL_CHNL15_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG15_KCAL_CHNL15_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG15_KCAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG15_KCAL_CHNL15  BLE_RF_DIG_FULCAL_REG15_KCAL_CHNL15_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG16 register *************/
#define BLE_RF_DIG_FULCAL_REG16_FCAL_CHNL16_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG16_FCAL_CHNL16_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG16_FCAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG16_FCAL_CHNL16  BLE_RF_DIG_FULCAL_REG16_FCAL_CHNL16_Msk
#define BLE_RF_DIG_FULCAL_REG16_ACAL_CHNL16_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG16_ACAL_CHNL16_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG16_ACAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG16_ACAL_CHNL16  BLE_RF_DIG_FULCAL_REG16_ACAL_CHNL16_Msk
#define BLE_RF_DIG_FULCAL_REG16_KCAL_CHNL16_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG16_KCAL_CHNL16_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG16_KCAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG16_KCAL_CHNL16  BLE_RF_DIG_FULCAL_REG16_KCAL_CHNL16_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG17 register *************/
#define BLE_RF_DIG_FULCAL_REG17_FCAL_CHNL17_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG17_FCAL_CHNL17_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG17_FCAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG17_FCAL_CHNL17  BLE_RF_DIG_FULCAL_REG17_FCAL_CHNL17_Msk
#define BLE_RF_DIG_FULCAL_REG17_ACAL_CHNL17_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG17_ACAL_CHNL17_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG17_ACAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG17_ACAL_CHNL17  BLE_RF_DIG_FULCAL_REG17_ACAL_CHNL17_Msk
#define BLE_RF_DIG_FULCAL_REG17_KCAL_CHNL17_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG17_KCAL_CHNL17_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG17_KCAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG17_KCAL_CHNL17  BLE_RF_DIG_FULCAL_REG17_KCAL_CHNL17_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG18 register *************/
#define BLE_RF_DIG_FULCAL_REG18_FCAL_CHNL18_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG18_FCAL_CHNL18_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG18_FCAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG18_FCAL_CHNL18  BLE_RF_DIG_FULCAL_REG18_FCAL_CHNL18_Msk
#define BLE_RF_DIG_FULCAL_REG18_ACAL_CHNL18_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG18_ACAL_CHNL18_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG18_ACAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG18_ACAL_CHNL18  BLE_RF_DIG_FULCAL_REG18_ACAL_CHNL18_Msk
#define BLE_RF_DIG_FULCAL_REG18_KCAL_CHNL18_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG18_KCAL_CHNL18_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG18_KCAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG18_KCAL_CHNL18  BLE_RF_DIG_FULCAL_REG18_KCAL_CHNL18_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG19 register *************/
#define BLE_RF_DIG_FULCAL_REG19_FCAL_CHNL19_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG19_FCAL_CHNL19_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG19_FCAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG19_FCAL_CHNL19  BLE_RF_DIG_FULCAL_REG19_FCAL_CHNL19_Msk
#define BLE_RF_DIG_FULCAL_REG19_ACAL_CHNL19_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG19_ACAL_CHNL19_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG19_ACAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG19_ACAL_CHNL19  BLE_RF_DIG_FULCAL_REG19_ACAL_CHNL19_Msk
#define BLE_RF_DIG_FULCAL_REG19_KCAL_CHNL19_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG19_KCAL_CHNL19_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG19_KCAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG19_KCAL_CHNL19  BLE_RF_DIG_FULCAL_REG19_KCAL_CHNL19_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG20 register *************/
#define BLE_RF_DIG_FULCAL_REG20_FCAL_CHNL20_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG20_FCAL_CHNL20_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG20_FCAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG20_FCAL_CHNL20  BLE_RF_DIG_FULCAL_REG20_FCAL_CHNL20_Msk
#define BLE_RF_DIG_FULCAL_REG20_ACAL_CHNL20_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG20_ACAL_CHNL20_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG20_ACAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG20_ACAL_CHNL20  BLE_RF_DIG_FULCAL_REG20_ACAL_CHNL20_Msk
#define BLE_RF_DIG_FULCAL_REG20_KCAL_CHNL20_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG20_KCAL_CHNL20_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG20_KCAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG20_KCAL_CHNL20  BLE_RF_DIG_FULCAL_REG20_KCAL_CHNL20_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG21 register *************/
#define BLE_RF_DIG_FULCAL_REG21_FCAL_CHNL21_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG21_FCAL_CHNL21_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG21_FCAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG21_FCAL_CHNL21  BLE_RF_DIG_FULCAL_REG21_FCAL_CHNL21_Msk
#define BLE_RF_DIG_FULCAL_REG21_ACAL_CHNL21_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG21_ACAL_CHNL21_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG21_ACAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG21_ACAL_CHNL21  BLE_RF_DIG_FULCAL_REG21_ACAL_CHNL21_Msk
#define BLE_RF_DIG_FULCAL_REG21_KCAL_CHNL21_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG21_KCAL_CHNL21_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG21_KCAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG21_KCAL_CHNL21  BLE_RF_DIG_FULCAL_REG21_KCAL_CHNL21_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG22 register *************/
#define BLE_RF_DIG_FULCAL_REG22_FCAL_CHNL22_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG22_FCAL_CHNL22_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG22_FCAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG22_FCAL_CHNL22  BLE_RF_DIG_FULCAL_REG22_FCAL_CHNL22_Msk
#define BLE_RF_DIG_FULCAL_REG22_ACAL_CHNL22_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG22_ACAL_CHNL22_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG22_ACAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG22_ACAL_CHNL22  BLE_RF_DIG_FULCAL_REG22_ACAL_CHNL22_Msk
#define BLE_RF_DIG_FULCAL_REG22_KCAL_CHNL22_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG22_KCAL_CHNL22_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG22_KCAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG22_KCAL_CHNL22  BLE_RF_DIG_FULCAL_REG22_KCAL_CHNL22_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG23 register *************/
#define BLE_RF_DIG_FULCAL_REG23_FCAL_CHNL23_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG23_FCAL_CHNL23_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG23_FCAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG23_FCAL_CHNL23  BLE_RF_DIG_FULCAL_REG23_FCAL_CHNL23_Msk
#define BLE_RF_DIG_FULCAL_REG23_ACAL_CHNL23_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG23_ACAL_CHNL23_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG23_ACAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG23_ACAL_CHNL23  BLE_RF_DIG_FULCAL_REG23_ACAL_CHNL23_Msk
#define BLE_RF_DIG_FULCAL_REG23_KCAL_CHNL23_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG23_KCAL_CHNL23_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG23_KCAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG23_KCAL_CHNL23  BLE_RF_DIG_FULCAL_REG23_KCAL_CHNL23_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG24 register *************/
#define BLE_RF_DIG_FULCAL_REG24_FCAL_CHNL24_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG24_FCAL_CHNL24_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG24_FCAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG24_FCAL_CHNL24  BLE_RF_DIG_FULCAL_REG24_FCAL_CHNL24_Msk
#define BLE_RF_DIG_FULCAL_REG24_ACAL_CHNL24_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG24_ACAL_CHNL24_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG24_ACAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG24_ACAL_CHNL24  BLE_RF_DIG_FULCAL_REG24_ACAL_CHNL24_Msk
#define BLE_RF_DIG_FULCAL_REG24_KCAL_CHNL24_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG24_KCAL_CHNL24_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG24_KCAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG24_KCAL_CHNL24  BLE_RF_DIG_FULCAL_REG24_KCAL_CHNL24_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG25 register *************/
#define BLE_RF_DIG_FULCAL_REG25_FCAL_CHNL25_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG25_FCAL_CHNL25_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG25_FCAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG25_FCAL_CHNL25  BLE_RF_DIG_FULCAL_REG25_FCAL_CHNL25_Msk
#define BLE_RF_DIG_FULCAL_REG25_ACAL_CHNL25_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG25_ACAL_CHNL25_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG25_ACAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG25_ACAL_CHNL25  BLE_RF_DIG_FULCAL_REG25_ACAL_CHNL25_Msk
#define BLE_RF_DIG_FULCAL_REG25_KCAL_CHNL25_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG25_KCAL_CHNL25_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG25_KCAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG25_KCAL_CHNL25  BLE_RF_DIG_FULCAL_REG25_KCAL_CHNL25_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG26 register *************/
#define BLE_RF_DIG_FULCAL_REG26_FCAL_CHNL26_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG26_FCAL_CHNL26_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG26_FCAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG26_FCAL_CHNL26  BLE_RF_DIG_FULCAL_REG26_FCAL_CHNL26_Msk
#define BLE_RF_DIG_FULCAL_REG26_ACAL_CHNL26_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG26_ACAL_CHNL26_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG26_ACAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG26_ACAL_CHNL26  BLE_RF_DIG_FULCAL_REG26_ACAL_CHNL26_Msk
#define BLE_RF_DIG_FULCAL_REG26_KCAL_CHNL26_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG26_KCAL_CHNL26_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG26_KCAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG26_KCAL_CHNL26  BLE_RF_DIG_FULCAL_REG26_KCAL_CHNL26_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG27 register *************/
#define BLE_RF_DIG_FULCAL_REG27_FCAL_CHNL27_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG27_FCAL_CHNL27_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG27_FCAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG27_FCAL_CHNL27  BLE_RF_DIG_FULCAL_REG27_FCAL_CHNL27_Msk
#define BLE_RF_DIG_FULCAL_REG27_ACAL_CHNL27_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG27_ACAL_CHNL27_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG27_ACAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG27_ACAL_CHNL27  BLE_RF_DIG_FULCAL_REG27_ACAL_CHNL27_Msk
#define BLE_RF_DIG_FULCAL_REG27_KCAL_CHNL27_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG27_KCAL_CHNL27_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG27_KCAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG27_KCAL_CHNL27  BLE_RF_DIG_FULCAL_REG27_KCAL_CHNL27_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG28 register *************/
#define BLE_RF_DIG_FULCAL_REG28_FCAL_CHNL28_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG28_FCAL_CHNL28_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG28_FCAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG28_FCAL_CHNL28  BLE_RF_DIG_FULCAL_REG28_FCAL_CHNL28_Msk
#define BLE_RF_DIG_FULCAL_REG28_ACAL_CHNL28_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG28_ACAL_CHNL28_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG28_ACAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG28_ACAL_CHNL28  BLE_RF_DIG_FULCAL_REG28_ACAL_CHNL28_Msk
#define BLE_RF_DIG_FULCAL_REG28_KCAL_CHNL28_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG28_KCAL_CHNL28_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG28_KCAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG28_KCAL_CHNL28  BLE_RF_DIG_FULCAL_REG28_KCAL_CHNL28_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG29 register *************/
#define BLE_RF_DIG_FULCAL_REG29_FCAL_CHNL29_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG29_FCAL_CHNL29_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG29_FCAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG29_FCAL_CHNL29  BLE_RF_DIG_FULCAL_REG29_FCAL_CHNL29_Msk
#define BLE_RF_DIG_FULCAL_REG29_ACAL_CHNL29_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG29_ACAL_CHNL29_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG29_ACAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG29_ACAL_CHNL29  BLE_RF_DIG_FULCAL_REG29_ACAL_CHNL29_Msk
#define BLE_RF_DIG_FULCAL_REG29_KCAL_CHNL29_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG29_KCAL_CHNL29_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG29_KCAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG29_KCAL_CHNL29  BLE_RF_DIG_FULCAL_REG29_KCAL_CHNL29_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG30 register *************/
#define BLE_RF_DIG_FULCAL_REG30_FCAL_CHNL30_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG30_FCAL_CHNL30_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG30_FCAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG30_FCAL_CHNL30  BLE_RF_DIG_FULCAL_REG30_FCAL_CHNL30_Msk
#define BLE_RF_DIG_FULCAL_REG30_ACAL_CHNL30_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG30_ACAL_CHNL30_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG30_ACAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG30_ACAL_CHNL30  BLE_RF_DIG_FULCAL_REG30_ACAL_CHNL30_Msk
#define BLE_RF_DIG_FULCAL_REG30_KCAL_CHNL30_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG30_KCAL_CHNL30_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG30_KCAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG30_KCAL_CHNL30  BLE_RF_DIG_FULCAL_REG30_KCAL_CHNL30_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG31 register *************/
#define BLE_RF_DIG_FULCAL_REG31_FCAL_CHNL31_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG31_FCAL_CHNL31_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG31_FCAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG31_FCAL_CHNL31  BLE_RF_DIG_FULCAL_REG31_FCAL_CHNL31_Msk
#define BLE_RF_DIG_FULCAL_REG31_ACAL_CHNL31_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG31_ACAL_CHNL31_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG31_ACAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG31_ACAL_CHNL31  BLE_RF_DIG_FULCAL_REG31_ACAL_CHNL31_Msk
#define BLE_RF_DIG_FULCAL_REG31_KCAL_CHNL31_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG31_KCAL_CHNL31_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG31_KCAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG31_KCAL_CHNL31  BLE_RF_DIG_FULCAL_REG31_KCAL_CHNL31_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG32 register *************/
#define BLE_RF_DIG_FULCAL_REG32_FCAL_CHNL32_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG32_FCAL_CHNL32_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG32_FCAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG32_FCAL_CHNL32  BLE_RF_DIG_FULCAL_REG32_FCAL_CHNL32_Msk
#define BLE_RF_DIG_FULCAL_REG32_ACAL_CHNL32_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG32_ACAL_CHNL32_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG32_ACAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG32_ACAL_CHNL32  BLE_RF_DIG_FULCAL_REG32_ACAL_CHNL32_Msk
#define BLE_RF_DIG_FULCAL_REG32_KCAL_CHNL32_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG32_KCAL_CHNL32_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG32_KCAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG32_KCAL_CHNL32  BLE_RF_DIG_FULCAL_REG32_KCAL_CHNL32_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG33 register *************/
#define BLE_RF_DIG_FULCAL_REG33_FCAL_CHNL33_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG33_FCAL_CHNL33_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG33_FCAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG33_FCAL_CHNL33  BLE_RF_DIG_FULCAL_REG33_FCAL_CHNL33_Msk
#define BLE_RF_DIG_FULCAL_REG33_ACAL_CHNL33_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG33_ACAL_CHNL33_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG33_ACAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG33_ACAL_CHNL33  BLE_RF_DIG_FULCAL_REG33_ACAL_CHNL33_Msk
#define BLE_RF_DIG_FULCAL_REG33_KCAL_CHNL33_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG33_KCAL_CHNL33_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG33_KCAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG33_KCAL_CHNL33  BLE_RF_DIG_FULCAL_REG33_KCAL_CHNL33_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG34 register *************/
#define BLE_RF_DIG_FULCAL_REG34_FCAL_CHNL34_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG34_FCAL_CHNL34_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG34_FCAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG34_FCAL_CHNL34  BLE_RF_DIG_FULCAL_REG34_FCAL_CHNL34_Msk
#define BLE_RF_DIG_FULCAL_REG34_ACAL_CHNL34_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG34_ACAL_CHNL34_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG34_ACAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG34_ACAL_CHNL34  BLE_RF_DIG_FULCAL_REG34_ACAL_CHNL34_Msk
#define BLE_RF_DIG_FULCAL_REG34_KCAL_CHNL34_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG34_KCAL_CHNL34_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG34_KCAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG34_KCAL_CHNL34  BLE_RF_DIG_FULCAL_REG34_KCAL_CHNL34_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG35 register *************/
#define BLE_RF_DIG_FULCAL_REG35_FCAL_CHNL35_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG35_FCAL_CHNL35_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG35_FCAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG35_FCAL_CHNL35  BLE_RF_DIG_FULCAL_REG35_FCAL_CHNL35_Msk
#define BLE_RF_DIG_FULCAL_REG35_ACAL_CHNL35_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG35_ACAL_CHNL35_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG35_ACAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG35_ACAL_CHNL35  BLE_RF_DIG_FULCAL_REG35_ACAL_CHNL35_Msk
#define BLE_RF_DIG_FULCAL_REG35_KCAL_CHNL35_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG35_KCAL_CHNL35_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG35_KCAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG35_KCAL_CHNL35  BLE_RF_DIG_FULCAL_REG35_KCAL_CHNL35_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG36 register *************/
#define BLE_RF_DIG_FULCAL_REG36_FCAL_CHNL36_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG36_FCAL_CHNL36_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG36_FCAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG36_FCAL_CHNL36  BLE_RF_DIG_FULCAL_REG36_FCAL_CHNL36_Msk
#define BLE_RF_DIG_FULCAL_REG36_ACAL_CHNL36_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG36_ACAL_CHNL36_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG36_ACAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG36_ACAL_CHNL36  BLE_RF_DIG_FULCAL_REG36_ACAL_CHNL36_Msk
#define BLE_RF_DIG_FULCAL_REG36_KCAL_CHNL36_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG36_KCAL_CHNL36_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG36_KCAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG36_KCAL_CHNL36  BLE_RF_DIG_FULCAL_REG36_KCAL_CHNL36_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG37 register *************/
#define BLE_RF_DIG_FULCAL_REG37_FCAL_CHNL37_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG37_FCAL_CHNL37_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG37_FCAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG37_FCAL_CHNL37  BLE_RF_DIG_FULCAL_REG37_FCAL_CHNL37_Msk
#define BLE_RF_DIG_FULCAL_REG37_ACAL_CHNL37_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG37_ACAL_CHNL37_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG37_ACAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG37_ACAL_CHNL37  BLE_RF_DIG_FULCAL_REG37_ACAL_CHNL37_Msk
#define BLE_RF_DIG_FULCAL_REG37_KCAL_CHNL37_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG37_KCAL_CHNL37_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG37_KCAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG37_KCAL_CHNL37  BLE_RF_DIG_FULCAL_REG37_KCAL_CHNL37_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG38 register *************/
#define BLE_RF_DIG_FULCAL_REG38_FCAL_CHNL38_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG38_FCAL_CHNL38_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG38_FCAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG38_FCAL_CHNL38  BLE_RF_DIG_FULCAL_REG38_FCAL_CHNL38_Msk
#define BLE_RF_DIG_FULCAL_REG38_ACAL_CHNL38_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG38_ACAL_CHNL38_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG38_ACAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG38_ACAL_CHNL38  BLE_RF_DIG_FULCAL_REG38_ACAL_CHNL38_Msk
#define BLE_RF_DIG_FULCAL_REG38_KCAL_CHNL38_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG38_KCAL_CHNL38_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG38_KCAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG38_KCAL_CHNL38  BLE_RF_DIG_FULCAL_REG38_KCAL_CHNL38_Msk

/************ Bit definition for BLE_RF_DIG_FULCAL_REG39 register *************/
#define BLE_RF_DIG_FULCAL_REG39_FCAL_CHNL39_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG39_FCAL_CHNL39_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG39_FCAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG39_FCAL_CHNL39  BLE_RF_DIG_FULCAL_REG39_FCAL_CHNL39_Msk
#define BLE_RF_DIG_FULCAL_REG39_ACAL_CHNL39_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG39_ACAL_CHNL39_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG39_ACAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG39_ACAL_CHNL39  BLE_RF_DIG_FULCAL_REG39_ACAL_CHNL39_Msk
#define BLE_RF_DIG_FULCAL_REG39_KCAL_CHNL39_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG39_KCAL_CHNL39_Msk  (0xFFFUL << BLE_RF_DIG_FULCAL_REG39_KCAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG39_KCAL_CHNL39  BLE_RF_DIG_FULCAL_REG39_KCAL_CHNL39_Msk

/************* Bit definition for BLE_RF_DIG_INCCAL_REG1 register *************/
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_EN_Pos  (0U)
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_EN_Msk  (0x1UL << BLE_RF_DIG_INCCAL_REG1_INCACAL_EN_Pos)
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_EN  BLE_RF_DIG_INCCAL_REG1_INCACAL_EN_Msk
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_EN_Pos  (1U)
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_EN_Msk  (0x1UL << BLE_RF_DIG_INCCAL_REG1_INCFCAL_EN_Pos)
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_EN  BLE_RF_DIG_INCCAL_REG1_INCFCAL_EN_Msk
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_WAIT_TIME_Pos  (2U)
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_WAIT_TIME_Msk  (0x3FUL << BLE_RF_DIG_INCCAL_REG1_INCACAL_WAIT_TIME_Pos)
#define BLE_RF_DIG_INCCAL_REG1_INCACAL_WAIT_TIME  BLE_RF_DIG_INCCAL_REG1_INCACAL_WAIT_TIME_Msk
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_WAIT_TIME_Pos  (8U)
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_WAIT_TIME_Msk  (0x3FUL << BLE_RF_DIG_INCCAL_REG1_INCFCAL_WAIT_TIME_Pos)
#define BLE_RF_DIG_INCCAL_REG1_INCFCAL_WAIT_TIME  BLE_RF_DIG_INCCAL_REG1_INCFCAL_WAIT_TIME_Msk
#define BLE_RF_DIG_INCCAL_REG1_IDAC_OFFSET_Pos  (14U)
#define BLE_RF_DIG_INCCAL_REG1_IDAC_OFFSET_Msk  (0x7FUL << BLE_RF_DIG_INCCAL_REG1_IDAC_OFFSET_Pos)
#define BLE_RF_DIG_INCCAL_REG1_IDAC_OFFSET  BLE_RF_DIG_INCCAL_REG1_IDAC_OFFSET_Msk
#define BLE_RF_DIG_INCCAL_REG1_PDX_OFFSET_Pos  (21U)
#define BLE_RF_DIG_INCCAL_REG1_PDX_OFFSET_Msk  (0xFFUL << BLE_RF_DIG_INCCAL_REG1_PDX_OFFSET_Pos)
#define BLE_RF_DIG_INCCAL_REG1_PDX_OFFSET  BLE_RF_DIG_INCCAL_REG1_PDX_OFFSET_Msk
#define BLE_RF_DIG_INCCAL_REG1_INCCAL_START_Pos  (29U)
#define BLE_RF_DIG_INCCAL_REG1_INCCAL_START_Msk  (0x1UL << BLE_RF_DIG_INCCAL_REG1_INCCAL_START_Pos)
#define BLE_RF_DIG_INCCAL_REG1_INCCAL_START  BLE_RF_DIG_INCCAL_REG1_INCCAL_START_Msk
#define BLE_RF_DIG_INCCAL_REG1_FRC_INCCAL_CLK_ON_Pos  (30U)
#define BLE_RF_DIG_INCCAL_REG1_FRC_INCCAL_CLK_ON_Msk  (0x1UL << BLE_RF_DIG_INCCAL_REG1_FRC_INCCAL_CLK_ON_Pos)
#define BLE_RF_DIG_INCCAL_REG1_FRC_INCCAL_CLK_ON  BLE_RF_DIG_INCCAL_REG1_FRC_INCCAL_CLK_ON_Msk

/************* Bit definition for BLE_RF_DIG_ROSCAL_REG1 register *************/
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_START_Pos  (0U)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_START_Msk  (0x1UL << BLE_RF_DIG_ROSCAL_REG1_ROSCAL_START_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_START  BLE_RF_DIG_ROSCAL_REG1_ROSCAL_START_Msk
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_BYPASS_Pos  (1U)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_BYPASS_Msk  (0x1UL << BLE_RF_DIG_ROSCAL_REG1_ROSCAL_BYPASS_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_BYPASS  BLE_RF_DIG_ROSCAL_REG1_ROSCAL_BYPASS_Msk
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_I_Pos  (2U)
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_I_Msk  (0x1UL << BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_I_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_I  BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_I_Msk
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_Q_Pos  (3U)
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_Q_Msk  (0x1UL << BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_Q_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_Q  BLE_RF_DIG_ROSCAL_REG1_EN_ROSDAC_Q_Msk
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TA_Pos  (4U)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TA_Msk  (0x1FFUL << BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TA_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TA  BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TA_Msk
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TB_Pos  (13U)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TB_Msk  (0xFUL << BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TB_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TB  BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TB_Msk
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TC_Pos  (17U)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TC_Msk  (0x7FUL << BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TC_Pos)
#define BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TC  BLE_RF_DIG_ROSCAL_REG1_ROSCAL_TC_Msk

/************* Bit definition for BLE_RF_DIG_ROSCAL_REG2 register *************/
#define BLE_RF_DIG_ROSCAL_REG2_ROSCAL_DONE_Pos  (0U)
#define BLE_RF_DIG_ROSCAL_REG2_ROSCAL_DONE_Msk  (0x1UL << BLE_RF_DIG_ROSCAL_REG2_ROSCAL_DONE_Pos)
#define BLE_RF_DIG_ROSCAL_REG2_ROSCAL_DONE  BLE_RF_DIG_ROSCAL_REG2_ROSCAL_DONE_Msk
#define BLE_RF_DIG_ROSCAL_REG2_DOS_I_SW_Pos  (1U)
#define BLE_RF_DIG_ROSCAL_REG2_DOS_I_SW_Msk  (0x7FUL << BLE_RF_DIG_ROSCAL_REG2_DOS_I_SW_Pos)
#define BLE_RF_DIG_ROSCAL_REG2_DOS_I_SW  BLE_RF_DIG_ROSCAL_REG2_DOS_I_SW_Msk
#define BLE_RF_DIG_ROSCAL_REG2_DOS_Q_SW_Pos  (8U)
#define BLE_RF_DIG_ROSCAL_REG2_DOS_Q_SW_Msk  (0x7FUL << BLE_RF_DIG_ROSCAL_REG2_DOS_Q_SW_Pos)
#define BLE_RF_DIG_ROSCAL_REG2_DOS_Q_SW  BLE_RF_DIG_ROSCAL_REG2_DOS_Q_SW_Msk

/************ Bit definition for BLE_RF_DIG_RCROSCAL_REG register *************/
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_Q_Pos  (0U)
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_Q_Msk  (0x3FFUL << BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_Q_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_Q  BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_Q_Msk
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_I_Pos  (10U)
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_I_Msk  (0x3FFUL << BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_I_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_I  BLE_RF_DIG_RCROSCAL_REG_ROS_ADC_I_Msk
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_DONE_Pos  (20U)
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_DONE_Msk  (0x1UL << BLE_RF_DIG_RCROSCAL_REG_RCCAL_DONE_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_DONE  BLE_RF_DIG_RCROSCAL_REG_RCCAL_DONE_Msk
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_START_Pos  (21U)
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_START_Msk  (0x1UL << BLE_RF_DIG_RCROSCAL_REG_RCCAL_START_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_RCCAL_START  BLE_RF_DIG_RCROSCAL_REG_RCCAL_START_Msk
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_OFFSET_Pos  (22U)
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_OFFSET_Msk  (0xFUL << BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_OFFSET_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_OFFSET  BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_OFFSET_Msk
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_Pos  (26U)
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_Msk  (0x1FUL << BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_Pos)
#define BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE  BLE_RF_DIG_RCROSCAL_REG_RC_CAPCODE_Msk

/************** Bit definition for BLE_RF_DIG_PACAL_REG register **************/
#define BLE_RF_DIG_PACAL_REG_PACAL_START_Pos  (0U)
#define BLE_RF_DIG_PACAL_REG_PACAL_START_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_PACAL_START_Pos)
#define BLE_RF_DIG_PACAL_REG_PACAL_START  BLE_RF_DIG_PACAL_REG_PACAL_START_Msk
#define BLE_RF_DIG_PACAL_REG_PACAL_DONE_Pos  (1U)
#define BLE_RF_DIG_PACAL_REG_PACAL_DONE_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_PACAL_DONE_Pos)
#define BLE_RF_DIG_PACAL_REG_PACAL_DONE  BLE_RF_DIG_PACAL_REG_PACAL_DONE_Msk
#define BLE_RF_DIG_PACAL_REG_SGN_CAL_RSLT_Pos  (2U)
#define BLE_RF_DIG_PACAL_REG_SGN_CAL_RSLT_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_SGN_CAL_RSLT_Pos)
#define BLE_RF_DIG_PACAL_REG_SGN_CAL_RSLT  BLE_RF_DIG_PACAL_REG_SGN_CAL_RSLT_Msk
#define BLE_RF_DIG_PACAL_REG_BC_CAL_RSLT_Pos  (3U)
#define BLE_RF_DIG_PACAL_REG_BC_CAL_RSLT_Msk  (0xFUL << BLE_RF_DIG_PACAL_REG_BC_CAL_RSLT_Pos)
#define BLE_RF_DIG_PACAL_REG_BC_CAL_RSLT  BLE_RF_DIG_PACAL_REG_BC_CAL_RSLT_Msk
#define BLE_RF_DIG_PACAL_REG_PACAL_RDY_Pos  (7U)
#define BLE_RF_DIG_PACAL_REG_PACAL_RDY_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_PACAL_RDY_Pos)
#define BLE_RF_DIG_PACAL_REG_PACAL_RDY  BLE_RF_DIG_PACAL_REG_PACAL_RDY_Msk
#define BLE_RF_DIG_PACAL_REG_PA_RSTB_FRC_EN_Pos  (8U)
#define BLE_RF_DIG_PACAL_REG_PA_RSTB_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_PA_RSTB_FRC_EN_Pos)
#define BLE_RF_DIG_PACAL_REG_PA_RSTB_FRC_EN  BLE_RF_DIG_PACAL_REG_PA_RSTB_FRC_EN_Msk
#define BLE_RF_DIG_PACAL_REG_PACAL_CLK_EN_Pos  (9U)
#define BLE_RF_DIG_PACAL_REG_PACAL_CLK_EN_Msk  (0x1UL << BLE_RF_DIG_PACAL_REG_PACAL_CLK_EN_Pos)
#define BLE_RF_DIG_PACAL_REG_PACAL_CLK_EN  BLE_RF_DIG_PACAL_REG_PACAL_CLK_EN_Msk

/************ Bit definition for BLE_RF_DIG_CU_ADDR_REG0 register *************/
#define BLE_RF_DIG_CU_ADDR_REG0_RXON_CFG_ADDR_Pos  (0U)
#define BLE_RF_DIG_CU_ADDR_REG0_RXON_CFG_ADDR_Msk  (0x3FFUL << BLE_RF_DIG_CU_ADDR_REG0_RXON_CFG_ADDR_Pos)
#define BLE_RF_DIG_CU_ADDR_REG0_RXON_CFG_ADDR  BLE_RF_DIG_CU_ADDR_REG0_RXON_CFG_ADDR_Msk
#define BLE_RF_DIG_CU_ADDR_REG0_RXOFF_CFG_ADDR_Pos  (10U)
#define BLE_RF_DIG_CU_ADDR_REG0_RXOFF_CFG_ADDR_Msk  (0x3FFUL << BLE_RF_DIG_CU_ADDR_REG0_RXOFF_CFG_ADDR_Pos)
#define BLE_RF_DIG_CU_ADDR_REG0_RXOFF_CFG_ADDR  BLE_RF_DIG_CU_ADDR_REG0_RXOFF_CFG_ADDR_Msk

/************ Bit definition for BLE_RF_DIG_CU_ADDR_REG1 register *************/
#define BLE_RF_DIG_CU_ADDR_REG1_TXON_CFG_ADDR_Pos  (0U)
#define BLE_RF_DIG_CU_ADDR_REG1_TXON_CFG_ADDR_Msk  (0x3FFUL << BLE_RF_DIG_CU_ADDR_REG1_TXON_CFG_ADDR_Pos)
#define BLE_RF_DIG_CU_ADDR_REG1_TXON_CFG_ADDR  BLE_RF_DIG_CU_ADDR_REG1_TXON_CFG_ADDR_Msk
#define BLE_RF_DIG_CU_ADDR_REG1_TXOFF_CFG_ADDR_Pos  (10U)
#define BLE_RF_DIG_CU_ADDR_REG1_TXOFF_CFG_ADDR_Msk  (0x3FFUL << BLE_RF_DIG_CU_ADDR_REG1_TXOFF_CFG_ADDR_Pos)
#define BLE_RF_DIG_CU_ADDR_REG1_TXOFF_CFG_ADDR  BLE_RF_DIG_CU_ADDR_REG1_TXOFF_CFG_ADDR_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD0 register ***************/
#define BLE_RF_DIG_CFG_CMD0_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD0_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD0_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD0_CMD0        BLE_RF_DIG_CFG_CMD0_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD0_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD0_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD0_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD0_CMD1        BLE_RF_DIG_CFG_CMD0_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD1 register ***************/
#define BLE_RF_DIG_CFG_CMD1_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD1_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD1_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD1_CMD0        BLE_RF_DIG_CFG_CMD1_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD1_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD1_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD1_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD1_CMD1        BLE_RF_DIG_CFG_CMD1_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD2 register ***************/
#define BLE_RF_DIG_CFG_CMD2_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD2_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD2_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD2_CMD0        BLE_RF_DIG_CFG_CMD2_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD2_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD2_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD2_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD2_CMD1        BLE_RF_DIG_CFG_CMD2_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD3 register ***************/
#define BLE_RF_DIG_CFG_CMD3_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD3_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD3_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD3_CMD0        BLE_RF_DIG_CFG_CMD3_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD3_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD3_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD3_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD3_CMD1        BLE_RF_DIG_CFG_CMD3_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD4 register ***************/
#define BLE_RF_DIG_CFG_CMD4_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD4_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD4_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD4_CMD0        BLE_RF_DIG_CFG_CMD4_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD4_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD4_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD4_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD4_CMD1        BLE_RF_DIG_CFG_CMD4_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD5 register ***************/
#define BLE_RF_DIG_CFG_CMD5_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD5_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD5_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD5_CMD0        BLE_RF_DIG_CFG_CMD5_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD5_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD5_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD5_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD5_CMD1        BLE_RF_DIG_CFG_CMD5_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD6 register ***************/
#define BLE_RF_DIG_CFG_CMD6_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD6_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD6_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD6_CMD0        BLE_RF_DIG_CFG_CMD6_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD6_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD6_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD6_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD6_CMD1        BLE_RF_DIG_CFG_CMD6_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD7 register ***************/
#define BLE_RF_DIG_CFG_CMD7_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD7_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD7_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD7_CMD0        BLE_RF_DIG_CFG_CMD7_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD7_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD7_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD7_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD7_CMD1        BLE_RF_DIG_CFG_CMD7_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD8 register ***************/
#define BLE_RF_DIG_CFG_CMD8_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD8_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD8_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD8_CMD0        BLE_RF_DIG_CFG_CMD8_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD8_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD8_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD8_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD8_CMD1        BLE_RF_DIG_CFG_CMD8_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD9 register ***************/
#define BLE_RF_DIG_CFG_CMD9_CMD0_Pos    (0U)
#define BLE_RF_DIG_CFG_CMD9_CMD0_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD9_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD9_CMD0        BLE_RF_DIG_CFG_CMD9_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD9_CMD1_Pos    (16U)
#define BLE_RF_DIG_CFG_CMD9_CMD1_Msk    (0x1FFFUL << BLE_RF_DIG_CFG_CMD9_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD9_CMD1        BLE_RF_DIG_CFG_CMD9_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD10 register **************/
#define BLE_RF_DIG_CFG_CMD10_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD10_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD10_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD10_CMD0       BLE_RF_DIG_CFG_CMD10_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD10_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD10_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD10_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD10_CMD1       BLE_RF_DIG_CFG_CMD10_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD11 register **************/
#define BLE_RF_DIG_CFG_CMD11_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD11_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD11_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD11_CMD0       BLE_RF_DIG_CFG_CMD11_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD11_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD11_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD11_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD11_CMD1       BLE_RF_DIG_CFG_CMD11_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD12 register **************/
#define BLE_RF_DIG_CFG_CMD12_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD12_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD12_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD12_CMD0       BLE_RF_DIG_CFG_CMD12_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD12_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD12_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD12_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD12_CMD1       BLE_RF_DIG_CFG_CMD12_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD13 register **************/
#define BLE_RF_DIG_CFG_CMD13_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD13_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD13_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD13_CMD0       BLE_RF_DIG_CFG_CMD13_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD13_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD13_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD13_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD13_CMD1       BLE_RF_DIG_CFG_CMD13_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD14 register **************/
#define BLE_RF_DIG_CFG_CMD14_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD14_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD14_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD14_CMD0       BLE_RF_DIG_CFG_CMD14_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD14_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD14_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD14_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD14_CMD1       BLE_RF_DIG_CFG_CMD14_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD15 register **************/
#define BLE_RF_DIG_CFG_CMD15_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD15_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD15_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD15_CMD0       BLE_RF_DIG_CFG_CMD15_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD15_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD15_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD15_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD15_CMD1       BLE_RF_DIG_CFG_CMD15_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD16 register **************/
#define BLE_RF_DIG_CFG_CMD16_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD16_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD16_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD16_CMD0       BLE_RF_DIG_CFG_CMD16_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD16_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD16_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD16_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD16_CMD1       BLE_RF_DIG_CFG_CMD16_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD17 register **************/
#define BLE_RF_DIG_CFG_CMD17_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD17_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD17_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD17_CMD0       BLE_RF_DIG_CFG_CMD17_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD17_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD17_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD17_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD17_CMD1       BLE_RF_DIG_CFG_CMD17_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD18 register **************/
#define BLE_RF_DIG_CFG_CMD18_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD18_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD18_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD18_CMD0       BLE_RF_DIG_CFG_CMD18_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD18_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD18_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD18_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD18_CMD1       BLE_RF_DIG_CFG_CMD18_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD19 register **************/
#define BLE_RF_DIG_CFG_CMD19_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD19_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD19_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD19_CMD0       BLE_RF_DIG_CFG_CMD19_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD19_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD19_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD19_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD19_CMD1       BLE_RF_DIG_CFG_CMD19_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD20 register **************/
#define BLE_RF_DIG_CFG_CMD20_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD20_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD20_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD20_CMD0       BLE_RF_DIG_CFG_CMD20_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD20_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD20_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD20_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD20_CMD1       BLE_RF_DIG_CFG_CMD20_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD21 register **************/
#define BLE_RF_DIG_CFG_CMD21_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD21_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD21_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD21_CMD0       BLE_RF_DIG_CFG_CMD21_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD21_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD21_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD21_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD21_CMD1       BLE_RF_DIG_CFG_CMD21_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD22 register **************/
#define BLE_RF_DIG_CFG_CMD22_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD22_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD22_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD22_CMD0       BLE_RF_DIG_CFG_CMD22_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD22_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD22_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD22_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD22_CMD1       BLE_RF_DIG_CFG_CMD22_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD23 register **************/
#define BLE_RF_DIG_CFG_CMD23_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD23_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD23_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD23_CMD0       BLE_RF_DIG_CFG_CMD23_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD23_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD23_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD23_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD23_CMD1       BLE_RF_DIG_CFG_CMD23_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD24 register **************/
#define BLE_RF_DIG_CFG_CMD24_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD24_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD24_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD24_CMD0       BLE_RF_DIG_CFG_CMD24_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD24_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD24_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD24_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD24_CMD1       BLE_RF_DIG_CFG_CMD24_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD25 register **************/
#define BLE_RF_DIG_CFG_CMD25_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD25_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD25_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD25_CMD0       BLE_RF_DIG_CFG_CMD25_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD25_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD25_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD25_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD25_CMD1       BLE_RF_DIG_CFG_CMD25_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD26 register **************/
#define BLE_RF_DIG_CFG_CMD26_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD26_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD26_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD26_CMD0       BLE_RF_DIG_CFG_CMD26_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD26_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD26_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD26_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD26_CMD1       BLE_RF_DIG_CFG_CMD26_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD27 register **************/
#define BLE_RF_DIG_CFG_CMD27_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD27_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD27_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD27_CMD0       BLE_RF_DIG_CFG_CMD27_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD27_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD27_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD27_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD27_CMD1       BLE_RF_DIG_CFG_CMD27_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD28 register **************/
#define BLE_RF_DIG_CFG_CMD28_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD28_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD28_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD28_CMD0       BLE_RF_DIG_CFG_CMD28_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD28_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD28_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD28_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD28_CMD1       BLE_RF_DIG_CFG_CMD28_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD29 register **************/
#define BLE_RF_DIG_CFG_CMD29_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD29_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD29_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD29_CMD0       BLE_RF_DIG_CFG_CMD29_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD29_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD29_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD29_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD29_CMD1       BLE_RF_DIG_CFG_CMD29_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD30 register **************/
#define BLE_RF_DIG_CFG_CMD30_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD30_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD30_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD30_CMD0       BLE_RF_DIG_CFG_CMD30_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD30_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD30_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD30_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD30_CMD1       BLE_RF_DIG_CFG_CMD30_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD31 register **************/
#define BLE_RF_DIG_CFG_CMD31_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD31_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD31_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD31_CMD0       BLE_RF_DIG_CFG_CMD31_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD31_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD31_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD31_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD31_CMD1       BLE_RF_DIG_CFG_CMD31_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD32 register **************/
#define BLE_RF_DIG_CFG_CMD32_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD32_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD32_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD32_CMD0       BLE_RF_DIG_CFG_CMD32_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD32_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD32_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD32_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD32_CMD1       BLE_RF_DIG_CFG_CMD32_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD33 register **************/
#define BLE_RF_DIG_CFG_CMD33_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD33_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD33_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD33_CMD0       BLE_RF_DIG_CFG_CMD33_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD33_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD33_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD33_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD33_CMD1       BLE_RF_DIG_CFG_CMD33_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD34 register **************/
#define BLE_RF_DIG_CFG_CMD34_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD34_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD34_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD34_CMD0       BLE_RF_DIG_CFG_CMD34_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD34_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD34_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD34_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD34_CMD1       BLE_RF_DIG_CFG_CMD34_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD35 register **************/
#define BLE_RF_DIG_CFG_CMD35_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD35_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD35_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD35_CMD0       BLE_RF_DIG_CFG_CMD35_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD35_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD35_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD35_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD35_CMD1       BLE_RF_DIG_CFG_CMD35_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD36 register **************/
#define BLE_RF_DIG_CFG_CMD36_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD36_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD36_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD36_CMD0       BLE_RF_DIG_CFG_CMD36_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD36_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD36_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD36_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD36_CMD1       BLE_RF_DIG_CFG_CMD36_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD37 register **************/
#define BLE_RF_DIG_CFG_CMD37_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD37_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD37_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD37_CMD0       BLE_RF_DIG_CFG_CMD37_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD37_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD37_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD37_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD37_CMD1       BLE_RF_DIG_CFG_CMD37_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD38 register **************/
#define BLE_RF_DIG_CFG_CMD38_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD38_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD38_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD38_CMD0       BLE_RF_DIG_CFG_CMD38_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD38_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD38_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD38_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD38_CMD1       BLE_RF_DIG_CFG_CMD38_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD39 register **************/
#define BLE_RF_DIG_CFG_CMD39_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD39_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD39_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD39_CMD0       BLE_RF_DIG_CFG_CMD39_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD39_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD39_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD39_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD39_CMD1       BLE_RF_DIG_CFG_CMD39_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD40 register **************/
#define BLE_RF_DIG_CFG_CMD40_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD40_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD40_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD40_CMD0       BLE_RF_DIG_CFG_CMD40_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD40_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD40_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD40_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD40_CMD1       BLE_RF_DIG_CFG_CMD40_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD41 register **************/
#define BLE_RF_DIG_CFG_CMD41_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD41_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD41_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD41_CMD0       BLE_RF_DIG_CFG_CMD41_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD41_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD41_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD41_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD41_CMD1       BLE_RF_DIG_CFG_CMD41_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD42 register **************/
#define BLE_RF_DIG_CFG_CMD42_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD42_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD42_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD42_CMD0       BLE_RF_DIG_CFG_CMD42_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD42_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD42_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD42_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD42_CMD1       BLE_RF_DIG_CFG_CMD42_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD43 register **************/
#define BLE_RF_DIG_CFG_CMD43_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD43_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD43_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD43_CMD0       BLE_RF_DIG_CFG_CMD43_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD43_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD43_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD43_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD43_CMD1       BLE_RF_DIG_CFG_CMD43_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD44 register **************/
#define BLE_RF_DIG_CFG_CMD44_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD44_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD44_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD44_CMD0       BLE_RF_DIG_CFG_CMD44_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD44_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD44_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD44_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD44_CMD1       BLE_RF_DIG_CFG_CMD44_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD45 register **************/
#define BLE_RF_DIG_CFG_CMD45_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD45_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD45_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD45_CMD0       BLE_RF_DIG_CFG_CMD45_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD45_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD45_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD45_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD45_CMD1       BLE_RF_DIG_CFG_CMD45_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD46 register **************/
#define BLE_RF_DIG_CFG_CMD46_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD46_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD46_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD46_CMD0       BLE_RF_DIG_CFG_CMD46_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD46_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD46_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD46_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD46_CMD1       BLE_RF_DIG_CFG_CMD46_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD47 register **************/
#define BLE_RF_DIG_CFG_CMD47_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD47_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD47_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD47_CMD0       BLE_RF_DIG_CFG_CMD47_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD47_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD47_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD47_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD47_CMD1       BLE_RF_DIG_CFG_CMD47_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD48 register **************/
#define BLE_RF_DIG_CFG_CMD48_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD48_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD48_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD48_CMD0       BLE_RF_DIG_CFG_CMD48_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD48_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD48_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD48_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD48_CMD1       BLE_RF_DIG_CFG_CMD48_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD49 register **************/
#define BLE_RF_DIG_CFG_CMD49_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD49_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD49_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD49_CMD0       BLE_RF_DIG_CFG_CMD49_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD49_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD49_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD49_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD49_CMD1       BLE_RF_DIG_CFG_CMD49_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD50 register **************/
#define BLE_RF_DIG_CFG_CMD50_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD50_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD50_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD50_CMD0       BLE_RF_DIG_CFG_CMD50_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD50_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD50_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD50_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD50_CMD1       BLE_RF_DIG_CFG_CMD50_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD51 register **************/
#define BLE_RF_DIG_CFG_CMD51_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD51_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD51_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD51_CMD0       BLE_RF_DIG_CFG_CMD51_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD51_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD51_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD51_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD51_CMD1       BLE_RF_DIG_CFG_CMD51_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD52 register **************/
#define BLE_RF_DIG_CFG_CMD52_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD52_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD52_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD52_CMD0       BLE_RF_DIG_CFG_CMD52_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD52_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD52_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD52_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD52_CMD1       BLE_RF_DIG_CFG_CMD52_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD53 register **************/
#define BLE_RF_DIG_CFG_CMD53_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD53_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD53_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD53_CMD0       BLE_RF_DIG_CFG_CMD53_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD53_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD53_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD53_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD53_CMD1       BLE_RF_DIG_CFG_CMD53_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD54 register **************/
#define BLE_RF_DIG_CFG_CMD54_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD54_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD54_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD54_CMD0       BLE_RF_DIG_CFG_CMD54_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD54_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD54_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD54_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD54_CMD1       BLE_RF_DIG_CFG_CMD54_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD55 register **************/
#define BLE_RF_DIG_CFG_CMD55_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD55_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD55_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD55_CMD0       BLE_RF_DIG_CFG_CMD55_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD55_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD55_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD55_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD55_CMD1       BLE_RF_DIG_CFG_CMD55_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD56 register **************/
#define BLE_RF_DIG_CFG_CMD56_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD56_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD56_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD56_CMD0       BLE_RF_DIG_CFG_CMD56_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD56_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD56_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD56_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD56_CMD1       BLE_RF_DIG_CFG_CMD56_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD57 register **************/
#define BLE_RF_DIG_CFG_CMD57_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD57_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD57_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD57_CMD0       BLE_RF_DIG_CFG_CMD57_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD57_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD57_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD57_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD57_CMD1       BLE_RF_DIG_CFG_CMD57_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD58 register **************/
#define BLE_RF_DIG_CFG_CMD58_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD58_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD58_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD58_CMD0       BLE_RF_DIG_CFG_CMD58_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD58_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD58_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD58_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD58_CMD1       BLE_RF_DIG_CFG_CMD58_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD59 register **************/
#define BLE_RF_DIG_CFG_CMD59_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD59_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD59_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD59_CMD0       BLE_RF_DIG_CFG_CMD59_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD59_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD59_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD59_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD59_CMD1       BLE_RF_DIG_CFG_CMD59_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD60 register **************/
#define BLE_RF_DIG_CFG_CMD60_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD60_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD60_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD60_CMD0       BLE_RF_DIG_CFG_CMD60_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD60_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD60_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD60_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD60_CMD1       BLE_RF_DIG_CFG_CMD60_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD61 register **************/
#define BLE_RF_DIG_CFG_CMD61_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD61_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD61_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD61_CMD0       BLE_RF_DIG_CFG_CMD61_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD61_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD61_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD61_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD61_CMD1       BLE_RF_DIG_CFG_CMD61_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD62 register **************/
#define BLE_RF_DIG_CFG_CMD62_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD62_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD62_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD62_CMD0       BLE_RF_DIG_CFG_CMD62_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD62_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD62_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD62_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD62_CMD1       BLE_RF_DIG_CFG_CMD62_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD63 register **************/
#define BLE_RF_DIG_CFG_CMD63_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD63_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD63_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD63_CMD0       BLE_RF_DIG_CFG_CMD63_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD63_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD63_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD63_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD63_CMD1       BLE_RF_DIG_CFG_CMD63_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD64 register **************/
#define BLE_RF_DIG_CFG_CMD64_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD64_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD64_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD64_CMD0       BLE_RF_DIG_CFG_CMD64_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD64_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD64_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD64_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD64_CMD1       BLE_RF_DIG_CFG_CMD64_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD65 register **************/
#define BLE_RF_DIG_CFG_CMD65_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD65_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD65_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD65_CMD0       BLE_RF_DIG_CFG_CMD65_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD65_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD65_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD65_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD65_CMD1       BLE_RF_DIG_CFG_CMD65_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD66 register **************/
#define BLE_RF_DIG_CFG_CMD66_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD66_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD66_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD66_CMD0       BLE_RF_DIG_CFG_CMD66_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD66_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD66_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD66_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD66_CMD1       BLE_RF_DIG_CFG_CMD66_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD67 register **************/
#define BLE_RF_DIG_CFG_CMD67_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD67_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD67_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD67_CMD0       BLE_RF_DIG_CFG_CMD67_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD67_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD67_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD67_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD67_CMD1       BLE_RF_DIG_CFG_CMD67_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD68 register **************/
#define BLE_RF_DIG_CFG_CMD68_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD68_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD68_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD68_CMD0       BLE_RF_DIG_CFG_CMD68_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD68_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD68_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD68_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD68_CMD1       BLE_RF_DIG_CFG_CMD68_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD69 register **************/
#define BLE_RF_DIG_CFG_CMD69_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD69_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD69_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD69_CMD0       BLE_RF_DIG_CFG_CMD69_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD69_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD69_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD69_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD69_CMD1       BLE_RF_DIG_CFG_CMD69_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD70 register **************/
#define BLE_RF_DIG_CFG_CMD70_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD70_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD70_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD70_CMD0       BLE_RF_DIG_CFG_CMD70_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD70_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD70_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD70_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD70_CMD1       BLE_RF_DIG_CFG_CMD70_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD71 register **************/
#define BLE_RF_DIG_CFG_CMD71_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD71_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD71_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD71_CMD0       BLE_RF_DIG_CFG_CMD71_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD71_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD71_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD71_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD71_CMD1       BLE_RF_DIG_CFG_CMD71_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD72 register **************/
#define BLE_RF_DIG_CFG_CMD72_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD72_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD72_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD72_CMD0       BLE_RF_DIG_CFG_CMD72_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD72_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD72_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD72_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD72_CMD1       BLE_RF_DIG_CFG_CMD72_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD73 register **************/
#define BLE_RF_DIG_CFG_CMD73_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD73_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD73_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD73_CMD0       BLE_RF_DIG_CFG_CMD73_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD73_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD73_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD73_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD73_CMD1       BLE_RF_DIG_CFG_CMD73_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD74 register **************/
#define BLE_RF_DIG_CFG_CMD74_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD74_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD74_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD74_CMD0       BLE_RF_DIG_CFG_CMD74_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD74_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD74_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD74_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD74_CMD1       BLE_RF_DIG_CFG_CMD74_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD75 register **************/
#define BLE_RF_DIG_CFG_CMD75_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD75_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD75_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD75_CMD0       BLE_RF_DIG_CFG_CMD75_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD75_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD75_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD75_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD75_CMD1       BLE_RF_DIG_CFG_CMD75_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD76 register **************/
#define BLE_RF_DIG_CFG_CMD76_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD76_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD76_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD76_CMD0       BLE_RF_DIG_CFG_CMD76_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD76_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD76_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD76_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD76_CMD1       BLE_RF_DIG_CFG_CMD76_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD77 register **************/
#define BLE_RF_DIG_CFG_CMD77_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD77_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD77_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD77_CMD0       BLE_RF_DIG_CFG_CMD77_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD77_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD77_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD77_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD77_CMD1       BLE_RF_DIG_CFG_CMD77_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD78 register **************/
#define BLE_RF_DIG_CFG_CMD78_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD78_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD78_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD78_CMD0       BLE_RF_DIG_CFG_CMD78_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD78_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD78_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD78_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD78_CMD1       BLE_RF_DIG_CFG_CMD78_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD79 register **************/
#define BLE_RF_DIG_CFG_CMD79_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD79_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD79_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD79_CMD0       BLE_RF_DIG_CFG_CMD79_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD79_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD79_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD79_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD79_CMD1       BLE_RF_DIG_CFG_CMD79_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD80 register **************/
#define BLE_RF_DIG_CFG_CMD80_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD80_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD80_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD80_CMD0       BLE_RF_DIG_CFG_CMD80_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD80_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD80_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD80_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD80_CMD1       BLE_RF_DIG_CFG_CMD80_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD81 register **************/
#define BLE_RF_DIG_CFG_CMD81_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD81_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD81_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD81_CMD0       BLE_RF_DIG_CFG_CMD81_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD81_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD81_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD81_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD81_CMD1       BLE_RF_DIG_CFG_CMD81_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD82 register **************/
#define BLE_RF_DIG_CFG_CMD82_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD82_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD82_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD82_CMD0       BLE_RF_DIG_CFG_CMD82_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD82_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD82_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD82_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD82_CMD1       BLE_RF_DIG_CFG_CMD82_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD83 register **************/
#define BLE_RF_DIG_CFG_CMD83_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD83_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD83_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD83_CMD0       BLE_RF_DIG_CFG_CMD83_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD83_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD83_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD83_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD83_CMD1       BLE_RF_DIG_CFG_CMD83_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD84 register **************/
#define BLE_RF_DIG_CFG_CMD84_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD84_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD84_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD84_CMD0       BLE_RF_DIG_CFG_CMD84_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD84_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD84_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD84_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD84_CMD1       BLE_RF_DIG_CFG_CMD84_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD85 register **************/
#define BLE_RF_DIG_CFG_CMD85_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD85_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD85_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD85_CMD0       BLE_RF_DIG_CFG_CMD85_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD85_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD85_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD85_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD85_CMD1       BLE_RF_DIG_CFG_CMD85_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD86 register **************/
#define BLE_RF_DIG_CFG_CMD86_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD86_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD86_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD86_CMD0       BLE_RF_DIG_CFG_CMD86_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD86_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD86_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD86_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD86_CMD1       BLE_RF_DIG_CFG_CMD86_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD87 register **************/
#define BLE_RF_DIG_CFG_CMD87_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD87_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD87_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD87_CMD0       BLE_RF_DIG_CFG_CMD87_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD87_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD87_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD87_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD87_CMD1       BLE_RF_DIG_CFG_CMD87_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD88 register **************/
#define BLE_RF_DIG_CFG_CMD88_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD88_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD88_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD88_CMD0       BLE_RF_DIG_CFG_CMD88_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD88_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD88_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD88_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD88_CMD1       BLE_RF_DIG_CFG_CMD88_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD89 register **************/
#define BLE_RF_DIG_CFG_CMD89_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD89_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD89_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD89_CMD0       BLE_RF_DIG_CFG_CMD89_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD89_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD89_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD89_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD89_CMD1       BLE_RF_DIG_CFG_CMD89_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD90 register **************/
#define BLE_RF_DIG_CFG_CMD90_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD90_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD90_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD90_CMD0       BLE_RF_DIG_CFG_CMD90_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD90_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD90_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD90_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD90_CMD1       BLE_RF_DIG_CFG_CMD90_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD91 register **************/
#define BLE_RF_DIG_CFG_CMD91_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD91_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD91_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD91_CMD0       BLE_RF_DIG_CFG_CMD91_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD91_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD91_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD91_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD91_CMD1       BLE_RF_DIG_CFG_CMD91_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD92 register **************/
#define BLE_RF_DIG_CFG_CMD92_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD92_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD92_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD92_CMD0       BLE_RF_DIG_CFG_CMD92_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD92_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD92_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD92_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD92_CMD1       BLE_RF_DIG_CFG_CMD92_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD93 register **************/
#define BLE_RF_DIG_CFG_CMD93_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD93_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD93_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD93_CMD0       BLE_RF_DIG_CFG_CMD93_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD93_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD93_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD93_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD93_CMD1       BLE_RF_DIG_CFG_CMD93_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD94 register **************/
#define BLE_RF_DIG_CFG_CMD94_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD94_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD94_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD94_CMD0       BLE_RF_DIG_CFG_CMD94_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD94_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD94_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD94_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD94_CMD1       BLE_RF_DIG_CFG_CMD94_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD95 register **************/
#define BLE_RF_DIG_CFG_CMD95_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD95_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD95_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD95_CMD0       BLE_RF_DIG_CFG_CMD95_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD95_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD95_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD95_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD95_CMD1       BLE_RF_DIG_CFG_CMD95_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD96 register **************/
#define BLE_RF_DIG_CFG_CMD96_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD96_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD96_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD96_CMD0       BLE_RF_DIG_CFG_CMD96_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD96_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD96_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD96_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD96_CMD1       BLE_RF_DIG_CFG_CMD96_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD97 register **************/
#define BLE_RF_DIG_CFG_CMD97_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD97_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD97_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD97_CMD0       BLE_RF_DIG_CFG_CMD97_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD97_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD97_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD97_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD97_CMD1       BLE_RF_DIG_CFG_CMD97_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD98 register **************/
#define BLE_RF_DIG_CFG_CMD98_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD98_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD98_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD98_CMD0       BLE_RF_DIG_CFG_CMD98_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD98_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD98_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD98_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD98_CMD1       BLE_RF_DIG_CFG_CMD98_CMD1_Msk

/************** Bit definition for BLE_RF_DIG_CFG_CMD99 register **************/
#define BLE_RF_DIG_CFG_CMD99_CMD0_Pos   (0U)
#define BLE_RF_DIG_CFG_CMD99_CMD0_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD99_CMD0_Pos)
#define BLE_RF_DIG_CFG_CMD99_CMD0       BLE_RF_DIG_CFG_CMD99_CMD0_Msk
#define BLE_RF_DIG_CFG_CMD99_CMD1_Pos   (16U)
#define BLE_RF_DIG_CFG_CMD99_CMD1_Msk   (0x1FFFUL << BLE_RF_DIG_CFG_CMD99_CMD1_Pos)
#define BLE_RF_DIG_CFG_CMD99_CMD1       BLE_RF_DIG_CFG_CMD99_CMD1_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX0 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_FCAL_CHNL0_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_FCAL_CHNL0_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX0_RX1M_FCAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_FCAL_CHNL0  BLE_RF_DIG_FULCAL_REG_RX0_RX1M_FCAL_CHNL0_Msk
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_ACAL_CHNL0_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_ACAL_CHNL0_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX0_RX1M_ACAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX1M_ACAL_CHNL0  BLE_RF_DIG_FULCAL_REG_RX0_RX1M_ACAL_CHNL0_Msk
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_FCAL_CHNL0_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_FCAL_CHNL0_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX0_RX2M_FCAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_FCAL_CHNL0  BLE_RF_DIG_FULCAL_REG_RX0_RX2M_FCAL_CHNL0_Msk
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_ACAL_CHNL0_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_ACAL_CHNL0_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX0_RX2M_ACAL_CHNL0_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX0_RX2M_ACAL_CHNL0  BLE_RF_DIG_FULCAL_REG_RX0_RX2M_ACAL_CHNL0_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX1 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_FCAL_CHNL1_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_FCAL_CHNL1_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX1_RX1M_FCAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_FCAL_CHNL1  BLE_RF_DIG_FULCAL_REG_RX1_RX1M_FCAL_CHNL1_Msk
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_ACAL_CHNL1_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_ACAL_CHNL1_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX1_RX1M_ACAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX1M_ACAL_CHNL1  BLE_RF_DIG_FULCAL_REG_RX1_RX1M_ACAL_CHNL1_Msk
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_FCAL_CHNL1_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_FCAL_CHNL1_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX1_RX2M_FCAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_FCAL_CHNL1  BLE_RF_DIG_FULCAL_REG_RX1_RX2M_FCAL_CHNL1_Msk
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_ACAL_CHNL1_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_ACAL_CHNL1_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX1_RX2M_ACAL_CHNL1_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX1_RX2M_ACAL_CHNL1  BLE_RF_DIG_FULCAL_REG_RX1_RX2M_ACAL_CHNL1_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX2 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_FCAL_CHNL2_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_FCAL_CHNL2_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX2_RX1M_FCAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_FCAL_CHNL2  BLE_RF_DIG_FULCAL_REG_RX2_RX1M_FCAL_CHNL2_Msk
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_ACAL_CHNL2_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_ACAL_CHNL2_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX2_RX1M_ACAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX1M_ACAL_CHNL2  BLE_RF_DIG_FULCAL_REG_RX2_RX1M_ACAL_CHNL2_Msk
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_FCAL_CHNL2_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_FCAL_CHNL2_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX2_RX2M_FCAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_FCAL_CHNL2  BLE_RF_DIG_FULCAL_REG_RX2_RX2M_FCAL_CHNL2_Msk
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_ACAL_CHNL2_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_ACAL_CHNL2_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX2_RX2M_ACAL_CHNL2_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX2_RX2M_ACAL_CHNL2  BLE_RF_DIG_FULCAL_REG_RX2_RX2M_ACAL_CHNL2_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX3 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_FCAL_CHNL3_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_FCAL_CHNL3_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX3_RX1M_FCAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_FCAL_CHNL3  BLE_RF_DIG_FULCAL_REG_RX3_RX1M_FCAL_CHNL3_Msk
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_ACAL_CHNL3_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_ACAL_CHNL3_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX3_RX1M_ACAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX1M_ACAL_CHNL3  BLE_RF_DIG_FULCAL_REG_RX3_RX1M_ACAL_CHNL3_Msk
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_FCAL_CHNL3_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_FCAL_CHNL3_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX3_RX2M_FCAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_FCAL_CHNL3  BLE_RF_DIG_FULCAL_REG_RX3_RX2M_FCAL_CHNL3_Msk
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_ACAL_CHNL3_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_ACAL_CHNL3_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX3_RX2M_ACAL_CHNL3_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX3_RX2M_ACAL_CHNL3  BLE_RF_DIG_FULCAL_REG_RX3_RX2M_ACAL_CHNL3_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX4 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_FCAL_CHNL4_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_FCAL_CHNL4_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX4_RX1M_FCAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_FCAL_CHNL4  BLE_RF_DIG_FULCAL_REG_RX4_RX1M_FCAL_CHNL4_Msk
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_ACAL_CHNL4_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_ACAL_CHNL4_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX4_RX1M_ACAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX1M_ACAL_CHNL4  BLE_RF_DIG_FULCAL_REG_RX4_RX1M_ACAL_CHNL4_Msk
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_FCAL_CHNL4_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_FCAL_CHNL4_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX4_RX2M_FCAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_FCAL_CHNL4  BLE_RF_DIG_FULCAL_REG_RX4_RX2M_FCAL_CHNL4_Msk
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_ACAL_CHNL4_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_ACAL_CHNL4_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX4_RX2M_ACAL_CHNL4_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX4_RX2M_ACAL_CHNL4  BLE_RF_DIG_FULCAL_REG_RX4_RX2M_ACAL_CHNL4_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX5 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_FCAL_CHNL5_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_FCAL_CHNL5_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX5_RX1M_FCAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_FCAL_CHNL5  BLE_RF_DIG_FULCAL_REG_RX5_RX1M_FCAL_CHNL5_Msk
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_ACAL_CHNL5_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_ACAL_CHNL5_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX5_RX1M_ACAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX1M_ACAL_CHNL5  BLE_RF_DIG_FULCAL_REG_RX5_RX1M_ACAL_CHNL5_Msk
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_FCAL_CHNL5_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_FCAL_CHNL5_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX5_RX2M_FCAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_FCAL_CHNL5  BLE_RF_DIG_FULCAL_REG_RX5_RX2M_FCAL_CHNL5_Msk
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_ACAL_CHNL5_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_ACAL_CHNL5_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX5_RX2M_ACAL_CHNL5_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX5_RX2M_ACAL_CHNL5  BLE_RF_DIG_FULCAL_REG_RX5_RX2M_ACAL_CHNL5_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX6 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_FCAL_CHNL6_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_FCAL_CHNL6_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX6_RX1M_FCAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_FCAL_CHNL6  BLE_RF_DIG_FULCAL_REG_RX6_RX1M_FCAL_CHNL6_Msk
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_ACAL_CHNL6_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_ACAL_CHNL6_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX6_RX1M_ACAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX1M_ACAL_CHNL6  BLE_RF_DIG_FULCAL_REG_RX6_RX1M_ACAL_CHNL6_Msk
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_FCAL_CHNL6_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_FCAL_CHNL6_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX6_RX2M_FCAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_FCAL_CHNL6  BLE_RF_DIG_FULCAL_REG_RX6_RX2M_FCAL_CHNL6_Msk
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_ACAL_CHNL6_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_ACAL_CHNL6_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX6_RX2M_ACAL_CHNL6_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX6_RX2M_ACAL_CHNL6  BLE_RF_DIG_FULCAL_REG_RX6_RX2M_ACAL_CHNL6_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX7 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_FCAL_CHNL7_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_FCAL_CHNL7_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX7_RX1M_FCAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_FCAL_CHNL7  BLE_RF_DIG_FULCAL_REG_RX7_RX1M_FCAL_CHNL7_Msk
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_ACAL_CHNL7_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_ACAL_CHNL7_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX7_RX1M_ACAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX1M_ACAL_CHNL7  BLE_RF_DIG_FULCAL_REG_RX7_RX1M_ACAL_CHNL7_Msk
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_FCAL_CHNL7_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_FCAL_CHNL7_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX7_RX2M_FCAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_FCAL_CHNL7  BLE_RF_DIG_FULCAL_REG_RX7_RX2M_FCAL_CHNL7_Msk
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_ACAL_CHNL7_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_ACAL_CHNL7_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX7_RX2M_ACAL_CHNL7_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX7_RX2M_ACAL_CHNL7  BLE_RF_DIG_FULCAL_REG_RX7_RX2M_ACAL_CHNL7_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX8 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_FCAL_CHNL8_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_FCAL_CHNL8_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX8_RX1M_FCAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_FCAL_CHNL8  BLE_RF_DIG_FULCAL_REG_RX8_RX1M_FCAL_CHNL8_Msk
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_ACAL_CHNL8_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_ACAL_CHNL8_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX8_RX1M_ACAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX1M_ACAL_CHNL8  BLE_RF_DIG_FULCAL_REG_RX8_RX1M_ACAL_CHNL8_Msk
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_FCAL_CHNL8_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_FCAL_CHNL8_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX8_RX2M_FCAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_FCAL_CHNL8  BLE_RF_DIG_FULCAL_REG_RX8_RX2M_FCAL_CHNL8_Msk
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_ACAL_CHNL8_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_ACAL_CHNL8_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX8_RX2M_ACAL_CHNL8_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX8_RX2M_ACAL_CHNL8  BLE_RF_DIG_FULCAL_REG_RX8_RX2M_ACAL_CHNL8_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX9 register ************/
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_FCAL_CHNL9_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_FCAL_CHNL9_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX9_RX1M_FCAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_FCAL_CHNL9  BLE_RF_DIG_FULCAL_REG_RX9_RX1M_FCAL_CHNL9_Msk
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_ACAL_CHNL9_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_ACAL_CHNL9_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX9_RX1M_ACAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX1M_ACAL_CHNL9  BLE_RF_DIG_FULCAL_REG_RX9_RX1M_ACAL_CHNL9_Msk
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_FCAL_CHNL9_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_FCAL_CHNL9_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX9_RX2M_FCAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_FCAL_CHNL9  BLE_RF_DIG_FULCAL_REG_RX9_RX2M_FCAL_CHNL9_Msk
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_ACAL_CHNL9_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_ACAL_CHNL9_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX9_RX2M_ACAL_CHNL9_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX9_RX2M_ACAL_CHNL9  BLE_RF_DIG_FULCAL_REG_RX9_RX2M_ACAL_CHNL9_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX10 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_FCAL_CHNL10_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_FCAL_CHNL10_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX10_RX1M_FCAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_FCAL_CHNL10  BLE_RF_DIG_FULCAL_REG_RX10_RX1M_FCAL_CHNL10_Msk
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_ACAL_CHNL10_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_ACAL_CHNL10_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX10_RX1M_ACAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX1M_ACAL_CHNL10  BLE_RF_DIG_FULCAL_REG_RX10_RX1M_ACAL_CHNL10_Msk
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_FCAL_CHNL10_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_FCAL_CHNL10_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX10_RX2M_FCAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_FCAL_CHNL10  BLE_RF_DIG_FULCAL_REG_RX10_RX2M_FCAL_CHNL10_Msk
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_ACAL_CHNL10_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_ACAL_CHNL10_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX10_RX2M_ACAL_CHNL10_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX10_RX2M_ACAL_CHNL10  BLE_RF_DIG_FULCAL_REG_RX10_RX2M_ACAL_CHNL10_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX11 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_FCAL_CHNL11_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_FCAL_CHNL11_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX11_RX1M_FCAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_FCAL_CHNL11  BLE_RF_DIG_FULCAL_REG_RX11_RX1M_FCAL_CHNL11_Msk
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_ACAL_CHNL11_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_ACAL_CHNL11_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX11_RX1M_ACAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX1M_ACAL_CHNL11  BLE_RF_DIG_FULCAL_REG_RX11_RX1M_ACAL_CHNL11_Msk
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_FCAL_CHNL11_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_FCAL_CHNL11_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX11_RX2M_FCAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_FCAL_CHNL11  BLE_RF_DIG_FULCAL_REG_RX11_RX2M_FCAL_CHNL11_Msk
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_ACAL_CHNL11_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_ACAL_CHNL11_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX11_RX2M_ACAL_CHNL11_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX11_RX2M_ACAL_CHNL11  BLE_RF_DIG_FULCAL_REG_RX11_RX2M_ACAL_CHNL11_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX12 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_FCAL_CHNL12_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_FCAL_CHNL12_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX12_RX1M_FCAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_FCAL_CHNL12  BLE_RF_DIG_FULCAL_REG_RX12_RX1M_FCAL_CHNL12_Msk
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_ACAL_CHNL12_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_ACAL_CHNL12_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX12_RX1M_ACAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX1M_ACAL_CHNL12  BLE_RF_DIG_FULCAL_REG_RX12_RX1M_ACAL_CHNL12_Msk
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_FCAL_CHNL12_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_FCAL_CHNL12_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX12_RX2M_FCAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_FCAL_CHNL12  BLE_RF_DIG_FULCAL_REG_RX12_RX2M_FCAL_CHNL12_Msk
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_ACAL_CHNL12_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_ACAL_CHNL12_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX12_RX2M_ACAL_CHNL12_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX12_RX2M_ACAL_CHNL12  BLE_RF_DIG_FULCAL_REG_RX12_RX2M_ACAL_CHNL12_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX13 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_FCAL_CHNL13_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_FCAL_CHNL13_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX13_RX1M_FCAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_FCAL_CHNL13  BLE_RF_DIG_FULCAL_REG_RX13_RX1M_FCAL_CHNL13_Msk
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_ACAL_CHNL13_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_ACAL_CHNL13_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX13_RX1M_ACAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX1M_ACAL_CHNL13  BLE_RF_DIG_FULCAL_REG_RX13_RX1M_ACAL_CHNL13_Msk
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_FCAL_CHNL13_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_FCAL_CHNL13_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX13_RX2M_FCAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_FCAL_CHNL13  BLE_RF_DIG_FULCAL_REG_RX13_RX2M_FCAL_CHNL13_Msk
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_ACAL_CHNL13_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_ACAL_CHNL13_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX13_RX2M_ACAL_CHNL13_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX13_RX2M_ACAL_CHNL13  BLE_RF_DIG_FULCAL_REG_RX13_RX2M_ACAL_CHNL13_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX14 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_FCAL_CHNL14_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_FCAL_CHNL14_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX14_RX1M_FCAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_FCAL_CHNL14  BLE_RF_DIG_FULCAL_REG_RX14_RX1M_FCAL_CHNL14_Msk
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_ACAL_CHNL14_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_ACAL_CHNL14_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX14_RX1M_ACAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX1M_ACAL_CHNL14  BLE_RF_DIG_FULCAL_REG_RX14_RX1M_ACAL_CHNL14_Msk
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_FCAL_CHNL14_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_FCAL_CHNL14_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX14_RX2M_FCAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_FCAL_CHNL14  BLE_RF_DIG_FULCAL_REG_RX14_RX2M_FCAL_CHNL14_Msk
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_ACAL_CHNL14_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_ACAL_CHNL14_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX14_RX2M_ACAL_CHNL14_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX14_RX2M_ACAL_CHNL14  BLE_RF_DIG_FULCAL_REG_RX14_RX2M_ACAL_CHNL14_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX15 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_FCAL_CHNL15_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_FCAL_CHNL15_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX15_RX1M_FCAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_FCAL_CHNL15  BLE_RF_DIG_FULCAL_REG_RX15_RX1M_FCAL_CHNL15_Msk
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_ACAL_CHNL15_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_ACAL_CHNL15_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX15_RX1M_ACAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX1M_ACAL_CHNL15  BLE_RF_DIG_FULCAL_REG_RX15_RX1M_ACAL_CHNL15_Msk
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_FCAL_CHNL15_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_FCAL_CHNL15_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX15_RX2M_FCAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_FCAL_CHNL15  BLE_RF_DIG_FULCAL_REG_RX15_RX2M_FCAL_CHNL15_Msk
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_ACAL_CHNL15_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_ACAL_CHNL15_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX15_RX2M_ACAL_CHNL15_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX15_RX2M_ACAL_CHNL15  BLE_RF_DIG_FULCAL_REG_RX15_RX2M_ACAL_CHNL15_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX16 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_FCAL_CHNL16_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_FCAL_CHNL16_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX16_RX1M_FCAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_FCAL_CHNL16  BLE_RF_DIG_FULCAL_REG_RX16_RX1M_FCAL_CHNL16_Msk
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_ACAL_CHNL16_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_ACAL_CHNL16_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX16_RX1M_ACAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX1M_ACAL_CHNL16  BLE_RF_DIG_FULCAL_REG_RX16_RX1M_ACAL_CHNL16_Msk
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_FCAL_CHNL16_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_FCAL_CHNL16_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX16_RX2M_FCAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_FCAL_CHNL16  BLE_RF_DIG_FULCAL_REG_RX16_RX2M_FCAL_CHNL16_Msk
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_ACAL_CHNL16_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_ACAL_CHNL16_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX16_RX2M_ACAL_CHNL16_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX16_RX2M_ACAL_CHNL16  BLE_RF_DIG_FULCAL_REG_RX16_RX2M_ACAL_CHNL16_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX17 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_FCAL_CHNL17_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_FCAL_CHNL17_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX17_RX1M_FCAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_FCAL_CHNL17  BLE_RF_DIG_FULCAL_REG_RX17_RX1M_FCAL_CHNL17_Msk
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_ACAL_CHNL17_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_ACAL_CHNL17_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX17_RX1M_ACAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX1M_ACAL_CHNL17  BLE_RF_DIG_FULCAL_REG_RX17_RX1M_ACAL_CHNL17_Msk
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_FCAL_CHNL17_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_FCAL_CHNL17_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX17_RX2M_FCAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_FCAL_CHNL17  BLE_RF_DIG_FULCAL_REG_RX17_RX2M_FCAL_CHNL17_Msk
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_ACAL_CHNL17_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_ACAL_CHNL17_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX17_RX2M_ACAL_CHNL17_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX17_RX2M_ACAL_CHNL17  BLE_RF_DIG_FULCAL_REG_RX17_RX2M_ACAL_CHNL17_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX18 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_FCAL_CHNL18_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_FCAL_CHNL18_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX18_RX1M_FCAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_FCAL_CHNL18  BLE_RF_DIG_FULCAL_REG_RX18_RX1M_FCAL_CHNL18_Msk
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_ACAL_CHNL18_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_ACAL_CHNL18_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX18_RX1M_ACAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX1M_ACAL_CHNL18  BLE_RF_DIG_FULCAL_REG_RX18_RX1M_ACAL_CHNL18_Msk
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_FCAL_CHNL18_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_FCAL_CHNL18_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX18_RX2M_FCAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_FCAL_CHNL18  BLE_RF_DIG_FULCAL_REG_RX18_RX2M_FCAL_CHNL18_Msk
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_ACAL_CHNL18_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_ACAL_CHNL18_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX18_RX2M_ACAL_CHNL18_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX18_RX2M_ACAL_CHNL18  BLE_RF_DIG_FULCAL_REG_RX18_RX2M_ACAL_CHNL18_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX19 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_FCAL_CHNL19_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_FCAL_CHNL19_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX19_RX1M_FCAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_FCAL_CHNL19  BLE_RF_DIG_FULCAL_REG_RX19_RX1M_FCAL_CHNL19_Msk
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_ACAL_CHNL19_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_ACAL_CHNL19_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX19_RX1M_ACAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX1M_ACAL_CHNL19  BLE_RF_DIG_FULCAL_REG_RX19_RX1M_ACAL_CHNL19_Msk
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_FCAL_CHNL19_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_FCAL_CHNL19_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX19_RX2M_FCAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_FCAL_CHNL19  BLE_RF_DIG_FULCAL_REG_RX19_RX2M_FCAL_CHNL19_Msk
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_ACAL_CHNL19_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_ACAL_CHNL19_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX19_RX2M_ACAL_CHNL19_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX19_RX2M_ACAL_CHNL19  BLE_RF_DIG_FULCAL_REG_RX19_RX2M_ACAL_CHNL19_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX20 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_FCAL_CHNL20_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_FCAL_CHNL20_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX20_RX1M_FCAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_FCAL_CHNL20  BLE_RF_DIG_FULCAL_REG_RX20_RX1M_FCAL_CHNL20_Msk
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_ACAL_CHNL20_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_ACAL_CHNL20_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX20_RX1M_ACAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX1M_ACAL_CHNL20  BLE_RF_DIG_FULCAL_REG_RX20_RX1M_ACAL_CHNL20_Msk
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_FCAL_CHNL20_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_FCAL_CHNL20_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX20_RX2M_FCAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_FCAL_CHNL20  BLE_RF_DIG_FULCAL_REG_RX20_RX2M_FCAL_CHNL20_Msk
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_ACAL_CHNL20_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_ACAL_CHNL20_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX20_RX2M_ACAL_CHNL20_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX20_RX2M_ACAL_CHNL20  BLE_RF_DIG_FULCAL_REG_RX20_RX2M_ACAL_CHNL20_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX21 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_FCAL_CHNL21_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_FCAL_CHNL21_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX21_RX1M_FCAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_FCAL_CHNL21  BLE_RF_DIG_FULCAL_REG_RX21_RX1M_FCAL_CHNL21_Msk
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_ACAL_CHNL21_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_ACAL_CHNL21_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX21_RX1M_ACAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX1M_ACAL_CHNL21  BLE_RF_DIG_FULCAL_REG_RX21_RX1M_ACAL_CHNL21_Msk
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_FCAL_CHNL21_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_FCAL_CHNL21_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX21_RX2M_FCAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_FCAL_CHNL21  BLE_RF_DIG_FULCAL_REG_RX21_RX2M_FCAL_CHNL21_Msk
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_ACAL_CHNL21_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_ACAL_CHNL21_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX21_RX2M_ACAL_CHNL21_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX21_RX2M_ACAL_CHNL21  BLE_RF_DIG_FULCAL_REG_RX21_RX2M_ACAL_CHNL21_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX22 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_FCAL_CHNL22_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_FCAL_CHNL22_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX22_RX1M_FCAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_FCAL_CHNL22  BLE_RF_DIG_FULCAL_REG_RX22_RX1M_FCAL_CHNL22_Msk
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_ACAL_CHNL22_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_ACAL_CHNL22_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX22_RX1M_ACAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX1M_ACAL_CHNL22  BLE_RF_DIG_FULCAL_REG_RX22_RX1M_ACAL_CHNL22_Msk
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_FCAL_CHNL22_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_FCAL_CHNL22_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX22_RX2M_FCAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_FCAL_CHNL22  BLE_RF_DIG_FULCAL_REG_RX22_RX2M_FCAL_CHNL22_Msk
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_ACAL_CHNL22_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_ACAL_CHNL22_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX22_RX2M_ACAL_CHNL22_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX22_RX2M_ACAL_CHNL22  BLE_RF_DIG_FULCAL_REG_RX22_RX2M_ACAL_CHNL22_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX23 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_FCAL_CHNL23_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_FCAL_CHNL23_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX23_RX1M_FCAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_FCAL_CHNL23  BLE_RF_DIG_FULCAL_REG_RX23_RX1M_FCAL_CHNL23_Msk
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_ACAL_CHNL23_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_ACAL_CHNL23_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX23_RX1M_ACAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX1M_ACAL_CHNL23  BLE_RF_DIG_FULCAL_REG_RX23_RX1M_ACAL_CHNL23_Msk
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_FCAL_CHNL23_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_FCAL_CHNL23_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX23_RX2M_FCAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_FCAL_CHNL23  BLE_RF_DIG_FULCAL_REG_RX23_RX2M_FCAL_CHNL23_Msk
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_ACAL_CHNL23_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_ACAL_CHNL23_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX23_RX2M_ACAL_CHNL23_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX23_RX2M_ACAL_CHNL23  BLE_RF_DIG_FULCAL_REG_RX23_RX2M_ACAL_CHNL23_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX24 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_FCAL_CHNL24_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_FCAL_CHNL24_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX24_RX1M_FCAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_FCAL_CHNL24  BLE_RF_DIG_FULCAL_REG_RX24_RX1M_FCAL_CHNL24_Msk
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_ACAL_CHNL24_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_ACAL_CHNL24_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX24_RX1M_ACAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX1M_ACAL_CHNL24  BLE_RF_DIG_FULCAL_REG_RX24_RX1M_ACAL_CHNL24_Msk
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_FCAL_CHNL24_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_FCAL_CHNL24_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX24_RX2M_FCAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_FCAL_CHNL24  BLE_RF_DIG_FULCAL_REG_RX24_RX2M_FCAL_CHNL24_Msk
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_ACAL_CHNL24_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_ACAL_CHNL24_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX24_RX2M_ACAL_CHNL24_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX24_RX2M_ACAL_CHNL24  BLE_RF_DIG_FULCAL_REG_RX24_RX2M_ACAL_CHNL24_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX25 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_FCAL_CHNL25_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_FCAL_CHNL25_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX25_RX1M_FCAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_FCAL_CHNL25  BLE_RF_DIG_FULCAL_REG_RX25_RX1M_FCAL_CHNL25_Msk
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_ACAL_CHNL25_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_ACAL_CHNL25_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX25_RX1M_ACAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX1M_ACAL_CHNL25  BLE_RF_DIG_FULCAL_REG_RX25_RX1M_ACAL_CHNL25_Msk
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_FCAL_CHNL25_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_FCAL_CHNL25_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX25_RX2M_FCAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_FCAL_CHNL25  BLE_RF_DIG_FULCAL_REG_RX25_RX2M_FCAL_CHNL25_Msk
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_ACAL_CHNL25_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_ACAL_CHNL25_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX25_RX2M_ACAL_CHNL25_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX25_RX2M_ACAL_CHNL25  BLE_RF_DIG_FULCAL_REG_RX25_RX2M_ACAL_CHNL25_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX26 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_FCAL_CHNL26_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_FCAL_CHNL26_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX26_RX1M_FCAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_FCAL_CHNL26  BLE_RF_DIG_FULCAL_REG_RX26_RX1M_FCAL_CHNL26_Msk
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_ACAL_CHNL26_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_ACAL_CHNL26_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX26_RX1M_ACAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX1M_ACAL_CHNL26  BLE_RF_DIG_FULCAL_REG_RX26_RX1M_ACAL_CHNL26_Msk
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_FCAL_CHNL26_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_FCAL_CHNL26_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX26_RX2M_FCAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_FCAL_CHNL26  BLE_RF_DIG_FULCAL_REG_RX26_RX2M_FCAL_CHNL26_Msk
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_ACAL_CHNL26_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_ACAL_CHNL26_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX26_RX2M_ACAL_CHNL26_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX26_RX2M_ACAL_CHNL26  BLE_RF_DIG_FULCAL_REG_RX26_RX2M_ACAL_CHNL26_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX27 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_FCAL_CHNL27_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_FCAL_CHNL27_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX27_RX1M_FCAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_FCAL_CHNL27  BLE_RF_DIG_FULCAL_REG_RX27_RX1M_FCAL_CHNL27_Msk
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_ACAL_CHNL27_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_ACAL_CHNL27_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX27_RX1M_ACAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX1M_ACAL_CHNL27  BLE_RF_DIG_FULCAL_REG_RX27_RX1M_ACAL_CHNL27_Msk
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_FCAL_CHNL27_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_FCAL_CHNL27_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX27_RX2M_FCAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_FCAL_CHNL27  BLE_RF_DIG_FULCAL_REG_RX27_RX2M_FCAL_CHNL27_Msk
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_ACAL_CHNL27_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_ACAL_CHNL27_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX27_RX2M_ACAL_CHNL27_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX27_RX2M_ACAL_CHNL27  BLE_RF_DIG_FULCAL_REG_RX27_RX2M_ACAL_CHNL27_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX28 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_FCAL_CHNL28_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_FCAL_CHNL28_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX28_RX1M_FCAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_FCAL_CHNL28  BLE_RF_DIG_FULCAL_REG_RX28_RX1M_FCAL_CHNL28_Msk
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_ACAL_CHNL28_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_ACAL_CHNL28_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX28_RX1M_ACAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX1M_ACAL_CHNL28  BLE_RF_DIG_FULCAL_REG_RX28_RX1M_ACAL_CHNL28_Msk
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_FCAL_CHNL28_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_FCAL_CHNL28_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX28_RX2M_FCAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_FCAL_CHNL28  BLE_RF_DIG_FULCAL_REG_RX28_RX2M_FCAL_CHNL28_Msk
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_ACAL_CHNL28_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_ACAL_CHNL28_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX28_RX2M_ACAL_CHNL28_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX28_RX2M_ACAL_CHNL28  BLE_RF_DIG_FULCAL_REG_RX28_RX2M_ACAL_CHNL28_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX29 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_FCAL_CHNL29_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_FCAL_CHNL29_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX29_RX1M_FCAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_FCAL_CHNL29  BLE_RF_DIG_FULCAL_REG_RX29_RX1M_FCAL_CHNL29_Msk
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_ACAL_CHNL29_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_ACAL_CHNL29_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX29_RX1M_ACAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX1M_ACAL_CHNL29  BLE_RF_DIG_FULCAL_REG_RX29_RX1M_ACAL_CHNL29_Msk
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_FCAL_CHNL29_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_FCAL_CHNL29_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX29_RX2M_FCAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_FCAL_CHNL29  BLE_RF_DIG_FULCAL_REG_RX29_RX2M_FCAL_CHNL29_Msk
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_ACAL_CHNL29_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_ACAL_CHNL29_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX29_RX2M_ACAL_CHNL29_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX29_RX2M_ACAL_CHNL29  BLE_RF_DIG_FULCAL_REG_RX29_RX2M_ACAL_CHNL29_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX30 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_FCAL_CHNL30_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_FCAL_CHNL30_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX30_RX1M_FCAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_FCAL_CHNL30  BLE_RF_DIG_FULCAL_REG_RX30_RX1M_FCAL_CHNL30_Msk
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_ACAL_CHNL30_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_ACAL_CHNL30_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX30_RX1M_ACAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX1M_ACAL_CHNL30  BLE_RF_DIG_FULCAL_REG_RX30_RX1M_ACAL_CHNL30_Msk
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_FCAL_CHNL30_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_FCAL_CHNL30_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX30_RX2M_FCAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_FCAL_CHNL30  BLE_RF_DIG_FULCAL_REG_RX30_RX2M_FCAL_CHNL30_Msk
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_ACAL_CHNL30_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_ACAL_CHNL30_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX30_RX2M_ACAL_CHNL30_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX30_RX2M_ACAL_CHNL30  BLE_RF_DIG_FULCAL_REG_RX30_RX2M_ACAL_CHNL30_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX31 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_FCAL_CHNL31_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_FCAL_CHNL31_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX31_RX1M_FCAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_FCAL_CHNL31  BLE_RF_DIG_FULCAL_REG_RX31_RX1M_FCAL_CHNL31_Msk
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_ACAL_CHNL31_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_ACAL_CHNL31_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX31_RX1M_ACAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX1M_ACAL_CHNL31  BLE_RF_DIG_FULCAL_REG_RX31_RX1M_ACAL_CHNL31_Msk
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_FCAL_CHNL31_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_FCAL_CHNL31_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX31_RX2M_FCAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_FCAL_CHNL31  BLE_RF_DIG_FULCAL_REG_RX31_RX2M_FCAL_CHNL31_Msk
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_ACAL_CHNL31_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_ACAL_CHNL31_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX31_RX2M_ACAL_CHNL31_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX31_RX2M_ACAL_CHNL31  BLE_RF_DIG_FULCAL_REG_RX31_RX2M_ACAL_CHNL31_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX32 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_FCAL_CHNL32_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_FCAL_CHNL32_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX32_RX1M_FCAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_FCAL_CHNL32  BLE_RF_DIG_FULCAL_REG_RX32_RX1M_FCAL_CHNL32_Msk
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_ACAL_CHNL32_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_ACAL_CHNL32_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX32_RX1M_ACAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX1M_ACAL_CHNL32  BLE_RF_DIG_FULCAL_REG_RX32_RX1M_ACAL_CHNL32_Msk
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_FCAL_CHNL32_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_FCAL_CHNL32_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX32_RX2M_FCAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_FCAL_CHNL32  BLE_RF_DIG_FULCAL_REG_RX32_RX2M_FCAL_CHNL32_Msk
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_ACAL_CHNL32_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_ACAL_CHNL32_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX32_RX2M_ACAL_CHNL32_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX32_RX2M_ACAL_CHNL32  BLE_RF_DIG_FULCAL_REG_RX32_RX2M_ACAL_CHNL32_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX33 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_FCAL_CHNL33_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_FCAL_CHNL33_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX33_RX1M_FCAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_FCAL_CHNL33  BLE_RF_DIG_FULCAL_REG_RX33_RX1M_FCAL_CHNL33_Msk
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_ACAL_CHNL33_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_ACAL_CHNL33_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX33_RX1M_ACAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX1M_ACAL_CHNL33  BLE_RF_DIG_FULCAL_REG_RX33_RX1M_ACAL_CHNL33_Msk
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_FCAL_CHNL33_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_FCAL_CHNL33_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX33_RX2M_FCAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_FCAL_CHNL33  BLE_RF_DIG_FULCAL_REG_RX33_RX2M_FCAL_CHNL33_Msk
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_ACAL_CHNL33_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_ACAL_CHNL33_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX33_RX2M_ACAL_CHNL33_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX33_RX2M_ACAL_CHNL33  BLE_RF_DIG_FULCAL_REG_RX33_RX2M_ACAL_CHNL33_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX34 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_FCAL_CHNL34_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_FCAL_CHNL34_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX34_RX1M_FCAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_FCAL_CHNL34  BLE_RF_DIG_FULCAL_REG_RX34_RX1M_FCAL_CHNL34_Msk
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_ACAL_CHNL34_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_ACAL_CHNL34_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX34_RX1M_ACAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX1M_ACAL_CHNL34  BLE_RF_DIG_FULCAL_REG_RX34_RX1M_ACAL_CHNL34_Msk
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_FCAL_CHNL34_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_FCAL_CHNL34_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX34_RX2M_FCAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_FCAL_CHNL34  BLE_RF_DIG_FULCAL_REG_RX34_RX2M_FCAL_CHNL34_Msk
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_ACAL_CHNL34_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_ACAL_CHNL34_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX34_RX2M_ACAL_CHNL34_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX34_RX2M_ACAL_CHNL34  BLE_RF_DIG_FULCAL_REG_RX34_RX2M_ACAL_CHNL34_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX35 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_FCAL_CHNL35_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_FCAL_CHNL35_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX35_RX1M_FCAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_FCAL_CHNL35  BLE_RF_DIG_FULCAL_REG_RX35_RX1M_FCAL_CHNL35_Msk
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_ACAL_CHNL35_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_ACAL_CHNL35_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX35_RX1M_ACAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX1M_ACAL_CHNL35  BLE_RF_DIG_FULCAL_REG_RX35_RX1M_ACAL_CHNL35_Msk
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_FCAL_CHNL35_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_FCAL_CHNL35_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX35_RX2M_FCAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_FCAL_CHNL35  BLE_RF_DIG_FULCAL_REG_RX35_RX2M_FCAL_CHNL35_Msk
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_ACAL_CHNL35_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_ACAL_CHNL35_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX35_RX2M_ACAL_CHNL35_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX35_RX2M_ACAL_CHNL35  BLE_RF_DIG_FULCAL_REG_RX35_RX2M_ACAL_CHNL35_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX36 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_FCAL_CHNL36_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_FCAL_CHNL36_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX36_RX1M_FCAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_FCAL_CHNL36  BLE_RF_DIG_FULCAL_REG_RX36_RX1M_FCAL_CHNL36_Msk
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_ACAL_CHNL36_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_ACAL_CHNL36_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX36_RX1M_ACAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX1M_ACAL_CHNL36  BLE_RF_DIG_FULCAL_REG_RX36_RX1M_ACAL_CHNL36_Msk
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_FCAL_CHNL36_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_FCAL_CHNL36_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX36_RX2M_FCAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_FCAL_CHNL36  BLE_RF_DIG_FULCAL_REG_RX36_RX2M_FCAL_CHNL36_Msk
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_ACAL_CHNL36_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_ACAL_CHNL36_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX36_RX2M_ACAL_CHNL36_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX36_RX2M_ACAL_CHNL36  BLE_RF_DIG_FULCAL_REG_RX36_RX2M_ACAL_CHNL36_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX37 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_FCAL_CHNL37_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_FCAL_CHNL37_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX37_RX1M_FCAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_FCAL_CHNL37  BLE_RF_DIG_FULCAL_REG_RX37_RX1M_FCAL_CHNL37_Msk
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_ACAL_CHNL37_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_ACAL_CHNL37_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX37_RX1M_ACAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX1M_ACAL_CHNL37  BLE_RF_DIG_FULCAL_REG_RX37_RX1M_ACAL_CHNL37_Msk
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_FCAL_CHNL37_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_FCAL_CHNL37_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX37_RX2M_FCAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_FCAL_CHNL37  BLE_RF_DIG_FULCAL_REG_RX37_RX2M_FCAL_CHNL37_Msk
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_ACAL_CHNL37_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_ACAL_CHNL37_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX37_RX2M_ACAL_CHNL37_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX37_RX2M_ACAL_CHNL37  BLE_RF_DIG_FULCAL_REG_RX37_RX2M_ACAL_CHNL37_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX38 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_FCAL_CHNL38_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_FCAL_CHNL38_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX38_RX1M_FCAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_FCAL_CHNL38  BLE_RF_DIG_FULCAL_REG_RX38_RX1M_FCAL_CHNL38_Msk
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_ACAL_CHNL38_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_ACAL_CHNL38_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX38_RX1M_ACAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX1M_ACAL_CHNL38  BLE_RF_DIG_FULCAL_REG_RX38_RX1M_ACAL_CHNL38_Msk
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_FCAL_CHNL38_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_FCAL_CHNL38_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX38_RX2M_FCAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_FCAL_CHNL38  BLE_RF_DIG_FULCAL_REG_RX38_RX2M_FCAL_CHNL38_Msk
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_ACAL_CHNL38_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_ACAL_CHNL38_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX38_RX2M_ACAL_CHNL38_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX38_RX2M_ACAL_CHNL38  BLE_RF_DIG_FULCAL_REG_RX38_RX2M_ACAL_CHNL38_Msk

/*********** Bit definition for BLE_RF_DIG_FULCAL_REG_RX39 register ***********/
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_FCAL_CHNL39_Pos  (0U)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_FCAL_CHNL39_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX39_RX1M_FCAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_FCAL_CHNL39  BLE_RF_DIG_FULCAL_REG_RX39_RX1M_FCAL_CHNL39_Msk
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_ACAL_CHNL39_Pos  (8U)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_ACAL_CHNL39_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX39_RX1M_ACAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX1M_ACAL_CHNL39  BLE_RF_DIG_FULCAL_REG_RX39_RX1M_ACAL_CHNL39_Msk
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_FCAL_CHNL39_Pos  (16U)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_FCAL_CHNL39_Msk  (0xFFUL << BLE_RF_DIG_FULCAL_REG_RX39_RX2M_FCAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_FCAL_CHNL39  BLE_RF_DIG_FULCAL_REG_RX39_RX2M_FCAL_CHNL39_Msk
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_ACAL_CHNL39_Pos  (24U)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_ACAL_CHNL39_Msk  (0x7FUL << BLE_RF_DIG_FULCAL_REG_RX39_RX2M_ACAL_CHNL39_Pos)
#define BLE_RF_DIG_FULCAL_REG_RX39_RX2M_ACAL_CHNL39  BLE_RF_DIG_FULCAL_REG_RX39_RX2M_ACAL_CHNL39_Msk

/*************** Bit definition for BLE_RF_DIG_AGC_REG register ***************/
#define BLE_RF_DIG_AGC_REG_LNA_GAIN_FRC_EN_Pos  (0U)
#define BLE_RF_DIG_AGC_REG_LNA_GAIN_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_AGC_REG_LNA_GAIN_FRC_EN_Pos)
#define BLE_RF_DIG_AGC_REG_LNA_GAIN_FRC_EN  BLE_RF_DIG_AGC_REG_LNA_GAIN_FRC_EN_Msk
#define BLE_RF_DIG_AGC_REG_CBPF_GAIN_FRC_EN_Pos  (1U)
#define BLE_RF_DIG_AGC_REG_CBPF_GAIN_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_AGC_REG_CBPF_GAIN_FRC_EN_Pos)
#define BLE_RF_DIG_AGC_REG_CBPF_GAIN_FRC_EN  BLE_RF_DIG_AGC_REG_CBPF_GAIN_FRC_EN_Msk
#define BLE_RF_DIG_AGC_REG_VGA_GAIN_FRC_EN_Pos  (2U)
#define BLE_RF_DIG_AGC_REG_VGA_GAIN_FRC_EN_Msk  (0x1UL << BLE_RF_DIG_AGC_REG_VGA_GAIN_FRC_EN_Pos)
#define BLE_RF_DIG_AGC_REG_VGA_GAIN_FRC_EN  BLE_RF_DIG_AGC_REG_VGA_GAIN_FRC_EN_Msk
#define BLE_RF_DIG_AGC_REG_LNA_GC_Pos   (3U)
#define BLE_RF_DIG_AGC_REG_LNA_GC_Msk   (0xFUL << BLE_RF_DIG_AGC_REG_LNA_GC_Pos)
#define BLE_RF_DIG_AGC_REG_LNA_GC       BLE_RF_DIG_AGC_REG_LNA_GC_Msk
#define BLE_RF_DIG_AGC_REG_CBPF_GC_Pos  (7U)
#define BLE_RF_DIG_AGC_REG_CBPF_GC_Msk  (0x3UL << BLE_RF_DIG_AGC_REG_CBPF_GC_Pos)
#define BLE_RF_DIG_AGC_REG_CBPF_GC      BLE_RF_DIG_AGC_REG_CBPF_GC_Msk
#define BLE_RF_DIG_AGC_REG_VGA_GC_Pos   (9U)
#define BLE_RF_DIG_AGC_REG_VGA_GC_Msk   (0x1FUL << BLE_RF_DIG_AGC_REG_VGA_GC_Pos)
#define BLE_RF_DIG_AGC_REG_VGA_GC       BLE_RF_DIG_AGC_REG_VGA_GC_Msk

#endif
/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/