#include <stdint.h>
#include <string.h>
#include "bf0_hal.h"
#include "mem_map.h"
#include "register.h"
#include "bf0_hal_patch.h"
#ifdef HAL_LCPU_PATCH_MODULE
const unsigned int g_lcpu_patch_list[] =
{
    0x50544348, 0x00000048, 0x00026664, 0x2102F9BD,
    0x00002014, 0xB00EF004, 0x00018E24, 0xBAB6F3ED,
    0x0005135C, 0xB9BAF3B6, 0x000511A4, 0xBA74F3B6,
    0x00014EC0, 0xBF002902, 0x00003BCC, 0x20024010,
    0x0004A738, 0x2C061D84, 0x0002A0C0, 0x44384651,
};

const unsigned int g_lcpu_patch_bin[] =
{
    0xF001B580, 0x4805FCCD, 0x60014905, 0x49064805,
    0x48066001, 0x60014906, 0xBF00BD80, 0x2040E4E0,
    0x004062A5, 0x2040E424, 0x00406059, 0x2040E4E4,
    0x00406C25, 0xF7FBB5E0, 0xB110F688, 0x408EF04F,
    0x2004BD8C, 0x466AA901, 0x20019000, 0xF6E4F7FB,
    0xE7FEB100, 0xBD8C9801, 0xBF042803, 0x47704807,
    0xBF04283C, 0x47704807, 0xBF042817, 0x47704804,
    0xBF14280B, 0x48042000, 0xBF004770, 0x00407CA0,
    0x00407CCC, 0x00407CC0, 0x00407CAC, 0x43F8E92D,
    0x8094F8DF, 0x290A4605, 0xF04F460C, 0xF04F0201,
    0xF8580600, 0xF4200020, 0xBFC86000, 0x6000F500,
    0x0025F848, 0x21004608, 0xFA14F001, 0x1025F858,
    0x4A1B4F1A, 0xF3602C0B, 0x7878210A, 0x1025F848,
    0xB118DB04, 0xF0416811, 0x6011010C, 0xBFC82C00,
    0x46912601, 0xBF082800, 0x2C042602, 0x2602BFC8,
    0xB9307838, 0x42B078F8, 0x4630D003, 0xF00170FE,
    0x4620F99F, 0x22002101, 0xF9ECF001, 0x0025F808,
    0xB1387878, 0xDC052C0A, 0x0000F8D9, 0x000CF020,
    0x0000F8C9, 0x83F8E8BD, 0x2040EDF0, 0x20407CFC,
    0x400840EC, 0x460DB5BC, 0x518AF64F, 0xD017428B,
    0x5186F64F, 0xD063428B, 0x5185F64F, 0xD11C428B,
    0x5088F44F, 0xF64F2100, 0x23015285, 0xFD74F411,
    0x29037829, 0x682AD817, 0xF8434B2E, 0xE01D2021,
    0x5088F44F, 0xF64F2100, 0x2301528A, 0xFD64F411,
    0x78284604, 0x2012B1A8, 0x4629E03E, 0xE8BDB002,
    0xF40540B0, 0x4922B8FB, 0xF442680A, 0x600A4200,
    0x2400F8D1, 0x4200F442, 0x2400F8C1, 0x70012100,
    0x7868E02C, 0xD0152802, 0xD01A2801, 0xD1E22800,
    0x4D142006, 0x0107F10D, 0x0007F88D, 0x462A4812,
    0x20016803, 0xB1884798, 0x88814810, 0x80A96800,
    0xE00B6028, 0x490B88E8, 0xF8D58088, 0x60080002,
    0x48F9E004, 0xF0817801, 0x70010155, 0xF10048F6,
    0xF4240119, 0x2000F839, 0x46207020, 0xF944F40E,
    0xBDBC2000, 0x2040F480, 0x2040E99C, 0x0005C3E9,
    0x400904D0, 0x20407D03, 0x1205F241, 0x42904902,
    0x2100BF18, 0x47704608, 0x00406135, 0x460AB580,
    0x5088F44F, 0x230A2100, 0xFCFEF411, 0x7101210C,
    0xF8C04904, 0x49041006, 0xF40E6001, 0x2000F91D,
    0xBF00BD80, 0x02200A4C, 0x02000C00, 0x4607B5F8,
    0x461C4610, 0x460D4616, 0xFDA8F411, 0xD1082805,
    0x88A97868, 0x000FF000, 0xB2C03801, 0xF63AF7FF,
    0x4638E005, 0x46324629, 0xF40E4623, 0x2000FDBF,
    0x0000BDF8, 0xB2C9B580, 0x20004602, 0xD8122908,
    0xF001E8DF, 0x11110A05, 0x1111110E, 0x46100012,
    0x4080E8BD, 0xBD04F000, 0xF0004610, 0xBD80F8FD,
    0xF0004610, 0xBD80FB21, 0xF7FF4610, 0xBD80FFA5,
    0x4607B5F8, 0x46154808, 0x461C460E, 0x68024629,
    0x601FF240, 0x46384790, 0x462A4631, 0xF4124623,
    0x2000F91D, 0xBF00BDF8, 0x2040E454, 0x880CB510,
    0xE8BDB11C, 0xF4124010, 0x2000B9CD, 0x0000BD10,
    0x4604B5F8, 0x88474818, 0x00FFF027, 0x7F80F5B0,
    0x4816D123, 0x7FC06800, 0xD1202804, 0x20007821,
    0xF5B2F7FF, 0x4606B1E8, 0x78224811, 0x44306861,
    0xFED5F44C, 0x0001F248, 0x23062200, 0xEA802500,
    0xB2812007, 0x1007F241, 0xFC6EF411, 0x80867821,
    0x70818007, 0xFCA6F411, 0x2502E004, 0x2500E002,
    0x2501E000, 0xBDF84628, 0x2040EAE4, 0x2040E880,
    0x20408000, 0x43F0E92D, 0x4604B085, 0x5D00483C,
    0x28043816, 0x4847D370, 0x2101460D, 0x6024F850,
    0x284F2000, 0xEB06D014, 0xF00002D0, 0xF8920306,
    0x40DA223C, 0x0203F002, 0xD1072A03, 0xF00008C2,
    0x5CAB0707, 0xF707FA01, 0x54AB43BB, 0xE7E83001,
    0xF4434628, 0xF506F88B, 0x4629780A, 0x4640220A,
    0xFE78F44C, 0xD0472800, 0x003CF896, 0xD1132802,
    0xF10D2000, 0xF10D0112, 0xF8AD0211, 0xF88D0012,
    0x46200011, 0xF83CF418, 0x0011F89D, 0x1012F8BD,
    0xFB003006, 0xE005F701, 0xF41D4620, 0xEB00FD8F,
    0x00470040, 0xFE7EF421, 0xA8014681, 0xF4194621,
    0x2F64FF23, 0x2764BF98, 0x0047EB09, 0x22019901,
    0xF04F462B, 0x44080901, 0x005AF3C0, 0xF0203001,
    0x46200701, 0xF4194639, 0xB968FBB1, 0x46294640,
    0xF44C220A, 0xF896FE44, 0x46203036, 0x22014639,
    0xFC90F417, 0x924CF886, 0xE8BDB005, 0xBF0083F0,
    0x2040F478, 0x4D0BB5B0, 0xF8550A14, 0xB11C4024,
    0x40B0E8BD, 0xBD18F413, 0xBDB02000, 0x4D05B5B0,
    0xF8550A14, 0xB11C4024, 0x40B0E8BD, 0xBD36F413,
    0xBDB02000, 0x2040F408, 0x6101F240, 0xBF044288,
    0x4770480D, 0x6FC4F5B0, 0x480EBF04, 0xF2414770,
    0x42881107, 0x4807BF04, 0xF2404770, 0x42886125,
    0x4806BF04, 0xF2404770, 0x42886122, 0x2000BF14,
    0x47704803, 0x0040626D, 0x00406495, 0x004062E1,
    0x0040630D, 0x004064AD, 0x41F0E92D, 0x48194604,
    0x2A017A42, 0x7A80D109, 0xBF184288, 0xD1042800,
    0x70414815, 0xF44A7004, 0x4844F8EB, 0x0024F850,
    0x2100B110, 0xFC88F452, 0xF0004620, 0x2600FE69,
    0xF144F7FA, 0x46804F73, 0x8D382510, 0x1A088D79,
    0xFB90B2F1, 0x4288F0F5, 0x4620DD07, 0xFF00F421,
    0xF421B118, 0x3601FF2F, 0x4640E7EF, 0x41F0E8BD,
    0xB130F7FA, 0x20407D03, 0x20407D13, 0xB1FAB5B0,
    0x78094910, 0xF421B1E1, 0xB1C8FEEB, 0x490F4862,
    0x24004D0F, 0x0021F890, 0x1000EA41, 0x8D09495D,
    0xF04F5A40, 0xEB01417A, 0xF1B06000, 0xBFC84F3E,
    0x78282401, 0xD00342A0, 0xF0004620, 0x702CFDCB,
    0xBF00BDB0, 0x20407CFD, 0x2040F480, 0x20408008,
    0x20407D15, 0x461CB5F8, 0x460E4615, 0xF7FA4607,
    0x4916F0F5, 0x1027F851, 0xF891B1A1, 0x270C2099,
    0xD1102A01, 0x1097F891, 0x0306EA45, 0x43239A06,
    0xF04F2900, 0xBF08010C, 0x31FFF04F, 0xBF08431A,
    0xE000460F, 0xF7FA270C, 0xB2F8F0DD, 0xBF00BDF8,
    0xF000B580, 0x2000FDF5, 0x0000BD80, 0xF8514903,
    0x28000020, 0x2001BF18, 0xBF004770, 0x2040E8A0,
    0x4DF0B570, 0x26102400, 0x8D698D28, 0xB2E11A08,
    0xF0F6FB90, 0xDD074288, 0xF4212002, 0xB118FE81,
    0xFEB0F421, 0xE7EF3401, 0xBF00BD70, 0x43F8E92D,
    0x68054827, 0xD0482D00, 0xF7FA2700, 0x4EE1F0A7,
    0x24104680, 0x8D718D30, 0xB2F91A08, 0xF0F4FB90,
    0xDD084288, 0x0047F895, 0xFE62F421, 0xF421B118,
    0x3701FE91, 0x481CE7EE, 0xF04F491C, 0x46890E10,
    0x0C02F101, 0x0021F890, 0x8D344607, 0x1B098D71,
    0xF3FEFB91, 0x181AB2F9, 0xDD19428A, 0xF2F3FBB1,
    0x1113FB02, 0xEA49464A, 0x5F1A1301, 0x3FFFF1B2,
    0x0109DC0C, 0x010CEA41, 0xF8955B09, 0xEBB22047,
    0xD1032FD1, 0xF36F5AE1, 0x52E131DF, 0xE7DC3701,
    0xE8BD4640, 0xF7FA43F8, 0xE8BDB065, 0xBF0083F8,
    0x2040F4AC, 0x2040EE0C, 0x2040F480, 0x20408000,
    0xF851491F, 0xB1100020, 0xF4522100, 0x4770BB8D,
    0x1107F3C1, 0xF240481A, 0xF85012FD, 0x01490021,
    0x0052F890, 0x0040EB02, 0xF3C04A08, 0x281F0047,
    0x201FBF28, 0xF3C35853, 0x42832304, 0x588BD005,
    0x53F8F423, 0x2000EA43, 0x47705088, 0x40090610,
    0x460CB5F8, 0xF02AF7FA, 0x46054E09, 0x0024F856,
    0x2100B148, 0xF4522700, 0xF856FB5F, 0xF4100024,
    0xF846FFF5, 0x46287024, 0xF01CF7FA, 0xBDF82000,
    0x2040E898, 0x4614B570, 0x4606460D, 0xF93CF000,
    0x202AB108, 0x4630E004, 0x46224629, 0xFF8AF423,
    0xBD70B2C0, 0x43F8E92D, 0x88884681, 0xF640460D,
    0x2620437A, 0x42991F81, 0x88E9D879, 0x2B7C088B,
    0x892BD875, 0x4476F640, 0x070AF1A3, 0xD86E42A7,
    0x0080EB00, 0x08403101, 0xEB034348, 0x00490183,
    0xD2014281, 0xE0622620, 0xF85048B1, 0xF8B00029,
    0x07C00092, 0x8968D15A, 0x71FFF647, 0xB2801A10,
    0xD2014288, 0xE0522628, 0x21004648, 0xFF8CF428,
    0xD1052805, 0x21014648, 0xFF86F428, 0xD1492805,
    0x21014648, 0xFF80F428, 0x2709B108, 0xF44FE018,
    0x21047086, 0x23302204, 0xF9EEF411, 0x21054A29,
    0xF4284606, 0x2000FF7B, 0x46322101, 0x002DF8A6,
    0xF64F6130, 0x82F070FF, 0xF4284648, 0x2707FF75,
    0x21014648, 0x0801F04F, 0xFF5EF428, 0xD11D2805,
    0x21014648, 0xFF4EF428, 0x463A4649, 0xF4284606,
    0x88A8FF7D, 0x802CF886, 0x22004639, 0xF8D58530,
    0x60F00006, 0x84B08968, 0x84708868, 0xF8867868,
    0x46480026, 0xF4292600, 0xE000F93F, 0x46302624,
    0x83F8E8BD, 0x21004648, 0xF4282600, 0x88A9FF2B,
    0x85012200, 0x1006F8D5, 0x896960C1, 0x88698481,
    0x78698441, 0x1026F880, 0x21034648, 0xFAE4F427,
    0xBF00E7E5, 0x0002FDB1, 0xF5B04903, 0xBF187F05,
    0x46082100, 0xBF004770, 0x00406A95, 0x7B4B4907,
    0xD1092B01, 0x42917B89, 0x2900BF18, 0x4904D104,
    0x7008704A, 0xBEE4F449, 0xBF004770, 0x20407D03,
    0x20407D13, 0x460FB5F8, 0xF7F94605, 0x4604F73F,
    0xF85048AF, 0xB1D66025, 0x0086F8B6, 0x0010F020,
    0x1004F367, 0x0086F8A6, 0xF4334628, 0xB167FCAB,
    0xF4354628, 0xF896FE93, 0xB130009A, 0x46314628,
    0x27002200, 0xF900F000, 0x2700E002, 0x270CE000,
    0xF7F94620, 0x4638F71F, 0xBF00BDF8, 0x460FB5F8,
    0xF7F94605, 0x4604F713, 0xF8504899, 0xB1A66025,
    0x002CF106, 0xF7FF4639, 0xF8B6F58E, 0xF0400086,
    0xF8A60008, 0x46280086, 0xFE68F435, 0x46314628,
    0xF0002201, 0x2500F8D9, 0x250CE000, 0xF7F94620,
    0x4628F6F9, 0xBF00BDF8, 0x4906B158, 0x0096F890,
    0x88492294, 0x1002FB00, 0x5A424903, 0x0220F042,
    0x47705242, 0x2040EE0C, 0x20408002, 0xF8514980,
    0xB1300020, 0x0097F890, 0xFAB03801, 0x0940F080,
    0x20004770, 0xBF004770, 0xF8514979, 0xB1280020,
    0x004AF890, 0xBF182800, 0x47702001, 0x47702000,
    0x460FB5F8, 0xF7F94605, 0x4604F6C1, 0xF8504870,
    0xB1866025, 0x0086F8B6, 0xF04062B7, 0xF8A60008,
    0x46280086, 0xFE1AF435, 0x46314628, 0xF0002201,
    0x2500F88B, 0x250CE000, 0xF7F94620, 0x4628F6AB,
    0xBF00BDF8, 0x41F0E92D, 0x46070A14, 0x46154698,
    0x4620460E, 0xFD5EF441, 0xF851490D, 0xB1591024,
    0x88328900, 0xD30A4282, 0xD8052C09, 0x0094F891,
    0x0003F000, 0xD1022803, 0xE8BD2000, 0x463881F0,
    0x462A4631, 0xE8BD4643, 0xF43341F0, 0xBF00BFF1,
    0x2040F4B8, 0x47F0E92D, 0x4614461E, 0x46054689,
    0xF674F7F9, 0xA128F8DF, 0xF85A4680, 0xB3C77025,
    0x004AF897, 0x980AB9F8, 0x2108E9DD, 0x0048F8A7,
    0xF8A72001, 0x00B22042, 0x004AF887, 0xF8A700A0,
    0xEA4F0040, 0xF8A70089, 0x01480074, 0x200EE9C7,
    0xEBB06EB8, 0xD9060F86, 0x109CF897, 0xBF242902,
    0xF4354628, 0xF897FC7F, 0x26000097, 0x6044F887,
    0xF43AB980, 0x4604F911, 0x0025F85A, 0x26002100,
    0xF97AF452, 0x0096F897, 0x22014621, 0xFEBCF434,
    0x260CE000, 0xF7F94640, 0x4630F635, 0x87F0E8BD,
    0xF8514927, 0xB1280020, 0x0097F890, 0xBF183802,
    0x47702001, 0x47702000, 0x41F0E92D, 0xF8914605,
    0xBBD80097, 0x0086F8B1, 0x07C0460E, 0xF896D036,
    0xB39800B6, 0x4614481A, 0x8025F850, 0xF8DCF43A,
    0x104AF896, 0x6CF04607, 0xD1022901, 0x1044F896,
    0x1BC1BB21, 0x4270F021, 0x428A6EB1, 0xFBB2D91E,
    0xF8B6F2F1, 0xF64F307E, 0xFA1F7CFF, 0x4573FE82,
    0x4462BF08, 0xB2921A9B, 0x307EF8A6, 0x0011FB02,
    0xF0202100, 0x64F04070, 0xF4524640, 0x4628F925,
    0x46224639, 0x41F0E8BD, 0xBE66F434, 0x81F0E8BD,
    0x2040F514, 0xBF042801, 0x47704803, 0x4803B908,
    0x20004770, 0xBF004770, 0x004067B5, 0x004067D5,
    0x7F814806, 0xFA91B141, 0x2301F2A1, 0xF282FAB2,
    0xF202FA03, 0x77814391, 0x47702000, 0x2040E78C,
    0xF241B510, 0x42830001, 0x4608D106, 0x0101F241,
    0x4010E8BD, 0xBAE2F7FF, 0x20004A0A, 0xD00E285D,
    0x4030F832, 0xD001429C, 0xE7F73001, 0x00C0EB02,
    0xB1226842, 0x46194608, 0x4010E8BD, 0x20004710,
    0xBF00BD10, 0x0005E53C, 0x4C08B5B0, 0x5054F894,
    0x7CE4B135, 0xD1032C02, 0xB10C7B4C, 0xB11C7B8C,
    0x40B0E8BD, 0xBD2EF443, 0xBDB02000, 0x2040F750,
    0x1105F241, 0xBF044288, 0x47704804, 0x5101F240,
    0xBF0C4288, 0x20004802, 0xBF004770, 0x00406C61,
    0x00406CA9, 0x4605B5B0, 0x460C7840, 0xD10E2818,
    0x0A204958, 0x0020F851, 0x0036F890, 0xD1062801,
    0xF4114620, 0x2801F85B, 0x2000BF04, 0x4628BDB0,
    0xE8BD4621, 0xF44440B0, 0xBF00BD09, 0x4606B5F8,
    0x0A0D484C, 0xF850460C, 0x46087025, 0xF846F411,
    0xD00D281F, 0xD10E2825, 0x0036F897, 0xF897B958,
    0x46283039, 0x2223210B, 0xFFA2F416, 0xBDF82000,
    0xF8872000, 0x4630017A, 0xE8BD4621, 0xF44540F8,
    0xBF00B8DD, 0x4605B5F8, 0x0A0C483A, 0x7024F850,
    0xF4114608, 0x2819F823, 0x2101D807, 0xF000FA01,
    0x4208491B, 0x2000D001, 0x7828BDF8, 0x0301F000,
    0x0036F897, 0x3039F887, 0xD11F2801, 0x0609F105,
    0xF7FF4630, 0x2814F3B3, 0x7A28D31A, 0xD2172802,
    0x700AF507, 0x220A4631, 0xF999F44C, 0x68697A2A,
    0x46334620, 0xBF182A00, 0xF4182201, 0x7A28FF45,
    0xBF182800, 0xF8872001, 0xE7D4024C, 0xE0022224,
    0x3039F897, 0x4620221E, 0xF416213C, 0xE7CAFF51,
    0x03C00001, 0x4605B5F8, 0x0A0E4816, 0xF850460C,
    0x46087026, 0xFFDAF410, 0x0116F1A0, 0xD3132904,
    0x282EB190, 0x4810D10B, 0xFB062134, 0x7A400001,
    0xF897B128, 0xB9100036, 0x07C07828, 0x7828D10A,
    0x0001F000, 0x0039F887, 0x46214628, 0x40F8E8BD,
    0xB8C2F448, 0x21174630, 0x23012223, 0xFF20F416,
    0xBDF82000, 0x2040F408, 0x2040F410, 0x4614B5B0,
    0x25004602, 0x2A292000, 0x2908BF08, 0x4618D102,
    0xFA4EF7FF, 0xBDB07025, 0x4614B510, 0xD0202807,
    0xD00B2814, 0xD0102819, 0xD0142820, 0xBF082831,
    0xD11E2902, 0xF0004618, 0xE01AF9E3, 0x4F00F1B1,
    0xB2D8D117, 0xFC3CF7FF, 0x2904E011, 0xB2D8D111,
    0xFBDCF7FF, 0x2908E00B, 0x4618D10B, 0xFC42F000,
    0xF5B1E007, 0xD1042F00, 0xF7FF4618, 0x2001FD8D,
    0x2000E000, 0xBD107020, 0x460CB510, 0x20004601,
    0x70102907, 0x9902D114, 0x5F80F5B4, 0xF5B4D00A,
    0xD00E4F80, 0x6F00F1B4, 0x2000BF12, 0xF7FFB2D8,
    0xBD10FD9F, 0x2900B2D8, 0x2101BF18, 0xFD1AF7FF,
    0xB2D8BD10, 0xFD42F7FF, 0xB510BD10, 0x46142812,
    0x0200F04F, 0xF5B1BF08, 0xD1050F80, 0xB2D89902,
    0xF7FFB2C9, 0x2201FAE9, 0xBD107022, 0x4614B510,
    0x28142200, 0xBF087022, 0x3F80F5B1, 0xE9DDD106,
    0xB2D81202, 0xF7FFB2C9, 0x2201FB63, 0x70222000,
    0xB5B0BD10, 0x460A4614, 0x28072100, 0xE9DD7021,
    0xD00F1504, 0xD0062808, 0xD0132813, 0xBF082815,
    0xD01A2A04, 0xF5B2BDB0, 0xD1FB6F80, 0xF7FFB2D8,
    0xE010FDEF, 0xD1F52A40, 0xB2C9B2D8, 0xF7FFB2EA,
    0xE013FCBD, 0x0F80F5B2, 0xF5B2D00C, 0xD1E93F80,
    0xF7FFB2C8, 0xB948FB3B, 0xB2EAE7E4, 0xF7FF4618,
    0xE003FBB7, 0x4618B2EA, 0xFAD8F7FF, 0x70202001,
    0xB5B0BDB0, 0x22004614, 0x70222815, 0xF5B1BF08,
    0xD0017F00, 0xBDB02000, 0xB2D8AD04, 0x462BCD26,
    0xF7FFB2C9, 0x2101FBBD, 0xBDB07021, 0x70102000,
    0x20004770, 0x20007010, 0x00004770, 0x4614B570,
    0xD00C2817, 0xD00F2821, 0xD0122827, 0xD01B2828,
    0xBF082831, 0xD1362920, 0xF9BEF000, 0x2901E033,
    0xF7FFD131, 0xE03CFB05, 0xD12C2908, 0xFBB6F000,
    0xF5B1E029, 0xD1267F80, 0x6801481B, 0x01CCF8D0,
    0xBF184308, 0xE01F2001, 0x2F00F5B1, 0x2940D01E,
    0xF5B1D01F, 0xD0222F80, 0xD1142908, 0x4E154D14,
    0x70282001, 0x00A0F8D6, 0x0008F020, 0x00A0F8C6,
    0xF7BEF7FD, 0x10A0F8D6, 0x70282000, 0x0108F041,
    0x10A0F8C6, 0x2000E000, 0xBD707020, 0xFC4CF000,
    0x4806E007, 0xFAB06800, 0x0940F080, 0xF000E7F4,
    0x2001FC21, 0xBF00E7F0, 0x2040F8B0, 0x4004004C,
    0x20407CFC, 0x2040EC28, 0xB086B570, 0x46024614,
    0x2A142000, 0xD01B7020, 0xD1362A07, 0x4F80F1B1,
    0xA80AD132, 0x3034F8BD, 0x2030F8BD, 0x4038F8BD,
    0x503CF8BD, 0x6040F8BD, 0x011CF100, 0xF8109105,
    0xF89D1F04, 0xE88D0028, 0xF7FF0070, 0xE01CFCE3,
    0x5F00F5B1, 0xA80AD118, 0x3034F8BD, 0x2030F8BD,
    0x5038F8BD, 0x603CF89D, 0x0118F100, 0xF8309104,
    0xF89D1F04, 0xE9CD0028, 0xF7FF5600, 0x28FFFA4B,
    0x2101BF1C, 0xE0007021, 0xB0062000, 0x2000BD70,
    0x47707010, 0x2800490B, 0xF0226A0A, 0xBF080301,
    0x0301F042, 0x2800620B, 0xF002680A, 0x600A4200,
    0xBF044A05, 0x1220F244, 0x5200F2C0, 0x43106808,
    0x47706008, 0x40084070, 0x01000860, 0x21004A7B,
    0xBFC82800, 0x78522101, 0xBF082A00, 0x28042102,
    0x2102BFC8, 0x47704608, 0x21004A23, 0xD0042907,
    0x42835653, 0x3101DA01, 0x4870E7F8, 0x47707181,
    0x43F8E92D, 0x8A38F8DF, 0xF8882600, 0x491A1005,
    0x0004F888, 0x2E072000, 0x578BD006, 0xDA034293,
    0x5080F500, 0xE7F63601, 0x6703EA40, 0x9A08F8DF,
    0x2500B25C, 0xD0072D07, 0x4621B2E8, 0x7025F849,
    0xFF3CF7FE, 0xE7F53501, 0x6002F888, 0x83F8E8BD,
    0x495AB5B0, 0x4D0A4B09, 0x4020F853, 0x4906788A,
    0x5689402C, 0x340EF362, 0x6201EA44, 0x2020F843,
    0x40B0E8BD, 0xBF22F7FE, 0x00407C3C, 0x2040EDF0,
    0x00FF8FFF, 0xA1054804, 0xF3C06800, 0x28030041,
    0x2002BF08, 0x47705C08, 0x40082834, 0x00020001,
    0x4604B5FE, 0xA9022004, 0x0206F10D, 0x0006F8AD,
    0xF7FA2006, 0x9D02F5C9, 0x0E284FE6, 0x78387078,
    0xF997B120, 0xF0000003, 0xE027F8D3, 0x0C2E21FF,
    0x70F90A28, 0xEBB10631, 0xDD056F05, 0xB270B241,
    0xF7FF4602, 0xE005FF8D, 0xB268B241, 0xF7FF4602,
    0x462EFF87, 0xF7FFB270, 0x4831FF77, 0xF36F6881,
    0x6081419A, 0xF0416881, 0x6081712A, 0xF36F68C1,
    0x60C1419A, 0xF04168C1, 0x60C1712E, 0x2000B26E,
    0x25002111, 0x003AF884, 0x1038F884, 0xBFC82E0A,
    0x2E0B2501, 0x4630DB0B, 0x462A2100, 0xF8EAF000,
    0xB3117879, 0x680A491E, 0x020CF042, 0x2E00E01C,
    0x2001BFC8, 0x7839787A, 0xBF082A00, 0x2E042002,
    0x2002BFC8, 0x78F9B929, 0xD0024281, 0xF00070F8,
    0x4630F87F, 0x462A2101, 0xF8CCF000, 0xB1217879,
    0x680A490F, 0x020CF022, 0xF884600A, 0x4D11503B,
    0x0039F884, 0xC02CF8DF, 0x4A0C490B, 0x4E0F4B0C,
    0x48104F0F, 0x4D0C62A5, 0x0702E9C4, 0x6204E9C4,
    0x3106E9C4, 0x5C08E9C4, 0xBF00BDFE, 0x20407CFC,
    0x400840EC, 0x00407B6D, 0x00407C15, 0x00407575,
    0x004074E5, 0x0040743D, 0x00407AE9, 0x00407BE1,
    0x00407629, 0x004075D1, 0x49144813, 0x4B1A4A14,
    0x4814500A, 0x60024A14, 0x60424A14, 0x60824A14,
    0x7255F240, 0x4A1360C2, 0x4B146013, 0x4B146053,
    0x4B146093, 0x230D60D3, 0xF363680A, 0x23500205,
    0x4911600A, 0xF3635842, 0x50424217, 0xF3635842,
    0x50420207, 0xBF004770, 0xFFFFFC70, 0x40090400,
    0x20201000, 0x40090880, 0x003C0537, 0x00500350,
    0x00500755, 0x40090890, 0x00090907, 0x00070503,
    0x1CA80903, 0x17280003, 0xFFFFFC0C, 0x4770B240,
    0x2802491F, 0xF022680A, 0x600A0206, 0xF022680A,
    0x600A0201, 0xF422684A, 0x604A4278, 0xF422684A,
    0x604A6280, 0x2801D010, 0xBB10D014, 0xF0406808,
    0x60080002, 0xF0406808, 0x60080001, 0xF4406848,
    0x60486000, 0xE0126848, 0xF0406808, 0x60080004,
    0xE0046808, 0x60086808, 0xF0406808, 0x60080001,
    0xF4406848, 0x60484078, 0xF4406848, 0x60486080,
    0xA0044770, 0xF44FA104, 0xF4497201, 0xBF00BF4F,
    0x40082834, 0x00000030, 0x745F6672, 0x6F705F78,
    0x5F726577, 0x73616C63, 0x6F635F73, 0x6769666E,
    0x00000000, 0x460CB570, 0x2A014605, 0x4936D108,
    0x28082000, 0x5C0AD016, 0xDA1442AA, 0xE7F83001,
    0xFEA8F7FF, 0x1000EBC0, 0xEB014930, 0x21000040,
    0xD0162910, 0x2011F910, 0xD0142A7F, 0xDA1342AA,
    0xE7F53101, 0x1B512200, 0xF284FAB4, 0x2101BF18,
    0x40110952, 0x2A00B2C2, 0x2201BF18, 0x1A404011,
    0x2110E016, 0x3901E000, 0x2600B2CA, 0x3012F910,
    0xFAB442AB, 0xBFC8F384, 0x095B2601, 0xBF182A00,
    0x40332201, 0x1A89401A, 0xEB00B2C9, 0x78400041,
    0xBD70B2C0, 0x4615B5B0, 0xF7FF4604, 0xB13DFE6B,
    0x2C071C60, 0x3801D815, 0x480FD1FB, 0xE0115D00,
    0x1200EBC0, 0xEB01490D, 0x22000342, 0xD0082A10,
    0x0542EB03, 0x2DFF786D, 0x42A5D003, 0x3201D004,
    0x2000E7F4, 0xBDB0B240, 0x1000EBC0, 0x0040EB01,
    0x0012F810, 0xBF00E7F6, 0x00407CB8, 0x00407C43,
    0x492BB570, 0xF8514D2B, 0xF3C22020, 0xF9953302,
    0xEBB44005, 0xDA1A6F22, 0x4C28B1CB, 0x40221E5E,
    0xB2F34C25, 0x320EF366, 0xEA4256E4, 0xF8416204,
    0x46212020, 0xFD42F7FE, 0x0005F995, 0x42842100,
    0xF086FAB6, 0x2101BFD8, 0x43080940, 0x2001BD70,
    0xBF00BD70, 0x20407CFC, 0x4915B5F8, 0xF8514E15,
    0xF3C22020, 0xF9963502, 0xEBB33004, 0xDD1C6F22,
    0xD01A2D07, 0x1C6B4C10, 0x403A4F10, 0xF36356E4,
    0xEA42320E, 0xF8416204, 0x46212020, 0xFD16F7FE,
    0x0004F996, 0x42842100, 0x0000F04F, 0x2001BFA8,
    0xBF882D05, 0x43082101, 0x2001BDF8, 0xBF00BDF8,
    0x2040EDF0, 0x20407CFC, 0x00407C3C, 0x00FF8FFF,
    0xB086B570, 0x480D4603, 0x0C04F10D, 0xE8902922,
    0x69000074, 0x0074E8AC, 0x90054C09, 0x2122BF28,
    0xB2C20048, 0x46204619, 0xFD21F44B, 0x0030F104,
    0x2214A901, 0xFD1BF44B, 0xBD70B006, 0x00407C28,
    0x2040EE0C, 0x4F19B5F8, 0x460D4606, 0x46142000,
    0x1201E9C7, 0x0003F88D, 0x4628A115, 0xF7F92203,
    0xB158F1DB, 0x4628A113, 0xF7F92203, 0xB128F1D5,
    0x4628A111, 0xF7F92204, 0xB9B0F1CF, 0xF066F7F9,
    0x28017838, 0xE7FED100, 0x70382001, 0xFE88F44D,
    0x6803480B, 0x4630B11B, 0x46224629, 0xF44A4798,
    0xF89DFF75, 0x28000003, 0xBDF8D0FB, 0x20407CF0,
    0x00656B5F, 0x005F656B, 0x74616C70, 0x00000000,
    0x2040E5C0, 0x4605B5F8, 0xF040F7F9, 0x46044E16,
    0x46304629, 0xF671F7FE, 0x4813B118, 0xF7FE4629,
    0x2100F63B, 0x68364861, 0x6872B176, 0x1A9F686B,
    0x4770F027, 0xD8074287, 0xD1034293, 0x892B8932,
    0xD9014293, 0xE7EE4631, 0xB1194807, 0xF7FE462A,
    0xE004F662, 0xF7FE4629, 0xF450F6B2, 0x4620FF19,
    0x40F8E8BD, 0xB016F7F9, 0x2040FA98, 0x43FEE92D,
    0x8130F8DF, 0xF8D84668, 0xF4506000, 0xB1D6FD23,
    0x0008F8D8, 0x6871B1B8, 0x1A8B9A00, 0xF0231A51,
    0xF0214370, 0xF1B34170, 0xBF886F00, 0x4942424B,
    0x78097EC2, 0x428B4411, 0x2100DC05, 0x1008F8C8,
    0xB1016A01, 0x2E004788, 0x8930D06B, 0xF5A06871,
    0xF1B26287, 0xDC053FFF, 0xF2021E48, 0xF0202271,
    0xE7F64170, 0x4C359800, 0x1A0B46A1, 0x4370F023,
    0xD80642A3, 0xD12F4281, 0x0004F8BD, 0x46084282,
    0x4640DA2A, 0xF645F7FE, 0x050CF108, 0x46284601,
    0xF649F7FE, 0x4C2A4F28, 0x6000F8D8, 0xD0402E00,
    0x68717838, 0x1A08464B, 0x98001901, 0xF0221A42,
    0x454A4270, 0xF021D810, 0x42814170, 0x8931D104,
    0x2004F8BD, 0xD907428A, 0xF7FE4640, 0x4601F622,
    0xF7FE4628, 0xE7DFF628, 0xF06F4917, 0x78094270,
    0x68724051, 0x1A0A4411, 0x4270F022, 0xD808454A,
    0x4170F021, 0xD1144288, 0xF8BD8930, 0x42881004,
    0x4640D80F, 0xF605F7FE, 0xF8D84605, 0xB1100008,
    0xB1016A01, 0x69E94788, 0x5008F8C8, 0x4628B109,
    0xF8D84788, 0xB108000C, 0xFC52F450, 0xFA5EF451,
    0x83FEE8BD, 0x2040FAA0, 0x2040FA94, 0x07FFFFFE,
    0xF0000001, 0x48244925, 0xF5006101, 0x61412103,
    0x60C12171, 0x4180F44F, 0x68816081, 0x6108F441,
    0x68816081, 0x7110F441, 0x68816081, 0x0101F041,
    0x68016081, 0xD5FC0789, 0x60412101, 0xF0216881,
    0x60810101, 0xBF004770, 0x21014813, 0x68816041,
    0x0101F021, 0x49116081, 0xF5006101, 0x61412103,
    0x60C12171, 0x0110F244, 0x68816081, 0x6108F441,
    0x68816081, 0x7110F441, 0x68816081, 0x0101F041,
    0x68016081, 0xD5FC0789, 0x60412101, 0xF0216881,
    0x60810101, 0xBF004770, 0x40001000, 0x20407D18,
    0x4835B510, 0x60014935, 0x49364835, 0x48366001,
    0x60014936, 0x49374836, 0x48376001, 0x60014937,
    0x49384837, 0x48386001, 0x60014938, 0x49394838,
    0x48396001, 0x60014939, 0x493A4839, 0x483A6001,
    0x6001493A, 0x493B483A, 0x483B6001, 0x2314E9D0,
    0xF8D06C81, 0xF441409C, 0x64810180, 0x7101F443,
    0xF4446541, 0xF8D07180, 0xF8C04084, 0xF8D0109C,
    0xF04410A4, 0xF0410408, 0xF8C00108, 0xF8D010A4,
    0xF0411080, 0xF8C00108, 0x6CC11080, 0x0182F441,
    0xE9D064C1, 0xF8C01307, 0x4C284084, 0x65024322,
    0x43114A27, 0xF4434A27, 0xE9C06380, 0xF8D01307,
    0xF04110C4, 0xF8C00126, 0xF8D010C4, 0x431110A0,
    0x10A0F8C0, 0xF0416E41, 0x66410104, 0xF0416DC1,
    0x65C10101, 0xBF00BD10, 0x2040E508, 0x00407013,
    0x2040E50C, 0x0040701D, 0x2040E4EC, 0x00406E89,
    0x2040E4E8, 0x00406E6D, 0x2040E4F4, 0x00406F2B,
    0x2040E4F0, 0x00406EE9, 0x2040E4FC, 0x00406F73,
    0x2040E4F8, 0x00406F4D, 0x2040E504, 0x0040700D,
    0x2040E500, 0x00406FE3, 0x2040E514, 0x0040715F,
    0x2040E510, 0x004070D9, 0x2040EC28, 0x80012000,
    0x48085040, 0x000C0048, 0x2801B5F0, 0x4C46D101,
    0x4839E027, 0x2A012594, 0xFB018844, 0x4C354605,
    0xF4275B37, 0xEA476700, 0x533727C2, 0xFB018846,
    0x442C6505, 0xF834D106, 0xF0255F16, 0xEA4505E0,
    0xE0041543, 0x5F1AF834, 0x05FFF025, 0x8025441D,
    0x88808844, 0x24941B00, 0xF0F4FB90, 0xDD124288,
    0xF8544C09, 0x2A010021, 0x20CBF362, 0x0021F844,
    0x0181EB04, 0xF020BF1A, 0x441800FF, 0x200AF363,
    0x20006008, 0xE7FEBDF0, 0x2040EDB8, 0x2801B5B0,
    0x4825D12D, 0xF8504B17, 0xEB010021, 0xEA430141,
    0x4B151141, 0x6200F400, 0x5B0D8CDC, 0x6500F425,
    0x530A442A, 0x8CDC08C2, 0x440C0952, 0xF36288E5,
    0x80E51547, 0x44118CDA, 0xF3608A8A, 0x828A0207,
    0x680A490A, 0xD40A04D2, 0x0A00680A, 0x0007F000,
    0x6260F422, 0x680A600A, 0x2040EA42, 0x20006008,
    0xBF00BDB0, 0x20408000, 0x2040EE0C, 0x400900B0,
    0x4A09B510, 0xF8524B09, 0x400B1020, 0x798C4908,
    0x1004F991, 0x330EF364, 0x6301EA43, 0x3020F842,
    0x4010E8BD, 0xBA42F7FE, 0x2040EDF0, 0x00FF8FFF,
    0x20407CFC, 0x280A2200, 0x2201BFC8, 0xBF182900,
    0xEA422101, 0x47700001, 0x32B832A4, 0x33BC32CC,
    0x344C33CC, 0x44783468, 0x5CF85488, 0x0A070400,
    0xEC13100D, 0xF215EF13, 0xF71BF617, 0xF91EF81D,
    0xFB22FA20, 0xFD27FC24, 0xFF2FFE2B, 0x7F3E0034,
    0xF011ECFF, 0xF717F313, 0xFD1BFA18, 0x0125001E,
    0x032F022B, 0x7F3D0434, 0x000000FF, 0xE6000000,
    0xF911F010, 0x0113FE12, 0x051A0415, 0x07240620,
    0x092E0828, 0x7F3E0A34, 0x000000FF, 0x00000000,
    0x00406CF5, 0x00407CE4, 0x00000082, 0x00406D2D,
    0x00407CDF, 0x000000D1, 0x0A060300, 0x13100D0D,
    0x00406D75, 0x00407CD8, 0x000000D0, 0x00406E05,
    0x00407CE7, 0x0000004A, 0x31424C42, 0x42004230,
    0x00423631, 0x42004242, 0x48484842, 0x00000048,
};
void lcpu_patch_install()
{
#ifdef SOC_BF0_HCPU
    memset((void *)(LCPU_PATCH_START_ADDR_S), 0, LCPU_PATCH_TOTAL_SIZE);
    memcpy((void *)(LCPU_PATCH_START_ADDR_S), g_lcpu_patch_bin, sizeof(g_lcpu_patch_bin));
#else
    memset((void *)(LCPU_PATCH_START_ADDR), 0, LCPU_PATCH_TOTAL_SIZE);
    memcpy((void *)(LCPU_PATCH_START_ADDR), g_lcpu_patch_bin, sizeof(g_lcpu_patch_bin));
#endif
    memcpy((void *)(LCPU_PATCH_RECORD_ADDR), g_lcpu_patch_list, sizeof(g_lcpu_patch_list));
    HAL_PATCH_install();
}

#endif
