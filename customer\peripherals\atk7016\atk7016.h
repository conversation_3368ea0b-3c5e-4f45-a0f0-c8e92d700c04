/**
  ******************************************************************************
  * @file   atk7016.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the atk7016.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __ATK7016_H__
#define __ATK7016_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ATK7016
  * @{
  */

/** @defgroup ATK7016_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup ATK7016_Exported_Constants
  * @{
  */

/**
  * @brief ATK7016 chip IDs
  */
#define ATK7016_ID                  0x85

/**
  * @brief  ATK7016 Size
  */
#define  ATK7016_LCD_PIXEL_WIDTH    ((uint16_t)1024)
#define  ATK7016_LCD_PIXEL_HEIGHT   ((uint16_t)600)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define ATK7016_ORIENTATION_PORTRAIT         ((uint32_t)0x00) /* Portrait orientation choice of LCD screen  */
#define ATK7016_ORIENTATION_LANDSCAPE        ((uint32_t)0x01) /* Landscape orientation choice of LCD screen */
#define ATK7016_ORIENTATION_LANDSCAPE_ROT180 ((uint32_t)0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  ATK7016 Registers
  */
#define ATK7016_LCD_ID             0x04


/**
  * @}
  */

/** @defgroup ATK7016_Exported_Functions
  * @{
  */
void     ATK7016_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t ATK7016_ReadID(LCDC_HandleTypeDef *hlcdc);

void     ATK7016_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     ATK7016_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void ATK7016_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void ATK7016_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void ATK7016_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t ATK7016_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void ATK7016_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void ATK7016_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */



#ifdef __cplusplus
}
#endif

#endif /* __ATK7016_H__ */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
