/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File    :   vendor_os.c
*<AUTHOR>   qwkj
*@Date    :   2025/03/05
*@Description :   支付宝OS模块
************************************************************************/
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <rtthread.h>


#include "hal_os.h"
#include "csi_os.h"
#include "alipay_common.h"
#include "vendor_os.h"
#include "multicore_time.h"

uint32_t alipay_get_timestamp(void) __attribute__ ((weak));
void alipay_log_ext(const char *format, ...) __attribute__ ((weak));
uint32_t alipay_get_compile_timestamp() __attribute__ ((weak));
void alipay_free(void* pt) __attribute__ ((weak));
void* alipay_malloc(uint32_t size) __attribute__ ((weak));
void* alipay_realloc(void* pt, uint32_t size) __attribute__ ((weak));
void* alipay_calloc(uint32_t nblock,uint32_t size) __attribute__ ((weak));


int32_t get_Rng_number(uint8_t *data, unsigned size) {
    hal_rng_get_data(data, size);
    alipay_log_ext("random bytes[%d]: data[0]:0x%02x, data[1]:0x%02x - data[n-2]:0x%02x, data[n-1]0x%02x", size, data[0], data[1], data[size-2], data[size-1]);
    return 1;
}

uint32_t alipay_get_timestamp(void) {
    uint32_t ts_now = 0;
    hal_get_timestamp(&ts_now);
    return ts_now;
}

uint32_t alipay_get_compile_timestamp() {
    uint32_t build_timestamp = 0;
    hal_get_compile_timestamp(&build_timestamp);
    alipay_log_ext("build_timestamp: %d", build_timestamp);
    return build_timestamp;
}


void* alipay_malloc(uint32_t size) {
    void *ptr = hal_malloc(size);
    if (!ptr) {
        alipay_log_ext("malloc failed");
        return NULL;
    }
    memset(ptr, 0, size);
    return ptr;
}

void* alipay_calloc(uint32_t nblock,uint32_t size) {
    void *ptr = hal_calloc(nblock, size);
    if (!ptr) {
        alipay_log_ext("calloc failed");
    }
    return ptr;
}

void alipay_free(void* pt) {
    if (pt) hal_free(pt);
}

void* alipay_realloc(void* ptr, uint32_t size) {
    return hal_realloc(ptr, size);
}


void alipay_log_ext(const char *format, ...) {
    static char ALIPAY_log_buf[1024];
    memset(ALIPAY_log_buf, 0, sizeof(ALIPAY_log_buf));
    va_list arg;
    va_start(arg, format);
    vsnprintf(ALIPAY_log_buf, sizeof(ALIPAY_log_buf)-1, format, arg);
    hal_log_ext("%s\n", ALIPAY_log_buf);
    va_end(arg);
}

/*-------------------------------    HAL     --------------------------------------*/


hal_error_t hal_get_timestamp(uint32_t *tm)
{
    csi_error_t ret;

    if(!tm) {
        return HAL_INVALID_PARAM;
    }

    ret = csi_get_timestamp(tm);
    if(ret) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

hal_error_t hal_get_compile_timestamp(uint32_t *timestamp)
{
    csi_error_t ret;

    if(timestamp == NULL) {
        return HAL_INVALID_PARAM;
    }

    ret = csi_get_compile_timestamp(timestamp);
    if(ret) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

void *hal_malloc(uint32_t size)
{
    return csi_malloc(size);
}

void *hal_calloc(uint32_t nblock, uint32_t size)
{
    return csi_calloc(nblock, size);
}

void hal_free(void *pt)
{
    csi_free(pt);
}

void *hal_realloc(void *pt, uint32_t size)
{
    return csi_realloc(pt, size);
}

void hal_log_ext(const char *format, ...)
{
    va_list valist;
    va_start(valist,format);
    csi_log_ext(format,&valist);
    va_end(valist);
}

hal_error_t hal_rng_get_data(void *random, uint32_t size)
{
    csi_error_t ret;

    if(!random || !size) {
        return HAL_INVALID_PARAM;
    }

    ret = csi_rng_get_data(random, size);
    if(ret) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

void alipay_iot_get_local_time(alipay_iot_local_time *time)
{

    time->year = 2024;
    time->month = 8;
    time->day = 5;
    time->hour = 14;
    time->minute = 43;
    time->second = 12;
}

int alipay_rand(void)
{
    srand(time(0));

    // 生成一个0到100之间的随机数
    return rand() % 100;
}


void alipay_set_system_time(PARAM_IN int32_t timestamp_s)
{
    struct tm *tm_local;
    rt_err_t ret = 0;
    time_t time_value = (time_t)timestamp_s; // 转换为无符号类型

    tm_local = localtime(&time_value); // 将时间戳转换为本地时间

    if (tm_local)
    {
        //ret = multicore_time_write((multicore_time_t){tm_local->tm_year + 1900,tm_local->tm_mon + 1,tm_local->tm_mday,tm_local->tm_hour,tm_local->tm_min,tm_local->tm_sec});

        if(ret != 0)
        {
            rt_kprintf("multicore_time_write failed\n");
        }
    }
    else
    {
        rt_kprintf("exchange fail\n");
    }
}