/*
 * Copyright (C) 2015-2019 Alibaba Group Holding Limited
 */
#include <rtthread.h>
#include <string.h>
#include <stdio.h>
#include "utils.h"
#include "../se_v1/vendor_se_v1.h"
#include "ble_csi_layer.h"

extern char g_mac_address[18];

/*○ 功能描述
    ■ 获取设备ID号(以冒号分割的16进制mac地址)，要求内容以‘\0’结尾且长度不包含'\0'。所有字母大写，长度为17。例如：“AA:BB:CC:00:11:22”
  ○ 接口参数
    ■ buf_did - 存放设备ID数据地址
    ■ len_did - 存放设备ID长度地址
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_get_deviceid(uint8_t* buf_did, uint32_t *len_did)
{
	//  uint8_t Mac[6] = {0x08, 0x00, 0x20, 0x0A, 0x8C, 0x6D};
	 //Get_BleAddr(Mac);
     uint8_t Mac[6] = {0};
    csi_get_ble_addr(Mac);
	 char str[20];
	 sprintf(str,"%02x:%02x:%02x:%02x:%02x:%02x",Mac[0],Mac[1],Mac[2],Mac[3],Mac[4],Mac[5]);
	
    *len_did= strlen(str);
    memcpy(buf_did, str, strlen(str));	
	
    return CSI_OK;
}

/*○ 功能描述
    ■ 获取当前系统时间戳（Unix时间戳格式）
  ○ 接口参数
    ■ tm - 存放系统时间戳的变量地址
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_get_timestamp(uint32_t *tm)
{
    time_t now;

    now = time(RT_NULL);
    *tm = now;

    return CSI_OK;
}

/*○ description:
    ■ get compile timestamp
  ○ param
  ○ return
    ■ compile timestamp*/
csi_error_t csi_get_compile_timestamp(uint32_t *timestamp)
{
    if(timestamp == NULL) {
        return CSI_ERROR;
    }

    *timestamp = get_compile_timestamp();
    return CSI_OK;
}

/*○ 功能描述
    ■ 获取设备SN(厂商印刷在卡片上的设备序列号)，长度不超过32个字符，只能包含大小写字母、数字、下划线。仅卡片类产品且有SN在小程序显示需求的厂商实现，其他厂商请输出""(空字符串)，len_sn=0
  ○ 接口参数
    ■ buf_sn - 存放设备SN数据地址
    ■ len_sn - 存放设备SN长度地址
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_get_sn(uint8_t* buf_sn, uint32_t *len_sn)
{
    const char* mock_sn = "abc123";
    *len_sn = strlen(mock_sn);
    memcpy(buf_sn, mock_sn, strlen(mock_sn));
    
    return CSI_OK;
}

/*○ 功能描述
    ■ 获取设备company name
  ○ 接口参数
    ■ buffer - 存放设备company name数据地址
    ■ len - 存放设备company name长度地址
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_get_companyname(uint8_t* buffer, uint32_t* len)
{
    const char* mock_company = "iGPSPORT";
    *len = strlen(mock_company);
    memcpy(buffer, mock_company, strlen(mock_company));
    
    return CSI_OK;
}

/*○ 功能描述
    ■ 获取设备通讯协议类型
  ○ 接口参数
    ■ ptype - 存放设备通讯协议类型变量地址
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_get_protoctype(uint32_t* ptype)
{
    *ptype = 0;
    return CSI_OK;
}

/*○ 功能描述
    ■ 发送蓝牙数据
  ○ 接口参数
    ■ data - 存放发送数据地址
    ■ len - 存放发送数据长度(len<=20)
  ○ 返回值
    ■ 0表示成功，非0表示失败*/
csi_error_t csi_ble_write(uint8_t *data, uint16_t len)
{
    csi_send_nus_data(data, len);
    return CSI_OK;
}

/*○ 功能描述
    ■ 打印日志信息
  ○ 接口参数
    ■ level - 日志调试打印等级
    ■ format - 格式化输出字符串
    ■ value - 输出数据
  ○ 返回值
    ■ 无*/
void csi_log(int level, const char *format, int32_t value)
{
    //printf("[level%d %d] %s\n",level, value,format);
	MyPrintf("[level%d %d] %s\n",level, value,format);
}

/*○ 功能描述
    ■ 打印日志信息
  ○ 接口参数
    ■ format - 格式化输出字符串
    ■ ... - 可变参数
  ○ 返回值
    ■ 无*/
void csi_log_ext(const char *format, va_list* val_list)
{
   char log_buf[2048] = {0};
	//va_list arg_list;
	//va_start(arg_list, format);
	vsprintf(log_buf, format, *val_list);

	MyPrintf("%s", log_buf);
	//va_end(arg_list);
}

csi_error_t csi_get_productmodel(uint8_t* buffer, uint32_t* len)
{
	const char* mock_company = "WR02";
    *len = strlen(mock_company);
    memcpy(buffer, mock_company, strlen(mock_company));
    
	return CSI_OK;
}

void *csi_malloc(uint32_t size)
{
    return rt_malloc(size);
}

void *csi_calloc(uint32_t nblock, uint32_t size)
{
    return rt_calloc(nblock, size);
}

void csi_free(void *pt)
{
    rt_free(pt);
}

void *csi_realloc(void *pt, uint32_t size)
{
    return rt_realloc(pt, size);
}