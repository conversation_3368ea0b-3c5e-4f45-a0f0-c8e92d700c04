#ifndef _ZPA4756_H_
#define _ZPA4756_H_

#include <stdint.h>
#include <string.h>
#include <stdlib.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "bsp_board.h"

#ifndef SUCCEED
#define SUCCEED      true
#endif
#ifndef FAILURE
#define FAILURE      false
#endif

#define ZPA4756_ADDRESS	    0x5C //1011100
#define ZPA4756_PART_ID     0xB9 

#define WHOAMI3         0x04
#define WHOAMI2         0x05
#define WHOAMI1         0x06
#define WHOAMI0         0x07
#define ARB_CTRL        0x0B
#define DEVICE_ID       0x0F
#define RES_CONF        0x10
#define BUFF_CTRL       0x11
#define STBY            0x12
#define OPTN_REG0       0x13 
#define OPTN_REG1       0x14
#define CTR_REG0        0x20
#define CTR_REG1        0x21
#define CTR_REG2        0x22
#define CTR_REG3        0x23
#define STATUS_REG      0x27
#define PRESS_OUT_XL    0x28
#define PRESS_OUT_L     0x29
#define PRESS_OUT_H     0x2A
#define TEMP_OUT_L      0x2B
#define TEMP_OUT_H      0x2C


typedef enum {
    ZPA4756_CONTINUOUS_MODE = 0,
    ZPA4756_ONESHOT_MODE,
} ZPA4756_POWERMODE;


typedef struct
{
    int32_t altitude; //海拔高度
    int32_t temperature; //温度
}barometer_data_t;

//初始化
int32_t zpa4756_init(void);
//反初始化
int32_t zpa4756_deinit(void);
//读芯片ID
uint8_t zpa4756_checkID(void);
//读取数据
int32_t zpa4756_data_read(int32_t *bmp, int32_t *temperature);
//切换模式
int32_t zpa4756_switch_powermode(uint8_t power_mode);

#endif

