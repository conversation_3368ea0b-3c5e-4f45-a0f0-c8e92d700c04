#! armclang -E --target=arm-arm-none-eabi -mcpu=cortex-m33 -xc -I ../../../../drivers/cmsis/sf32lb55x 
#include "../rtconfig.h"
#include "mem_map.h"



; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 HCPU_FLASH_CODE_START_ADDR HCPU_FLASH_CODE_SIZE  {    ; load region size_region
  ER_IROM1 HCPU_FLASH_CODE_START_ADDR HCPU_FLASH_CODE_SIZE  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
   *.o (.rodata.*)
  }
  ER_IROM1_EX HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
   bf0_hal_qspi.o (.text.*)
   flash_table.o (.text.*)
   flash_table.o (.rodata.*)

   bf0_hal_hpaon.o (.text.*)

   *.o (.l1_non_ret_text_*)
   *.o (.l1_non_ret_rodata_*)
  } 

#ifdef BSP_USING_PSRAM
  RW_PSRAM1 PSRAM_BASE UNINIT{  ; ZI data, retained
    *.o (.l2_ret_data_*)
    *.o (.l2_ret_bss_*)
    *.o (.l2_cache_ret_data_*)
    *.o (.l2_cache_ret_bss_*)    
  }
  RW_PSRAM2 +0  UNINIT{  ; ZI data, not retained and reused by SRAM retention
    *.o (.nand_cache)
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)  
  }
  ScatterAssert((ImageLength(RW_PSRAM1)+ImageLength(RW_PSRAM2))<PSRAM_SIZE)
#endif

  RW_IRAM_RET HPSYS_RETM_BASE HPSYS_RETM_SIZE {  
;    .ANY2 (+RW +ZI)    

   *.o (.l1_ret_text_*)
   *.o (.l1_ret_rodata_*)
   *.o (.retm_bss_*)
   *.o (.retm_data_*)

 
   idle.o (.bss.rt_thread_stack)
   bf0_hal_rcc.o   (.text.*)
#ifdef BSP_USING_PM   
   bf0_pm.o        (.text.sifli_light_handler)
   bf0_pm.o        (.text.sifli_deep_handler)
   bf0_pm.o        (.text.sifli_standby_handler)
   drv_io.o           (.text.*)
   bf0_hal_gpio.o     (.text.*)
#endif  
    
    drv_psram.o(.bss.bf0_psram_handle)
  }

  ER_ITCM HPSYS_ITCM_BASE HPSYS_ITCM_SIZE {
  }

  RW_IRAM0 HCPU_RAM_DATA_START_ADDR UNINIT {  ; ZI data, not retained
#ifdef BSP_USING_PM  
    *.o (non_ret) ; non-retention section
    *.o (STACK)   ; ISR stack
#endif

    *.o (.l1_non_ret_data_*)
    *.o (.l1_non_ret_bss_*)
#ifndef BSP_USING_PSRAM
    *.o (.l2_non_ret_data_*)
    *.o (.l2_non_ret_bss_*)
    *.o (.l2_cache_non_ret_data_*)
    *.o (.l2_cache_non_ret_bss_*)
    *.o (.nand_cache)
#endif
  }  

  RW_IRAM1 +0  {  ; RW data  retained
#ifdef BSP_USING_JLINK_RTT  
   *.o (Jlink_RTT, +First)
#endif   
    *.o (.l1_ret_data_*)
    *.o (.l1_ret_bss_*)
   .ANY (+RW +ZI)
  }
  
  ScatterAssert(ImageLength(RW_IRAM0)+ImageLength(RW_IRAM1)<HCPU_RAM_DATA_SIZE)
}



;LR_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {    ; load region size_region
;  ER_IROM2 HCPU_RO_DATA_START_ADDR HCPU_RO_DATA_SIZE  {  ; load address = execution address
;   *.o (.rodata.*)
;  }
;}

;LR_IROM3 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; load region size_region
;  RW_IRAM6 PSRAM_BASE PSRAM_SIZE_HCPU  {  ; RW data
;   *.o (.ROM3_IMG)
;   }
;}


;LR_IROM3 RAM6_BASE RAM6_SIZE  {  ; load region size_region
;  RW_IRAM6 RAM6_BASE RAM6_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER1)
;   *.o (KWS_BIAS)
;  }
;  RW_IRAM7 RAM7_BASE RAM7_SIZE  {  ; RW data
;   *.o (KWS_WT)
;  }
;  RW_IRAM8 RAM8_BASE RAM8_SIZE  {  ; RW data
;   *.o (.bss.KWS_BUFFER2)
;   *.o (KWS_BIAS2)
;  }
;}


