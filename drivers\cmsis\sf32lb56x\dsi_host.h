#ifndef __DSI_HOST_H
#define __DSI_HOST_H

typedef struct
{
    __IO uint32_t VER;
    __IO uint32_t CTRL;
    __IO uint32_t DPI_CFG1;
    __IO uint32_t DPI_CFG2;
    __IO uint32_t DBI_CFG1;
    __IO uint32_t DBI_CFG2;
    __IO uint32_t COM_CFG;
    __IO uint32_t PKT_CFG;
    __IO uint32_t MODE_CFG;
    __IO uint32_t CMODE_CFG;
    __IO uint32_t COM_INTF;
    __IO uint32_t COM_PLD;
    __IO uint32_t CMD_PKT_STAT;
    __IO uint32_t ST_CNT_CFG1;
    __IO uint32_t ST_CNT_CFG2;
    __IO uint32_t ST_CNT_CFG3;
    __IO uint32_t ST_CNT_CFG4;
    __IO uint32_t VMODE_CFG1;
    __IO uint32_t VMODE_CFG2;
    __IO uint32_t VTIMER1;
    __IO uint32_t VTIMER2;
    __IO uint32_t VTIMER3;
    __IO uint32_t VTIMER4;
    __IO uint32_t SDF3D;
    __IO uint32_t PHY_CFG1;
    __IO uint32_t PHY_CFG2;
    __IO uint32_t PHY_CTRL;
    __IO uint32_t PHY_STAT;
    __IO uint32_t GP;
    __IO uint32_t INT_STAT1;
    __IO uint32_t INT_STAT2;
    __IO uint32_t INT_MASK1;
    __IO uint32_t INT_MASK2;
    __IO uint32_t INT_FORCE1;
    __IO uint32_t INT_FORCE2;
    __IO uint32_t VAUX_CTRL;
    __IO uint32_t DPI_CFG1_R;
    __IO uint32_t DPI_CFG2_R;
    __IO uint32_t VMODE_CFG1_R;
    __IO uint32_t VMODE_CFG2_R;
    __IO uint32_t VTIMER1_R;
    __IO uint32_t VTIMER2_R;
    __IO uint32_t VTIMER3_R;
    __IO uint32_t VTIMER4_R;
    __IO uint32_t SDF3D_R;
    __IO uint32_t DCOM_INTF_CTRL;
    __IO uint32_t DCOM_INTF_CFG1;
    __IO uint32_t DCOM_INTF_CFG2;
    __IO uint32_t DCOM_INTF_CFG3;
} DSI_HOST_TypeDef;


/****************** Bit definition for DSI_HOST_VER register ******************/
#define DSI_HOST_VER_VALUE_Pos          (0U)
#define DSI_HOST_VER_VALUE_Msk          (0xFFFFFFFFUL << DSI_HOST_VER_VALUE_Pos)
#define DSI_HOST_VER_VALUE              DSI_HOST_VER_VALUE_Msk

/***************** Bit definition for DSI_HOST_CTRL register ******************/
#define DSI_HOST_CTRL_PU_Pos            (0U)
#define DSI_HOST_CTRL_PU_Msk            (0x1UL << DSI_HOST_CTRL_PU_Pos)
#define DSI_HOST_CTRL_PU                DSI_HOST_CTRL_PU_Msk
#define DSI_HOST_CTRL_ESC_DIV_Pos       (1U)
#define DSI_HOST_CTRL_ESC_DIV_Msk       (0xFFUL << DSI_HOST_CTRL_ESC_DIV_Pos)
#define DSI_HOST_CTRL_ESC_DIV           DSI_HOST_CTRL_ESC_DIV_Msk
#define DSI_HOST_CTRL_TIMEOUT_DIV_Pos   (9U)
#define DSI_HOST_CTRL_TIMEOUT_DIV_Msk   (0xFFUL << DSI_HOST_CTRL_TIMEOUT_DIV_Pos)
#define DSI_HOST_CTRL_TIMEOUT_DIV       DSI_HOST_CTRL_TIMEOUT_DIV_Msk

/*************** Bit definition for DSI_HOST_DPI_CFG1 register ****************/
#define DSI_HOST_DPI_CFG1_VID_Pos       (0U)
#define DSI_HOST_DPI_CFG1_VID_Msk       (0x3UL << DSI_HOST_DPI_CFG1_VID_Pos)
#define DSI_HOST_DPI_CFG1_VID           DSI_HOST_DPI_CFG1_VID_Msk
#define DSI_HOST_DPI_CFG1_FORMAT_Pos    (2U)
#define DSI_HOST_DPI_CFG1_FORMAT_Msk    (0xFUL << DSI_HOST_DPI_CFG1_FORMAT_Pos)
#define DSI_HOST_DPI_CFG1_FORMAT        DSI_HOST_DPI_CFG1_FORMAT_Msk
#define DSI_HOST_DPI_CFG1_LOOSE_EN_Pos  (6U)
#define DSI_HOST_DPI_CFG1_LOOSE_EN_Msk  (0x1UL << DSI_HOST_DPI_CFG1_LOOSE_EN_Pos)
#define DSI_HOST_DPI_CFG1_LOOSE_EN      DSI_HOST_DPI_CFG1_LOOSE_EN_Msk
#define DSI_HOST_DPI_CFG1_D_EN_POL_Pos  (7U)
#define DSI_HOST_DPI_CFG1_D_EN_POL_Msk  (0x1UL << DSI_HOST_DPI_CFG1_D_EN_POL_Pos)
#define DSI_HOST_DPI_CFG1_D_EN_POL      DSI_HOST_DPI_CFG1_D_EN_POL_Msk
#define DSI_HOST_DPI_CFG1_VSYNC_POL_Pos  (8U)
#define DSI_HOST_DPI_CFG1_VSYNC_POL_Msk  (0x1UL << DSI_HOST_DPI_CFG1_VSYNC_POL_Pos)
#define DSI_HOST_DPI_CFG1_VSYNC_POL     DSI_HOST_DPI_CFG1_VSYNC_POL_Msk
#define DSI_HOST_DPI_CFG1_HSYNC_POL_Pos  (9U)
#define DSI_HOST_DPI_CFG1_HSYNC_POL_Msk  (0x1UL << DSI_HOST_DPI_CFG1_HSYNC_POL_Pos)
#define DSI_HOST_DPI_CFG1_HSYNC_POL     DSI_HOST_DPI_CFG1_HSYNC_POL_Msk
#define DSI_HOST_DPI_CFG1_SHUTDOWN_POL_Pos  (10U)
#define DSI_HOST_DPI_CFG1_SHUTDOWN_POL_Msk  (0x1UL << DSI_HOST_DPI_CFG1_SHUTDOWN_POL_Pos)
#define DSI_HOST_DPI_CFG1_SHUTDOWN_POL  DSI_HOST_DPI_CFG1_SHUTDOWN_POL_Msk
#define DSI_HOST_DPI_CFG1_COLORM_POL_Pos  (11U)
#define DSI_HOST_DPI_CFG1_COLORM_POL_Msk  (0x1UL << DSI_HOST_DPI_CFG1_COLORM_POL_Pos)
#define DSI_HOST_DPI_CFG1_COLORM_POL    DSI_HOST_DPI_CFG1_COLORM_POL_Msk

/*************** Bit definition for DSI_HOST_DPI_CFG2 register ****************/
#define DSI_HOST_DPI_CFG2_INVACT_Pos    (0U)
#define DSI_HOST_DPI_CFG2_INVACT_Msk    (0xFFUL << DSI_HOST_DPI_CFG2_INVACT_Pos)
#define DSI_HOST_DPI_CFG2_INVACT        DSI_HOST_DPI_CFG2_INVACT_Msk
#define DSI_HOST_DPI_CFG2_OUTVACT_Pos   (8U)
#define DSI_HOST_DPI_CFG2_OUTVACT_Msk   (0xFFUL << DSI_HOST_DPI_CFG2_OUTVACT_Pos)
#define DSI_HOST_DPI_CFG2_OUTVACT       DSI_HOST_DPI_CFG2_OUTVACT_Msk

/*************** Bit definition for DSI_HOST_DBI_CFG1 register ****************/
#define DSI_HOST_DBI_CFG1_VID_Pos       (0U)
#define DSI_HOST_DBI_CFG1_VID_Msk       (0x3UL << DSI_HOST_DBI_CFG1_VID_Pos)
#define DSI_HOST_DBI_CFG1_VID           DSI_HOST_DBI_CFG1_VID_Msk
#define DSI_HOST_DBI_CFG1_IFORMAT_Pos   (2U)
#define DSI_HOST_DBI_CFG1_IFORMAT_Msk   (0xFUL << DSI_HOST_DBI_CFG1_IFORMAT_Pos)
#define DSI_HOST_DBI_CFG1_IFORMAT       DSI_HOST_DBI_CFG1_IFORMAT_Msk
#define DSI_HOST_DBI_CFG1_OFORMAT_Pos   (6U)
#define DSI_HOST_DBI_CFG1_OFORMAT_Msk   (0xFUL << DSI_HOST_DBI_CFG1_OFORMAT_Pos)
#define DSI_HOST_DBI_CFG1_OFORMAT       DSI_HOST_DBI_CFG1_OFORMAT_Msk
#define DSI_HOST_DBI_CFG1_LUT_SIZE_Pos  (10U)
#define DSI_HOST_DBI_CFG1_LUT_SIZE_Msk  (0x3UL << DSI_HOST_DBI_CFG1_LUT_SIZE_Pos)
#define DSI_HOST_DBI_CFG1_LUT_SIZE      DSI_HOST_DBI_CFG1_LUT_SIZE_Msk
#define DSI_HOST_DBI_CFG1_PARTITIONING_EN_Pos  (12U)
#define DSI_HOST_DBI_CFG1_PARTITIONING_EN_Msk  (0x1UL << DSI_HOST_DBI_CFG1_PARTITIONING_EN_Pos)
#define DSI_HOST_DBI_CFG1_PARTITIONING_EN  DSI_HOST_DBI_CFG1_PARTITIONING_EN_Msk
#define DSI_HOST_DBI_CFG1_INTF_DLY_SEL_Pos  (13U)
#define DSI_HOST_DBI_CFG1_INTF_DLY_SEL_Msk  (0x3UL << DSI_HOST_DBI_CFG1_INTF_DLY_SEL_Pos)
#define DSI_HOST_DBI_CFG1_INTF_DLY_SEL  DSI_HOST_DBI_CFG1_INTF_DLY_SEL_Msk
#define DSI_HOST_DBI_CFG1_WR_CONT_MODE_Pos  (15U)
#define DSI_HOST_DBI_CFG1_WR_CONT_MODE_Msk  (0x1UL << DSI_HOST_DBI_CFG1_WR_CONT_MODE_Pos)
#define DSI_HOST_DBI_CFG1_WR_CONT_MODE  DSI_HOST_DBI_CFG1_WR_CONT_MODE_Msk
#define DSI_HOST_DBI_CFG1_TURBO_MODE_Pos  (16U)
#define DSI_HOST_DBI_CFG1_TURBO_MODE_Msk  (0x1UL << DSI_HOST_DBI_CFG1_TURBO_MODE_Pos)
#define DSI_HOST_DBI_CFG1_TURBO_MODE    DSI_HOST_DBI_CFG1_TURBO_MODE_Msk
#define DSI_HOST_DBI_CFG1_TURBO_RSTN_Pos  (17U)
#define DSI_HOST_DBI_CFG1_TURBO_RSTN_Msk  (0x1UL << DSI_HOST_DBI_CFG1_TURBO_RSTN_Pos)
#define DSI_HOST_DBI_CFG1_TURBO_RSTN    DSI_HOST_DBI_CFG1_TURBO_RSTN_Msk

/*************** Bit definition for DSI_HOST_DBI_CFG2 register ****************/
#define DSI_HOST_DBI_CFG2_WR_SIZE_Pos   (0U)
#define DSI_HOST_DBI_CFG2_WR_SIZE_Msk   (0xFFFFUL << DSI_HOST_DBI_CFG2_WR_SIZE_Pos)
#define DSI_HOST_DBI_CFG2_WR_SIZE       DSI_HOST_DBI_CFG2_WR_SIZE_Msk
#define DSI_HOST_DBI_CFG2_ALLOWED_SIZE_Pos  (16U)
#define DSI_HOST_DBI_CFG2_ALLOWED_SIZE_Msk  (0xFFFFUL << DSI_HOST_DBI_CFG2_ALLOWED_SIZE_Pos)
#define DSI_HOST_DBI_CFG2_ALLOWED_SIZE  DSI_HOST_DBI_CFG2_ALLOWED_SIZE_Msk

/**************** Bit definition for DSI_HOST_COM_CFG register ****************/
#define DSI_HOST_COM_CFG_VID_Pos        (0U)
#define DSI_HOST_COM_CFG_VID_Msk        (0x3UL << DSI_HOST_COM_CFG_VID_Pos)
#define DSI_HOST_COM_CFG_VID            DSI_HOST_COM_CFG_VID_Msk

/**************** Bit definition for DSI_HOST_PKT_CFG register ****************/
#define DSI_HOST_PKT_CFG_EOTP_TX_EN_Pos  (0U)
#define DSI_HOST_PKT_CFG_EOTP_TX_EN_Msk  (0x1UL << DSI_HOST_PKT_CFG_EOTP_TX_EN_Pos)
#define DSI_HOST_PKT_CFG_EOTP_TX_EN     DSI_HOST_PKT_CFG_EOTP_TX_EN_Msk
#define DSI_HOST_PKT_CFG_EOTP_RX_EN_Pos  (1U)
#define DSI_HOST_PKT_CFG_EOTP_RX_EN_Msk  (0x1UL << DSI_HOST_PKT_CFG_EOTP_RX_EN_Pos)
#define DSI_HOST_PKT_CFG_EOTP_RX_EN     DSI_HOST_PKT_CFG_EOTP_RX_EN_Msk
#define DSI_HOST_PKT_CFG_BTA_EN_Pos     (2U)
#define DSI_HOST_PKT_CFG_BTA_EN_Msk     (0x1UL << DSI_HOST_PKT_CFG_BTA_EN_Pos)
#define DSI_HOST_PKT_CFG_BTA_EN         DSI_HOST_PKT_CFG_BTA_EN_Msk
#define DSI_HOST_PKT_CFG_ECC_RX_EN_Pos  (3U)
#define DSI_HOST_PKT_CFG_ECC_RX_EN_Msk  (0x1UL << DSI_HOST_PKT_CFG_ECC_RX_EN_Pos)
#define DSI_HOST_PKT_CFG_ECC_RX_EN      DSI_HOST_PKT_CFG_ECC_RX_EN_Msk
#define DSI_HOST_PKT_CFG_CRC_RX_EN_Pos  (4U)
#define DSI_HOST_PKT_CFG_CRC_RX_EN_Msk  (0x1UL << DSI_HOST_PKT_CFG_CRC_RX_EN_Pos)
#define DSI_HOST_PKT_CFG_CRC_RX_EN      DSI_HOST_PKT_CFG_CRC_RX_EN_Msk

/*************** Bit definition for DSI_HOST_MODE_CFG register ****************/
#define DSI_HOST_MODE_CFG_CMD_VIDEO_SEL_Pos  (0U)
#define DSI_HOST_MODE_CFG_CMD_VIDEO_SEL_Msk  (0x1UL << DSI_HOST_MODE_CFG_CMD_VIDEO_SEL_Pos)
#define DSI_HOST_MODE_CFG_CMD_VIDEO_SEL  DSI_HOST_MODE_CFG_CMD_VIDEO_SEL_Msk

/*************** Bit definition for DSI_HOST_CMODE_CFG register ***************/
#define DSI_HOST_CMODE_CFG_FMARK_EN_Pos  (0U)
#define DSI_HOST_CMODE_CFG_FMARK_EN_Msk  (0x1UL << DSI_HOST_CMODE_CFG_FMARK_EN_Pos)
#define DSI_HOST_CMODE_CFG_FMARK_EN     DSI_HOST_CMODE_CFG_FMARK_EN_Msk
#define DSI_HOST_CMODE_CFG_ACK_REQ_EN_Pos  (1U)
#define DSI_HOST_CMODE_CFG_ACK_REQ_EN_Msk  (0x1UL << DSI_HOST_CMODE_CFG_ACK_REQ_EN_Pos)
#define DSI_HOST_CMODE_CFG_ACK_REQ_EN   DSI_HOST_CMODE_CFG_ACK_REQ_EN_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR0_Pos  (2U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR0_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_WR0_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR0  DSI_HOST_CMODE_CFG_COM_SHORT_WR0_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR1_Pos  (3U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR1_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_WR1_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR1  DSI_HOST_CMODE_CFG_COM_SHORT_WR1_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR2_Pos  (4U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR2_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_WR2_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_WR2  DSI_HOST_CMODE_CFG_COM_SHORT_WR2_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD0_Pos  (5U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD0_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_RD0_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD0  DSI_HOST_CMODE_CFG_COM_SHORT_RD0_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD1_Pos  (6U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD1_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_RD1_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD1  DSI_HOST_CMODE_CFG_COM_SHORT_RD1_Msk
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD2_Pos  (7U)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD2_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_SHORT_RD2_Pos)
#define DSI_HOST_CMODE_CFG_COM_SHORT_RD2  DSI_HOST_CMODE_CFG_COM_SHORT_RD2_Msk
#define DSI_HOST_CMODE_CFG_COM_LONG_WR_Pos  (8U)
#define DSI_HOST_CMODE_CFG_COM_LONG_WR_Msk  (0x1UL << DSI_HOST_CMODE_CFG_COM_LONG_WR_Pos)
#define DSI_HOST_CMODE_CFG_COM_LONG_WR  DSI_HOST_CMODE_CFG_COM_LONG_WR_Msk
#define DSI_HOST_CMODE_CFG_SHORT_WR0_Pos  (9U)
#define DSI_HOST_CMODE_CFG_SHORT_WR0_Msk  (0x1UL << DSI_HOST_CMODE_CFG_SHORT_WR0_Pos)
#define DSI_HOST_CMODE_CFG_SHORT_WR0    DSI_HOST_CMODE_CFG_SHORT_WR0_Msk
#define DSI_HOST_CMODE_CFG_SHORT_WR1_Pos  (10U)
#define DSI_HOST_CMODE_CFG_SHORT_WR1_Msk  (0x1UL << DSI_HOST_CMODE_CFG_SHORT_WR1_Pos)
#define DSI_HOST_CMODE_CFG_SHORT_WR1    DSI_HOST_CMODE_CFG_SHORT_WR1_Msk
#define DSI_HOST_CMODE_CFG_SHORT_RD0_Pos  (11U)
#define DSI_HOST_CMODE_CFG_SHORT_RD0_Msk  (0x1UL << DSI_HOST_CMODE_CFG_SHORT_RD0_Pos)
#define DSI_HOST_CMODE_CFG_SHORT_RD0    DSI_HOST_CMODE_CFG_SHORT_RD0_Msk
#define DSI_HOST_CMODE_CFG_LONG_WR_Pos  (12U)
#define DSI_HOST_CMODE_CFG_LONG_WR_Msk  (0x1UL << DSI_HOST_CMODE_CFG_LONG_WR_Pos)
#define DSI_HOST_CMODE_CFG_LONG_WR      DSI_HOST_CMODE_CFG_LONG_WR_Msk
#define DSI_HOST_CMODE_CFG_MAX_RD_SIZE_Pos  (13U)
#define DSI_HOST_CMODE_CFG_MAX_RD_SIZE_Msk  (0x1UL << DSI_HOST_CMODE_CFG_MAX_RD_SIZE_Pos)
#define DSI_HOST_CMODE_CFG_MAX_RD_SIZE  DSI_HOST_CMODE_CFG_MAX_RD_SIZE_Msk

/*************** Bit definition for DSI_HOST_COM_INTF register ****************/
#define DSI_HOST_COM_INTF_DATA_TYPE_Pos  (0U)
#define DSI_HOST_COM_INTF_DATA_TYPE_Msk  (0x3FUL << DSI_HOST_COM_INTF_DATA_TYPE_Pos)
#define DSI_HOST_COM_INTF_DATA_TYPE     DSI_HOST_COM_INTF_DATA_TYPE_Msk
#define DSI_HOST_COM_INTF_VID_Pos       (6U)
#define DSI_HOST_COM_INTF_VID_Msk       (0x3UL << DSI_HOST_COM_INTF_VID_Pos)
#define DSI_HOST_COM_INTF_VID           DSI_HOST_COM_INTF_VID_Msk
#define DSI_HOST_COM_INTF_LOW_DATA_Pos  (8U)
#define DSI_HOST_COM_INTF_LOW_DATA_Msk  (0xFFUL << DSI_HOST_COM_INTF_LOW_DATA_Pos)
#define DSI_HOST_COM_INTF_LOW_DATA      DSI_HOST_COM_INTF_LOW_DATA_Msk
#define DSI_HOST_COM_INTF_HIGH_DATA_Pos  (16U)
#define DSI_HOST_COM_INTF_HIGH_DATA_Msk  (0xFFUL << DSI_HOST_COM_INTF_HIGH_DATA_Pos)
#define DSI_HOST_COM_INTF_HIGH_DATA     DSI_HOST_COM_INTF_HIGH_DATA_Msk

/**************** Bit definition for DSI_HOST_COM_PLD register ****************/
#define DSI_HOST_COM_PLD_PDATA_Pos      (0U)
#define DSI_HOST_COM_PLD_PDATA_Msk      (0xFFFFFFFFUL << DSI_HOST_COM_PLD_PDATA_Pos)
#define DSI_HOST_COM_PLD_PDATA          DSI_HOST_COM_PLD_PDATA_Msk

/************* Bit definition for DSI_HOST_CMD_PKT_STAT register **************/
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_EMPTY_Pos  (0U)
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_CMD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_EMPTY  DSI_HOST_CMD_PKT_STAT_COM_CMD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_FULL_Pos  (1U)
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_CMD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_CMD_FULL  DSI_HOST_CMD_PKT_STAT_COM_CMD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_EMPTY_Pos  (2U)
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_WRPLD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_EMPTY  DSI_HOST_CMD_PKT_STAT_COM_WRPLD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_FULL_Pos  (3U)
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_WRPLD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_WRPLD_FULL  DSI_HOST_CMD_PKT_STAT_COM_WRPLD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_EMPTY_Pos  (4U)
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_RDPLD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_EMPTY  DSI_HOST_CMD_PKT_STAT_COM_RDPLD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_FULL_Pos  (5U)
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_RDPLD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_RDPLD_FULL  DSI_HOST_CMD_PKT_STAT_COM_RDPLD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_COM_RDCMD_BUSY_Pos  (6U)
#define DSI_HOST_CMD_PKT_STAT_COM_RDCMD_BUSY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_COM_RDCMD_BUSY_Pos)
#define DSI_HOST_CMD_PKT_STAT_COM_RDCMD_BUSY  DSI_HOST_CMD_PKT_STAT_COM_RDCMD_BUSY_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_EMPTY_Pos  (7U)
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_CMD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_EMPTY  DSI_HOST_CMD_PKT_STAT_DBI_CMD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_FULL_Pos  (8U)
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_CMD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_CMD_FULL  DSI_HOST_CMD_PKT_STAT_DBI_CMD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_EMPTY_Pos  (9U)
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_EMPTY  DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_FULL_Pos  (10U)
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_FULL  DSI_HOST_CMD_PKT_STAT_DBI_WRPLD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_EMPTY_Pos  (11U)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_EMPTY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_EMPTY_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_EMPTY  DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_EMPTY_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_FULL_Pos  (12U)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_FULL_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_FULL_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_FULL  DSI_HOST_CMD_PKT_STAT_DBI_RDPLD_FULL_Msk
#define DSI_HOST_CMD_PKT_STAT_DBI_RDCMD_BUSY_Pos  (13U)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDCMD_BUSY_Msk  (0x1UL << DSI_HOST_CMD_PKT_STAT_DBI_RDCMD_BUSY_Pos)
#define DSI_HOST_CMD_PKT_STAT_DBI_RDCMD_BUSY  DSI_HOST_CMD_PKT_STAT_DBI_RDCMD_BUSY_Msk

/************** Bit definition for DSI_HOST_ST_CNT_CFG1 register **************/
#define DSI_HOST_ST_CNT_CFG1_HSTX_TIMEOUT_Pos  (0U)
#define DSI_HOST_ST_CNT_CFG1_HSTX_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG1_HSTX_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG1_HSTX_TIMEOUT  DSI_HOST_ST_CNT_CFG1_HSTX_TIMEOUT_Msk
#define DSI_HOST_ST_CNT_CFG1_LPRX_TIMEOUT_Pos  (16U)
#define DSI_HOST_ST_CNT_CFG1_LPRX_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG1_LPRX_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG1_LPRX_TIMEOUT  DSI_HOST_ST_CNT_CFG1_LPRX_TIMEOUT_Msk

/************** Bit definition for DSI_HOST_ST_CNT_CFG2 register **************/
#define DSI_HOST_ST_CNT_CFG2_HSRD_TIMEOUT_Pos  (0U)
#define DSI_HOST_ST_CNT_CFG2_HSRD_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG2_HSRD_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG2_HSRD_TIMEOUT  DSI_HOST_ST_CNT_CFG2_HSRD_TIMEOUT_Msk
#define DSI_HOST_ST_CNT_CFG2_LPRD_TIMEOUT_Pos  (16U)
#define DSI_HOST_ST_CNT_CFG2_LPRD_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG2_LPRD_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG2_LPRD_TIMEOUT  DSI_HOST_ST_CNT_CFG2_LPRD_TIMEOUT_Msk

/************** Bit definition for DSI_HOST_ST_CNT_CFG3 register **************/
#define DSI_HOST_ST_CNT_CFG3_HSWR_TIMEOUT_Pos  (0U)
#define DSI_HOST_ST_CNT_CFG3_HSWR_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG3_HSWR_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG3_HSWR_TIMEOUT  DSI_HOST_ST_CNT_CFG3_HSWR_TIMEOUT_Msk
#define DSI_HOST_ST_CNT_CFG3_LPWR_TIMEOUT_Pos  (16U)
#define DSI_HOST_ST_CNT_CFG3_LPWR_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG3_LPWR_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG3_LPWR_TIMEOUT  DSI_HOST_ST_CNT_CFG3_LPWR_TIMEOUT_Msk

/************** Bit definition for DSI_HOST_ST_CNT_CFG4 register **************/
#define DSI_HOST_ST_CNT_CFG4_BTA_TIMEOUT_Pos  (0U)
#define DSI_HOST_ST_CNT_CFG4_BTA_TIMEOUT_Msk  (0xFFFFUL << DSI_HOST_ST_CNT_CFG4_BTA_TIMEOUT_Pos)
#define DSI_HOST_ST_CNT_CFG4_BTA_TIMEOUT  DSI_HOST_ST_CNT_CFG4_BTA_TIMEOUT_Msk

/************** Bit definition for DSI_HOST_VMODE_CFG1 register ***************/
#define DSI_HOST_VMODE_CFG1_VMODE_TYPE_Pos  (0U)
#define DSI_HOST_VMODE_CFG1_VMODE_TYPE_Msk  (0x3UL << DSI_HOST_VMODE_CFG1_VMODE_TYPE_Pos)
#define DSI_HOST_VMODE_CFG1_VMODE_TYPE  DSI_HOST_VMODE_CFG1_VMODE_TYPE_Msk
#define DSI_HOST_VMODE_CFG1_LPVSA_EN_Pos  (2U)
#define DSI_HOST_VMODE_CFG1_LPVSA_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPVSA_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPVSA_EN    DSI_HOST_VMODE_CFG1_LPVSA_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPVBP_EN_Pos  (3U)
#define DSI_HOST_VMODE_CFG1_LPVBP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPVBP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPVBP_EN    DSI_HOST_VMODE_CFG1_LPVBP_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPVFP_EN_Pos  (4U)
#define DSI_HOST_VMODE_CFG1_LPVFP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPVFP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPVFP_EN    DSI_HOST_VMODE_CFG1_LPVFP_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPVACT_EN_Pos  (5U)
#define DSI_HOST_VMODE_CFG1_LPVACT_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPVACT_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPVACT_EN   DSI_HOST_VMODE_CFG1_LPVACT_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPHBP_EN_Pos  (6U)
#define DSI_HOST_VMODE_CFG1_LPHBP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPHBP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPHBP_EN    DSI_HOST_VMODE_CFG1_LPHBP_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPHFP_EN_Pos  (7U)
#define DSI_HOST_VMODE_CFG1_LPHFP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPHFP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPHFP_EN    DSI_HOST_VMODE_CFG1_LPHFP_EN_Msk
#define DSI_HOST_VMODE_CFG1_BTA_ACK_EN_Pos  (8U)
#define DSI_HOST_VMODE_CFG1_BTA_ACK_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_BTA_ACK_EN_Pos)
#define DSI_HOST_VMODE_CFG1_BTA_ACK_EN  DSI_HOST_VMODE_CFG1_BTA_ACK_EN_Msk
#define DSI_HOST_VMODE_CFG1_LPCMD_EN_Pos  (9U)
#define DSI_HOST_VMODE_CFG1_LPCMD_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_LPCMD_EN_Pos)
#define DSI_HOST_VMODE_CFG1_LPCMD_EN    DSI_HOST_VMODE_CFG1_LPCMD_EN_Msk
#define DSI_HOST_VMODE_CFG1_VPG_EN_Pos  (10U)
#define DSI_HOST_VMODE_CFG1_VPG_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_VPG_EN_Pos)
#define DSI_HOST_VMODE_CFG1_VPG_EN      DSI_HOST_VMODE_CFG1_VPG_EN_Msk
#define DSI_HOST_VMODE_CFG1_VPG_MODE_Pos  (11U)
#define DSI_HOST_VMODE_CFG1_VPG_MODE_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_VPG_MODE_Pos)
#define DSI_HOST_VMODE_CFG1_VPG_MODE    DSI_HOST_VMODE_CFG1_VPG_MODE_Msk
#define DSI_HOST_VMODE_CFG1_VPG_ORIENTATION_Pos  (12U)
#define DSI_HOST_VMODE_CFG1_VPG_ORIENTATION_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_VPG_ORIENTATION_Pos)
#define DSI_HOST_VMODE_CFG1_VPG_ORIENTATION  DSI_HOST_VMODE_CFG1_VPG_ORIENTATION_Msk
#define DSI_HOST_VMODE_CFG1_PKT_SIZE_Pos  (13U)
#define DSI_HOST_VMODE_CFG1_PKT_SIZE_Msk  (0x3FFFUL << DSI_HOST_VMODE_CFG1_PKT_SIZE_Pos)
#define DSI_HOST_VMODE_CFG1_PKT_SIZE    DSI_HOST_VMODE_CFG1_PKT_SIZE_Msk

/************** Bit definition for DSI_HOST_VMODE_CFG2 register ***************/
#define DSI_HOST_VMODE_CFG2_NUM_CHUNKS_Pos  (0U)
#define DSI_HOST_VMODE_CFG2_NUM_CHUNKS_Msk  (0x1FFFUL << DSI_HOST_VMODE_CFG2_NUM_CHUNKS_Pos)
#define DSI_HOST_VMODE_CFG2_NUM_CHUNKS  DSI_HOST_VMODE_CFG2_NUM_CHUNKS_Msk
#define DSI_HOST_VMODE_CFG2_NULL_SIZE_Pos  (13U)
#define DSI_HOST_VMODE_CFG2_NULL_SIZE_Msk  (0x1FFFUL << DSI_HOST_VMODE_CFG2_NULL_SIZE_Pos)
#define DSI_HOST_VMODE_CFG2_NULL_SIZE   DSI_HOST_VMODE_CFG2_NULL_SIZE_Msk

/**************** Bit definition for DSI_HOST_VTIMER1 register ****************/
#define DSI_HOST_VTIMER1_HSA_Pos        (0U)
#define DSI_HOST_VTIMER1_HSA_Msk        (0xFFFUL << DSI_HOST_VTIMER1_HSA_Pos)
#define DSI_HOST_VTIMER1_HSA            DSI_HOST_VTIMER1_HSA_Msk
#define DSI_HOST_VTIMER1_HBP_Pos        (12U)
#define DSI_HOST_VTIMER1_HBP_Msk        (0xFFFUL << DSI_HOST_VTIMER1_HBP_Pos)
#define DSI_HOST_VTIMER1_HBP            DSI_HOST_VTIMER1_HBP_Msk

/**************** Bit definition for DSI_HOST_VTIMER2 register ****************/
#define DSI_HOST_VTIMER2_HLINE_Pos      (0U)
#define DSI_HOST_VTIMER2_HLINE_Msk      (0x7FFFUL << DSI_HOST_VTIMER2_HLINE_Pos)
#define DSI_HOST_VTIMER2_HLINE          DSI_HOST_VTIMER2_HLINE_Msk
#define DSI_HOST_VTIMER2_VSA_Pos        (15U)
#define DSI_HOST_VTIMER2_VSA_Msk        (0x3FFUL << DSI_HOST_VTIMER2_VSA_Pos)
#define DSI_HOST_VTIMER2_VSA            DSI_HOST_VTIMER2_VSA_Msk

/**************** Bit definition for DSI_HOST_VTIMER3 register ****************/
#define DSI_HOST_VTIMER3_VBP_Pos        (0U)
#define DSI_HOST_VTIMER3_VBP_Msk        (0x3FFUL << DSI_HOST_VTIMER3_VBP_Pos)
#define DSI_HOST_VTIMER3_VBP            DSI_HOST_VTIMER3_VBP_Msk
#define DSI_HOST_VTIMER3_VFP_Pos        (10U)
#define DSI_HOST_VTIMER3_VFP_Msk        (0x3FFUL << DSI_HOST_VTIMER3_VFP_Pos)
#define DSI_HOST_VTIMER3_VFP            DSI_HOST_VTIMER3_VFP_Msk

/**************** Bit definition for DSI_HOST_VTIMER4 register ****************/
#define DSI_HOST_VTIMER4_VACT_Pos       (0U)
#define DSI_HOST_VTIMER4_VACT_Msk       (0x3FFFUL << DSI_HOST_VTIMER4_VACT_Pos)
#define DSI_HOST_VTIMER4_VACT           DSI_HOST_VTIMER4_VACT_Msk

/***************** Bit definition for DSI_HOST_SDF3D register *****************/
#define DSI_HOST_SDF3D_MODE_Pos         (0U)
#define DSI_HOST_SDF3D_MODE_Msk         (0x3UL << DSI_HOST_SDF3D_MODE_Pos)
#define DSI_HOST_SDF3D_MODE             DSI_HOST_SDF3D_MODE_Msk
#define DSI_HOST_SDF3D_FORMAT_Pos       (2U)
#define DSI_HOST_SDF3D_FORMAT_Msk       (0x3UL << DSI_HOST_SDF3D_FORMAT_Pos)
#define DSI_HOST_SDF3D_FORMAT           DSI_HOST_SDF3D_FORMAT_Msk
#define DSI_HOST_SDF3D_VSYNC_2ND_Pos    (4U)
#define DSI_HOST_SDF3D_VSYNC_2ND_Msk    (0x1UL << DSI_HOST_SDF3D_VSYNC_2ND_Pos)
#define DSI_HOST_SDF3D_VSYNC_2ND        DSI_HOST_SDF3D_VSYNC_2ND_Msk
#define DSI_HOST_SDF3D_DATA_ORDER_Pos   (5U)
#define DSI_HOST_SDF3D_DATA_ORDER_Msk   (0x1UL << DSI_HOST_SDF3D_DATA_ORDER_Pos)
#define DSI_HOST_SDF3D_DATA_ORDER       DSI_HOST_SDF3D_DATA_ORDER_Msk
#define DSI_HOST_SDF3D_CFG_EN_Pos       (6U)
#define DSI_HOST_SDF3D_CFG_EN_Msk       (0x1UL << DSI_HOST_SDF3D_CFG_EN_Pos)
#define DSI_HOST_SDF3D_CFG_EN           DSI_HOST_SDF3D_CFG_EN_Msk

/*************** Bit definition for DSI_HOST_PHY_CFG1 register ****************/
#define DSI_HOST_PHY_CFG1_NUM_ACT_LANE_Pos  (0U)
#define DSI_HOST_PHY_CFG1_NUM_ACT_LANE_Msk  (0x3UL << DSI_HOST_PHY_CFG1_NUM_ACT_LANE_Pos)
#define DSI_HOST_PHY_CFG1_NUM_ACT_LANE  DSI_HOST_PHY_CFG1_NUM_ACT_LANE_Msk
#define DSI_HOST_PHY_CFG1_WAIT_TIMER_Pos  (2U)
#define DSI_HOST_PHY_CFG1_WAIT_TIMER_Msk  (0xFFUL << DSI_HOST_PHY_CFG1_WAIT_TIMER_Pos)
#define DSI_HOST_PHY_CFG1_WAIT_TIMER    DSI_HOST_PHY_CFG1_WAIT_TIMER_Msk
#define DSI_HOST_PHY_CFG1_CKLP2HS_TIMER_Pos  (10U)
#define DSI_HOST_PHY_CFG1_CKLP2HS_TIMER_Msk  (0x3FFUL << DSI_HOST_PHY_CFG1_CKLP2HS_TIMER_Pos)
#define DSI_HOST_PHY_CFG1_CKLP2HS_TIMER  DSI_HOST_PHY_CFG1_CKLP2HS_TIMER_Msk
#define DSI_HOST_PHY_CFG1_CKHS2LP_TIMER_Pos  (20U)
#define DSI_HOST_PHY_CFG1_CKHS2LP_TIMER_Msk  (0x3FFUL << DSI_HOST_PHY_CFG1_CKHS2LP_TIMER_Pos)
#define DSI_HOST_PHY_CFG1_CKHS2LP_TIMER  DSI_HOST_PHY_CFG1_CKHS2LP_TIMER_Msk

/*************** Bit definition for DSI_HOST_PHY_CFG2 register ****************/
#define DSI_HOST_PHY_CFG2_DATALP2HS_TIMER_Pos  (0U)
#define DSI_HOST_PHY_CFG2_DATALP2HS_TIMER_Msk  (0xFFUL << DSI_HOST_PHY_CFG2_DATALP2HS_TIMER_Pos)
#define DSI_HOST_PHY_CFG2_DATALP2HS_TIMER  DSI_HOST_PHY_CFG2_DATALP2HS_TIMER_Msk
#define DSI_HOST_PHY_CFG2_DATAHS2LP_TIMER_Pos  (8U)
#define DSI_HOST_PHY_CFG2_DATAHS2LP_TIMER_Msk  (0xFFUL << DSI_HOST_PHY_CFG2_DATAHS2LP_TIMER_Pos)
#define DSI_HOST_PHY_CFG2_DATAHS2LP_TIMER  DSI_HOST_PHY_CFG2_DATAHS2LP_TIMER_Msk
#define DSI_HOST_PHY_CFG2_MAX_RD_TIMER_Pos  (16U)
#define DSI_HOST_PHY_CFG2_MAX_RD_TIMER_Msk  (0x7FFFUL << DSI_HOST_PHY_CFG2_MAX_RD_TIMER_Pos)
#define DSI_HOST_PHY_CFG2_MAX_RD_TIMER  DSI_HOST_PHY_CFG2_MAX_RD_TIMER_Msk

/*************** Bit definition for DSI_HOST_PHY_CTRL register ****************/
#define DSI_HOST_PHY_CTRL_PHY_PD_N_Pos  (0U)
#define DSI_HOST_PHY_CTRL_PHY_PD_N_Msk  (0x1UL << DSI_HOST_PHY_CTRL_PHY_PD_N_Pos)
#define DSI_HOST_PHY_CTRL_PHY_PD_N      DSI_HOST_PHY_CTRL_PHY_PD_N_Msk
#define DSI_HOST_PHY_CTRL_PHY_RSTN_Pos  (1U)
#define DSI_HOST_PHY_CTRL_PHY_RSTN_Msk  (0x1UL << DSI_HOST_PHY_CTRL_PHY_RSTN_Pos)
#define DSI_HOST_PHY_CTRL_PHY_RSTN      DSI_HOST_PHY_CTRL_PHY_RSTN_Msk
#define DSI_HOST_PHY_CTRL_PLL_EN_Pos    (2U)
#define DSI_HOST_PHY_CTRL_PLL_EN_Msk    (0x1UL << DSI_HOST_PHY_CTRL_PLL_EN_Pos)
#define DSI_HOST_PHY_CTRL_PLL_EN        DSI_HOST_PHY_CTRL_PLL_EN_Msk
#define DSI_HOST_PHY_CTRL_CKLANE_ENABLE_Pos  (3U)
#define DSI_HOST_PHY_CTRL_CKLANE_ENABLE_Msk  (0x1UL << DSI_HOST_PHY_CTRL_CKLANE_ENABLE_Pos)
#define DSI_HOST_PHY_CTRL_CKLANE_ENABLE  DSI_HOST_PHY_CTRL_CKLANE_ENABLE_Msk
#define DSI_HOST_PHY_CTRL_CKLANE_AUTO_CTRL_Pos  (4U)
#define DSI_HOST_PHY_CTRL_CKLANE_AUTO_CTRL_Msk  (0x1UL << DSI_HOST_PHY_CTRL_CKLANE_AUTO_CTRL_Pos)
#define DSI_HOST_PHY_CTRL_CKLANE_AUTO_CTRL  DSI_HOST_PHY_CTRL_CKLANE_AUTO_CTRL_Msk
#define DSI_HOST_PHY_CTRL_CKLANE_HSTXREQ_Pos  (5U)
#define DSI_HOST_PHY_CTRL_CKLANE_HSTXREQ_Msk  (0x1UL << DSI_HOST_PHY_CTRL_CKLANE_HSTXREQ_Pos)
#define DSI_HOST_PHY_CTRL_CKLANE_HSTXREQ  DSI_HOST_PHY_CTRL_CKLANE_HSTXREQ_Msk
#define DSI_HOST_PHY_CTRL_CKLANE_REQULPS_Pos  (6U)
#define DSI_HOST_PHY_CTRL_CKLANE_REQULPS_Msk  (0x1UL << DSI_HOST_PHY_CTRL_CKLANE_REQULPS_Pos)
#define DSI_HOST_PHY_CTRL_CKLANE_REQULPS  DSI_HOST_PHY_CTRL_CKLANE_REQULPS_Msk
#define DSI_HOST_PHY_CTRL_CKLANE_EXITULPS_Pos  (7U)
#define DSI_HOST_PHY_CTRL_CKLANE_EXITULPS_Msk  (0x1UL << DSI_HOST_PHY_CTRL_CKLANE_EXITULPS_Pos)
#define DSI_HOST_PHY_CTRL_CKLANE_EXITULPS  DSI_HOST_PHY_CTRL_CKLANE_EXITULPS_Msk
#define DSI_HOST_PHY_CTRL_DLANE_REQULPS_Pos  (8U)
#define DSI_HOST_PHY_CTRL_DLANE_REQULPS_Msk  (0x1UL << DSI_HOST_PHY_CTRL_DLANE_REQULPS_Pos)
#define DSI_HOST_PHY_CTRL_DLANE_REQULPS  DSI_HOST_PHY_CTRL_DLANE_REQULPS_Msk
#define DSI_HOST_PHY_CTRL_DLANE_EXITULPS_Pos  (9U)
#define DSI_HOST_PHY_CTRL_DLANE_EXITULPS_Msk  (0x1UL << DSI_HOST_PHY_CTRL_DLANE_EXITULPS_Pos)
#define DSI_HOST_PHY_CTRL_DLANE_EXITULPS  DSI_HOST_PHY_CTRL_DLANE_EXITULPS_Msk
#define DSI_HOST_PHY_CTRL_TX_TRIGGERS_Pos  (10U)
#define DSI_HOST_PHY_CTRL_TX_TRIGGERS_Msk  (0xFUL << DSI_HOST_PHY_CTRL_TX_TRIGGERS_Pos)
#define DSI_HOST_PHY_CTRL_TX_TRIGGERS   DSI_HOST_PHY_CTRL_TX_TRIGGERS_Msk

/*************** Bit definition for DSI_HOST_PHY_STAT register ****************/
#define DSI_HOST_PHY_STAT_LOCK_Pos      (0U)
#define DSI_HOST_PHY_STAT_LOCK_Msk      (0x1UL << DSI_HOST_PHY_STAT_LOCK_Pos)
#define DSI_HOST_PHY_STAT_LOCK          DSI_HOST_PHY_STAT_LOCK_Msk
#define DSI_HOST_PHY_STAT_DIR_Pos       (1U)
#define DSI_HOST_PHY_STAT_DIR_Msk       (0x1UL << DSI_HOST_PHY_STAT_DIR_Pos)
#define DSI_HOST_PHY_STAT_DIR           DSI_HOST_PHY_STAT_DIR_Msk
#define DSI_HOST_PHY_STAT_CKLANE_STOP_Pos  (2U)
#define DSI_HOST_PHY_STAT_CKLANE_STOP_Msk  (0x1UL << DSI_HOST_PHY_STAT_CKLANE_STOP_Pos)
#define DSI_HOST_PHY_STAT_CKLANE_STOP   DSI_HOST_PHY_STAT_CKLANE_STOP_Msk
#define DSI_HOST_PHY_STAT_CKLANE_ULPS_N_Pos  (3U)
#define DSI_HOST_PHY_STAT_CKLANE_ULPS_N_Msk  (0x1UL << DSI_HOST_PHY_STAT_CKLANE_ULPS_N_Pos)
#define DSI_HOST_PHY_STAT_CKLANE_ULPS_N  DSI_HOST_PHY_STAT_CKLANE_ULPS_N_Msk
#define DSI_HOST_PHY_STAT_DLANE0_STOP_Pos  (4U)
#define DSI_HOST_PHY_STAT_DLANE0_STOP_Msk  (0x1UL << DSI_HOST_PHY_STAT_DLANE0_STOP_Pos)
#define DSI_HOST_PHY_STAT_DLANE0_STOP   DSI_HOST_PHY_STAT_DLANE0_STOP_Msk
#define DSI_HOST_PHY_STAT_DLANE0_ULPS_N_Pos  (5U)
#define DSI_HOST_PHY_STAT_DLANE0_ULPS_N_Msk  (0x1UL << DSI_HOST_PHY_STAT_DLANE0_ULPS_N_Pos)
#define DSI_HOST_PHY_STAT_DLANE0_ULPS_N  DSI_HOST_PHY_STAT_DLANE0_ULPS_N_Msk
#define DSI_HOST_PHY_STAT_DLANE0_RXULPS_ESC_Pos  (6U)
#define DSI_HOST_PHY_STAT_DLANE0_RXULPS_ESC_Msk  (0x1UL << DSI_HOST_PHY_STAT_DLANE0_RXULPS_ESC_Pos)
#define DSI_HOST_PHY_STAT_DLANE0_RXULPS_ESC  DSI_HOST_PHY_STAT_DLANE0_RXULPS_ESC_Msk
#define DSI_HOST_PHY_STAT_DLANE1_STOP_Pos  (7U)
#define DSI_HOST_PHY_STAT_DLANE1_STOP_Msk  (0x1UL << DSI_HOST_PHY_STAT_DLANE1_STOP_Pos)
#define DSI_HOST_PHY_STAT_DLANE1_STOP   DSI_HOST_PHY_STAT_DLANE1_STOP_Msk
#define DSI_HOST_PHY_STAT_DLANE1_ULPS_N_Pos  (8U)
#define DSI_HOST_PHY_STAT_DLANE1_ULPS_N_Msk  (0x1UL << DSI_HOST_PHY_STAT_DLANE1_ULPS_N_Pos)
#define DSI_HOST_PHY_STAT_DLANE1_ULPS_N  DSI_HOST_PHY_STAT_DLANE1_ULPS_N_Msk
#define DSI_HOST_PHY_STAT_UNDEF_Pos     (9U)
#define DSI_HOST_PHY_STAT_UNDEF_Msk     (0xFUL << DSI_HOST_PHY_STAT_UNDEF_Pos)
#define DSI_HOST_PHY_STAT_UNDEF         DSI_HOST_PHY_STAT_UNDEF_Msk

/****************** Bit definition for DSI_HOST_GP register *******************/
#define DSI_HOST_GP_GP_H_Pos            (0U)
#define DSI_HOST_GP_GP_H_Msk            (0x1UL << DSI_HOST_GP_GP_H_Pos)
#define DSI_HOST_GP_GP_H                DSI_HOST_GP_GP_H_Msk
#define DSI_HOST_GP_GP_L_Pos            (1U)
#define DSI_HOST_GP_GP_L_Msk            (0x3FFUL << DSI_HOST_GP_GP_L_Pos)
#define DSI_HOST_GP_GP_L                DSI_HOST_GP_GP_L_Msk
#define DSI_HOST_GP_GP_RD_Pos           (11U)
#define DSI_HOST_GP_GP_RD_Msk           (0xFFUL << DSI_HOST_GP_GP_RD_Pos)
#define DSI_HOST_GP_GP_RD               DSI_HOST_GP_GP_RD_Msk

/*************** Bit definition for DSI_HOST_INT_STAT1 register ***************/
#define DSI_HOST_INT_STAT1_SOT_Pos      (0U)
#define DSI_HOST_INT_STAT1_SOT_Msk      (0x1UL << DSI_HOST_INT_STAT1_SOT_Pos)
#define DSI_HOST_INT_STAT1_SOT          DSI_HOST_INT_STAT1_SOT_Msk
#define DSI_HOST_INT_STAT1_SOT_SYNC_Pos  (1U)
#define DSI_HOST_INT_STAT1_SOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_STAT1_SOT_SYNC_Pos)
#define DSI_HOST_INT_STAT1_SOT_SYNC     DSI_HOST_INT_STAT1_SOT_SYNC_Msk
#define DSI_HOST_INT_STAT1_EOT_SYNC_Pos  (2U)
#define DSI_HOST_INT_STAT1_EOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_STAT1_EOT_SYNC_Pos)
#define DSI_HOST_INT_STAT1_EOT_SYNC     DSI_HOST_INT_STAT1_EOT_SYNC_Msk
#define DSI_HOST_INT_STAT1_ESC_ENTRY_CMD_Pos  (3U)
#define DSI_HOST_INT_STAT1_ESC_ENTRY_CMD_Msk  (0x1UL << DSI_HOST_INT_STAT1_ESC_ENTRY_CMD_Pos)
#define DSI_HOST_INT_STAT1_ESC_ENTRY_CMD  DSI_HOST_INT_STAT1_ESC_ENTRY_CMD_Msk
#define DSI_HOST_INT_STAT1_LPTX_SYNC_Pos  (4U)
#define DSI_HOST_INT_STAT1_LPTX_SYNC_Msk  (0x1UL << DSI_HOST_INT_STAT1_LPTX_SYNC_Pos)
#define DSI_HOST_INT_STAT1_LPTX_SYNC    DSI_HOST_INT_STAT1_LPTX_SYNC_Msk
#define DSI_HOST_INT_STAT1_PERI_TIMEOUT_Pos  (5U)
#define DSI_HOST_INT_STAT1_PERI_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_STAT1_PERI_TIMEOUT_Pos)
#define DSI_HOST_INT_STAT1_PERI_TIMEOUT  DSI_HOST_INT_STAT1_PERI_TIMEOUT_Msk
#define DSI_HOST_INT_STAT1_FALSE_CTRL_Pos  (6U)
#define DSI_HOST_INT_STAT1_FALSE_CTRL_Msk  (0x1UL << DSI_HOST_INT_STAT1_FALSE_CTRL_Pos)
#define DSI_HOST_INT_STAT1_FALSE_CTRL   DSI_HOST_INT_STAT1_FALSE_CTRL_Msk
#define DSI_HOST_INT_STAT1_RSVD_INFO0_Pos  (7U)
#define DSI_HOST_INT_STAT1_RSVD_INFO0_Msk  (0x1UL << DSI_HOST_INT_STAT1_RSVD_INFO0_Pos)
#define DSI_HOST_INT_STAT1_RSVD_INFO0   DSI_HOST_INT_STAT1_RSVD_INFO0_Msk
#define DSI_HOST_INT_STAT1_SINGLE_ECC_Pos  (8U)
#define DSI_HOST_INT_STAT1_SINGLE_ECC_Msk  (0x1UL << DSI_HOST_INT_STAT1_SINGLE_ECC_Pos)
#define DSI_HOST_INT_STAT1_SINGLE_ECC   DSI_HOST_INT_STAT1_SINGLE_ECC_Msk
#define DSI_HOST_INT_STAT1_MULTI_ECC_Pos  (9U)
#define DSI_HOST_INT_STAT1_MULTI_ECC_Msk  (0x1UL << DSI_HOST_INT_STAT1_MULTI_ECC_Pos)
#define DSI_HOST_INT_STAT1_MULTI_ECC    DSI_HOST_INT_STAT1_MULTI_ECC_Msk
#define DSI_HOST_INT_STAT1_LONG_PKT_CHKSUM_Pos  (10U)
#define DSI_HOST_INT_STAT1_LONG_PKT_CHKSUM_Msk  (0x1UL << DSI_HOST_INT_STAT1_LONG_PKT_CHKSUM_Pos)
#define DSI_HOST_INT_STAT1_LONG_PKT_CHKSUM  DSI_HOST_INT_STAT1_LONG_PKT_CHKSUM_Msk
#define DSI_HOST_INT_STAT1_UNDEF_DTYPE_Pos  (11U)
#define DSI_HOST_INT_STAT1_UNDEF_DTYPE_Msk  (0x1UL << DSI_HOST_INT_STAT1_UNDEF_DTYPE_Pos)
#define DSI_HOST_INT_STAT1_UNDEF_DTYPE  DSI_HOST_INT_STAT1_UNDEF_DTYPE_Msk
#define DSI_HOST_INT_STAT1_INVALID_VCID_Pos  (12U)
#define DSI_HOST_INT_STAT1_INVALID_VCID_Msk  (0x1UL << DSI_HOST_INT_STAT1_INVALID_VCID_Pos)
#define DSI_HOST_INT_STAT1_INVALID_VCID  DSI_HOST_INT_STAT1_INVALID_VCID_Msk
#define DSI_HOST_INT_STAT1_INVALID_TRANS_LEN_Pos  (13U)
#define DSI_HOST_INT_STAT1_INVALID_TRANS_LEN_Msk  (0x1UL << DSI_HOST_INT_STAT1_INVALID_TRANS_LEN_Pos)
#define DSI_HOST_INT_STAT1_INVALID_TRANS_LEN  DSI_HOST_INT_STAT1_INVALID_TRANS_LEN_Msk
#define DSI_HOST_INT_STAT1_RSVD_INFO1_Pos  (14U)
#define DSI_HOST_INT_STAT1_RSVD_INFO1_Msk  (0x1UL << DSI_HOST_INT_STAT1_RSVD_INFO1_Pos)
#define DSI_HOST_INT_STAT1_RSVD_INFO1   DSI_HOST_INT_STAT1_RSVD_INFO1_Msk
#define DSI_HOST_INT_STAT1_DSI_PROTOCOL_VIO_Pos  (15U)
#define DSI_HOST_INT_STAT1_DSI_PROTOCOL_VIO_Msk  (0x1UL << DSI_HOST_INT_STAT1_DSI_PROTOCOL_VIO_Pos)
#define DSI_HOST_INT_STAT1_DSI_PROTOCOL_VIO  DSI_HOST_INT_STAT1_DSI_PROTOCOL_VIO_Msk
#define DSI_HOST_INT_STAT1_D0_ERRESC_Pos  (16U)
#define DSI_HOST_INT_STAT1_D0_ERRESC_Msk  (0x1UL << DSI_HOST_INT_STAT1_D0_ERRESC_Pos)
#define DSI_HOST_INT_STAT1_D0_ERRESC    DSI_HOST_INT_STAT1_D0_ERRESC_Msk
#define DSI_HOST_INT_STAT1_D0_ERRSYNCESC_Pos  (17U)
#define DSI_HOST_INT_STAT1_D0_ERRSYNCESC_Msk  (0x1UL << DSI_HOST_INT_STAT1_D0_ERRSYNCESC_Pos)
#define DSI_HOST_INT_STAT1_D0_ERRSYNCESC  DSI_HOST_INT_STAT1_D0_ERRSYNCESC_Msk
#define DSI_HOST_INT_STAT1_D0_ERRCTRL_Pos  (18U)
#define DSI_HOST_INT_STAT1_D0_ERRCTRL_Msk  (0x1UL << DSI_HOST_INT_STAT1_D0_ERRCTRL_Pos)
#define DSI_HOST_INT_STAT1_D0_ERRCTRL   DSI_HOST_INT_STAT1_D0_ERRCTRL_Msk
#define DSI_HOST_INT_STAT1_D0_LP0_CONT_Pos  (19U)
#define DSI_HOST_INT_STAT1_D0_LP0_CONT_Msk  (0x1UL << DSI_HOST_INT_STAT1_D0_LP0_CONT_Pos)
#define DSI_HOST_INT_STAT1_D0_LP0_CONT  DSI_HOST_INT_STAT1_D0_LP0_CONT_Msk
#define DSI_HOST_INT_STAT1_D0_LP1_CONT_Pos  (20U)
#define DSI_HOST_INT_STAT1_D0_LP1_CONT_Msk  (0x1UL << DSI_HOST_INT_STAT1_D0_LP1_CONT_Pos)
#define DSI_HOST_INT_STAT1_D0_LP1_CONT  DSI_HOST_INT_STAT1_D0_LP1_CONT_Msk

/*************** Bit definition for DSI_HOST_INT_STAT2 register ***************/
#define DSI_HOST_INT_STAT2_HSTX_TIMEOUT_Pos  (0U)
#define DSI_HOST_INT_STAT2_HSTX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_STAT2_HSTX_TIMEOUT_Pos)
#define DSI_HOST_INT_STAT2_HSTX_TIMEOUT  DSI_HOST_INT_STAT2_HSTX_TIMEOUT_Msk
#define DSI_HOST_INT_STAT2_LPRX_TIMEOUT_Pos  (1U)
#define DSI_HOST_INT_STAT2_LPRX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_STAT2_LPRX_TIMEOUT_Pos)
#define DSI_HOST_INT_STAT2_LPRX_TIMEOUT  DSI_HOST_INT_STAT2_LPRX_TIMEOUT_Msk
#define DSI_HOST_INT_STAT2_ECC_SINGLE_Pos  (2U)
#define DSI_HOST_INT_STAT2_ECC_SINGLE_Msk  (0x1UL << DSI_HOST_INT_STAT2_ECC_SINGLE_Pos)
#define DSI_HOST_INT_STAT2_ECC_SINGLE   DSI_HOST_INT_STAT2_ECC_SINGLE_Msk
#define DSI_HOST_INT_STAT2_ECC_MULTI_Pos  (3U)
#define DSI_HOST_INT_STAT2_ECC_MULTI_Msk  (0x1UL << DSI_HOST_INT_STAT2_ECC_MULTI_Pos)
#define DSI_HOST_INT_STAT2_ECC_MULTI    DSI_HOST_INT_STAT2_ECC_MULTI_Msk
#define DSI_HOST_INT_STAT2_CRC_Pos      (4U)
#define DSI_HOST_INT_STAT2_CRC_Msk      (0x1UL << DSI_HOST_INT_STAT2_CRC_Pos)
#define DSI_HOST_INT_STAT2_CRC          DSI_HOST_INT_STAT2_CRC_Msk
#define DSI_HOST_INT_STAT2_PACKET_SIZE_Pos  (5U)
#define DSI_HOST_INT_STAT2_PACKET_SIZE_Msk  (0x1UL << DSI_HOST_INT_STAT2_PACKET_SIZE_Pos)
#define DSI_HOST_INT_STAT2_PACKET_SIZE  DSI_HOST_INT_STAT2_PACKET_SIZE_Msk
#define DSI_HOST_INT_STAT2_EOTP_Pos     (6U)
#define DSI_HOST_INT_STAT2_EOTP_Msk     (0x1UL << DSI_HOST_INT_STAT2_EOTP_Pos)
#define DSI_HOST_INT_STAT2_EOTP         DSI_HOST_INT_STAT2_EOTP_Msk
#define DSI_HOST_INT_STAT2_DPI_PAYLOAD_WR_Pos  (7U)
#define DSI_HOST_INT_STAT2_DPI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_STAT2_DPI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_STAT2_DPI_PAYLOAD_WR  DSI_HOST_INT_STAT2_DPI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_STAT2_COM_CMD_WR_Pos  (8U)
#define DSI_HOST_INT_STAT2_COM_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_STAT2_COM_CMD_WR_Pos)
#define DSI_HOST_INT_STAT2_COM_CMD_WR   DSI_HOST_INT_STAT2_COM_CMD_WR_Msk
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_WR_Pos  (9U)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_STAT2_COM_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_WR  DSI_HOST_INT_STAT2_COM_PAYLOAD_WR_Msk
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_SEND_Pos  (10U)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_SEND_Msk  (0x1UL << DSI_HOST_INT_STAT2_COM_PAYLOAD_SEND_Pos)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_SEND  DSI_HOST_INT_STAT2_COM_PAYLOAD_SEND_Msk
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RD_Pos  (11U)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_STAT2_COM_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RD  DSI_HOST_INT_STAT2_COM_PAYLOAD_RD_Msk
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RCV_Pos  (12U)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_STAT2_COM_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_STAT2_COM_PAYLOAD_RCV  DSI_HOST_INT_STAT2_COM_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_STAT2_DBI_CMD_WR_Pos  (13U)
#define DSI_HOST_INT_STAT2_DBI_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_STAT2_DBI_CMD_WR_Pos)
#define DSI_HOST_INT_STAT2_DBI_CMD_WR   DSI_HOST_INT_STAT2_DBI_CMD_WR_Msk
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_WR_Pos  (14U)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_STAT2_DBI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_WR  DSI_HOST_INT_STAT2_DBI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RD_Pos  (15U)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_STAT2_DBI_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RD  DSI_HOST_INT_STAT2_DBI_PAYLOAD_RD_Msk
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RCV_Pos  (16U)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_STAT2_DBI_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_STAT2_DBI_PAYLOAD_RCV  DSI_HOST_INT_STAT2_DBI_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_STAT2_DBI_INVALID_CMD_Pos  (17U)
#define DSI_HOST_INT_STAT2_DBI_INVALID_CMD_Msk  (0x1UL << DSI_HOST_INT_STAT2_DBI_INVALID_CMD_Pos)
#define DSI_HOST_INT_STAT2_DBI_INVALID_CMD  DSI_HOST_INT_STAT2_DBI_INVALID_CMD_Msk

/*************** Bit definition for DSI_HOST_INT_MASK1 register ***************/
#define DSI_HOST_INT_MASK1_SOT_Pos      (0U)
#define DSI_HOST_INT_MASK1_SOT_Msk      (0x1UL << DSI_HOST_INT_MASK1_SOT_Pos)
#define DSI_HOST_INT_MASK1_SOT          DSI_HOST_INT_MASK1_SOT_Msk
#define DSI_HOST_INT_MASK1_SOT_SYNC_Pos  (1U)
#define DSI_HOST_INT_MASK1_SOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_MASK1_SOT_SYNC_Pos)
#define DSI_HOST_INT_MASK1_SOT_SYNC     DSI_HOST_INT_MASK1_SOT_SYNC_Msk
#define DSI_HOST_INT_MASK1_EOT_SYNC_Pos  (2U)
#define DSI_HOST_INT_MASK1_EOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_MASK1_EOT_SYNC_Pos)
#define DSI_HOST_INT_MASK1_EOT_SYNC     DSI_HOST_INT_MASK1_EOT_SYNC_Msk
#define DSI_HOST_INT_MASK1_ESC_ENTRY_CMD_Pos  (3U)
#define DSI_HOST_INT_MASK1_ESC_ENTRY_CMD_Msk  (0x1UL << DSI_HOST_INT_MASK1_ESC_ENTRY_CMD_Pos)
#define DSI_HOST_INT_MASK1_ESC_ENTRY_CMD  DSI_HOST_INT_MASK1_ESC_ENTRY_CMD_Msk
#define DSI_HOST_INT_MASK1_LPTX_SYNC_Pos  (4U)
#define DSI_HOST_INT_MASK1_LPTX_SYNC_Msk  (0x1UL << DSI_HOST_INT_MASK1_LPTX_SYNC_Pos)
#define DSI_HOST_INT_MASK1_LPTX_SYNC    DSI_HOST_INT_MASK1_LPTX_SYNC_Msk
#define DSI_HOST_INT_MASK1_PERI_TIMEOUT_Pos  (5U)
#define DSI_HOST_INT_MASK1_PERI_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_MASK1_PERI_TIMEOUT_Pos)
#define DSI_HOST_INT_MASK1_PERI_TIMEOUT  DSI_HOST_INT_MASK1_PERI_TIMEOUT_Msk
#define DSI_HOST_INT_MASK1_FALSE_CTRL_Pos  (6U)
#define DSI_HOST_INT_MASK1_FALSE_CTRL_Msk  (0x1UL << DSI_HOST_INT_MASK1_FALSE_CTRL_Pos)
#define DSI_HOST_INT_MASK1_FALSE_CTRL   DSI_HOST_INT_MASK1_FALSE_CTRL_Msk
#define DSI_HOST_INT_MASK1_RSVD_INFO0_Pos  (7U)
#define DSI_HOST_INT_MASK1_RSVD_INFO0_Msk  (0x1UL << DSI_HOST_INT_MASK1_RSVD_INFO0_Pos)
#define DSI_HOST_INT_MASK1_RSVD_INFO0   DSI_HOST_INT_MASK1_RSVD_INFO0_Msk
#define DSI_HOST_INT_MASK1_SINGLE_ECC_Pos  (8U)
#define DSI_HOST_INT_MASK1_SINGLE_ECC_Msk  (0x1UL << DSI_HOST_INT_MASK1_SINGLE_ECC_Pos)
#define DSI_HOST_INT_MASK1_SINGLE_ECC   DSI_HOST_INT_MASK1_SINGLE_ECC_Msk
#define DSI_HOST_INT_MASK1_MULTI_ECC_Pos  (9U)
#define DSI_HOST_INT_MASK1_MULTI_ECC_Msk  (0x1UL << DSI_HOST_INT_MASK1_MULTI_ECC_Pos)
#define DSI_HOST_INT_MASK1_MULTI_ECC    DSI_HOST_INT_MASK1_MULTI_ECC_Msk
#define DSI_HOST_INT_MASK1_LONG_PKT_CHKSUM_Pos  (10U)
#define DSI_HOST_INT_MASK1_LONG_PKT_CHKSUM_Msk  (0x1UL << DSI_HOST_INT_MASK1_LONG_PKT_CHKSUM_Pos)
#define DSI_HOST_INT_MASK1_LONG_PKT_CHKSUM  DSI_HOST_INT_MASK1_LONG_PKT_CHKSUM_Msk
#define DSI_HOST_INT_MASK1_UNDEF_DTYPE_Pos  (11U)
#define DSI_HOST_INT_MASK1_UNDEF_DTYPE_Msk  (0x1UL << DSI_HOST_INT_MASK1_UNDEF_DTYPE_Pos)
#define DSI_HOST_INT_MASK1_UNDEF_DTYPE  DSI_HOST_INT_MASK1_UNDEF_DTYPE_Msk
#define DSI_HOST_INT_MASK1_INVALID_VCID_Pos  (12U)
#define DSI_HOST_INT_MASK1_INVALID_VCID_Msk  (0x1UL << DSI_HOST_INT_MASK1_INVALID_VCID_Pos)
#define DSI_HOST_INT_MASK1_INVALID_VCID  DSI_HOST_INT_MASK1_INVALID_VCID_Msk
#define DSI_HOST_INT_MASK1_INVALID_TRANS_LEN_Pos  (13U)
#define DSI_HOST_INT_MASK1_INVALID_TRANS_LEN_Msk  (0x1UL << DSI_HOST_INT_MASK1_INVALID_TRANS_LEN_Pos)
#define DSI_HOST_INT_MASK1_INVALID_TRANS_LEN  DSI_HOST_INT_MASK1_INVALID_TRANS_LEN_Msk
#define DSI_HOST_INT_MASK1_RSVD_INFO1_Pos  (14U)
#define DSI_HOST_INT_MASK1_RSVD_INFO1_Msk  (0x1UL << DSI_HOST_INT_MASK1_RSVD_INFO1_Pos)
#define DSI_HOST_INT_MASK1_RSVD_INFO1   DSI_HOST_INT_MASK1_RSVD_INFO1_Msk
#define DSI_HOST_INT_MASK1_DSI_PROTOCOL_VIO_Pos  (15U)
#define DSI_HOST_INT_MASK1_DSI_PROTOCOL_VIO_Msk  (0x1UL << DSI_HOST_INT_MASK1_DSI_PROTOCOL_VIO_Pos)
#define DSI_HOST_INT_MASK1_DSI_PROTOCOL_VIO  DSI_HOST_INT_MASK1_DSI_PROTOCOL_VIO_Msk
#define DSI_HOST_INT_MASK1_D0_ERRESC_Pos  (16U)
#define DSI_HOST_INT_MASK1_D0_ERRESC_Msk  (0x1UL << DSI_HOST_INT_MASK1_D0_ERRESC_Pos)
#define DSI_HOST_INT_MASK1_D0_ERRESC    DSI_HOST_INT_MASK1_D0_ERRESC_Msk
#define DSI_HOST_INT_MASK1_D0_ERRSYNCESC_Pos  (17U)
#define DSI_HOST_INT_MASK1_D0_ERRSYNCESC_Msk  (0x1UL << DSI_HOST_INT_MASK1_D0_ERRSYNCESC_Pos)
#define DSI_HOST_INT_MASK1_D0_ERRSYNCESC  DSI_HOST_INT_MASK1_D0_ERRSYNCESC_Msk
#define DSI_HOST_INT_MASK1_D0_ERRCTRL_Pos  (18U)
#define DSI_HOST_INT_MASK1_D0_ERRCTRL_Msk  (0x1UL << DSI_HOST_INT_MASK1_D0_ERRCTRL_Pos)
#define DSI_HOST_INT_MASK1_D0_ERRCTRL   DSI_HOST_INT_MASK1_D0_ERRCTRL_Msk
#define DSI_HOST_INT_MASK1_D0_LP0_CONT_Pos  (19U)
#define DSI_HOST_INT_MASK1_D0_LP0_CONT_Msk  (0x1UL << DSI_HOST_INT_MASK1_D0_LP0_CONT_Pos)
#define DSI_HOST_INT_MASK1_D0_LP0_CONT  DSI_HOST_INT_MASK1_D0_LP0_CONT_Msk
#define DSI_HOST_INT_MASK1_D0_LP1_CONT_Pos  (20U)
#define DSI_HOST_INT_MASK1_D0_LP1_CONT_Msk  (0x1UL << DSI_HOST_INT_MASK1_D0_LP1_CONT_Pos)
#define DSI_HOST_INT_MASK1_D0_LP1_CONT  DSI_HOST_INT_MASK1_D0_LP1_CONT_Msk

/*************** Bit definition for DSI_HOST_INT_MASK2 register ***************/
#define DSI_HOST_INT_MASK2_HSTX_TIMEOUT_Pos  (0U)
#define DSI_HOST_INT_MASK2_HSTX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_MASK2_HSTX_TIMEOUT_Pos)
#define DSI_HOST_INT_MASK2_HSTX_TIMEOUT  DSI_HOST_INT_MASK2_HSTX_TIMEOUT_Msk
#define DSI_HOST_INT_MASK2_LPRX_TIMEOUT_Pos  (1U)
#define DSI_HOST_INT_MASK2_LPRX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_MASK2_LPRX_TIMEOUT_Pos)
#define DSI_HOST_INT_MASK2_LPRX_TIMEOUT  DSI_HOST_INT_MASK2_LPRX_TIMEOUT_Msk
#define DSI_HOST_INT_MASK2_ECC_SINGLE_Pos  (2U)
#define DSI_HOST_INT_MASK2_ECC_SINGLE_Msk  (0x1UL << DSI_HOST_INT_MASK2_ECC_SINGLE_Pos)
#define DSI_HOST_INT_MASK2_ECC_SINGLE   DSI_HOST_INT_MASK2_ECC_SINGLE_Msk
#define DSI_HOST_INT_MASK2_ECC_MULTI_Pos  (3U)
#define DSI_HOST_INT_MASK2_ECC_MULTI_Msk  (0x1UL << DSI_HOST_INT_MASK2_ECC_MULTI_Pos)
#define DSI_HOST_INT_MASK2_ECC_MULTI    DSI_HOST_INT_MASK2_ECC_MULTI_Msk
#define DSI_HOST_INT_MASK2_CRC_Pos      (4U)
#define DSI_HOST_INT_MASK2_CRC_Msk      (0x1UL << DSI_HOST_INT_MASK2_CRC_Pos)
#define DSI_HOST_INT_MASK2_CRC          DSI_HOST_INT_MASK2_CRC_Msk
#define DSI_HOST_INT_MASK2_PACKET_SIZE_Pos  (5U)
#define DSI_HOST_INT_MASK2_PACKET_SIZE_Msk  (0x1UL << DSI_HOST_INT_MASK2_PACKET_SIZE_Pos)
#define DSI_HOST_INT_MASK2_PACKET_SIZE  DSI_HOST_INT_MASK2_PACKET_SIZE_Msk
#define DSI_HOST_INT_MASK2_EOTP_Pos     (6U)
#define DSI_HOST_INT_MASK2_EOTP_Msk     (0x1UL << DSI_HOST_INT_MASK2_EOTP_Pos)
#define DSI_HOST_INT_MASK2_EOTP         DSI_HOST_INT_MASK2_EOTP_Msk
#define DSI_HOST_INT_MASK2_DPI_PAYLOAD_WR_Pos  (7U)
#define DSI_HOST_INT_MASK2_DPI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_MASK2_DPI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_MASK2_DPI_PAYLOAD_WR  DSI_HOST_INT_MASK2_DPI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_MASK2_COM_CMD_WR_Pos  (8U)
#define DSI_HOST_INT_MASK2_COM_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_MASK2_COM_CMD_WR_Pos)
#define DSI_HOST_INT_MASK2_COM_CMD_WR   DSI_HOST_INT_MASK2_COM_CMD_WR_Msk
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_WR_Pos  (9U)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_MASK2_COM_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_WR  DSI_HOST_INT_MASK2_COM_PAYLOAD_WR_Msk
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_SEND_Pos  (10U)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_SEND_Msk  (0x1UL << DSI_HOST_INT_MASK2_COM_PAYLOAD_SEND_Pos)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_SEND  DSI_HOST_INT_MASK2_COM_PAYLOAD_SEND_Msk
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RD_Pos  (11U)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_MASK2_COM_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RD  DSI_HOST_INT_MASK2_COM_PAYLOAD_RD_Msk
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RCV_Pos  (12U)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_MASK2_COM_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_MASK2_COM_PAYLOAD_RCV  DSI_HOST_INT_MASK2_COM_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_MASK2_DBI_CMD_WR_Pos  (13U)
#define DSI_HOST_INT_MASK2_DBI_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_MASK2_DBI_CMD_WR_Pos)
#define DSI_HOST_INT_MASK2_DBI_CMD_WR   DSI_HOST_INT_MASK2_DBI_CMD_WR_Msk
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_WR_Pos  (14U)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_MASK2_DBI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_WR  DSI_HOST_INT_MASK2_DBI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RD_Pos  (15U)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_MASK2_DBI_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RD  DSI_HOST_INT_MASK2_DBI_PAYLOAD_RD_Msk
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RCV_Pos  (16U)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_MASK2_DBI_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_MASK2_DBI_PAYLOAD_RCV  DSI_HOST_INT_MASK2_DBI_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_MASK2_DBI_INVALID_CMD_Pos  (17U)
#define DSI_HOST_INT_MASK2_DBI_INVALID_CMD_Msk  (0x1UL << DSI_HOST_INT_MASK2_DBI_INVALID_CMD_Pos)
#define DSI_HOST_INT_MASK2_DBI_INVALID_CMD  DSI_HOST_INT_MASK2_DBI_INVALID_CMD_Msk

/************** Bit definition for DSI_HOST_INT_FORCE1 register ***************/
#define DSI_HOST_INT_FORCE1_SOT_Pos     (0U)
#define DSI_HOST_INT_FORCE1_SOT_Msk     (0x1UL << DSI_HOST_INT_FORCE1_SOT_Pos)
#define DSI_HOST_INT_FORCE1_SOT         DSI_HOST_INT_FORCE1_SOT_Msk
#define DSI_HOST_INT_FORCE1_SOT_SYNC_Pos  (1U)
#define DSI_HOST_INT_FORCE1_SOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_SOT_SYNC_Pos)
#define DSI_HOST_INT_FORCE1_SOT_SYNC    DSI_HOST_INT_FORCE1_SOT_SYNC_Msk
#define DSI_HOST_INT_FORCE1_EOT_SYNC_Pos  (2U)
#define DSI_HOST_INT_FORCE1_EOT_SYNC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_EOT_SYNC_Pos)
#define DSI_HOST_INT_FORCE1_EOT_SYNC    DSI_HOST_INT_FORCE1_EOT_SYNC_Msk
#define DSI_HOST_INT_FORCE1_ESC_ENTRY_CMD_Pos  (3U)
#define DSI_HOST_INT_FORCE1_ESC_ENTRY_CMD_Msk  (0x1UL << DSI_HOST_INT_FORCE1_ESC_ENTRY_CMD_Pos)
#define DSI_HOST_INT_FORCE1_ESC_ENTRY_CMD  DSI_HOST_INT_FORCE1_ESC_ENTRY_CMD_Msk
#define DSI_HOST_INT_FORCE1_LPTX_SYNC_Pos  (4U)
#define DSI_HOST_INT_FORCE1_LPTX_SYNC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_LPTX_SYNC_Pos)
#define DSI_HOST_INT_FORCE1_LPTX_SYNC   DSI_HOST_INT_FORCE1_LPTX_SYNC_Msk
#define DSI_HOST_INT_FORCE1_PERI_TIMEOUT_Pos  (5U)
#define DSI_HOST_INT_FORCE1_PERI_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_FORCE1_PERI_TIMEOUT_Pos)
#define DSI_HOST_INT_FORCE1_PERI_TIMEOUT  DSI_HOST_INT_FORCE1_PERI_TIMEOUT_Msk
#define DSI_HOST_INT_FORCE1_FALSE_CTRL_Pos  (6U)
#define DSI_HOST_INT_FORCE1_FALSE_CTRL_Msk  (0x1UL << DSI_HOST_INT_FORCE1_FALSE_CTRL_Pos)
#define DSI_HOST_INT_FORCE1_FALSE_CTRL  DSI_HOST_INT_FORCE1_FALSE_CTRL_Msk
#define DSI_HOST_INT_FORCE1_RSVD_INFO0_Pos  (7U)
#define DSI_HOST_INT_FORCE1_RSVD_INFO0_Msk  (0x1UL << DSI_HOST_INT_FORCE1_RSVD_INFO0_Pos)
#define DSI_HOST_INT_FORCE1_RSVD_INFO0  DSI_HOST_INT_FORCE1_RSVD_INFO0_Msk
#define DSI_HOST_INT_FORCE1_SINGLE_ECC_Pos  (8U)
#define DSI_HOST_INT_FORCE1_SINGLE_ECC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_SINGLE_ECC_Pos)
#define DSI_HOST_INT_FORCE1_SINGLE_ECC  DSI_HOST_INT_FORCE1_SINGLE_ECC_Msk
#define DSI_HOST_INT_FORCE1_MULTI_ECC_Pos  (9U)
#define DSI_HOST_INT_FORCE1_MULTI_ECC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_MULTI_ECC_Pos)
#define DSI_HOST_INT_FORCE1_MULTI_ECC   DSI_HOST_INT_FORCE1_MULTI_ECC_Msk
#define DSI_HOST_INT_FORCE1_LONG_PKT_CHKSUM_Pos  (10U)
#define DSI_HOST_INT_FORCE1_LONG_PKT_CHKSUM_Msk  (0x1UL << DSI_HOST_INT_FORCE1_LONG_PKT_CHKSUM_Pos)
#define DSI_HOST_INT_FORCE1_LONG_PKT_CHKSUM  DSI_HOST_INT_FORCE1_LONG_PKT_CHKSUM_Msk
#define DSI_HOST_INT_FORCE1_UNDEF_DTYPE_Pos  (11U)
#define DSI_HOST_INT_FORCE1_UNDEF_DTYPE_Msk  (0x1UL << DSI_HOST_INT_FORCE1_UNDEF_DTYPE_Pos)
#define DSI_HOST_INT_FORCE1_UNDEF_DTYPE  DSI_HOST_INT_FORCE1_UNDEF_DTYPE_Msk
#define DSI_HOST_INT_FORCE1_INVALID_VCID_Pos  (12U)
#define DSI_HOST_INT_FORCE1_INVALID_VCID_Msk  (0x1UL << DSI_HOST_INT_FORCE1_INVALID_VCID_Pos)
#define DSI_HOST_INT_FORCE1_INVALID_VCID  DSI_HOST_INT_FORCE1_INVALID_VCID_Msk
#define DSI_HOST_INT_FORCE1_INVALID_TRANS_LEN_Pos  (13U)
#define DSI_HOST_INT_FORCE1_INVALID_TRANS_LEN_Msk  (0x1UL << DSI_HOST_INT_FORCE1_INVALID_TRANS_LEN_Pos)
#define DSI_HOST_INT_FORCE1_INVALID_TRANS_LEN  DSI_HOST_INT_FORCE1_INVALID_TRANS_LEN_Msk
#define DSI_HOST_INT_FORCE1_RSVD_INFO1_Pos  (14U)
#define DSI_HOST_INT_FORCE1_RSVD_INFO1_Msk  (0x1UL << DSI_HOST_INT_FORCE1_RSVD_INFO1_Pos)
#define DSI_HOST_INT_FORCE1_RSVD_INFO1  DSI_HOST_INT_FORCE1_RSVD_INFO1_Msk
#define DSI_HOST_INT_FORCE1_DSI_PROTOCOL_VIO_Pos  (15U)
#define DSI_HOST_INT_FORCE1_DSI_PROTOCOL_VIO_Msk  (0x1UL << DSI_HOST_INT_FORCE1_DSI_PROTOCOL_VIO_Pos)
#define DSI_HOST_INT_FORCE1_DSI_PROTOCOL_VIO  DSI_HOST_INT_FORCE1_DSI_PROTOCOL_VIO_Msk
#define DSI_HOST_INT_FORCE1_D0_ERRESC_Pos  (16U)
#define DSI_HOST_INT_FORCE1_D0_ERRESC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_D0_ERRESC_Pos)
#define DSI_HOST_INT_FORCE1_D0_ERRESC   DSI_HOST_INT_FORCE1_D0_ERRESC_Msk
#define DSI_HOST_INT_FORCE1_D0_ERRSYNCESC_Pos  (17U)
#define DSI_HOST_INT_FORCE1_D0_ERRSYNCESC_Msk  (0x1UL << DSI_HOST_INT_FORCE1_D0_ERRSYNCESC_Pos)
#define DSI_HOST_INT_FORCE1_D0_ERRSYNCESC  DSI_HOST_INT_FORCE1_D0_ERRSYNCESC_Msk
#define DSI_HOST_INT_FORCE1_D0_ERRCTRL_Pos  (18U)
#define DSI_HOST_INT_FORCE1_D0_ERRCTRL_Msk  (0x1UL << DSI_HOST_INT_FORCE1_D0_ERRCTRL_Pos)
#define DSI_HOST_INT_FORCE1_D0_ERRCTRL  DSI_HOST_INT_FORCE1_D0_ERRCTRL_Msk
#define DSI_HOST_INT_FORCE1_D0_LP0_CONT_Pos  (19U)
#define DSI_HOST_INT_FORCE1_D0_LP0_CONT_Msk  (0x1UL << DSI_HOST_INT_FORCE1_D0_LP0_CONT_Pos)
#define DSI_HOST_INT_FORCE1_D0_LP0_CONT  DSI_HOST_INT_FORCE1_D0_LP0_CONT_Msk
#define DSI_HOST_INT_FORCE1_D0_LP1_CONT_Pos  (20U)
#define DSI_HOST_INT_FORCE1_D0_LP1_CONT_Msk  (0x1UL << DSI_HOST_INT_FORCE1_D0_LP1_CONT_Pos)
#define DSI_HOST_INT_FORCE1_D0_LP1_CONT  DSI_HOST_INT_FORCE1_D0_LP1_CONT_Msk

/************** Bit definition for DSI_HOST_INT_FORCE2 register ***************/
#define DSI_HOST_INT_FORCE2_HSTX_TIMEOUT_Pos  (0U)
#define DSI_HOST_INT_FORCE2_HSTX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_FORCE2_HSTX_TIMEOUT_Pos)
#define DSI_HOST_INT_FORCE2_HSTX_TIMEOUT  DSI_HOST_INT_FORCE2_HSTX_TIMEOUT_Msk
#define DSI_HOST_INT_FORCE2_LPRX_TIMEOUT_Pos  (1U)
#define DSI_HOST_INT_FORCE2_LPRX_TIMEOUT_Msk  (0x1UL << DSI_HOST_INT_FORCE2_LPRX_TIMEOUT_Pos)
#define DSI_HOST_INT_FORCE2_LPRX_TIMEOUT  DSI_HOST_INT_FORCE2_LPRX_TIMEOUT_Msk
#define DSI_HOST_INT_FORCE2_ECC_SINGLE_Pos  (2U)
#define DSI_HOST_INT_FORCE2_ECC_SINGLE_Msk  (0x1UL << DSI_HOST_INT_FORCE2_ECC_SINGLE_Pos)
#define DSI_HOST_INT_FORCE2_ECC_SINGLE  DSI_HOST_INT_FORCE2_ECC_SINGLE_Msk
#define DSI_HOST_INT_FORCE2_ECC_MULTI_Pos  (3U)
#define DSI_HOST_INT_FORCE2_ECC_MULTI_Msk  (0x1UL << DSI_HOST_INT_FORCE2_ECC_MULTI_Pos)
#define DSI_HOST_INT_FORCE2_ECC_MULTI   DSI_HOST_INT_FORCE2_ECC_MULTI_Msk
#define DSI_HOST_INT_FORCE2_CRC_Pos     (4U)
#define DSI_HOST_INT_FORCE2_CRC_Msk     (0x1UL << DSI_HOST_INT_FORCE2_CRC_Pos)
#define DSI_HOST_INT_FORCE2_CRC         DSI_HOST_INT_FORCE2_CRC_Msk
#define DSI_HOST_INT_FORCE2_PACKET_SIZE_Pos  (5U)
#define DSI_HOST_INT_FORCE2_PACKET_SIZE_Msk  (0x1UL << DSI_HOST_INT_FORCE2_PACKET_SIZE_Pos)
#define DSI_HOST_INT_FORCE2_PACKET_SIZE  DSI_HOST_INT_FORCE2_PACKET_SIZE_Msk
#define DSI_HOST_INT_FORCE2_EOTP_Pos    (6U)
#define DSI_HOST_INT_FORCE2_EOTP_Msk    (0x1UL << DSI_HOST_INT_FORCE2_EOTP_Pos)
#define DSI_HOST_INT_FORCE2_EOTP        DSI_HOST_INT_FORCE2_EOTP_Msk
#define DSI_HOST_INT_FORCE2_DPI_PAYLOAD_WR_Pos  (7U)
#define DSI_HOST_INT_FORCE2_DPI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DPI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_FORCE2_DPI_PAYLOAD_WR  DSI_HOST_INT_FORCE2_DPI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_FORCE2_COM_CMD_WR_Pos  (8U)
#define DSI_HOST_INT_FORCE2_COM_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_FORCE2_COM_CMD_WR_Pos)
#define DSI_HOST_INT_FORCE2_COM_CMD_WR  DSI_HOST_INT_FORCE2_COM_CMD_WR_Msk
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_WR_Pos  (9U)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_FORCE2_COM_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_WR  DSI_HOST_INT_FORCE2_COM_PAYLOAD_WR_Msk
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_SEND_Pos  (10U)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_SEND_Msk  (0x1UL << DSI_HOST_INT_FORCE2_COM_PAYLOAD_SEND_Pos)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_SEND  DSI_HOST_INT_FORCE2_COM_PAYLOAD_SEND_Msk
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RD_Pos  (11U)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_FORCE2_COM_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RD  DSI_HOST_INT_FORCE2_COM_PAYLOAD_RD_Msk
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RCV_Pos  (12U)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_FORCE2_COM_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_FORCE2_COM_PAYLOAD_RCV  DSI_HOST_INT_FORCE2_COM_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_FORCE2_DBI_CMD_WR_Pos  (13U)
#define DSI_HOST_INT_FORCE2_DBI_CMD_WR_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DBI_CMD_WR_Pos)
#define DSI_HOST_INT_FORCE2_DBI_CMD_WR  DSI_HOST_INT_FORCE2_DBI_CMD_WR_Msk
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_WR_Pos  (14U)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_WR_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DBI_PAYLOAD_WR_Pos)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_WR  DSI_HOST_INT_FORCE2_DBI_PAYLOAD_WR_Msk
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RD_Pos  (15U)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RD_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RD_Pos)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RD  DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RD_Msk
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RCV_Pos  (16U)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RCV_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RCV_Pos)
#define DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RCV  DSI_HOST_INT_FORCE2_DBI_PAYLOAD_RCV_Msk
#define DSI_HOST_INT_FORCE2_DBI_INVALID_CMD_Pos  (17U)
#define DSI_HOST_INT_FORCE2_DBI_INVALID_CMD_Msk  (0x1UL << DSI_HOST_INT_FORCE2_DBI_INVALID_CMD_Pos)
#define DSI_HOST_INT_FORCE2_DBI_INVALID_CMD  DSI_HOST_INT_FORCE2_DBI_INVALID_CMD_Msk

/*************** Bit definition for DSI_HOST_VAUX_CTRL register ***************/
#define DSI_HOST_VAUX_CTRL_AUX_LOAD_Pos  (0U)
#define DSI_HOST_VAUX_CTRL_AUX_LOAD_Msk  (0x1UL << DSI_HOST_VAUX_CTRL_AUX_LOAD_Pos)
#define DSI_HOST_VAUX_CTRL_AUX_LOAD     DSI_HOST_VAUX_CTRL_AUX_LOAD_Msk
#define DSI_HOST_VAUX_CTRL_AUX_REQ_Pos  (1U)
#define DSI_HOST_VAUX_CTRL_AUX_REQ_Msk  (0x1UL << DSI_HOST_VAUX_CTRL_AUX_REQ_Pos)
#define DSI_HOST_VAUX_CTRL_AUX_REQ      DSI_HOST_VAUX_CTRL_AUX_REQ_Msk
#define DSI_HOST_VAUX_CTRL_EXT_REQ_Pos  (2U)
#define DSI_HOST_VAUX_CTRL_EXT_REQ_Msk  (0x1UL << DSI_HOST_VAUX_CTRL_EXT_REQ_Pos)
#define DSI_HOST_VAUX_CTRL_EXT_REQ      DSI_HOST_VAUX_CTRL_EXT_REQ_Msk

/************** Bit definition for DSI_HOST_DPI_CFG1_R register ***************/
#define DSI_HOST_DPI_CFG1_R_VID_Pos     (0U)
#define DSI_HOST_DPI_CFG1_R_VID_Msk     (0x3UL << DSI_HOST_DPI_CFG1_R_VID_Pos)
#define DSI_HOST_DPI_CFG1_R_VID         DSI_HOST_DPI_CFG1_R_VID_Msk
#define DSI_HOST_DPI_CFG1_R_FORMAT_Pos  (2U)
#define DSI_HOST_DPI_CFG1_R_FORMAT_Msk  (0xFUL << DSI_HOST_DPI_CFG1_R_FORMAT_Pos)
#define DSI_HOST_DPI_CFG1_R_FORMAT      DSI_HOST_DPI_CFG1_R_FORMAT_Msk
#define DSI_HOST_DPI_CFG1_R_LOOSE_EN_Pos  (6U)
#define DSI_HOST_DPI_CFG1_R_LOOSE_EN_Msk  (0x1UL << DSI_HOST_DPI_CFG1_R_LOOSE_EN_Pos)
#define DSI_HOST_DPI_CFG1_R_LOOSE_EN    DSI_HOST_DPI_CFG1_R_LOOSE_EN_Msk

/************** Bit definition for DSI_HOST_DPI_CFG2_R register ***************/
#define DSI_HOST_DPI_CFG2_R_INVACT_Pos  (0U)
#define DSI_HOST_DPI_CFG2_R_INVACT_Msk  (0xFFUL << DSI_HOST_DPI_CFG2_R_INVACT_Pos)
#define DSI_HOST_DPI_CFG2_R_INVACT      DSI_HOST_DPI_CFG2_R_INVACT_Msk
#define DSI_HOST_DPI_CFG2_R_OUTVACT_Pos  (8U)
#define DSI_HOST_DPI_CFG2_R_OUTVACT_Msk  (0xFFUL << DSI_HOST_DPI_CFG2_R_OUTVACT_Pos)
#define DSI_HOST_DPI_CFG2_R_OUTVACT     DSI_HOST_DPI_CFG2_R_OUTVACT_Msk

/************* Bit definition for DSI_HOST_VMODE_CFG1_R register **************/
#define DSI_HOST_VMODE_CFG1_R_VMODE_TYPE_Pos  (0U)
#define DSI_HOST_VMODE_CFG1_R_VMODE_TYPE_Msk  (0x3UL << DSI_HOST_VMODE_CFG1_R_VMODE_TYPE_Pos)
#define DSI_HOST_VMODE_CFG1_R_VMODE_TYPE  DSI_HOST_VMODE_CFG1_R_VMODE_TYPE_Msk
#define DSI_HOST_VMODE_CFG1_R_LPVSA_EN_Pos  (2U)
#define DSI_HOST_VMODE_CFG1_R_LPVSA_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPVSA_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPVSA_EN  DSI_HOST_VMODE_CFG1_R_LPVSA_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPVBP_EN_Pos  (3U)
#define DSI_HOST_VMODE_CFG1_R_LPVBP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPVBP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPVBP_EN  DSI_HOST_VMODE_CFG1_R_LPVBP_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPVFP_EN_Pos  (4U)
#define DSI_HOST_VMODE_CFG1_R_LPVFP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPVFP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPVFP_EN  DSI_HOST_VMODE_CFG1_R_LPVFP_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPVACT_EN_Pos  (5U)
#define DSI_HOST_VMODE_CFG1_R_LPVACT_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPVACT_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPVACT_EN  DSI_HOST_VMODE_CFG1_R_LPVACT_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPHBP_EN_Pos  (6U)
#define DSI_HOST_VMODE_CFG1_R_LPHBP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPHBP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPHBP_EN  DSI_HOST_VMODE_CFG1_R_LPHBP_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPHFP_EN_Pos  (7U)
#define DSI_HOST_VMODE_CFG1_R_LPHFP_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPHFP_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPHFP_EN  DSI_HOST_VMODE_CFG1_R_LPHFP_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_BTA_ACK_EN_Pos  (8U)
#define DSI_HOST_VMODE_CFG1_R_BTA_ACK_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_BTA_ACK_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_BTA_ACK_EN  DSI_HOST_VMODE_CFG1_R_BTA_ACK_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_LPCMD_EN_Pos  (9U)
#define DSI_HOST_VMODE_CFG1_R_LPCMD_EN_Msk  (0x1UL << DSI_HOST_VMODE_CFG1_R_LPCMD_EN_Pos)
#define DSI_HOST_VMODE_CFG1_R_LPCMD_EN  DSI_HOST_VMODE_CFG1_R_LPCMD_EN_Msk
#define DSI_HOST_VMODE_CFG1_R_PKT_SIZE_Pos  (13U)
#define DSI_HOST_VMODE_CFG1_R_PKT_SIZE_Msk  (0x3FFFUL << DSI_HOST_VMODE_CFG1_R_PKT_SIZE_Pos)
#define DSI_HOST_VMODE_CFG1_R_PKT_SIZE  DSI_HOST_VMODE_CFG1_R_PKT_SIZE_Msk

/************* Bit definition for DSI_HOST_VMODE_CFG2_R register **************/
#define DSI_HOST_VMODE_CFG2_R_NUM_CHUNKS_Pos  (0U)
#define DSI_HOST_VMODE_CFG2_R_NUM_CHUNKS_Msk  (0x1FFFUL << DSI_HOST_VMODE_CFG2_R_NUM_CHUNKS_Pos)
#define DSI_HOST_VMODE_CFG2_R_NUM_CHUNKS  DSI_HOST_VMODE_CFG2_R_NUM_CHUNKS_Msk
#define DSI_HOST_VMODE_CFG2_R_NULL_SIZE_Pos  (13U)
#define DSI_HOST_VMODE_CFG2_R_NULL_SIZE_Msk  (0x1FFFUL << DSI_HOST_VMODE_CFG2_R_NULL_SIZE_Pos)
#define DSI_HOST_VMODE_CFG2_R_NULL_SIZE  DSI_HOST_VMODE_CFG2_R_NULL_SIZE_Msk

/*************** Bit definition for DSI_HOST_VTIMER1_R register ***************/
#define DSI_HOST_VTIMER1_R_HSA_Pos      (0U)
#define DSI_HOST_VTIMER1_R_HSA_Msk      (0xFFFUL << DSI_HOST_VTIMER1_R_HSA_Pos)
#define DSI_HOST_VTIMER1_R_HSA          DSI_HOST_VTIMER1_R_HSA_Msk
#define DSI_HOST_VTIMER1_R_HBP_Pos      (12U)
#define DSI_HOST_VTIMER1_R_HBP_Msk      (0xFFFUL << DSI_HOST_VTIMER1_R_HBP_Pos)
#define DSI_HOST_VTIMER1_R_HBP          DSI_HOST_VTIMER1_R_HBP_Msk

/*************** Bit definition for DSI_HOST_VTIMER2_R register ***************/
#define DSI_HOST_VTIMER2_R_HLINE_Pos    (0U)
#define DSI_HOST_VTIMER2_R_HLINE_Msk    (0x7FFFUL << DSI_HOST_VTIMER2_R_HLINE_Pos)
#define DSI_HOST_VTIMER2_R_HLINE        DSI_HOST_VTIMER2_R_HLINE_Msk
#define DSI_HOST_VTIMER2_R_VSA_Pos      (15U)
#define DSI_HOST_VTIMER2_R_VSA_Msk      (0x3FFUL << DSI_HOST_VTIMER2_R_VSA_Pos)
#define DSI_HOST_VTIMER2_R_VSA          DSI_HOST_VTIMER2_R_VSA_Msk

/*************** Bit definition for DSI_HOST_VTIMER3_R register ***************/
#define DSI_HOST_VTIMER3_R_VBP_Pos      (0U)
#define DSI_HOST_VTIMER3_R_VBP_Msk      (0x3FFUL << DSI_HOST_VTIMER3_R_VBP_Pos)
#define DSI_HOST_VTIMER3_R_VBP          DSI_HOST_VTIMER3_R_VBP_Msk
#define DSI_HOST_VTIMER3_R_VFP_Pos      (10U)
#define DSI_HOST_VTIMER3_R_VFP_Msk      (0x3FFUL << DSI_HOST_VTIMER3_R_VFP_Pos)
#define DSI_HOST_VTIMER3_R_VFP          DSI_HOST_VTIMER3_R_VFP_Msk

/*************** Bit definition for DSI_HOST_VTIMER4_R register ***************/
#define DSI_HOST_VTIMER4_R_VACT_Pos     (0U)
#define DSI_HOST_VTIMER4_R_VACT_Msk     (0x3FFFUL << DSI_HOST_VTIMER4_R_VACT_Pos)
#define DSI_HOST_VTIMER4_R_VACT         DSI_HOST_VTIMER4_R_VACT_Msk

/**************** Bit definition for DSI_HOST_SDF3D_R register ****************/
#define DSI_HOST_SDF3D_R_MODE_Pos       (0U)
#define DSI_HOST_SDF3D_R_MODE_Msk       (0x3UL << DSI_HOST_SDF3D_R_MODE_Pos)
#define DSI_HOST_SDF3D_R_MODE           DSI_HOST_SDF3D_R_MODE_Msk
#define DSI_HOST_SDF3D_R_FORMAT_Pos     (2U)
#define DSI_HOST_SDF3D_R_FORMAT_Msk     (0x3UL << DSI_HOST_SDF3D_R_FORMAT_Pos)
#define DSI_HOST_SDF3D_R_FORMAT         DSI_HOST_SDF3D_R_FORMAT_Msk
#define DSI_HOST_SDF3D_R_VSYNC_2ND_Pos  (4U)
#define DSI_HOST_SDF3D_R_VSYNC_2ND_Msk  (0x1UL << DSI_HOST_SDF3D_R_VSYNC_2ND_Pos)
#define DSI_HOST_SDF3D_R_VSYNC_2ND      DSI_HOST_SDF3D_R_VSYNC_2ND_Msk
#define DSI_HOST_SDF3D_R_DATA_ORDER_Pos  (5U)
#define DSI_HOST_SDF3D_R_DATA_ORDER_Msk  (0x1UL << DSI_HOST_SDF3D_R_DATA_ORDER_Pos)
#define DSI_HOST_SDF3D_R_DATA_ORDER     DSI_HOST_SDF3D_R_DATA_ORDER_Msk
#define DSI_HOST_SDF3D_R_CFG_EN_Pos     (6U)
#define DSI_HOST_SDF3D_R_CFG_EN_Msk     (0x1UL << DSI_HOST_SDF3D_R_CFG_EN_Pos)
#define DSI_HOST_SDF3D_R_CFG_EN         DSI_HOST_SDF3D_R_CFG_EN_Msk

/************ Bit definition for DSI_HOST_DCOM_INTF_CTRL register *************/
#define DSI_HOST_DCOM_INTF_CTRL_START_Pos  (0U)
#define DSI_HOST_DCOM_INTF_CTRL_START_Msk  (0x1UL << DSI_HOST_DCOM_INTF_CTRL_START_Pos)
#define DSI_HOST_DCOM_INTF_CTRL_START   DSI_HOST_DCOM_INTF_CTRL_START_Msk
#define DSI_HOST_DCOM_INTF_CTRL_SW_RESET_Pos  (1U)
#define DSI_HOST_DCOM_INTF_CTRL_SW_RESET_Msk  (0x1UL << DSI_HOST_DCOM_INTF_CTRL_SW_RESET_Pos)
#define DSI_HOST_DCOM_INTF_CTRL_SW_RESET  DSI_HOST_DCOM_INTF_CTRL_SW_RESET_Msk

/************ Bit definition for DSI_HOST_DCOM_INTF_CFG1 register *************/
#define DSI_HOST_DCOM_INTF_CFG1_WAIT_CYCLE_Pos  (0U)
#define DSI_HOST_DCOM_INTF_CFG1_WAIT_CYCLE_Msk  (0xFFFFUL << DSI_HOST_DCOM_INTF_CFG1_WAIT_CYCLE_Pos)
#define DSI_HOST_DCOM_INTF_CFG1_WAIT_CYCLE  DSI_HOST_DCOM_INTF_CFG1_WAIT_CYCLE_Msk
#define DSI_HOST_DCOM_INTF_CFG1_PKT_SIZE_Pos  (16U)
#define DSI_HOST_DCOM_INTF_CFG1_PKT_SIZE_Msk  (0xFFFFUL << DSI_HOST_DCOM_INTF_CFG1_PKT_SIZE_Pos)
#define DSI_HOST_DCOM_INTF_CFG1_PKT_SIZE  DSI_HOST_DCOM_INTF_CFG1_PKT_SIZE_Msk

/************ Bit definition for DSI_HOST_DCOM_INTF_CFG2 register *************/
#define DSI_HOST_DCOM_INTF_CFG2_TOTAL_SIZE_Pos  (0U)
#define DSI_HOST_DCOM_INTF_CFG2_TOTAL_SIZE_Msk  (0xFFFFFFFFUL << DSI_HOST_DCOM_INTF_CFG2_TOTAL_SIZE_Pos)
#define DSI_HOST_DCOM_INTF_CFG2_TOTAL_SIZE  DSI_HOST_DCOM_INTF_CFG2_TOTAL_SIZE_Msk

/************ Bit definition for DSI_HOST_DCOM_INTF_CFG3 register *************/
#define DSI_HOST_DCOM_INTF_CFG3_I_FORMAT_Pos  (0U)
#define DSI_HOST_DCOM_INTF_CFG3_I_FORMAT_Msk  (0x3UL << DSI_HOST_DCOM_INTF_CFG3_I_FORMAT_Pos)
#define DSI_HOST_DCOM_INTF_CFG3_I_FORMAT  DSI_HOST_DCOM_INTF_CFG3_I_FORMAT_Msk
#define DSI_HOST_DCOM_INTF_CFG3_ID_Pos  (2U)
#define DSI_HOST_DCOM_INTF_CFG3_ID_Msk  (0x3UL << DSI_HOST_DCOM_INTF_CFG3_ID_Pos)
#define DSI_HOST_DCOM_INTF_CFG3_ID      DSI_HOST_DCOM_INTF_CFG3_ID_Msk
#define DSI_HOST_DCOM_INTF_CFG3_DATA_TYPE_Pos  (4U)
#define DSI_HOST_DCOM_INTF_CFG3_DATA_TYPE_Msk  (0x3FUL << DSI_HOST_DCOM_INTF_CFG3_DATA_TYPE_Pos)
#define DSI_HOST_DCOM_INTF_CFG3_DATA_TYPE  DSI_HOST_DCOM_INTF_CFG3_DATA_TYPE_Msk

#endif
