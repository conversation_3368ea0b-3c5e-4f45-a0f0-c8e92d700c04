/**
  ******************************************************************************
  * @file   st7735.c
  * <AUTHOR> software development team
  * @brief   This file includes the driver for ST7735 LCD mounted on the Adafruit
  *          1.8" TFT LCD shield (reference ID 802).
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON>fli nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st7735.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7735
  * @brief      This file provides a set of functions needed to drive the
  *             ST7735 LCD.
  * @{
  */

/** @defgroup ST7735_Private_TypesDefinitions
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7735_Private_Defines
  * @{
  */

#define ST7735_NOP     0x00
#define ST7735_SWRESET 0x01
#define ST7735_RDDID   0x04
#define ST7735_RDDST   0x09
#define ST7735_RDDMADCTL 0x0B

#define ST7735_SLPIN   0x10
#define ST7735_SLPOUT  0x11
#define ST7735_PTLON   0x12
#define ST7735_NORON   0x13

#define ST7735_INVOFF  0x20
#define ST7735_INVON   0x21
#define ST7735_DISPOFF 0x28
#define ST7735_DISPON  0x29
#define ST7735_CASET   0x2A
#define ST7735_RASET   0x2B
#define ST7735_RAMWR   0x2C
#define ST7735_RAMRD   0x2E

#define ST7735_PTLAR   0x30
#define ST7735_COLMOD  0x3A
#define ST7735_MADCTL  0x36

#define ST7735_FRMCTR1 0xB1
#define ST7735_FRMCTR2 0xB2
#define ST7735_FRMCTR3 0xB3
#define ST7735_INVCTR  0xB4
#define ST7735_DISSET5 0xB6

#define ST7735_PWCTR1  0xC0
#define ST7735_PWCTR2  0xC1
#define ST7735_PWCTR3  0xC2
#define ST7735_PWCTR4  0xC3
#define ST7735_PWCTR5  0xC4
#define ST7735_VMCTR1  0xC5

#define ST7735_RDID1   0xDA
#define ST7735_RDID2   0xDB
#define ST7735_RDID3   0xDC
#define ST7735_RDID4   0xDD

#define ST7735_PWCTR6  0xFC

#define ST7735_GMCTRP1 0xE0
#define ST7735_GMCTRN1 0xE1

#define ST7735_MADCTL_MY  0x80
#define ST7735_MADCTL_MX  0x40
#define ST7735_MADCTL_MV  0x20
#define ST7735_MADCTL_ML  0x10
#define ST7735_MADCTL_RGB 0x00
#define ST7735_MADCTL_BGR 0x08
#define ST7735_MADCTL_MH  0x04


/**
  * @}
  */

/** @defgroup ST7735_Private_Macros
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7735_Private_Variables
  * @{
  */


LCD_DrvOpsDef   st7735_drv =
{
    st7735_Init,
    0,
    st7735_DisplayOn,
    st7735_DisplayOff,
    st7735_SetCursor,
    st7735_SetRegion,
    st7735_WritePixel,
    st7735_WriteMultiplePixels,
    0,
    0,
    st7735_SetDisplayWindow,
    st7735_DrawHLine,
    st7735_DrawVLine,
    st7735_GetLcdPixelWidth,
    st7735_GetLcdPixelHeight,
    st7735_DrawBitmap,
};

static uint16_t ArrayRGB[320] = {0};

/**
* @}
*/

/** @defgroup ST7735_Private_FunctionPrototypes
  * @{
  */

/**
* @}
*/

/** @defgroup ST7735_Private_Functions
  * @{
  */

void delay_us1(rt_uint32_t nus)
{
    //rt_thread_delay(1);
    while (nus--)
    {
        __NOP();
    }
}

/**
  * @brief  Initialize the ST7735 LCD Component.
  * @param  None
  * @retval None
  */
void st7735_Init(void)
{
    uint8_t data = 0;

    /* Initialize ST7735 low level bus layer -----------------------------------*/
    LCD_IO_Init();

    LCD_IO_WriteReg(ST7735_SWRESET);
    //rt_thread_mdelay(150);
    delay_us1(150 * 1000);
    LCD_IO_WriteReg(ST7735_SLPOUT);
    //rt_thread_mdelay(500);
    delay_us1(500 * 1000);

    LCD_IO_WriteReg(ST7735_FRMCTR1);
    LCD_IO_WriteData(0x01);
    LCD_IO_WriteData(0x2C);
    LCD_IO_WriteData(0x2D);
    LCD_IO_WriteReg(ST7735_FRMCTR2);
    LCD_IO_WriteData(0x01);
    LCD_IO_WriteData(0x2C);
    LCD_IO_WriteData(0x2D);
    LCD_IO_WriteReg(ST7735_FRMCTR3);
    LCD_IO_WriteData(0x01);
    LCD_IO_WriteData(0x2C);
    LCD_IO_WriteData(0x2D);
    LCD_IO_WriteData(0x01);
    LCD_IO_WriteData(0x2C);
    LCD_IO_WriteData(0x2D);

    LCD_IO_WriteReg(ST7735_INVCTR);
    LCD_IO_WriteData(0x07);

    LCD_IO_WriteReg(ST7735_PWCTR1);
    LCD_IO_WriteData(0xA2);
    LCD_IO_WriteData(0x02);
    LCD_IO_WriteData(0x84);
    LCD_IO_WriteReg(ST7735_PWCTR2);
    LCD_IO_WriteData(0xC5);
    LCD_IO_WriteReg(ST7735_PWCTR3);
    LCD_IO_WriteData(0x0A);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteReg(ST7735_PWCTR3);
    LCD_IO_WriteData(0x8A);
    LCD_IO_WriteData(0x2A);
    LCD_IO_WriteReg(ST7735_PWCTR5);
    LCD_IO_WriteData(0x8A);
    LCD_IO_WriteData(0xEE);
    LCD_IO_WriteData(0x0E);

    LCD_IO_WriteReg(ST7735_INVOFF);
    LCD_IO_WriteReg(ST7735_MADCTL);
    LCD_IO_WriteData(0xC8);

    LCD_IO_WriteReg(ST7735_COLMOD);
    LCD_IO_WriteData(0x05);

    LCD_IO_WriteReg(ST7735_CASET);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x7F);
    LCD_IO_WriteReg(ST7735_RASET);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x9F);

    LCD_IO_WriteReg(ST7735_GMCTRP1);
    LCD_IO_WriteData(0x02);
    LCD_IO_WriteData(0x1c);
    LCD_IO_WriteData(0x07);
    LCD_IO_WriteData(0x12);
    LCD_IO_WriteData(0x37);
    LCD_IO_WriteData(0x32);
    LCD_IO_WriteData(0x29);
    LCD_IO_WriteData(0x2d);
    LCD_IO_WriteData(0x29);
    LCD_IO_WriteData(0x25);
    LCD_IO_WriteData(0x2b);
    LCD_IO_WriteData(0x39);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x01);
    LCD_IO_WriteData(0x03);
    LCD_IO_WriteData(0x10);
    LCD_IO_WriteReg(ST7735_GMCTRN1);
    LCD_IO_WriteData(0x03);
    LCD_IO_WriteData(0x1d);
    LCD_IO_WriteData(0x07);
    LCD_IO_WriteData(0x06);
    LCD_IO_WriteData(0x2e);
    LCD_IO_WriteData(0x2c);
    LCD_IO_WriteData(0x29);
    LCD_IO_WriteData(0x2d);
    LCD_IO_WriteData(0x2e);
    LCD_IO_WriteData(0x2e);
    LCD_IO_WriteData(0x37);
    LCD_IO_WriteData(0x3f);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x00);
    LCD_IO_WriteData(0x02);
    LCD_IO_WriteData(0x10);

    LCD_IO_WriteReg(ST7735_NORON);
    //rt_thread_mdelay(10);
    delay_us1(10 * 1000);

    LCD_IO_WriteReg(ST7735_DISPON);
    //rt_thread_mdelay(1000);
    delay_us1(1000 * 1000);


#if 0
    /* Out of sleep mode, 0 args, no delay */
    st7735_WriteReg(LCD_REG_17, 0x00);
    /* Frame rate ctrl - normal mode, 3 args:Rate = fosc/(1x2+40) * (LINE+2C+2D)*/
    LCD_IO_WriteReg(LCD_REG_177);
    data = 0x01;
    LCD_IO_WriteData(data);
    data = 0x2C;
    LCD_IO_WriteMultipleData(&data, 1);
    data = 0x2D;
    LCD_IO_WriteMultipleData(&data, 1);
    /* Frame rate control - idle mode, 3 args:Rate = fosc/(1x2+40) * (LINE+2C+2D) */
    st7735_WriteReg(LCD_REG_178, 0x01);
    st7735_WriteReg(LCD_REG_178, 0x2C);
    st7735_WriteReg(LCD_REG_178, 0x2D);
    /* Frame rate ctrl - partial mode, 6 args: Dot inversion mode, Line inversion mode */
    st7735_WriteReg(LCD_REG_179, 0x01);
    st7735_WriteReg(LCD_REG_179, 0x2C);
    st7735_WriteReg(LCD_REG_179, 0x2D);
    st7735_WriteReg(LCD_REG_179, 0x01);
    st7735_WriteReg(LCD_REG_179, 0x2C);
    st7735_WriteReg(LCD_REG_179, 0x2D);
    /* Display inversion ctrl, 1 arg, no delay: No inversion */
    st7735_WriteReg(LCD_REG_180, 0x07);
    /* Power control, 3 args, no delay: -4.6V , AUTO mode */
    st7735_WriteReg(LCD_REG_192, 0xA2);
    st7735_WriteReg(LCD_REG_192, 0x02);
    st7735_WriteReg(LCD_REG_192, 0x84);
    /* Power control, 1 arg, no delay: VGH25 = 2.4C VGSEL = -10 VGH = 3 * AVDD */
    st7735_WriteReg(LCD_REG_193, 0xC5);
    /* Power control, 2 args, no delay: Opamp current small, Boost frequency */
    st7735_WriteReg(LCD_REG_194, 0x0A);
    st7735_WriteReg(LCD_REG_194, 0x00);
    /* Power control, 2 args, no delay: BCLK/2, Opamp current small & Medium low */
    st7735_WriteReg(LCD_REG_195, 0x8A);
    st7735_WriteReg(LCD_REG_195, 0x2A);
    /* Power control, 2 args, no delay */
    st7735_WriteReg(LCD_REG_196, 0x8A);
    st7735_WriteReg(LCD_REG_196, 0xEE);
    /* Power control, 1 arg, no delay */
    st7735_WriteReg(LCD_REG_197, 0x0E);
    /* Don't invert display, no args, no delay */
    LCD_IO_WriteReg(LCD_REG_32);
    /* Set color mode, 1 arg, no delay: 16-bit color */
    st7735_WriteReg(LCD_REG_58, 0x05);
    /* Column addr set, 4 args, no delay: XSTART = 0, XEND = 127 */
    LCD_IO_WriteReg(LCD_REG_42);
    data = 0x00;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteMultipleData(&data, 1);
    data = 0x7F;
    LCD_IO_WriteMultipleData(&data, 1);
    /* Row addr set, 4 args, no delay: YSTART = 0, YEND = 159 */
    LCD_IO_WriteReg(LCD_REG_43);
    data = 0x00;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteMultipleData(&data, 1);
    data = 0x9F;
    LCD_IO_WriteMultipleData(&data, 1);
    /* Magical unicorn dust, 16 args, no delay */
    st7735_WriteReg(LCD_REG_224, 0x02);
    st7735_WriteReg(LCD_REG_224, 0x1c);
    st7735_WriteReg(LCD_REG_224, 0x07);
    st7735_WriteReg(LCD_REG_224, 0x12);
    st7735_WriteReg(LCD_REG_224, 0x37);
    st7735_WriteReg(LCD_REG_224, 0x32);
    st7735_WriteReg(LCD_REG_224, 0x29);
    st7735_WriteReg(LCD_REG_224, 0x2d);
    st7735_WriteReg(LCD_REG_224, 0x29);
    st7735_WriteReg(LCD_REG_224, 0x25);
    st7735_WriteReg(LCD_REG_224, 0x2B);
    st7735_WriteReg(LCD_REG_224, 0x39);
    st7735_WriteReg(LCD_REG_224, 0x00);
    st7735_WriteReg(LCD_REG_224, 0x01);
    st7735_WriteReg(LCD_REG_224, 0x03);
    st7735_WriteReg(LCD_REG_224, 0x10);
    /* Sparkles and rainbows, 16 args, no delay */
    st7735_WriteReg(LCD_REG_225, 0x03);
    st7735_WriteReg(LCD_REG_225, 0x1d);
    st7735_WriteReg(LCD_REG_225, 0x07);
    st7735_WriteReg(LCD_REG_225, 0x06);
    st7735_WriteReg(LCD_REG_225, 0x2E);
    st7735_WriteReg(LCD_REG_225, 0x2C);
    st7735_WriteReg(LCD_REG_225, 0x29);
    st7735_WriteReg(LCD_REG_225, 0x2D);
    st7735_WriteReg(LCD_REG_225, 0x2E);
    st7735_WriteReg(LCD_REG_225, 0x2E);
    st7735_WriteReg(LCD_REG_225, 0x37);
    st7735_WriteReg(LCD_REG_225, 0x3F);
    st7735_WriteReg(LCD_REG_225, 0x00);
    st7735_WriteReg(LCD_REG_225, 0x00);
    st7735_WriteReg(LCD_REG_225, 0x02);
    st7735_WriteReg(LCD_REG_225, 0x10);
    /* Normal display on, no args, no delay */
    st7735_WriteReg(LCD_REG_19, 0x00);
    /* Main screen turn on, no delay */
    st7735_WriteReg(LCD_REG_41, 0x00);
    /* Memory access control: MY = 1, MX = 1, MV = 0, ML = 0 */
    st7735_WriteReg(LCD_REG_54, 0xC0);
#endif
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void st7735_DisplayOn(void)
{
    uint8_t data = 0;
    LCD_IO_WriteReg(LCD_REG_19);
    LCD_DRIVER_DELAY_MS(1);
    LCD_IO_WriteReg(LCD_REG_41);
    LCD_DRIVER_DELAY_MS(1);
    LCD_IO_WriteReg(LCD_REG_54);
    data = 0xC0;
    LCD_IO_WriteMultipleData(&data, 1);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void st7735_DisplayOff(void)
{
    uint8_t data = 0;
    LCD_IO_WriteReg(LCD_REG_19);
    LCD_DRIVER_DELAY_MS(1);
    LCD_IO_WriteReg(LCD_REG_40);
    LCD_DRIVER_DELAY_MS(1);
    LCD_IO_WriteReg(LCD_REG_54);
    data = 0xC0;
    LCD_IO_WriteMultipleData(&data, 1);
}

/**
  * @brief  Sets Cursor position.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @retval None
  */
void st7735_SetCursor(uint16_t Xpos, uint16_t Ypos)
{
    uint8_t data = 0;
    LCD_IO_WriteReg(LCD_REG_42);
    data = (Xpos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteReg(LCD_REG_43);
    data = (Ypos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteReg(LCD_REG_44);
}

void st7735_SetRegion(uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t data = 0;
    LCD_IO_WriteReg(LCD_REG_42);
    data = (Xpos0) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos0) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos1) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos1) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteReg(LCD_REG_43);
    data = (Ypos0) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos0) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos1) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos1) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    LCD_IO_WriteReg(LCD_REG_44);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void st7735_WritePixel(uint16_t Xpos, uint16_t Ypos, uint16_t RGBCode)
{
    uint8_t data = 0;


    /* Set Cursor */
    st7735_SetCursor(Xpos, Ypos);

    data = RGBCode >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = RGBCode;
    LCD_IO_WriteMultipleData(&data, 1);
}

void st7735_WriteMultiplePixels(uint16_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size = 0;


    /* Set Cursor */
    st7735_SetRegion(Xpos0, Ypos0, Xpos1, Ypos1);

    LCD_IO_WriteMultipleData((uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
}



/**
  * @brief  Writes to the selected LCD register.
  * @param  LCDReg: Address of the selected register.
  * @param  LCDRegValue: value to write to the selected register.
  * @retval None
  */
void st7735_WriteReg(uint8_t LCDReg, uint8_t LCDRegValue)
{
    LCD_IO_WriteReg(LCDReg);
    LCD_IO_WriteData(LCDRegValue);
}

/**
  * @brief  Sets a display window
  * @param  Xpos:   specifies the X bottom left position.
  * @param  Ypos:   specifies the Y bottom left position.
  * @param  Height: display window height.
  * @param  Width:  display window width.
  * @retval None
  */
void st7735_SetDisplayWindow(uint16_t Xpos, uint16_t Ypos, uint16_t Width, uint16_t Height)
{
    uint8_t data = 0;
    /* Column addr set, 4 args, no delay: XSTART = Xpos, XEND = (Xpos + Width - 1) */
    LCD_IO_WriteReg(LCD_REG_42);
    data = (Xpos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos + Width - 1) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Xpos + Width - 1) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    /* Row addr set, 4 args, no delay: YSTART = Ypos, YEND = (Ypos + Height - 1) */
    LCD_IO_WriteReg(LCD_REG_43);
    data = (Ypos) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos + Height - 1) >> 8;
    LCD_IO_WriteMultipleData(&data, 1);
    data = (Ypos + Height - 1) & 0xFF;
    LCD_IO_WriteMultipleData(&data, 1);
}

/**
  * @brief  Draws horizontal line.
  * @param  RGBCode: Specifies the RGB color
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  Length: specifies the line length.
  * @retval None
  */
void st7735_DrawHLine(uint16_t RGBCode, uint16_t Xpos, uint16_t Ypos, uint16_t Length)
{
    uint8_t counter = 0;

    if (Xpos + Length > ST7735_LCD_PIXEL_WIDTH) return;

    /* Set Cursor */
    st7735_SetRegion(Xpos, Ypos, Xpos + Length, Ypos);

    for (counter = 0; counter < Length; counter++)
    {
        ArrayRGB[counter] = RGBCode;
    }
    LCD_IO_WriteMultipleData((uint8_t *)&ArrayRGB[0], Length * 2);
}

/**
  * @brief  Draws vertical line.
  * @param  RGBCode: Specifies the RGB color
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  Length: specifies the line length.
  * @retval None
  */
void st7735_DrawVLine(uint16_t RGBCode, uint16_t Xpos, uint16_t Ypos, uint16_t Length)
{
    uint8_t counter = 0;

    if (Ypos + Length > ST7735_LCD_PIXEL_HEIGHT) return;
    for (counter = 0; counter < Length; counter++)
    {
        st7735_WritePixel(Xpos, Ypos + counter, RGBCode);
    }
}

/**
  * @brief  Gets the LCD pixel Width.
  * @param  None
  * @retval The Lcd Pixel Width
  */
uint16_t st7735_GetLcdPixelWidth(void)
{
    return ST7735_LCD_PIXEL_WIDTH;
}

/**
  * @brief  Gets the LCD pixel Height.
  * @param  None
  * @retval The Lcd Pixel Height
  */
uint16_t st7735_GetLcdPixelHeight(void)
{
    return ST7735_LCD_PIXEL_HEIGHT;
}

/**
  * @brief  Displays a bitmap picture loaded in the internal Flash.
  * @param  BmpAddress: Bmp picture address in the internal Flash.
  * @retval None
  */
void st7735_DrawBitmap(uint16_t Xpos, uint16_t Ypos, uint8_t *pbmp)
{
    uint32_t index = 0, size = 0;

    /* Read bitmap size */
    size = *(volatile uint16_t *)(pbmp + 2);
    size |= (*(volatile uint16_t *)(pbmp + 4)) << 16;
    /* Get bitmap data address offset */
    index = *(volatile uint16_t *)(pbmp + 10);
    index |= (*(volatile uint16_t *)(pbmp + 12)) << 16;
    size = (size - index) / 2;
    pbmp += index;

    /* Set GRAM write direction and BGR = 0 */
    /* Memory access control: MY = 0, MX = 1, MV = 0, ML = 0 */
    st7735_WriteReg(LCD_REG_54, 0x40);

    /* Set Cursor */
    st7735_SetCursor(Xpos, Ypos);

    LCD_IO_WriteMultipleData((uint8_t *)pbmp, size * 2);

    /* Set GRAM write direction and BGR = 0 */
    /* Memory access control: MY = 1, MX = 1, MV = 0, ML = 0 */
    st7735_WriteReg(LCD_REG_54, 0xC0);
}

/**
* @}
*/

/**
* @}
*/

/**
* @}
*/

/**
* @}
*/

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/

