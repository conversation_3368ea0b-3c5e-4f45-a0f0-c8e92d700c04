/**
  ******************************************************************************
  * @file   ft2308.c
  * <AUTHOR> development team
  * @brief   This file includes the LCD driver for ft2308 LCD.
  * @attention
  ******************************************************************************
*/

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "ft2308.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ft2308
  * @brief This file provides a set of functions needed to drive the
  *        ft2308 LCD.
  * @{
  */

/** @defgroup ft2308_Private_TypesDefinitions
  * @{
  */

#define ROW_OFFSET  (0x00)
#define COL_OFFSET  (0x00)

typedef struct
{
    rt_uint16_t width;
    rt_uint16_t height;
    rt_uint16_t id;
    rt_uint8_t  dir;            //Horizontal or vertical screen control: 0, vertical; 1, horizontal
    rt_uint16_t wramcmd;
    rt_uint16_t setxcmd;
    rt_uint16_t setycmd;
} lcd_info_t;


/**
  * @}
  */

/** @defgroup ft2308_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ft2308_Private_Macros
  * @{
  */
#define RGB_ARRAY_LEN (320)

//Definition of scan direction
#define L2R_U2D  0
#define L2R_D2U  1
#define R2L_U2D  2
#define R2L_D2U  3
#define U2D_L2R  4
#define U2D_R2L  5
#define D2U_L2R  6
#define D2U_R2L  7
#define DFT_SCAN_DIR  L2R_U2D




#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif


static lcd_info_t lcddev;

/**
  * @}
  */

/** @defgroup ft2308_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ft2308_drv =
{
    ft2308_Init,
    ft2308_ReadID,
    ft2308_DisplayOn,
    ft2308_DisplayOff,

    ft2308_SetRegion,
    ft2308_WritePixel,
    ft2308_WriteMultiplePixels,
    ft2308_ReadPixel,

    ft2308_SetColorMode,
    ft2308_SetBrightness,
    NULL,
    NULL,
    NULL,
    ft2308_TimeoutDbg,
    ft2308_TimeoutReset
};


static uint16_t ArrayRGB[RGB_ARRAY_LEN] = {0};

#ifdef BSP_LCDC_USING_DDR_QADSPI
    #define QAD_SPI_ITF LCDC_INTF_SPI_DCX_DDR_4DATA
    #define QAD_SPI_ITF_FREQ   40000000
#else
    #define QAD_SPI_ITF LCDC_INTF_SPI_DCX_4DATA
    #define QAD_SPI_ITF_FREQ   50000000
#endif

static LCDC_InitTypeDef lcdc_int_cfg_spi =
{
    .lcd_itf = QAD_SPI_ITF, //LCDC_INTF_SPI_NODCX_1DATA,
    .freq = QAD_SPI_ITF_FREQ,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,

    .cfg = {
        .spi = {
            .dummy_clock = 1, //0: QAD-SPI/SPI3   1:SPI4
#ifdef LCD_FT2308_VSYNC_ENABLE
            .syn_mode = HAL_LCDC_SYNC_VER, //HAL_LCDC_SYNC_VER,
#else
            .syn_mode = HAL_LCDC_SYNC_DISABLE, //HAL_LCDC_SYNC_VER,
#endif
            .vsyn_polarity = 1,
            //default_vbp=2, frame rate=82, delay=115us,
            //TODO: use us to define delay instead of cycle, delay_cycle=115*48
            .vsyn_delay_us = 0,
            .hsyn_num = 0,
#ifdef LCDC_SUPPORT_DDR_QSPI
            .flags = SPI_LCD_FLAG_DDR_DUMMY_CLOCK,
#endif /* LCDC_SUPPORT_DDR_QSPI */
            .readback_from_Dx = 3,
        },
    },

};

static LCDC_InitTypeDef lcdc_int_cfg;
static void     ft2308_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
static uint32_t ft2308_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);
static void ft2308_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable);


LCD_DRIVER_EXPORT(ft2308, ft2308_ID, &lcdc_int_cfg,
                  &ft2308_drv,
                  ft2308_LCD_PIXEL_WIDTH,
                  ft2308_LCD_PIXEL_HEIGHT,
                  2);

/**
  * @}
  */


/** @defgroup ft2308_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ft2308_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ft2308_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf) || HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 2000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }
    }
}


/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
#if 1//def LCD_USING_ED_LB5XSPI19701_QADSPI_LB56X_HDK
void ft2308_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t parameter[32];

    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_spi, sizeof(lcdc_int_cfg));
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(1);
    rt_thread_mdelay(10);
    BSP_LCD_Reset(0);//Reset LCD
    rt_thread_mdelay(10);
    BSP_LCD_Reset(1);
    rt_thread_mdelay(10);
    uint16_t num = 0;

    uint8_t data[40] = {0};
    data[0] = 0x23;
    data[1] = 0x08;
    data[2] = 0x01;
    ft2308_WriteReg(hlcdc, 0xFF00, data, 3);

    data[0] = 0x23;
    data[1] = 0x08;
    ft2308_WriteReg(hlcdc, 0xFF80, data, 2);

//    data[0] = 0xEF;
//    data[1] = 0x00;
//    data[2] = 0x58;
//    ft2308_WriteReg(hlcdc, 0xC3B9, data, 3);

//    data[0] = 0xBC;
//    data[1] = 0xD0;
//    data[2] = 0x54;
//    ft2308_WriteReg(hlcdc, 0xC3C9, data, 3);

//    data[0] = 0xEF;
//    data[1] = 0x00;
//    data[2] = 0x58;
//    ft2308_WriteReg(hlcdc, 0xC3D9, data, 3);

//    data[0] = 0xBC;
//    data[1] = 0xD0;
//    data[2] = 0x54;
//    ft2308_WriteReg(hlcdc, 0xC3E9, data, 3);

//    data[0] = 0x01;
//    ft2308_WriteReg(hlcdc, 0xC49A, data, 1);

    data[0] = 0x39;
    ft2308_WriteReg(hlcdc, 0xF381, data, 1);

    data[0] = 0x00;
    ft2308_WriteReg(hlcdc, 0xC990, data, 1);

#ifdef BSP_LCDC_USING_DDR_QADSPI

#else
#if BSP_USING_PSRAM
    //50fps
    data[0] = 0x6e;
    ft2308_WriteReg(hlcdc, 0xC083, data, 1);

    data[0] = 0x6e;
    ft2308_WriteReg(hlcdc, 0xC099, data, 1);
#else
    //40fps
    data[0] = 0x01;

    data[1] = 0x04;

    ft2308_WriteReg(hlcdc, 0xC082, data, 2);

    data[0] = 0x01;

    data[1] = 0x04;

    ft2308_WriteReg(hlcdc, 0xC098, data, 2);
#endif
#endif

    data[0] = 0x81;
    ft2308_WriteReg(hlcdc, 0x9600, data, 1);


    if (LCDC_INTF_SPI_DCX_DDR_4DATA == lcdc_int_cfg.lcd_itf)
    {
        data[0] = 0x81;
        ft2308_WriteReg(hlcdc, 0xb181, data, 1);
    }

    data[0] = 0x00;
    data[1] = 0x00;
    ft2308_WriteReg(hlcdc, 0xFF80, data, 2);


    data[0] = 0x00;
    data[1] = 0x00;
    ft2308_WriteReg(hlcdc, 0xFF00, data, 2);


//    data[0] = 0x92;
//    ft2308_WriteReg(hlcdc, 0xD804, data, 1);
//    data[0] = 0xff;
//    ft2308_WriteReg(hlcdc, 0x5100, data, 1);

//    data[0] = 0xC0;
//    ft2308_WriteReg(hlcdc, 0xC470, data, 1);

    if (LCDC_PIXEL_FORMAT_RGB888 == lcdc_int_cfg.color_mode)
        data[0] = 0x77; //24bit rgb
    else if (LCDC_PIXEL_FORMAT_RGB565 == lcdc_int_cfg.color_mode)
        data[0] = 0x55; //16bit rgb
    else
        RT_ASSERT(0); //fix me

    ft2308_WriteReg(hlcdc, 0x3A00, data, 1);

    data[0] = 0x00;
    ft2308_WriteReg(hlcdc, 0x3500, data, 1);

    ft2308_WriteReg(hlcdc, 0x1100, NULL, 0);

    rt_thread_delay(60);

    ft2308_WriteReg(hlcdc, 0x2900, NULL, 0);

    rt_thread_delay(10);

//    data[0] = 0x23;
//    data[1] = 0x08;
//    data[2] = 0x01;
//    data[3] = 0x01;
//    ft2308_WriteReg(hlcdc, 0xFF00, data, 4);



//    rt_kprintf("ft2308_Init done\r\n");
}
#else
void ft2308_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t parameter[32];

    rt_kprintf("ft2308_Init\r\n");
    memcpy(&lcdc_int_cfg, &lcdc_int_cfg_spi, sizeof(lcdc_int_cfg));

    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(20);
    BSP_LCD_Reset(1);
    LCD_DRIVER_DELAY_MS(30);
    uint16_t num = 0;

    uint8_t data[40] = {0};
    data[0] = 0x23;
    data[1] = 0x08;
    data[2] = 0x01;
    ft2308_WriteReg(hlcdc, 0xFF00, data, 3);
    data[0] = 0x23;
    data[1] = 0x08;
    ft2308_WriteReg(hlcdc, 0xFF80, data, 2);
    data[0] = 0x63;
    ft2308_WriteReg(hlcdc, 0xb282, data, 1);
    data[0] = 0x55;
    ft2308_WriteReg(hlcdc, 0x3a00, data, 1);
    data[0] = 0xff;
    data[1] = 0x00;
    ft2308_WriteReg(hlcdc, 0x5100, data, 2);
    data[0] = 0x00;
    ft2308_WriteReg(hlcdc, 0x3500, data, 1);
    data[0] = 0x00;
    ft2308_WriteReg(hlcdc, 0xc990, data, 1);

    ft2308_WriteReg(hlcdc, 0x1100, NULL, 0);
    LCD_DRIVER_DELAY_MS(120);

    ft2308_WriteReg(hlcdc, 0x2900, NULL, 0);
    data[0] = 0x23;
    data[1] = 0x08;
    data[2] = 0x01;
    data[3] = 0x01;
    ft2308_WriteReg(hlcdc, 0xFF00, data, 4);

    rt_kprintf("ft2308_Init done\r\n");
}
#endif


/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ft2308_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    data = ft2308_ReadData(hlcdc, 0xda00, 2);
    rt_kprintf("\nft2308_ReadID0 0x%x \n", data);

    data = ft2308_ReadData(hlcdc, ft2308_LCD_ID, 2);
    rt_kprintf("\nft2308_ReadID1 0x%x \n", data);

    data = ft2308_ReadData(hlcdc, 0xa800, 4);
    rt_kprintf("\nft2308_ReadID1 0x%x \n", data);

    data = ft2308_ID; //GC FAE: NOT support read id now
    return data;

}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ft2308_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    ft2308_WriteReg(hlcdc, ft2308_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ft2308_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    ft2308_WriteReg(hlcdc, ft2308_DISPLAY_OFF, (uint8_t *)NULL, 0);
    ft2308_WriteReg(hlcdc, ft2308_SLEEP_IN, (uint8_t *)NULL, 0);
}

void ft2308_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];

    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Xpos0 += COL_OFFSET;
    Xpos1 += COL_OFFSET;

    Ypos0 += ROW_OFFSET;
    Ypos1 += ROW_OFFSET;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    ft2308_WriteReg(hlcdc, ft2308_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    ft2308_WriteReg(hlcdc, ft2308_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ft2308_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;

    /* Set Cursor */
    ft2308_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ft2308_WriteReg(hlcdc, ft2308_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void ft2308_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    // rt_kprintf("ft2308_WriteMultiplePixels:RGBCode:%x,x1:%d,y1:%d,x2:%d,y2:%d\r\n", RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);

    if (0)
    {
    }
    else if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
//        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ((0x32 << 24) | (ft2308_WRITE_RAM << 8)), 4);
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ((0x32 << 24) | ft2308_WRITE_RAM), 4);
        //rt_kprintf("ft2308_WriteMultiplePixels:add:0x%x,add_len:%x\r\n", ((0x32 << 24) | ft2308_WRITE_RAM), 4);

    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, (ft2308_WRITE_RAM << 8), 2);
    }
    else
    {
        HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ft2308_WRITE_RAM, 1);
    }
}


/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ft2308_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
#if 0
    if (LCD_Reg == ft2308_CASET)
    {
        DEBUG_PRINTF("ft2308_SetX[%d,%d]\n", ((Parameters[0] << 8) | Parameters[1]),
                     ((Parameters[2] << 8) | Parameters[3]));
    }
    else if (LCD_Reg == ft2308_RASET)
    {
        DEBUG_PRINTF("ft2308_SetY[%d,%d]\n", ((Parameters[0] << 8) | Parameters[1]),
                     ((Parameters[2] << 8) | Parameters[3]));
    }
#endif

    if (0)
    {
    }
#ifdef BSP_LCDC_USING_DSI
    else if (HAL_LCDC_IS_DSI_IF(lcdc_int_cfg.lcd_itf))
    {
        HAL_LCDC_WriteDSIGenDatas(hlcdc, LCD_Reg, 1, Parameters, NbParameters);
    }
#endif /* BSP_LCDC_USING_DSI */
    else if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
        uint32_t cmd;

        // cmd = (0x02 << 24) | (LCD_Reg << 8);
        cmd = (0x02 << 24) | (LCD_Reg);

        if (0 != NbParameters)
        {
            /* Send command's parameters if any */
            HAL_LCDC_WriteU32Reg(hlcdc, cmd, Parameters, NbParameters);
//          rt_kprintf("ft2308_WriteReg:cmd:0x%x,data:0x%x,size:%x\r\n",cmd,Parameters,NbParameters);
        }
        else
        {
            uint32_t v = 0;
            HAL_LCDC_WriteU32Reg(hlcdc, cmd, (uint8_t *)&v, 1);
//          rt_kprintf("ft2308_WriteReg:cmd:0x%x,0x%x,%x\r\n",cmd,v,1);
        }

    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        uint8_t i;

        LCD_Reg = LCD_Reg << 8;

        if (0 == NbParameters)
        {
            HAL_LCDC_WriteU16Reg(hlcdc, LCD_Reg, NULL, 0);
        }
        else
        {
            for (i = 0; i < NbParameters; i++)
            {
                uint8_t v[2];
                v[0] = 0;
                v[1] = Parameters[i];

                HAL_LCDC_WriteU16Reg(hlcdc, LCD_Reg + i, v, 2);
            }
        }
    }
    else
    {
        HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
    }
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ft2308_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ft2308_ReadMode(hlcdc, true);
    if (0)
    {
    }
    else if (QAD_SPI_ITF == lcdc_int_cfg.lcd_itf)
    {
//        HAL_LCDC_ReadU32Reg(hlcdc, ((0x03 << 24) | (RegValue << 8)), (uint8_t *)&rd_data, ReadSize);
        HAL_LCDC_ReadU32Reg(hlcdc, ((0x03 << 24) | RegValue), (uint8_t *)&rd_data, ReadSize);

        rt_kprintf("ft2308_ReadData:reg:0x%x,data:0x%x,size:0x%x\r\n", ((0x03 << 24) | RegValue), rd_data, ReadSize);
    }
    else if (HAL_LCDC_IS_DBI_IF(lcdc_int_cfg.lcd_itf))
    {
        uint8_t i;

        RegValue = RegValue << 8;
        for (i = 0; i < ReadSize; i++)
        {
            uint16_t v;
            HAL_LCDC_ReadU16Reg(hlcdc, RegValue + i, (uint8_t *)&v, 1);

            rd_data = (rd_data << 8) | (v & 0xFF);
        }
    }
    else
    {
        HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);
    }
    ft2308_ReadMode(hlcdc, false);
    return rd_data;
}



uint32_t ft2308_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t  r, g, b;
    uint32_t ret_v, read_value;

    DEBUG_PRINTF("ft2308 NOT support read pixel!");

    return 0;


    DEBUG_PRINTF("ft2308_ReadPixel[%d,%d]\n", Xpos, Ypos);


    ft2308_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    read_value = ft2308_ReadData(hlcdc, ft2308_READ_RAM, 4);
    DEBUG_PRINTF("result: [%x]\n", read_value);

    b = (read_value >> 0) & 0xFF;
    g = (read_value >> 8) & 0xFF;
    r = (read_value >> 16) & 0xFF;

    DEBUG_PRINTF("r=%d, g=%d, b=%d \n", r, g, b);

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        ret_v = (uint32_t)(((r << 11) & 0xF800) | ((g << 5) & 0x7E0) | ((b >> 3) & 0X1F));
        break;

    /*
       (8bit R + 3bit dummy + 8bit G + 3bit dummy + 8bit B)

    */
    case LCDC_PIXEL_FORMAT_RGB888:
        ret_v = (uint32_t)(((r << 16) & 0xFF0000) | ((g << 8) & 0xFF00) | ((b) & 0XFF));
        break;

    default:
        RT_ASSERT(0);
        break;
    }


    //ft2308_WriteReg(hlcdc,ft2308_COLOR_MODE, parameter, 1);

    return ret_v;
}


void ft2308_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];


    /*

    Control interface color format
    ��011�� = 12bit/pixel ��101�� = 16bit/pixel ��110�� = 18bit/pixel ��111�� = 16M truncated

    */
    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x55;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;

    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        parameter[0] = 0x77;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
        break;

    default:
        return; //unsupport
        break;
    }

    ft2308_WriteReg(hlcdc, ft2308_COLOR_MODE, parameter, 1);

    uint32_t data = ft2308_ReadData(hlcdc, 0x0c00, 1);
    DEBUG_PRINTF("\nft2308_color_format 0x%x \n", data);

    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

#define ft2308_BRIGHTNESS_MAX 0xFF

void     ft2308_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((int)ft2308_BRIGHTNESS_MAX * br / 100);
    ft2308_WriteReg(hlcdc, ft2308_WBRIGHT, &bright, 1);
}



void  ft2308_TimeoutDbg(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    HAL_LCDC_Init(hlcdc);

    data = ft2308_ReadData(hlcdc, 0xda00, 2);
    rt_kprintf("\nft2308_ReadID0 0x%x \n", data);

    data = ft2308_ReadData(hlcdc, ft2308_LCD_ID, 2);
    rt_kprintf("\nft2308_ReadID1 0x%x \n", data);

    data = ft2308_ReadData(hlcdc, 0xa800, 4);
    rt_kprintf("\nft2308_ReadID1 0x%x \n", data);
}

void ft2308_TimeoutReset(LCDC_HandleTypeDef *hlcdc)
{
    BSP_LCD_Reset(0);//Reset LCD
    rt_thread_mdelay(100);
    BSP_LCD_Reset(1);
    rt_thread_mdelay(10);

    ft2308_Init(hlcdc);
}

