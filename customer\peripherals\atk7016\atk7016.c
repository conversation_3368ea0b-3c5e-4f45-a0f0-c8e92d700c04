/**
  ******************************************************************************
  * @file   atk7016.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for atk7016 LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "atk7016.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ATK7016
  * @brief This file provides a set of functions needed to drive the
  *        ATK7016 LCD.
  * @{
  */

/** @defgroup ATK7016_Private_TypesDefinitions
  * @{
  */

#ifdef ROW_OFFSET_PLUS
    #define ROW_OFFSET  (ROW_OFFSET_PLUS)
#else
    #define ROW_OFFSET  (0)
#endif

typedef struct
{
    rt_uint16_t width;
    rt_uint16_t height;
    rt_uint16_t id;
    rt_uint8_t  dir;            //Horizontal or vertical screen control: 0, vertical; 1, horizontal
    rt_uint16_t wramcmd;
    rt_uint16_t setxcmd;
    rt_uint16_t setycmd;
} lcd_info_t;


/**
  * @}
  */

/** @defgroup ATK7016_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ATK7016_Private_Macros
  * @{
  */
#define RGB_ARRAY_LEN (320)

//Definition of scan direction
#define L2R_U2D  0
#define L2R_D2U  1
#define R2L_U2D  2
#define R2L_D2U  3
#define U2D_L2R  4
#define U2D_R2L  5
#define D2U_L2R  6
#define D2U_R2L  7
#define DFT_SCAN_DIR  L2R_U2D


//#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

static lcd_info_t lcddev;

/**
  * @}
  */

/** @defgroup ATK7016_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ATK7016_drv =
{
    ATK7016_Init,
    ATK7016_ReadID,
    ATK7016_DisplayOn,
    ATK7016_DisplayOff,

    ATK7016_SetRegion,
    NULL,
    ATK7016_WriteMultiplePixels,

    NULL,

    ATK7016_SetColorMode,
    NULL,
    NULL,
    NULL
};



static LCDC_InitTypeDef lcdc_int_cfg =
{
    .lcd_itf = LCDC_INTF_DPI,
    .freq = 48 * 1000 * 1000,
    .color_mode = LCDC_PIXEL_FORMAT_RGB888,

    .cfg = {
        .dpi = {
            .PCLK_polarity = 0,
            .DE_polarity   = 0,
            .VS_polarity   = 1,
            .HS_polarity   = 1,

            .VS_width      = 3,    //VLW
            .HS_width      = 20,   //HLW

            .bank_col_head = 20,   //VBP
            .valid_columns = 600,
            .bank_col_tail = 12,   //VFP

            .bank_row_head = 140,  //HBP
            .valid_rows    = 1024,
            .bank_row_tail = 160,  //HFP

            .interrupt_line_num = 1,
        },
    },

};

LCD_DRIVER_EXPORT(atk7016, ATK7016_ID, &lcdc_int_cfg,
                  &ATK7016_drv,
                  ATK7016_LCD_PIXEL_WIDTH,
                  ATK7016_LCD_PIXEL_HEIGHT,
                  1);
/**
  * @}
  */

/** @defgroup ATK7016_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ATK7016_Private_Functions
  * @{
  */


/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ATK7016_Init(LCDC_HandleTypeDef *hlcdc)
{
    uint8_t   parameter[14];

    /* Initialize ATK7016 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    HAL_Delay_us(10);
    BSP_LCD_Reset(1);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ATK7016_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    return ATK7016_ID;
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ATK7016_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    //ATK7016_WriteReg(hlcdc, ATK7016_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ATK7016_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    //ATK7016_WriteReg(hlcdc, ATK7016_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void ATK7016_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    HAL_LCDC_SetROIArea(hlcdc, 0, 0, ATK7016_LCD_PIXEL_WIDTH - 1, ATK7016_LCD_PIXEL_HEIGHT - 1); //Not support partical columns
}




void ATK7016_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    HAL_LCDC_SendLayerData_IT(hlcdc);
}


void ATK7016_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];


    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;

    case RTGRAPHIC_PIXEL_FORMAT_RGB888:
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB888;
        break;

    default:
        return; //unsupport
        break;
    }

    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}




/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
