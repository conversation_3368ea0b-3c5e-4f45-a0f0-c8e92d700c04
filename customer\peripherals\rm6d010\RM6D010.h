/**
  ******************************************************************************
  * @file   RM6D010.h
  * <AUTHOR> software development team
  * @brief   This file contains all the functions prototypes for the RM6D010.c
  *          driver.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef __RM6D010_H
#define __RM6D010_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "../common/lcd.h"
#include "rtconfig.h"
/** @addtogroup BSP
  * @{
  */
/** @addtogroup Components
  * @{
  */

/** @addtogroup RM6D010
  * @{
  */

/** @defgroup RM6D010_Exported_Types
  * @{
  */
/**
  * @}
  */

/** @defgroup RM6D010_Exported_Constants
  * @{
  */

/**
  * @brief RM6D010 chip IDs
  */
#define RM6D010_ID                  0x1190a7

/**
  * @brief  RM6D010 Size
  */
#define  RM6D010_LCD_PIXEL_WIDTH    (412)
#define  RM6D010_LCD_PIXEL_HEIGHT   (412)

/**
 *  @brief LCD_OrientationTypeDef
 *  Possible values of Display Orientation
 */
#define RM6D010_ORIENTATION_PORTRAIT         (0x00) /* Portrait orientation choice of LCD screen  */
#define RM6D010_ORIENTATION_LANDSCAPE        (0x01) /* Landscape orientation choice of LCD screen */
#define RM6D010_ORIENTATION_LANDSCAPE_ROT180 (0x02) /* Landscape rotated 180 orientation choice of LCD screen */

/**
  * @brief  RM6D010 Registers
  */
#define RM6D010_SW_RESET           0x01
#define RM6D010_LCD_ID             0x04
#define RM6D010_READ_MODE          0x09
#define RM6D010_POWER_MODE         0x0A
#define RM6D010_SLEEP_IN           0x10
#define RM6D010_SLEEP_OUT          0x11
#define RM6D010_PARTIAL_DISPLAY    0x12
#define RM6D010_DISPLAY_INVERSION  0x21
#define RM6D010_DISPLAY_OFF        0x28
#define RM6D010_DISPLAY_ON         0x29
#define RM6D010_WRITE_RAM          0x2C
#define RM6D010_READ_RAM           0x2E
#define RM6D010_CASET              0x2A
#define RM6D010_RASET              0x2B
#define RM6D010_PART_CASET         0x30
#define RM6D010_PART_RASET         0x31
#define RM6D010_VSCRDEF            0x33 /* Vertical Scroll Definition */
#define RM6D010_VSCSAD             0x37 /* Vertical Scroll Start Address of RAM */
#define RM6D010_TEARING_EFFECT     0x35
#define RM6D010_NORMAL_DISPLAY     0x36
#define RM6D010_IDLE_MODE_OFF      0x38
#define RM6D010_IDLE_MODE_ON       0x39
#define RM6D010_COLOR_MODE         0x3A
#define RM6D010_CONTINUE_WRITE_RAM 0x3C
#define RM6D010_WBRIGHT            0x51 /* Write brightness*/
#define RM6D010_RBRIGHT            0x53 /* Read brightness*/
#define RM6D010_PORCH_CTRL         0xB2
#define RM6D010_FRAME_CTRL         0xB3
#define RM6D010_GATE_CTRL          0xB7
#define RM6D010_VCOM_SET           0xBB
#define RM6D010_LCM_CTRL           0xC0
#define RM6D010_SET_TIME_SRC       0xC2
#define RM6D010_SET_DISP_MODE      0xC4
#define RM6D010_VCOMH_OFFSET_SET   0xC5
#define RM6D010_FR_CTRL            0xC6
#define RM6D010_POWER_CTRL         0xD0
#define RM6D010_PV_GAMMA_CTRL      0xE0
#define RM6D010_NV_GAMMA_CTRL      0xE1
#define RM6D010_SPI2EN             0xE7

/**
  * @}
  */

/** @defgroup RM6D010_Exported_Functions
  * @{
  */
void     RM6D010_Init(LCDC_HandleTypeDef *hlcdc);
uint32_t RM6D010_ReadID(LCDC_HandleTypeDef *hlcdc);

void     RM6D010_DisplayOn(LCDC_HandleTypeDef *hlcdc);
void     RM6D010_DisplayOff(LCDC_HandleTypeDef *hlcdc);


void RM6D010_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);
void RM6D010_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode);
void RM6D010_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1);

uint32_t RM6D010_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos);
void RM6D010_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode);
void RM6D010_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t bright);

/* LCD driver structure */




#ifdef __cplusplus
}
#endif

#endif /* __RM6D010_H */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
