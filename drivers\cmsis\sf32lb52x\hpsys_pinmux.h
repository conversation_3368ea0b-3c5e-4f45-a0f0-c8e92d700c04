#ifndef __HPSYS_PINMUX_H
#define __HPSYS_PINMUX_H

typedef struct
{
__IO uint32_t PAD_SA00;
__IO uint32_t PAD_SA01;
__IO uint32_t PAD_SA02;
__IO uint32_t PAD_SA03;
__IO uint32_t PAD_SA04;
__IO uint32_t PAD_SA05;
__IO uint32_t PAD_SA06;
__IO uint32_t PAD_SA07;
__IO uint32_t PAD_SA08;
__IO uint32_t PAD_SA09;
__IO uint32_t PAD_SA10;
__IO uint32_t PAD_SA11;
__IO uint32_t PAD_SA12;
__IO uint32_t PAD_PA00;
__IO uint32_t PAD_PA01;
__IO uint32_t PAD_PA02;
__IO uint32_t PAD_PA03;
__IO uint32_t PAD_PA04;
__IO uint32_t PAD_PA05;
__IO uint32_t PAD_PA06;
__IO uint32_t PAD_PA07;
__IO uint32_t PAD_PA08;
__IO uint32_t PAD_PA09;
__IO uint32_t PAD_PA10;
__IO uint32_t PAD_PA11;
__IO uint32_t PAD_PA12;
__IO uint32_t PAD_PA13;
__IO uint32_t PAD_PA14;
__IO uint32_t PAD_PA15;
__IO uint32_t PAD_PA16;
__IO uint32_t PAD_PA17;
__IO uint32_t PAD_PA18;
__IO uint32_t PAD_PA19;
__IO uint32_t PAD_PA20;
__IO uint32_t PAD_PA21;
__IO uint32_t PAD_PA22;
__IO uint32_t PAD_PA23;
__IO uint32_t PAD_PA24;
__IO uint32_t PAD_PA25;
__IO uint32_t PAD_PA26;
__IO uint32_t PAD_PA27;
__IO uint32_t PAD_PA28;
__IO uint32_t PAD_PA29;
__IO uint32_t PAD_PA30;
__IO uint32_t PAD_PA31;
__IO uint32_t PAD_PA32;
__IO uint32_t PAD_PA33;
__IO uint32_t PAD_PA34;
__IO uint32_t PAD_PA35;
__IO uint32_t PAD_PA36;
__IO uint32_t PAD_PA37;
__IO uint32_t PAD_PA38;
__IO uint32_t PAD_PA39;
__IO uint32_t PAD_PA40;
__IO uint32_t PAD_PA41;
__IO uint32_t PAD_PA42;
__IO uint32_t PAD_PA43;
__IO uint32_t PAD_PA44;
} HPSYS_PINMUX_TypeDef;


/************* Bit definition for HPSYS_PINMUX_PAD_SA00 register **************/
#define HPSYS_PINMUX_PAD_SA00_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA00_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA00_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA00_FSEL      HPSYS_PINMUX_PAD_SA00_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA00_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA00_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA00_PE_Pos)
#define HPSYS_PINMUX_PAD_SA00_PE        HPSYS_PINMUX_PAD_SA00_PE_Msk
#define HPSYS_PINMUX_PAD_SA00_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA00_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA00_PS_Pos)
#define HPSYS_PINMUX_PAD_SA00_PS        HPSYS_PINMUX_PAD_SA00_PS_Msk
#define HPSYS_PINMUX_PAD_SA00_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA00_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA00_IE_Pos)
#define HPSYS_PINMUX_PAD_SA00_IE        HPSYS_PINMUX_PAD_SA00_IE_Msk
#define HPSYS_PINMUX_PAD_SA00_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA00_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA00_IS_Pos)
#define HPSYS_PINMUX_PAD_SA00_IS        HPSYS_PINMUX_PAD_SA00_IS_Msk
#define HPSYS_PINMUX_PAD_SA00_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA00_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA00_SR_Pos)
#define HPSYS_PINMUX_PAD_SA00_SR        HPSYS_PINMUX_PAD_SA00_SR_Msk
#define HPSYS_PINMUX_PAD_SA00_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA00_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA00_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA00_DS0       HPSYS_PINMUX_PAD_SA00_DS0_Msk
#define HPSYS_PINMUX_PAD_SA00_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA00_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA00_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA00_DS1       HPSYS_PINMUX_PAD_SA00_DS1_Msk
#define HPSYS_PINMUX_PAD_SA00_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA00_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA00_POE_Pos)
#define HPSYS_PINMUX_PAD_SA00_POE       HPSYS_PINMUX_PAD_SA00_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA01 register **************/
#define HPSYS_PINMUX_PAD_SA01_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA01_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA01_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA01_FSEL      HPSYS_PINMUX_PAD_SA01_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA01_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA01_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA01_PE_Pos)
#define HPSYS_PINMUX_PAD_SA01_PE        HPSYS_PINMUX_PAD_SA01_PE_Msk
#define HPSYS_PINMUX_PAD_SA01_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA01_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA01_PS_Pos)
#define HPSYS_PINMUX_PAD_SA01_PS        HPSYS_PINMUX_PAD_SA01_PS_Msk
#define HPSYS_PINMUX_PAD_SA01_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA01_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA01_IE_Pos)
#define HPSYS_PINMUX_PAD_SA01_IE        HPSYS_PINMUX_PAD_SA01_IE_Msk
#define HPSYS_PINMUX_PAD_SA01_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA01_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA01_IS_Pos)
#define HPSYS_PINMUX_PAD_SA01_IS        HPSYS_PINMUX_PAD_SA01_IS_Msk
#define HPSYS_PINMUX_PAD_SA01_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA01_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA01_SR_Pos)
#define HPSYS_PINMUX_PAD_SA01_SR        HPSYS_PINMUX_PAD_SA01_SR_Msk
#define HPSYS_PINMUX_PAD_SA01_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA01_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA01_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA01_DS0       HPSYS_PINMUX_PAD_SA01_DS0_Msk
#define HPSYS_PINMUX_PAD_SA01_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA01_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA01_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA01_DS1       HPSYS_PINMUX_PAD_SA01_DS1_Msk
#define HPSYS_PINMUX_PAD_SA01_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA01_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA01_POE_Pos)
#define HPSYS_PINMUX_PAD_SA01_POE       HPSYS_PINMUX_PAD_SA01_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA02 register **************/
#define HPSYS_PINMUX_PAD_SA02_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA02_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA02_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA02_FSEL      HPSYS_PINMUX_PAD_SA02_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA02_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA02_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA02_PE_Pos)
#define HPSYS_PINMUX_PAD_SA02_PE        HPSYS_PINMUX_PAD_SA02_PE_Msk
#define HPSYS_PINMUX_PAD_SA02_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA02_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA02_PS_Pos)
#define HPSYS_PINMUX_PAD_SA02_PS        HPSYS_PINMUX_PAD_SA02_PS_Msk
#define HPSYS_PINMUX_PAD_SA02_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA02_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA02_IE_Pos)
#define HPSYS_PINMUX_PAD_SA02_IE        HPSYS_PINMUX_PAD_SA02_IE_Msk
#define HPSYS_PINMUX_PAD_SA02_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA02_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA02_IS_Pos)
#define HPSYS_PINMUX_PAD_SA02_IS        HPSYS_PINMUX_PAD_SA02_IS_Msk
#define HPSYS_PINMUX_PAD_SA02_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA02_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA02_SR_Pos)
#define HPSYS_PINMUX_PAD_SA02_SR        HPSYS_PINMUX_PAD_SA02_SR_Msk
#define HPSYS_PINMUX_PAD_SA02_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA02_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA02_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA02_DS0       HPSYS_PINMUX_PAD_SA02_DS0_Msk
#define HPSYS_PINMUX_PAD_SA02_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA02_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA02_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA02_DS1       HPSYS_PINMUX_PAD_SA02_DS1_Msk
#define HPSYS_PINMUX_PAD_SA02_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA02_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA02_POE_Pos)
#define HPSYS_PINMUX_PAD_SA02_POE       HPSYS_PINMUX_PAD_SA02_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA03 register **************/
#define HPSYS_PINMUX_PAD_SA03_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA03_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA03_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA03_FSEL      HPSYS_PINMUX_PAD_SA03_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA03_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA03_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA03_PE_Pos)
#define HPSYS_PINMUX_PAD_SA03_PE        HPSYS_PINMUX_PAD_SA03_PE_Msk
#define HPSYS_PINMUX_PAD_SA03_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA03_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA03_PS_Pos)
#define HPSYS_PINMUX_PAD_SA03_PS        HPSYS_PINMUX_PAD_SA03_PS_Msk
#define HPSYS_PINMUX_PAD_SA03_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA03_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA03_IE_Pos)
#define HPSYS_PINMUX_PAD_SA03_IE        HPSYS_PINMUX_PAD_SA03_IE_Msk
#define HPSYS_PINMUX_PAD_SA03_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA03_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA03_IS_Pos)
#define HPSYS_PINMUX_PAD_SA03_IS        HPSYS_PINMUX_PAD_SA03_IS_Msk
#define HPSYS_PINMUX_PAD_SA03_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA03_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA03_SR_Pos)
#define HPSYS_PINMUX_PAD_SA03_SR        HPSYS_PINMUX_PAD_SA03_SR_Msk
#define HPSYS_PINMUX_PAD_SA03_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA03_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA03_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA03_DS0       HPSYS_PINMUX_PAD_SA03_DS0_Msk
#define HPSYS_PINMUX_PAD_SA03_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA03_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA03_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA03_DS1       HPSYS_PINMUX_PAD_SA03_DS1_Msk
#define HPSYS_PINMUX_PAD_SA03_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA03_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA03_POE_Pos)
#define HPSYS_PINMUX_PAD_SA03_POE       HPSYS_PINMUX_PAD_SA03_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA04 register **************/
#define HPSYS_PINMUX_PAD_SA04_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA04_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA04_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA04_FSEL      HPSYS_PINMUX_PAD_SA04_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA04_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA04_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA04_PE_Pos)
#define HPSYS_PINMUX_PAD_SA04_PE        HPSYS_PINMUX_PAD_SA04_PE_Msk
#define HPSYS_PINMUX_PAD_SA04_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA04_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA04_PS_Pos)
#define HPSYS_PINMUX_PAD_SA04_PS        HPSYS_PINMUX_PAD_SA04_PS_Msk
#define HPSYS_PINMUX_PAD_SA04_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA04_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA04_IE_Pos)
#define HPSYS_PINMUX_PAD_SA04_IE        HPSYS_PINMUX_PAD_SA04_IE_Msk
#define HPSYS_PINMUX_PAD_SA04_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA04_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA04_IS_Pos)
#define HPSYS_PINMUX_PAD_SA04_IS        HPSYS_PINMUX_PAD_SA04_IS_Msk
#define HPSYS_PINMUX_PAD_SA04_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA04_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA04_SR_Pos)
#define HPSYS_PINMUX_PAD_SA04_SR        HPSYS_PINMUX_PAD_SA04_SR_Msk
#define HPSYS_PINMUX_PAD_SA04_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA04_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA04_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA04_DS0       HPSYS_PINMUX_PAD_SA04_DS0_Msk
#define HPSYS_PINMUX_PAD_SA04_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA04_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA04_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA04_DS1       HPSYS_PINMUX_PAD_SA04_DS1_Msk
#define HPSYS_PINMUX_PAD_SA04_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA04_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA04_POE_Pos)
#define HPSYS_PINMUX_PAD_SA04_POE       HPSYS_PINMUX_PAD_SA04_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA05 register **************/
#define HPSYS_PINMUX_PAD_SA05_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA05_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA05_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA05_FSEL      HPSYS_PINMUX_PAD_SA05_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA05_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA05_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA05_PE_Pos)
#define HPSYS_PINMUX_PAD_SA05_PE        HPSYS_PINMUX_PAD_SA05_PE_Msk
#define HPSYS_PINMUX_PAD_SA05_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA05_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA05_PS_Pos)
#define HPSYS_PINMUX_PAD_SA05_PS        HPSYS_PINMUX_PAD_SA05_PS_Msk
#define HPSYS_PINMUX_PAD_SA05_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA05_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA05_IE_Pos)
#define HPSYS_PINMUX_PAD_SA05_IE        HPSYS_PINMUX_PAD_SA05_IE_Msk
#define HPSYS_PINMUX_PAD_SA05_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA05_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA05_IS_Pos)
#define HPSYS_PINMUX_PAD_SA05_IS        HPSYS_PINMUX_PAD_SA05_IS_Msk
#define HPSYS_PINMUX_PAD_SA05_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA05_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA05_SR_Pos)
#define HPSYS_PINMUX_PAD_SA05_SR        HPSYS_PINMUX_PAD_SA05_SR_Msk
#define HPSYS_PINMUX_PAD_SA05_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA05_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA05_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA05_DS0       HPSYS_PINMUX_PAD_SA05_DS0_Msk
#define HPSYS_PINMUX_PAD_SA05_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA05_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA05_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA05_DS1       HPSYS_PINMUX_PAD_SA05_DS1_Msk
#define HPSYS_PINMUX_PAD_SA05_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA05_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA05_POE_Pos)
#define HPSYS_PINMUX_PAD_SA05_POE       HPSYS_PINMUX_PAD_SA05_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA06 register **************/
#define HPSYS_PINMUX_PAD_SA06_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA06_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA06_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA06_FSEL      HPSYS_PINMUX_PAD_SA06_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA06_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA06_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA06_PE_Pos)
#define HPSYS_PINMUX_PAD_SA06_PE        HPSYS_PINMUX_PAD_SA06_PE_Msk
#define HPSYS_PINMUX_PAD_SA06_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA06_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA06_PS_Pos)
#define HPSYS_PINMUX_PAD_SA06_PS        HPSYS_PINMUX_PAD_SA06_PS_Msk
#define HPSYS_PINMUX_PAD_SA06_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA06_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA06_IE_Pos)
#define HPSYS_PINMUX_PAD_SA06_IE        HPSYS_PINMUX_PAD_SA06_IE_Msk
#define HPSYS_PINMUX_PAD_SA06_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA06_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA06_IS_Pos)
#define HPSYS_PINMUX_PAD_SA06_IS        HPSYS_PINMUX_PAD_SA06_IS_Msk
#define HPSYS_PINMUX_PAD_SA06_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA06_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA06_SR_Pos)
#define HPSYS_PINMUX_PAD_SA06_SR        HPSYS_PINMUX_PAD_SA06_SR_Msk
#define HPSYS_PINMUX_PAD_SA06_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA06_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA06_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA06_DS0       HPSYS_PINMUX_PAD_SA06_DS0_Msk
#define HPSYS_PINMUX_PAD_SA06_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA06_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA06_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA06_DS1       HPSYS_PINMUX_PAD_SA06_DS1_Msk
#define HPSYS_PINMUX_PAD_SA06_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA06_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA06_POE_Pos)
#define HPSYS_PINMUX_PAD_SA06_POE       HPSYS_PINMUX_PAD_SA06_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA07 register **************/
#define HPSYS_PINMUX_PAD_SA07_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA07_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA07_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA07_FSEL      HPSYS_PINMUX_PAD_SA07_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA07_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA07_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA07_PE_Pos)
#define HPSYS_PINMUX_PAD_SA07_PE        HPSYS_PINMUX_PAD_SA07_PE_Msk
#define HPSYS_PINMUX_PAD_SA07_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA07_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA07_PS_Pos)
#define HPSYS_PINMUX_PAD_SA07_PS        HPSYS_PINMUX_PAD_SA07_PS_Msk
#define HPSYS_PINMUX_PAD_SA07_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA07_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA07_IE_Pos)
#define HPSYS_PINMUX_PAD_SA07_IE        HPSYS_PINMUX_PAD_SA07_IE_Msk
#define HPSYS_PINMUX_PAD_SA07_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA07_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA07_IS_Pos)
#define HPSYS_PINMUX_PAD_SA07_IS        HPSYS_PINMUX_PAD_SA07_IS_Msk
#define HPSYS_PINMUX_PAD_SA07_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA07_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA07_SR_Pos)
#define HPSYS_PINMUX_PAD_SA07_SR        HPSYS_PINMUX_PAD_SA07_SR_Msk
#define HPSYS_PINMUX_PAD_SA07_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA07_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA07_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA07_DS0       HPSYS_PINMUX_PAD_SA07_DS0_Msk
#define HPSYS_PINMUX_PAD_SA07_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA07_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA07_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA07_DS1       HPSYS_PINMUX_PAD_SA07_DS1_Msk
#define HPSYS_PINMUX_PAD_SA07_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA07_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA07_POE_Pos)
#define HPSYS_PINMUX_PAD_SA07_POE       HPSYS_PINMUX_PAD_SA07_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA08 register **************/
#define HPSYS_PINMUX_PAD_SA08_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA08_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA08_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA08_FSEL      HPSYS_PINMUX_PAD_SA08_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA08_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA08_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA08_PE_Pos)
#define HPSYS_PINMUX_PAD_SA08_PE        HPSYS_PINMUX_PAD_SA08_PE_Msk
#define HPSYS_PINMUX_PAD_SA08_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA08_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA08_PS_Pos)
#define HPSYS_PINMUX_PAD_SA08_PS        HPSYS_PINMUX_PAD_SA08_PS_Msk
#define HPSYS_PINMUX_PAD_SA08_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA08_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA08_IE_Pos)
#define HPSYS_PINMUX_PAD_SA08_IE        HPSYS_PINMUX_PAD_SA08_IE_Msk
#define HPSYS_PINMUX_PAD_SA08_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA08_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA08_IS_Pos)
#define HPSYS_PINMUX_PAD_SA08_IS        HPSYS_PINMUX_PAD_SA08_IS_Msk
#define HPSYS_PINMUX_PAD_SA08_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA08_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA08_SR_Pos)
#define HPSYS_PINMUX_PAD_SA08_SR        HPSYS_PINMUX_PAD_SA08_SR_Msk
#define HPSYS_PINMUX_PAD_SA08_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA08_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA08_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA08_DS0       HPSYS_PINMUX_PAD_SA08_DS0_Msk
#define HPSYS_PINMUX_PAD_SA08_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA08_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA08_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA08_DS1       HPSYS_PINMUX_PAD_SA08_DS1_Msk
#define HPSYS_PINMUX_PAD_SA08_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA08_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA08_POE_Pos)
#define HPSYS_PINMUX_PAD_SA08_POE       HPSYS_PINMUX_PAD_SA08_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA09 register **************/
#define HPSYS_PINMUX_PAD_SA09_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA09_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA09_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA09_FSEL      HPSYS_PINMUX_PAD_SA09_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA09_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA09_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA09_PE_Pos)
#define HPSYS_PINMUX_PAD_SA09_PE        HPSYS_PINMUX_PAD_SA09_PE_Msk
#define HPSYS_PINMUX_PAD_SA09_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA09_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA09_PS_Pos)
#define HPSYS_PINMUX_PAD_SA09_PS        HPSYS_PINMUX_PAD_SA09_PS_Msk
#define HPSYS_PINMUX_PAD_SA09_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA09_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA09_IE_Pos)
#define HPSYS_PINMUX_PAD_SA09_IE        HPSYS_PINMUX_PAD_SA09_IE_Msk
#define HPSYS_PINMUX_PAD_SA09_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA09_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA09_IS_Pos)
#define HPSYS_PINMUX_PAD_SA09_IS        HPSYS_PINMUX_PAD_SA09_IS_Msk
#define HPSYS_PINMUX_PAD_SA09_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA09_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA09_SR_Pos)
#define HPSYS_PINMUX_PAD_SA09_SR        HPSYS_PINMUX_PAD_SA09_SR_Msk
#define HPSYS_PINMUX_PAD_SA09_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA09_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA09_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA09_DS0       HPSYS_PINMUX_PAD_SA09_DS0_Msk
#define HPSYS_PINMUX_PAD_SA09_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA09_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA09_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA09_DS1       HPSYS_PINMUX_PAD_SA09_DS1_Msk
#define HPSYS_PINMUX_PAD_SA09_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA09_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA09_POE_Pos)
#define HPSYS_PINMUX_PAD_SA09_POE       HPSYS_PINMUX_PAD_SA09_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA10 register **************/
#define HPSYS_PINMUX_PAD_SA10_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA10_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA10_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA10_FSEL      HPSYS_PINMUX_PAD_SA10_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA10_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA10_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA10_PE_Pos)
#define HPSYS_PINMUX_PAD_SA10_PE        HPSYS_PINMUX_PAD_SA10_PE_Msk
#define HPSYS_PINMUX_PAD_SA10_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA10_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA10_PS_Pos)
#define HPSYS_PINMUX_PAD_SA10_PS        HPSYS_PINMUX_PAD_SA10_PS_Msk
#define HPSYS_PINMUX_PAD_SA10_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA10_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA10_IE_Pos)
#define HPSYS_PINMUX_PAD_SA10_IE        HPSYS_PINMUX_PAD_SA10_IE_Msk
#define HPSYS_PINMUX_PAD_SA10_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA10_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA10_IS_Pos)
#define HPSYS_PINMUX_PAD_SA10_IS        HPSYS_PINMUX_PAD_SA10_IS_Msk
#define HPSYS_PINMUX_PAD_SA10_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA10_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA10_SR_Pos)
#define HPSYS_PINMUX_PAD_SA10_SR        HPSYS_PINMUX_PAD_SA10_SR_Msk
#define HPSYS_PINMUX_PAD_SA10_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA10_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA10_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA10_DS0       HPSYS_PINMUX_PAD_SA10_DS0_Msk
#define HPSYS_PINMUX_PAD_SA10_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA10_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA10_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA10_DS1       HPSYS_PINMUX_PAD_SA10_DS1_Msk
#define HPSYS_PINMUX_PAD_SA10_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA10_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA10_POE_Pos)
#define HPSYS_PINMUX_PAD_SA10_POE       HPSYS_PINMUX_PAD_SA10_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA11 register **************/
#define HPSYS_PINMUX_PAD_SA11_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA11_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA11_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA11_FSEL      HPSYS_PINMUX_PAD_SA11_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA11_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA11_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA11_PE_Pos)
#define HPSYS_PINMUX_PAD_SA11_PE        HPSYS_PINMUX_PAD_SA11_PE_Msk
#define HPSYS_PINMUX_PAD_SA11_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA11_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA11_PS_Pos)
#define HPSYS_PINMUX_PAD_SA11_PS        HPSYS_PINMUX_PAD_SA11_PS_Msk
#define HPSYS_PINMUX_PAD_SA11_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA11_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA11_IE_Pos)
#define HPSYS_PINMUX_PAD_SA11_IE        HPSYS_PINMUX_PAD_SA11_IE_Msk
#define HPSYS_PINMUX_PAD_SA11_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA11_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA11_IS_Pos)
#define HPSYS_PINMUX_PAD_SA11_IS        HPSYS_PINMUX_PAD_SA11_IS_Msk
#define HPSYS_PINMUX_PAD_SA11_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA11_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA11_SR_Pos)
#define HPSYS_PINMUX_PAD_SA11_SR        HPSYS_PINMUX_PAD_SA11_SR_Msk
#define HPSYS_PINMUX_PAD_SA11_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA11_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA11_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA11_DS0       HPSYS_PINMUX_PAD_SA11_DS0_Msk
#define HPSYS_PINMUX_PAD_SA11_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA11_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA11_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA11_DS1       HPSYS_PINMUX_PAD_SA11_DS1_Msk
#define HPSYS_PINMUX_PAD_SA11_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA11_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA11_POE_Pos)
#define HPSYS_PINMUX_PAD_SA11_POE       HPSYS_PINMUX_PAD_SA11_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_SA12 register **************/
#define HPSYS_PINMUX_PAD_SA12_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_SA12_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_SA12_FSEL_Pos)
#define HPSYS_PINMUX_PAD_SA12_FSEL      HPSYS_PINMUX_PAD_SA12_FSEL_Msk
#define HPSYS_PINMUX_PAD_SA12_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_SA12_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA12_PE_Pos)
#define HPSYS_PINMUX_PAD_SA12_PE        HPSYS_PINMUX_PAD_SA12_PE_Msk
#define HPSYS_PINMUX_PAD_SA12_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_SA12_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA12_PS_Pos)
#define HPSYS_PINMUX_PAD_SA12_PS        HPSYS_PINMUX_PAD_SA12_PS_Msk
#define HPSYS_PINMUX_PAD_SA12_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_SA12_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA12_IE_Pos)
#define HPSYS_PINMUX_PAD_SA12_IE        HPSYS_PINMUX_PAD_SA12_IE_Msk
#define HPSYS_PINMUX_PAD_SA12_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_SA12_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA12_IS_Pos)
#define HPSYS_PINMUX_PAD_SA12_IS        HPSYS_PINMUX_PAD_SA12_IS_Msk
#define HPSYS_PINMUX_PAD_SA12_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_SA12_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_SA12_SR_Pos)
#define HPSYS_PINMUX_PAD_SA12_SR        HPSYS_PINMUX_PAD_SA12_SR_Msk
#define HPSYS_PINMUX_PAD_SA12_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_SA12_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA12_DS0_Pos)
#define HPSYS_PINMUX_PAD_SA12_DS0       HPSYS_PINMUX_PAD_SA12_DS0_Msk
#define HPSYS_PINMUX_PAD_SA12_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_SA12_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA12_DS1_Pos)
#define HPSYS_PINMUX_PAD_SA12_DS1       HPSYS_PINMUX_PAD_SA12_DS1_Msk
#define HPSYS_PINMUX_PAD_SA12_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_SA12_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_SA12_POE_Pos)
#define HPSYS_PINMUX_PAD_SA12_POE       HPSYS_PINMUX_PAD_SA12_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA00 register **************/
#define HPSYS_PINMUX_PAD_PA00_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA00_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA00_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA00_FSEL      HPSYS_PINMUX_PAD_PA00_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA00_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA00_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_PE_Pos)
#define HPSYS_PINMUX_PAD_PA00_PE        HPSYS_PINMUX_PAD_PA00_PE_Msk
#define HPSYS_PINMUX_PAD_PA00_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA00_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_PS_Pos)
#define HPSYS_PINMUX_PAD_PA00_PS        HPSYS_PINMUX_PAD_PA00_PS_Msk
#define HPSYS_PINMUX_PAD_PA00_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA00_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_IE_Pos)
#define HPSYS_PINMUX_PAD_PA00_IE        HPSYS_PINMUX_PAD_PA00_IE_Msk
#define HPSYS_PINMUX_PAD_PA00_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA00_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_IS_Pos)
#define HPSYS_PINMUX_PAD_PA00_IS        HPSYS_PINMUX_PAD_PA00_IS_Msk
#define HPSYS_PINMUX_PAD_PA00_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA00_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA00_SR_Pos)
#define HPSYS_PINMUX_PAD_PA00_SR        HPSYS_PINMUX_PAD_PA00_SR_Msk
#define HPSYS_PINMUX_PAD_PA00_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA00_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA00_DS0       HPSYS_PINMUX_PAD_PA00_DS0_Msk
#define HPSYS_PINMUX_PAD_PA00_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA00_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA00_DS1       HPSYS_PINMUX_PAD_PA00_DS1_Msk
#define HPSYS_PINMUX_PAD_PA00_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA00_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA00_POE_Pos)
#define HPSYS_PINMUX_PAD_PA00_POE       HPSYS_PINMUX_PAD_PA00_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA01 register **************/
#define HPSYS_PINMUX_PAD_PA01_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA01_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA01_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA01_FSEL      HPSYS_PINMUX_PAD_PA01_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA01_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA01_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_PE_Pos)
#define HPSYS_PINMUX_PAD_PA01_PE        HPSYS_PINMUX_PAD_PA01_PE_Msk
#define HPSYS_PINMUX_PAD_PA01_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA01_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_PS_Pos)
#define HPSYS_PINMUX_PAD_PA01_PS        HPSYS_PINMUX_PAD_PA01_PS_Msk
#define HPSYS_PINMUX_PAD_PA01_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA01_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_IE_Pos)
#define HPSYS_PINMUX_PAD_PA01_IE        HPSYS_PINMUX_PAD_PA01_IE_Msk
#define HPSYS_PINMUX_PAD_PA01_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA01_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_IS_Pos)
#define HPSYS_PINMUX_PAD_PA01_IS        HPSYS_PINMUX_PAD_PA01_IS_Msk
#define HPSYS_PINMUX_PAD_PA01_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA01_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA01_SR_Pos)
#define HPSYS_PINMUX_PAD_PA01_SR        HPSYS_PINMUX_PAD_PA01_SR_Msk
#define HPSYS_PINMUX_PAD_PA01_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA01_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA01_DS0       HPSYS_PINMUX_PAD_PA01_DS0_Msk
#define HPSYS_PINMUX_PAD_PA01_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA01_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA01_DS1       HPSYS_PINMUX_PAD_PA01_DS1_Msk
#define HPSYS_PINMUX_PAD_PA01_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA01_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA01_POE_Pos)
#define HPSYS_PINMUX_PAD_PA01_POE       HPSYS_PINMUX_PAD_PA01_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA02 register **************/
#define HPSYS_PINMUX_PAD_PA02_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA02_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA02_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA02_FSEL      HPSYS_PINMUX_PAD_PA02_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA02_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA02_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_PE_Pos)
#define HPSYS_PINMUX_PAD_PA02_PE        HPSYS_PINMUX_PAD_PA02_PE_Msk
#define HPSYS_PINMUX_PAD_PA02_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA02_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_PS_Pos)
#define HPSYS_PINMUX_PAD_PA02_PS        HPSYS_PINMUX_PAD_PA02_PS_Msk
#define HPSYS_PINMUX_PAD_PA02_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA02_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_IE_Pos)
#define HPSYS_PINMUX_PAD_PA02_IE        HPSYS_PINMUX_PAD_PA02_IE_Msk
#define HPSYS_PINMUX_PAD_PA02_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA02_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_IS_Pos)
#define HPSYS_PINMUX_PAD_PA02_IS        HPSYS_PINMUX_PAD_PA02_IS_Msk
#define HPSYS_PINMUX_PAD_PA02_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA02_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA02_SR_Pos)
#define HPSYS_PINMUX_PAD_PA02_SR        HPSYS_PINMUX_PAD_PA02_SR_Msk
#define HPSYS_PINMUX_PAD_PA02_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA02_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA02_DS0       HPSYS_PINMUX_PAD_PA02_DS0_Msk
#define HPSYS_PINMUX_PAD_PA02_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA02_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA02_DS1       HPSYS_PINMUX_PAD_PA02_DS1_Msk
#define HPSYS_PINMUX_PAD_PA02_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA02_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA02_POE_Pos)
#define HPSYS_PINMUX_PAD_PA02_POE       HPSYS_PINMUX_PAD_PA02_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA03 register **************/
#define HPSYS_PINMUX_PAD_PA03_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA03_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA03_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA03_FSEL      HPSYS_PINMUX_PAD_PA03_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA03_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA03_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_PE_Pos)
#define HPSYS_PINMUX_PAD_PA03_PE        HPSYS_PINMUX_PAD_PA03_PE_Msk
#define HPSYS_PINMUX_PAD_PA03_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA03_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_PS_Pos)
#define HPSYS_PINMUX_PAD_PA03_PS        HPSYS_PINMUX_PAD_PA03_PS_Msk
#define HPSYS_PINMUX_PAD_PA03_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA03_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_IE_Pos)
#define HPSYS_PINMUX_PAD_PA03_IE        HPSYS_PINMUX_PAD_PA03_IE_Msk
#define HPSYS_PINMUX_PAD_PA03_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA03_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_IS_Pos)
#define HPSYS_PINMUX_PAD_PA03_IS        HPSYS_PINMUX_PAD_PA03_IS_Msk
#define HPSYS_PINMUX_PAD_PA03_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA03_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA03_SR_Pos)
#define HPSYS_PINMUX_PAD_PA03_SR        HPSYS_PINMUX_PAD_PA03_SR_Msk
#define HPSYS_PINMUX_PAD_PA03_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA03_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA03_DS0       HPSYS_PINMUX_PAD_PA03_DS0_Msk
#define HPSYS_PINMUX_PAD_PA03_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA03_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA03_DS1       HPSYS_PINMUX_PAD_PA03_DS1_Msk
#define HPSYS_PINMUX_PAD_PA03_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA03_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA03_POE_Pos)
#define HPSYS_PINMUX_PAD_PA03_POE       HPSYS_PINMUX_PAD_PA03_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA04 register **************/
#define HPSYS_PINMUX_PAD_PA04_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA04_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA04_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA04_FSEL      HPSYS_PINMUX_PAD_PA04_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA04_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA04_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_PE_Pos)
#define HPSYS_PINMUX_PAD_PA04_PE        HPSYS_PINMUX_PAD_PA04_PE_Msk
#define HPSYS_PINMUX_PAD_PA04_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA04_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_PS_Pos)
#define HPSYS_PINMUX_PAD_PA04_PS        HPSYS_PINMUX_PAD_PA04_PS_Msk
#define HPSYS_PINMUX_PAD_PA04_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA04_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_IE_Pos)
#define HPSYS_PINMUX_PAD_PA04_IE        HPSYS_PINMUX_PAD_PA04_IE_Msk
#define HPSYS_PINMUX_PAD_PA04_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA04_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_IS_Pos)
#define HPSYS_PINMUX_PAD_PA04_IS        HPSYS_PINMUX_PAD_PA04_IS_Msk
#define HPSYS_PINMUX_PAD_PA04_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA04_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA04_SR_Pos)
#define HPSYS_PINMUX_PAD_PA04_SR        HPSYS_PINMUX_PAD_PA04_SR_Msk
#define HPSYS_PINMUX_PAD_PA04_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA04_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA04_DS0       HPSYS_PINMUX_PAD_PA04_DS0_Msk
#define HPSYS_PINMUX_PAD_PA04_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA04_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA04_DS1       HPSYS_PINMUX_PAD_PA04_DS1_Msk
#define HPSYS_PINMUX_PAD_PA04_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA04_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA04_POE_Pos)
#define HPSYS_PINMUX_PAD_PA04_POE       HPSYS_PINMUX_PAD_PA04_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA05 register **************/
#define HPSYS_PINMUX_PAD_PA05_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA05_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA05_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA05_FSEL      HPSYS_PINMUX_PAD_PA05_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA05_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA05_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_PE_Pos)
#define HPSYS_PINMUX_PAD_PA05_PE        HPSYS_PINMUX_PAD_PA05_PE_Msk
#define HPSYS_PINMUX_PAD_PA05_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA05_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_PS_Pos)
#define HPSYS_PINMUX_PAD_PA05_PS        HPSYS_PINMUX_PAD_PA05_PS_Msk
#define HPSYS_PINMUX_PAD_PA05_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA05_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_IE_Pos)
#define HPSYS_PINMUX_PAD_PA05_IE        HPSYS_PINMUX_PAD_PA05_IE_Msk
#define HPSYS_PINMUX_PAD_PA05_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA05_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_IS_Pos)
#define HPSYS_PINMUX_PAD_PA05_IS        HPSYS_PINMUX_PAD_PA05_IS_Msk
#define HPSYS_PINMUX_PAD_PA05_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA05_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA05_SR_Pos)
#define HPSYS_PINMUX_PAD_PA05_SR        HPSYS_PINMUX_PAD_PA05_SR_Msk
#define HPSYS_PINMUX_PAD_PA05_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA05_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA05_DS0       HPSYS_PINMUX_PAD_PA05_DS0_Msk
#define HPSYS_PINMUX_PAD_PA05_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA05_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA05_DS1       HPSYS_PINMUX_PAD_PA05_DS1_Msk
#define HPSYS_PINMUX_PAD_PA05_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA05_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA05_POE_Pos)
#define HPSYS_PINMUX_PAD_PA05_POE       HPSYS_PINMUX_PAD_PA05_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA06 register **************/
#define HPSYS_PINMUX_PAD_PA06_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA06_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA06_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA06_FSEL      HPSYS_PINMUX_PAD_PA06_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA06_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA06_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_PE_Pos)
#define HPSYS_PINMUX_PAD_PA06_PE        HPSYS_PINMUX_PAD_PA06_PE_Msk
#define HPSYS_PINMUX_PAD_PA06_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA06_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_PS_Pos)
#define HPSYS_PINMUX_PAD_PA06_PS        HPSYS_PINMUX_PAD_PA06_PS_Msk
#define HPSYS_PINMUX_PAD_PA06_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA06_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_IE_Pos)
#define HPSYS_PINMUX_PAD_PA06_IE        HPSYS_PINMUX_PAD_PA06_IE_Msk
#define HPSYS_PINMUX_PAD_PA06_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA06_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_IS_Pos)
#define HPSYS_PINMUX_PAD_PA06_IS        HPSYS_PINMUX_PAD_PA06_IS_Msk
#define HPSYS_PINMUX_PAD_PA06_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA06_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA06_SR_Pos)
#define HPSYS_PINMUX_PAD_PA06_SR        HPSYS_PINMUX_PAD_PA06_SR_Msk
#define HPSYS_PINMUX_PAD_PA06_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA06_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA06_DS0       HPSYS_PINMUX_PAD_PA06_DS0_Msk
#define HPSYS_PINMUX_PAD_PA06_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA06_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA06_DS1       HPSYS_PINMUX_PAD_PA06_DS1_Msk
#define HPSYS_PINMUX_PAD_PA06_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA06_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA06_POE_Pos)
#define HPSYS_PINMUX_PAD_PA06_POE       HPSYS_PINMUX_PAD_PA06_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA07 register **************/
#define HPSYS_PINMUX_PAD_PA07_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA07_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA07_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA07_FSEL      HPSYS_PINMUX_PAD_PA07_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA07_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA07_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_PE_Pos)
#define HPSYS_PINMUX_PAD_PA07_PE        HPSYS_PINMUX_PAD_PA07_PE_Msk
#define HPSYS_PINMUX_PAD_PA07_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA07_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_PS_Pos)
#define HPSYS_PINMUX_PAD_PA07_PS        HPSYS_PINMUX_PAD_PA07_PS_Msk
#define HPSYS_PINMUX_PAD_PA07_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA07_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_IE_Pos)
#define HPSYS_PINMUX_PAD_PA07_IE        HPSYS_PINMUX_PAD_PA07_IE_Msk
#define HPSYS_PINMUX_PAD_PA07_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA07_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_IS_Pos)
#define HPSYS_PINMUX_PAD_PA07_IS        HPSYS_PINMUX_PAD_PA07_IS_Msk
#define HPSYS_PINMUX_PAD_PA07_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA07_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA07_SR_Pos)
#define HPSYS_PINMUX_PAD_PA07_SR        HPSYS_PINMUX_PAD_PA07_SR_Msk
#define HPSYS_PINMUX_PAD_PA07_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA07_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA07_DS0       HPSYS_PINMUX_PAD_PA07_DS0_Msk
#define HPSYS_PINMUX_PAD_PA07_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA07_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA07_DS1       HPSYS_PINMUX_PAD_PA07_DS1_Msk
#define HPSYS_PINMUX_PAD_PA07_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA07_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA07_POE_Pos)
#define HPSYS_PINMUX_PAD_PA07_POE       HPSYS_PINMUX_PAD_PA07_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA08 register **************/
#define HPSYS_PINMUX_PAD_PA08_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA08_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA08_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA08_FSEL      HPSYS_PINMUX_PAD_PA08_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA08_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA08_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_PE_Pos)
#define HPSYS_PINMUX_PAD_PA08_PE        HPSYS_PINMUX_PAD_PA08_PE_Msk
#define HPSYS_PINMUX_PAD_PA08_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA08_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_PS_Pos)
#define HPSYS_PINMUX_PAD_PA08_PS        HPSYS_PINMUX_PAD_PA08_PS_Msk
#define HPSYS_PINMUX_PAD_PA08_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA08_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_IE_Pos)
#define HPSYS_PINMUX_PAD_PA08_IE        HPSYS_PINMUX_PAD_PA08_IE_Msk
#define HPSYS_PINMUX_PAD_PA08_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA08_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_IS_Pos)
#define HPSYS_PINMUX_PAD_PA08_IS        HPSYS_PINMUX_PAD_PA08_IS_Msk
#define HPSYS_PINMUX_PAD_PA08_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA08_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA08_SR_Pos)
#define HPSYS_PINMUX_PAD_PA08_SR        HPSYS_PINMUX_PAD_PA08_SR_Msk
#define HPSYS_PINMUX_PAD_PA08_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA08_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA08_DS0       HPSYS_PINMUX_PAD_PA08_DS0_Msk
#define HPSYS_PINMUX_PAD_PA08_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA08_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA08_DS1       HPSYS_PINMUX_PAD_PA08_DS1_Msk
#define HPSYS_PINMUX_PAD_PA08_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA08_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA08_POE_Pos)
#define HPSYS_PINMUX_PAD_PA08_POE       HPSYS_PINMUX_PAD_PA08_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA09 register **************/
#define HPSYS_PINMUX_PAD_PA09_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA09_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA09_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA09_FSEL      HPSYS_PINMUX_PAD_PA09_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA09_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA09_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_PE_Pos)
#define HPSYS_PINMUX_PAD_PA09_PE        HPSYS_PINMUX_PAD_PA09_PE_Msk
#define HPSYS_PINMUX_PAD_PA09_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA09_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_PS_Pos)
#define HPSYS_PINMUX_PAD_PA09_PS        HPSYS_PINMUX_PAD_PA09_PS_Msk
#define HPSYS_PINMUX_PAD_PA09_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA09_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_IE_Pos)
#define HPSYS_PINMUX_PAD_PA09_IE        HPSYS_PINMUX_PAD_PA09_IE_Msk
#define HPSYS_PINMUX_PAD_PA09_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA09_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_IS_Pos)
#define HPSYS_PINMUX_PAD_PA09_IS        HPSYS_PINMUX_PAD_PA09_IS_Msk
#define HPSYS_PINMUX_PAD_PA09_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA09_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA09_SR_Pos)
#define HPSYS_PINMUX_PAD_PA09_SR        HPSYS_PINMUX_PAD_PA09_SR_Msk
#define HPSYS_PINMUX_PAD_PA09_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA09_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA09_DS0       HPSYS_PINMUX_PAD_PA09_DS0_Msk
#define HPSYS_PINMUX_PAD_PA09_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA09_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA09_DS1       HPSYS_PINMUX_PAD_PA09_DS1_Msk
#define HPSYS_PINMUX_PAD_PA09_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA09_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA09_POE_Pos)
#define HPSYS_PINMUX_PAD_PA09_POE       HPSYS_PINMUX_PAD_PA09_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA10 register **************/
#define HPSYS_PINMUX_PAD_PA10_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA10_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA10_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA10_FSEL      HPSYS_PINMUX_PAD_PA10_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA10_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA10_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_PE_Pos)
#define HPSYS_PINMUX_PAD_PA10_PE        HPSYS_PINMUX_PAD_PA10_PE_Msk
#define HPSYS_PINMUX_PAD_PA10_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA10_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_PS_Pos)
#define HPSYS_PINMUX_PAD_PA10_PS        HPSYS_PINMUX_PAD_PA10_PS_Msk
#define HPSYS_PINMUX_PAD_PA10_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA10_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_IE_Pos)
#define HPSYS_PINMUX_PAD_PA10_IE        HPSYS_PINMUX_PAD_PA10_IE_Msk
#define HPSYS_PINMUX_PAD_PA10_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA10_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_IS_Pos)
#define HPSYS_PINMUX_PAD_PA10_IS        HPSYS_PINMUX_PAD_PA10_IS_Msk
#define HPSYS_PINMUX_PAD_PA10_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA10_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA10_SR_Pos)
#define HPSYS_PINMUX_PAD_PA10_SR        HPSYS_PINMUX_PAD_PA10_SR_Msk
#define HPSYS_PINMUX_PAD_PA10_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA10_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA10_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA10_DS0       HPSYS_PINMUX_PAD_PA10_DS0_Msk
#define HPSYS_PINMUX_PAD_PA10_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA10_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA10_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA10_DS1       HPSYS_PINMUX_PAD_PA10_DS1_Msk
#define HPSYS_PINMUX_PAD_PA10_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA10_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA10_POE_Pos)
#define HPSYS_PINMUX_PAD_PA10_POE       HPSYS_PINMUX_PAD_PA10_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA11 register **************/
#define HPSYS_PINMUX_PAD_PA11_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA11_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA11_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA11_FSEL      HPSYS_PINMUX_PAD_PA11_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA11_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA11_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_PE_Pos)
#define HPSYS_PINMUX_PAD_PA11_PE        HPSYS_PINMUX_PAD_PA11_PE_Msk
#define HPSYS_PINMUX_PAD_PA11_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA11_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_PS_Pos)
#define HPSYS_PINMUX_PAD_PA11_PS        HPSYS_PINMUX_PAD_PA11_PS_Msk
#define HPSYS_PINMUX_PAD_PA11_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA11_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_IE_Pos)
#define HPSYS_PINMUX_PAD_PA11_IE        HPSYS_PINMUX_PAD_PA11_IE_Msk
#define HPSYS_PINMUX_PAD_PA11_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA11_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_IS_Pos)
#define HPSYS_PINMUX_PAD_PA11_IS        HPSYS_PINMUX_PAD_PA11_IS_Msk
#define HPSYS_PINMUX_PAD_PA11_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA11_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA11_SR_Pos)
#define HPSYS_PINMUX_PAD_PA11_SR        HPSYS_PINMUX_PAD_PA11_SR_Msk
#define HPSYS_PINMUX_PAD_PA11_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA11_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA11_DS0       HPSYS_PINMUX_PAD_PA11_DS0_Msk
#define HPSYS_PINMUX_PAD_PA11_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA11_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA11_DS1       HPSYS_PINMUX_PAD_PA11_DS1_Msk
#define HPSYS_PINMUX_PAD_PA11_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA11_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA11_POE_Pos)
#define HPSYS_PINMUX_PAD_PA11_POE       HPSYS_PINMUX_PAD_PA11_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA12 register **************/
#define HPSYS_PINMUX_PAD_PA12_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA12_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA12_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA12_FSEL      HPSYS_PINMUX_PAD_PA12_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA12_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA12_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_PE_Pos)
#define HPSYS_PINMUX_PAD_PA12_PE        HPSYS_PINMUX_PAD_PA12_PE_Msk
#define HPSYS_PINMUX_PAD_PA12_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA12_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_PS_Pos)
#define HPSYS_PINMUX_PAD_PA12_PS        HPSYS_PINMUX_PAD_PA12_PS_Msk
#define HPSYS_PINMUX_PAD_PA12_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA12_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_IE_Pos)
#define HPSYS_PINMUX_PAD_PA12_IE        HPSYS_PINMUX_PAD_PA12_IE_Msk
#define HPSYS_PINMUX_PAD_PA12_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA12_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_IS_Pos)
#define HPSYS_PINMUX_PAD_PA12_IS        HPSYS_PINMUX_PAD_PA12_IS_Msk
#define HPSYS_PINMUX_PAD_PA12_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA12_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA12_SR_Pos)
#define HPSYS_PINMUX_PAD_PA12_SR        HPSYS_PINMUX_PAD_PA12_SR_Msk
#define HPSYS_PINMUX_PAD_PA12_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA12_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA12_DS0       HPSYS_PINMUX_PAD_PA12_DS0_Msk
#define HPSYS_PINMUX_PAD_PA12_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA12_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA12_DS1       HPSYS_PINMUX_PAD_PA12_DS1_Msk
#define HPSYS_PINMUX_PAD_PA12_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA12_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA12_POE_Pos)
#define HPSYS_PINMUX_PAD_PA12_POE       HPSYS_PINMUX_PAD_PA12_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA13 register **************/
#define HPSYS_PINMUX_PAD_PA13_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA13_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA13_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA13_FSEL      HPSYS_PINMUX_PAD_PA13_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA13_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA13_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_PE_Pos)
#define HPSYS_PINMUX_PAD_PA13_PE        HPSYS_PINMUX_PAD_PA13_PE_Msk
#define HPSYS_PINMUX_PAD_PA13_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA13_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_PS_Pos)
#define HPSYS_PINMUX_PAD_PA13_PS        HPSYS_PINMUX_PAD_PA13_PS_Msk
#define HPSYS_PINMUX_PAD_PA13_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA13_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_IE_Pos)
#define HPSYS_PINMUX_PAD_PA13_IE        HPSYS_PINMUX_PAD_PA13_IE_Msk
#define HPSYS_PINMUX_PAD_PA13_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA13_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_IS_Pos)
#define HPSYS_PINMUX_PAD_PA13_IS        HPSYS_PINMUX_PAD_PA13_IS_Msk
#define HPSYS_PINMUX_PAD_PA13_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA13_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA13_SR_Pos)
#define HPSYS_PINMUX_PAD_PA13_SR        HPSYS_PINMUX_PAD_PA13_SR_Msk
#define HPSYS_PINMUX_PAD_PA13_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA13_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA13_DS0       HPSYS_PINMUX_PAD_PA13_DS0_Msk
#define HPSYS_PINMUX_PAD_PA13_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA13_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA13_DS1       HPSYS_PINMUX_PAD_PA13_DS1_Msk
#define HPSYS_PINMUX_PAD_PA13_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA13_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA13_POE_Pos)
#define HPSYS_PINMUX_PAD_PA13_POE       HPSYS_PINMUX_PAD_PA13_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA14 register **************/
#define HPSYS_PINMUX_PAD_PA14_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA14_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA14_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA14_FSEL      HPSYS_PINMUX_PAD_PA14_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA14_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA14_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_PE_Pos)
#define HPSYS_PINMUX_PAD_PA14_PE        HPSYS_PINMUX_PAD_PA14_PE_Msk
#define HPSYS_PINMUX_PAD_PA14_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA14_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_PS_Pos)
#define HPSYS_PINMUX_PAD_PA14_PS        HPSYS_PINMUX_PAD_PA14_PS_Msk
#define HPSYS_PINMUX_PAD_PA14_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA14_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_IE_Pos)
#define HPSYS_PINMUX_PAD_PA14_IE        HPSYS_PINMUX_PAD_PA14_IE_Msk
#define HPSYS_PINMUX_PAD_PA14_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA14_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_IS_Pos)
#define HPSYS_PINMUX_PAD_PA14_IS        HPSYS_PINMUX_PAD_PA14_IS_Msk
#define HPSYS_PINMUX_PAD_PA14_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA14_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA14_SR_Pos)
#define HPSYS_PINMUX_PAD_PA14_SR        HPSYS_PINMUX_PAD_PA14_SR_Msk
#define HPSYS_PINMUX_PAD_PA14_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA14_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA14_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA14_DS0       HPSYS_PINMUX_PAD_PA14_DS0_Msk
#define HPSYS_PINMUX_PAD_PA14_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA14_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA14_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA14_DS1       HPSYS_PINMUX_PAD_PA14_DS1_Msk
#define HPSYS_PINMUX_PAD_PA14_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA14_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA14_POE_Pos)
#define HPSYS_PINMUX_PAD_PA14_POE       HPSYS_PINMUX_PAD_PA14_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA15 register **************/
#define HPSYS_PINMUX_PAD_PA15_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA15_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA15_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA15_FSEL      HPSYS_PINMUX_PAD_PA15_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA15_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA15_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_PE_Pos)
#define HPSYS_PINMUX_PAD_PA15_PE        HPSYS_PINMUX_PAD_PA15_PE_Msk
#define HPSYS_PINMUX_PAD_PA15_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA15_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_PS_Pos)
#define HPSYS_PINMUX_PAD_PA15_PS        HPSYS_PINMUX_PAD_PA15_PS_Msk
#define HPSYS_PINMUX_PAD_PA15_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA15_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_IE_Pos)
#define HPSYS_PINMUX_PAD_PA15_IE        HPSYS_PINMUX_PAD_PA15_IE_Msk
#define HPSYS_PINMUX_PAD_PA15_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA15_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_IS_Pos)
#define HPSYS_PINMUX_PAD_PA15_IS        HPSYS_PINMUX_PAD_PA15_IS_Msk
#define HPSYS_PINMUX_PAD_PA15_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA15_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA15_SR_Pos)
#define HPSYS_PINMUX_PAD_PA15_SR        HPSYS_PINMUX_PAD_PA15_SR_Msk
#define HPSYS_PINMUX_PAD_PA15_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA15_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA15_DS0       HPSYS_PINMUX_PAD_PA15_DS0_Msk
#define HPSYS_PINMUX_PAD_PA15_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA15_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA15_DS1       HPSYS_PINMUX_PAD_PA15_DS1_Msk
#define HPSYS_PINMUX_PAD_PA15_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA15_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA15_POE_Pos)
#define HPSYS_PINMUX_PAD_PA15_POE       HPSYS_PINMUX_PAD_PA15_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA16 register **************/
#define HPSYS_PINMUX_PAD_PA16_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA16_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA16_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA16_FSEL      HPSYS_PINMUX_PAD_PA16_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA16_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA16_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_PE_Pos)
#define HPSYS_PINMUX_PAD_PA16_PE        HPSYS_PINMUX_PAD_PA16_PE_Msk
#define HPSYS_PINMUX_PAD_PA16_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA16_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_PS_Pos)
#define HPSYS_PINMUX_PAD_PA16_PS        HPSYS_PINMUX_PAD_PA16_PS_Msk
#define HPSYS_PINMUX_PAD_PA16_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA16_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_IE_Pos)
#define HPSYS_PINMUX_PAD_PA16_IE        HPSYS_PINMUX_PAD_PA16_IE_Msk
#define HPSYS_PINMUX_PAD_PA16_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA16_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_IS_Pos)
#define HPSYS_PINMUX_PAD_PA16_IS        HPSYS_PINMUX_PAD_PA16_IS_Msk
#define HPSYS_PINMUX_PAD_PA16_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA16_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA16_SR_Pos)
#define HPSYS_PINMUX_PAD_PA16_SR        HPSYS_PINMUX_PAD_PA16_SR_Msk
#define HPSYS_PINMUX_PAD_PA16_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA16_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA16_DS0       HPSYS_PINMUX_PAD_PA16_DS0_Msk
#define HPSYS_PINMUX_PAD_PA16_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA16_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA16_DS1       HPSYS_PINMUX_PAD_PA16_DS1_Msk
#define HPSYS_PINMUX_PAD_PA16_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA16_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA16_POE_Pos)
#define HPSYS_PINMUX_PAD_PA16_POE       HPSYS_PINMUX_PAD_PA16_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA17 register **************/
#define HPSYS_PINMUX_PAD_PA17_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA17_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA17_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA17_FSEL      HPSYS_PINMUX_PAD_PA17_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA17_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA17_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_PE_Pos)
#define HPSYS_PINMUX_PAD_PA17_PE        HPSYS_PINMUX_PAD_PA17_PE_Msk
#define HPSYS_PINMUX_PAD_PA17_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA17_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_PS_Pos)
#define HPSYS_PINMUX_PAD_PA17_PS        HPSYS_PINMUX_PAD_PA17_PS_Msk
#define HPSYS_PINMUX_PAD_PA17_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA17_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_IE_Pos)
#define HPSYS_PINMUX_PAD_PA17_IE        HPSYS_PINMUX_PAD_PA17_IE_Msk
#define HPSYS_PINMUX_PAD_PA17_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA17_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_IS_Pos)
#define HPSYS_PINMUX_PAD_PA17_IS        HPSYS_PINMUX_PAD_PA17_IS_Msk
#define HPSYS_PINMUX_PAD_PA17_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA17_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA17_SR_Pos)
#define HPSYS_PINMUX_PAD_PA17_SR        HPSYS_PINMUX_PAD_PA17_SR_Msk
#define HPSYS_PINMUX_PAD_PA17_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA17_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA17_DS0       HPSYS_PINMUX_PAD_PA17_DS0_Msk
#define HPSYS_PINMUX_PAD_PA17_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA17_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA17_DS1       HPSYS_PINMUX_PAD_PA17_DS1_Msk
#define HPSYS_PINMUX_PAD_PA17_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA17_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA17_POE_Pos)
#define HPSYS_PINMUX_PAD_PA17_POE       HPSYS_PINMUX_PAD_PA17_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA18 register **************/
#define HPSYS_PINMUX_PAD_PA18_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA18_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA18_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA18_FSEL      HPSYS_PINMUX_PAD_PA18_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA18_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA18_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_PE_Pos)
#define HPSYS_PINMUX_PAD_PA18_PE        HPSYS_PINMUX_PAD_PA18_PE_Msk
#define HPSYS_PINMUX_PAD_PA18_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA18_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_PS_Pos)
#define HPSYS_PINMUX_PAD_PA18_PS        HPSYS_PINMUX_PAD_PA18_PS_Msk
#define HPSYS_PINMUX_PAD_PA18_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA18_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_IE_Pos)
#define HPSYS_PINMUX_PAD_PA18_IE        HPSYS_PINMUX_PAD_PA18_IE_Msk
#define HPSYS_PINMUX_PAD_PA18_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA18_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_IS_Pos)
#define HPSYS_PINMUX_PAD_PA18_IS        HPSYS_PINMUX_PAD_PA18_IS_Msk
#define HPSYS_PINMUX_PAD_PA18_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA18_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA18_SR_Pos)
#define HPSYS_PINMUX_PAD_PA18_SR        HPSYS_PINMUX_PAD_PA18_SR_Msk
#define HPSYS_PINMUX_PAD_PA18_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA18_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA18_DS0       HPSYS_PINMUX_PAD_PA18_DS0_Msk
#define HPSYS_PINMUX_PAD_PA18_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA18_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA18_DS1       HPSYS_PINMUX_PAD_PA18_DS1_Msk
#define HPSYS_PINMUX_PAD_PA18_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA18_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA18_POE_Pos)
#define HPSYS_PINMUX_PAD_PA18_POE       HPSYS_PINMUX_PAD_PA18_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA19 register **************/
#define HPSYS_PINMUX_PAD_PA19_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA19_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA19_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA19_FSEL      HPSYS_PINMUX_PAD_PA19_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA19_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA19_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_PE_Pos)
#define HPSYS_PINMUX_PAD_PA19_PE        HPSYS_PINMUX_PAD_PA19_PE_Msk
#define HPSYS_PINMUX_PAD_PA19_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA19_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_PS_Pos)
#define HPSYS_PINMUX_PAD_PA19_PS        HPSYS_PINMUX_PAD_PA19_PS_Msk
#define HPSYS_PINMUX_PAD_PA19_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA19_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_IE_Pos)
#define HPSYS_PINMUX_PAD_PA19_IE        HPSYS_PINMUX_PAD_PA19_IE_Msk
#define HPSYS_PINMUX_PAD_PA19_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA19_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_IS_Pos)
#define HPSYS_PINMUX_PAD_PA19_IS        HPSYS_PINMUX_PAD_PA19_IS_Msk
#define HPSYS_PINMUX_PAD_PA19_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA19_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA19_SR_Pos)
#define HPSYS_PINMUX_PAD_PA19_SR        HPSYS_PINMUX_PAD_PA19_SR_Msk
#define HPSYS_PINMUX_PAD_PA19_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA19_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA19_DS0       HPSYS_PINMUX_PAD_PA19_DS0_Msk
#define HPSYS_PINMUX_PAD_PA19_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA19_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA19_DS1       HPSYS_PINMUX_PAD_PA19_DS1_Msk
#define HPSYS_PINMUX_PAD_PA19_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA19_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA19_POE_Pos)
#define HPSYS_PINMUX_PAD_PA19_POE       HPSYS_PINMUX_PAD_PA19_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA20 register **************/
#define HPSYS_PINMUX_PAD_PA20_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA20_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA20_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA20_FSEL      HPSYS_PINMUX_PAD_PA20_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA20_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA20_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_PE_Pos)
#define HPSYS_PINMUX_PAD_PA20_PE        HPSYS_PINMUX_PAD_PA20_PE_Msk
#define HPSYS_PINMUX_PAD_PA20_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA20_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_PS_Pos)
#define HPSYS_PINMUX_PAD_PA20_PS        HPSYS_PINMUX_PAD_PA20_PS_Msk
#define HPSYS_PINMUX_PAD_PA20_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA20_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_IE_Pos)
#define HPSYS_PINMUX_PAD_PA20_IE        HPSYS_PINMUX_PAD_PA20_IE_Msk
#define HPSYS_PINMUX_PAD_PA20_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA20_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_IS_Pos)
#define HPSYS_PINMUX_PAD_PA20_IS        HPSYS_PINMUX_PAD_PA20_IS_Msk
#define HPSYS_PINMUX_PAD_PA20_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA20_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA20_SR_Pos)
#define HPSYS_PINMUX_PAD_PA20_SR        HPSYS_PINMUX_PAD_PA20_SR_Msk
#define HPSYS_PINMUX_PAD_PA20_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA20_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA20_DS0       HPSYS_PINMUX_PAD_PA20_DS0_Msk
#define HPSYS_PINMUX_PAD_PA20_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA20_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA20_DS1       HPSYS_PINMUX_PAD_PA20_DS1_Msk
#define HPSYS_PINMUX_PAD_PA20_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA20_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA20_POE_Pos)
#define HPSYS_PINMUX_PAD_PA20_POE       HPSYS_PINMUX_PAD_PA20_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA21 register **************/
#define HPSYS_PINMUX_PAD_PA21_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA21_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA21_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA21_FSEL      HPSYS_PINMUX_PAD_PA21_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA21_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA21_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_PE_Pos)
#define HPSYS_PINMUX_PAD_PA21_PE        HPSYS_PINMUX_PAD_PA21_PE_Msk
#define HPSYS_PINMUX_PAD_PA21_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA21_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_PS_Pos)
#define HPSYS_PINMUX_PAD_PA21_PS        HPSYS_PINMUX_PAD_PA21_PS_Msk
#define HPSYS_PINMUX_PAD_PA21_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA21_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_IE_Pos)
#define HPSYS_PINMUX_PAD_PA21_IE        HPSYS_PINMUX_PAD_PA21_IE_Msk
#define HPSYS_PINMUX_PAD_PA21_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA21_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_IS_Pos)
#define HPSYS_PINMUX_PAD_PA21_IS        HPSYS_PINMUX_PAD_PA21_IS_Msk
#define HPSYS_PINMUX_PAD_PA21_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA21_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA21_SR_Pos)
#define HPSYS_PINMUX_PAD_PA21_SR        HPSYS_PINMUX_PAD_PA21_SR_Msk
#define HPSYS_PINMUX_PAD_PA21_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA21_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA21_DS0       HPSYS_PINMUX_PAD_PA21_DS0_Msk
#define HPSYS_PINMUX_PAD_PA21_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA21_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA21_DS1       HPSYS_PINMUX_PAD_PA21_DS1_Msk
#define HPSYS_PINMUX_PAD_PA21_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA21_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA21_POE_Pos)
#define HPSYS_PINMUX_PAD_PA21_POE       HPSYS_PINMUX_PAD_PA21_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA22 register **************/
#define HPSYS_PINMUX_PAD_PA22_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA22_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA22_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA22_FSEL      HPSYS_PINMUX_PAD_PA22_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA22_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA22_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_PE_Pos)
#define HPSYS_PINMUX_PAD_PA22_PE        HPSYS_PINMUX_PAD_PA22_PE_Msk
#define HPSYS_PINMUX_PAD_PA22_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA22_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_PS_Pos)
#define HPSYS_PINMUX_PAD_PA22_PS        HPSYS_PINMUX_PAD_PA22_PS_Msk
#define HPSYS_PINMUX_PAD_PA22_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA22_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_IE_Pos)
#define HPSYS_PINMUX_PAD_PA22_IE        HPSYS_PINMUX_PAD_PA22_IE_Msk
#define HPSYS_PINMUX_PAD_PA22_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA22_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_IS_Pos)
#define HPSYS_PINMUX_PAD_PA22_IS        HPSYS_PINMUX_PAD_PA22_IS_Msk
#define HPSYS_PINMUX_PAD_PA22_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA22_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA22_SR_Pos)
#define HPSYS_PINMUX_PAD_PA22_SR        HPSYS_PINMUX_PAD_PA22_SR_Msk
#define HPSYS_PINMUX_PAD_PA22_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA22_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA22_DS0       HPSYS_PINMUX_PAD_PA22_DS0_Msk
#define HPSYS_PINMUX_PAD_PA22_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA22_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA22_DS1       HPSYS_PINMUX_PAD_PA22_DS1_Msk
#define HPSYS_PINMUX_PAD_PA22_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA22_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA22_POE_Pos)
#define HPSYS_PINMUX_PAD_PA22_POE       HPSYS_PINMUX_PAD_PA22_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA23 register **************/
#define HPSYS_PINMUX_PAD_PA23_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA23_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA23_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA23_FSEL      HPSYS_PINMUX_PAD_PA23_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA23_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA23_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_PE_Pos)
#define HPSYS_PINMUX_PAD_PA23_PE        HPSYS_PINMUX_PAD_PA23_PE_Msk
#define HPSYS_PINMUX_PAD_PA23_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA23_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_PS_Pos)
#define HPSYS_PINMUX_PAD_PA23_PS        HPSYS_PINMUX_PAD_PA23_PS_Msk
#define HPSYS_PINMUX_PAD_PA23_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA23_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_IE_Pos)
#define HPSYS_PINMUX_PAD_PA23_IE        HPSYS_PINMUX_PAD_PA23_IE_Msk
#define HPSYS_PINMUX_PAD_PA23_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA23_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_IS_Pos)
#define HPSYS_PINMUX_PAD_PA23_IS        HPSYS_PINMUX_PAD_PA23_IS_Msk
#define HPSYS_PINMUX_PAD_PA23_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA23_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA23_SR_Pos)
#define HPSYS_PINMUX_PAD_PA23_SR        HPSYS_PINMUX_PAD_PA23_SR_Msk
#define HPSYS_PINMUX_PAD_PA23_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA23_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA23_DS0       HPSYS_PINMUX_PAD_PA23_DS0_Msk
#define HPSYS_PINMUX_PAD_PA23_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA23_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA23_DS1       HPSYS_PINMUX_PAD_PA23_DS1_Msk
#define HPSYS_PINMUX_PAD_PA23_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA23_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA23_POE_Pos)
#define HPSYS_PINMUX_PAD_PA23_POE       HPSYS_PINMUX_PAD_PA23_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA24 register **************/
#define HPSYS_PINMUX_PAD_PA24_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA24_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA24_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA24_FSEL      HPSYS_PINMUX_PAD_PA24_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA24_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA24_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_PE_Pos)
#define HPSYS_PINMUX_PAD_PA24_PE        HPSYS_PINMUX_PAD_PA24_PE_Msk
#define HPSYS_PINMUX_PAD_PA24_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA24_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_PS_Pos)
#define HPSYS_PINMUX_PAD_PA24_PS        HPSYS_PINMUX_PAD_PA24_PS_Msk
#define HPSYS_PINMUX_PAD_PA24_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA24_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_IE_Pos)
#define HPSYS_PINMUX_PAD_PA24_IE        HPSYS_PINMUX_PAD_PA24_IE_Msk
#define HPSYS_PINMUX_PAD_PA24_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA24_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_IS_Pos)
#define HPSYS_PINMUX_PAD_PA24_IS        HPSYS_PINMUX_PAD_PA24_IS_Msk
#define HPSYS_PINMUX_PAD_PA24_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA24_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA24_SR_Pos)
#define HPSYS_PINMUX_PAD_PA24_SR        HPSYS_PINMUX_PAD_PA24_SR_Msk
#define HPSYS_PINMUX_PAD_PA24_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA24_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA24_DS0       HPSYS_PINMUX_PAD_PA24_DS0_Msk
#define HPSYS_PINMUX_PAD_PA24_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA24_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA24_DS1       HPSYS_PINMUX_PAD_PA24_DS1_Msk
#define HPSYS_PINMUX_PAD_PA24_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA24_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA24_POE_Pos)
#define HPSYS_PINMUX_PAD_PA24_POE       HPSYS_PINMUX_PAD_PA24_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA25 register **************/
#define HPSYS_PINMUX_PAD_PA25_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA25_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA25_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA25_FSEL      HPSYS_PINMUX_PAD_PA25_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA25_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA25_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_PE_Pos)
#define HPSYS_PINMUX_PAD_PA25_PE        HPSYS_PINMUX_PAD_PA25_PE_Msk
#define HPSYS_PINMUX_PAD_PA25_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA25_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_PS_Pos)
#define HPSYS_PINMUX_PAD_PA25_PS        HPSYS_PINMUX_PAD_PA25_PS_Msk
#define HPSYS_PINMUX_PAD_PA25_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA25_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_IE_Pos)
#define HPSYS_PINMUX_PAD_PA25_IE        HPSYS_PINMUX_PAD_PA25_IE_Msk
#define HPSYS_PINMUX_PAD_PA25_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA25_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_IS_Pos)
#define HPSYS_PINMUX_PAD_PA25_IS        HPSYS_PINMUX_PAD_PA25_IS_Msk
#define HPSYS_PINMUX_PAD_PA25_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA25_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA25_SR_Pos)
#define HPSYS_PINMUX_PAD_PA25_SR        HPSYS_PINMUX_PAD_PA25_SR_Msk
#define HPSYS_PINMUX_PAD_PA25_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA25_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA25_DS0       HPSYS_PINMUX_PAD_PA25_DS0_Msk
#define HPSYS_PINMUX_PAD_PA25_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA25_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA25_DS1       HPSYS_PINMUX_PAD_PA25_DS1_Msk
#define HPSYS_PINMUX_PAD_PA25_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA25_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA25_POE_Pos)
#define HPSYS_PINMUX_PAD_PA25_POE       HPSYS_PINMUX_PAD_PA25_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA26 register **************/
#define HPSYS_PINMUX_PAD_PA26_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA26_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA26_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA26_FSEL      HPSYS_PINMUX_PAD_PA26_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA26_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA26_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_PE_Pos)
#define HPSYS_PINMUX_PAD_PA26_PE        HPSYS_PINMUX_PAD_PA26_PE_Msk
#define HPSYS_PINMUX_PAD_PA26_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA26_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_PS_Pos)
#define HPSYS_PINMUX_PAD_PA26_PS        HPSYS_PINMUX_PAD_PA26_PS_Msk
#define HPSYS_PINMUX_PAD_PA26_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA26_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_IE_Pos)
#define HPSYS_PINMUX_PAD_PA26_IE        HPSYS_PINMUX_PAD_PA26_IE_Msk
#define HPSYS_PINMUX_PAD_PA26_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA26_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_IS_Pos)
#define HPSYS_PINMUX_PAD_PA26_IS        HPSYS_PINMUX_PAD_PA26_IS_Msk
#define HPSYS_PINMUX_PAD_PA26_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA26_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA26_SR_Pos)
#define HPSYS_PINMUX_PAD_PA26_SR        HPSYS_PINMUX_PAD_PA26_SR_Msk
#define HPSYS_PINMUX_PAD_PA26_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA26_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA26_DS0       HPSYS_PINMUX_PAD_PA26_DS0_Msk
#define HPSYS_PINMUX_PAD_PA26_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA26_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA26_DS1       HPSYS_PINMUX_PAD_PA26_DS1_Msk
#define HPSYS_PINMUX_PAD_PA26_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA26_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA26_POE_Pos)
#define HPSYS_PINMUX_PAD_PA26_POE       HPSYS_PINMUX_PAD_PA26_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA27 register **************/
#define HPSYS_PINMUX_PAD_PA27_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA27_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA27_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA27_FSEL      HPSYS_PINMUX_PAD_PA27_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA27_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA27_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_PE_Pos)
#define HPSYS_PINMUX_PAD_PA27_PE        HPSYS_PINMUX_PAD_PA27_PE_Msk
#define HPSYS_PINMUX_PAD_PA27_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA27_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_PS_Pos)
#define HPSYS_PINMUX_PAD_PA27_PS        HPSYS_PINMUX_PAD_PA27_PS_Msk
#define HPSYS_PINMUX_PAD_PA27_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA27_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_IE_Pos)
#define HPSYS_PINMUX_PAD_PA27_IE        HPSYS_PINMUX_PAD_PA27_IE_Msk
#define HPSYS_PINMUX_PAD_PA27_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA27_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_IS_Pos)
#define HPSYS_PINMUX_PAD_PA27_IS        HPSYS_PINMUX_PAD_PA27_IS_Msk
#define HPSYS_PINMUX_PAD_PA27_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA27_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA27_SR_Pos)
#define HPSYS_PINMUX_PAD_PA27_SR        HPSYS_PINMUX_PAD_PA27_SR_Msk
#define HPSYS_PINMUX_PAD_PA27_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA27_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA27_DS0       HPSYS_PINMUX_PAD_PA27_DS0_Msk
#define HPSYS_PINMUX_PAD_PA27_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA27_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA27_DS1       HPSYS_PINMUX_PAD_PA27_DS1_Msk
#define HPSYS_PINMUX_PAD_PA27_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA27_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA27_POE_Pos)
#define HPSYS_PINMUX_PAD_PA27_POE       HPSYS_PINMUX_PAD_PA27_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA28 register **************/
#define HPSYS_PINMUX_PAD_PA28_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA28_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA28_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA28_FSEL      HPSYS_PINMUX_PAD_PA28_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA28_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA28_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_PE_Pos)
#define HPSYS_PINMUX_PAD_PA28_PE        HPSYS_PINMUX_PAD_PA28_PE_Msk
#define HPSYS_PINMUX_PAD_PA28_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA28_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_PS_Pos)
#define HPSYS_PINMUX_PAD_PA28_PS        HPSYS_PINMUX_PAD_PA28_PS_Msk
#define HPSYS_PINMUX_PAD_PA28_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA28_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_IE_Pos)
#define HPSYS_PINMUX_PAD_PA28_IE        HPSYS_PINMUX_PAD_PA28_IE_Msk
#define HPSYS_PINMUX_PAD_PA28_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA28_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_IS_Pos)
#define HPSYS_PINMUX_PAD_PA28_IS        HPSYS_PINMUX_PAD_PA28_IS_Msk
#define HPSYS_PINMUX_PAD_PA28_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA28_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA28_SR_Pos)
#define HPSYS_PINMUX_PAD_PA28_SR        HPSYS_PINMUX_PAD_PA28_SR_Msk
#define HPSYS_PINMUX_PAD_PA28_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA28_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA28_DS0       HPSYS_PINMUX_PAD_PA28_DS0_Msk
#define HPSYS_PINMUX_PAD_PA28_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA28_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA28_DS1       HPSYS_PINMUX_PAD_PA28_DS1_Msk
#define HPSYS_PINMUX_PAD_PA28_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA28_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA28_POE_Pos)
#define HPSYS_PINMUX_PAD_PA28_POE       HPSYS_PINMUX_PAD_PA28_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA29 register **************/
#define HPSYS_PINMUX_PAD_PA29_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA29_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA29_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA29_FSEL      HPSYS_PINMUX_PAD_PA29_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA29_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA29_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_PE_Pos)
#define HPSYS_PINMUX_PAD_PA29_PE        HPSYS_PINMUX_PAD_PA29_PE_Msk
#define HPSYS_PINMUX_PAD_PA29_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA29_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_PS_Pos)
#define HPSYS_PINMUX_PAD_PA29_PS        HPSYS_PINMUX_PAD_PA29_PS_Msk
#define HPSYS_PINMUX_PAD_PA29_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA29_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_IE_Pos)
#define HPSYS_PINMUX_PAD_PA29_IE        HPSYS_PINMUX_PAD_PA29_IE_Msk
#define HPSYS_PINMUX_PAD_PA29_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA29_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_IS_Pos)
#define HPSYS_PINMUX_PAD_PA29_IS        HPSYS_PINMUX_PAD_PA29_IS_Msk
#define HPSYS_PINMUX_PAD_PA29_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA29_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA29_SR_Pos)
#define HPSYS_PINMUX_PAD_PA29_SR        HPSYS_PINMUX_PAD_PA29_SR_Msk
#define HPSYS_PINMUX_PAD_PA29_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA29_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA29_DS0       HPSYS_PINMUX_PAD_PA29_DS0_Msk
#define HPSYS_PINMUX_PAD_PA29_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA29_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA29_DS1       HPSYS_PINMUX_PAD_PA29_DS1_Msk
#define HPSYS_PINMUX_PAD_PA29_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA29_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA29_POE_Pos)
#define HPSYS_PINMUX_PAD_PA29_POE       HPSYS_PINMUX_PAD_PA29_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA30 register **************/
#define HPSYS_PINMUX_PAD_PA30_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA30_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA30_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA30_FSEL      HPSYS_PINMUX_PAD_PA30_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA30_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA30_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_PE_Pos)
#define HPSYS_PINMUX_PAD_PA30_PE        HPSYS_PINMUX_PAD_PA30_PE_Msk
#define HPSYS_PINMUX_PAD_PA30_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA30_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_PS_Pos)
#define HPSYS_PINMUX_PAD_PA30_PS        HPSYS_PINMUX_PAD_PA30_PS_Msk
#define HPSYS_PINMUX_PAD_PA30_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA30_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_IE_Pos)
#define HPSYS_PINMUX_PAD_PA30_IE        HPSYS_PINMUX_PAD_PA30_IE_Msk
#define HPSYS_PINMUX_PAD_PA30_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA30_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_IS_Pos)
#define HPSYS_PINMUX_PAD_PA30_IS        HPSYS_PINMUX_PAD_PA30_IS_Msk
#define HPSYS_PINMUX_PAD_PA30_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA30_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA30_SR_Pos)
#define HPSYS_PINMUX_PAD_PA30_SR        HPSYS_PINMUX_PAD_PA30_SR_Msk
#define HPSYS_PINMUX_PAD_PA30_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA30_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA30_DS0       HPSYS_PINMUX_PAD_PA30_DS0_Msk
#define HPSYS_PINMUX_PAD_PA30_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA30_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA30_DS1       HPSYS_PINMUX_PAD_PA30_DS1_Msk
#define HPSYS_PINMUX_PAD_PA30_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA30_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA30_POE_Pos)
#define HPSYS_PINMUX_PAD_PA30_POE       HPSYS_PINMUX_PAD_PA30_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA31 register **************/
#define HPSYS_PINMUX_PAD_PA31_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA31_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA31_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA31_FSEL      HPSYS_PINMUX_PAD_PA31_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA31_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA31_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_PE_Pos)
#define HPSYS_PINMUX_PAD_PA31_PE        HPSYS_PINMUX_PAD_PA31_PE_Msk
#define HPSYS_PINMUX_PAD_PA31_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA31_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_PS_Pos)
#define HPSYS_PINMUX_PAD_PA31_PS        HPSYS_PINMUX_PAD_PA31_PS_Msk
#define HPSYS_PINMUX_PAD_PA31_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA31_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_IE_Pos)
#define HPSYS_PINMUX_PAD_PA31_IE        HPSYS_PINMUX_PAD_PA31_IE_Msk
#define HPSYS_PINMUX_PAD_PA31_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA31_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_IS_Pos)
#define HPSYS_PINMUX_PAD_PA31_IS        HPSYS_PINMUX_PAD_PA31_IS_Msk
#define HPSYS_PINMUX_PAD_PA31_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA31_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA31_SR_Pos)
#define HPSYS_PINMUX_PAD_PA31_SR        HPSYS_PINMUX_PAD_PA31_SR_Msk
#define HPSYS_PINMUX_PAD_PA31_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA31_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA31_DS0       HPSYS_PINMUX_PAD_PA31_DS0_Msk
#define HPSYS_PINMUX_PAD_PA31_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA31_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA31_DS1       HPSYS_PINMUX_PAD_PA31_DS1_Msk
#define HPSYS_PINMUX_PAD_PA31_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA31_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA31_POE_Pos)
#define HPSYS_PINMUX_PAD_PA31_POE       HPSYS_PINMUX_PAD_PA31_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA32 register **************/
#define HPSYS_PINMUX_PAD_PA32_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA32_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA32_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA32_FSEL      HPSYS_PINMUX_PAD_PA32_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA32_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA32_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_PE_Pos)
#define HPSYS_PINMUX_PAD_PA32_PE        HPSYS_PINMUX_PAD_PA32_PE_Msk
#define HPSYS_PINMUX_PAD_PA32_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA32_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_PS_Pos)
#define HPSYS_PINMUX_PAD_PA32_PS        HPSYS_PINMUX_PAD_PA32_PS_Msk
#define HPSYS_PINMUX_PAD_PA32_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA32_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_IE_Pos)
#define HPSYS_PINMUX_PAD_PA32_IE        HPSYS_PINMUX_PAD_PA32_IE_Msk
#define HPSYS_PINMUX_PAD_PA32_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA32_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_IS_Pos)
#define HPSYS_PINMUX_PAD_PA32_IS        HPSYS_PINMUX_PAD_PA32_IS_Msk
#define HPSYS_PINMUX_PAD_PA32_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA32_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA32_SR_Pos)
#define HPSYS_PINMUX_PAD_PA32_SR        HPSYS_PINMUX_PAD_PA32_SR_Msk
#define HPSYS_PINMUX_PAD_PA32_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA32_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA32_DS0       HPSYS_PINMUX_PAD_PA32_DS0_Msk
#define HPSYS_PINMUX_PAD_PA32_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA32_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA32_DS1       HPSYS_PINMUX_PAD_PA32_DS1_Msk
#define HPSYS_PINMUX_PAD_PA32_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA32_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA32_POE_Pos)
#define HPSYS_PINMUX_PAD_PA32_POE       HPSYS_PINMUX_PAD_PA32_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA33 register **************/
#define HPSYS_PINMUX_PAD_PA33_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA33_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA33_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA33_FSEL      HPSYS_PINMUX_PAD_PA33_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA33_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA33_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_PE_Pos)
#define HPSYS_PINMUX_PAD_PA33_PE        HPSYS_PINMUX_PAD_PA33_PE_Msk
#define HPSYS_PINMUX_PAD_PA33_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA33_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_PS_Pos)
#define HPSYS_PINMUX_PAD_PA33_PS        HPSYS_PINMUX_PAD_PA33_PS_Msk
#define HPSYS_PINMUX_PAD_PA33_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA33_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_IE_Pos)
#define HPSYS_PINMUX_PAD_PA33_IE        HPSYS_PINMUX_PAD_PA33_IE_Msk
#define HPSYS_PINMUX_PAD_PA33_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA33_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_IS_Pos)
#define HPSYS_PINMUX_PAD_PA33_IS        HPSYS_PINMUX_PAD_PA33_IS_Msk
#define HPSYS_PINMUX_PAD_PA33_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA33_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA33_SR_Pos)
#define HPSYS_PINMUX_PAD_PA33_SR        HPSYS_PINMUX_PAD_PA33_SR_Msk
#define HPSYS_PINMUX_PAD_PA33_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA33_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA33_DS0       HPSYS_PINMUX_PAD_PA33_DS0_Msk
#define HPSYS_PINMUX_PAD_PA33_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA33_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA33_DS1       HPSYS_PINMUX_PAD_PA33_DS1_Msk
#define HPSYS_PINMUX_PAD_PA33_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA33_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA33_POE_Pos)
#define HPSYS_PINMUX_PAD_PA33_POE       HPSYS_PINMUX_PAD_PA33_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA34 register **************/
#define HPSYS_PINMUX_PAD_PA34_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA34_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA34_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA34_FSEL      HPSYS_PINMUX_PAD_PA34_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA34_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA34_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_PE_Pos)
#define HPSYS_PINMUX_PAD_PA34_PE        HPSYS_PINMUX_PAD_PA34_PE_Msk
#define HPSYS_PINMUX_PAD_PA34_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA34_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_PS_Pos)
#define HPSYS_PINMUX_PAD_PA34_PS        HPSYS_PINMUX_PAD_PA34_PS_Msk
#define HPSYS_PINMUX_PAD_PA34_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA34_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_IE_Pos)
#define HPSYS_PINMUX_PAD_PA34_IE        HPSYS_PINMUX_PAD_PA34_IE_Msk
#define HPSYS_PINMUX_PAD_PA34_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA34_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_IS_Pos)
#define HPSYS_PINMUX_PAD_PA34_IS        HPSYS_PINMUX_PAD_PA34_IS_Msk
#define HPSYS_PINMUX_PAD_PA34_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA34_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA34_SR_Pos)
#define HPSYS_PINMUX_PAD_PA34_SR        HPSYS_PINMUX_PAD_PA34_SR_Msk
#define HPSYS_PINMUX_PAD_PA34_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA34_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA34_DS0       HPSYS_PINMUX_PAD_PA34_DS0_Msk
#define HPSYS_PINMUX_PAD_PA34_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA34_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA34_DS1       HPSYS_PINMUX_PAD_PA34_DS1_Msk
#define HPSYS_PINMUX_PAD_PA34_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA34_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA34_POE_Pos)
#define HPSYS_PINMUX_PAD_PA34_POE       HPSYS_PINMUX_PAD_PA34_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA35 register **************/
#define HPSYS_PINMUX_PAD_PA35_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA35_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA35_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA35_FSEL      HPSYS_PINMUX_PAD_PA35_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA35_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA35_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_PE_Pos)
#define HPSYS_PINMUX_PAD_PA35_PE        HPSYS_PINMUX_PAD_PA35_PE_Msk
#define HPSYS_PINMUX_PAD_PA35_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA35_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_PS_Pos)
#define HPSYS_PINMUX_PAD_PA35_PS        HPSYS_PINMUX_PAD_PA35_PS_Msk
#define HPSYS_PINMUX_PAD_PA35_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA35_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_IE_Pos)
#define HPSYS_PINMUX_PAD_PA35_IE        HPSYS_PINMUX_PAD_PA35_IE_Msk
#define HPSYS_PINMUX_PAD_PA35_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA35_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_IS_Pos)
#define HPSYS_PINMUX_PAD_PA35_IS        HPSYS_PINMUX_PAD_PA35_IS_Msk
#define HPSYS_PINMUX_PAD_PA35_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA35_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA35_SR_Pos)
#define HPSYS_PINMUX_PAD_PA35_SR        HPSYS_PINMUX_PAD_PA35_SR_Msk
#define HPSYS_PINMUX_PAD_PA35_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA35_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA35_DS0       HPSYS_PINMUX_PAD_PA35_DS0_Msk
#define HPSYS_PINMUX_PAD_PA35_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA35_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA35_DS1       HPSYS_PINMUX_PAD_PA35_DS1_Msk
#define HPSYS_PINMUX_PAD_PA35_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA35_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA35_POE_Pos)
#define HPSYS_PINMUX_PAD_PA35_POE       HPSYS_PINMUX_PAD_PA35_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA36 register **************/
#define HPSYS_PINMUX_PAD_PA36_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA36_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA36_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA36_FSEL      HPSYS_PINMUX_PAD_PA36_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA36_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA36_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_PE_Pos)
#define HPSYS_PINMUX_PAD_PA36_PE        HPSYS_PINMUX_PAD_PA36_PE_Msk
#define HPSYS_PINMUX_PAD_PA36_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA36_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_PS_Pos)
#define HPSYS_PINMUX_PAD_PA36_PS        HPSYS_PINMUX_PAD_PA36_PS_Msk
#define HPSYS_PINMUX_PAD_PA36_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA36_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_IE_Pos)
#define HPSYS_PINMUX_PAD_PA36_IE        HPSYS_PINMUX_PAD_PA36_IE_Msk
#define HPSYS_PINMUX_PAD_PA36_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA36_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_IS_Pos)
#define HPSYS_PINMUX_PAD_PA36_IS        HPSYS_PINMUX_PAD_PA36_IS_Msk
#define HPSYS_PINMUX_PAD_PA36_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA36_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA36_SR_Pos)
#define HPSYS_PINMUX_PAD_PA36_SR        HPSYS_PINMUX_PAD_PA36_SR_Msk
#define HPSYS_PINMUX_PAD_PA36_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA36_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA36_DS0       HPSYS_PINMUX_PAD_PA36_DS0_Msk
#define HPSYS_PINMUX_PAD_PA36_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA36_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA36_DS1       HPSYS_PINMUX_PAD_PA36_DS1_Msk
#define HPSYS_PINMUX_PAD_PA36_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA36_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA36_POE_Pos)
#define HPSYS_PINMUX_PAD_PA36_POE       HPSYS_PINMUX_PAD_PA36_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA37 register **************/
#define HPSYS_PINMUX_PAD_PA37_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA37_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA37_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA37_FSEL      HPSYS_PINMUX_PAD_PA37_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA37_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA37_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_PE_Pos)
#define HPSYS_PINMUX_PAD_PA37_PE        HPSYS_PINMUX_PAD_PA37_PE_Msk
#define HPSYS_PINMUX_PAD_PA37_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA37_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_PS_Pos)
#define HPSYS_PINMUX_PAD_PA37_PS        HPSYS_PINMUX_PAD_PA37_PS_Msk
#define HPSYS_PINMUX_PAD_PA37_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA37_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_IE_Pos)
#define HPSYS_PINMUX_PAD_PA37_IE        HPSYS_PINMUX_PAD_PA37_IE_Msk
#define HPSYS_PINMUX_PAD_PA37_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA37_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_IS_Pos)
#define HPSYS_PINMUX_PAD_PA37_IS        HPSYS_PINMUX_PAD_PA37_IS_Msk
#define HPSYS_PINMUX_PAD_PA37_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA37_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA37_SR_Pos)
#define HPSYS_PINMUX_PAD_PA37_SR        HPSYS_PINMUX_PAD_PA37_SR_Msk
#define HPSYS_PINMUX_PAD_PA37_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA37_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA37_DS0       HPSYS_PINMUX_PAD_PA37_DS0_Msk
#define HPSYS_PINMUX_PAD_PA37_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA37_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA37_DS1       HPSYS_PINMUX_PAD_PA37_DS1_Msk
#define HPSYS_PINMUX_PAD_PA37_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA37_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA37_POE_Pos)
#define HPSYS_PINMUX_PAD_PA37_POE       HPSYS_PINMUX_PAD_PA37_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA38 register **************/
#define HPSYS_PINMUX_PAD_PA38_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA38_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA38_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA38_FSEL      HPSYS_PINMUX_PAD_PA38_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA38_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA38_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_PE_Pos)
#define HPSYS_PINMUX_PAD_PA38_PE        HPSYS_PINMUX_PAD_PA38_PE_Msk
#define HPSYS_PINMUX_PAD_PA38_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA38_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_PS_Pos)
#define HPSYS_PINMUX_PAD_PA38_PS        HPSYS_PINMUX_PAD_PA38_PS_Msk
#define HPSYS_PINMUX_PAD_PA38_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA38_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_IE_Pos)
#define HPSYS_PINMUX_PAD_PA38_IE        HPSYS_PINMUX_PAD_PA38_IE_Msk
#define HPSYS_PINMUX_PAD_PA38_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA38_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_IS_Pos)
#define HPSYS_PINMUX_PAD_PA38_IS        HPSYS_PINMUX_PAD_PA38_IS_Msk
#define HPSYS_PINMUX_PAD_PA38_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA38_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA38_SR_Pos)
#define HPSYS_PINMUX_PAD_PA38_SR        HPSYS_PINMUX_PAD_PA38_SR_Msk
#define HPSYS_PINMUX_PAD_PA38_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA38_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA38_DS0       HPSYS_PINMUX_PAD_PA38_DS0_Msk
#define HPSYS_PINMUX_PAD_PA38_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA38_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA38_DS1       HPSYS_PINMUX_PAD_PA38_DS1_Msk
#define HPSYS_PINMUX_PAD_PA38_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA38_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA38_POE_Pos)
#define HPSYS_PINMUX_PAD_PA38_POE       HPSYS_PINMUX_PAD_PA38_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA39 register **************/
#define HPSYS_PINMUX_PAD_PA39_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA39_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA39_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA39_FSEL      HPSYS_PINMUX_PAD_PA39_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA39_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA39_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_PE_Pos)
#define HPSYS_PINMUX_PAD_PA39_PE        HPSYS_PINMUX_PAD_PA39_PE_Msk
#define HPSYS_PINMUX_PAD_PA39_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA39_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_PS_Pos)
#define HPSYS_PINMUX_PAD_PA39_PS        HPSYS_PINMUX_PAD_PA39_PS_Msk
#define HPSYS_PINMUX_PAD_PA39_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA39_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_IE_Pos)
#define HPSYS_PINMUX_PAD_PA39_IE        HPSYS_PINMUX_PAD_PA39_IE_Msk
#define HPSYS_PINMUX_PAD_PA39_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA39_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_IS_Pos)
#define HPSYS_PINMUX_PAD_PA39_IS        HPSYS_PINMUX_PAD_PA39_IS_Msk
#define HPSYS_PINMUX_PAD_PA39_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA39_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA39_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA39_MODE      HPSYS_PINMUX_PAD_PA39_MODE_Msk
#define HPSYS_PINMUX_PAD_PA39_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA39_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA39_DS_Pos)
#define HPSYS_PINMUX_PAD_PA39_DS        HPSYS_PINMUX_PAD_PA39_DS_Msk
#define HPSYS_PINMUX_PAD_PA39_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA39_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA39_POE_Pos)
#define HPSYS_PINMUX_PAD_PA39_POE       HPSYS_PINMUX_PAD_PA39_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA40 register **************/
#define HPSYS_PINMUX_PAD_PA40_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA40_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA40_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA40_FSEL      HPSYS_PINMUX_PAD_PA40_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA40_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA40_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_PE_Pos)
#define HPSYS_PINMUX_PAD_PA40_PE        HPSYS_PINMUX_PAD_PA40_PE_Msk
#define HPSYS_PINMUX_PAD_PA40_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA40_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_PS_Pos)
#define HPSYS_PINMUX_PAD_PA40_PS        HPSYS_PINMUX_PAD_PA40_PS_Msk
#define HPSYS_PINMUX_PAD_PA40_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA40_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_IE_Pos)
#define HPSYS_PINMUX_PAD_PA40_IE        HPSYS_PINMUX_PAD_PA40_IE_Msk
#define HPSYS_PINMUX_PAD_PA40_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA40_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_IS_Pos)
#define HPSYS_PINMUX_PAD_PA40_IS        HPSYS_PINMUX_PAD_PA40_IS_Msk
#define HPSYS_PINMUX_PAD_PA40_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA40_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA40_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA40_MODE      HPSYS_PINMUX_PAD_PA40_MODE_Msk
#define HPSYS_PINMUX_PAD_PA40_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA40_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA40_DS_Pos)
#define HPSYS_PINMUX_PAD_PA40_DS        HPSYS_PINMUX_PAD_PA40_DS_Msk
#define HPSYS_PINMUX_PAD_PA40_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA40_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA40_POE_Pos)
#define HPSYS_PINMUX_PAD_PA40_POE       HPSYS_PINMUX_PAD_PA40_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA41 register **************/
#define HPSYS_PINMUX_PAD_PA41_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA41_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA41_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA41_FSEL      HPSYS_PINMUX_PAD_PA41_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA41_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA41_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_PE_Pos)
#define HPSYS_PINMUX_PAD_PA41_PE        HPSYS_PINMUX_PAD_PA41_PE_Msk
#define HPSYS_PINMUX_PAD_PA41_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA41_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_PS_Pos)
#define HPSYS_PINMUX_PAD_PA41_PS        HPSYS_PINMUX_PAD_PA41_PS_Msk
#define HPSYS_PINMUX_PAD_PA41_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA41_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_IE_Pos)
#define HPSYS_PINMUX_PAD_PA41_IE        HPSYS_PINMUX_PAD_PA41_IE_Msk
#define HPSYS_PINMUX_PAD_PA41_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA41_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_IS_Pos)
#define HPSYS_PINMUX_PAD_PA41_IS        HPSYS_PINMUX_PAD_PA41_IS_Msk
#define HPSYS_PINMUX_PAD_PA41_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA41_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA41_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA41_MODE      HPSYS_PINMUX_PAD_PA41_MODE_Msk
#define HPSYS_PINMUX_PAD_PA41_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA41_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA41_DS_Pos)
#define HPSYS_PINMUX_PAD_PA41_DS        HPSYS_PINMUX_PAD_PA41_DS_Msk
#define HPSYS_PINMUX_PAD_PA41_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA41_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA41_POE_Pos)
#define HPSYS_PINMUX_PAD_PA41_POE       HPSYS_PINMUX_PAD_PA41_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA42 register **************/
#define HPSYS_PINMUX_PAD_PA42_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA42_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA42_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA42_FSEL      HPSYS_PINMUX_PAD_PA42_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA42_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA42_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_PE_Pos)
#define HPSYS_PINMUX_PAD_PA42_PE        HPSYS_PINMUX_PAD_PA42_PE_Msk
#define HPSYS_PINMUX_PAD_PA42_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA42_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_PS_Pos)
#define HPSYS_PINMUX_PAD_PA42_PS        HPSYS_PINMUX_PAD_PA42_PS_Msk
#define HPSYS_PINMUX_PAD_PA42_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA42_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_IE_Pos)
#define HPSYS_PINMUX_PAD_PA42_IE        HPSYS_PINMUX_PAD_PA42_IE_Msk
#define HPSYS_PINMUX_PAD_PA42_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA42_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_IS_Pos)
#define HPSYS_PINMUX_PAD_PA42_IS        HPSYS_PINMUX_PAD_PA42_IS_Msk
#define HPSYS_PINMUX_PAD_PA42_MODE_Pos  (8U)
#define HPSYS_PINMUX_PAD_PA42_MODE_Msk  (0x1UL << HPSYS_PINMUX_PAD_PA42_MODE_Pos)
#define HPSYS_PINMUX_PAD_PA42_MODE      HPSYS_PINMUX_PAD_PA42_MODE_Msk
#define HPSYS_PINMUX_PAD_PA42_DS_Pos    (10U)
#define HPSYS_PINMUX_PAD_PA42_DS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA42_DS_Pos)
#define HPSYS_PINMUX_PAD_PA42_DS        HPSYS_PINMUX_PAD_PA42_DS_Msk
#define HPSYS_PINMUX_PAD_PA42_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA42_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA42_POE_Pos)
#define HPSYS_PINMUX_PAD_PA42_POE       HPSYS_PINMUX_PAD_PA42_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA43 register **************/
#define HPSYS_PINMUX_PAD_PA43_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA43_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA43_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA43_FSEL      HPSYS_PINMUX_PAD_PA43_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA43_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA43_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_PE_Pos)
#define HPSYS_PINMUX_PAD_PA43_PE        HPSYS_PINMUX_PAD_PA43_PE_Msk
#define HPSYS_PINMUX_PAD_PA43_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA43_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_PS_Pos)
#define HPSYS_PINMUX_PAD_PA43_PS        HPSYS_PINMUX_PAD_PA43_PS_Msk
#define HPSYS_PINMUX_PAD_PA43_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA43_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_IE_Pos)
#define HPSYS_PINMUX_PAD_PA43_IE        HPSYS_PINMUX_PAD_PA43_IE_Msk
#define HPSYS_PINMUX_PAD_PA43_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA43_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_IS_Pos)
#define HPSYS_PINMUX_PAD_PA43_IS        HPSYS_PINMUX_PAD_PA43_IS_Msk
#define HPSYS_PINMUX_PAD_PA43_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA43_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA43_SR_Pos)
#define HPSYS_PINMUX_PAD_PA43_SR        HPSYS_PINMUX_PAD_PA43_SR_Msk
#define HPSYS_PINMUX_PAD_PA43_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA43_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA43_DS0       HPSYS_PINMUX_PAD_PA43_DS0_Msk
#define HPSYS_PINMUX_PAD_PA43_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA43_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA43_DS1       HPSYS_PINMUX_PAD_PA43_DS1_Msk
#define HPSYS_PINMUX_PAD_PA43_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA43_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA43_POE_Pos)
#define HPSYS_PINMUX_PAD_PA43_POE       HPSYS_PINMUX_PAD_PA43_POE_Msk

/************* Bit definition for HPSYS_PINMUX_PAD_PA44 register **************/
#define HPSYS_PINMUX_PAD_PA44_FSEL_Pos  (0U)
#define HPSYS_PINMUX_PAD_PA44_FSEL_Msk  (0xFUL << HPSYS_PINMUX_PAD_PA44_FSEL_Pos)
#define HPSYS_PINMUX_PAD_PA44_FSEL      HPSYS_PINMUX_PAD_PA44_FSEL_Msk
#define HPSYS_PINMUX_PAD_PA44_PE_Pos    (4U)
#define HPSYS_PINMUX_PAD_PA44_PE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_PE_Pos)
#define HPSYS_PINMUX_PAD_PA44_PE        HPSYS_PINMUX_PAD_PA44_PE_Msk
#define HPSYS_PINMUX_PAD_PA44_PS_Pos    (5U)
#define HPSYS_PINMUX_PAD_PA44_PS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_PS_Pos)
#define HPSYS_PINMUX_PAD_PA44_PS        HPSYS_PINMUX_PAD_PA44_PS_Msk
#define HPSYS_PINMUX_PAD_PA44_IE_Pos    (6U)
#define HPSYS_PINMUX_PAD_PA44_IE_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_IE_Pos)
#define HPSYS_PINMUX_PAD_PA44_IE        HPSYS_PINMUX_PAD_PA44_IE_Msk
#define HPSYS_PINMUX_PAD_PA44_IS_Pos    (7U)
#define HPSYS_PINMUX_PAD_PA44_IS_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_IS_Pos)
#define HPSYS_PINMUX_PAD_PA44_IS        HPSYS_PINMUX_PAD_PA44_IS_Msk
#define HPSYS_PINMUX_PAD_PA44_SR_Pos    (8U)
#define HPSYS_PINMUX_PAD_PA44_SR_Msk    (0x1UL << HPSYS_PINMUX_PAD_PA44_SR_Pos)
#define HPSYS_PINMUX_PAD_PA44_SR        HPSYS_PINMUX_PAD_PA44_SR_Msk
#define HPSYS_PINMUX_PAD_PA44_DS0_Pos   (9U)
#define HPSYS_PINMUX_PAD_PA44_DS0_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_DS0_Pos)
#define HPSYS_PINMUX_PAD_PA44_DS0       HPSYS_PINMUX_PAD_PA44_DS0_Msk
#define HPSYS_PINMUX_PAD_PA44_DS1_Pos   (10U)
#define HPSYS_PINMUX_PAD_PA44_DS1_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_DS1_Pos)
#define HPSYS_PINMUX_PAD_PA44_DS1       HPSYS_PINMUX_PAD_PA44_DS1_Msk
#define HPSYS_PINMUX_PAD_PA44_POE_Pos   (11U)
#define HPSYS_PINMUX_PAD_PA44_POE_Msk   (0x1UL << HPSYS_PINMUX_PAD_PA44_POE_Pos)
#define HPSYS_PINMUX_PAD_PA44_POE       HPSYS_PINMUX_PAD_PA44_POE_Msk

#endif
