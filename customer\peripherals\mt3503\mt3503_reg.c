/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   mt3503_reg.c
@Time    :   2025/02/13 20:34:29
@Brief   :   光旋钮寄存器驱动模块
@Details :
*
************************************************************/

#include "rtconfig.h"

#ifdef BSP_USING_MT3503

#include "mt3503_reg.h"
#include <rtthread.h>
int32_t __weak mt3503_read_reg(const stmdev_ctx_t* ctx, uint8_t reg, uint8_t* data, uint16_t len)
{
    int32_t ret;

    if (ctx == NULL)
    {
        return -1;
    }
    //rt_kprintf("r1\n");
    ret = ctx->read_reg(ctx->handle, reg, data, len);

    return ret;
}
int32_t __weak mt3503_write_reg(const stmdev_ctx_t* ctx, uint8_t reg, uint8_t* data, uint16_t len)
{
    int32_t ret;

    if (ctx == NULL)
    {
        return -1;
    }

    ret = ctx->write_reg(ctx->handle, reg, data, len);

    return ret;
}

static void bytecpy(uint8_t* target, uint8_t* source)
{
    if ((target != NULL) && (source != NULL))
    {
        *target = *source;
    }
}

int32_t mt3503_id_get(const stmdev_ctx_t* ctx, mt3503_id_t* val)
{
    uint8_t reg;
    int32_t ret;

    ret = mt3503_read_reg(ctx, MT3503_Product_ID1, &reg, 1);
    val->whoami = reg;

    return ret;
}



static void sensor_write_Byte(const stmdev_ctx_t* ctx, uint8_t reg, uint8_t val)
{
    mt3503_write_reg(ctx, reg, &(val), 1);
}

//厂商提供的初始化（包含一些数据手册上没有的寄存器的配置，故不知含义）
void mt3503_state_init(const stmdev_ctx_t* ctx)
{
    //5.5.3
    sensor_write_Byte(ctx, 0x09,0x5a);//写保护取消
    sensor_write_Byte(ctx, 0x7f,0x5a);//按键写保护取消
    sensor_write_Byte(ctx, 0x0D,0x40);// 设置 RES_X_Lo： 低八位
    sensor_write_Byte(ctx, 0x0E,0x40);// 设置 RES_Y_Lo ：低八位
    sensor_write_Byte(ctx, 0x0B,0x30);// cfg sleep2 默认 30    采样率4*64ms   20.48s
    //1ctx,
    sensor_write_Byte(ctx, 0x0A,0x30);// cfg sleep1      30    采样率4*4ms    16ms  匹配TP
    sensor_write_Byte(ctx, 0x0F,0x11);// RES_XY_Hi  X高位 1
    sensor_write_Byte(ctx, 0x7E,0x01);
    sensor_write_Byte(ctx, 0x19,0x0C);
    sensor_write_Byte(ctx, 0x26,0x01);//set int mode
    sensor_write_Byte(ctx, 0x7E,0x02);
    sensor_write_Byte(ctx, 0x57,0x06);//86小数点使能，06关闭
    //2ctx,
    sensor_write_Byte(ctx, 0x28,0x02);//KEY
    sensor_write_Byte(ctx, 0x29,0x00);//KEY
    sensor_write_Byte(ctx, 0x2A,0x00);//KEY
    sensor_write_Byte(ctx, 0x2B,0x20);
    sensor_write_Byte(ctx, 0x1C,0x44);
    //3ctx,
    sensor_write_Byte(ctx, 0x1F,0x4a);
    sensor_write_Byte(ctx, 0x20,0x10);
    sensor_write_Byte(ctx, 0x21,0x10);
    sensor_write_Byte(ctx, 0x75,0x90);//
    sensor_write_Byte(ctx, 0x76,0x90);
    //4ctx,
    sensor_write_Byte(ctx, 0x77,0xA0);
    sensor_write_Byte(ctx, 0x14,0x10);//
    sensor_write_Byte(ctx, 0x1b,0x72);//
    sensor_write_Byte(ctx, 0x52,0x30);
    sensor_write_Byte(ctx, 0x5C,0x50);
    sensor_write_Byte(ctx, 0x7E,0x02);
    sensor_write_Byte(ctx, 0x67,0x13);
    sensor_write_Byte(ctx, 0x2E,0x48);
    sensor_write_Byte(ctx, 0x30,0x2F);
    sensor_write_Byte(ctx, 0x3F,0x06);
    sensor_write_Byte(ctx, 0x53,0x06);
    sensor_write_Byte(ctx, 0x34,0x56);
    sensor_write_Byte(ctx, 0x70,0x40);
    sensor_write_Byte(ctx, 0x71,0xE8);
    sensor_write_Byte(ctx, 0x72,0xcf);
    sensor_write_Byte(ctx, 0x7E,0x03);
    sensor_write_Byte(ctx, 0x35,0x03);
    sensor_write_Byte(ctx, 0x30,0x31);
    sensor_write_Byte(ctx, 0x40,0x65);
    sensor_write_Byte(ctx, 0x7E,0x01);
//  sensor_write_Byte(ctx, 0x05,0x10);
}


int32_t mt3503_motion_status_get(const stmdev_ctx_t* ctx, mt3503_v_motion_status_t* val)
{
    mt3503_motion_status_t status;
    uint8_t reg[1];
    int32_t ret;

    ret = mt3503_read_reg(ctx, MT3503_Motion_Status, reg, 1);

    bytecpy((uint8_t*)&status, &reg[0]);

    val->is_motion = status.motion;
    val->over_flow_y = status.dyovf;
    val->over_flow_x = status.dxovf;

    return ret;
}

//get delta data x y
int32_t mt3503_data_get(const stmdev_ctx_t* ctx, mt3503_delta_xy_t* val)
{
    mt3503_motion_status_t status;
    mt3503_delta_x_lo_t delta_x_lo;
    mt3503_delta_y_lo_t delta_y_lo;
    mt3503_delta_xy_hi_t delta_xy_hi;
    uint8_t reg[1];
    int32_t ret;
    ret = mt3503_read_reg(ctx, MT3503_Motion_Status, reg, 1);
    bytecpy((uint8_t*)&status, &reg[0]);
    if(status.motion)
    {
        //read y_lo  xy_hi only motion bit is 1;
        uint8_t regx[1];
        uint8_t regy[1];
        uint8_t regxy[1];
        ret = mt3503_read_reg(ctx, MT3503_Delta_X_Lo, regx, 1);
        ret = mt3503_read_reg(ctx, MT3503_Delta_Y_Lo, regy, 1);
        ret = mt3503_read_reg(ctx, MT3503_Delta_XY_Hi, regxy, 1);

        bytecpy((uint8_t*)&delta_x_lo, &regx[0]);
        bytecpy((uint8_t*)&delta_y_lo, &regy[0]);
        bytecpy((uint8_t*)&delta_xy_hi, &regxy[0]);

        val->x = (delta_x_lo.delta_x_lo + delta_xy_hi.delta_x_hi*256)&0x0FFF;
        val->y = (delta_y_lo.delta_y_lo + delta_xy_hi.delta_y_hi*256)&0x0FFF;
        val->motion = true;
        //// 符号变换
        //if(val->x>=2048) val->x = val->x - 4096;
        //if(val->y>=2048) val->y = val->y - 4096;
    }else{
        val->x = 0;
        val->y = 0;
        val->motion = false;
    }
    return ret;
}


int32_t mt3503_sleep_mode_set(const stmdev_ctx_t* ctx, mt3503_sleep_cfg_t* val)
{
    mt3503_operation_mode_t op_mode;
    mt3503_configuration_t op2_mode;
    mt3503_sleep1_t sleep_cfg1;
    mt3503_sleep2_t sleep_cfg2;
    mt3503_sleep3_t sleep_cfg3;
    uint8_t reg[5];
    int32_t ret;

    ret = mt3503_read_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);
    bytecpy((uint8_t*)&op_mode, &reg[0]);

    if(val->sleep_enable.level == 1)
    {
        op_mode.slp_en = val->sleep_enable.enanle;

        bytecpy(&reg[0],(uint8_t*)&op_mode);

        ret = mt3503_write_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);

        if(val->change_cfg)
        {
            ret = mt3503_read_reg(ctx, MT3503_Sleep1, &(reg[2]), 1);
            bytecpy((uint8_t*)&sleep_cfg1, &reg[2]);

            sleep_cfg1.slp1_etm = val->sleep_cfg.etm;
            sleep_cfg1.slp1_freq = val->sleep_cfg.freq;

            bytecpy(&reg[2],(uint8_t*)&sleep_cfg1);
            ret = mt3503_write_reg(ctx, MT3503_Sleep1, &(reg[2]), 1);
        }
    }
    else if(val->sleep_enable.level == 2)
    {
        op_mode.slp2_en = val->sleep_enable.enanle;

        bytecpy(&reg[0],(uint8_t*)&op_mode);

        ret = mt3503_write_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);

        if(val->change_cfg)
        {
            ret = mt3503_read_reg(ctx, MT3503_Sleep2, &(reg[3]), 1);
            bytecpy((uint8_t*)&sleep_cfg2, &reg[3]);

            sleep_cfg2.slp2_etm = val->sleep_cfg.etm;
            sleep_cfg2.slp2_freq = val->sleep_cfg.freq;

            bytecpy(&reg[3],(uint8_t*)&sleep_cfg2);
            ret = mt3503_write_reg(ctx, MT3503_Sleep2, &(reg[3]), 1);
        }
    }
    else if(val->sleep_enable.level == 3)
    {
        ret = mt3503_read_reg(ctx, MT3503_Configuration, &(reg[1]), 1);
        bytecpy((uint8_t*)&op2_mode, &reg[1]);
        op2_mode.slp3_en = val->sleep_enable.enanle;
        bytecpy(&reg[1],(uint8_t*)&op2_mode);
        ret = mt3503_write_reg(ctx, MT3503_Configuration, &(reg[1]), 1);

        if(val->change_cfg)
        {
            ret = mt3503_read_reg(ctx, MT3503_Sleep3, &(reg[4]), 1);
            bytecpy((uint8_t*)&sleep_cfg3, &reg[4]);

            sleep_cfg3.slp3_etm = val->sleep_cfg.etm;
            sleep_cfg3.slp3_freq = val->sleep_cfg.freq;

            bytecpy(&reg[4],(uint8_t*)&sleep_cfg3);
            ret = mt3503_write_reg(ctx, MT3503_Sleep3, &(reg[4]), 1);
        }
    }
    else
    {

    }
    return ret;
}


/**
 * 进入不同的模式
 *  MT3503_MODE_POWER_DOWN 掉电
 *  MT3503_MODE_SLEEP2 前提是必须sleep1 enable
 *  MT3503_MODE_SLEEP3 前提是必须sleep1、2 enable
 *  MT3503_MODE_WAKEUP 唤醒
 *
*/
int32_t mt3503_mode_enter(const stmdev_ctx_t* ctx, mt3503_mode_e val)
{
    mt3503_operation_mode_t op_mode;
    mt3503_configuration_t op2_mode;
    uint8_t reg[2];
    int32_t ret;

    ret = mt3503_read_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);
    ret = mt3503_read_reg(ctx, MT3503_Configuration, &(reg[1]), 1);

    bytecpy((uint8_t*)&op_mode, &reg[0]);
    bytecpy((uint8_t*)&op2_mode, &reg[1]);

    switch (val)
    {
    case MT3503_MODE_POWER_DOWN:
        op2_mode.pd_en = 1;
        bytecpy(&reg[1],(uint8_t*)&op2_mode);
        ret = mt3503_write_reg(ctx, MT3503_Configuration, &(reg[1]), 1);
        break;
    case MT3503_MODE_RESET:
        op2_mode.reset = 1;
        bytecpy(&reg[1],(uint8_t*)&op2_mode);
        ret = mt3503_write_reg(ctx, MT3503_Configuration, &(reg[1]), 1);
        break;
    case MT3503_MODE_SLEEP1:
        op_mode.slp1mu = 1;
        bytecpy(&reg[0],(uint8_t*)&op_mode);
        ret = mt3503_write_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);
        break;
    case MT3503_MODE_SLEEP2:
        op_mode.slp2mu = 1;
        bytecpy(&reg[0],(uint8_t*)&op_mode);
        ret = mt3503_write_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);
        break;
    case MT3503_MODE_WAKEUP:
        op_mode.wakeup = 1;
        bytecpy(&reg[0],(uint8_t*)&op_mode);
        ret = mt3503_write_reg(ctx, MT3503_Operation_Mode, &(reg[0]), 1);
        break;
    default:
        break;
    }
    return ret;
}



int32_t mt3503_Write_protect_set(const stmdev_ctx_t* ctx, mt3503_wp_t* val)
{
    mt3503_write_protect_t wp_cfg;
    mt3503_key_wp_t wp_key_cfg;

    uint32_t ret = 0;
    uint8_t reg[1];

    if(val->type == MT3503_WP_TYPE_XY)
    {
        ret = mt3503_read_reg(ctx, MT3503_Write_Protect, &(reg[0]), 1);
        bytecpy((uint8_t*)&wp_cfg, &reg[0]);
        wp_cfg.wp = val->state;
        bytecpy(&reg[0],(uint8_t*)&wp_cfg);
        ret = mt3503_write_reg(ctx, MT3503_Write_Protect, &(reg[0]), 1);
    }
    else if(val->type == MT3503_WP_TYPE_KEY)
    {
        ret = mt3503_read_reg(ctx, MT3503_Key_WP, &(reg[0]), 1);
        bytecpy((uint8_t*)&wp_key_cfg, &reg[0]);
        wp_key_cfg.key_wp = val->state;
        bytecpy(&reg[0],(uint8_t*)&wp_key_cfg);
        ret = mt3503_write_reg(ctx, MT3503_Key_WP, &(reg[0]), 1);
    }
    return ret;
}

//设置RES_X值(刻度) 用于校正旋钮旋转一圈的值到一个设定值
int32_t mt3503_res_x_set(const stmdev_ctx_t* ctx, uint16_t val)
{
    uint8_t reg[2];
    mt3503_res_x_lo_t res_x_lo;
    mt3503_res_xy_hi_t res_xy_hi;

    mt3503_read_reg(ctx, MT3503_RES_X_Lo, &(reg[0]), 1);
    mt3503_read_reg(ctx, MT3503_RES_XY_Hi, &(reg[1]), 1);

    bytecpy((uint8_t*)&res_x_lo, &reg[0]);
    bytecpy((uint8_t*)&res_xy_hi, &reg[1]);

    res_x_lo.res_x = val & 0xFF;
    res_xy_hi.res_x_hi = (val>>8) & 0x0F;
    bytecpy(&reg[0],(uint8_t*)&res_x_lo);
    bytecpy(&reg[1],(uint8_t*)&res_xy_hi);
    mt3503_write_reg(ctx, MT3503_RES_X_Lo, &(reg[0]), 1);
    mt3503_write_reg(ctx, MT3503_RES_XY_Hi, &(reg[1]), 1);

    return 0;
}

int32_t mt3503_res_x_get(const stmdev_ctx_t* ctx, uint16_t *val)
{
    uint8_t reg[2];
    mt3503_res_x_lo_t res_x_lo;
    mt3503_res_xy_hi_t res_xy_hi;

    mt3503_read_reg(ctx, MT3503_RES_X_Lo, &(reg[0]), 1);
    mt3503_read_reg(ctx, MT3503_RES_XY_Hi, &(reg[1]), 1);

    bytecpy((uint8_t*)&res_x_lo, &reg[0]);
    bytecpy((uint8_t*)&res_xy_hi, &reg[1]);

    uint16_t value = res_xy_hi.res_x_hi;

    value = (value<<8)|res_x_lo.res_x;

    *val = value;

    return 0;
}

#endif //BSP_USING_MT3503
