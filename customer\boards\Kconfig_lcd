if BSP_USING_LCD
    choice
        prompt "Select LCD"
        default LCD_USING_ED_LB55DSI17801
        depends on BF0_HCPU
        depends on BSP_USING_RTTHREAD

        config LCD_USING_ED_LB55DSI17801
            bool "1.78 rect DSI LCD(ED-LB55DSI17801)"
            depends on BSP_SUPPORT_DSI
            select TSC_USING_TMA525B if BSP_USING_TOUCHD
            select LCD_USING_RM69090
            select BSP_LCDC_USING_DSI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55DSI17801
               config LCD_RM69090_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_ED_LB55DSI17801_QADSPI
            bool "1.78 rect QAD-SPI LCD(ED-LB55DSI17801)"
            select TSC_USING_TMA525B if BSP_USING_TOUCHD
            select LCD_USING_RM69090
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55DSI17801_QADSPI
               config LCD_RM69090_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_ED_LB55SPI17801
            bool "1.78 rect QAD-SPI LCD(ED-LB55SPI17801)"
            select TSC_USING_FT3168 if BSP_USING_TOUCHD
            select LCD_USING_RM69090
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55SPI17801
               config LCD_RM69090_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool n
            endif

        config LCD_USING_ED_LB55BILI8688E
            bool "1.78 rect QAD-SPI LCD(ED-LB55DSI17801 - Green)"
            select LCD_USING_ILI8688E
            select TSC_USING_CST918 if BSP_USING_TOUCHD
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55BILI8688E
               config LCD_ILI8688E_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ILI8688E_DSI
            bool "1.78 rect DSI LCD(ED-LB55DSI17801 - Green)"
            select LCD_USING_ILI8688E
            select TSC_USING_CST918 if BSP_USING_TOUCHD
            select BSP_LCDC_USING_DSI
            select BSP_USING_RECT_TYPE_LCD
            depends on BSP_SUPPORT_DSI
            if LCD_USING_ILI8688E_DSI
                config LCD_ILI8688E_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
           
        config LCD_USING_ED_LB55SPI17201
            bool "1.72 rect QAD-SPI LCD(ED-LB55SPI17201)"
            select LCD_USING_SPD2012
            select BSP_LCDC_USING_QADSPI
            select BL_USING_AW9364
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55SPI17201
               config LCD_SPD2012_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ED_LB55SPI17202
            bool "1.77 rect QAD-SPI LCD(ED-LB55SPI17202)"
            select TSC_USING_CST816 if BSP_USING_TOUCHD
            select LCD_USING_GC9B71
            select BSP_LCDC_USING_QADSPI
            select LCD_USING_PWM_AS_BACKLIGHT
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55SPI17202
               config LCD_GC9B71_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_ED_LB55SPI17601
            bool "1.77 rect QAD-SPI RAMLESS LCD(ED-LB55SPI17601)"
            select TSC_USING_BL6133 if BSP_USING_TOUCHD
            select LCD_USING_ST77903
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RAMLESS_LCD
            select LCD_USING_PWM_AS_BACKLIGHT
            select BSP_USING_RECT_TYPE_LCD

            
        config LCD_USING_ED_LB55SPI17701
            bool "1.77 rect QAD-SPI LCD(ED-LB55SPI17701)"
            select TSC_USING_CST816 if BSP_USING_TOUCHD
            select LCD_USING_GC9B71
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB55SPI17701
               config LCD_GC9B71_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
        config LCD_USING_ED_LB55DSI13901
            bool "1.39 round 454RGB*454 DSI LCD(ED-LB55DSI13901)"
            depends on BSP_SUPPORT_DSI
            select TSC_USING_ZTW622 if BSP_USING_TOUCHD
            select LCD_USING_RM69090
            select BSP_LCDC_USING_DSI
            select BSP_USING_ROUND_TYPE_LCD
            if LCD_USING_ED_LB55DSI13901
               config LCD_RM69090_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ED_LB55DSI13902
            bool "1.39 round 454RGB*454 DSI LCD(ED-LB55DSI13902)"
            depends on BSP_SUPPORT_DSI            
            select TSC_USING_TMA525B if BSP_USING_TOUCHD
            select LCD_USING_RM69330
            select BSP_LCDC_USING_DSI
            select BSP_USING_ROUND_TYPE_LCD
            if LCD_USING_ED_LB55DSI13902
               config LCD_RM69330_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_ED_LB55DSI13903
            bool "1.39 round 454RGB*454 DSI LCD(ED-LB55DSI13903)"
            depends on BSP_SUPPORT_DSI
            select TSC_USING_IT7259E if BSP_USING_TOUCHD
            select LCD_USING_RM69330
            select BSP_LCDC_USING_DSI
            select BSP_USING_ROUND_TYPE_LCD
            if LCD_USING_ED_LB55DSI13903
               config LCD_RM69330_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool n
            endif

        config LCD_USING_ED_LB55DSI_ICN3311
            bool "round DSI LCD(ED_LB55DSI_ICN3311)"
            depends on BSP_SUPPORT_DSI
            select TSC_USING_CST918_0x15 if BSP_USING_TOUCHD
            select LCD_USING_ICN3311
            select BSP_LCDC_USING_DSI
            select BSP_USING_ROUND_TYPE_LCD
            if LCD_USING_ED_LB55DSI_ICN3311
               config LCD_ICN3311_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ATK7016_ASIC
            bool "ALIENTEK 7INCH RGB DPI TFTLCD"
            select LCD_USING_ATK7016
            select BSP_LCDC_USING_DPI
            select BSP_USING_RECT_TYPE_LCD
            depends on BSP_SUPPORT_DPI

        config LCD_USING_ST7701S_TFT
            bool "3.97 inch 480x800 DSI TFT LCD ST7701S"
            depends on BSP_SUPPORT_DSI
            select LCD_USING_ST7701S
            select BSP_LCDC_USING_DSI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ST7701S_TFT
                config LCD_ST7701S_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool n
            endif

        config LCD_USING_JDI387A_JDI
            bool "1.2 round JDI387A LCD(use LCDC2)"
            select LCD_USING_JDI387A
            select BSP_LCDC_USING_JDI_PARALLEL
            select BSP_USE_LCDC2_ON_HPSYS
            select BSP_USING_ROUND_TYPE_LCD

        config LCD_USING_LS013B7DD02_JDI
            bool "1.2 round JDI LS013B7DD02 LCD"
            select LCD_USING_LS013B7DD02
            select BSP_LCDC_USING_JDI_PARALLEL
            select BSP_USING_ROUND_TYPE_LCD


        config LCD_USING_ED_LB5XSPI18501
            bool "1.85 rect QAD-SPI LCD(ED_LB5XSPI18501)"
            select TSC_USING_CST816 if BSP_USING_TOUCHD
            select LCD_USING_GC9B71
            select BSP_LCDC_USING_QADSPI
            select LCD_USING_PWM_AS_BACKLIGHT
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB5XSPI18501
               config LCD_GC9B71_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ED_LB5XSPI19701
            bool "1.97 rect QAD-SPI LCD(ED-LB5xSPI19701)"
            select TSC_USING_FT3169 if BSP_USING_TOUCHD
            select LCD_USING_FT2308
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB5XSPI19701
               config LCD_FT2308_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_ED_LB5XSPI19701_DDR
            bool "1.97 rect DDR QAD-SPI LCD(ED-LB5xSPI19701)"
            select TSC_USING_FT3169 if BSP_USING_TOUCHD
            select LCD_USING_FT2308
            select BSP_LCDC_USING_DDR_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB5XSPI19701_DDR
               config LCD_FT2308_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
	    
         config LCD_USING_ED_LB5XSPI14901
            bool "1.49 rect QAD-SPI LCD(ED_LB5XSPI14901)"
            select TSC_USING_FT3168 if BSP_USING_TOUCHD
            select LCD_USING_RM690C0
            select BSP_LCDC_USING_QADSPI
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB5XSPI14901
               config LCD_RM690C0_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool n
            endif
	    
         config LCD_USING_ED_LB5XSPI14901_DDR
            bool "1.49 rect DDR QAD-SPI LCD(ED_LB5XSPI14901)"
            select TSC_USING_FT3168 if BSP_USING_TOUCHD
            select LCD_USING_RM690C0
            select BSP_LCDC_USING_DDR_QADSPI 
            select BSP_USING_RECT_TYPE_LCD
            if LCD_USING_ED_LB5XSPI14901_DDR
               config LCD_RM690C0_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool n
            endif	
    	 
         config LCD_USING_SPI_ST7789_GTM024_08_SPI8P
            bool "2.4 rect SPI LCD for test box(GTM024_08_SPI8P_LCM)"
            select LCD_USING_ST7789_GTM024_08_SPI8P
            select BSP_LCDC_USING_SPI_DCX_1DATA
            select BSP_USING_RECT_TYPE_LCD

    endchoice

    config LCD_HOR_RES_MAX
        int
        default 368 if LCD_USING_ED_LB55DSI17801
        default 368 if LCD_USING_ED_LB55DSI17801_QADSPI
        default 368 if LCD_USING_ED_LB55SPI17801
        default 454 if LCD_USING_ED_LB55DSI13901
        default 454 if LCD_USING_ED_LB55DSI13902
        default 454 if LCD_USING_ED_LB55DSI13903
        default 454 if LCD_USING_ICN3311
        default 368 if LCD_USING_ED_LB55BILI8688E
        default 368 if LCD_USING_ILI8688E_DSI
        default 1024 if LCD_USING_ATK7016_ASIC
        default 356 if LCD_USING_ED_LB55SPI17201
        default 320 if LCD_USING_ED_LB55SPI17202
        default 480 if LCD_USING_ST7701S
        default 240 if LCD_USING_JDI387A_JDI
        default 260 if LCD_USING_LS013B7DD02_JDI
        default 320 if LCD_USING_ED_LB5XSPI18501
        default 320 if LCD_USING_ED_LB55SPI17601
        default 410 if LCD_USING_ED_LB5XSPI19701
        default 410 if LCD_USING_ED_LB5XSPI19701_DDR
        default 480 if LCD_USING_ED_LB5XSPI14901
        default 480 if LCD_USING_ED_LB5XSPI14901_DDR
        default 320 if LCD_USING_SPI_ST7789_GTM024_08_SPI8P

        
    config LCD_VER_RES_MAX
        int
        default 448 if LCD_USING_ED_LB55DSI17801
        default 448 if LCD_USING_ED_LB55DSI17801_QADSPI
        default 448 if LCD_USING_ED_LB55SPI17801
        default 454 if LCD_USING_ED_LB55DSI13901
        default 454 if LCD_USING_ED_LB55DSI13902
        default 454 if LCD_USING_ED_LB55DSI13903
        default 454 if LCD_USING_ICN3311
        default 448 if LCD_USING_ED_LB55BILI8688E
        default 448 if LCD_USING_ILI8688E_DSI
        default 600 if LCD_USING_ATK7016_ASIC
        default 400 if LCD_USING_ED_LB55SPI17201
        default 380 if LCD_USING_ED_LB55SPI17202
        default 800 if LCD_USING_ST7701S
        default 240 if LCD_USING_JDI387A_JDI
        default 260 if LCD_USING_LS013B7DD02_JDI
        default 386 if LCD_USING_ED_LB5XSPI18501
        default 385 if LCD_USING_ED_LB55SPI17601
        default 494 if LCD_USING_ED_LB5XSPI19701
        default 494 if LCD_USING_ED_LB5XSPI19701_DDR
        default 480 if LCD_USING_ED_LB5XSPI14901
        default 480 if LCD_USING_ED_LB5XSPI14901_DDR
        default 240 if LCD_USING_SPI_ST7789_GTM024_08_SPI8P

    config LCD_DPI
        int
        default 315 if LCD_USING_ED_LB55DSI17801
        default 315 if LCD_USING_ED_LB55DSI17801_QADSPI
        default 315 if LCD_USING_ED_LB55SPI17801
        default 315 if LCD_USING_ED_LB55DSI13901
        default 315 if LCD_USING_ED_LB55DSI13902
        default 315 if LCD_USING_ED_LB55DSI13903
        default 315 if LCD_USING_ED_LB55BILI8688E
        default 315 if LCD_USING_ILI8688E_DSI
        default 315 if LCD_USING_ICN3311        
        default 100 if LCD_USING_ATK7016_ASIC
        default 315 if LCD_USING_ED_LB55SPI17201
        default 315 if LCD_USING_ED_LB55SPI17202
        default 315 if LCD_USING_ST7701S
        default 200 if LCD_USING_JDI387A_JDI
        default 210 if LCD_USING_LS013B7DD02_JDI
        default 315 if LCD_USING_ED_LB5XSPI18501
        default 315 if LCD_USING_ED_LB55SPI17601
        default 315 if LCD_USING_ED_LB5XSPI19701
        default 315 if LCD_USING_ED_LB5XSPI19701_DDR
        default 315 if LCD_USING_ED_LB5XSPI14901
        default 315 if LCD_USING_ED_LB5XSPI14901_DDR
        default 167 if LCD_USING_SPI_ST7789_GTM024_08_SPI8P
        
    menu "Touch config"
        config BSP_TOUCH_IRQ_FROM_DATASVC
            bool "Touch IRQ using data service in LCPU"
            default y
            depends on BSP_USING_DATA_SVC && SOC_SF32LB55X

        config TOUCH_DEVICE_NAME
            string "Touch device bus name"
            default "i2c1"
    endmenu
endif

menuconfig BSP_USING_LCD_LCPU
    bool "Enable LCPU LCD on the board"
        select BSP_USING_LCDC
        default n
        depends on BF0_LCPU   
        depends on BSP_USING_RTTHREAD

    if BSP_USING_LCD_LCPU
        config LCD_USING_ED_LB55DSI17801_LCPU
            bool "1.78 rect QADSPI LCD(ED-LB55DSI17801)"
            depends on BF0_LCPU
            select LCD_USING_RM69090
            select BSP_LCDC_USING_QADSPI
            if LCD_USING_ED_LB55DSI17801_QADSPI_LB555
                config LCD_RM69090_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif
            
        config LCD_USING_ILI8688E_QADSPI_LCPU
            bool "RECT QADSPI LCD(ED-LB55DSI17801 - Green)"
            select LCD_USING_ILI8688E
            select TSC_USING_CST918 if BSP_USING_TOUCHD
            select BSP_LCDC_USING_QADSPI
            if LCD_USING_ILI8688E_QADSPI_LCPU
                config LCD_ILI8688E_VSYNC_ENABLE
                    bool "Enable LCD VSYNC (TE signal)"
                    def_bool y
            endif

        config LCD_USING_JDI387A_JDI_LCPU
            bool "1.2 round JDI LCD"
            select LCD_USING_JDI387A
            select BSP_LCDC_USING_JDI_PARALLEL
    endif

    config LCD_HOR_RES_MAX
        int
        default 368 if LCD_USING_ED_LB55DSI17801_LCPU
        default 368 if LCD_USING_ILI8688E_QADSPI_LCPU
        default 240 if LCD_USING_JDI387A_JDI_LCPU
    config LCD_VER_RES_MAX
        int
        default 448 if LCD_USING_ED_LB55DSI17801_LCPU
        default 448 if LCD_USING_ILI8688E_QADSPI_LCPU
        default 240 if LCD_USING_JDI387A_JDI_LCPU
    config LCD_DPI
        int
        default 315 if LCD_USING_ED_LB55DSI17801_LCPU
        default 315 if LCD_USING_ILI8688E_QADSPI_LCPU
        default 200 if LCD_USING_JDI387A_JDI_LCPU
