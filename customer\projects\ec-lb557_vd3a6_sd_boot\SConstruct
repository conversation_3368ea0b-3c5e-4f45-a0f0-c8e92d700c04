import os
import rtconfig

# Check SDK 
SIFLI_SDK = os.getenv('SIFLI_SDK')
if not SIFLI_SDK:
    print("Please run set_env.bat in root folder of SIFLI SDK to set environment.")
    exit()
from building import *

# Set default compile options.
SifliEnv()

################################## change rtconfig.xxx to customize build ########################################
# print (rtconfig.OUTPUT_DIR)

OUTPUT_DIR=rtconfig.OUTPUT_DIR
TARGET = OUTPUT_DIR + rtconfig.TARGET_NAME + '.' + rtconfig.TARGET_EXT
env = Environment(tools = ['mingw'],
    AS = rtconfig.AS, ASFLAGS = rtconfig.AFLAGS,
    CC = rtconfig.CC, CCFLAGS = rtconfig.CFLAGS,
    AR = rtconfig.AR, ARFLAGS = '-rc',
    LINK = rtconfig.LINK, LINKFLAGS = rtconfig.LFLAGS)
env.PrependENVPath('PATH', rtconfig.EXEC_PATH)

AddOption('--patch',
  dest='patch',
  nargs=1, type='int',
  action='store',
  metavar='DIR',
  help='patch to be built')

patch = GetOption('patch')

if (patch==1):
    # prepare building environment
    objs = PrepareBuilding(env)
    #Build patch
    env['LINKFLAGS'] = "--cpu=Cortex-M33 --strict --scatter ./board/linker_scripts/patch_link.sct --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols --info sizes --info totals --info unused --info veneers --list build/patch.map --libpath=C:/Keil_v5/ARM/ARMCLANG/lib build/bootloader.symdefs "
    TARGET2 = 'boot_patch' + '.' + rtconfig.TARGET_EXT    
    objs_2 = SConscript('../../patch/patch_scon', duplicate=0)
    rtconfig.POST_ACTION = 'fromelf --bin $TARGET --output build/boot_patch.bin \nfromelf -z $TARGET'
    DoBuilding(TARGET2, objs_2)
else:
    # prepare building environment
    objs = PrepareBuilding(env)
    # make a building
    DoBuilding(TARGET, objs)
    
    
