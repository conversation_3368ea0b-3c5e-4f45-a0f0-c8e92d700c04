#ifndef __TSEN_H
#define __TSEN_H

typedef struct
{
    __IO uint32_t TSEN_CTRL_REG;
    __IO uint32_t TSEN_RDATA;
    __IO uint32_t TSEN_IRQ;
#ifndef SF32LB52X
    __IO uint32_t ANAU_RESERVE;
    __IO uint32_t ANAU_ANA_TP;
    __IO uint32_t BGR;
#endif
} TSEN_TypeDef;


/*************** Bit definition for TSEN_TSEN_CTRL_REG register ***************/
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_PU_Pos  (0U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_PU_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_PU_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_PU  TSEN_TSEN_CTRL_REG_ANAU_TSEN_PU_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RSTB_Pos  (1U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RSTB_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_RSTB_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RSTB  TSEN_TSEN_CTRL_REG_ANAU_TSEN_RSTB_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RUN_Pos  (2U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RUN_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_RUN_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RUN  TSEN_TSEN_CTRL_REG_ANAU_TSEN_RUN_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_IG_VBE_Pos  (3U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_IG_VBE_Msk  (0x7UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_IG_VBE_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_IG_VBE  TSEN_TSEN_CTRL_REG_ANAU_TSEN_IG_VBE_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_FCK_SEL_Pos  (6U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_FCK_SEL_Msk  (0x3UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_FCK_SEL_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_FCK_SEL  TSEN_TSEN_CTRL_REG_ANAU_TSEN_FCK_SEL_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SGN_EN_Pos  (8U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SGN_EN_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_SGN_EN_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SGN_EN  TSEN_TSEN_CTRL_REG_ANAU_TSEN_SGN_EN_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SER_PAR_SEL_Pos  (9U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SER_PAR_SEL_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_SER_PAR_SEL_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_SER_PAR_SEL  TSEN_TSEN_CTRL_REG_ANAU_TSEN_SER_PAR_SEL_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RDY_Pos  (10U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RDY_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_RDY_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_RDY  TSEN_TSEN_CTRL_REG_ANAU_TSEN_RDY_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_EN_Pos  (11U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_EN_Msk  (0x1UL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_EN_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_EN  TSEN_TSEN_CTRL_REG_ANAU_TSEN_EN_Msk
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_CLK_DIV_Pos  (12U)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_CLK_DIV_Msk  (0x3FUL << TSEN_TSEN_CTRL_REG_ANAU_TSEN_CLK_DIV_Pos)
#define TSEN_TSEN_CTRL_REG_ANAU_TSEN_CLK_DIV  TSEN_TSEN_CTRL_REG_ANAU_TSEN_CLK_DIV_Msk

/**************** Bit definition for TSEN_TSEN_RDATA register *****************/
#define TSEN_TSEN_RDATA_TSEN_RDATA_Pos  (0U)
#define TSEN_TSEN_RDATA_TSEN_RDATA_Msk  (0xFFFUL << TSEN_TSEN_RDATA_TSEN_RDATA_Pos)
#define TSEN_TSEN_RDATA_TSEN_RDATA      TSEN_TSEN_RDATA_TSEN_RDATA_Msk

/***************** Bit definition for TSEN_TSEN_IRQ register ******************/
#define TSEN_TSEN_IRQ_TSEN_ICR_Pos      (0U)
#define TSEN_TSEN_IRQ_TSEN_ICR_Msk      (0x1UL << TSEN_TSEN_IRQ_TSEN_ICR_Pos)
#define TSEN_TSEN_IRQ_TSEN_ICR          TSEN_TSEN_IRQ_TSEN_ICR_Msk
#define TSEN_TSEN_IRQ_TSEN_IMR_Pos      (1U)
#define TSEN_TSEN_IRQ_TSEN_IMR_Msk      (0x1UL << TSEN_TSEN_IRQ_TSEN_IMR_Pos)
#define TSEN_TSEN_IRQ_TSEN_IMR          TSEN_TSEN_IRQ_TSEN_IMR_Msk
#define TSEN_TSEN_IRQ_TSEN_IRSR_Pos     (2U)
#define TSEN_TSEN_IRQ_TSEN_IRSR_Msk     (0x1UL << TSEN_TSEN_IRQ_TSEN_IRSR_Pos)
#define TSEN_TSEN_IRQ_TSEN_IRSR         TSEN_TSEN_IRQ_TSEN_IRSR_Msk
#define TSEN_TSEN_IRQ_TSEN_ISR_Pos      (3U)
#define TSEN_TSEN_IRQ_TSEN_ISR_Msk      (0x1UL << TSEN_TSEN_IRQ_TSEN_ISR_Pos)
#define TSEN_TSEN_IRQ_TSEN_ISR          TSEN_TSEN_IRQ_TSEN_ISR_Msk

#ifndef SF32LB52X
    /*************** Bit definition for TSEN_ANAU_RESERVE register ****************/
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE0_Pos  (0U)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE0_Msk  (0xFFUL << TSEN_ANAU_RESERVE_ANAU_RESERVE0_Pos)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE0  TSEN_ANAU_RESERVE_ANAU_RESERVE0_Msk
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE1_Pos  (8U)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE1_Msk  (0xFFUL << TSEN_ANAU_RESERVE_ANAU_RESERVE1_Pos)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE1  TSEN_ANAU_RESERVE_ANAU_RESERVE1_Msk
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE2_Pos  (16U)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE2_Msk  (0xFFUL << TSEN_ANAU_RESERVE_ANAU_RESERVE2_Pos)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE2  TSEN_ANAU_RESERVE_ANAU_RESERVE2_Msk
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE3_Pos  (24U)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE3_Msk  (0xFFUL << TSEN_ANAU_RESERVE_ANAU_RESERVE3_Pos)
    #define TSEN_ANAU_RESERVE_ANAU_RESERVE3  TSEN_ANAU_RESERVE_ANAU_RESERVE3_Msk

    /**************** Bit definition for TSEN_ANAU_ANA_TP register ****************/
    #define TSEN_ANAU_ANA_TP_ANAU_IARY_EN_Pos  (0U)
    #define TSEN_ANAU_ANA_TP_ANAU_IARY_EN_Msk  (0x1UL << TSEN_ANAU_ANA_TP_ANAU_IARY_EN_Pos)
    #define TSEN_ANAU_ANA_TP_ANAU_IARY_EN   TSEN_ANAU_ANA_TP_ANAU_IARY_EN_Msk
    #ifndef SF32LB55X
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR_Pos  (1U)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR_Msk  (0x7UL << TSEN_ANAU_ANA_TP_ANAU_DC_MR_Pos)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR     TSEN_ANAU_ANA_TP_ANAU_DC_MR_Msk
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR_Pos  (4U)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR_Msk  (0x7UL << TSEN_ANAU_ANA_TP_ANAU_DC_TR_Pos)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR     TSEN_ANAU_ANA_TP_ANAU_DC_TR_Msk
    #else
        #define TSEN_ANAU_ANA_TP_CHIP_DC_TE_Pos  (1U)
        #define TSEN_ANAU_ANA_TP_CHIP_DC_TE_Msk  (0x1UL << TSEN_ANAU_ANA_TP_CHIP_DC_TE_Pos)
        #define TSEN_ANAU_ANA_TP_CHIP_DC_TE     TSEN_ANAU_ANA_TP_CHIP_DC_TE_Msk
        #define TSEN_ANAU_ANA_TP_CHIP_DC_UR_Pos  (2U)
        #define TSEN_ANAU_ANA_TP_CHIP_DC_UR_Msk  (0x7UL << TSEN_ANAU_ANA_TP_CHIP_DC_UR_Pos)
        #define TSEN_ANAU_ANA_TP_CHIP_DC_UR     TSEN_ANAU_ANA_TP_CHIP_DC_UR_Msk
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR_Pos  (5U)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR_Msk  (0x7UL << TSEN_ANAU_ANA_TP_ANAU_DC_MR_Pos)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_MR     TSEN_ANAU_ANA_TP_ANAU_DC_MR_Msk
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR_Pos  (8U)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR_Msk  (0x7UL << TSEN_ANAU_ANA_TP_ANAU_DC_TR_Pos)
        #define TSEN_ANAU_ANA_TP_ANAU_DC_TR     TSEN_ANAU_ANA_TP_ANAU_DC_TR_Msk
    #endif /* SF32LB55X */
    /******************** Bit definition for TSEN_BGR register ********************/
    #define TSEN_BGR_EN_Pos                 (0U)
    #define TSEN_BGR_EN_Msk                 (0x1UL << TSEN_BGR_EN_Pos)
    #define TSEN_BGR_EN                     TSEN_BGR_EN_Msk
    #define TSEN_BGR_VREF06_Pos             (4U)
    #define TSEN_BGR_VREF06_Msk             (0xFUL << TSEN_BGR_VREF06_Pos)
    #define TSEN_BGR_VREF06                 TSEN_BGR_VREF06_Msk
    #define TSEN_BGR_VREF12_Pos             (8U)
    #define TSEN_BGR_VREF12_Msk             (0xFUL << TSEN_BGR_VREF12_Pos)
    #define TSEN_BGR_VREF12                 TSEN_BGR_VREF12_Msk

#endif  /* SF32LB52X */

#endif
