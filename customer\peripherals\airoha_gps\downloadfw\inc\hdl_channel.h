/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_CHANNEL_H__
#define __HDL_CHANNEL_H__

#include "hdl_ports/hdl_uart.h"
#include "hdl_data.h"
#include "hdl_ports/hdl_os_util.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    CHANNEL_TYPE_UART = 0,
    CHANNEL_TYPE_SPI,
    CHANNEL_TYPE_I2C
} HDL_CHANNEL_TYPE;

typedef struct {
   bool     (*m_com_init)                  (void);
   uint8_t  (*m_com_get_byte)              (void);
   bool     (*m_com_get_byte_status)       (uint8_t *data);
   uint32_t (*m_com_get_byte_buffer)       (uint8_t *buf, uint32_t length);
   void     (*m_com_put_byte)              (uint8_t data);
   uint32_t (*m_com_put_byte_buffer)       (uint8_t *buf, uint32_t length);
   void     (*m_com_put_byte_complete)     (uint8_t data);
   uint16_t (*m_com_get_data16)            (void);
   void     (*m_com_put_data16)            (uint16_t data);
   uint32_t (*m_com_get_data32)            (void);
   void     (*m_com_put_data32)            (uint32_t data);
   void     (*m_com_purge_fifo)            (void);
   void     (*m_com_set_baudrate)          (uint32_t baud_rate);
   void     (*m_com_setup_new_port)        (void);
   bool     (*m_com_deinit)                (void);
} g_hdl_channel_t;

extern g_hdl_channel_t g_hdl_channel;

#define HDL_COM_Init                          g_hdl_channel.m_com_init
#define HDL_COM_GetByte                       g_hdl_channel.m_com_get_byte
#define HDL_COM_GetByteStatus(data)           g_hdl_channel.m_com_get_byte_status(data)
#define HDL_COM_GetByte_Buffer(buf,length)    g_hdl_channel.m_com_get_byte_buffer(buf, length)
#define HDL_COM_PutByte(data)                 g_hdl_channel.m_com_put_byte(data)
#define HDL_COM_PutByte_Buffer(buf,length)    g_hdl_channel.m_com_put_byte_buffer(buf, length)
#define HDL_COM_PutByte_Complete(data)        g_hdl_channel.m_com_put_byte_complete(data)
#define HDL_COM_GetData16                     g_hdl_channel.m_com_get_data16
#define HDL_COM_PutData16(data)               g_hdl_channel.m_com_put_data16(data)
#define HDL_COM_GetData32                     g_hdl_channel.m_com_get_data32
#define HDL_COM_PutData32(data)               g_hdl_channel.m_com_put_data32(data)
#define HDL_COM_PurgeFIFO                     g_hdl_channel.m_com_purge_fifo
#define HDL_COM_SetBaudRate(baud_rate)        g_hdl_channel.m_com_set_baudrate(baud_rate)
#define HDL_COM_SetupNewPort                  g_hdl_channel.m_com_setup_new_port
#define HDL_COM_Deinit                        g_hdl_channel.m_com_deinit

bool    hdl_channel_init(void);
uint8_t hdl_get_channel_type();

#ifdef __cplusplus
}
#endif

#endif //__HDL_CHANNEL_H__

