/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AW86224.h
@Time    :   2025/03/12 09:14:24
@Brief   :
@Details :
*
************************************************************/

#ifndef _AW86224_H_
#define _AW86224_H_

#include <stdint.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "bsp_board.h"
#include "haptic_nv.h"
#include "haptic_nv_reg.h"
#include "pmic_controller.h"


#define AW86224_MAX_GAIN 0x50


#define AW86224_ADDRESS 0x58

#define AW86224_ID 0x10

#define AW86224_ENABLE_IO VIBRATOR_RST_PIN //马达驱动芯片复位引脚 PA08
#define AW86224_EXT_INTERRUPT_PIN VIBRATOR_INT_PIN //马达驱动芯片中断引脚 PA00

//模式枚举
typedef enum {
    AW86224_NOMAL_MODE = 0,
    AW86224_SLEEP_MODE = 1
} AW86224_PowerMode_e;



//初始化
extern int32_t AW86224_Init(void);
extern int32_t AW86224_DeInit(void);

//BOOT里的马达振动提醒
int32_t AW86224_Remind(void);
//检查ID
extern uint8_t AW86224_CheckID(void);
//读取数据
extern int32_t AW86224_Control(uint16_t pdata);
//切换模式
extern int32_t AW86224_Switch_PowerMode(AW86224_PowerMode_e mode);

int32_t vibration_init(void);

int32_t vibration_deinit(void);

int32_t set_vibration_gain(uint8_t gain);

uint8_t get_vibration_gain(void);

int32_t set_vibration_start(uint8_t* rtp_data,uint32_t rtp_len,uint8_t gain);

int32_t set_vibration_cont(uint8_t index, uint8_t gain);               //连续振动

int32_t set_vibration_short(uint8_t index, uint8_t gain,uint8_t loop); //短暂振动

int32_t set_vibration_stop(void);

int32_t vibration_f0_check(uint32_t *freq);

int32_t vibration_f0_calib(uint32_t *freq);  //F0 校正

int32_t vibration_f0_calib_set(uint16_t f0_pre,uint8_t f0_cali_percent); //F0 校正设置

int32_t vibration_osc_check(uint32_t *ocs);

int32_t vibration_res_check(uint32_t *res);

int32_t vibration_vbat_check(uint32_t *vbat);

#endif /* DRIVERS_I2C_DEVICE_ALS_ALS_AW86224_H_ */
