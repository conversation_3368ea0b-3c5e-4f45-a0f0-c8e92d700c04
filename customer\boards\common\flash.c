/**
  ******************************************************************************
  * @file   flash.c
  * <AUTHOR> software development team
  * @brief Nor Flash Controller BSP driver
  This driver is validated by using MSH command 'date'.
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of <PERSON><PERSON><PERSON> nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "rtconfig.h"

#ifndef BSP_USING_PC_SIMULATOR

#include "bsp_board.h"
#include "dma_config.h"
#include "flash_config.h"
#include "drv_io.h"
#include "flash_table.h"
#include "register.h"

//#define EN_FLASH_WDT

static QSPI_FLASH_CTX_T spi_flash_handle[FLASH_MAX_INSTANCE];
static DMA_HandleTypeDef spi_flash_dma_handle[FLASH_MAX_INSTANCE];

int8_t Addr2Id(uint32_t addr)
{
    int8_t id = -1;
    uint32_t i;

    for (i = 0; i < FLASH_MAX_INSTANCE; i++)
    {
        if ((addr >= spi_flash_handle[i].base_addr)
                && (addr < (spi_flash_handle[i].base_addr + spi_flash_handle[i].total_size)))
        {
            id = i;
            break;
        }
    }
    if (i >= FLASH_MAX_INSTANCE)
    {
        id = -1;
    }

    return id;
}

void *BSP_Flash_get_handle(uint32_t addr)
{
    int8_t id = 0;

    id = Addr2Id(addr);

    if (id < 0)
        return NULL;

    return (void *) & (spi_flash_handle[id].handle);
}

static void *flash_memset(void *s, int c, long count)
{
    char *xs = (char *)s;

    while (count--)
        *xs++ = c;

    return s;
}

__weak int nor_lock(uint32_t addr)
{
    return 0;
}

__weak int nor_unlock(uint32_t addr)
{
    return 0;
}


void BSP_Flash_var_init(void)
{
    flash_memset(spi_flash_handle, 0, sizeof(spi_flash_handle));
    HAL_Delay_us(0);
}

int BSP_Flash_read_id(uint32_t addr)
{
    int8_t id = Addr2Id(addr);
    if (id < 0)
        return FLASH_UNKNOW_ID;

    if (spi_flash_handle[id].dev_id != FLASH_UNKNOW_ID)
        return spi_flash_handle[id].dev_id;

    spi_flash_handle[id].dev_id =   HAL_QSPI_READ_ID(&spi_flash_handle[id].handle);

    return spi_flash_handle[id].dev_id;
}

int BSP_Nor_erase(void *handle, uint32_t addr, uint32_t size)
{
    uint32_t al_size;
    uint32_t al_addr;
    int ret = 0;
    FLASH_HandleTypeDef *hflash = (FLASH_HandleTypeDef *)handle;
    //rt_kprintf("rt_nor_erase_rom addr 0x%x, size 0x%x\n", addr, size);

    if (hflash == NULL)
        return -1;
    if (size == 0)
        return 0;

    if (size >= hflash->size)
    {
        nor_lock(hflash->base);
        ret = HAL_QSPIEX_CHIP_ERASE(hflash);
        nor_unlock(hflash->base);
        return ret;
    }
    // address to offset if needed
    if (addr >= hflash->base)
        addr -= hflash->base;
    if (!IS_ALIGNED((QSPI_NOR_SECT_SIZE << hflash->dualFlash), addr))
    {
        HAL_ASSERT(0);
        ret = -1;
        goto _exit;
    }
    if (!IS_ALIGNED((QSPI_NOR_SECT_SIZE << hflash->dualFlash), size))
    {
        HAL_ASSERT(0);
        ret = -1;
        goto _exit;
    }

    // page erase not support, start addr should be aligned.
    al_addr = GET_ALIGNED_DOWN((QSPI_NOR_SECT_SIZE << hflash->dualFlash), addr);
    al_size = GET_ALIGNED_UP((QSPI_NOR_SECT_SIZE << hflash->dualFlash), size);

    //rt_kprintf("flash erase from 0x%x + %d to 0x%x + %d\n", addr, size, al_addr, al_size);

alig64k:
    // 1 block 64k aligned, for start addr not aligned do not process, need support later
    if (IS_ALIGNED((QSPI_NOR_BLK64_SIZE << hflash->dualFlash), al_addr) && (al_size >= (QSPI_NOR_BLK64_SIZE << hflash->dualFlash))) // block erease first
    {
        while (al_size >= (QSPI_NOR_BLK64_SIZE << hflash->dualFlash))
        {
            //level = rt_hw_interrupt_disable();
            nor_lock(hflash->base);
            HAL_QSPIEX_BLK64_ERASE(hflash, al_addr);
            //rt_hw_interrupt_enable(level);
            nor_unlock(hflash->base);
            al_size -= QSPI_NOR_BLK64_SIZE << hflash->dualFlash;
            al_addr += QSPI_NOR_BLK64_SIZE << hflash->dualFlash;
        }
        //LOG_D("Block64 erase to 0x%x\n", al_addr);
    }
    // sector aligned
    if ((al_size >= (QSPI_NOR_SECT_SIZE << hflash->dualFlash)) && IS_ALIGNED((QSPI_NOR_SECT_SIZE << hflash->dualFlash), al_addr))
    {
        while (al_size >= (QSPI_NOR_SECT_SIZE << hflash->dualFlash))
        {
            nor_lock(hflash->base);
            HAL_QSPIEX_SECT_ERASE(hflash, al_addr);
            nor_unlock(hflash->base);
            al_size -= QSPI_NOR_SECT_SIZE << hflash->dualFlash;
            al_addr += QSPI_NOR_SECT_SIZE << hflash->dualFlash;
            if (IS_ALIGNED((QSPI_NOR_BLK64_SIZE << hflash->dualFlash), al_addr) && (al_size >= (QSPI_NOR_BLK64_SIZE << hflash->dualFlash)))
                goto alig64k;
        }
        //LOG_D("sector erase to 0x%x\n", al_addr);
    }

    if (al_size > 0)    // something wrong
    {
        HAL_ASSERT(0);
        ret = -1;
        goto _exit;
    }

_exit:

    return ret;
}

int BSP_Nor_write(void *handle, uint32_t addr, const uint8_t *buf, uint32_t size)
{
    int i, cnt, taddr, tsize, aligned_size, start;
    uint8_t *tbuf;
    FLASH_HandleTypeDef *hflash = (FLASH_HandleTypeDef *)handle;

    if (hflash == NULL  || size == 0)
        return 0;

    cnt = 0;
    tsize = size;
    tbuf = (uint8_t *)buf;
    // address to offset if needed
    if (addr >= hflash->base)
        taddr = addr - hflash->base;
    else
        taddr = addr;

    if (hflash->dualFlash) // need lenght and address 2 aligned
    {
        if (taddr & 1) // dst odd, make 2 bytes write
        {
            nor_lock(hflash->base);
            HAL_QSPIEX_FILL_EVEN(hflash, taddr, tbuf, 1);
            nor_unlock(hflash->base);
            //rt_kprintf("Fill prev byte at pos %d\n", taddr);
            // update buffer and address
            taddr++;
            tbuf++;
            tsize--;
            cnt++;
        }
    }

    if (tsize <= 0)
        goto exit;

    // check address page align
    aligned_size = QSPI_NOR_PAGE_SIZE << hflash->dualFlash;
    //cnt = taddr - (taddr & (~(aligned_size - 1)));
    start = taddr & (aligned_size - 1);
    if (start > 0)    // start address not page aligned
    {
        start = aligned_size - start;   // start to remain size in one page
        if (start > tsize)    // not over one page
        {
            start = tsize;
        }

        if (hflash->dualFlash && (start & 1))   // for this case, it should be the lastest write
        {
            nor_lock(hflash->base);
            i = HAL_QSPIEX_WRITE_PAGE(hflash, taddr, tbuf, start & (~1));

            taddr += i;
            tbuf += i;
            //tsize -= i;
            HAL_QSPIEX_FILL_EVEN(hflash, taddr, tbuf, 0);
            nor_unlock(hflash->base);
            cnt += start;

            goto exit;
        }
        else
        {
            nor_lock(hflash->base);
            i = HAL_QSPIEX_WRITE_PAGE(hflash, taddr, tbuf, start);
            nor_unlock(hflash->base);
            //rt_kprintf("Not page aligned write 0x%x, size %d\n", taddr, start);
            if (i != start)
            {
                //return 0;
                cnt = 0;
                goto exit;
            }
        }
        taddr += start;
        tbuf += start;
        tsize -= start;
        cnt += start;
    }
    // process page aligned data
    while (tsize >= aligned_size)
    {
        nor_lock(hflash->base);
        i = HAL_QSPIEX_WRITE_PAGE(hflash, taddr, tbuf, aligned_size);
        nor_unlock(hflash->base);
        cnt += aligned_size;
        taddr += aligned_size;
        tbuf += aligned_size;
        tsize -= aligned_size;
        //LOG_D("write:  %d\n", cnt);
    }
    // remain size
    if (tsize > 0)
    {
        if (hflash->dualFlash && (tsize & 1))
        {
            nor_lock(hflash->base);
            i = HAL_QSPIEX_WRITE_PAGE(hflash, taddr, tbuf, tsize & (~1));
            nor_unlock(hflash->base);

            if (tsize & 1)  // remain 1 byte
            {
                //cnt += i;
                taddr += i;
                tbuf += i;
                //tsize -= i;
                nor_lock(hflash->base);
                HAL_QSPIEX_FILL_EVEN(hflash, taddr, tbuf, 0);
                nor_unlock(hflash->base);
                //taddr++;
                //tbuf++;
                //tsize--;
                //cnt++;
            }
            cnt += tsize;
        }
        else
        {
            nor_lock(hflash->base);
            i = HAL_QSPIEX_WRITE_PAGE(hflash, taddr, tbuf, tsize);
            nor_unlock(hflash->base);
            if (i != tsize)
            {
                //return 0;
                cnt = 0;
                goto exit;
            }
            cnt += tsize;
        }
    }

exit:

    return cnt;
}


uint32_t flash_get_freq(int clk_module, uint16_t clk_div, uint8_t hcpu)
{
    int src;
    uint32_t freq;

    if (clk_div == 0)
        return 0;

    if (hcpu == 0)
    {
        freq = HAL_RCC_GetSysCLKFreq(CORE_ID_LCPU);
        return freq / clk_div;
    }

    src = HAL_RCC_HCPU_GetClockSrc(clk_module);
    if (RCC_CLK_FLASH_DLL2 == src)
    {
        freq = HAL_RCC_HCPU_GetDLL2Freq();
    }
    else if (RCC_CLK_FLASH_DLL3 == src)
    {
        freq = HAL_RCC_HCPU_GetDLL3Freq();
    }
    else
    {
        freq = HAL_RCC_GetSysCLKFreq(CORE_ID_HCPU);
    }

    return freq / clk_div;;
}

__HAL_ROM_USED int BSP_Flash_Init_WithID(uint8_t fid, qspi_configure_t *pflash_cfg, struct dma_config *pdma_cfg, uint8_t dtr)
{
    HAL_StatusTypeDef res = HAL_ERROR;
    uint16_t div;

    /* avoid using memset locating in nor flash */
    //flash_memset(&spi_flash_handle[fid], 0, sizeof(QSPI_FLASH_CTX_T));

    // check flash size, nor flash max support 32MB
    if (pflash_cfg->msize > 32)
    {
        return 0;
    }

    switch (fid)
    {
    case 0:
        div = BSP_GetFlash1DIV();
        spi_flash_handle[fid].handle.freq = flash_get_freq(RCC_CLK_MOD_FLASH1, div, 1);
        break;
    case 1:
        div = BSP_GetFlash2DIV();
        spi_flash_handle[fid].handle.freq = flash_get_freq(RCC_CLK_MOD_FLASH2, div, 1);
        break;
#ifdef QSPI3_MEM_BASE
    case 2:
        div = BSP_GetFlash3DIV();
        spi_flash_handle[fid].handle.freq = flash_get_freq(RCC_CLK_MOD_FLASH3, div, 1);
        break;
#endif
#ifdef QSPI4_MEM_BASE
    case 3:
        div = BSP_GetFlash4DIV();
#ifdef SOC_SF32LB55X
        spi_flash_handle[fid].handle.freq = flash_get_freq(0, div, 0);
#else
        spi_flash_handle[fid].handle.freq = flash_get_freq(RCC_CLK_MOD_FLASH4, div, 1);
#endif
        break;
#endif
#ifdef QSPI5_MEM_BASE
    case 4:
        div = BSP_GetFlash5DIV();
        spi_flash_handle[fid].handle.freq = flash_get_freq(0, div, 0);
        break;
#endif
    }

#ifdef CFG_BOOTLOADER
#ifndef USE_ATE_MODE
    pflash_cfg->line = 0;    // for bootloader, force to 1 line process
#endif
#endif
    spi_flash_handle[fid].handle.buf_mode = dtr;
    if (dtr == 1)
    {
//#ifdef SOC_SF32LB58X
        // todo, adjust with different frequency and version
        spi_flash_handle[fid].handle.ecc_en = (1 << 7) | (0xf); // high 1 bits for rx clock inv, low 7 bits for rx clock delay
//#else
        // todo, adjust with different frequency and version
//        spi_flash_handle[fid].handle.ecc_en = (1 << 7) | (0x15); // high 1 bits for rx clock inv, low 7 bits for rx clock delay
//#endif
    }
    // init hardware, set ctx, dma, clock
    res = HAL_FLASH_Init(&(spi_flash_handle[fid]), pflash_cfg, &spi_flash_dma_handle[fid], pdma_cfg, div);
    if (res == HAL_OK)
    {
#if defined(SOC_SF32LB56X) || defined(SOC_SF32LB52X)
        {
            uint16_t timeout = 0xffff;
            HAL_FLASH_SET_WDT(&(spi_flash_handle[fid].handle), timeout);
        }
#elif defined(EN_FLASH_WDT)
        {
            uint16_t timeout = 0x3ff;
            HAL_FLASH_SET_WDT(&(spi_flash_handle[fid].handle), timeout);
        }
#endif

        return 1;
    }
    //else
    //    rt_kprintf("Flash %d initial fail\n", fid);

    return 0;
}

__HAL_ROM_USED int BSP_Flash_hw1_init()
{
#if defined (BSP_ENABLE_QSPI1) && (BSP_QSPI1_MODE == SPI_MODE_NOR)
    /* avoid using memset locating in nor flash */
    flash_memset(&spi_flash_handle[0], 0, sizeof(QSPI_FLASH_CTX_T));

    qspi_configure_t flash_cfg = FLASH1_CONFIG;
    struct dma_config flash_dma = FLASH1_DMA_CONFIG;
    uint8_t dtr;
#ifdef BSP_MPI1_EN_DTR
    // check if use dtr mode, only for nor, for nand ,it should not be defined
    dtr = 1;
#else
    dtr = 0;
#endif //BSP_MPI1_EN_DTR

    return BSP_Flash_Init_WithID(0, &flash_cfg, &flash_dma, dtr);

#endif  // BSP_ENABLE_FLASH1 && (BSP_QSPI1_MODE == 0)

    return 0;
}

__HAL_ROM_USED int BSP_Flash_hw2_init()
{
#if defined(BSP_ENABLE_QSPI2)  && (BSP_QSPI2_MODE == SPI_MODE_NOR)
    /* avoid using memset locating in nor flash */
    flash_memset(&spi_flash_handle[1], 0, sizeof(QSPI_FLASH_CTX_T));

    qspi_configure_t flash_cfg2 = FLASH2_CONFIG;
    struct dma_config flash_dma2 = FLASH2_DMA_CONFIG;
    uint8_t dtr;

#ifdef BSP_MPI2_EN_DTR
    // check if use dtr mode, only for nor, for nand ,it should not be defined
    dtr = 1;
#else
    dtr = 0;
#endif //BSP_MPI2_EN_DTR

    return BSP_Flash_Init_WithID(1, &flash_cfg2, &flash_dma2, dtr);

#endif  // BSP_ENABLE_QSPI2 && (BSP_QSPI2_MODE == 0)

    return 0;
}

__HAL_ROM_USED int BSP_Flash_hw3_init()
{
#if defined(BSP_ENABLE_QSPI3)  && (BSP_QSPI3_MODE == SPI_MODE_NOR)
    //Initial flash3 context
    /* avoid using memset locating in nor flash */
    flash_memset(&spi_flash_handle[2], 0, sizeof(QSPI_FLASH_CTX_T));

    qspi_configure_t flash_cfg3 = FLASH3_CONFIG;
    struct dma_config flash_dma3 = FLASH3_DMA_CONFIG;
    uint8_t dtr;

#ifdef BSP_MPI3_EN_DTR
    // check if use dtr mode, only for nor, for nand ,it should not be defined
    dtr = 1;
#else
    dtr = 0;
#endif //BSP_MPI3_EN_DTR

    return BSP_Flash_Init_WithID(2, &flash_cfg3, &flash_dma3, dtr);
#endif  // BSP_ENABLE_QSPI3 && (BSP_QSPI3_MODE == 0)

    return 0;
}

__HAL_ROM_USED int BSP_Flash_hw4_init()
{
#if defined BSP_ENABLE_QSPI4 && (BSP_QSPI4_MODE == SPI_MODE_NOR)
    // Init flash4 context
    //memset(&spi_flash_handle[3], 0, sizeof(QSPI_FLASH_CTX_T));

    qspi_configure_t flash_cfg4 = FLASH4_CONFIG;
    struct dma_config flash_dma4 = FLASH4_DMA_CONFIG;
    uint8_t dtr;

#ifdef BSP_MPI4_EN_DTR
    // check if use dtr mode, only for nor, for nand ,it should not be defined
    dtr = 1;
#else
    dtr = 0;
#endif //BSP_MPI4_EN_DTR

    return BSP_Flash_Init_WithID(3, &flash_cfg4, &flash_dma4, dtr);

#endif  // BSP_ENABLE_QSPI4 && (BSP_QSPI4_MODE == 0)

    return 0;
}


__HAL_ROM_USED int BSP_Flash_hw5_init()
{
#ifdef QSPI5_MEM_BASE

    // Init flash4 context
    //memset(&spi_flash_handle[4], 0, sizeof(QSPI_FLASH_CTX_T));
#ifdef BSP_ENABLE_QSPI5
    qspi_configure_t flash_cfg5 = FLASH5_CONFIG;
    struct dma_config flash_dma5 = FLASH5_DMA_CONFIG;

#else
    qspi_configure_t flash_cfg5;
    struct dma_config flash_dma5;

    flash_cfg5.base = FLASH5_BASE_ADDR;
    flash_cfg5.Instance = FLASH5;
    flash_cfg5.line = 2;
    flash_cfg5.msize = 1;
    flash_cfg5.SpiMode = 0;

    flash_dma5.dma_irq = FLASH5_DMA_IRQ;
    flash_dma5.dma_rcc = FLASH5_DMA_RCC;
    flash_dma5.Instance = FLASH5_DMA_INSTANCE;
    flash_dma5.request = FLASH5_DMA_REQUEST;
#endif
    uint8_t dtr;

#ifdef BSP_MPI5_EN_DTR
    // check if use dtr mode, only for nor, for nand ,it should not be defined
    dtr = 1;
#else
    dtr = 0;
#endif //BSP_MPI5_EN_DTR

    return BSP_Flash_Init_WithID(4, &flash_cfg5, &flash_dma5, dtr);
#endif
    return 0;
}


/**
* @brief  Flash controller hardware initial.
* @retval each bit for a controller enabled.
*/
__HAL_ROM_USED int BSP_Flash_Init(void)
{
    int fen = 0;

    //memset(spi_flash_handle, 0, sizeof(QSPI_FLASH_CTX_T)*FLASH_MAX_INSTANCE);

    if (BSP_Flash_hw1_init())
        fen |= (1 << 0);

    if (BSP_Flash_hw2_init())
        fen |= (1 << 1);

    if (BSP_Flash_hw3_init())
        fen |= (1 << 2);

    if (BSP_Flash_hw4_init())
        fen |= (1 << 3);

#ifdef BSP_ENABLE_MPI5
    if (BSP_Flash_hw5_init())
        fen |= (1 << 4);
#endif

    return fen;
}



#endif

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
