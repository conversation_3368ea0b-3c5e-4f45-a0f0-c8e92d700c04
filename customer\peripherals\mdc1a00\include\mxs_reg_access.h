/* 
 * Copyright (c) 2022 MixoSense Technology Ltd <<EMAIL>>.
 * 
 * All rights are reserved.
 * Proprietary and confidential.
 * Unauthorized copying of this file, via any medium is strictly prohibited.
 * Any use is subject to an appropriate license granted by MixoSense Technology Ltd.
 * 
 */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

/*-----------------------------------------------------------------------------
 * HEADER FILES
 *---------------------------------------------------------------------------*/
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>

/*-----------------------------------------------------------------------------
 * MACRO DEFINITION
 *---------------------------------------------------------------------------*/
#ifdef _WIN32
#ifdef MXS_DLL_EXPORTS
#define MXS_API __declspec(dllexport)
#else
#define MXS_API
#endif
#else
#define MXS_API
#endif

#define CONFIG_MXS_USE_SPI

/*-----------------------------------------------------------------------------
 * FUNCTIONS DEFINITION
 *---------------------------------------------------------------------------*/
/**
 * @brief reg实例创建
 * 
 * @return void
 */
void mxs_reg_init(void);

/**
 * @brief reg实例销毁
 * 
 * @return void
 */
void mxs_reg_deinit(void);

/**
 * @brief mxs寄存器写入接口
 * 
 * @param {uint8_t} regAddr
 * @param {uint8_t} regData
 * 
 * @return {*}
 */
int mxs_reg_write(uint8_t regAddr, uint8_t regData);

/**
 * @brief mxs寄存器读取接口
 * 
 * @param {uint8_t} regAddr
 * @param {uint8_t} *regData
 * 
 * @return {*}
 */
int mxs_reg_read(uint8_t regAddr, uint8_t *regData);

#ifdef __cplusplus
}
#endif
