/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HDL_DEBUG_H__
#define __HDL_DEBUG_H__

#include "hdl_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ToDo Porting: Please use your platform API to implement it.
// Platform Log API
// #define HDL_DEBUG 
#ifdef HDL_DEBUG
#define HDL_LOGI    rt_kprintf
#define HDL_LOGW    rt_kprintf
#define HDL_LOGE    rt_kprintf
#else
#define HDL_LOGI(fmt,arg...)
#define HDL_LOGW(fmt,arg...)
#define HDL_LOGE(fmt,arg...)
#endif

#define HDL_SUCCESS_LOG(X)     HDL_LOGI("%s %s\n", X, ((success) ? "success" : "fail"))

/*
#ifndef HDL_Assert
#define HDL_Assert(X)                                           \
    do                                                          \
    {                                                           \
        if(!(X))                                                \
        {                                                       \
            HDL_LOGE("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");       \
            HDL_LOGE("HDL_Assert Fail: %s", #X);                \
            HDL_LOGE("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");       \
            vTaskDelay(1000);                                   \
            while(1) {}                                         \
        }                                                       \
    } while(0)
#endif
*/

#ifndef HDL_Require_Noerr_Action
#define HDL_Require_Noerr_Action(X, GOTO_LABEL, Fun)            \
    do {                                                        \
        if(!(X)) {                                              \
            { HDL_LOGI("%s fail\n", Fun); }                       \
            goto GOTO_LABEL;                                    \
        } else {                                                \
            { HDL_LOGI("%s success\n", Fun); }                    \
        }                                                       \
    } while(0)
#endif

#ifndef HDL_Require_Noerr
#define HDL_Require_Noerr(X, GOTO_LABEL)                        \
    do {                                                        \
        if(!(X)) {                                              \
            goto GOTO_LABEL;                                    \
        }                                                       \
    } while(0)
#endif


#ifndef HDL_MAIN_LOG
#define HDL_MAIN_LOG(fmt,arg...)                                \
    do                                                          \
    {                                                           \
        HDL_LOGI("##################################################\n"); \
        HDL_LOGI(fmt, ##arg);                                   \
        HDL_LOGI("##################################################\n"); \
    } while(0)
#endif

#ifdef __cplusplus
}
#endif

#endif
