/**
  ******************************************************************************
  * @file   st7789_gmt024_08_spi8p.c
  * <AUTHOR> software development team
  * @brief   This file includes the LCD driver for ST7789V_GMT024 LCD.
  * @attention
  ******************************************************************************
*/
/**
 * @attention
 * Copyright (c) 2019 - 2022,  Sifli Technology
 *
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification,
 * are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form, except as embedded into a Sifli integrated circuit
 *    in a product or a software update for such product, must reproduce the above
 *    copyright notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of Sifli nor the names of its contributors may be used to endorse
 *    or promote products derived from this software without specific prior written permission.
 *
 * 4. This software, with or without modification, must only be used with a
 *    Sifli integrated circuit.
 *
 * 5. Any software provided in binary form under this license must not be reverse
 *    engineered, decompiled, modified and/or disassembled.
 *
 * THIS SOFTWARE IS PROVIDED BY SIFLI TECHNOLOGY "AS IS" AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL SIFLI TECHNOLOGY OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <rtthread.h>
#include "string.h"
#include "board.h"
#include "drv_io.h"
#include "drv_lcd.h"
#include "st7789_gmt024_08_spi8p.h"
#include "log.h"

/** @addtogroup BSP
  * @{
  */

/** @addtogroup Components
  * @{
  */

/** @addtogroup ST7789V_GMT024
  * @brief This file provides a set of functions needed to drive the
  *        ST7789V_GMT024 LCD.
  * @{
  */

/** @defgroup ST7789V_GMT024_Private_TypesDefinitions
  * @{
  */





/**
  * @}
  */

/** @defgroup ST7789V_GMT024_Private_Defines
  * @{
  */
/**
  * @}
  */

/** @defgroup ST7789V_GMT024_Private_Macros
  * @{
  */


//#define DEBUG

#ifdef DEBUG
    #define DEBUG_PRINTF(...)   LOG_I(__VA_ARGS__)
#else
    #define DEBUG_PRINTF(...)
#endif

void ST7789V_GMT024_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters);
uint32_t ST7789V_GMT024_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize);


/**
  * @}
  */

/** @defgroup ST7789V_GMT024_Private_Variables
  * @{
  */

static const LCD_DrvOpsDef ST7789V_GMT024_drv =
{
    ST7789V_GMT024_Init,
    ST7789V_GMT024_ReadID,
    ST7789V_GMT024_DisplayOn,
    ST7789V_GMT024_DisplayOff,

    ST7789V_GMT024_SetRegion,
    ST7789V_GMT024_WritePixel,
    ST7789V_GMT024_WriteMultiplePixels,

    ST7789V_GMT024_ReadPixel,

    ST7789V_GMT024_SetColorMode,
    ST7789V_GMT024_SetBrightness,
    NULL,
    NULL

};


static LCDC_InitTypeDef lcdc_int_cfg =
{
    .lcd_itf = LCDC_INTF_SPI_DCX_1DATA,
    .freq = 48000000,
    .color_mode = LCDC_PIXEL_FORMAT_RGB565,

    .cfg = {
        .spi = {
            .dummy_clock = 0,
            .syn_mode = HAL_LCDC_SYNC_DISABLE,
            .vsyn_polarity = 0,
            //default_vbp=2, frame rate=82, delay=115us,
            //TODO: use us to define delay instead of cycle, delay_cycle=115*48
            .vsyn_delay_us = 1000,
            .hsyn_num = 0,
        },
    },

};

LCD_DRIVER_EXPORT(ST7789V_GMT024, ST7789V_GMT024_ID, &lcdc_int_cfg,
                  &ST7789V_GMT024_drv,
                  ST7789V_GMT024_LCD_PIXEL_WIDTH,
                  ST7789V_GMT024_LCD_PIXEL_HEIGHT,
                  1);
/**
  * @}
  */

/** @defgroup ST7789V_GMT024_Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup ST7789V_GMT024_Private_Functions
  * @{
  */

/**
  * @brief  spi read/write mode
  * @param  enable: false - write spi mode |  true - read spi mode
  * @retval None
  */
void ST7789V_GMT024_ReadMode(LCDC_HandleTypeDef *hlcdc, bool enable)
{
    if (HAL_LCDC_IS_SPI_IF(lcdc_int_cfg.lcd_itf))
    {
        if (enable)
        {
            HAL_LCDC_SetFreq(hlcdc, 4000000); //read mode min cycle 300ns
        }
        else
        {
            HAL_LCDC_SetFreq(hlcdc, lcdc_int_cfg.freq); //Restore normal frequency
        }

    }
}

#define MAX_CMD_LEN 16
static const uint8_t lcd_init_cmds[][MAX_CMD_LEN] =
{
    {0x3A, 1, 0x05},       //65k mode
    {0xC5, 1, 0x1A},         //VCOM
    {0x36, 1, 0x00},                // 屏幕显示方向设置
    //-------------ST7789V Frame rate setting-----------//
    {0xb2, 5, 0x05, 0x05, 0x00, 0x33, 0x33},                  //Porch Setting
    {0xb7, 1, 0x05},          //Gate Control //12.2v   -10.43v
    //--------------ST7789V Power setting---------------//
    {0xBB, 1, 0x3F},     //VCOM
    {0xC0, 1, 0x2c}, //Power control
    {0xC2, 1, 0x01},      //VDV and VRH Command Enable
    {0xC3, 1, 0x0F},          //VRH Set //4.3+( vcom+vcom offset+vdv)
    {0xC4, 1, 0x20},          //VDV Set//0v
    {0xC6, 1, 0X01},              //Frame Rate Control in Normal Mode //111Hz
    {0xd0, 2, 0xa4, 0xa1},                 //Power Control 1
    {0xE8, 1, 0x03},              //Power Control 1
    {0xE9, 3, 0x09, 0x09, 0x08},                    //Equalize time control
    //---------------ST7789V gamma setting-------------//
    {0xE0, 14, 0xD0, 0x05, 0x09, 0x09, 0x08, 0x14, 0x28, 0x33, 0x3F, 0x07, 0x13, 0x14, 0x28, 0x30},                                       //Set Gamma
    {0XE1, 14, 0xD0, 0x05, 0x09, 0x09, 0x08, 0x03, 0x24, 0x32, 0x32, 0x3B, 0x14, 0x13, 0x28, 0x2F},                                       //Set Gamma
    {0x21, 0 },        //反显
};

/**
  * @brief  Power on the LCD.
  * @param  None
  * @retval None
  */
void ST7789V_GMT024_Init(LCDC_HandleTypeDef *hlcdc)
{
    /* Initialize ST7789V_GMT024 low level bus layer ----------------------------------*/
    memcpy(&hlcdc->Init, &lcdc_int_cfg, sizeof(LCDC_InitTypeDef));
    HAL_LCDC_Init(hlcdc);

    BSP_LCD_Reset(0);//Reset LCD
    LCD_DRIVER_DELAY_MS(20);
    BSP_LCD_Reset(1);

    /* Wait for 120ms */
    LCD_DRIVER_DELAY_MS(120);


    ST7789V_GMT024_WriteReg(hlcdc, 0X01, NULL, 0);
    LCD_DRIVER_DELAY_MS(120);

    ST7789V_GMT024_WriteReg(hlcdc, 0X11, NULL, 0);
    LCD_DRIVER_DELAY_MS(120);

    for (uint32_t i = 0; i < sizeof(lcd_init_cmds) / MAX_CMD_LEN; i++)
    {
        //rt_kprintf("write %d,cmd=0x%x,len=%d\n",i,(int)lcd_init_cmds[i][0], (int)lcd_init_cmds[i][1]);
        //HAL_DBG_print_data((char*)&(lcd_init_cmds[i][2]),0,(int)lcd_init_cmds[i][1]);
        ST7789V_GMT024_WriteReg(hlcdc, lcd_init_cmds[i][0], (uint8_t *)&lcd_init_cmds[i][2], lcd_init_cmds[i][1]);

        //__asm("B .");
    }
    LCD_DRIVER_DELAY_MS(120);


    ST7789V_GMT024_WriteReg(hlcdc, 0X29, NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval LCD Register Value.
  */
uint32_t ST7789V_GMT024_ReadID(LCDC_HandleTypeDef *hlcdc)
{
    uint32_t data;

    data = ST7789V_GMT024_ReadData(hlcdc, ST7789V_GMT024_LCD_ID, 4);
    rt_kprintf("\n ST7789V_GMT024_ReadID 0x%x \n", data);
    data = ((data << 1) >> 8) & 0xFFFFFF;


    return ST7789V_GMT024_ID;
}

/**
  * @brief  Enables the Display.
  * @param  None
  * @retval None
  */
void ST7789V_GMT024_DisplayOn(LCDC_HandleTypeDef *hlcdc)
{
    /* Display On */
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_DISPLAY_ON, (uint8_t *)NULL, 0);
}

/**
  * @brief  Disables the Display.
  * @param  None
  * @retval None
  */
void ST7789V_GMT024_DisplayOff(LCDC_HandleTypeDef *hlcdc)
{
    /* Display Off */
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_DISPLAY_OFF, (uint8_t *)NULL, 0);
}

void ST7789V_GMT024_SetRegion(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint8_t   parameter[4];

    //Cut to 240x320
    if (Xpos0 > 239) return;
    if (Ypos0 > 319) return;
    if (Xpos1 > 239) Xpos1 = 239;
    if (Ypos1 > 319) Ypos1 = 319;


    HAL_LCDC_SetROIArea(hlcdc, Xpos0, Ypos0, Xpos1, Ypos1);

    Ypos0 += 0;
    Ypos1 += 0;

    parameter[0] = (Xpos0) >> 8;
    parameter[1] = (Xpos0) & 0xFF;
    parameter[2] = (Xpos1) >> 8;
    parameter[3] = (Xpos1) & 0xFF;
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_CASET, parameter, 4);

    parameter[0] = (Ypos0) >> 8;
    parameter[1] = (Ypos0) & 0xFF;
    parameter[2] = (Ypos1) >> 8;
    parameter[3] = (Ypos1) & 0xFF;
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_RASET, parameter, 4);
}

/**
  * @brief  Writes pixel.
  * @param  Xpos: specifies the X position.
  * @param  Ypos: specifies the Y position.
  * @param  RGBCode: the RGB pixel color
  * @retval None
  */
void ST7789V_GMT024_WritePixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos, const uint8_t *RGBCode)
{
    uint8_t data = 0;

    /* Set Cursor */
    ST7789V_GMT024_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_WRITE_RAM, (uint8_t *)RGBCode, 2);
}

void ST7789V_GMT024_WriteMultiplePixels(LCDC_HandleTypeDef *hlcdc, const uint8_t *RGBCode, uint16_t Xpos0, uint16_t Ypos0, uint16_t Xpos1, uint16_t Ypos1)
{
    uint32_t size;

    HAL_LCDC_LayerSetData(hlcdc, HAL_LCDC_LAYER_DEFAULT, (uint8_t *)RGBCode, Xpos0, Ypos0, Xpos1, Ypos1);
    HAL_LCDC_SendLayerData2Reg_IT(hlcdc, ST7789V_GMT024_WRITE_RAM, 1);
}



/**
  * @brief  Writes  to the selected LCD register.
  * @param  LCD_Reg: address of the selected register.
  * @retval None
  */
void ST7789V_GMT024_WriteReg(LCDC_HandleTypeDef *hlcdc, uint16_t LCD_Reg, uint8_t *Parameters, uint32_t NbParameters)
{
    HAL_LCDC_WriteU8Reg(hlcdc, LCD_Reg, Parameters, NbParameters);
}


/**
  * @brief  Reads the selected LCD Register.
  * @param  RegValue: Address of the register to read
  * @param  ReadSize: Number of bytes to read
  * @retval LCD Register Value.
  */
uint32_t ST7789V_GMT024_ReadData(LCDC_HandleTypeDef *hlcdc, uint16_t RegValue, uint8_t ReadSize)
{
    uint32_t rd_data = 0;

    ST7789V_GMT024_ReadMode(hlcdc, true);

    HAL_LCDC_ReadU8Reg(hlcdc, RegValue, (uint8_t *)&rd_data, ReadSize);

    ST7789V_GMT024_ReadMode(hlcdc, false);

    return rd_data;
}



uint32_t ST7789V_GMT024_ReadPixel(LCDC_HandleTypeDef *hlcdc, uint16_t Xpos, uint16_t Ypos)
{
    uint8_t   parameter[2];
    uint32_t c;
    uint32_t ret_v;

    parameter[0] = 0x66;
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_COLOR_MODE, parameter, 1);

    ST7789V_GMT024_SetRegion(hlcdc, Xpos, Ypos, Xpos, Ypos);

    /*
        read ram need 7 dummy cycle, and it's result is 24bit color which format is:

        6bit red + 2bit dummy + 6bit green + 2bit dummy + 6bit blue + 2bit dummy

    */
    c =  ST7789V_GMT024_ReadData(hlcdc, ST7789V_GMT024_READ_RAM, 4);
    c <<= 1;
    //c >>= lcdc_int_cfg.dummy_clock; //revert fixed dummy cycle

    switch (lcdc_int_cfg.color_mode)
    {
    case LCDC_PIXEL_FORMAT_RGB565:
        parameter[0] = 0x55;
        ret_v = (uint32_t)(((c >> 8) & 0xF800) | ((c >> 5) & 0x7E0) | ((c >> 3) & 0X1F));
        break;

    case LCDC_PIXEL_FORMAT_RGB666:
        parameter[0] = 0x66;
        ret_v = (uint32_t)(((c >> 6) & 0x3F000) | ((c >> 4) & 0xFC0) | ((c >> 2) & 0X3F));
        break;

    case LCDC_PIXEL_FORMAT_RGB888:
        /*
           pretend that ST7789V_GMT024 can support RGB888,

           treated as RGB666 actually(6bit R + 2bit dummy + 6bit G + 2bit dummy + 6bit B + 2bit dummy )

           lcdc NOT support RGB666
        */
        /*
            st7789 NOT support RGB888：

            fill 2bit dummy bit with 2bit MSB of color
        */
        parameter[0] = 0x66;
        ret_v = (uint32_t)((c & 0xFCFCFC) | ((c >> 6) & 0x030303));
        break;

    default:
        RT_ASSERT(0);
        break;
    }

    //rt_kprintf("ST7789V_GMT024_ReadPixel %x -> %x\n",c, ret_v);


    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_COLOR_MODE, parameter, 1);

    return ret_v;
}


void ST7789V_GMT024_SetColorMode(LCDC_HandleTypeDef *hlcdc, uint16_t color_mode)
{
    uint8_t   parameter[2];

    /*

    Control interface color format
    ‘011’ = 12bit/pixel ‘101’ = 16bit/pixel ‘110’ = 18bit/pixel ‘111’ = 16M truncated

    */
    switch (color_mode)
    {
    case RTGRAPHIC_PIXEL_FORMAT_RGB565:
        /* Color mode 16bits/pixel */
        parameter[0] = 0x55;
        lcdc_int_cfg.color_mode = LCDC_PIXEL_FORMAT_RGB565;
        break;


    default:
        RT_ASSERT(0);
        return; //unsupport
        break;

    }

    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_COLOR_MODE, parameter, 1);
    HAL_LCDC_SetOutFormat(hlcdc, lcdc_int_cfg.color_mode);
}

void     ST7789V_GMT024_SetBrightness(LCDC_HandleTypeDef *hlcdc, uint8_t br)
{
    uint8_t bright = (uint8_t)((uint16_t)UINT8_MAX * br / 100);
    ST7789V_GMT024_WriteReg(hlcdc, ST7789V_GMT024_WBRIGHT, &br, 1);
}

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT Sifli Technology *******END OF FILE****/
