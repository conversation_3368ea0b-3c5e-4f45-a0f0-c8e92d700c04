/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : hdl_image_download.h
*<AUTHOR>
*@Date : 2024/12/29
*@Description : GNSS固件升级相关接口定义
************************************************************************
*/

#ifndef HDL_IMAGE_DOWNLOAD_H_
#define HDL_IMAGE_DOWNLOAD_H_

#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @brief GNSS固件升级状态枚举
 */
typedef enum
{
    GNSS_UPDATE_IDLE = 0x01,     /**< 空闲状态 */
    GNSS_UPDATE_ING,             /**< 升级中状态 */
    GNSS_UPDATE_SUCCESS,         /**< 升级成功状态 */
    GNSS_UPDATE_FAIL,           /**< 升级失败状态 */
}GNSS_UPDATED_STATUS;

/**
 * @brief 等待GNSS固件升级完成
 * @details 阻塞等待固件升级完成
 */
void wait_gps_fw_updated(void);

/**
 * @brief 检查是否需要升级GNSS固件
 * @param gnss_folder_path GNSS固件文件夹路径
 */
void gnss_fw_updated(const char *gnss_folder_path);

/**
 * @brief 获取GNSS固件升级状态
 * @return GNSS_UPDATED_STATUS 当前升级状态
 */
GNSS_UPDATED_STATUS gnss_fw_get_update_status(void);

/**
 * @brief 获取GNSS固件升级进度
 * @return int 升级进度百分比(0-100)
 */
int gnss_fw_updated_pct_get(void);

#ifdef __cplusplus
}
#endif

#endif
