﻿/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   airoha_ag333x.c
@Time    :   2025/01/17 13:35:11
*
**************************************************************************/

#include <rtthread.h>
#include "board.h"
#include "ag333x_cmd.h"
#include "airoha_ag333x.h"
#include "gps_api.h"
#include "qw_fs.h"
#include "rtconfig.h"
#include "power_ctl.h"
#include "wr02_board_config.h"
#include "bsp_pinmux.h"
#include "gps_dev.h"
#include "qw_user_debug.h"
#include "qw_time_service.h"
#include "drv_usart.h"
#if GPS_USING_AG333X

#ifndef SIMULATOR
#include "drv_io.h"
#include "ulog.h"

#define GPS_IO_IN_HCPU  0

#define GPS_TX_PIN PAD_PB06
#define GPS_RX_PIN PAD_PB11
#define GPS_CHIP_EN (GPIO1_PIN_NUM + 37)        // PB37
#define GPS_WAKEUP_MCU_PIN (GPIO1_PIN_NUM + 48) // PB48
#define MCU_WAKEUP_GPS_PIN (GPIO1_PIN_NUM + 41) // PB41

static bool s_ag333x_init = false;
static bool s_ag333x_poweron = false;

static rt_device_t ag333x_serial = NULL;


static rt_err_t ag333x_send_data(const uint8_t* buf, const uint32_t len)
{
    rt_err_t ret = RT_ERROR;

#ifdef RT_USING_PM
    rt_pm_request(PM_SLEEP_MODE_IDLE);
#endif

    if (ag333x_serial && s_ag333x_init) {
        if(rt_device_write(ag333x_serial, 0, buf, len) > 0){
            ret = RT_EOK;
        }
    }

#ifdef RT_USING_PM
    rt_pm_release(PM_SLEEP_MODE_IDLE);
#endif

    return ret;
}

#define MAX_FRAMES 15 // 定义最大帧数
#define GPS_LOG_BUF_SIZE 1024 // 定义每帧的大小
static uint8_t recv_buf[MAX_FRAMES][GPS_LOG_BUF_SIZE]; // 多帧接收缓冲区
static uint32_t recv_buf_len[MAX_FRAMES]; // 每帧的长度
static uint32_t current_frame_save = 0; // 保存帧索引
static uint32_t current_frame_write = 0; // 写文件帧索引
static uint32_t count_write = 0; // 写文件计数
static bool gps_debug_log_enable = false;
static QW_FIL* gps_log_fp = RT_NULL;
static rt_sem_t log_sem = RT_NULL; // 信号量用于线程同步
static uint16_t gps_data_loss_count = 0; // GPS数据丢失计数

static void gps_log_thread(void *parameter) {
    UINT byte = 0;
    while (1) {
        rt_sem_take(log_sem, RT_WAITING_FOREVER); // 等待信号量
        if (gps_log_fp) {
            // 写入当前帧数据
            qw_f_write(gps_log_fp, recv_buf[current_frame_write], recv_buf_len[current_frame_write], &byte);
            // 更新当前帧索引，循环使用缓冲区
            current_frame_write = (current_frame_write + 1) % MAX_FRAMES;
            if (count_write++ >= 100) {
                qw_f_sync(gps_log_fp); // 每写入100帧数据同步一次文件
                count_write = 0;
            }
        }
    }
}

static rt_err_t ag333x_recv_data(uint8_t* buf, uint32_t len)
{
    if (ag333x_serial && s_ag333x_init) {
        int ret = rt_device_read(ag333x_serial, 0, buf, len);
        if (ret > 0)
        {
            gps_data_loss_count = 0;
            if (gps_debug_log_enable)
            {
                // 复制收到的数据到当前帧
                recv_buf_len[current_frame_save] = ret;
                rt_memcpy(recv_buf[current_frame_save], buf, ret);
                rt_sem_release(log_sem); // 释放信号量以触发写入线程
                // 更新当前帧索引，确保不超过最大帧数
                current_frame_save = (current_frame_save + 1) % MAX_FRAMES;
            }
            return RT_EOK;
        }
        else
        {
            if (gps_data_loss_count++ >= 2)
            {
                DRV_GPS_LOG_E("USART5_DMA_check!!!\n");
                USART5_DMA_check();
                gps_data_loss_count = 0;
            }
        }
    }
    return RT_ERROR;
}

/**
 * @brief 开启记录log
 *
 */
void ag333x_gps_debug_log_start(void)
{
    int ret = 0;

    // 生成文件名
    char file_name[128] = {0};
    qw_tm_t tm_time = {0};
    service_get_rtctime(&tm_time);
    snprintf(file_name, sizeof(file_name), "0:/iGPSPORT/System/capture/GNSS_DEBUG_%04d%02d%02d_%02d%02d%02d.txt",
        tm_time.tm_year + 1900, tm_time.tm_mon + 1, tm_time.tm_mday,
        tm_time.tm_hour + 8, tm_time.tm_min, tm_time.tm_sec);

    //打开文件
    ret = qw_f_open(&gps_log_fp,file_name, QW_FA_OPEN_ALWAYS|QW_FA_WRITE|QW_FA_APPEND);  // 打开文件以开始记录
    if (ret != QW_OK)
    {
        DRV_GPS_LOG_E("Failed to open log file %d\n", ret); // 记录打开文件失败的日志
        return; // 如果打开文件失败，直接返回
    }

    if(log_sem == RT_NULL){
        // 检查信号量是否已创建，如果没有则创建
        log_sem = rt_sem_create("log_sem", 0, RT_IPC_FLAG_FIFO); // 创建信号量
    }
    // 检查线程是否已创建，如果没有则创建
    rt_thread_t log_tid = rt_thread_find("gpsLog");
    if (log_tid == RT_NULL) {
        log_tid = rt_thread_create("gpsLog", gps_log_thread, RT_NULL, 1024*8, 15, 10); // 创建日志线程
        if (log_tid != RT_NULL) {
            rt_thread_startup(log_tid); // 启动线程
        }
    }
    rt_thread_mdelay(100);
    //启动记录使能
    gps_debug_log_enable = true;
}

/**
 * @brief 停止记录log
 *
 */
void ag333x_gps_debug_log_stop(void)
{
    //关闭文件
    if (gps_log_fp) {
        qw_f_close(gps_log_fp);
        gps_log_fp = RT_NULL;
    }
    //关闭记录使能
    gps_debug_log_enable = false;
}

#endif
/* ----------------------------------- 操作 ----------------------------------- */
//命令指针结构体
typedef struct
{
    enum_airoha_cmd cmd;
    const uint8_t* p_cmd;
    const uint8_t cmd_num;
}stru_gps_cmd;

//命令数组
static const stru_gps_cmd cmd_array[] = {
    { enum_AIROHA_NEMA_4_1, NEMA_4_1, sizeof(NEMA_4_1) },

    {enum_AIROHA_POWER_ON,GNSS_POWER_ON,sizeof(GNSS_POWER_ON) },
    {enum_AIROHA_POWER_OFF,GNSS_POWER_OFF,sizeof(GNSS_POWER_OFF) },
    {enum_AIROHA_NVRAM_SAVE_NAVIGATION_DATA,GNSS_NVRAM_SAVE_NAVIGATION_DATA,sizeof(GNSS_NVRAM_SAVE_NAVIGATION_DATA)},

    { enum_AIROHA_GxGSV_ON, GxGSV_ON, sizeof(GxGSV_ON) },
    { enum_AIROHA_GxGSV_OFF, GxGSV_OFF, sizeof(GxGSV_OFF) },
    { enum_AIROHA_GxGGA_ON, GxGGA_ON, sizeof(GxGGA_ON) },
    { enum_AIROHA_GxGGA_OFF, GxGGA_OFF, sizeof(GxGGA_OFF) },
    { enum_AIROHA_GxGLL_ON, GxGLL_ON, sizeof(GxGLL_ON) },
    { enum_AIROHA_GxGLL_OFF, GxGLL_OFF, sizeof(GxGLL_OFF) },
    { enum_AIROHA_GxGSA_ON, GxGSA_ON, sizeof(GxGSA_ON) },
    { enum_AIROHA_GxGSA_OFF, GxGSA_OFF, sizeof(GxGSA_OFF) },
    { enum_AIROHA_GxRMC_ON, GxRMC_ON, sizeof(GxRMC_ON) },
    { enum_AIROHA_GxRMC_OFF, GxRMC_OFF, sizeof(GxRMC_OFF) },
    { enum_AIROHA_GxVTG_ON, GxVTG_ON, sizeof(GxVTG_ON) },
    { enum_AIROHA_GxVTG_OFF, GxVTG_OFF, sizeof(GxVTG_OFF) },

    { enum_AIROHA_GPS_QZSS_GLONASS_ON, GPS_BD_GL_GA_QZSS_ON, sizeof(GPS_BD_GL_GA_QZSS_ON) },
    { enum_AIROHA_GPS_GA_QZSS_BEIDOU_ON, GPS_GA_QZSS_BEIDOU_ON, sizeof(GPS_GA_QZSS_BEIDOU_ON) },
    { enum_AIROHA_GPS_QZSS_BEIDOU_ON, GPS_BD_GA_QZSS_ON, sizeof(GPS_BD_GA_QZSS_ON) },
    { enum_AIROHA_GPS_GA_QZSS_ON, GPS_GA_QZSS_ON, sizeof(GPS_GA_QZSS_ON) },

    { enum_AIROHA_GPS_GLONASS_GA_ON, GPS_GLONASS_GA_ON, sizeof(GPS_GLONASS_GA_ON) },
    { enum_AIROHA_GPS_GLONASS_ON, GPS_GL_ON, sizeof(GPS_GL_ON) },
    { enum_AIROHA_GPS_BEIDOU_ON, GPS_BD_ON, sizeof(GPS_BD_ON) },
    { enum_AIROHA_GPS_ON, GPS_ON, sizeof(GPS_ON) },
    { enum_AIROHA_BEIDOU_ON, BD_ON, sizeof(BD_ON) },
    { enum_AIROHA_GPS_BD_GA_ON, GPS_BD_GA_ON, sizeof(GPS_BD_GA_ON) },
    { enum_AIROHA_GPS_GA_ON, GPS_GA_ON, sizeof(GPS_GA_ON) },

    { enum_AIROHA_SBAS_ON, SBAS_ON, sizeof(SBAS_ON) },
    { enum_AIROHA_SBAS_OFF, SBAS_OFF, sizeof(SBAS_OFF) },

    { enum_AIROHA_AIC_ON_FILTER, AIC_ON, sizeof(AIC_ON) },//主动干扰消除打开

    { enum_AIROHA_PWR_NORMAL, PWR_NORMAL, sizeof(PWR_NORMAL) },// 正常模式
    { enum_AIROHA_PWR_HIGH_CPU, PWR_HIGH_CPU, sizeof(PWR_HIGH_CPU) },// 性能模式

    { enum_AIROHA_COLDSTART, GNSS_COLD_START, sizeof(GNSS_COLD_START) },//冷启动
    { enum_AIROHA_WARMSTART, GNSS_WARM_START, sizeof(GNSS_WARM_START) },//温启动
    { enum_AIROHA_HOTSTART, GNSS_HOT_START, sizeof(GNSS_HOT_START) },//热启动

    { enum_AIROHA_DPM_PEDESTRIAN, NAVIGATION_MODE, sizeof(NAVIGATION_MODE) },//行人运动模式
    { enum_AIROHA_DPM_BIKE, NAVIGATION_MODE_BIKE, sizeof(NAVIGATION_MODE_BIKE) },//自行车运动模式
    { enum_AIROHA_DPM_FITNESS, NAVIGATION_MODE_FITNESS, sizeof(NAVIGATION_MODE_FITNESS) },//健身模式
    { enum_AIROHA_DPM_SWIMMING, NAVIGATION_MODE_SWIMMING, sizeof(NAVIGATION_MODE_SWIMMING) },//游泳模式

    { enum_AIROHA_LOW_POWER_ENABLE, PAIR_ALP_ENABLE, sizeof(PAIR_ALP_ENABLE) },//低功耗模式
    { enum_AIROHA_LOW_POWER_DISABLE, PAIR_ALP_DISABLE, sizeof(PAIR_ALP_DISABLE) },//关闭低功耗模式

    { enum_AIROHA_GET_EPO_STATUS, GNSS_EPO_STATUS, sizeof(GNSS_EPO_STATUS)},
    { enum_AIROHA_EPO_CLEAR, GNSS_EPO_CLEAR, sizeof(GNSS_EPO_CLEAR)},
    { enum_AIROHA_ENTER_RTC_MODE, GNSS_RTC_MODE, sizeof(GNSS_RTC_MODE)},
    { enum_AIROHA_GET_REF_UTC, GNSS_GET_REF_UTC, sizeof(GNSS_GET_REF_UTC)},
    { enum_AIROHA_DCB_ENABLE_OUTPUT, GNSS_DCB_ENABLE_OUTPUT, sizeof(GNSS_DCB_ENABLE_OUTPUT)},
    { enum_AIROHA_IMMEDIATE_SPEED_MODE, GNSS_IMMEDIATE_SPEED_MODE, sizeof(GNSS_IMMEDIATE_SPEED_MODE)},

    { enum_AIROHA_EPOC_PREDICT_ENABLE, GNSS_EPOC_PREDICT_ENABLE, sizeof(GNSS_EPOC_PREDICT_ENABLE)},
    { enum_AIROHA_EPOC_PREDICT_DISABLE, GNSS_EPOC_PREDICT_DISABLE, sizeof(GNSS_EPOC_PREDICT_DISABLE)},
    { enum_AIROHA_EPOC_MODE_NORMAL, GNSS_EPOC_MODE_NORMAL, sizeof(GNSS_EPOC_MODE_NORMAL)},
    { enum_AIROHA_EPOC_CONFIG, GNSS_EPOC_CONFIG, sizeof(GNSS_EPOC_CONFIG)},
    { enum_AIROHA_EPOC_GET_STATUS, GNSS_EPOC_GET_STATUS, sizeof(GNSS_EPOC_GET_STATUS)},
    { enum_AIROHA_SAVE_CONFIG, GNSS_EPOC_SAVE_CONFIG, sizeof(GNSS_EPOC_SAVE_CONFIG)},
    { enum_AIROHA_NAVI_SAVE_AUTO, GNSS_EPOC_NAVIDATA_AUTO_SAVE, sizeof(GNSS_EPOC_NAVIDATA_AUTO_SAVE)},
    { enum_AIROHA_NAVI_SAVE_AUTO_DISABLE, GNSS_EPOC_NAVIDATA_AUTO_SAVE_DISABLE, sizeof(GNSS_EPOC_NAVIDATA_AUTO_SAVE_DISABLE)},
    { enum_AIROHA_NAVI_SAVE, GNSS_EPOC_NAVIDATA_SAVE, sizeof(GNSS_EPOC_NAVIDATA_SAVE)},
    { enum_AIROHA_CLEAR_NAVI_DATA, GNSS_EPOC_CLEAR_NAVIDATA_DATA, sizeof(GNSS_EPOC_CLEAR_NAVIDATA_DATA)},
    { enum_AIROHA_DEBUG_LOG_ENABLE, GNSS_DEBUG_LOG_ENABLE, sizeof(GNSS_DEBUG_LOG_ENABLE)},
    { enum_AIROHA_DEBUG_LOG_DISABLE, GNSS_DEBUG_LOG_DISABLE, sizeof(GNSS_DEBUG_LOG_DISABLE)},
    { enum_AIROHA_PERIODIC_SET_MODE, GNSS_PERIODIC_SET_MODE, sizeof(GNSS_PERIODIC_SET_MODE)},
    { enum_AIROHA_PERIODIC_MODE_CANCEL, GNSS_PERIODIC_MODE_CANCEL, sizeof(GNSS_PERIODIC_MODE_CANCEL)},
    { enum_AIROHA_PERIODIC_GET_MODE, GNSS_PERIODIC_GET_MODE, sizeof(GNSS_PERIODIC_GET_MODE)},
    { enum_AIROHA_TIME_SET_REF_UTC, GNSS_TIME_SET_REF_UTC, sizeof(GNSS_TIME_SET_REF_UTC)},
    { enum_AIROHA_LOC_SET_REF, GNSS_LOC_SET_REF, sizeof(GNSS_LOC_SET_REF)},
    { enum_AIROHA_SET_DUAL_BAND_EN, GNSS_SET_DUAL_BAND_EN, sizeof(GNSS_SET_DUAL_BAND_EN)},
    { enum_AIROHA_SET_DUAL_BAND_DIS, GNSS_SET_DUAL_BAND_DIS, sizeof(GNSS_SET_DUAL_BAND_DIS)},
    { enum_AIROHA_GET_DUAL_BAND, GNSS_GET_DUAL_BAND, sizeof(GNSS_GET_DUAL_BAND)},
    { enum_AIROHA_SET_NAVIGATION_MODE, GNSS_SET_NAVIGATION_MODE, sizeof(GNSS_SET_NAVIGATION_MODE)},
    { enum_AIROHA_GET_NAVIGATION_MODE, GNSS_GET_NAVIGATION_MODE, sizeof(GNSS_GET_NAVIGATION_MODE)},
    { enum_AIROHA_ULP_ENABLE_EN, GNSS_ULP_ENABLE_EN, sizeof(GNSS_ULP_ENABLE_EN)},
    { enum_AIROHA_ULP_ENABLE_DIS, GNSS_ULP_ENABLE_DIS, sizeof(GNSS_ULP_ENABLE_DIS)},
    { enum_AIROHA_ULP_GET_STATUS, GNSS_ULP_GET_STATUS, sizeof(GNSS_ULP_GET_STATUS)},
    { enum_AIROHA_LOCK_SYSTEM_SLEEP_EN, GNSS_LOCK_SYSTEM_SLEEP_EN, sizeof(GNSS_LOCK_SYSTEM_SLEEP_EN)},
    { enum_AIROHA_LOCK_SYSTEM_SLEEP_DIS, GNSS_LOCK_SYSTEM_SLEEP_DIS, sizeof(GNSS_LOCK_SYSTEM_SLEEP_DIS)},
};

uint32_t ag333x_pair_command_encode(const uint8_t* buf, int32_t buf_len, uint8_t* temp_buf, int32_t temp_buf_len)
{
    const int32_t wait_ticket = 0xFFFFFFFF;
    int32_t ret_len = 0;
    const uint8_t* ind;
    uint8_t checkSumL = 0, checkSumR;
    if (buf_len + 6 > temp_buf_len) {
        return 0;
    }

    ind = buf;
    while (ind - buf < buf_len) {
        checkSumL ^= *ind;
        ind++;
    }

    temp_buf[0] = '$';
    rt_memcpy(temp_buf + 1, buf, buf_len);

    temp_buf[buf_len + 1] = '*';

    checkSumR = checkSumL & 0x0F;
    checkSumL = (checkSumL >> 4) & 0x0F;
    temp_buf[buf_len + 2] = checkSumL >= 10 ? checkSumL + 'A' - 10 : checkSumL + '0';
    temp_buf[buf_len + 3] = checkSumR >= 10 ? checkSumR + 'A' - 10 : checkSumR + '0';

    temp_buf[buf_len + 4] = '\r';
    temp_buf[buf_len + 5] = '\n';

    buf_len += 6;

    return buf_len;
}

static void ag333x_band_ctrl(enum_airoha_cmd gps_cmd)
{
#ifndef SIMULATOR
    switch(gps_cmd)
    {
        case enum_AIROHA_L1:
            // rt_pin_write(GPS_POWER_L1, PIN_HIGH);
            // rt_pin_write(GPS_POWER_L5, PIN_LOW);
        break;
        case enum_AIROHA_L5:
            // rt_pin_write(GPS_POWER_L1, PIN_LOW);
            // rt_pin_write(GPS_POWER_L5, PIN_HIGH);
        break;
        case enum_AIROHA_L1_L5:
            // rt_pin_write(GPS_POWER_L1, PIN_HIGH);
            // rt_pin_write(GPS_POWER_L5, PIN_HIGH);
        break;
        default:
            break;
    }
#endif
}

void ag333x_gps_cmd_ctrl(enum_airoha_cmd gps_cmd)
{
    if(gps_cmd >= enum_AIROHA_L1 && gps_cmd <= enum_AIROHA_L1_L5)
    {
        // ag333x_band_ctrl(gps_cmd);
    }
    else{
        uint8_t tmp_cmd_buf[128];
        uint32_t encode_size;

        // rt_kprintf("gps_cmd:%d\n", gps_cmd);
        uint8_t max_num = sizeof(cmd_array) / sizeof(stru_gps_cmd); //命令数量
        for (uint8_t num = 0; num < max_num; num++)
        {
            if (gps_cmd == cmd_array[num].cmd)
            {
                // rt_kprintf("gps_cmd:%d, cmd_array[num].cmd:%d\n", gps_cmd, cmd_array[num].cmd);
                encode_size = ag333x_pair_command_encode(cmd_array[num].p_cmd, cmd_array[num].cmd_num - 1, tmp_cmd_buf, 128);
                ag333x_send_data(tmp_cmd_buf, encode_size);
                DRV_GPS_LOG_E("encode_buf:%s\n", tmp_cmd_buf);
            }
        }
    }
}

void ag333x_gps_pair_debug(uint8_t *pair)
{
    uint8_t tmp_cmd_buf[128];
    uint32_t encode_size;

    encode_size = ag333x_pair_command_encode(pair, strlen((const char *)pair), tmp_cmd_buf, 128);
    ag333x_send_data(tmp_cmd_buf, encode_size);
    DRV_GPS_LOG_D("encode_buf:%s\n", tmp_cmd_buf);
}

/**
 * @brief GPS chirp使能
 *
 */
static void ag333x_chirp_enable(void)
{
    rt_pin_mode(GPS_CHIP_EN, PIN_MODE_OUTPUT);
    rt_pin_write(GPS_CHIP_EN, PIN_HIGH);
    HAL_Delay(2);
}

/**
 * @brief GPS chirp禁用
 *
 */
static void ag333x_chirp_disable(void)
{
    rt_pin_mode(GPS_CHIP_EN, PIN_MODE_OUTPUT);
    rt_pin_write(GPS_CHIP_EN, PIN_LOW);
    HAL_Delay(10);
}

/**
 * @brief GPS IO口高阻态
 *
 */
void ag333x_io_suspend(void)
{
    //操作口高阻态
    // HAL_PIN_Set_Analog(PAD_PB39,GPS_IO_IN_HCPU);//power_en
    // HAL_PIN_Set_Analog(PAD_PB37,GPS_IO_IN_HCPU);//CHIRP
    // HAL_PIN_Set_Analog(PAD_PB40,GPS_IO_IN_HCPU);//外部中断引脚
    //唤醒口高阻态
   // HAL_PIN_Set_Analog(PAD_PB48,GPS_IO_IN_HCPU);//gps唤醒mcu
    HAL_PIN_Set_Analog(PAD_PB41,GPS_IO_IN_HCPU);//mcu唤醒gps
}


/**
 * @brief GPS进入RTC模式
 *
 */
void ag333x_gps_sleep(void)
{
    ag333x_gps_cmd_ctrl(enum_AIROHA_ENTER_RTC_MODE);
}

/**
 * @brief GPS退出RTC模式
 *
 */
void ag333x_gps_wakeup(void)
{
    rt_pin_mode(GPS_RTC_EINT_PIN, PIN_MODE_OUTPUT);
    rt_pin_write(GPS_RTC_EINT_PIN, PIN_LOW);
    HAL_Delay(2);
    rt_pin_write(GPS_RTC_EINT_PIN, PIN_HIGH);
    HAL_Delay(2);
    rt_pin_write(GPS_RTC_EINT_PIN, PIN_LOW);
    HAL_Delay(10);
}

/**
 * @brief GPS退出睡眠模式
 *
 */
void ag333x_gps_mcu_wakeup_gnss(void)
{
    //外部唤醒IO
    qw_gpio_set(MCU_WAKE_UP_GPS, GPIO_MODE_OUTPUT, PIN_NOPULL);

    rt_pin_mode(MCU_WAKEUP_GPS_PIN, PIN_MODE_OUTPUT);
    rt_pin_write(MCU_WAKEUP_GPS_PIN, PIN_HIGH);
    HAL_Delay(2);
    rt_pin_write(MCU_WAKEUP_GPS_PIN, PIN_LOW);
    HAL_Delay(10);
}

//GPS上电
void ag333x_gps_poweron(void)
{
#ifndef SIMULATOR
    ag333x_chirp_enable();
    //上电
    power_control_gps(true);
    HAL_Delay(8);
#endif
}

//GPS下电
void ag333x_gps_poweroff(void)
{
#ifndef SIMULATOR
    ag333x_chirp_disable();
    //下电
    power_control_gps(false);
    HAL_Delay(2);
#endif
}

//GPS得到数据
int ag333x_gps_get(uint8_t* data, uint16_t length)
{
    return ag333x_recv_data(data, length);
}

//GPS发送数据
int ag333x_gps_set(uint8_t* data, uint16_t length)
{
    return ag333x_send_data(data, length);
}


//设置GPS GNSS模式
void ag333x_gps_gnss_set(gps_mode_type_e mode)
{
    // uint8_t cfg_word = enum_GNSS_INVALID;
    // gps_mode_cfg_t* gps_mode_cfg = get_gps_mode_cfg();

    // for (uint8_t i = 0; i < enum_gps_mode_end; i++)
    // {
    //     if (gps_mode_cfg[i].type == mode)
    //     {
    //         cfg_word = gps_mode_cfg[i].cfg_word;
    //         break;
    //     }
    // }

    // switch (cfg_word)
    // {
    // case (enum_GNSS_GPS | enum_GNSS_QZSS | enum_GNSS_GLONASS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_QZSS_GLONASS_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_QZSS | enum_GNSS_BD):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_QZSS_BEIDOU_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_GLONASS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GLONASS_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_BD):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_BEIDOU_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_GALILEO):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GA_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case enum_GNSS_GPS:
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case enum_GNSS_BD:
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_BEIDOU_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_BD | enum_GNSS_GALILEO):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_BD_GA_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_OFF);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_BD | enum_GNSS_GALILEO | enum_GNSS_QZSS | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GA_QZSS_BEIDOU_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_GALILEO | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GA_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_BD | enum_GNSS_GALILEO | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_BD_GA_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_BD | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_BEIDOU_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_GALILEO | enum_GNSS_QZSS | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GA_QZSS_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // case (enum_GNSS_GPS | enum_GNSS_GLONASS | enum_GNSS_GALILEO | enum_GNSS_SBAS):
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_GPS_GLONASS_GA_ON);
    //     ag333x_gps_cmd_ctrl(enum_AIROHA_SBAS_ON);
    //     break;
    // default:
    //     break;
    // }
}

void ag333x_epoc_config(void)
{
    ag333x_gps_cmd_ctrl(enum_AIROHA_EPOC_PREDICT_ENABLE);
    ag333x_gps_cmd_ctrl(enum_AIROHA_EPOC_MODE_NORMAL);
    ag333x_gps_cmd_ctrl(enum_AIROHA_EPOC_CONFIG);
    ag333x_gps_cmd_ctrl(enum_AIROHA_EPOC_GET_STATUS);
}

void ag333x_config_default(void)
{
    //ag333x_gps_cmd_ctrl(enum_AIROHA_NEMA_4_1);//default
    ag333x_gps_cmd_ctrl(enum_AIROHA_GxRMC_ON);
    ag333x_gps_cmd_ctrl(enum_AIROHA_GxGGA_ON);
    //ag333x_gps_cmd_ctrl(enum_AIROHA_GxGSA_ON);//default on

    ag333x_gps_cmd_ctrl(enum_AIROHA_GxVTG_OFF);
    ag333x_gps_cmd_ctrl(enum_AIROHA_GxGLL_OFF);
    // ag333x_gps_cmd_ctrl(enum_AIROHA_GxGSV_ON);

    // ag333x_gps_cmd_ctrl(enum_AIROHA_DEBUG_LOG_ENABLE);

    ag333x_gps_cmd_ctrl(enum_AIROHA_NAVI_SAVE_AUTO);
    // ag333x_gps_cmd_ctrl(enum_AIROHA_NAVI_SAVE_AUTO_DISABLE);

    ag333x_gps_cmd_ctrl(enum_AIROHA_EPOC_GET_STATUS);

#ifdef CONFIG_GPS_DEBUG_CAPTURE
    ag333x_gps_cmd_ctrl(enum_AIROHA_DEBUG_LOG_ENABLE);
#endif

}

void airoha_ag333x_poweron_with_chip_reset(void)
{
#ifndef SIMULATOR
    //gps上电
    ag333x_gps_poweron();
    //gps唤醒
    ag333x_gps_wakeup();
#endif
}

void ag333x_gps_poweroff_with_chipen(void)
{
#ifndef SIMULATOR
    //gps下电
    ag333x_gps_poweroff();
#endif
}



/**
 * @brief ag333x初始化
 *
 * @return int
 */
int ag333x_init(void)
{
    DRV_GPS_LOG_D("[ag333x_init]: ag333x_serial: %d, s_ag333x_init: %d\n", ag333x_serial, s_ag333x_init);
    if (!ag333x_serial && !s_ag333x_init) {
        ag333x_serial = rt_device_find(GPS_AG335X_UART_NAME);
        rt_err_t err = rt_device_open(ag333x_serial, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_DMA_RX);
        if(err == RT_EOK){
            s_ag333x_init = true;
            return RT_EOK;
        }
    }
    return RT_EBUSY;
}

/**
 * @brief ag333x反初始化
 *
 * @return int
 */
int ag333x_uninit(void)
{
    DRV_GPS_LOG_D("[ag333x_uninit]: ag333x_serial: %d, s_ag333x_init: %d\n", ag333x_serial, s_ag333x_init);
    if (ag333x_serial && s_ag333x_init) {
        s_ag333x_init = false;
        rt_device_close(ag333x_serial);

        ag333x_serial = RT_NULL;
        ag333x_io_suspend();
    }
    return RT_EOK;
}



void ag333x_set_time_utc(char* utc_time)
{
    uint8_t utc_time_buf[64];
    uint8_t tmp_cmd_buf[128];
    uint32_t encode_size;
    rt_snprintf((char *)utc_time_buf, sizeof(utc_time_buf), "%s%s", GNSS_TIME_SET_REF_UTC, utc_time);

    encode_size = ag333x_pair_command_encode(utc_time_buf, rt_strlen((const char*)utc_time_buf), tmp_cmd_buf, 128);
    ag333x_send_data(tmp_cmd_buf, encode_size);
    DRV_GPS_LOG_D("cmd_ctrl_set_time_utc:%s\n", (char *)tmp_cmd_buf);
}

void ag333x_set_location(char* location)
{
    uint8_t location_buf[64];
    uint8_t tmp_cmd_buf[128];
    uint32_t encode_size;
    rt_snprintf((char *)location_buf, sizeof(location_buf), "%s%s", GNSS_LOC_SET_REF, location);

    encode_size = ag333x_pair_command_encode(location_buf, rt_strlen((const char*)location_buf), tmp_cmd_buf, 128);
    ag333x_send_data(tmp_cmd_buf, encode_size);
    DRV_GPS_LOG_D("cmd_ctrl_set_location:%s\n", (char *)tmp_cmd_buf);
}

void ag333x_set_navigation_mode(char mode)
{
    uint8_t location_buf[64];
    uint8_t tmp_cmd_buf[128];
    uint32_t encode_size;
    rt_snprintf((char *)location_buf, sizeof(location_buf), "%s%01d", GNSS_SET_NAVIGATION_MODE, mode);

    encode_size = ag333x_pair_command_encode(location_buf, rt_strlen((const char*)location_buf), tmp_cmd_buf, 128);
    ag333x_send_data(tmp_cmd_buf, encode_size);
    DRV_GPS_LOG_D("cmd_ctrl_set_navigation_mode:%d\n", mode);
}

#endif
