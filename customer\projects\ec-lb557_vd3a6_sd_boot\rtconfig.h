/*Auto generated configuration*/
#ifndef RT_CONFIG_H
#define RT_CONFIG_H

#define SOC_SF32LB55X 1
#define CORE "HCPU"
#define CPU "Cortex-M33"
#define BSP_USING_BOARD_EC_LB557XXX 1
#define LXT_FREQ 32768
#define LXT_LP_CYCLE 200
#define BLE_TX_POWER_VAL 0
#define BSP_LB55X_CHIP_ID 2
#define ASIC 1
#define TOUCH_IRQ_PIN 79
#define HRS3300_POW_PIN 105
#define BSP_SUPPORT_DSI 1
#define BSP_SUPPORT_DPI 1
#define LB557_VD3A6 1
#define BSP_USING_PSRAM 1
#define BSP_USING_GPIO 1
#define BSP_GPIO_HANDLE 1
#define BSP_USING_DMA 1
#define BSP_USING_UART 1
#define BSP_USING_UART1 1
#define BSP_UART1_RX_USING_DMA 1
#define BSP_USING_UART3 1
#define BSP_UART3_RX_USING_DMA 1
#define BSP_USING_UART4 1
#define BSP_UART4_RX_USING_DMA 1
#define BSP_USING_SPI_FLASH 1
#define BSP_ENABLE_QSPI1 1
#define BSP_QSPI1_MODE 0
#define BSP_QSPI1_USING_DMA 1
#define BSP_QSPI1_MEM_SIZE 1
#define BSP_ENABLE_QSPI4 1
#define BSP_QSPI4_MODE 2
#define BSP_QSPI4_USING_DMA 1
#define BSP_USING_PSRAM4 1
#define BSP_QSPI4_MEM_SIZE 2
#define BSP_USING_QSPI 1
#define BSP_QSPI1_CHIP_ID 0
#define BSP_QSPI4_MODE_2 1
#define BSP_QSPI4_CHIP_ID 0
#define BSP_USING_EXT_DMA 1
#define BSP_USING_PSRAM0 1
#define PSRAM_FULL_SIZE 16
#define BSP_USING_XCCELA_PSRAM 1
#define PSRAM_USING_DMA 1
#define BSP_USING_SDIO 1
#define BSP_USING_SDHCI 1
#define SD_MMC_OLINE_SUPPORT 1
#define BSP_USING_SDHCI1 1
#define SD_MAX_FREQ 24000000
#define SD_DMA_MODE 2
#define SDIO_CARD_MODE 1
#define BSP_USING_PINMUX 1
#define BSP_NOT_DISABLE_UNUSED_MODULE 1
#define BSP_USING_RTTHREAD 1
#define RT_USING_COMPONENTS_INIT 1
#define RT_USING_USER_MAIN 1
#define RT_MAIN_THREAD_STACK_SIZE 2048
#define RT_MAIN_THREAD_PRIORITY 10
#define RT_USING_FINSH 1
#define FINSH_THREAD_NAME "tshell"
#define FINSH_USING_HISTORY 1
#define FINSH_HISTORY_LINES 5
#define FINSH_USING_SYMTAB 1
#define FINSH_USING_DESCRIPTION 1
#define FINSH_THREAD_PRIORITY 20
#define FINSH_THREAD_STACK_SIZE 4096
#define FINSH_CMD_SIZE 80
#define FINSH_USING_MSH 1
#define FINSH_USING_MSH_DEFAULT 1
#define FINSH_ARG_MAX 12
#define RT_USING_DFS 1
#define DFS_USING_WORKDIR 1
#define DFS_FILESYSTEMS_MAX 2
#define DFS_FILESYSTEM_TYPES_MAX 2
#define DFS_FD_MAX 16
#define RT_USING_DFS_DEVFS 1
#define RT_USING_DEVICE_IPC 1
#define RT_PIPE_BUFSZ 512
#define RT_USING_SYSTEM_WORKQUEUE 1
#define RT_SYSTEM_WORKQUEUE_STACKSIZE 2048
#define RT_SYSTEM_WORKQUEUE_PRIORITY 23
#define RT_USING_SERIAL 1
#define RT_SERIAL_USING_DMA 1
#define RT_SERIAL_RB_BUFSZ 256
#define RT_SERIAL_DEFAULT_BAUDRATE 1000000
#define RT_USING_PIN 1
#define RT_USING_SDIO 1
#define RT_SDIO_STACK_SIZE 512
#define RT_SDIO_THREAD_PRIORITY 15
#define RT_MMCSD_STACK_SIZE 2048
#define RT_MMCSD_THREAD_PREORITY 22
#define RT_MMCSD_MAX_PARTITION 16
#define RT_MMCSD_USER_OFFSET 0
#define RT_SDIO_DEBUG 1
#define RT_USING_LIBC 1
#define RT_USING_POSIX 1
#define RT_USING_ULOG 1
#define ULOG_OUTPUT_LVL_D 1
#define ULOG_OUTPUT_LVL 7
#define ULOG_USING_ISR_LOG 1
#define ULOG_ASSERT_ENABLE 1
#define ULOG_LINE_BUF_SIZE 128
#define ULOG_OUTPUT_TIME 1
#define ULOG_OUTPUT_LEVEL 1
#define ULOG_OUTPUT_TAG 1
#define ULOG_BACKEND_USING_CONSOLE 1
#define RT_NAME_MAX 8
#define RT_ALIGN_SIZE 4
#define RT_THREAD_PRIORITY_32 1
#define RT_THREAD_PRIORITY_MAX 32
#define RT_TICK_PER_SECOND 1000
#define RT_USING_OVERFLOW_CHECK 1
#define RT_USING_HOOK 1
#define RT_USING_IDLE_HOOK 1
#define RT_IDEL_HOOK_LIST_SIZE 4
#define IDLE_THREAD_STACK_SIZE 1024
#define RT_DEBUG 1
#define RT_USING_SEMAPHORE 1
#define RT_USING_MUTEX 1
#define RT_USING_EVENT 1
#define RT_USING_MAILBOX 1
#define RT_USING_MESSAGEQUEUE 1
#define RT_USING_MEMPOOL 1
#define RT_USING_MEMHEAP 1
#define RT_USING_SMALL_MEM 1
#define RT_USING_HEAP 1
#define RT_USING_DEVICE 1
#define RT_USING_CONSOLE 1
#define RT_CONSOLEBUF_SIZE 256
#define RT_CONSOLE_DEVICE_NAME "uart3"
#define RT_VER_NUM 0x30103
#define BSP_USING_FULL_ASSERT 1
#define USING_IPC_QUEUE 1
#define BF0_HCPU 1
#define CFG_BOOTLOADER 1
#endif
