@使用指南

    @介绍：rt_driver ec-lb551工程是基于RTthreadOS，在EC-LB551 evb板子上作为BLE peripheral role的示例。
                - 包含了创建自定义的GATT service，进行BLE的广播、连接，以及连接后基本的GATT数据交互。
                - BLE的service和application均运行在HCPU(High performance CPU)
    
    @Menuconfig配置：该工程所用到BLE相关的配置如下：
                - Sifli Middleware->Enable BLE service[*]： 打开BLE service，该service会提供BLE GAP/GATT/COMMON的服务
                    - Enable NVDS synchronous[*]: 打开同步NVDS服务，NVDS相关操作均会同步执行。NVDS服务会将BLE stack/service的重要信息存入Flash
                - Third party packages->FlashDB[*]: 打开FlashDB提供访问Flash的接口，NVDS需要打开该服务
    
    @函数入口：
        1. main(): 系统开始调度后会被call到，该函数会enable BLE service进而打开BLE，初始化OS的mailbox，并进入while loop。在收到蓝牙power on的通知后，注册自定义GATT service并并打开广播。
        2. ble_app_event_handler(): 该函数通过BLE_EVENT_REGISTER注册到BLE service中，处理GAP/GATT/Common等BLE相关的事件。
        3. 自定义GATT service UUID：“00000000-0000-0070-7061-5F696C666973”。
                - 自定义characteristic UUID: "00000000-0000-0170-7061-5F696C666973"
                
    @相关Shell命令：
        1. 出厂化BLE相关Flash数据：nvds reset_all 1
                - 为避免Flash冲突，第一次使用最好先下该命令。
        2. 设置蓝牙MAC地址：nvds update addr 6 [addr]. Example: nvds update addr 6 2345670123C3

    @手机端建议：
		1. iPhone手机推荐用第三方软件LightBlue，Android端用nRF Connect进行BLE测试。