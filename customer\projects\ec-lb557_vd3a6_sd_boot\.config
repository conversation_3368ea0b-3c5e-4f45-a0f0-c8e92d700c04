CONFIG_SOC_SF32LB55X=y
# CONFIG_SOC_SF32LB58X is not set
# CONFIG_SOC_SF32LB56X is not set
# CONFIG_SOC_SF32LB52X is not set
# CONFIG_SOC_SIMULATOR is not set
CONFIG_CORE="HCPU"
CONFIG_CPU="Cortex-M33"
# CONFIG_BSP_USING_BOARD_EC_LB555_WATCH is not set
# CONFIG_BSP_USING_BOARD_EH_SS6600XXX is not set
# CONFIG_BSP_USING_BOARD_EC_LB551XXX is not set
# CONFIG_BSP_USING_BOARD_EC_LB555XXX is not set
# CONFIG_BSP_USING_BOARD_EH_LB555 is not set
CONFIG_BSP_USING_BOARD_EC_LB557XXX=y
# CONFIG_BSP_USING_BOARD_FPGA_A0 is not set
# CONFIG_BSP_USING_BOARD_CUSTOMER is not set
# CONFIG_BSP_USING_NON_SIFLI_BOARD is not set

#
# Board Config
#
# CONFIG_LXT_DISABLE is not set
CONFIG_LXT_FREQ=32768
# CONFIG_BPS_V33 is not set
CONFIG_LXT_LP_CYCLE=200
CONFIG_BLE_TX_POWER_VAL=0
# CONFIG_BSP_CHIP_ID_COMPATIBLE is not set
CONFIG_BSP_LB55X_CHIP_ID=2
# end of Board Config

CONFIG_ASIC=y
CONFIG_TOUCH_IRQ_PIN=79
CONFIG_HRS3300_POW_PIN=105
CONFIG_BSP_SUPPORT_DSI=y
CONFIG_BSP_SUPPORT_DPI=y
CONFIG_LB557_VD3A6=y
# CONFIG_LB557_VC536 is not set
CONFIG_BSP_USING_PSRAM=y
# CONFIG_BSP_USING_LCD is not set

#
# On-chip Peripheral RTOS Drivers
#
CONFIG_BSP_USING_GPIO=y
CONFIG_BSP_GPIO_HANDLE=1
# CONFIG_BSP_USING_BUTTON is not set
CONFIG_BSP_USING_DMA=y
CONFIG_BSP_USING_UART=y
CONFIG_BSP_USING_UART1=y
CONFIG_BSP_UART1_RX_USING_DMA=y
# CONFIG_BSP_UART1_TX_USING_DMA is not set
# CONFIG_BSP_USING_UART2 is not set
CONFIG_BSP_USING_UART3=y
CONFIG_BSP_UART3_RX_USING_DMA=y
# CONFIG_BSP_UART3_TX_USING_DMA is not set
CONFIG_BSP_USING_UART4=y
CONFIG_BSP_UART4_RX_USING_DMA=y
# CONFIG_BSP_UART4_TX_USING_DMA is not set
# CONFIG_BSP_USING_UART5 is not set
# CONFIG_BSP_USING_SPI is not set
# CONFIG_BSP_USING_I2C is not set
# CONFIG_BSP_USING_SOFT_I2C is not set
# CONFIG_BSP_USING_TIM is not set
# CONFIG_BSP_USING_PWM is not set
# CONFIG_BSP_USING_ADC is not set
# CONFIG_BSP_USING_SDADC is not set
# CONFIG_BSP_USING_COMP is not set
CONFIG_BSP_USING_SPI_FLASH=y
CONFIG_BSP_ENABLE_QSPI1=y
CONFIG_BSP_QSPI1_MODE=0
CONFIG_BSP_QSPI1_USING_DMA=y
CONFIG_BSP_QSPI1_MEM_SIZE=1
# CONFIG_BSP_ENABLE_QSPI2 is not set
# CONFIG_BSP_ENABLE_QSPI3 is not set
CONFIG_BSP_ENABLE_QSPI4=y
CONFIG_BSP_QSPI4_MODE=2
CONFIG_BSP_QSPI4_USING_DMA=y
CONFIG_BSP_USING_PSRAM4=y
CONFIG_BSP_QSPI4_MEM_SIZE=2
CONFIG_BSP_USING_QSPI=y
CONFIG_BSP_QSPI1_CHIP_ID=0
# CONFIG_BSP_QSPI4_MODE_0 is not set
# CONFIG_BSP_QSPI4_MODE_1 is not set
CONFIG_BSP_QSPI4_MODE_2=y
CONFIG_BSP_QSPI4_CHIP_ID=0
# CONFIG_BSP_QSPI4_DUAL_CHIP is not set
CONFIG_BSP_USING_EXT_DMA=y
# CONFIG_BSP_USING_HW_AES is not set
CONFIG_BSP_USING_PSRAM0=y
CONFIG_PSRAM_FULL_SIZE=16
# CONFIG_BSP_USING_EXT_PSRAM is not set
CONFIG_BSP_USING_XCCELA_PSRAM=y
# CONFIG_BSP_USING_DUAL_PSRAM is not set
CONFIG_PSRAM_USING_DMA=y
# CONFIG_BSP_USING_USBD is not set
# CONFIG_BSP_USING_USBH is not set
# CONFIG_BSP_USING_ONCHIP_RTC is not set
# CONFIG_BSP_USING_WDT is not set
# CONFIG_BSP_USING_EPIC is not set
# CONFIG_BSP_USING_LCDC is not set
# CONFIG_BSP_USING_TOUCHD is not set
# CONFIG_BSP_USING_CMSIS_NN is not set
# CONFIG_BSP_USING_NN_ACC is not set
# CONFIG_BSP_USING_HWMAILBOX is not set
# CONFIG_BSP_USING_I2S is not set
CONFIG_BSP_USING_SDIO=y
CONFIG_BSP_USING_SDHCI=y
# CONFIG_SD_MMC_DDR_SUPPORT is not set
CONFIG_SD_MMC_OLINE_SUPPORT=y
CONFIG_BSP_USING_SDHCI1=y
CONFIG_SD_MAX_FREQ=24000000
CONFIG_SD_DMA_MODE=2
CONFIG_SDIO_CARD_MODE=1
# CONFIG_BSP_USING_SDHCI2 is not set
CONFIG_BSP_USING_PINMUX=y
# CONFIG_BSP_USING_LCPU_PATCH is not set
# CONFIG_BSP_USING_PDM is not set
# CONFIG_RT_USING_BT is not set
# CONFIG_APP_BSP_TEST is not set
# CONFIG_BSP_USING_MOTOR is not set
# CONFIG_BSP_USING_BUSMON is not set
CONFIG_BSP_NOT_DISABLE_UNUSED_MODULE=y
# end of On-chip Peripheral RTOS Drivers

#
# Select board peripherals
#

#
# Key config
#
# CONFIG_BSP_USING_KEY1 is not set
# CONFIG_BSP_USING_KEY2 is not set
# end of Key config

#
# Charger config
#
# CONFIG_BSP_USING_CHARGER is not set
# end of Charger config

# CONFIG_SENSOR_USING_GPS is not set
# CONFIG_SENSOR_USING_HR is not set
# CONFIG_SENSOR_USING_PEDO is not set
# CONFIG_SENSOR_USING_6D is not set
# CONFIG_SENSOR_USING_MAG is not set
# CONFIG_SENSOR_USING_BAROMETER is not set
# CONFIG_SENSOR_USING_ASL is not set
# CONFIG_BL_USING_AW9364 is not set
# CONFIG_AUDIO_USING_DA7212 is not set
# CONFIG_BUZZER_ENABLED is not set
# CONFIG_VIBRATOR_ENABLED is not set
# CONFIG_MOTOR_ENABLED is not set
# CONFIG_SENSOR_USING_MPU6050 is not set
# CONFIG_SENSOR_USING_DOF10 is not set
# CONFIG_ENCODER_USING_DK05E01TF412 is not set
# CONFIG_PMIC_CTRL_ENABLE is not set
# CONFIG_TOUCH_WAKEUP_SUPPORT is not set
# CONFIG_BSP_USING_SPI_CAMERA is not set
# CONFIG_RT_USING_GPS is not set
# CONFIG_PA_USING_AW882XX is not set
# CONFIG_PA_USING_AW87390 is not set
# CONFIG_PA_USING_AW8155 is not set
# end of Select board peripherals

#
# ------------End of Board configuration-----------
#

#
# RTOS
#
# CONFIG_BSP_USING_NO_OS is not set
CONFIG_BSP_USING_RTTHREAD=y
# CONFIG_BSP_USING_FREERTOS is not set

#
# RT-Thread Components
#
CONFIG_RT_USING_COMPONENTS_INIT=y
CONFIG_RT_USING_USER_MAIN=y
CONFIG_RT_MAIN_THREAD_STACK_SIZE=2048
CONFIG_RT_MAIN_THREAD_PRIORITY=10

#
# C++ features
#
# CONFIG_RT_USING_CPLUSPLUS is not set
# end of C++ features

#
# Command shell
#
CONFIG_RT_USING_FINSH=y
CONFIG_FINSH_THREAD_NAME="tshell"
CONFIG_FINSH_USING_HISTORY=y
CONFIG_FINSH_HISTORY_LINES=5
CONFIG_FINSH_USING_SYMTAB=y
CONFIG_FINSH_USING_DESCRIPTION=y
# CONFIG_FINSH_ECHO_DISABLE_DEFAULT is not set
CONFIG_FINSH_THREAD_PRIORITY=20
CONFIG_FINSH_THREAD_STACK_SIZE=4096
CONFIG_FINSH_CMD_SIZE=80
# CONFIG_FINSH_USING_AUTH is not set
CONFIG_FINSH_USING_MSH=y
CONFIG_FINSH_USING_MSH_DEFAULT=y
# CONFIG_FINSH_USING_MSH_ONLY is not set
CONFIG_FINSH_ARG_MAX=12
# end of Command shell

#
# Device virtual file system
#
CONFIG_RT_USING_DFS=y
CONFIG_DFS_USING_WORKDIR=y
CONFIG_DFS_FILESYSTEMS_MAX=2
CONFIG_DFS_FILESYSTEM_TYPES_MAX=2
CONFIG_DFS_FD_MAX=16
# CONFIG_RT_USING_DFS_MNTTABLE is not set
# CONFIG_RT_USING_DFS_ELMFAT is not set
CONFIG_RT_USING_DFS_DEVFS=y
# CONFIG_RT_USING_DFS_ROMFS is not set
# CONFIG_RT_USING_DFS_RAMFS is not set
# CONFIG_RT_USING_DFS_UFFS is not set
# CONFIG_RT_USING_DFS_JFFS2 is not set
# CONFIG_RT_USING_LITTLE_FS is not set
# CONFIG_PKG_USING_DFS_YAFFS is not set
# end of Device virtual file system

#
# Device Drivers
#
CONFIG_RT_USING_DEVICE_IPC=y
CONFIG_RT_PIPE_BUFSZ=512
CONFIG_RT_USING_SYSTEM_WORKQUEUE=y
CONFIG_RT_SYSTEM_WORKQUEUE_STACKSIZE=2048
CONFIG_RT_SYSTEM_WORKQUEUE_PRIORITY=23
CONFIG_RT_USING_SERIAL=y
CONFIG_RT_SERIAL_USING_DMA=y
CONFIG_RT_SERIAL_RB_BUFSZ=256
CONFIG_RT_SERIAL_DEFAULT_BAUDRATE=1000000
# CONFIG_RT_USING_CAN is not set
# CONFIG_RT_USING_HWTIMER is not set
# CONFIG_RT_USING_HWMAILBOX is not set
# CONFIG_RT_USING_CPUTIME is not set
# CONFIG_RT_USING_I2C is not set
CONFIG_RT_USING_PIN=y
# CONFIG_RT_USING_BLUETOOTH is not set
# CONFIG_RT_USING_ADC is not set
# CONFIG_RT_USING_PWM is not set
# CONFIG_RT_USING_MTD_NOR is not set
# CONFIG_RT_USING_MTD_NAND is not set
# CONFIG_RT_USING_PM is not set
# CONFIG_RT_USING_RTC is not set
CONFIG_RT_USING_SDIO=y
CONFIG_RT_SDIO_STACK_SIZE=512
CONFIG_RT_SDIO_THREAD_PRIORITY=15
CONFIG_RT_MMCSD_STACK_SIZE=2048
CONFIG_RT_MMCSD_THREAD_PREORITY=22
CONFIG_RT_MMCSD_MAX_PARTITION=16
CONFIG_RT_MMCSD_USER_OFFSET=0
CONFIG_RT_SDIO_DEBUG=y
# CONFIG_RT_USING_SPI is not set
# CONFIG_RT_USING_WDT is not set
# CONFIG_RT_USING_AUDIO is not set
# CONFIG_RT_USING_SENSOR is not set
# CONFIG_RT_USING_MOTOR is not set

#
# Using WiFi
#
# CONFIG_RT_USING_WIFI is not set
# end of Using WiFi

#
# Using USB
#
# CONFIG_RT_USING_USB_HOST is not set
# CONFIG_RT_USING_USB_DEVICE is not set
# end of Using USB
# end of Device Drivers

#
# POSIX layer and C standard library
#
CONFIG_RT_USING_LIBC=y
# CONFIG_RT_USING_PTHREADS is not set
CONFIG_RT_USING_POSIX=y
# CONFIG_RT_USING_POSIX_MMAP is not set
# CONFIG_RT_USING_POSIX_TERMIOS is not set
# CONFIG_RT_USING_POSIX_AIO is not set
# CONFIG_RT_USING_MODULE is not set
# end of POSIX layer and C standard library

#
# Network
#

#
# Socket abstraction layer
#
# CONFIG_RT_USING_SAL is not set
# end of Socket abstraction layer

#
# Network interface device
#
# CONFIG_RT_USING_NETDEV is not set
# end of Network interface device

#
# light weight TCP/IP stack
#
# CONFIG_RT_USING_LWIP is not set
# end of light weight TCP/IP stack

#
# Modbus master and slave stack
#
# CONFIG_RT_USING_MODBUS is not set
# end of Modbus master and slave stack

#
# AT commands
#
# CONFIG_RT_USING_AT is not set
# end of AT commands
# end of Network

#
# Utilities
#
# CONFIG_RT_USING_RYM is not set
CONFIG_RT_USING_ULOG=y
# CONFIG_ULOG_OUTPUT_LVL_A is not set
# CONFIG_ULOG_OUTPUT_LVL_E is not set
# CONFIG_ULOG_OUTPUT_LVL_W is not set
# CONFIG_ULOG_OUTPUT_LVL_I is not set
CONFIG_ULOG_OUTPUT_LVL_D=y
CONFIG_ULOG_OUTPUT_LVL=7
CONFIG_ULOG_USING_ISR_LOG=y
CONFIG_ULOG_ASSERT_ENABLE=y
CONFIG_ULOG_LINE_BUF_SIZE=128
# CONFIG_ULOG_USING_ASYNC_OUTPUT is not set

#
# log format
#
# CONFIG_ULOG_OUTPUT_FLOAT is not set
# CONFIG_ULOG_USING_COLOR is not set
CONFIG_ULOG_OUTPUT_TIME=y
# CONFIG_ULOG_TIME_USING_TIMESTAMP is not set
CONFIG_ULOG_OUTPUT_LEVEL=y
CONFIG_ULOG_OUTPUT_TAG=y
# CONFIG_ULOG_OUTPUT_THREAD_NAME is not set
# end of log format

CONFIG_ULOG_BACKEND_USING_CONSOLE=y
# CONFIG_ULOG_BACKEND_USING_TSDB is not set
# CONFIG_SAVE_ASSERT_CONTEXT_IN_FLASH is not set
# CONFIG_ULOG_BACKEND_USING_RAM is not set
# CONFIG_ULOG_USING_FILTER is not set
# CONFIG_ULOG_USING_SYSLOG is not set
# CONFIG_RT_USING_UTEST is not set
# CONFIG_RT_USING_LONG_LIFETIME_MEMHEAP is not set
# end of Utilities
# end of RT-Thread Components

#
# RT-Thread Kernel
#
CONFIG_RT_NAME_MAX=8
# CONFIG_RT_USING_ARCH_DATA_TYPE is not set
CONFIG_RT_ALIGN_SIZE=4
# CONFIG_RT_THREAD_PRIORITY_8 is not set
CONFIG_RT_THREAD_PRIORITY_32=y
# CONFIG_RT_THREAD_PRIORITY_256 is not set
CONFIG_RT_THREAD_PRIORITY_MAX=32
CONFIG_RT_TICK_PER_SECOND=1000
CONFIG_RT_USING_OVERFLOW_CHECK=y
CONFIG_RT_USING_HOOK=y
CONFIG_RT_USING_IDLE_HOOK=y
CONFIG_RT_IDEL_HOOK_LIST_SIZE=4
CONFIG_IDLE_THREAD_STACK_SIZE=1024
# CONFIG_RT_USING_TIMER_SOFT is not set
CONFIG_RT_DEBUG=y
# CONFIG_RT_DEBUG_COLOR is not set
# CONFIG_RT_DEBUG_INIT_CONFIG is not set
# CONFIG_RT_DEBUG_THREAD_CONFIG is not set
# CONFIG_RT_DEBUG_SCHEDULER_CONFIG is not set
# CONFIG_RT_DEBUG_IPC_CONFIG is not set
# CONFIG_RT_DEBUG_TIMER_CONFIG is not set
# CONFIG_RT_DEBUG_IRQ_CONFIG is not set
# CONFIG_RT_DEBUG_MEM_CONFIG is not set
# CONFIG_RT_DEBUG_SLAB_CONFIG is not set
# CONFIG_RT_DEBUG_MEMHEAP_CONFIG is not set
# CONFIG_RT_DEBUG_MODULE_CONFIG is not set

#
# Inter-Thread communication
#
CONFIG_RT_USING_SEMAPHORE=y
CONFIG_RT_USING_MUTEX=y
CONFIG_RT_USING_EVENT=y
CONFIG_RT_USING_MAILBOX=y
CONFIG_RT_USING_MESSAGEQUEUE=y
# CONFIG_RT_USING_SIGNALS is not set
# end of Inter-Thread communication

#
# Memory Management
#
CONFIG_RT_USING_MEMPOOL=y
CONFIG_RT_USING_MEMHEAP=y
# CONFIG_RT_USING_NOHEAP is not set
CONFIG_RT_USING_SMALL_MEM=y
# CONFIG_RT_USING_SLAB is not set
# CONFIG_RT_USING_MEMHEAP_AS_HEAP is not set
# CONFIG_RT_USING_MEMTRACE is not set
CONFIG_RT_USING_HEAP=y
# end of Memory Management

#
# Kernel Device Object
#
CONFIG_RT_USING_DEVICE=y
# CONFIG_RT_USING_DEVICE_OPS is not set
# CONFIG_RT_USING_INTERRUPT_INFO is not set
CONFIG_RT_USING_CONSOLE=y
CONFIG_RT_CONSOLEBUF_SIZE=256
CONFIG_RT_CONSOLE_DEVICE_NAME="uart3"
# end of Kernel Device Object

CONFIG_RT_VER_NUM=0x30103
# end of RT-Thread Kernel

#
# RT-Thread online packages
#

#
# IoT - internet of things
#
# CONFIG_PKG_USING_NETUTILS is not set
# end of IoT - internet of things

#
# miscellaneous packages
#

#
# samples: kernel and components samples
#
# CONFIG_PKG_USING_NETWORK_SAMPLES is not set
# end of samples: kernel and components samples
# end of miscellaneous packages
# end of RT-Thread online packages
# end of RTOS

#
# Sifli middleware
#
CONFIG_BSP_USING_FULL_ASSERT=y
# CONFIG_BSP_USING_LOOP_ASSERT is not set
# CONFIG_BSP_USING_EMPTY_ASSERT is not set
# CONFIG_BLUETOOTH is not set

#
# RW Bluetooth
#
# CONFIG_RWBT_ENABLE is not set
# end of RW Bluetooth

# CONFIG_BSP_USING_PM is not set
# CONFIG_BSP_USING_DATA_SVC is not set
# CONFIG_BSP_USING_DFU is not set
# CONFIG_DFU_USING_DOWNLOAD_BACKUP is not set
# CONFIG_BSP_USING_ML_KWS is not set
# CONFIG_GUI_APP_FRAMEWORK is not set
# CONFIG_USING_BUTTON_LIB is not set
# CONFIG_USING_PROFILER is not set
# CONFIG_USING_SECTION_ITER is not set
CONFIG_USING_IPC_QUEUE=y
# CONFIG_USING_IPC_QUEUE_DEVICE_WRAPPER is not set
# CONFIG_USING_CPU_USAGE_PROFILER is not set
# CONFIG_USING_CONTEXT_BACKUP is not set
# CONFIG_USING_LIBRARY_ONLY is not set
# CONFIG_USING_METRICS_COLLECTOR is not set
# CONFIG_USING_EZIPA_DEC is not set
# CONFIG_USING_ACPU_CTRL_FWK is not set
# CONFIG_USING_FILE_LOGGER is not set
# CONFIG_USING_NAND_BBM is not set
# CONFIG_USING_PARTITION_TABLE is not set
# CONFIG_USING_ELM_RW is not set
# CONFIG_USING_SEC_ENV is not set
# end of Sifli middleware

#
# Third party packages
#
# CONFIG_RT_AWTK is not set
# CONFIG_PKG_USING_AZUREGUIX is not set
# CONFIG_PKG_USING_CAIRO is not set
# CONFIG_PKG_USING_CMBACKTRACE is not set
# CONFIG_BSP_USING_CMSIS_DSP is not set
# CONFIG_RT_USING_RTT_CMSIS is not set
# CONFIG_PKG_USING_COREMARK is not set
# CONFIG_PKG_USING_EASYTLV is not set
# CONFIG_PKG_USING_AAC_DECODER_LIBFAAD is not set
# CONFIG_PKG_USING_FFMPEG is not set
# CONFIG_PKG_USING_FLASHDB is not set
# CONFIG_PKG_FREETYPE is not set
# CONFIG_PKG_USING_JERRYSCRIPT is not set
# CONFIG_USING_JPEG_NANOENC is not set
# CONFIG_USING_JPEG_NANODEC is not set
# CONFIG_PKG_USING_LIBHELIX is not set
# CONFIG_PKG_USING_MP3_DECODER_LIBMAD is not set
# CONFIG_PKG_USING_LITTLEVGL2RTT is not set
# CONFIG_PKG_USING_LZ4 is not set
# CONFIG_PKG_SIFLI_MBEDTLS_BOOT is not set
# CONFIG_PKG_USING_MICROPYTHON is not set
# CONFIG_PKG_USING_MPU6XXX is not set
# CONFIG_PKG_USING_NANOPB is not set
# CONFIG_GPS_USING_NMEALIB is not set
# CONFIG_PKG_USING_OPENH264 is not set
# CONFIG_PKG_LIB_OPUS is not set
# CONFIG_PKG_USING_PIXMAN is not set
# CONFIG_PKG_USING_QUICKJS is not set
# CONFIG_PKG_USING_RLOTTIE is not set
# CONFIG_PKG_USING_SEGGER_RTT is not set
# CONFIG_PKG_USING_SYSTEMVIEW is not set
# CONFIG_PKG_SIFLI_SENSOR_LIB is not set
# CONFIG_PKG_USING_UI_LOADER is not set
# CONFIG_USING_VGLITE is not set
# CONFIG_PKG_USING_WEBRTC is not set
# CONFIG_PKG_USING_ZLIB is not set
# CONFIG_PKG_USING_TINYMP3 is not set
# CONFIG_PKG_USING_SILK_V3 is not set
# CONFIG_PKG_ALIPAY_SE is not set
# CONFIG_PKG_SCHRIFT is not set
# end of Third party packages

#
# ------------End of SDK configuration-------------
#
CONFIG_BF0_HCPU=y
CONFIG_CFG_BOOTLOADER=y
